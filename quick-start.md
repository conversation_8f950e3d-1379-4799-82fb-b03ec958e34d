# Quick Start Guide

## Fix Docker Permissions (One-time setup)

```bash
# Start Docker service (requires sudo password)
sudo systemctl start docker

# Apply docker group membership without logging out
newgrp docker
```

## Start Infrastructure Services

```bash
# Start PostgreSQL and Redis only
./start-infrastructure-only.sh
```

## Set Up NestJS Backend

```bash
# Set up the backend application
./setup-nestjs-backend.sh

# Start development server
cd nestjs-backend
npm run start:dev
```

## Access Services

- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379  
- **API**: localhost:3000 (when NestJS is running)
- **API Docs**: localhost:3000/api/docs

## Troubleshooting

If you get permission denied errors:
```bash
sudo systemctl start docker
newgrp docker
```

Then try the commands again.
