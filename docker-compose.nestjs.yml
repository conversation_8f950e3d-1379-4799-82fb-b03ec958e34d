# Modern Multi-Tenant NestJS Stack - Production Ready
# High-performance configuration with PostgreSQL, Redis, and Nginx

services:
  # PostgreSQL 16 with Multi-Tenant Row Level Security
  postgres:
    image: postgres:16-alpine
    container_name: nestjs_postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-nestjs_app}
      POSTGRES_USER: ${POSTGRES_USER:-nestjs}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_logs:/var/log/postgresql
      - ./docker/postgres/init:/docker-entrypoint-initdb.d:ro
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - backend
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-nestjs} -d ${POSTGRES_DB:-nestjs_app}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis 7 for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: nestjs_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    networks:
      - backend
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1.5G
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD:-redis_password}", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

  # NestJS Application
  nestjs-app:
    build:
      context: ./nestjs-backend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: nestjs_app
    restart: unless-stopped
    ports:
      - "${NESTJS_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3000
      - DATABASE_URL=postgresql://${POSTGRES_USER:-nestjs}:${POSTGRES_PASSWORD:-password}@postgres:5432/${POSTGRES_DB:-nestjs_app}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3001}
    volumes:
      - ./nestjs-backend:/app
      - /app/node_modules
      - app_uploads:/app/uploads
    networks:
      - frontend
      - backend
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Next.js Admin Dashboard
  nextjs-admin:
    build:
      context: ./nextjs-admin
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: nextjs_admin
    restart: unless-stopped
    ports:
      - "${NEXTJS_PORT:-3001}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:3000}
      - NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost:3001}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-your-nextauth-secret}
    volumes:
      - ./nextjs-admin:/app
      - /app/node_modules
      - /app/.next
    networks:
      - frontend
    depends_on:
      - nestjs-app
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.3'
          memory: 256M

  # Nginx Reverse Proxy with Multi-Tenant Routing
  nginx:
    image: nginx:1.25-alpine
    container_name: nestjs_nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
      - nginx_logs:/var/log/nginx
    networks:
      - frontend
    depends_on:
      - nestjs-app
      - nextjs-admin
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 128M

# Networks
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true

# Volumes
volumes:
  # PostgreSQL Storage
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres

  postgres_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-./logs}/postgres

  # Redis Storage
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/redis

  redis_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-./logs}/redis

  # Application Storage
  app_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/uploads

  # Nginx Cache
  nginx_cache:
    driver: local
    driver_opts:
      type: tmpfs
      device: tmpfs
      o: size=500m,uid=101,gid=101

  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-./logs}/nginx
