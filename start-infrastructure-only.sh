#!/bin/bash

# Start Infrastructure Only Script
# This script starts only PostgreSQL and Redis without the NestJS app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "Starting Infrastructure Services Only"
echo "=========================================="
echo

# Check if Docker is accessible
if ! docker ps >/dev/null 2>&1; then
    print_error "Docker is not accessible. Please run:"
    echo "sudo systemctl start docker"
    echo "newgrp docker"
    echo "Then try this script again."
    exit 1
fi

print_success "Docker is accessible"

# Create a simplified docker-compose file for infrastructure only
cat > docker-compose.infrastructure.yml << 'EOF'
# Infrastructure Only - PostgreSQL and Redis
services:
  # PostgreSQL 16 with Multi-Tenant Row Level Security
  postgres:
    image: postgres:16-alpine
    container_name: nestjs_postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-nestjs_app}
      POSTGRES_USER: ${POSTGRES_USER:-nestjs}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d:ro
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-nestjs} -d ${POSTGRES_DB:-nestjs_app}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis 7 for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: nestjs_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    networks:
      - backend
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD:-redis_password}", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

# Networks
networks:
  backend:
    driver: bridge

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
EOF

print_status "Created infrastructure-only Docker Compose file"

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    print_status "Loaded environment variables from .env"
else
    print_warning "No .env file found, using defaults"
fi

# Start infrastructure services
print_status "Starting PostgreSQL and Redis..."
docker-compose -f docker-compose.infrastructure.yml up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose -f docker-compose.infrastructure.yml ps

# Test PostgreSQL connection
print_status "Testing PostgreSQL connection..."
if docker exec nestjs_postgres pg_isready -U ${POSTGRES_USER:-nestjs} -d ${POSTGRES_DB:-nestjs_app} >/dev/null 2>&1; then
    print_success "PostgreSQL is ready and accepting connections"
else
    print_warning "PostgreSQL might not be fully ready yet"
fi

# Test Redis connection
print_status "Testing Redis connection..."
if docker exec nestjs_redis redis-cli --no-auth-warning -a ${REDIS_PASSWORD:-redis_password} ping >/dev/null 2>&1; then
    print_success "Redis is ready and accepting connections"
else
    print_warning "Redis might not be fully ready yet"
fi

echo
print_success "Infrastructure services are running!"
echo
print_status "Services available:"
echo "- PostgreSQL: localhost:5432"
echo "- Redis: localhost:6379"
echo
print_status "Next steps:"
echo "1. Set up NestJS backend: ./setup-nestjs-backend.sh"
echo "2. Start NestJS development server: cd nestjs-backend && npm run start:dev"
echo
print_status "To stop services: docker-compose -f docker-compose.infrastructure.yml down"
