# Docker Infrastructure for Multi-Tenant NestJS Application

This document describes the Docker infrastructure setup for migrating from Laravel to a modern NestJS stack with PostgreSQL, Redis, and Nginx.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  Next.js Admin  │    │  NestJS API     │
│  Multi-Tenant   │◄──►│   Dashboard     │◄──►│   Backend       │
│    Routing      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │   File Storage  │
│  Multi-Tenant   │    │  Cache/Session  │    │    Uploads      │
│      RLS        │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Services

### 1. PostgreSQL 16 (Database)
- **Container**: `nestjs_postgres`
- **Port**: 5432
- **Features**:
  - Row Level Security (RLS) for multi-tenancy
  - Optimized configuration for performance
  - Automatic backups and logging
  - Extensions: uuid-ossp, pg_stat_statements, pg_trgm, unaccent

### 2. Redis 7 (Cache & Sessions)
- **Container**: `nestjs_redis`
- **Port**: 6379
- **Features**:
  - Persistent storage with AOF
  - Memory optimization
  - Password protection
  - Used for caching, sessions, and queues

### 3. NestJS Application (Backend API)
- **Container**: `nestjs_app`
- **Port**: 3000
- **Features**:
  - TypeScript-based REST API
  - JWT authentication
  - Multi-tenant middleware
  - Swagger documentation
  - Mobile-ready endpoints

### 4. Next.js Admin Dashboard (Frontend)
- **Container**: `nextjs_admin`
- **Port**: 3001
- **Features**:
  - TypeScript + Tailwind CSS
  - Server-side rendering
  - Real-time updates
  - Responsive design
  - Multi-tenant switching

### 5. Nginx (Reverse Proxy)
- **Container**: `nestjs_nginx`
- **Ports**: 80, 443
- **Features**:
  - Multi-tenant routing by subdomain
  - Rate limiting
  - SSL termination (production)
  - Load balancing
  - Static file serving

## Multi-Tenancy Implementation

### Tenant Identification
- **Method**: Subdomain-based routing
- **Examples**:
  - `company1.api.yourdomain.com` → API for Company 1
  - `company1.admin.yourdomain.com` → Admin for Company 1
  - `company2.api.yourdomain.com` → API for Company 2

### Data Isolation
- **PostgreSQL Row Level Security (RLS)**
- **Tenant Context Functions**:
  - `set_current_tenant(tenant_id)` - Set tenant context
  - `get_current_tenant()` - Get current tenant
  - `is_super_admin()` - Check super admin status

## Quick Start

### 1. Initial Setup
```bash
# Run the setup script
./setup-nestjs-stack.sh

# Review and update environment variables
nano .env
```

### 2. Start Infrastructure
```bash
# Start all services
docker-compose -f docker-compose.nestjs.yml up -d

# Check service status
docker-compose -f docker-compose.nestjs.yml ps
```

### 3. View Logs
```bash
# All services
docker-compose -f docker-compose.nestjs.yml logs -f

# Specific service
docker-compose -f docker-compose.nestjs.yml logs -f postgres
```

## Environment Configuration

Key environment variables in `.env`:

```bash
# Database
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=******************************************/nestjs_app

# Redis
REDIS_PASSWORD=your_redis_password
REDIS_URL=redis://:password@redis:6379

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Multi-Tenancy
MULTI_TENANT_ENABLED=true
TENANT_IDENTIFICATION=subdomain
```

## Database Management

### Connect to PostgreSQL
```bash
docker exec -it nestjs_postgres psql -U nestjs -d nestjs_app
```

### Common SQL Commands
```sql
-- Set tenant context
SELECT set_current_tenant(1);

-- Check current tenant
SELECT get_current_tenant();

-- List all companies
SELECT * FROM companies;

-- Enable RLS on a table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
```

### Connect to Redis
```bash
docker exec -it nestjs_redis redis-cli
AUTH your_redis_password
```

## Development Workflow

### 1. Backend Development (NestJS)
```bash
cd nestjs-backend
npm install
npm run start:dev
```

### 2. Frontend Development (Next.js)
```bash
cd nextjs-admin
npm install
npm run dev
```

### 3. Database Migrations
```bash
# Generate migration
npm run migration:generate -- --name=CreateUsersTable

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## Production Deployment

### 1. Environment Setup
```bash
# Copy production environment
cp .env.nestjs .env.production

# Update for production
NODE_ENV=production
DEBUG_MODE=false
SWAGGER_ENABLED=false
```

### 2. SSL Configuration
```bash
# Add SSL certificates
cp your-cert.pem docker/ssl/cert.pem
cp your-key.pem docker/ssl/key.pem

# Update nginx configuration for HTTPS
```

### 3. Deploy
```bash
# Build and start production services
docker-compose -f docker-compose.nestjs.yml -f docker-compose.prod.yml up -d
```

## Monitoring and Maintenance

### Health Checks
- **API**: `http://localhost:3000/health`
- **Admin**: `http://localhost:3001`
- **Nginx**: `http://localhost/nginx-health`

### Backup Database
```bash
docker exec nestjs_postgres pg_dump -U nestjs nestjs_app > backup.sql
```

### Restore Database
```bash
docker exec -i nestjs_postgres psql -U nestjs nestjs_app < backup.sql
```

### Performance Monitoring
```bash
# View resource usage
docker stats

# PostgreSQL performance
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -c "SELECT * FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"
```

## Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check logs
   docker-compose -f docker-compose.nestjs.yml logs
   
   # Restart services
   docker-compose -f docker-compose.nestjs.yml restart
   ```

2. **Database connection issues**
   ```bash
   # Check PostgreSQL status
   docker exec nestjs_postgres pg_isready -U nestjs
   
   # Reset database
   docker-compose -f docker-compose.nestjs.yml down -v
   docker-compose -f docker-compose.nestjs.yml up -d
   ```

3. **Permission issues**
   ```bash
   # Fix permissions
   sudo chown -R $USER:$USER data/ logs/ uploads/
   chmod -R 755 data/ logs/ uploads/
   ```

## Next Steps

1. **Create NestJS Backend** - Implement the API with TypeORM and multi-tenancy
2. **Create Next.js Admin** - Build the admin dashboard with modern UI
3. **Database Migration** - Convert Laravel schema to PostgreSQL
4. **Testing** - Implement comprehensive test suites
5. **Documentation** - Generate API documentation with Swagger

## Support

For issues and questions:
- Check the logs: `docker-compose -f docker-compose.nestjs.yml logs`
- Review the environment configuration in `.env`
- Ensure all prerequisites are installed
- Verify network connectivity between services
