# Database Migration Guide: <PERSON><PERSON> to PostgreSQL

This guide documents the migration from Laravel with MariaDB to NestJS with PostgreSQL, including multi-tenancy implementation using Row Level Security (RLS).

## Migration Overview

### Source: <PERSON><PERSON> + MariaDB
- **Framework**: <PERSON><PERSON> 11
- **Database**: MariaDB 11.4
- **Multi-tenancy**: Company-based scoping with middleware
- **ORM**: Eloquent with custom traits

### Target: NestJS + PostgreSQL
- **Framework**: NestJS with TypeORM
- **Database**: PostgreSQL 16
- **Multi-tenancy**: Row Level Security (RLS) + Application-level scoping
- **ORM**: TypeORM with custom decorators

## Schema Conversion

### Key Changes

1. **Data Types**:
   - `BIGINT AUTO_INCREMENT` → `BIGSERIAL`
   - `JSON` → `JSONB` (better performance)
   - `ENUM` → Custom PostgreSQL ENUMs
   - `TIMESTAMP` → `TIMESTAMP WITH TIME ZONE`

2. **Multi-tenancy**:
   - <PERSON><PERSON> middleware scoping → PostgreSQL RLS policies
   - Global scopes → RLS + application context
   - Tenant isolation enforced at database level

3. **Indexes**:
   - Converted all Laravel indexes to PostgreSQL
   - Added GIN indexes for JSONB columns
   - Optimized for multi-tenant queries

## Table Mapping

### Core Tables

| Laravel Table | PostgreSQL Table | Changes |
|---------------|------------------|---------|
| `companies` | `companies` | Added RLS, JSONB columns |
| `users` | `users` | Added RLS, enum types |
| `categories` | `categories` | Translatable JSON fields |
| `entities` | `entities` | Complex JSONB structures |
| `events` | `events` | Timezone handling improved |
| `posts` | `posts` | Multilingual content |

### Permission Tables (Spatie Compatible)

| Laravel Table | PostgreSQL Table | Changes |
|---------------|------------------|---------|
| `permissions` | `permissions` | Maintained compatibility |
| `roles` | `roles` | Added company_id for tenancy |
| `model_has_permissions` | `model_has_permissions` | Polymorphic relations |
| `model_has_roles` | `model_has_roles` | Polymorphic relations |
| `role_has_permissions` | `role_has_permissions` | Direct mapping |

### System Tables

| Laravel Table | PostgreSQL Table | Changes |
|---------------|------------------|---------|
| `activity_log` | `audit_logs` | Enhanced with RLS |
| `sessions` | `sessions` | Added tenant context |
| `personal_access_tokens` | `personal_access_tokens` | API token management |
| `media` | `media` | File attachment system |

## Multi-Tenancy Implementation

### Row Level Security (RLS)

PostgreSQL RLS provides database-level tenant isolation:

```sql
-- Enable RLS on tenant tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create tenant isolation policy
CREATE POLICY users_tenant_isolation ON users
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );
```

### Tenant Context Functions

```sql
-- Set current tenant context
SELECT set_current_tenant(1);

-- Get current tenant
SELECT get_current_tenant();

-- Check super admin status
SELECT is_super_admin();
```

### Application Integration

The NestJS application will use these functions to:
1. Set tenant context on each request
2. Automatically scope all queries
3. Allow super admins to bypass restrictions

## Data Migration Process

### 1. Schema Migration

```bash
# Run PostgreSQL initialization scripts
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -f /docker-entrypoint-initdb.d/01-init-database.sql
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -f /docker-entrypoint-initdb.d/02-create-tables.sql
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -f /docker-entrypoint-initdb.d/03-create-additional-tables.sql
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -f /docker-entrypoint-initdb.d/04-row-level-security.sql
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -f /docker-entrypoint-initdb.d/05-seed-data.sql
docker exec nestjs_postgres psql -U nestjs -d nestjs_app -f /docker-entrypoint-initdb.d/06-system-tables.sql
```

### 2. Data Export from Laravel

```bash
# Export companies
php artisan export:companies --format=json --output=companies.json

# Export users
php artisan export:users --format=json --output=users.json

# Export entities
php artisan export:entities --format=json --output=entities.json

# Export events
php artisan export:events --format=json --output=events.json
```

### 3. Data Import to PostgreSQL

```bash
# Import companies
node scripts/import-companies.js companies.json

# Import users (with password hashing)
node scripts/import-users.js users.json

# Import entities
node scripts/import-entities.js entities.json

# Import events
node scripts/import-events.js events.json
```

## Key Differences

### 1. JSON Handling

**Laravel (MariaDB)**:
```php
$user->preferences = ['theme' => 'dark'];
$user->save();
```

**NestJS (PostgreSQL)**:
```typescript
user.preferences = { theme: 'dark' };
await this.userRepository.save(user);
```

### 2. Multi-tenancy

**Laravel**:
```php
// Middleware sets company context
User::where('company_id', auth()->user()->company_id)->get();
```

**NestJS**:
```typescript
// RLS automatically filters by tenant
await this.userRepository.find(); // Only returns current tenant's users
```

### 3. Translatable Fields

**Laravel**:
```php
$entity->setTranslation('name', 'en', 'English Name');
$entity->setTranslation('name', 'es', 'Nombre en Español');
```

**PostgreSQL**:
```sql
UPDATE entities SET name = '{"en": "English Name", "es": "Nombre en Español"}';
```

## Performance Optimizations

### 1. Indexes

```sql
-- Multi-tenant optimized indexes
CREATE INDEX idx_users_company_id_active ON users(company_id, is_active);
CREATE INDEX idx_entities_company_status ON entities(company_id, status);

-- JSONB indexes for fast queries
CREATE INDEX idx_entities_name_gin ON entities USING GIN (name);
CREATE INDEX idx_users_preferences_gin ON users USING GIN (preferences);
```

### 2. Partitioning (Future)

```sql
-- Partition large tables by company_id
CREATE TABLE audit_logs_partitioned (
    LIKE audit_logs INCLUDING ALL
) PARTITION BY HASH (company_id);
```

## Security Enhancements

### 1. Row Level Security

- Automatic tenant isolation at database level
- Super admin bypass capability
- Public data access policies

### 2. Function Security

```sql
-- Security definer functions for tenant context
CREATE OR REPLACE FUNCTION set_current_tenant(tenant_id INTEGER)
RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_id::TEXT, TRUE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Testing Strategy

### 1. Data Integrity

```sql
-- Verify tenant isolation
SELECT set_current_tenant(1);
SELECT COUNT(*) FROM users; -- Should only show tenant 1 users

SELECT set_current_tenant(2);
SELECT COUNT(*) FROM users; -- Should only show tenant 2 users
```

### 2. Performance Testing

```sql
-- Test query performance with RLS
EXPLAIN ANALYZE SELECT * FROM users WHERE is_active = true;
```

### 3. Migration Validation

```bash
# Compare record counts
echo "Laravel Users: $(mysql -e 'SELECT COUNT(*) FROM users')"
echo "PostgreSQL Users: $(psql -c 'SELECT COUNT(*) FROM users')"
```

## Rollback Strategy

### 1. Database Backup

```bash
# Create backup before migration
pg_dump -U nestjs nestjs_app > backup_pre_migration.sql
```

### 2. Rollback Scripts

```bash
# Restore from backup if needed
psql -U nestjs nestjs_app < backup_pre_migration.sql
```

## Monitoring and Maintenance

### 1. Performance Monitoring

```sql
-- Monitor RLS policy performance
SELECT * FROM pg_stat_statements 
WHERE query LIKE '%get_current_tenant%' 
ORDER BY total_time DESC;
```

### 2. Tenant Statistics

```sql
-- Get tenant usage statistics
SELECT 
    c.name,
    COUNT(u.id) as user_count,
    COUNT(e.id) as entity_count
FROM companies c
LEFT JOIN users u ON c.id = u.company_id
LEFT JOIN entities e ON c.id = e.company_id
GROUP BY c.id, c.name;
```

## Next Steps

1. **NestJS Application Setup**: Create TypeORM entities matching the schema
2. **Migration Scripts**: Develop data migration utilities
3. **Testing**: Comprehensive testing of multi-tenancy and data integrity
4. **Performance Tuning**: Optimize queries and indexes based on usage patterns
5. **Documentation**: API documentation and deployment guides

## Support

For migration issues:
1. Check PostgreSQL logs: `docker logs nestjs_postgres`
2. Verify RLS policies: `\d+ table_name` in psql
3. Test tenant context: `SELECT get_current_tenant();`
4. Monitor performance: `EXPLAIN ANALYZE` for slow queries
