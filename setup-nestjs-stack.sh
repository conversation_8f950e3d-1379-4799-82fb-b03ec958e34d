#!/bin/bash

# Multi-Tenant NestJS Stack Setup Script
# This script sets up the complete Docker infrastructure for the NestJS application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    if ! command_exists node; then
        print_warning "Node.js is not installed. You'll need it for local development."
    fi
    
    print_success "Prerequisites check completed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Data directories
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p logs/postgres
    mkdir -p logs/redis
    mkdir -p logs/nginx
    mkdir -p uploads
    
    # SSL directory for future use
    mkdir -p docker/ssl
    
    # Application directories (will be created later)
    mkdir -p nestjs-backend
    mkdir -p nextjs-admin
    
    print_success "Directories created"
}

# Set up environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.nestjs .env
        print_success "Environment file created from template"
        print_warning "Please review and update the .env file with your specific configuration"
    else
        print_warning ".env file already exists. Skipping..."
    fi
}

# Generate secure passwords
generate_passwords() {
    print_status "Generating secure passwords..."
    
    # Generate random passwords
    POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
    NEXTAUTH_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    
    # Update .env file with generated passwords
    if [ -f .env ]; then
        sed -i "s|your_secure_postgres_password|$POSTGRES_PASSWORD|g" .env
        sed -i "s|your_secure_redis_password|$REDIS_PASSWORD|g" .env
        sed -i "s|your-super-secret-jwt-key-change-this-in-production|$JWT_SECRET|g" .env
        sed -i "s|your-nextauth-secret-change-this|$NEXTAUTH_SECRET|g" .env

        print_success "Secure passwords generated and updated in .env file"
    fi
}

# Set proper permissions
set_permissions() {
    print_status "Setting proper permissions..."
    
    # Set permissions for data directories
    chmod -R 755 data/
    chmod -R 755 logs/
    chmod -R 755 uploads/
    
    # Set permissions for Docker files
    chmod 644 docker/postgres/postgresql.conf
    chmod 644 docker/postgres/init/*.sql
    chmod 644 docker/nginx/nginx.conf
    chmod 644 docker/nginx/conf.d/*.conf
    
    print_success "Permissions set"
}

# Create Docker network if it doesn't exist
create_networks() {
    print_status "Creating Docker networks..."
    
    # Create networks if they don't exist
    docker network create nestjs_frontend 2>/dev/null || true
    docker network create nestjs_backend 2>/dev/null || true
    
    print_success "Docker networks created"
}

# Start infrastructure services
start_infrastructure() {
    print_status "Starting infrastructure services (PostgreSQL, Redis)..."
    
    # Start only infrastructure services first
    docker-compose -f docker-compose.nestjs.yml up -d postgres redis
    
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check if services are healthy
    if docker-compose -f docker-compose.nestjs.yml ps postgres | grep -q "healthy"; then
        print_success "PostgreSQL is ready"
    else
        print_warning "PostgreSQL might not be fully ready yet"
    fi
    
    if docker-compose -f docker-compose.nestjs.yml ps redis | grep -q "healthy"; then
        print_success "Redis is ready"
    else
        print_warning "Redis might not be fully ready yet"
    fi
}

# Create initial database structure
setup_database() {
    print_status "Setting up initial database structure..."
    
    # Wait a bit more for PostgreSQL to be fully ready
    sleep 10
    
    # Run any additional database setup if needed
    # This will be expanded when we create the NestJS application
    
    print_success "Database setup completed"
}

# Display next steps
show_next_steps() {
    print_success "Docker infrastructure setup completed!"
    echo
    print_status "Next steps:"
    echo "1. Review and update the .env file with your specific configuration"
    echo "2. Create the NestJS backend application in the nestjs-backend directory"
    echo "3. Create the Next.js admin dashboard in the nextjs-admin directory"
    echo "4. Run 'docker-compose -f docker-compose.nestjs.yml up -d' to start all services"
    echo
    print_status "Useful commands:"
    echo "- View logs: docker-compose -f docker-compose.nestjs.yml logs -f [service_name]"
    echo "- Stop services: docker-compose -f docker-compose.nestjs.yml down"
    echo "- Restart services: docker-compose -f docker-compose.nestjs.yml restart [service_name]"
    echo "- Access PostgreSQL: docker exec -it nestjs_postgres psql -U nestjs -d nestjs_app"
    echo "- Access Redis: docker exec -it nestjs_redis redis-cli"
    echo
    print_status "Service URLs (once applications are created):"
    echo "- API: http://localhost:3000"
    echo "- Admin Dashboard: http://localhost:3001"
    echo "- API Documentation: http://localhost:3000/api/docs"
}

# Main execution
main() {
    echo "=========================================="
    echo "Multi-Tenant NestJS Stack Setup"
    echo "=========================================="
    echo
    
    check_prerequisites
    create_directories
    setup_environment
    generate_passwords
    set_permissions
    create_networks
    start_infrastructure
    setup_database
    show_next_steps
    
    echo
    print_success "Setup completed successfully!"
}

# Run main function
main "$@"
