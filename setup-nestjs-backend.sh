#!/bin/bash

# NestJS Backend Setup Script
# This script sets up the NestJS backend application structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -f "nestjs-backend/package.json" ]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing NestJS backend dependencies..."
    cd nestjs-backend
    
    if command -v npm >/dev/null 2>&1; then
        npm install
    elif command -v yarn >/dev/null 2>&1; then
        yarn install
    else
        print_error "Neither npm nor yarn is installed"
        exit 1
    fi
    
    cd ..
    print_success "Dependencies installed successfully"
}

# Create remaining directory structure
create_directories() {
    print_status "Creating NestJS application directory structure..."
    
    cd nestjs-backend/src
    
    # Create module directories
    mkdir -p auth/{dto,guards,strategies}
    mkdir -p users/{dto,guards}
    mkdir -p companies/{dto,guards}
    mkdir -p entities/{dto,entities,guards}
    mkdir -p events/{dto,entities,guards}
    mkdir -p categories/{dto,guards}
    mkdir -p posts/{dto,guards}
    mkdir -p comments/{dto,guards}
    mkdir -p notifications/{dto,guards}
    mkdir -p transactions/{dto,guards}
    mkdir -p assessments/{dto,entities,guards}
    mkdir -p progress/{dto,entities,guards}
    mkdir -p roles/{dto,guards}
    mkdir -p permissions/{dto,guards}
    mkdir -p audit/{dto,entities,guards}
    mkdir -p media/{dto,guards}
    mkdir -p integrations/{dto,guards}
    mkdir -p webhooks/{dto,guards}
    mkdir -p health
    
    # Create common directories
    mkdir -p common/{guards,decorators,pipes,validators,utils}
    mkdir -p database/{migrations,seeds}
    
    # Create uploads directory
    mkdir -p ../../uploads/{avatars,documents,images}
    
    cd ../..
    print_success "Directory structure created"
}

# Create TypeScript configuration
create_typescript_config() {
    print_status "Creating TypeScript configuration..."
    
    cat > nestjs-backend/tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2021",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@/*": ["src/*"],
      "@/common/*": ["src/common/*"],
      "@/config/*": ["src/config/*"],
      "@/database/*": ["src/database/*"]
    }
  }
}
EOF

    print_success "TypeScript configuration created"
}

# Create NestJS CLI configuration
create_nest_config() {
    print_status "Creating NestJS CLI configuration..."
    
    cat > nestjs-backend/nest-cli.json << 'EOF'
{
  "$schema": "https://json.schemastore.org/nest-cli",
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "deleteOutDir": true,
    "webpack": true,
    "tsConfigPath": "tsconfig.json"
  }
}
EOF

    print_success "NestJS CLI configuration created"
}

# Create ESLint configuration
create_eslint_config() {
    print_status "Creating ESLint configuration..."
    
    cat > nestjs-backend/.eslintrc.js << 'EOF'
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    '@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
  },
};
EOF

    print_success "ESLint configuration created"
}

# Create Prettier configuration
create_prettier_config() {
    print_status "Creating Prettier configuration..."
    
    cat > nestjs-backend/.prettierrc << 'EOF'
{
  "singleQuote": true,
  "trailingComma": "all",
  "semi": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
EOF

    print_success "Prettier configuration created"
}

# Create environment file
create_env_file() {
    print_status "Creating environment file..."
    
    if [ ! -f nestjs-backend/.env ]; then
        cp .env.nestjs nestjs-backend/.env
        print_success "Environment file created"
    else
        print_warning "Environment file already exists"
    fi
}

# Create basic app controller and service
create_app_files() {
    print_status "Creating basic app files..."
    
    cat > nestjs-backend/src/app.controller.ts << 'EOF'
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('Application')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get application info' })
  @ApiResponse({ status: 200, description: 'Application information' })
  getHello(): string {
    return this.appService.getHello();
  }
}
EOF

    cat > nestjs-backend/src/app.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHello(): string {
    return 'Multi-Tenant NestJS API is running!';
  }
}
EOF

    print_success "Basic app files created"
}

# Create health module
create_health_module() {
    print_status "Creating health check module..."
    
    cat > nestjs-backend/src/health/health.controller.ts << 'EOF'
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  @Get()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
EOF

    cat > nestjs-backend/src/health/health.module.ts << 'EOF'
import { Module } from '@nestjs/common';
import { HealthController } from './health.controller';

@Module({
  controllers: [HealthController],
})
export class HealthModule {}
EOF

    print_success "Health check module created"
}

# Create common module
create_common_module() {
    print_status "Creating common module..."
    
    cat > nestjs-backend/src/common/common.module.ts << 'EOF'
import { Module, Global } from '@nestjs/common';

@Global()
@Module({
  providers: [],
  exports: [],
})
export class CommonModule {}
EOF

    print_success "Common module created"
}

# Create database module
create_database_module() {
    print_status "Creating database module..."
    
    cat > nestjs-backend/src/database/database.module.ts << 'EOF'
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DATABASE_HOST') || 'postgres',
        port: configService.get('DATABASE_PORT') || 5432,
        username: configService.get('POSTGRES_USER') || 'nestjs',
        password: configService.get('POSTGRES_PASSWORD'),
        database: configService.get('POSTGRES_DB') || 'nestjs_app',
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development' ? ['query', 'error'] : ['error'],
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
EOF

    print_success "Database module created"
}

# Create placeholder modules
create_placeholder_modules() {
    print_status "Creating placeholder modules..."
    
    # List of modules to create
    modules=(
        "users" "companies" "entities" "events" "categories" 
        "posts" "comments" "notifications" "transactions" 
        "assessments" "progress" "roles" "permissions" 
        "audit" "media" "integrations" "webhooks"
    )
    
    for module in "${modules[@]}"; do
        cat > "nestjs-backend/src/${module}/${module}.module.ts" << EOF
import { Module } from '@nestjs/common';

@Module({
  imports: [],
  controllers: [],
  providers: [],
  exports: [],
})
export class ${module^}Module {}
EOF
    done
    
    print_success "Placeholder modules created"
}

# Main execution
main() {
    echo "=========================================="
    echo "NestJS Backend Setup"
    echo "=========================================="
    echo
    
    check_directory
    install_dependencies
    create_directories
    create_typescript_config
    create_nest_config
    create_eslint_config
    create_prettier_config
    create_env_file
    create_app_files
    create_health_module
    create_common_module
    create_database_module
    create_placeholder_modules
    
    echo
    print_success "NestJS backend setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. Review and update nestjs-backend/.env file"
    echo "2. Start the development server: cd nestjs-backend && npm run start:dev"
    echo "3. Access API documentation: http://localhost:3000/api/docs"
    echo "4. Implement remaining controllers and services"
    echo
    print_status "Development commands:"
    echo "- Start dev server: npm run start:dev"
    echo "- Run tests: npm run test"
    echo "- Build for production: npm run build"
    echo "- Generate migration: npm run migration:generate -- --name=MigrationName"
    echo "- Run migrations: npm run migration:run"
}

# Run main function
main "$@"
