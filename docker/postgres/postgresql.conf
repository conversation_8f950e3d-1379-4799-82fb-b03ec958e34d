# PostgreSQL Configuration for Multi-Tenant NestJS Application
# Optimized for performance and multi-tenancy with Row Level Security

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 1GB
effective_cache_size = 3GB
work_mem = 16MB
maintenance_work_mem = 256MB
dynamic_shared_memory_type = posix

# Checkpoint Settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Query Planner Settings
random_page_cost = 1.1
effective_io_concurrency = 200

# Write Ahead Logging (WAL)
wal_level = replica
max_wal_size = 2GB
min_wal_size = 80MB
archive_mode = off

# Replication (for future scaling)
max_wal_senders = 3
max_replication_slots = 3

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# Row Level Security (for multi-tenancy)
row_security = on

# Performance Monitoring
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# Autovacuum
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min

# Locale Settings
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Timezone
timezone = 'UTC'

# Security
ssl = off
password_encryption = scram-sha-256

# Extensions
shared_preload_libraries = 'pg_stat_statements'

# Custom Settings for Multi-Tenancy
# These can be adjusted based on your specific needs
statement_timeout = 30s
lock_timeout = 10s
idle_in_transaction_session_timeout = 60s
