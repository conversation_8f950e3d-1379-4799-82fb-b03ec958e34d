-- System Tables for Multi-Tenant NestJS Application
-- Audit logs, sessions, and other system-level tables

-- Create audit_logs table
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    user_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    action audit_action NOT NULL,
    auditable_type VARCHAR(255) NOT NULL,
    auditable_id BIGINT,
    old_values JSONB,
    new_values JSONB,
    url VARCHAR(500),
    ip_address VARCHAR(45),
    user_agent TEXT,
    tags VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit_logs
CREATE INDEX idx_audit_logs_company_id ON audit_logs(company_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_auditable ON audit_logs(auditable_type, auditable_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_ip_address ON audit_logs(ip_address);

-- Create sessions table (for session management)
CREATE TABLE sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    payload TEXT NOT NULL,
    last_activity INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for sessions
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_company_id ON sessions(company_id);
CREATE INDEX idx_sessions_last_activity ON sessions(last_activity);

-- Create password_reset_tokens table
CREATE TABLE password_reset_tokens (
    email VARCHAR(255) PRIMARY KEY,
    token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create personal_access_tokens table (for API tokens)
CREATE TABLE personal_access_tokens (
    id BIGSERIAL PRIMARY KEY,
    tokenable_type VARCHAR(255) NOT NULL,
    tokenable_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    token VARCHAR(64) UNIQUE NOT NULL,
    abilities TEXT,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for personal_access_tokens
CREATE INDEX idx_personal_access_tokens_tokenable ON personal_access_tokens(tokenable_type, tokenable_id);
CREATE INDEX idx_personal_access_tokens_token ON personal_access_tokens(token);
CREATE INDEX idx_personal_access_tokens_last_used ON personal_access_tokens(last_used_at);

-- Create jobs table (for queue management)
CREATE TABLE jobs (
    id BIGSERIAL PRIMARY KEY,
    queue VARCHAR(255) NOT NULL,
    payload TEXT NOT NULL,
    attempts INTEGER NOT NULL DEFAULT 0,
    reserved_at INTEGER,
    available_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL
);

-- Create indexes for jobs
CREATE INDEX idx_jobs_queue ON jobs(queue);
CREATE INDEX idx_jobs_reserved_at ON jobs(reserved_at);
CREATE INDEX idx_jobs_available_at ON jobs(available_at);

-- Create job_batches table
CREATE TABLE job_batches (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    total_jobs INTEGER NOT NULL,
    pending_jobs INTEGER NOT NULL,
    failed_jobs INTEGER NOT NULL,
    failed_job_ids TEXT NOT NULL,
    options TEXT,
    cancelled_at INTEGER,
    created_at INTEGER NOT NULL,
    finished_at INTEGER
);

-- Create failed_jobs table
CREATE TABLE failed_jobs (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    connection TEXT NOT NULL,
    queue TEXT NOT NULL,
    payload TEXT NOT NULL,
    exception TEXT NOT NULL,
    failed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cache table (for database cache driver)
CREATE TABLE cache (
    key VARCHAR(255) PRIMARY KEY,
    value TEXT NOT NULL,
    expiration INTEGER NOT NULL
);

-- Create cache_locks table
CREATE TABLE cache_locks (
    key VARCHAR(255) PRIMARY KEY,
    owner VARCHAR(255) NOT NULL,
    expiration INTEGER NOT NULL
);

-- Create media table (for file attachments)
CREATE TABLE media (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    model_type VARCHAR(255) NOT NULL,
    model_id BIGINT NOT NULL,
    collection_name VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(255),
    disk VARCHAR(255) NOT NULL,
    conversions_disk VARCHAR(255),
    size BIGINT NOT NULL,
    manipulations JSONB NOT NULL,
    custom_properties JSONB NOT NULL,
    generated_conversions JSONB NOT NULL,
    responsive_images JSONB NOT NULL,
    order_column INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for media
CREATE INDEX idx_media_company_id ON media(company_id);
CREATE INDEX idx_media_model ON media(model_type, model_id);
CREATE INDEX idx_media_collection_name ON media(collection_name);
CREATE INDEX idx_media_order_column ON media(order_column);

-- Create migration_mappings table (for tracking migrations)
CREATE TABLE migration_mappings (
    id BIGSERIAL PRIMARY KEY,
    laravel_table VARCHAR(255) NOT NULL,
    laravel_id BIGINT NOT NULL,
    nestjs_table VARCHAR(255) NOT NULL,
    nestjs_id BIGINT NOT NULL,
    migration_batch VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(laravel_table, laravel_id, nestjs_table)
);

-- Create indexes for migration_mappings
CREATE INDEX idx_migration_mappings_laravel ON migration_mappings(laravel_table, laravel_id);
CREATE INDEX idx_migration_mappings_nestjs ON migration_mappings(nestjs_table, nestjs_id);
CREATE INDEX idx_migration_mappings_batch ON migration_mappings(migration_batch);

-- Create integrations table
CREATE TABLE integrations (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(255) NOT NULL,
    provider VARCHAR(255) NOT NULL,
    configuration JSONB NOT NULL,
    credentials JSONB, -- Encrypted
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(255) DEFAULT 'pending',
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for integrations
CREATE INDEX idx_integrations_company_id ON integrations(company_id);
CREATE INDEX idx_integrations_type ON integrations(type);
CREATE INDEX idx_integrations_provider ON integrations(provider);
CREATE INDEX idx_integrations_is_active ON integrations(is_active);
CREATE INDEX idx_integrations_last_sync ON integrations(last_sync_at);
CREATE INDEX idx_integrations_deleted_at ON integrations(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create webhooks table
CREATE TABLE webhooks (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    events JSONB NOT NULL,
    secret VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for webhooks
CREATE INDEX idx_webhooks_company_id ON webhooks(company_id);
CREATE INDEX idx_webhooks_is_active ON webhooks(is_active);
CREATE INDEX idx_webhooks_last_triggered ON webhooks(last_triggered_at);
CREATE INDEX idx_webhooks_deleted_at ON webhooks(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create webhook_calls table (for tracking webhook deliveries)
CREATE TABLE webhook_calls (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    webhook_id BIGINT NOT NULL REFERENCES webhooks(id) ON DELETE CASCADE,
    event_type VARCHAR(255) NOT NULL,
    payload JSONB NOT NULL,
    headers JSONB,
    response_status INTEGER,
    response_body TEXT,
    response_headers JSONB,
    duration_ms INTEGER,
    attempts INTEGER DEFAULT 1,
    status VARCHAR(255) DEFAULT 'pending', -- pending, success, failed
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for webhook_calls
CREATE INDEX idx_webhook_calls_webhook_id ON webhook_calls(webhook_id);
CREATE INDEX idx_webhook_calls_event_type ON webhook_calls(event_type);
CREATE INDEX idx_webhook_calls_status ON webhook_calls(status);
CREATE INDEX idx_webhook_calls_created_at ON webhook_calls(created_at);

-- Enable RLS on system tables that need tenant isolation
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE media ENABLE ROW LEVEL SECURITY;
ALTER TABLE integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for system tables
CREATE POLICY audit_logs_tenant_isolation ON audit_logs
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

CREATE POLICY sessions_tenant_isolation ON sessions
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

CREATE POLICY media_tenant_isolation ON media
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

CREATE POLICY integrations_tenant_isolation ON integrations
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

CREATE POLICY webhooks_tenant_isolation ON webhooks
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Grant permissions on system tables
GRANT SELECT, INSERT, UPDATE, DELETE ON audit_logs TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON sessions TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON password_reset_tokens TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON personal_access_tokens TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON jobs TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON job_batches TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON failed_jobs TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON cache TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON cache_locks TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON media TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON migration_mappings TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON integrations TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON webhooks TO nestjs_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON webhook_calls TO nestjs_app;

-- Log system tables creation completion
DO $$
BEGIN
    RAISE NOTICE 'System tables created successfully with RLS policies';
END
$$;
