-- PostgreSQL Schema Migration for Multi-Tenant NestJS Application
-- Converted from Laravel migrations with Row Level Security

-- Create companies table (tenant table)
CREATE TABLE companies (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    domain VARCHAR(255),
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    email VARCHAR(255),
    phone VARCHAR(255),
    website VARCHAR(255),
    logo_url VARCHAR(255),
    industry VARCHAR(255) DEFAULT 'generic',
    status company_status DEFAULT 'active',
    settings JSONB,
    branding JSONB,
    features JSON<PERSON>,
    license_tier VARCHAR(255) DEFAULT 'basic',
    license_expires_at TIMESTAMP WITH TIME ZONE,
    limits JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for companies
CREATE INDEX idx_companies_status_industry ON companies(status, industry);
CREATE INDEX idx_companies_license_tier ON companies(license_tier);
CREATE INDEX idx_companies_created_at ON companies(created_at);
CREATE INDEX idx_companies_deleted_at ON companies(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create categories table
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    parent_id BIGINT REFERENCES categories(id) ON DELETE CASCADE,
    name JSONB NOT NULL, -- Translatable field
    description JSONB, -- Translatable field
    slug VARCHAR(255) NOT NULL,
    icon VARCHAR(255),
    color VARCHAR(7),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for categories
CREATE INDEX idx_categories_company_id ON categories(company_id);
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug_company ON categories(slug, company_id);
CREATE INDEX idx_categories_is_active ON categories(is_active);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(255),
    date_of_birth DATE,
    gender user_gender,
    avatar_url VARCHAR(255),
    is_admin BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    permissions JSONB,
    preferences JSONB,
    language VARCHAR(5) DEFAULT 'en',
    timezone VARCHAR(255) DEFAULT 'UTC',
    remember_token VARCHAR(100),
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_login_ip VARCHAR(45),
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret TEXT,
    two_factor_recovery_codes JSONB,
    profile_data JSONB,
    metadata JSONB,
    custom_fields JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for users
CREATE INDEX idx_users_company_id_active ON users(company_id, is_active);
CREATE INDEX idx_users_email_company ON users(email, company_id);
CREATE INDEX idx_users_is_admin ON users(is_admin);
CREATE INDEX idx_users_last_login_at ON users(last_login_at);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_deleted_at ON users(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create entities table
CREATE TABLE entities (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL,
    name JSONB NOT NULL, -- Translatable field
    short_description JSONB, -- Translatable field
    description JSONB, -- Translatable field
    slug VARCHAR(255) NOT NULL,
    code VARCHAR(255),
    image_url VARCHAR(255),
    gallery JSONB,
    video_url VARCHAR(255),
    attachments JSONB,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    schedule JSONB,
    capacity INTEGER,
    min_participants INTEGER,
    max_participants INTEGER,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    pricing_tiers JSONB,
    status entity_status DEFAULT 'draft',
    visibility entity_visibility DEFAULT 'public',
    is_featured BOOLEAN DEFAULT false,
    requires_approval BOOLEAN DEFAULT false,
    difficulty_level VARCHAR(255),
    requirements JSONB,
    objectives JSONB,
    tags JSONB,
    view_count INTEGER DEFAULT 0,
    participant_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2),
    rating_count INTEGER DEFAULT 0,
    industry_config JSONB,
    metadata JSONB,
    custom_fields JSONB,
    created_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    updated_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for entities
CREATE INDEX idx_entities_company_id_status ON entities(company_id, status);
CREATE INDEX idx_entities_category_id_featured ON entities(category_id, is_featured);
CREATE INDEX idx_entities_slug_company ON entities(slug, company_id);
CREATE INDEX idx_entities_start_end_date ON entities(start_date, end_date);
CREATE INDEX idx_entities_price_currency ON entities(price, currency);
CREATE INDEX idx_entities_visibility_status ON entities(visibility, status);
CREATE INDEX idx_entities_difficulty_level ON entities(difficulty_level);
CREATE INDEX idx_entities_created_at ON entities(created_at);
CREATE INDEX idx_entities_deleted_at ON entities(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create entity_participants table
CREATE TABLE entity_participants (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(255) DEFAULT 'participant',
    status participation_status DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(entity_id, user_id)
);

-- Create indexes for entity_participants
CREATE INDEX idx_entity_participants_company_id ON entity_participants(company_id);
CREATE INDEX idx_entity_participants_entity_id ON entity_participants(entity_id);
CREATE INDEX idx_entity_participants_user_id ON entity_participants(user_id);
CREATE INDEX idx_entity_participants_status ON entity_participants(status);
CREATE INDEX idx_entity_participants_enrolled_at ON entity_participants(enrolled_at);

-- Create entity_content table
CREATE TABLE entity_content (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    title JSONB NOT NULL, -- Translatable field
    content JSONB, -- Translatable field
    content_type VARCHAR(255) DEFAULT 'text',
    sort_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT false,
    estimated_duration INTEGER,
    attachments JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for entity_content
CREATE INDEX idx_entity_content_company_id ON entity_content(company_id);
CREATE INDEX idx_entity_content_entity_id ON entity_content(entity_id);
CREATE INDEX idx_entity_content_sort_order ON entity_content(sort_order);
CREATE INDEX idx_entity_content_content_type ON entity_content(content_type);
CREATE INDEX idx_entity_content_deleted_at ON entity_content(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create events table
CREATE TABLE events (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    entity_id BIGINT REFERENCES entities(id) ON DELETE CASCADE,
    title JSONB NOT NULL, -- Translatable field
    description JSONB, -- Translatable field
    slug VARCHAR(255) NOT NULL,
    type VARCHAR(255) NOT NULL,
    start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    end_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    timezone VARCHAR(255) DEFAULT 'UTC',
    all_day BOOLEAN DEFAULT false,
    recurrence_rule JSONB,
    recurrence_parent_id BIGINT REFERENCES events(id) ON DELETE CASCADE,
    location_type VARCHAR(255) DEFAULT 'physical',
    location TEXT,
    virtual_url VARCHAR(255),
    virtual_details JSONB,
    capacity INTEGER,
    requires_registration BOOLEAN DEFAULT false,
    registration_deadline TIMESTAMP WITH TIME ZONE,
    registration_fee DECIMAL(10,2),
    status event_status DEFAULT 'draft',
    visibility entity_visibility DEFAULT 'public',
    is_featured BOOLEAN DEFAULT false,
    reminder_settings JSONB,
    send_notifications BOOLEAN DEFAULT true,
    last_reminder_sent TIMESTAMP WITH TIME ZONE,
    industry_config JSONB,
    metadata JSONB,
    custom_fields JSONB,
    created_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    updated_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for events
CREATE INDEX idx_events_company_id_status ON events(company_id, status);
CREATE INDEX idx_events_entity_id_start ON events(entity_id, start_datetime);
CREATE INDEX idx_events_start_end_datetime ON events(start_datetime, end_datetime);
CREATE INDEX idx_events_type_status ON events(type, status);
CREATE INDEX idx_events_slug_company ON events(slug, company_id);
CREATE INDEX idx_events_visibility ON events(visibility);
CREATE INDEX idx_events_recurrence_parent ON events(recurrence_parent_id);
CREATE INDEX idx_events_deleted_at ON events(deleted_at) WHERE deleted_at IS NOT NULL;

-- Create event_participants table
CREATE TABLE event_participants (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(255) DEFAULT 'participant',
    status participation_status DEFAULT 'pending',
    attended BOOLEAN DEFAULT false,
    notes TEXT,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(event_id, user_id)
);

-- Create indexes for event_participants
CREATE INDEX idx_event_participants_company_id ON event_participants(company_id);
CREATE INDEX idx_event_participants_event_id ON event_participants(event_id);
CREATE INDEX idx_event_participants_user_id ON event_participants(user_id);
CREATE INDEX idx_event_participants_status ON event_participants(status);
CREATE INDEX idx_event_participants_registered_at ON event_participants(registered_at);

-- Create posts table
CREATE TABLE posts (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE,
    category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL,
    author_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title JSONB NOT NULL, -- Translatable field
    content JSONB, -- Translatable field
    excerpt JSONB, -- Translatable field
    slug VARCHAR(255) NOT NULL,
    featured_image VARCHAR(255),
    gallery JSONB,
    status entity_status DEFAULT 'draft',
    visibility entity_visibility DEFAULT 'public',
    is_featured BOOLEAN DEFAULT false,
    is_pinned BOOLEAN DEFAULT false,
    published_at TIMESTAMP WITH TIME ZONE,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    tags JSONB,
    metadata JSONB,
    custom_fields JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for posts
CREATE INDEX idx_posts_company_id_status ON posts(company_id, status);
CREATE INDEX idx_posts_category_id ON posts(category_id);
CREATE INDEX idx_posts_author_id ON posts(author_id);
CREATE INDEX idx_posts_slug_company ON posts(slug, company_id);
CREATE INDEX idx_posts_published_at ON posts(published_at);
CREATE INDEX idx_posts_is_featured ON posts(is_featured);
CREATE INDEX idx_posts_is_pinned ON posts(is_pinned);
CREATE INDEX idx_posts_deleted_at ON posts(deleted_at) WHERE deleted_at IS NOT NULL;
