-- Row Level Security (RLS) Policies for Multi-Tenant Application
-- This ensures data isolation between tenants

-- Enable RLS on all tenant-scoped tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE entity_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE entity_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_field_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for users table
CREATE POLICY users_tenant_isolation ON users
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for categories table
CREATE POLICY categories_tenant_isolation ON categories
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for entities table
CREATE POLICY entities_tenant_isolation ON entities
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for entity_participants table
CREATE POLICY entity_participants_tenant_isolation ON entity_participants
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for entity_content table
CREATE POLICY entity_content_tenant_isolation ON entity_content
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for events table
CREATE POLICY events_tenant_isolation ON events
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for event_participants table
CREATE POLICY event_participants_tenant_isolation ON event_participants
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for posts table
CREATE POLICY posts_tenant_isolation ON posts
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for comments table
CREATE POLICY comments_tenant_isolation ON comments
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for roles table
CREATE POLICY roles_tenant_isolation ON roles
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin() OR
        company_id IS NULL -- System roles
    );

-- Create RLS policies for transactions table
CREATE POLICY transactions_tenant_isolation ON transactions
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for notifications table
CREATE POLICY notifications_tenant_isolation ON notifications
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for custom_fields table
CREATE POLICY custom_fields_tenant_isolation ON custom_fields
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for custom_field_values table
CREATE POLICY custom_field_values_tenant_isolation ON custom_field_values
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for assessments table
CREATE POLICY assessments_tenant_isolation ON assessments
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Create RLS policies for progress_tracking table
CREATE POLICY progress_tracking_tenant_isolation ON progress_tracking
    FOR ALL
    TO nestjs_app
    USING (
        company_id = get_current_tenant() OR 
        is_super_admin()
    );

-- Special policies for public data access (when no tenant context is set)
-- These allow public browsing of certain data

-- Public categories policy
CREATE POLICY categories_public_read ON categories
    FOR SELECT
    TO nestjs_app
    USING (
        get_current_tenant() = 0 AND 
        NOT is_super_admin()
    );

-- Public entities policy (for public browsing)
CREATE POLICY entities_public_read ON entities
    FOR SELECT
    TO nestjs_app
    USING (
        get_current_tenant() = 0 AND 
        visibility = 'public' AND 
        status = 'active'
    );

-- Public events policy (for public browsing)
CREATE POLICY events_public_read ON events
    FOR SELECT
    TO nestjs_app
    USING (
        get_current_tenant() = 0 AND 
        visibility = 'public' AND 
        status = 'published'
    );

-- Public posts policy (for public browsing)
CREATE POLICY posts_public_read ON posts
    FOR SELECT
    TO nestjs_app
    USING (
        get_current_tenant() = 0 AND 
        visibility = 'public' AND 
        status = 'active' AND
        published_at IS NOT NULL AND
        published_at <= NOW()
    );

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_entities_updated_at
    BEFORE UPDATE ON entities
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_entity_participants_updated_at
    BEFORE UPDATE ON entity_participants
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_entity_content_updated_at
    BEFORE UPDATE ON entity_content
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_events_updated_at
    BEFORE UPDATE ON events
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_event_participants_updated_at
    BEFORE UPDATE ON event_participants
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_posts_updated_at
    BEFORE UPDATE ON posts
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_comments_updated_at
    BEFORE UPDATE ON comments
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_permissions_updated_at
    BEFORE UPDATE ON permissions
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON roles
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_transactions_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_custom_fields_updated_at
    BEFORE UPDATE ON custom_fields
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_custom_field_values_updated_at
    BEFORE UPDATE ON custom_field_values
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_assessments_updated_at
    BEFORE UPDATE ON assessments
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER update_progress_tracking_updated_at
    BEFORE UPDATE ON progress_tracking
    FOR EACH ROW
    EXECUTE FUNCTION audit_trigger_function();

-- Grant necessary permissions to the application user
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO nestjs_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO nestjs_app;

-- Log RLS setup completion
DO $$
BEGIN
    RAISE NOTICE 'Row Level Security policies and triggers created successfully';
END
$$;
