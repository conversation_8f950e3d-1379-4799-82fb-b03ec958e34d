-- Seed Data for Multi-Tenant NestJS Application
-- Initial data for development and testing

-- Insert default permissions
INSERT INTO permissions (name, guard_name) VALUES
-- User management
('users.view', 'web'),
('users.create', 'web'),
('users.edit', 'web'),
('users.delete', 'web'),
('users.impersonate', 'web'),

-- Company management
('companies.view', 'web'),
('companies.create', 'web'),
('companies.edit', 'web'),
('companies.delete', 'web'),
('companies.switch', 'web'),

-- Entity management
('entities.view', 'web'),
('entities.create', 'web'),
('entities.edit', 'web'),
('entities.delete', 'web'),
('entities.publish', 'web'),

-- Event management
('events.view', 'web'),
('events.create', 'web'),
('events.edit', 'web'),
('events.delete', 'web'),
('events.manage_participants', 'web'),

-- Category management
('categories.view', 'web'),
('categories.create', 'web'),
('categories.edit', 'web'),
('categories.delete', 'web'),

-- Post management
('posts.view', 'web'),
('posts.create', 'web'),
('posts.edit', 'web'),
('posts.delete', 'web'),
('posts.publish', 'web'),

-- Role and permission management
('roles.view', 'web'),
('roles.create', 'web'),
('roles.edit', 'web'),
('roles.delete', 'web'),
('permissions.view', 'web'),
('permissions.assign', 'web'),

-- System administration
('system.view_dashboard', 'web'),
('system.view_analytics', 'web'),
('system.manage_settings', 'web'),
('system.view_audit_logs', 'web'),
('system.manage_integrations', 'web'),

-- Custom fields
('custom_fields.view', 'web'),
('custom_fields.create', 'web'),
('custom_fields.edit', 'web'),
('custom_fields.delete', 'web'),

-- Notifications
('notifications.view', 'web'),
('notifications.create', 'web'),
('notifications.send', 'web'),
('notifications.delete', 'web');

-- Insert system roles (no company_id for global roles)
INSERT INTO roles (name, guard_name, display_name, description, is_system, level) VALUES
('super-admin', 'web', 'Super Administrator', 'Full system access across all tenants', true, 100),
('system-admin', 'web', 'System Administrator', 'System-wide administration', true, 90);

-- Insert demo company
INSERT INTO companies (
    uuid, 
    name, 
    slug, 
    description, 
    email, 
    phone, 
    website, 
    industry, 
    status,
    settings,
    branding,
    features,
    license_tier,
    license_expires_at,
    limits
) VALUES (
    uuid_generate_v4(),
    'Demo Company',
    'demo-company',
    'A demonstration company for testing the multi-tenant application',
    '<EMAIL>',
    '******-0123',
    'https://demo-company.com',
    'technology',
    'active',
    '{"timezone": "UTC", "language": "en", "date_format": "Y-m-d"}',
    '{"primary_color": "#3B82F6", "secondary_color": "#10B981", "logo_url": null}',
    '["multi_company", "advanced_reporting", "integrations", "custom_fields", "webhooks", "api_access", "mobile_app", "advanced_permissions", "audit_logs", "real_time_notifications", "file_storage", "email_templates"]',
    'professional',
    '2025-12-31 23:59:59',
    '{"users": 100, "entities": 500, "storage_gb": 10}'
);

-- Get the demo company ID for further inserts
DO $$
DECLARE
    demo_company_id BIGINT;
BEGIN
    SELECT id INTO demo_company_id FROM companies WHERE slug = 'demo-company';
    
    -- Insert company-specific roles
    INSERT INTO roles (company_id, name, guard_name, display_name, description, is_system, level) VALUES
    (demo_company_id, 'admin', 'web', 'Administrator', 'Company administrator with full access', false, 80),
    (demo_company_id, 'manager', 'web', 'Manager', 'Manager with limited administrative access', false, 60),
    (demo_company_id, 'instructor', 'web', 'Instructor', 'Can create and manage content', false, 40),
    (demo_company_id, 'user', 'web', 'User', 'Regular user with basic access', false, 20);
    
    -- Insert demo admin user
    INSERT INTO users (
        uuid,
        company_id,
        name,
        email,
        email_verified_at,
        password,
        is_admin,
        is_active,
        language,
        timezone,
        preferences
    ) VALUES (
        uuid_generate_v4(),
        demo_company_id,
        'Demo Administrator',
        '<EMAIL>',
        NOW(),
        '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- password: password123
        true,
        true,
        'en',
        'UTC',
        '{"theme": "light", "notifications": {"email": true, "push": true}}'
    );
    
    -- Insert demo regular user
    INSERT INTO users (
        uuid,
        company_id,
        name,
        email,
        email_verified_at,
        password,
        is_admin,
        is_active,
        language,
        timezone
    ) VALUES (
        uuid_generate_v4(),
        demo_company_id,
        'Demo User',
        '<EMAIL>',
        NOW(),
        '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- password: password123
        false,
        true,
        'en',
        'UTC'
    );
    
    -- Insert demo categories
    INSERT INTO categories (
        uuid,
        company_id,
        name,
        description,
        slug,
        icon,
        color,
        sort_order,
        is_active
    ) VALUES 
    (
        uuid_generate_v4(),
        demo_company_id,
        '{"en": "Technology", "es": "Tecnología"}',
        '{"en": "Technology-related content", "es": "Contenido relacionado con tecnología"}',
        'technology',
        'laptop',
        '#3B82F6',
        1,
        true
    ),
    (
        uuid_generate_v4(),
        demo_company_id,
        '{"en": "Business", "es": "Negocios"}',
        '{"en": "Business and management content", "es": "Contenido de negocios y gestión"}',
        'business',
        'briefcase',
        '#10B981',
        2,
        true
    ),
    (
        uuid_generate_v4(),
        demo_company_id,
        '{"en": "Training", "es": "Entrenamiento"}',
        '{"en": "Training and development content", "es": "Contenido de entrenamiento y desarrollo"}',
        'training',
        'academic-cap',
        '#F59E0B',
        3,
        true
    );
    
    -- Insert demo entities
    INSERT INTO entities (
        uuid,
        company_id,
        category_id,
        name,
        short_description,
        description,
        slug,
        status,
        visibility,
        is_featured,
        price,
        currency,
        difficulty_level,
        created_by
    ) VALUES 
    (
        uuid_generate_v4(),
        demo_company_id,
        (SELECT id FROM categories WHERE company_id = demo_company_id AND slug = 'technology' LIMIT 1),
        '{"en": "Introduction to Web Development", "es": "Introducción al Desarrollo Web"}',
        '{"en": "Learn the basics of web development", "es": "Aprende los conceptos básicos del desarrollo web"}',
        '{"en": "A comprehensive introduction to web development covering HTML, CSS, and JavaScript fundamentals.", "es": "Una introducción completa al desarrollo web que cubre los fundamentos de HTML, CSS y JavaScript."}',
        'intro-web-development',
        'active',
        'public',
        true,
        99.99,
        'USD',
        'beginner',
        (SELECT id FROM users WHERE company_id = demo_company_id AND is_admin = true LIMIT 1)
    ),
    (
        uuid_generate_v4(),
        demo_company_id,
        (SELECT id FROM categories WHERE company_id = demo_company_id AND slug = 'business' LIMIT 1),
        '{"en": "Project Management Fundamentals", "es": "Fundamentos de Gestión de Proyectos"}',
        '{"en": "Master the art of project management", "es": "Domina el arte de la gestión de proyectos"}',
        '{"en": "Learn essential project management skills and methodologies to lead successful projects.", "es": "Aprende habilidades esenciales de gestión de proyectos y metodologías para liderar proyectos exitosos."}',
        'project-management-fundamentals',
        'active',
        'public',
        false,
        149.99,
        'USD',
        'intermediate',
        (SELECT id FROM users WHERE company_id = demo_company_id AND is_admin = true LIMIT 1)
    );
    
    -- Insert demo events
    INSERT INTO events (
        uuid,
        company_id,
        entity_id,
        title,
        description,
        slug,
        type,
        start_datetime,
        end_datetime,
        timezone,
        location_type,
        location,
        capacity,
        requires_registration,
        status,
        visibility,
        created_by
    ) VALUES 
    (
        uuid_generate_v4(),
        demo_company_id,
        (SELECT id FROM entities WHERE company_id = demo_company_id AND slug = 'intro-web-development' LIMIT 1),
        '{"en": "Web Development Workshop", "es": "Taller de Desarrollo Web"}',
        '{"en": "Hands-on workshop for web development beginners", "es": "Taller práctico para principiantes en desarrollo web"}',
        'web-dev-workshop-2025',
        'workshop',
        NOW() + INTERVAL '7 days',
        NOW() + INTERVAL '7 days' + INTERVAL '3 hours',
        'UTC',
        'virtual',
        'Online via Zoom',
        50,
        true,
        'published',
        'public',
        (SELECT id FROM users WHERE company_id = demo_company_id AND is_admin = true LIMIT 1)
    );
    
    -- Insert demo posts
    INSERT INTO posts (
        uuid,
        company_id,
        category_id,
        author_id,
        title,
        content,
        excerpt,
        slug,
        status,
        visibility,
        is_featured,
        published_at
    ) VALUES 
    (
        uuid_generate_v4(),
        demo_company_id,
        (SELECT id FROM categories WHERE company_id = demo_company_id AND slug = 'technology' LIMIT 1),
        (SELECT id FROM users WHERE company_id = demo_company_id AND is_admin = true LIMIT 1),
        '{"en": "Welcome to Our Platform", "es": "Bienvenido a Nuestra Plataforma"}',
        '{"en": "We are excited to welcome you to our multi-tenant learning platform. This platform offers a comprehensive solution for organizations to manage their training and development programs.", "es": "Estamos emocionados de darte la bienvenida a nuestra plataforma de aprendizaje multi-tenant. Esta plataforma ofrece una solución integral para que las organizaciones gestionen sus programas de entrenamiento y desarrollo."}',
        '{"en": "Welcome to our comprehensive learning platform", "es": "Bienvenido a nuestra plataforma de aprendizaje integral"}',
        'welcome-to-our-platform',
        'active',
        'public',
        true,
        NOW()
    );
    
    RAISE NOTICE 'Demo data inserted successfully for company: %', demo_company_id;
END
$$;

-- Log seed data completion
DO $$
BEGIN
    RAISE NOTICE 'Seed data insertion completed successfully';
    RAISE NOTICE 'Demo company created with admin user: <EMAIL> (password: password123)';
    RAISE NOTICE 'Demo regular user created: <EMAIL> (password: password123)';
END
$$;
