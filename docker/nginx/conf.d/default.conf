# Default Server Configuration for Multi-Tenant NestJS Application

# API Server (NestJS Backend)
server {
    listen 80;
    server_name api.* *.api.* localhost;
    
    # Security
    limit_conn conn_limit_per_ip 20;
    
    # Add tenant header for backend processing
    location / {
        # Rate limiting for API
        limit_req zone=api burst=20 nodelay;
        
        # Proxy settings
        proxy_pass http://nestjs_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID $tenant_id;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://nestjs_backend/health;
        access_log off;
    }
    
    # API Documentation
    location /api/docs {
        proxy_pass http://nestjs_backend/api/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Authentication endpoints with stricter rate limiting
    location ~ ^/api/v1/auth/ {
        limit_req zone=auth burst=5 nodelay;
        
        proxy_pass http://nestjs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID $tenant_id;
    }
}

# Admin Dashboard Server (Next.js)
server {
    listen 80;
    server_name admin.* *.admin.* dashboard.* *.dashboard.*;
    
    # Security
    limit_conn conn_limit_per_ip 10;
    
    location / {
        # Rate limiting for admin
        limit_req zone=admin burst=10 nodelay;
        
        # Proxy settings
        proxy_pass http://nextjs_admin;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID $tenant_id;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Static assets caching
    location /_next/static/ {
        proxy_pass http://nextjs_admin;
        proxy_cache_valid 200 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Next.js specific paths
    location /_next/ {
        proxy_pass http://nextjs_admin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Default catch-all server
server {
    listen 80 default_server;
    server_name _;
    
    # Redirect to API documentation or admin based on path
    location / {
        if ($request_uri ~ ^/admin) {
            return 301 http://admin.$host$request_uri;
        }
        return 301 http://api.$host$request_uri;
    }
    
    # Health check for load balancer
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# File upload handling
server {
    listen 80;
    server_name files.* *.files.*;
    
    client_max_body_size 100M;
    
    location /uploads/ {
        proxy_pass http://nestjs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID $tenant_id;
        
        # Increase timeouts for file uploads
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
