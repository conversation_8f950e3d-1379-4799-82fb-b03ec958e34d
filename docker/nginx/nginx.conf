# Nginx Configuration for Multi-Tenant NestJS Application
# High-performance reverse proxy with multi-tenant routing

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimize worker connections
events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging Format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'tenant="$tenant_id" rt=$request_time';
    
    log_format detailed '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       'tenant="$tenant_id" rt=$request_time '
                       'upstream_addr=$upstream_addr '
                       'upstream_response_time=$upstream_response_time';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=admin:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=3r/s;
    
    # Connection Limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # Upstream Servers
    upstream nestjs_backend {
        least_conn;
        server nestjs-app:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream nextjs_admin {
        least_conn;
        server nextjs-admin:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # Map for tenant identification
    map $host $tenant_id {
        default "default";
        ~^(?<tenant>[^.]+)\..*$ $tenant;
    }
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Hide Nginx version
    server_tokens off;
    
    # Include additional configurations
    include /etc/nginx/conf.d/*.conf;
}
