# NestJS Multi-Tenant Application Environment Configuration
# Copy this to .env and customize for your environment

# Application Environment
NODE_ENV=development
APP_NAME="Multi-Tenant NestJS App"
APP_VERSION=1.0.0

# Server Configuration
NESTJS_PORT=3000
NEXTJS_PORT=3001
HTTP_PORT=80
HTTPS_PORT=443

# Database Configuration (PostgreSQL)
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=nestjs_app
POSTGRES_USER=nestjs
POSTGRES_PASSWORD=your_secure_postgres_password
DATABASE_URL=***************************************************************/nestjs_app

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password
REDIS_URL=redis://:your_secure_redis_password@redis:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key
JWT_REFRESH_EXPIRES_IN=30d

# Multi-Tenancy Configuration
MULTI_TENANT_ENABLED=true
TENANT_IDENTIFICATION=subdomain
DEFAULT_TENANT=demo
SUPER_ADMIN_EMAIL=<EMAIL>

# CORS Configuration
CORS_ORIGIN=http://localhost:3001,http://localhost:3000
CORS_CREDENTIALS=true

# NextAuth Configuration (for Next.js admin)
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-nextauth-secret-change-this
NEXT_PUBLIC_API_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Email Configuration (for notifications)
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USER=your_mailtrap_user
MAIL_PASS=your_mailtrap_password
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME="Multi-Tenant App"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this
CSRF_SECRET=your-csrf-secret-change-this

# Feature Flags
FEATURE_AUDIT_LOGS=true
FEATURE_REAL_TIME_NOTIFICATIONS=true
FEATURE_FILE_STORAGE=true
FEATURE_EMAIL_TEMPLATES=true
FEATURE_WEBHOOKS=true
FEATURE_API_DOCS=true

# Docker Volume Paths
DATA_PATH=./data
LOG_PATH=./logs

# Development Configuration
SWAGGER_ENABLED=true
SWAGGER_PATH=api/docs
DEBUG_MODE=true

# Production Configuration (uncomment for production)
# NODE_ENV=production
# DEBUG_MODE=false
# SWAGGER_ENABLED=false
# LOG_LEVEL=warn

# SSL Configuration (for production)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PROMETHEUS_ENABLED=false

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# Queue Configuration (using Redis)
QUEUE_REDIS_HOST=redis
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=your_secure_redis_password

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# License Configuration
LICENSE_TIER=professional
LICENSE_EXPIRES_AT=2025-12-31

# Company Limits (for multi-tenancy)
DEFAULT_USER_LIMIT=100
DEFAULT_ENTITY_LIMIT=1000
DEFAULT_STORAGE_LIMIT_GB=10
