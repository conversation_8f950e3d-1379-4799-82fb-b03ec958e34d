#!/bin/bash

# Docker Permission Fix Script
# This script helps fix Docker permission issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "Docker Permission Fix"
echo "=========================================="
echo

# Check if Docker is installed
if ! command -v docker >/dev/null 2>&1; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if user is already in docker group
if groups $USER | grep -q '\bdocker\b'; then
    print_success "User $USER is already in the docker group"
else
    print_status "Adding user $USER to the docker group..."
    print_warning "This requires sudo privileges"
    
    if sudo usermod -aG docker $USER; then
        print_success "User $USER added to docker group successfully"
        print_warning "You need to log out and log back in (or restart) for the changes to take effect"
        print_status "Alternatively, you can run: newgrp docker"
    else
        print_error "Failed to add user to docker group"
        exit 1
    fi
fi

# Check Docker daemon status
print_status "Checking Docker daemon status..."
if sudo systemctl is-active --quiet docker; then
    print_success "Docker daemon is running"
else
    print_warning "Docker daemon is not running. Starting it..."
    if sudo systemctl start docker; then
        print_success "Docker daemon started successfully"
    else
        print_error "Failed to start Docker daemon"
        exit 1
    fi
fi

# Enable Docker to start on boot
print_status "Enabling Docker to start on boot..."
if sudo systemctl enable docker; then
    print_success "Docker enabled to start on boot"
else
    print_warning "Failed to enable Docker on boot (this is not critical)"
fi

echo
print_success "Docker permission fix completed!"
echo
print_status "Next steps:"
echo "1. Log out and log back in (or run: newgrp docker)"
echo "2. Test Docker access: docker run hello-world"
echo "3. Run the setup script again: ./setup-nestjs-stack.sh"
echo
print_warning "If you don't want to log out, you can run: newgrp docker"
print_warning "Then try running Docker commands in the new shell"
