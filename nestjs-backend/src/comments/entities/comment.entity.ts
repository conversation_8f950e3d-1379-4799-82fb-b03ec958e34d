import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';

@Entity('comments')
export class Comment {
  @ApiProperty({ description: 'Comment ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Comment content' })
  @Column('text')
  content: string;

  @ApiProperty({ description: 'User ID' })
  @Column()
  userId: number;

  @ManyToOne(() => User, (user) => user.comments)
  @JoinColumn({ name: 'userId' })
  user: User;
}