import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../../companies/entities/company.entity';

@Entity('categories')
export class Category {
  @ApiProperty({ description: 'Category ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Category name' })
  @Column()
  name: string;

  @ApiProperty({ description: 'Category description' })
  @Column({ nullable: true })
  description: string;

  @ApiProperty({ description: 'Company ID' })
  @Column()
  companyId: number;

  @ManyToOne(() => Company, (company) => company.categories)
  @JoinColumn({ name: 'companyId' })
  company: Company;
}