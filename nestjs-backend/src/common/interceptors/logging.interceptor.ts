import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || '';
    const tenantId = request.tenantId || 'none';
    const userId = request.user?.id || 'anonymous';

    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = response;
        const contentLength = response.get('content-length');
        const duration = Date.now() - startTime;

        this.logger.log(
          `${method} ${url} ${statusCode} ${contentLength || 0}b - ${duration}ms - ${ip} - ${userAgent} - Tenant: ${tenantId} - User: ${userId}`,
        );
      }),
    );
  }
}
