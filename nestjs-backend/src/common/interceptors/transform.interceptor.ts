import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontex<PERSON>,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  success: boolean;
  data: T;
  message?: string;
  meta?: {
    timestamp: string;
    path: string;
    method: string;
    tenantId?: number;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

@Injectable()
export class TransformInterceptor<T>
  implements NestInterceptor<T, Response<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    const request = context.switchToHttp().getRequest();
    const { method, url, tenantId } = request;

    return next.handle().pipe(
      map((data) => {
        // If data is already in the expected format, return as is
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }

        // Handle paginated responses
        if (data && typeof data === 'object' && 'items' in data) {
          return {
            success: true,
            data: data.items,
            meta: {
              timestamp: new Date().toISOString(),
              path: url,
              method,
              tenantId,
              pagination: {
                page: data.page || 1,
                limit: data.limit || 10,
                total: data.total || 0,
                totalPages: data.totalPages || 0,
              },
            },
          };
        }

        // Standard response transformation
        return {
          success: true,
          data,
          meta: {
            timestamp: new Date().toISOString(),
            path: url,
            method,
            tenantId,
          },
        };
      }),
    );
  }
}
