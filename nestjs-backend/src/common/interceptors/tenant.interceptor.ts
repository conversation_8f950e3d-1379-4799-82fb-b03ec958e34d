import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';

import { TenantService } from '../../tenant/tenant.service';

@Injectable()
export class TenantInterceptor implements NestInterceptor {
  constructor(
    private readonly configService: ConfigService,
    private readonly tenantService: TenantService,
    private readonly dataSource: DataSource,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    
    // Skip tenant resolution for certain paths
    if (this.shouldSkipTenantResolution(request.url)) {
      return next.handle();
    }

    // Only apply tenant scoping if multi-tenancy is enabled
    if (this.configService.get('tenant.enabled')) {
      const tenantId = await this.resolveTenantId(request);
      
      if (tenantId) {
        // Set tenant context in the request
        request.tenantId = tenantId;
        
        // Set tenant context in the database session
        await this.setDatabaseTenantContext(tenantId, request.user);
      }
    }

    return next.handle();
  }

  private shouldSkipTenantResolution(url: string): boolean {
    const skipPaths = [
      '/api/health',
      '/api/docs',
      '/api/v1/auth/login',
      '/api/v1/auth/register',
      '/api/v1/auth/forgot-password',
      '/api/v1/auth/reset-password',
      '/api/v1/public',
    ];

    return skipPaths.some(path => url.startsWith(path));
  }

  private async resolveTenantId(request: any): Promise<number | null> {
    const identificationMethod = this.configService.get('tenant.identification');

    switch (identificationMethod) {
      case 'subdomain':
        return this.resolveTenantFromSubdomain(request);
      
      case 'header':
        return this.resolveTenantFromHeader(request);
      
      case 'path':
        return this.resolveTenantFromPath(request);
      
      default:
        return this.resolveTenantFromUser(request);
    }
  }

  private async resolveTenantFromSubdomain(request: any): Promise<number | null> {
    const host = request.get('host') || request.get('x-forwarded-host');
    if (!host) return null;

    // Extract subdomain
    const parts = host.split('.');
    if (parts.length < 3) return null; // No subdomain

    const subdomain = parts[0];
    if (subdomain === 'www' || subdomain === 'api' || subdomain === 'admin') {
      return null; // Skip common subdomains
    }

    // Resolve tenant by subdomain/slug
    return this.tenantService.getTenantIdBySlug(subdomain);
  }

  private resolveTenantFromHeader(request: any): number | null {
    const tenantHeader = request.get('X-Tenant-ID');
    return tenantHeader ? parseInt(tenantHeader, 10) : null;
  }

  private async resolveTenantFromPath(request: any): Promise<number | null> {
    // Extract tenant from URL path like /api/v1/tenant/{slug}/...
    const pathMatch = request.url.match(/\/api\/v1\/tenant\/([^\/]+)/);
    if (pathMatch) {
      const slug = pathMatch[1];
      return await this.tenantService.getTenantIdBySlug(slug);
    }
    return null;
  }

  private resolveTenantFromUser(request: any): number | null {
    // Get tenant from authenticated user
    return request.user?.company_id || null;
  }

  private async setDatabaseTenantContext(
    tenantId: number,
    user?: any,
  ): Promise<void> {
    try {
      const queryRunner = this.dataSource.createQueryRunner();
      
      // Set current tenant context
      await queryRunner.query(
        'SELECT set_current_tenant($1)',
        [tenantId],
      );

      // Set super admin context if applicable
      const isSuperAdmin = user?.is_admin && 
        (user?.email === this.configService.get('tenant.superAdminEmail') ||
         user?.roles?.some((role: any) => ['super-admin', 'system-admin'].includes(role.name)));

      if (isSuperAdmin) {
        await queryRunner.query(
          "SELECT set_config('app.is_super_admin', 'true', true)",
        );
      }

      await queryRunner.release();
    } catch (error) {
      console.error('Failed to set database tenant context:', error);
      // Don't throw error to avoid breaking the request
    }
  }
}
