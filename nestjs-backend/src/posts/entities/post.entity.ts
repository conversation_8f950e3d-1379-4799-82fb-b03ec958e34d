import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { Company } from '../../companies/entities/company.entity';

@Entity('posts')
export class Post {
  @ApiProperty({ description: 'Post ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Post title' })
  @Column()
  title: string;

  @ApiProperty({ description: 'Post content' })
  @Column('text')
  content: string;

  @ApiProperty({ description: 'Author ID' })
  @Column()
  authorId: number;

  @ApiProperty({ description: 'Company ID' })
  @Column()
  companyId: number;

  @ManyToOne(() => User, (user) => user.posts)
  @JoinColumn({ name: 'authorId' })
  author: User;

  @ManyToOne(() => Company, (company) => company.posts)
  @JoinColumn({ name: 'companyId' })
  company: Company;
}