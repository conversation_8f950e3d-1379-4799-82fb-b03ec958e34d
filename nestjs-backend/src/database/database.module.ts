import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DATABASE_HOST') || 'postgres',
        port: configService.get('DATABASE_PORT') || 5432,
        username: configService.get('POSTGRES_USER') || 'nestjs',
        password: configService.get('POSTGRES_PASSWORD'),
        database: configService.get('POSTGRES_DB') || 'nestjs_app',
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development' ? ['query', 'error'] : ['error'],
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
