import { registerAs } from '@nestjs/config';

export const tenantConfig = registerAs('tenant', () => ({
  enabled: process.env.MULTI_TENANT_ENABLED === 'true',
  identification: process.env.TENANT_IDENTIFICATION || 'subdomain', // subdomain, header, path
  defaultTenant: process.env.DEFAULT_TENANT || 'demo',
  superAdminEmail: process.env.SUPER_ADMIN_EMAIL || '<EMAIL>',
  
  // Company limits
  limits: {
    defaultUserLimit: parseInt(process.env.DEFAULT_USER_LIMIT, 10) || 100,
    defaultEntityLimit: parseInt(process.env.DEFAULT_ENTITY_LIMIT, 10) || 1000,
    defaultStorageLimitGb: parseInt(process.env.DEFAULT_STORAGE_LIMIT_GB, 10) || 10,
  },

  // License configuration
  license: {
    tier: process.env.LICENSE_TIER || 'professional',
    expiresAt: process.env.LICENSE_EXPIRES_AT || '2025-12-31',
  },

  // Feature flags
  features: {
    auditLogs: process.env.FEATURE_AUDIT_LOGS === 'true',
    realTimeNotifications: process.env.FEATURE_REAL_TIME_NOTIFICATIONS === 'true',
    fileStorage: process.env.FEATURE_FILE_STORAGE === 'true',
    emailTemplates: process.env.FEATURE_EMAIL_TEMPLATES === 'true',
    webhooks: process.env.FEATURE_WEBHOOKS === 'true',
    apiDocs: process.env.FEATURE_API_DOCS === 'true',
  },
}));
