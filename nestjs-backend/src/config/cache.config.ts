import { registerAs } from '@nestjs/config';

export const cacheConfig = registerAs('cache', () => ({
  redis: {
    host: process.env.REDIS_HOST || 'redis',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
    url: process.env.REDIS_URL,
  },
  ttl: parseInt(process.env.CACHE_TTL, 10) || 3600,
  max: parseInt(process.env.CACHE_MAX_ITEMS, 10) || 1000,
}));
