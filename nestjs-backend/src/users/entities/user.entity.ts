import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

import { Company } from '../../companies/entities/company.entity';
import { EntityParticipant } from '../../entities/entities/entity-participant.entity';
import { EventParticipant } from '../../events/entities/event-participant.entity';
import { Post } from '../../posts/entities/post.entity';
import { Comment } from '../../comments/entities/comment.entity';
import { Transaction } from '../../transactions/entities/transaction.entity';
import { Assessment } from '../../assessments/entities/assessment.entity';
import { ProgressTracking } from '../../progress/entities/progress-tracking.entity';
import { TenantAware } from '../../common/decorators/tenant-aware.decorator';

export enum UserGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  PREFER_NOT_TO_SAY = 'prefer_not_to_say',
}

@Entity('users')
@TenantAware()
@Index(['company_id', 'is_active'])
@Index(['email', 'company_id'])
@Index(['is_admin'])
@Index(['last_login_at'])
@Index(['created_at'])
export class User {
  @ApiProperty({ description: 'Unique identifier' })
  @PrimaryGeneratedColumn('increment')
  id: number;

  @ApiProperty({ description: 'UUID for external references' })
  @Column({ type: 'uuid', generated: 'uuid', unique: true })
  uuid: string;

  @ApiProperty({ description: 'Company/tenant ID' })
  @Column({ type: 'bigint', nullable: true })
  company_id?: number;

  @ApiProperty({ description: 'Full name' })
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @ApiProperty({ description: 'Email address' })
  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @ApiProperty({ description: 'Email verification timestamp', required: false })
  @Column({ type: 'timestamp with time zone', nullable: true })
  email_verified_at?: Date;

  @ApiProperty({ description: 'Hashed password' })
  @Exclude()
  @Column({ type: 'varchar', length: 255 })
  password: string;

  @ApiProperty({ description: 'Phone number', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  phone?: string;

  @ApiProperty({ description: 'Date of birth', required: false })
  @Column({ type: 'date', nullable: true })
  date_of_birth?: Date;

  @ApiProperty({ description: 'Gender', enum: UserGender, required: false })
  @Column({ type: 'enum', enum: UserGender, nullable: true })
  gender?: UserGender;

  @ApiProperty({ description: 'Avatar URL', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar_url?: string;

  @ApiProperty({ description: 'Whether user is an admin', default: false })
  @Column({ type: 'boolean', default: false })
  is_admin: boolean;

  @ApiProperty({ description: 'Whether user is active', default: true })
  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @ApiProperty({ description: 'User permissions', required: false })
  @Exclude()
  @Column({ type: 'jsonb', nullable: true })
  permissions?: string[];

  @ApiProperty({ description: 'User preferences', required: false })
  @Column({ type: 'jsonb', nullable: true })
  preferences?: Record<string, any>;

  @ApiProperty({ description: 'Language preference', default: 'en' })
  @Column({ type: 'varchar', length: 5, default: 'en' })
  language: string;

  @ApiProperty({ description: 'Timezone', default: 'UTC' })
  @Column({ type: 'varchar', length: 255, default: 'UTC' })
  timezone: string;

  @ApiProperty({ description: 'Remember token for authentication', required: false })
  @Exclude()
  @Column({ type: 'varchar', length: 100, nullable: true })
  remember_token?: string;

  @ApiProperty({ description: 'Last login timestamp', required: false })
  @Column({ type: 'timestamp with time zone', nullable: true })
  last_login_at?: Date;

  @ApiProperty({ description: 'Last login IP address', required: false })
  @Column({ type: 'varchar', length: 45, nullable: true })
  last_login_ip?: string;

  @ApiProperty({ description: 'Two-factor authentication enabled', default: false })
  @Column({ type: 'boolean', default: false })
  two_factor_enabled: boolean;

  @ApiProperty({ description: 'Two-factor secret', required: false })
  @Exclude()
  @Column({ type: 'text', nullable: true })
  two_factor_secret?: string;

  @ApiProperty({ description: 'Two-factor recovery codes', required: false })
  @Exclude()
  @Column({ type: 'jsonb', nullable: true })
  two_factor_recovery_codes?: string[];

  @ApiProperty({ description: 'Industry-specific profile data', required: false })
  @Column({ type: 'jsonb', nullable: true })
  profile_data?: Record<string, any>;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @Exclude()
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Custom field values', required: false })
  @Column({ type: 'jsonb', nullable: true })
  custom_fields?: Record<string, any>;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  @ApiProperty({ description: 'Deletion timestamp', required: false })
  @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
  deleted_at?: Date;

  // Relationships
  @ApiProperty({ description: 'User company', type: () => Company })
  @ManyToOne(() => Company, (company) => company.users, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'company_id' })
  company?: Company;

  @ApiProperty({ description: 'Entity participations', type: () => [EntityParticipant] })
  @OneToMany(() => EntityParticipant, (participant) => participant.user)
  entity_participants: EntityParticipant[];

  @ApiProperty({ description: 'Event participations', type: () => [EventParticipant] })
  @OneToMany(() => EventParticipant, (participant) => participant.user)
  event_participants: EventParticipant[];

  @ApiProperty({ description: 'Authored posts', type: () => [Post] })
  @OneToMany(() => Post, (post) => post.author)
  posts: Post[];

  @ApiProperty({ description: 'User comments', type: () => [Comment] })
  @OneToMany(() => Comment, (comment) => comment.user)
  comments: Comment[];

  @ApiProperty({ description: 'User transactions', type: () => [Transaction] })
  @OneToMany(() => Transaction, (transaction) => transaction.user)
  transactions: Transaction[];

  @ApiProperty({ description: 'User assessments', type: () => [Assessment] })
  @OneToMany(() => Assessment, (assessment) => assessment.user)
  assessments: Assessment[];

  @ApiProperty({ description: 'Progress tracking', type: () => [ProgressTracking] })
  @OneToMany(() => ProgressTracking, (progress) => progress.user)
  progress_tracking: ProgressTracking[];

  // Virtual properties
  @ApiProperty({ description: 'Full avatar URL' })
  get avatar_url_full(): string {
    if (this.avatar_url) {
      if (this.avatar_url.startsWith('http://') || this.avatar_url.startsWith('https://')) {
        return this.avatar_url;
      }
      return `/uploads/avatars/${this.avatar_url}`;
    }
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(this.name)}&color=7F9CF5&background=EBF4FF`;
  }

  @ApiProperty({ description: 'User initials' })
  get initials(): string {
    const names = this.name.split(' ');
    let initials = '';
    for (const name of names) {
      initials += name.charAt(0).toUpperCase();
      if (initials.length >= 2) break;
    }
    return initials || 'U';
  }

  @ApiProperty({ description: 'Display name' })
  get display_name(): string {
    return this.name;
  }

  // Methods
  getPreference(key: string, defaultValue?: any): any {
    return this.preferences?.[key] ?? defaultValue;
  }

  setPreference(key: string, value: any): void {
    this.preferences = { ...this.preferences, [key]: value };
  }

  getProfileData(key: string, defaultValue?: any): any {
    return this.profile_data?.[key] ?? defaultValue;
  }

  setProfileData(key: string, value: any): void {
    this.profile_data = { ...this.profile_data, [key]: value };
  }

  hasDirectPermission(permission: string): boolean {
    return this.permissions?.includes(permission) ?? false;
  }

  updateLastLogin(ip?: string): void {
    this.last_login_at = new Date();
    this.last_login_ip = ip;
  }
}
