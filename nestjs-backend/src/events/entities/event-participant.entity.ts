import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, <PERSON>inColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { Event } from './event.entity';

@Entity('event_participants')
export class EventParticipant {
  @ApiProperty({ description: 'Participant ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'User ID' })
  @Column()
  userId: number;

  @ApiProperty({ description: 'Event ID' })
  @Column()
  eventId: number;

  @ManyToOne(() => User, (user) => user.event_participants)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Event)
  @JoinColumn({ name: 'eventId' })
  event: Event;
}