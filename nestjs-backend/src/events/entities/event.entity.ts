import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../../companies/entities/company.entity';

@Entity('events')
export class Event {
  @ApiProperty({ description: 'Event ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Event title' })
  @Column()
  title: string;

  @ApiProperty({ description: 'Event description' })
  @Column({ nullable: true })
  description: string;

  @ApiProperty({ description: 'Event date' })
  @Column()
  eventDate: Date;

  @ApiProperty({ description: 'Company ID' })
  @Column()
  companyId: number;

  @ManyToOne(() => Company, (company) => company.events)
  @JoinColumn({ name: 'companyId' })
  company: Company;
}