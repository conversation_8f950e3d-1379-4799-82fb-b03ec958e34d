import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

import { Company } from '../companies/entities/company.entity';

@Injectable()
export class TenantService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
  ) {}

  /**
   * Get tenant ID by slug with caching
   */
  async getTenantIdBySlug(slug: string): Promise<number | null> {
    const cacheKey = `tenant:slug:${slug}`;
    
    // Try to get from cache first
    let tenantId = await this.cacheManager.get<number>(cacheKey);
    
    if (tenantId === undefined) {
      // Not in cache, query database
      const company = await this.companyRepository.findOne({
        where: { slug, status: 'active' },
        select: ['id'],
      });
      
      tenantId = company?.id || null;
      
      // Cache the result (cache null values too to avoid repeated queries)
      await this.cacheManager.set(cacheKey, tenantId, 300); // 5 minutes
    }
    
    return tenantId;
  }

  /**
   * Get tenant by ID with caching
   */
  async getTenantById(id: number): Promise<Company | null> {
    const cacheKey = `tenant:id:${id}`;
    
    // Try to get from cache first
    let company = await this.cacheManager.get<Company>(cacheKey);
    
    if (!company) {
      // Not in cache, query database
      company = await this.companyRepository.findOne({
        where: { id, status: 'active' },
      });
      
      if (company) {
        // Cache the result
        await this.cacheManager.set(cacheKey, company, 300); // 5 minutes
      }
    }
    
    return company || null;
  }

  /**
   * Get tenant by domain with caching
   */
  async getTenantByDomain(domain: string): Promise<Company | null> {
    const cacheKey = `tenant:domain:${domain}`;
    
    // Try to get from cache first
    let company = await this.cacheManager.get<Company>(cacheKey);
    
    if (!company) {
      // Not in cache, query database
      company = await this.companyRepository.findOne({
        where: { domain, status: 'active' },
      });
      
      if (company) {
        // Cache the result
        await this.cacheManager.set(cacheKey, company, 300); // 5 minutes
      }
    }
    
    return company || null;
  }

  /**
   * Validate tenant access for user
   */
  async validateTenantAccess(tenantId: number, userId: number): Promise<boolean> {
    const cacheKey = `tenant:access:${tenantId}:${userId}`;
    
    // Try to get from cache first
    let hasAccess = await this.cacheManager.get<boolean>(cacheKey);
    
    if (hasAccess === undefined) {
      // Check if user belongs to the tenant
      const company = await this.companyRepository.findOne({
        where: { id: tenantId },
        relations: ['users'],
      });
      
      hasAccess = company?.users?.some(user => user.id === userId) || false;
      
      // Cache the result
      await this.cacheManager.set(cacheKey, hasAccess, 60); // 1 minute
    }
    
    return hasAccess;
  }

  /**
   * Clear tenant cache
   */
  async clearTenantCache(tenantId: number, slug?: string, domain?: string): Promise<void> {
    const keysToDelete = [
      `tenant:id:${tenantId}`,
    ];
    
    if (slug) {
      keysToDelete.push(`tenant:slug:${slug}`);
    }
    
    if (domain) {
      keysToDelete.push(`tenant:domain:${domain}`);
    }
    
    // Clear access cache for all users (this is a simplified approach)
    // In a real implementation, you might want to track user-tenant relationships more efficiently
    
    for (const key of keysToDelete) {
      await this.cacheManager.del(key);
    }
  }

  /**
   * Get all active tenants (for system admin)
   */
  async getAllActiveTenants(): Promise<Company[]> {
    return this.companyRepository.find({
      where: { status: 'active' },
      order: { name: 'ASC' },
    });
  }

  /**
   * Check if tenant exists and is active
   */
  async isTenantActive(tenantId: number): Promise<boolean> {
    const company = await this.getTenantById(tenantId);
    return company?.status === 'active';
  }

  /**
   * Get tenant settings
   */
  async getTenantSettings(tenantId: number): Promise<Record<string, any> | null> {
    const company = await this.getTenantById(tenantId);
    return company?.settings || null;
  }

  /**
   * Get tenant features
   */
  async getTenantFeatures(tenantId: number): Promise<string[]> {
    const company = await this.getTenantById(tenantId);
    return company?.features || [];
  }

  /**
   * Check if tenant has feature enabled
   */
  async hasTenantFeature(tenantId: number, feature: string): Promise<boolean> {
    const features = await this.getTenantFeatures(tenantId);
    return features.includes(feature);
  }

  /**
   * Get tenant limits
   */
  async getTenantLimits(tenantId: number): Promise<Record<string, any> | null> {
    const company = await this.getTenantById(tenantId);
    return company?.limits || null;
  }

  /**
   * Check if tenant is within limits for a specific resource
   */
  async isWithinTenantLimit(
    tenantId: number,
    resource: string,
    currentUsage: number,
  ): Promise<boolean> {
    const limits = await this.getTenantLimits(tenantId);
    const limit = limits?.[resource];
    
    if (!limit) {
      return true; // No limit set
    }
    
    return currentUsage < limit;
  }
}
