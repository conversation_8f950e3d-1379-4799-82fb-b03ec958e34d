import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

import { User } from '../../users/entities/user.entity';
import { Category } from '../../categories/entities/category.entity';
import { EntityModel } from '../../entities/entities/entity.entity';
import { Event } from '../../events/entities/event.entity';
import { Post } from '../../posts/entities/post.entity';

export enum CompanyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

@Entity('companies')
@Index(['status', 'industry'])
@Index(['license_tier'])
@Index(['created_at'])
export class Company {
  @ApiProperty({ description: 'Unique identifier' })
  @PrimaryGeneratedColumn('increment')
  id: number;

  @ApiProperty({ description: 'UUID for external references' })
  @Column({ type: 'uuid', generated: 'uuid', unique: true })
  uuid: string;

  @ApiProperty({ description: 'Company name' })
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @ApiProperty({ description: 'Company domain', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  domain?: string;

  @ApiProperty({ description: 'URL-friendly slug' })
  @Column({ type: 'varchar', length: 255, unique: true })
  slug: string;

  @ApiProperty({ description: 'Company description', required: false })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: 'Contact email', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  email?: string;

  @ApiProperty({ description: 'Contact phone', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  phone?: string;

  @ApiProperty({ description: 'Company website', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  website?: string;

  @ApiProperty({ description: 'Logo URL', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  logo_url?: string;

  @ApiProperty({ description: 'Industry type', default: 'generic' })
  @Column({ type: 'varchar', length: 255, default: 'generic' })
  industry: string;

  @ApiProperty({ description: 'Company status', enum: CompanyStatus })
  @Column({ type: 'enum', enum: CompanyStatus, default: CompanyStatus.ACTIVE })
  status: CompanyStatus;

  @ApiProperty({ description: 'Company settings', required: false })
  @Column({ type: 'jsonb', nullable: true })
  settings?: Record<string, any>;

  @ApiProperty({ description: 'Branding configuration', required: false })
  @Exclude()
  @Column({ type: 'jsonb', nullable: true })
  branding?: Record<string, any>;

  @ApiProperty({ description: 'Enabled features', required: false })
  @Column({ type: 'jsonb', nullable: true })
  features?: string[];

  @ApiProperty({ description: 'License tier', default: 'basic' })
  @Column({ type: 'varchar', length: 255, default: 'basic' })
  license_tier: string;

  @ApiProperty({ description: 'License expiration date', required: false })
  @Column({ type: 'timestamp with time zone', nullable: true })
  license_expires_at?: Date;

  @ApiProperty({ description: 'Usage limits', required: false })
  @Exclude()
  @Column({ type: 'jsonb', nullable: true })
  limits?: Record<string, any>;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @Exclude()
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  @ApiProperty({ description: 'Deletion timestamp', required: false })
  @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
  deleted_at?: Date;

  // Relationships
  @ApiProperty({ description: 'Company users', type: () => [User] })
  @OneToMany(() => User, (user) => user.company)
  users: User[];

  @ApiProperty({ description: 'Company categories', type: () => [Category] })
  @OneToMany(() => Category, (category) => category.company)
  categories: Category[];

  @ApiProperty({ description: 'Company entities', type: () => [EntityModel] })
  @OneToMany(() => EntityModel, (entity) => entity.company)
  entities: EntityModel[];

  @ApiProperty({ description: 'Company events', type: () => [Event] })
  @OneToMany(() => Event, (event) => event.company)
  events: Event[];

  @ApiProperty({ description: 'Company posts', type: () => [Post] })
  @OneToMany(() => Post, (post) => post.company)
  posts: Post[];

  // Virtual properties
  @ApiProperty({ description: 'Whether company is active' })
  get is_active(): boolean {
    return this.status === CompanyStatus.ACTIVE;
  }

  @ApiProperty({ description: 'Display name for the company' })
  get display_name(): string {
    return this.name;
  }

  // Methods
  activate(): void {
    this.status = CompanyStatus.ACTIVE;
  }

  deactivate(): void {
    this.status = CompanyStatus.INACTIVE;
  }

  suspend(): void {
    this.status = CompanyStatus.SUSPENDED;
  }

  getSetting(key: string, defaultValue?: any): any {
    return this.settings?.[key] ?? defaultValue;
  }

  setSetting(key: string, value: any): void {
    this.settings = { ...this.settings, [key]: value };
  }

  hasFeature(feature: string): boolean {
    return this.features?.includes(feature) ?? false;
  }

  isWithinLimit(type: string): boolean {
    const limit = this.limits?.[type];
    if (!limit) return true;

    // This would be implemented based on actual usage tracking
    // For now, return true
    return true;
  }
}
