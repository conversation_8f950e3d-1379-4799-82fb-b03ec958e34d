import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../../companies/entities/company.entity';

@Entity('entities')
export class EntityModel {
  @ApiProperty({ description: 'Entity ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Entity name' })
  @Column()
  name: string;

  @ApiProperty({ description: 'Entity type' })
  @Column()
  type: string;

  @ApiProperty({ description: 'Company ID' })
  @Column()
  companyId: number;

  @ManyToOne(() => Company, (company) => company.entities)
  @JoinColumn({ name: 'companyId' })
  company: Company;
}