import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, <PERSON>inColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { EntityModel } from './entity.entity';

@Entity('entity_participants')
export class EntityParticipant {
  @ApiProperty({ description: 'Participant ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'User ID' })
  @Column()
  userId: number;

  @ApiProperty({ description: 'Entity ID' })
  @Column()
  entityId: number;

  @ManyToOne(() => User, (user) => user.entity_participants)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => EntityModel)
  @JoinColumn({ name: 'entityId' })
  entity: EntityModel;
}