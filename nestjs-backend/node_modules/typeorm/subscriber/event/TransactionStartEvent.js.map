{"version": 3, "sources": ["../../src/subscriber/event/TransactionStartEvent.ts"], "names": [], "mappings": "", "file": "TransactionStartEvent.js", "sourcesContent": ["import { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\n\n/**\n * TransactionStartEvent is an object that broadcaster sends to the entity subscriber before transaction is started.\n */\nexport interface TransactionStartEvent {\n    /**\n     * Connection used in the event.\n     */\n    connection: DataSource\n\n    /**\n     * QueryRunner used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this query runner instance.\n     */\n    queryRunner: QueryRunner\n\n    /**\n     * EntityManager used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this entity manager instance.\n     */\n    manager: EntityManager\n}\n"], "sourceRoot": "../.."}