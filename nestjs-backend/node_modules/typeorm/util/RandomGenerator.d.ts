export declare class RandomGenerator {
    /**
     *  discuss at: http://locutus.io/php/sha1/
     * original by: Webtoolkit.info (http://www.webtoolkit.info/)
     * improved by: <PERSON> (http://getsprink.com)
     * improved by: <PERSON> (http://kvz.io)
     *    input by: <PERSON> (http://brett-zamir.me)
     *      note 1: Keep in mind that in accordance with PHP, the whole string is buffered and then
     *      note 1: hashed. If available, we'd recommend using Node's native crypto modules directly
     *      note 1: in a steaming fashion for faster and more efficient hashing
     *   example 1: sha1('<PERSON>')
     *   returns 1: '54916d2e62f65b3afa6e192e6a601cdbe5cb5897'
     */
    static sha1(str: string): string;
}
