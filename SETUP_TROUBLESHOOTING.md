# Setup Troubleshooting Guide

This guide helps resolve common issues during the NestJS multi-tenant stack setup.

## Quick Fix Commands

### 1. Fix Docker Permissions
```bash
# Run the Docker permission fix script
./fix-docker-permissions.sh

# Or manually:
sudo usermod -aG docker $USER
sudo systemctl start docker
sudo systemctl enable docker

# Then either:
# Option A: Log out and log back in
# Option B: Run in current session
newgrp docker
```

### 2. Fix Setup Script Issues
```bash
# If the setup script fails, try running it again
./setup-nestjs-stack.sh

# Or run individual steps manually
```

## Common Issues and Solutions

### Issue 1: Docker Permission Denied
**Error**: `permission denied while trying to connect to the Docker daemon socket`

**Solution**:
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Start Docker service
sudo systemctl start docker

# Enable Docker on boot
sudo systemctl enable docker

# Apply group changes (choose one):
# Option A: Log out and back in
# Option B: Use newgrp
newgrp docker

# Test Docker access
docker run hello-world
```

### Issue 2: Sed Command Error
**Error**: `sed: -e expression #1, char 104: unterminated 's' command`

**Solution**: This has been fixed in the updated script. The issue was with special characters in generated passwords. The script now uses `|` as delimiter instead of `/`.

### Issue 3: Docker Compose Version Warning
**Error**: `the attribute 'version' is obsolete`

**Solution**: This has been fixed by removing the version field from docker-compose.nestjs.yml.

### Issue 4: Port Already in Use
**Error**: `port is already allocated`

**Solution**:
```bash
# Check what's using the ports
sudo netstat -tulpn | grep :3000
sudo netstat -tulpn | grep :5432
sudo netstat -tulpn | grep :6379

# Stop conflicting services
sudo systemctl stop postgresql  # If PostgreSQL is running locally
sudo systemctl stop redis       # If Redis is running locally

# Or change ports in .env file
```

### Issue 5: Database Connection Failed
**Error**: `connection refused` or `authentication failed`

**Solution**:
```bash
# Check if PostgreSQL container is running
docker ps | grep postgres

# Check PostgreSQL logs
docker logs nestjs_postgres

# Reset database if needed
docker-compose -f docker-compose.nestjs.yml down -v
docker-compose -f docker-compose.nestjs.yml up -d postgres

# Wait for database to be ready
docker-compose -f docker-compose.nestjs.yml logs -f postgres
```

### Issue 6: Redis Connection Failed
**Error**: `Redis connection failed`

**Solution**:
```bash
# Check if Redis container is running
docker ps | grep redis

# Check Redis logs
docker logs nestjs_redis

# Test Redis connection
docker exec -it nestjs_redis redis-cli ping
```

### Issue 7: Node.js/NPM Issues
**Error**: `npm not found` or `node version mismatch`

**Solution**:
```bash
# Install Node.js 20 (recommended)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Or use nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 20
nvm use 20
```

## Step-by-Step Manual Setup

If the automated scripts fail, you can set up manually:

### 1. Fix Docker Permissions
```bash
sudo usermod -aG docker $USER
sudo systemctl start docker
sudo systemctl enable docker
newgrp docker
```

### 2. Create Directories
```bash
mkdir -p data/{postgres,redis}
mkdir -p logs/{postgres,redis,nginx}
mkdir -p uploads
mkdir -p nestjs-backend
mkdir -p nextjs-admin
```

### 3. Set Up Environment
```bash
cp .env.nestjs .env
# Edit .env file with your preferred editor
nano .env
```

### 4. Generate Secure Passwords
```bash
# Generate passwords manually
POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)

echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD"
echo "REDIS_PASSWORD=$REDIS_PASSWORD"
echo "JWT_SECRET=$JWT_SECRET"

# Update .env file manually
```

### 5. Start Services
```bash
# Start infrastructure only
docker-compose -f docker-compose.nestjs.yml up -d postgres redis

# Wait for services to be ready
sleep 30

# Check service health
docker-compose -f docker-compose.nestjs.yml ps
```

### 6. Set Up NestJS Backend
```bash
cd nestjs-backend
npm install
cd ..
```

## Verification Steps

### 1. Check Docker Services
```bash
# List running containers
docker ps

# Check service health
docker-compose -f docker-compose.nestjs.yml ps

# Check logs
docker-compose -f docker-compose.nestjs.yml logs
```

### 2. Test Database Connection
```bash
# Connect to PostgreSQL
docker exec -it nestjs_postgres psql -U nestjs -d nestjs_app

# In PostgreSQL shell:
\l                          # List databases
\dt                         # List tables
SELECT * FROM companies;    # Test query
\q                          # Quit
```

### 3. Test Redis Connection
```bash
# Connect to Redis
docker exec -it nestjs_redis redis-cli

# In Redis shell:
ping                        # Should return PONG
set test "hello"           # Set a test key
get test                   # Should return "hello"
exit                       # Quit
```

### 4. Test NestJS Application
```bash
cd nestjs-backend
npm run start:dev

# In another terminal:
curl http://localhost:3000/health
```

## Getting Help

If you're still having issues:

1. **Check the logs**:
   ```bash
   docker-compose -f docker-compose.nestjs.yml logs -f
   ```

2. **Verify your environment**:
   ```bash
   cat .env | grep -v PASSWORD  # Show env without passwords
   ```

3. **Check system resources**:
   ```bash
   docker system df           # Docker disk usage
   docker system prune        # Clean up if needed
   ```

4. **Reset everything**:
   ```bash
   # Nuclear option - removes all containers and volumes
   docker-compose -f docker-compose.nestjs.yml down -v
   docker system prune -a
   ./setup-nestjs-stack.sh
   ```

## Next Steps After Successful Setup

1. **Verify all services are running**:
   ```bash
   docker-compose -f docker-compose.nestjs.yml ps
   ```

2. **Access the services**:
   - PostgreSQL: `localhost:5432`
   - Redis: `localhost:6379`
   - API (when running): `localhost:3000`

3. **Set up the NestJS backend**:
   ```bash
   ./setup-nestjs-backend.sh
   ```

4. **Start development**:
   ```bash
   cd nestjs-backend
   npm run start:dev
   ```

Remember: The most common issue is Docker permissions. Make sure to run `newgrp docker` or log out/in after adding your user to the docker group!
