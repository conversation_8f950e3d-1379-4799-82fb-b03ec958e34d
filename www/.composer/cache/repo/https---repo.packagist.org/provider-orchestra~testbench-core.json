{"minified": "composer/2.0", "packages": {"orchestra/testbench-core": [{"name": "orchestra/testbench-core", "description": "Testing Helper for Laravel Development", "keywords": ["testing", "BDD", "TDD", "laravel", "dev", "laravel-packages"], "homepage": "https://packages.tools/testbench", "version": "v10.4.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/crynobone"}], "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d1c45a7be15c4d99fb7d48685b038dd39acc7b84"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d1c45a7be15c4d99fb7d48685b038dd39acc7b84", "type": "zip", "shasum": "", "reference": "d1c45a7be15c4d99fb7d48685b038dd39acc7b84"}, "type": "library", "support": {"issues": "https://github.com/orchestral/testbench/issues", "source": "https://github.com/orchestral/testbench-core"}, "funding": [], "time": "2025-06-08T04:36:36+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Orchestra\\Testbench\\": "src/"}}, "bin": ["testbench"], "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "~1.1.16|^1.2.12", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^12.8.0", "laravel/pint": "^1.22", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1.14", "phpunit/phpunit": "^10.5.35|^11.5.3|^12.0.1", "spatie/laravel-ray": "^1.40.2", "symfony/process": "^7.2.0", "symfony/yaml": "^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^12.8.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^10.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5.35|^11.5.3|^12.0.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.2).", "symfony/yaml": "Required for Testbench CLI (^7.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.6.1)."}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<12.8.0|>=13.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.5.3|12.0.0|>=12.3.0"}}, {"version": "v10.3.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3cacf7bc512665b9eb6d2380bb4433574458a557"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3cacf7bc512665b9eb6d2380bb4433574458a557", "type": "zip", "shasum": "", "reference": "3cacf7bc512665b9eb6d2380bb4433574458a557"}, "time": "2025-05-12T05:41:57+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5|^1.2.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<12.8.0|>=13.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.5.3|12.0.0|>=12.2.0"}}, {"version": "v10.2.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "810216133b8917ea56b003306a532f785a136e05"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/810216133b8917ea56b003306a532f785a136e05", "type": "zip", "shasum": "", "reference": "810216133b8917ea56b003306a532f785a136e05"}, "time": "2025-05-06T23:25:17+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.2.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^12.3.0", "laravel/pint": "^1.22", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1.14", "phpunit/phpunit": "^10.5.35|^11.5.3|^12.0.1", "spatie/laravel-ray": "^1.40.2", "symfony/process": "^7.2.0", "symfony/yaml": "^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^12.3.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^10.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5.35|^11.5.3|^12.0.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.2).", "symfony/yaml": "Required for Testbench CLI (^7.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.6.1)."}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<12.3.0|>=13.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.5.3|12.0.0|>=12.2.0"}}, {"version": "v10.2.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "beed0fa81c3bb37b67e348d0963a33c5f1933fe5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/beed0fa81c3bb37b67e348d0963a33c5f1933fe5", "type": "zip", "shasum": "", "reference": "beed0fa81c3bb37b67e348d0963a33c5f1933fe5"}, "time": "2025-04-27T05:36:02+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^12.1.1", "laravel/pint": "^1.22", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1.12", "phpunit/phpunit": "^10.5.35|^11.5.3|^12.0.1", "spatie/laravel-ray": "^1.40.2", "symfony/process": "^7.2.0", "symfony/yaml": "^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^12.1.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^10.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5.35|^11.5.3|^12.0.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.2).", "symfony/yaml": "Required for Testbench CLI (^7.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.6.1)."}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<12.1.1|>=13.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.5.3|12.0.0|>=12.2.0"}}, {"version": "v10.2.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "87f7e0e08e601756a444d7ac48027656ebeacf01"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/87f7e0e08e601756a444d7ac48027656ebeacf01", "type": "zip", "shasum": "", "reference": "87f7e0e08e601756a444d7ac48027656ebeacf01"}, "time": "2025-04-13T01:12:52+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^12.1.1", "laravel/pint": "^1.21", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35|^11.5.3|^12.0.1", "spatie/laravel-ray": "^1.39.1", "symfony/process": "^7.2.0", "symfony/yaml": "^7.2.0", "vlucas/phpdotenv": "^5.6.1"}}, {"version": "v10.2.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d149593f8023f0294cece81593903a4c61be61da"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d149593f8023f0294cece81593903a4c61be61da", "type": "zip", "shasum": "", "reference": "d149593f8023f0294cece81593903a4c61be61da"}, "time": "2025-04-06T06:26:48+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}}, {"version": "v10.1.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1ce476ab753a235958fb0b16ea78ca8fe815d47d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1ce476ab753a235958fb0b16ea78ca8fe815d47d", "type": "zip", "shasum": "", "reference": "1ce476ab753a235958fb0b16ea78ca8fe815d47d"}, "time": "2025-03-06T10:17:18+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^12.1.1", "laravel/pint": "^1.21", "laravel/serializable-closure": "^1.3|^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35|^11.5.3|^12.0.1", "spatie/laravel-ray": "^1.39.1", "symfony/process": "^7.2.0", "symfony/yaml": "^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<12.1.1|>=13.0.0", "laravel/serializable-closure": "<1.3.0|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.5.3|12.0.0|>=12.1.0"}}, {"version": "v10.0.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "318bef53826b3c652fd4a41c2f1004526039aa0a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/318bef53826b3c652fd4a41c2f1004526039aa0a", "type": "zip", "shasum": "", "reference": "318bef53826b3c652fd4a41c2f1004526039aa0a"}, "time": "2025-03-03T15:48:18+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.3", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^12.0.0", "laravel/pint": "^1.21", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35 || ^11.5.3 || ^12.0.1", "spatie/laravel-ray": "^1.39.1", "symfony/process": "^7.2.0", "symfony/yaml": "^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^12.0.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0 || ^12.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.2).", "symfony/yaml": "Required for Testbench CLI (^7.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<12.0.0 || >=13.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.5.3 || 12.0.0 || >=12.1.0"}}, {"version": "v10.0.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4a412b377ab8c616fc4c239f50bf82e4092b859b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4a412b377ab8c616fc4c239f50bf82e4092b859b", "type": "zip", "shasum": "", "reference": "4a412b377ab8c616fc4c239f50bf82e4092b859b"}, "time": "2025-02-25T09:32:21+00:00"}, {"version": "v10.0.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2bc1322bd0fa6c7bbbd2f1c04d0dab8c935bd8e1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2bc1322bd0fa6c7bbbd2f1c04d0dab8c935bd8e1", "type": "zip", "shasum": "", "reference": "2bc1322bd0fa6c7bbbd2f1c04d0dab8c935bd8e1"}, "time": "2025-02-24T14:28:17+00:00"}, {"version": "v10.0.0", "version_normalized": "10.0.0.0", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6544a24b2dcbe942e670675d70120311b3f4e343"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6544a24b2dcbe942e670675d70120311b3f4e343", "type": "zip", "shasum": "", "reference": "6544a24b2dcbe942e670675d70120311b3f4e343"}, "time": "2025-02-24T13:22:40+00:00"}, {"version": "v9.15.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4f13b9543feba1b549c4e7cc19cd7657be4c20c8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4f13b9543feba1b549c4e7cc19cd7657be4c20c8", "type": "zip", "shasum": "", "reference": "4f13b9543feba1b549c4e7cc19cd7657be4c20c8"}, "time": "2025-06-08T04:26:48+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "~1.1.14|^1.2.10", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.7", "laravel/pint": "^1.22", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1.14", "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1", "spatie/laravel-ray": "^1.40.2", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.44.2).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^9.10).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5.35|^11.3.6|^12.0.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.6.1)."}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<11.44.7|>=12.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "orchestra/testbench-dusk": "<9.10.0|>=10.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.3.6|>=12.0.0 <12.0.1|>=12.2.0"}}, {"version": "v9.14.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "082e001931c2efa5ae21dafbeb01fce4c8add875"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/082e001931c2efa5ae21dafbeb01fce4c8add875", "type": "zip", "shasum": "", "reference": "082e001931c2efa5ae21dafbeb01fce4c8add875"}, "time": "2025-05-12T05:29:11+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5|^1.2.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}}, {"version": "v9.13.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "bb35c2903efcd85f6e0ce6204cd61972b6f9ab13"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/bb35c2903efcd85f6e0ce6204cd61972b6f9ab13", "type": "zip", "shasum": "", "reference": "bb35c2903efcd85f6e0ce6204cd61972b6f9ab13"}, "time": "2025-04-27T05:26:53+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.7", "laravel/pint": "^1.22", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1.12", "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}}, {"version": "v9.13.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3629931516b73ad7bafc285a1fea6b4e0cf455a6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3629931516b73ad7bafc285a1fea6b4e0cf455a6", "type": "zip", "shasum": "", "reference": "3629931516b73ad7bafc285a1fea6b4e0cf455a6"}, "time": "2025-04-24T14:06:27+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.5", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<11.44.5|>=12.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "orchestra/testbench-dusk": "<9.10.0|>=10.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.3.6|>=12.0.0 <12.0.1|>=12.2.0"}}, {"version": "v9.13.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c70abf563a9c027f33eb59b87b2bb16cccec88d5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c70abf563a9c027f33eb59b87b2bb16cccec88d5", "type": "zip", "shasum": "", "reference": "c70abf563a9c027f33eb59b87b2bb16cccec88d5"}, "time": "2025-04-23T14:52:00+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.3", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<11.44.3|>=12.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "orchestra/testbench-dusk": "<9.10.0|>=10.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.3.6|>=12.0.0 <12.0.1|>=12.2.0"}}, {"version": "v9.13.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "779d6ef911d221cc5301971af6fc466e6194ca16"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/779d6ef911d221cc5301971af6fc466e6194ca16", "type": "zip", "shasum": "", "reference": "779d6ef911d221cc5301971af6fc466e6194ca16"}, "time": "2025-04-06T06:13:34+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.2", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0.4", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<11.44.2|>=12.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0 <2.0.3|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "orchestra/testbench-dusk": "<9.10.0|>=10.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.3.6|>=12.0.0 <12.0.1|>=12.2.0"}}, {"version": "v9.12.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "af32a4e2ec2f4db965a8996fb8ee88bb0077229e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/af32a4e2ec2f4db965a8996fb8ee88bb0077229e", "type": "zip", "shasum": "", "reference": "af32a4e2ec2f4db965a8996fb8ee88bb0077229e"}, "time": "2025-03-06T10:15:41+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.1", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.44.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^9.10).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5|^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.6.1)."}, "conflict": {"brianium/paratest": "<7.3.0|>=8.0.0", "laravel/framework": "<11.44.1|>=12.0.0", "laravel/serializable-closure": "<1.3.0|>=3.0.0", "nunomaduro/collision": "<8.0.0|>=9.0.0", "orchestra/testbench-dusk": "<9.10.0|>=10.0.0", "phpunit/phpunit": "<10.5.35|>=11.0.0 <11.3.6|>=12.0.0 <12.0.1|>=12.1.0"}}, {"version": "v9.11.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dced433e1438aad77b8b92440861a1798cb0671c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dced433e1438aad77b8b92440861a1798cb0671c", "type": "zip", "shasum": "", "reference": "dced433e1438aad77b8b92440861a1798cb0671c"}, "time": "2025-03-03T15:31:04+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.2", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.44.0", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35 || ^11.3.6 || ^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.44.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.44.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "orchestra/testbench-dusk": "<9.10.0 || >=10.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=12.0.0 <12.0.1 || >=12.1.0"}}, {"version": "v9.11.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c75e229450d1678f91db2fb714bb115a43dd6b90"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c75e229450d1678f91db2fb714bb115a43dd6b90", "type": "zip", "shasum": "", "reference": "c75e229450d1678f91db2fb714bb115a43dd6b90"}, "time": "2025-02-25T04:42:11+00:00"}, {"version": "v9.11.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dd17e39437175eb787852a18157011ad87bc41ac"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dd17e39437175eb787852a18157011ad87bc41ac", "type": "zip", "shasum": "", "reference": "dd17e39437175eb787852a18157011ad87bc41ac"}, "time": "2025-02-19T00:18:39+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.43.0", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35 || ^11.3.6 || ^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.43.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.43.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "orchestra/testbench-dusk": "<9.10.0 || >=10.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=12.0.0 <12.0.1 || >=12.1.0"}}, {"version": "v9.10.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "941ad0c6db8f569e45d0adb93029c38ecaec19d5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/941ad0c6db8f569e45d0adb93029c38ecaec19d5", "type": "zip", "shasum": "", "reference": "941ad0c6db8f569e45d0adb93029c38ecaec19d5"}, "time": "2025-02-15T04:01:14+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "symfony/polyfill-php83": "^1.31", "symfony/polyfill-php84": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.35.0", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.35 || ^11.3.6 || ^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.35.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.35.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "orchestra/testbench-dusk": "<9.10.0 || >=10.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=12.0.0 <12.0.1 || >=12.1.0"}}, {"version": "v9.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "473974c6ce2c6b10d65ff6b6f4106aa7939021f0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/473974c6ce2c6b10d65ff6b6f4106aa7939021f0", "type": "zip", "shasum": "", "reference": "473974c6ce2c6b10d65ff6b6f4106aa7939021f0"}, "time": "2025-02-07T11:21:53+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.35.0", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.5.35 || ^11.3.6 || ^12.0.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}}, {"version": "v9.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "169abdebda487292a35a295bb56b25b0d6d21e60"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/169abdebda487292a35a295bb56b25b0d6d21e60", "type": "zip", "shasum": "", "reference": "169abdebda487292a35a295bb56b25b0d6d21e60"}, "time": "2025-02-02T09:59:23+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.35.0", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.5.35 || ^11.3.6 || ^12.0.0", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.35.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "orchestra/testbench-dusk": "<9.10.0 || >=10.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=12.1.0"}}, {"version": "v9.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c7bcf7ab14fa09941278a7b1b731362db77e1745"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c7bcf7ab14fa09941278a7b1b731362db77e1745", "type": "zip", "shasum": "", "reference": "c7bcf7ab14fa09941278a7b1b731362db77e1745"}, "time": "2025-01-22T03:23:53+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.35.0", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.5.35 || ^11.3.6", "spatie/laravel-ray": "^1.39", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.35.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "orchestra/testbench-dusk": "<9.10.0 || >=10.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=11.6.0"}}, {"version": "v9.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ad211dc59c830a987eb34c18742f9c7875178eae"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ad211dc59c830a987eb34c18742f9c7875178eae", "type": "zip", "shasum": "", "reference": "ad211dc59c830a987eb34c18742f9c7875178eae"}, "time": "2025-01-07T02:00:47+00:00"}, {"version": "v9.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b605631fa3c779e51bc45d78013a979ddeb0d9fe"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b605631fa3c779e51bc45d78013a979ddeb0d9fe", "type": "zip", "shasum": "", "reference": "b605631fa3c779e51bc45d78013a979ddeb0d9fe"}, "time": "2024-12-24T01:10:06+00:00"}, {"version": "v9.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d4889bfe8e7dc95212dfd8b7f9a5e471fe397136"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d4889bfe8e7dc95212dfd8b7f9a5e471fe397136", "type": "zip", "shasum": "", "reference": "d4889bfe8e7dc95212dfd8b7f9a5e471fe397136"}, "time": "2024-12-19T00:12:23+00:00"}, {"version": "v9.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1538f7917addc9cde5014614a5ca75ea7a32ae47"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1538f7917addc9cde5014614a5ca75ea7a32ae47", "type": "zip", "shasum": "", "reference": "1538f7917addc9cde5014614a5ca75ea7a32ae47"}, "time": "2024-12-17T22:13:47+00:00"}, {"version": "v9.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7717d92054493096f14d21fc06ec6c43db7a2f3a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7717d92054493096f14d21fc06ec6c43db7a2f3a", "type": "zip", "shasum": "", "reference": "7717d92054493096f14d21fc06ec6c43db7a2f3a"}, "time": "2024-12-15T14:14:32+00:00"}, {"version": "v9.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7eb65df1c4bcf6b761f075006c9d8848dacddea6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7eb65df1c4bcf6b761f075006c9d8848dacddea6", "type": "zip", "shasum": "", "reference": "7eb65df1c4bcf6b761f075006c9d8848dacddea6"}, "time": "2024-12-14T01:53:15+00:00", "require-dev": {"fakerphp/faker": "^1.24", "laravel/framework": "^11.35.0", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.5.35 || ^11.3.6", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}}, {"version": "v9.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "466a6e4e144e71e15bc67deabc84989dce178485"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/466a6e4e144e71e15bc67deabc84989dce178485", "type": "zip", "shasum": "", "reference": "466a6e4e144e71e15bc67deabc84989dce178485"}, "time": "2024-12-06T03:34:33+00:00"}, {"version": "v9.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fc7ebe133b99785974294ce2a3a304224ac0b265"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fc7ebe133b99785974294ce2a3a304224ac0b265", "type": "zip", "shasum": "", "reference": "fc7ebe133b99785974294ce2a3a304224ac0b265"}, "time": "2024-12-05T02:24:37+00:00"}, {"version": "v9.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4b405d310b14d092861ff2b3182ddfe96b7e826a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4b405d310b14d092861ff2b3182ddfe96b7e826a", "type": "zip", "shasum": "", "reference": "4b405d310b14d092861ff2b3182ddfe96b7e826a"}, "time": "2024-12-01T09:31:00+00:00", "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.35.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "orchestra/testbench-dusk": "<9.10.0 || >=10.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=11.5.0"}}, {"version": "v9.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c5916db3ab12e97a04ad846dbad777307a76bff7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c5916db3ab12e97a04ad846dbad777307a76bff7", "type": "zip", "shasum": "", "reference": "c5916db3ab12e97a04ad846dbad777307a76bff7"}, "time": "2024-12-06T03:33:09+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.33.2", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.5.35 || ^11.3.6", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.33.2).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.33.2 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=11.6.0"}}, {"version": "v9.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0d153fbae0d3ab14cad4325f73310aa1e1d0e65f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0d153fbae0d3ab14cad4325f73310aa1e1d0e65f", "type": "zip", "shasum": "", "reference": "0d153fbae0d3ab14cad4325f73310aa1e1d0e65f"}, "time": "2024-12-05T02:16:52+00:00"}, {"version": "v9.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a776c94ab989f88e44b3d7f2f9df8e7ccb242974"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a776c94ab989f88e44b3d7f2f9df8e7ccb242974", "type": "zip", "shasum": "", "reference": "a776c94ab989f88e44b3d7f2f9df8e7ccb242974"}, "time": "2024-11-20T00:25:28+00:00", "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.33.2 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=11.5.0"}}, {"version": "v9.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "09a3d394417991bdd90b7bce9cece48ca3a20c15"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/09a3d394417991bdd90b7bce9cece48ca3a20c15", "type": "zip", "shasum": "", "reference": "09a3d394417991bdd90b7bce9cece48ca3a20c15"}, "time": "2024-11-19T23:56:36+00:00"}, {"version": "v9.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "22c0253a785494c81ba2a17b5c77e73a23f31a68"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/22c0253a785494c81ba2a17b5c77e73a23f31a68", "type": "zip", "shasum": "", "reference": "22c0253a785494c81ba2a17b5c77e73a23f31a68"}, "time": "2024-11-18T12:52:40+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.31", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.6.10", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.5.35 || ^11.3.6", "symfony/process": "^7.0.3", "symfony/yaml": "^7.0.3", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.31).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.31.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.35 || >=11.0.0 <11.3.6 || >=11.5.0"}}, {"version": "v9.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "26860d007c9316a20b1e9986dadb38f3ffb35433"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/26860d007c9316a20b1e9986dadb38f3ffb35433", "type": "zip", "shasum": "", "reference": "26860d007c9316a20b1e9986dadb38f3ffb35433"}, "time": "2024-10-30T23:18:34+00:00", "require": {"php": "^8.2", "composer-runtime-api": "^2.2", "symfony/polyfill-php83": "^1.28"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.11", "laravel/pint": "^1.17", "mockery/mockery": "^1.6", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^10.5 || ^11.0.1", "spatie/laravel-ray": "^1.35", "symfony/process": "^7.0", "symfony/yaml": "^7.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.11).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.11.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.5.0"}}, {"version": "v9.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "681a071a31e3b85ae080fa98614af0aec511cf6f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/681a071a31e3b85ae080fa98614af0aec511cf6f", "type": "zip", "shasum": "", "reference": "681a071a31e3b85ae080fa98614af0aec511cf6f"}, "time": "2024-10-25T14:43:57+00:00"}, {"version": "v9.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9eb99ee55935da2178a471fd7d338c18164a7fbe"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9eb99ee55935da2178a471fd7d338c18164a7fbe", "type": "zip", "shasum": "", "reference": "9eb99ee55935da2178a471fd7d338c18164a7fbe"}, "time": "2024-10-24T03:25:02+00:00"}, {"version": "v9.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9a5754622881f601951427a94c04c50e448cbf09"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9a5754622881f601951427a94c04c50e448cbf09", "type": "zip", "shasum": "", "reference": "9a5754622881f601951427a94c04c50e448cbf09"}, "time": "2024-10-06T11:20:27+00:00"}, {"version": "v9.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "93250c529db127620cfc37bc02d4ea93fc5c92fb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/93250c529db127620cfc37bc02d4ea93fc5c92fb", "type": "zip", "shasum": "", "reference": "93250c529db127620cfc37bc02d4ea93fc5c92fb"}, "time": "2024-10-05T14:45:41+00:00"}, {"version": "v9.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a083229e30daf7c9d6e323f19df0049a590fe51b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a083229e30daf7c9d6e323f19df0049a590fe51b", "type": "zip", "shasum": "", "reference": "a083229e30daf7c9d6e323f19df0049a590fe51b"}, "time": "2024-09-25T10:02:32+00:00"}, {"version": "v9.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4880c3f840cac2b9c0a26d46e927d1994aabc69f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4880c3f840cac2b9c0a26d46e927d1994aabc69f", "type": "zip", "shasum": "", "reference": "4880c3f840cac2b9c0a26d46e927d1994aabc69f"}, "time": "2024-09-23T13:18:23+00:00", "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.11.0 || >=12.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.4.0"}}, {"version": "v9.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b1f1a046cafd07d2a7b0cb96e9a2911aee160515"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b1f1a046cafd07d2a7b0cb96e9a2911aee160515", "type": "zip", "shasum": "", "reference": "b1f1a046cafd07d2a7b0cb96e9a2911aee160515"}, "time": "2024-09-12T10:54:24+00:00"}, {"version": "v9.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "422827e195741ca397408eced09ca473ebbb4086"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/422827e195741ca397408eced09ca473ebbb4086", "type": "zip", "shasum": "", "reference": "422827e195741ca397408eced09ca473ebbb4086"}, "time": "2024-08-26T05:01:33+00:00"}, {"version": "v9.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d66217119f326f82190a3638739a92a985ad73b3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d66217119f326f82190a3638739a92a985ad73b3", "type": "zip", "shasum": "", "reference": "d66217119f326f82190a3638739a92a985ad73b3"}, "time": "2024-08-14T05:55:35+00:00", "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.11.0 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.4.0"}}, {"version": "v9.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "405568eb663c4a3d5fc5c94ca7430c6643b9c7ff"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/405568eb663c4a3d5fc5c94ca7430c6643b9c7ff", "type": "zip", "shasum": "", "reference": "405568eb663c4a3d5fc5c94ca7430c6643b9c7ff"}, "time": "2024-08-02T10:12:21+00:00"}, {"version": "v9.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e0283b4fadbfa0104ea9ad772a390a5bfec602f0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e0283b4fadbfa0104ea9ad772a390a5bfec602f0", "type": "zip", "shasum": "", "reference": "e0283b4fadbfa0104ea9ad772a390a5bfec602f0"}, "time": "2024-07-19T10:35:39+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.11", "laravel/pint": "^1.6", "mockery/mockery": "^1.6", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^10.5 || ^11.0.1", "spatie/laravel-ray": "^1.35", "symfony/process": "^7.0", "symfony/yaml": "^7.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.11.0 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.3.0"}}, {"version": "v9.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "99f14ecffafadd3001df7adbc2b14e129dd32835"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/99f14ecffafadd3001df7adbc2b14e129dd32835", "type": "zip", "shasum": "", "reference": "99f14ecffafadd3001df7adbc2b14e129dd32835"}, "time": "2024-07-13T08:24:01+00:00"}, {"version": "v9.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "adaa3e09de1005ffadd9f6fd7b49133b0021753e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/adaa3e09de1005ffadd9f6fd7b49133b0021753e", "type": "zip", "shasum": "", "reference": "adaa3e09de1005ffadd9f6fd7b49133b0021753e"}, "time": "2024-07-13T07:15:25+00:00"}, {"version": "v9.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "872742adfc2a05c4ae6b0b4f1eb8961575ec2580"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/872742adfc2a05c4ae6b0b4f1eb8961575ec2580", "type": "zip", "shasum": "", "reference": "872742adfc2a05c4ae6b0b4f1eb8961575ec2580"}, "time": "2024-07-10T01:22:27+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.1", "laravel/pint": "^1.6", "mockery/mockery": "^1.6", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^10.5 || ^11.0.1", "spatie/laravel-ray": "^1.35", "symfony/process": "^7.0", "symfony/yaml": "^7.0", "vlucas/phpdotenv": "^5.4.1"}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.1.0 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.3.0"}}, {"version": "v9.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "690890054f37e5cb71f6753d07eb6519a0fe90b1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/690890054f37e5cb71f6753d07eb6519a0fe90b1", "type": "zip", "shasum": "", "reference": "690890054f37e5cb71f6753d07eb6519a0fe90b1"}, "time": "2024-06-28T11:48:15+00:00"}, {"version": "v9.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6e1ff7afaae0a0fa5dae2b796c83058d1fdd9a8a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6e1ff7afaae0a0fa5dae2b796c83058d1fdd9a8a", "type": "zip", "shasum": "", "reference": "6e1ff7afaae0a0fa5dae2b796c83058d1fdd9a8a"}, "time": "2024-06-26T12:16:41+00:00", "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.1.0 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.2.0"}}, {"version": "v9.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "66116144568050cc55a08fc93458b22b60e75740"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/66116144568050cc55a08fc93458b22b60e75740", "type": "zip", "shasum": "", "reference": "66116144568050cc55a08fc93458b22b60e75740"}, "time": "2024-06-10T10:24:57+00:00"}, {"version": "v9.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ee777eb4ff09fef9d20da0946b77057d93ad7266"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ee777eb4ff09fef9d20da0946b77057d93ad7266", "type": "zip", "shasum": "", "reference": "ee777eb4ff09fef9d20da0946b77057d93ad7266"}, "time": "2024-06-10T09:15:54+00:00"}, {"version": "v9.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8738ddaf1fc657053bc466b81a136b60b7b8256f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8738ddaf1fc657053bc466b81a136b60b7b8256f", "type": "zip", "shasum": "", "reference": "8738ddaf1fc657053bc466b81a136b60b7b8256f"}, "time": "2024-06-06T07:15:36+00:00"}, {"version": "v9.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "650758a0a7ae178c62a9344ad46c38347a2feb78"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/650758a0a7ae178c62a9344ad46c38347a2feb78", "type": "zip", "shasum": "", "reference": "650758a0a7ae178c62a9344ad46c38347a2feb78"}, "time": "2024-06-04T05:14:04+00:00"}, {"version": "v9.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "37ed7cca1603c57e729388e7df6c13f87ba37bbb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/37ed7cca1603c57e729388e7df6c13f87ba37bbb", "type": "zip", "shasum": "", "reference": "37ed7cca1603c57e729388e7df6c13f87ba37bbb"}, "time": "2024-06-01T09:30:45+00:00"}, {"version": "v9.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "113478a0c27edcbe6fe95456e448bf8cae2ff6db"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/113478a0c27edcbe6fe95456e448bf8cae2ff6db", "type": "zip", "shasum": "", "reference": "113478a0c27edcbe6fe95456e448bf8cae2ff6db"}, "time": "2024-05-23T02:19:56+00:00"}, {"version": "v9.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "174eca97e949c15250dc21a4c34835eedab72599"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/174eca97e949c15250dc21a4c34835eedab72599", "type": "zip", "shasum": "", "reference": "174eca97e949c15250dc21a4c34835eedab72599"}, "time": "2024-05-21T00:03:19+00:00"}, {"version": "v9.0.16", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dbd43bf3845f5d07fed375fff0cb4083924c5de2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dbd43bf3845f5d07fed375fff0cb4083924c5de2", "type": "zip", "shasum": "", "reference": "dbd43bf3845f5d07fed375fff0cb4083924c5de2"}, "time": "2024-05-09T06:53:54+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.1", "laravel/pint": "^1.6", "mockery/mockery": "^1.6", "phpstan/phpstan": "^1.10.50", "phpunit/phpunit": "^10.5 || ^11.0.1", "spatie/laravel-ray": "^1.35", "symfony/process": "^7.0", "symfony/yaml": "^7.0", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v9.0.15", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f3a8819310294646d628fc3c5b5958a0a651b066"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f3a8819310294646d628fc3c5b5958a0a651b066", "type": "zip", "shasum": "", "reference": "f3a8819310294646d628fc3c5b5958a0a651b066"}, "time": "2024-04-24T12:11:58+00:00"}, {"version": "v9.0.14", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "eabe0c72e787aae29df96bd4e33c61fffa6d5edc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/eabe0c72e787aae29df96bd4e33c61fffa6d5edc", "type": "zip", "shasum": "", "reference": "eabe0c72e787aae29df96bd4e33c61fffa6d5edc"}, "time": "2024-04-21T08:07:50+00:00", "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.1.0 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.1.0"}}, {"version": "v9.0.13", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1e4d35372c6ae8158572b4476e19c394e6b640e4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1e4d35372c6ae8158572b4476e19c394e6b640e4", "type": "zip", "shasum": "", "reference": "1e4d35372c6ae8158572b4476e19c394e6b640e4"}, "time": "2024-04-16T07:38:37+00:00"}, {"version": "v9.0.12", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1ede9aaaf809b9ff6afecaf420cc336b543d11e1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1ede9aaaf809b9ff6afecaf420cc336b543d11e1", "type": "zip", "shasum": "", "reference": "1ede9aaaf809b9ff6afecaf420cc336b543d11e1"}, "time": "2024-04-13T08:45:27+00:00"}, {"version": "v9.0.11", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d8422871876a729ce243503e1ce007195eb0407b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d8422871876a729ce243503e1ce007195eb0407b", "type": "zip", "shasum": "", "reference": "d8422871876a729ce243503e1ce007195eb0407b"}, "time": "2024-04-08T09:56:42+00:00"}, {"version": "v9.0.10", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "186f6f5aec03e62e132be6a301fd760ddbd540d3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/186f6f5aec03e62e132be6a301fd760ddbd540d3", "type": "zip", "shasum": "", "reference": "186f6f5aec03e62e132be6a301fd760ddbd540d3"}, "time": "2024-04-05T13:55:45+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.0.3", "laravel/pint": "^1.6", "mockery/mockery": "^1.6", "phpstan/phpstan": "^1.10.50", "phpunit/phpunit": "^10.5 || ^11.0.1", "spatie/laravel-ray": "^1.35", "symfony/process": "^7.0", "symfony/yaml": "^7.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.0.3).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5 || ^11.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.0.3 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.1.0"}}, {"version": "v9.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "052b2f17051a57d46c54e6e56ebaa791c58f675a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/052b2f17051a57d46c54e6e56ebaa791c58f675a", "type": "zip", "shasum": "", "reference": "052b2f17051a57d46c54e6e56ebaa791c58f675a"}, "time": "2024-03-26T23:21:46+00:00", "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.0.3).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}}, {"version": "v9.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f01150a92c033802f56850f03e2debeb03f7fe96"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f01150a92c033802f56850f03e2debeb03f7fe96", "type": "zip", "shasum": "", "reference": "f01150a92c033802f56850f03e2debeb03f7fe96"}, "time": "2024-03-26T15:32:39+00:00"}, {"version": "v9.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "93adea230e675b4ac1983966475d116a13a146cb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/93adea230e675b4ac1983966475d116a13a146cb", "type": "zip", "shasum": "", "reference": "93adea230e675b4ac1983966475d116a13a146cb"}, "time": "2024-03-25T04:45:39+00:00"}, {"version": "v9.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ea532af82da288d363b7dc7c96edae6bbb329efe"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ea532af82da288d363b7dc7c96edae6bbb329efe", "type": "zip", "shasum": "", "reference": "ea532af82da288d363b7dc7c96edae6bbb329efe"}, "time": "2024-03-19T11:20:27+00:00"}, {"version": "v9.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5f25e4b174e649561eb97611ec8b33bff7be0e12"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5f25e4b174e649561eb97611ec8b33bff7be0e12", "type": "zip", "shasum": "", "reference": "5f25e4b174e649561eb97611ec8b33bff7be0e12"}, "time": "2024-03-19T01:12:05+00:00"}, {"version": "v9.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c86b66e16b3313b12c13575ed3b4cfedcb57e950"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c86b66e16b3313b12c13575ed3b4cfedcb57e950", "type": "zip", "shasum": "", "reference": "c86b66e16b3313b12c13575ed3b4cfedcb57e950"}, "time": "2024-03-18T14:40:23+00:00"}, {"version": "v9.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b14f865a1abc827193e73c25559ad9bcf7851802"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b14f865a1abc827193e73c25559ad9bcf7851802", "type": "zip", "shasum": "", "reference": "b14f865a1abc827193e73c25559ad9bcf7851802"}, "time": "2024-03-14T14:04:45+00:00"}, {"version": "v9.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8101d503d023b9e19d9373121130377cc8fbf2c0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8101d503d023b9e19d9373121130377cc8fbf2c0", "type": "zip", "shasum": "", "reference": "8101d503d023b9e19d9373121130377cc8fbf2c0"}, "time": "2024-03-13T22:51:27+00:00"}, {"version": "v9.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ad08292492f2952e358e2c23bf5dbd53df53e5b7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ad08292492f2952e358e2c23bf5dbd53df53e5b7", "type": "zip", "shasum": "", "reference": "ad08292492f2952e358e2c23bf5dbd53df53e5b7"}, "time": "2024-03-13T05:02:43+00:00"}, {"version": "v9.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4e04e1a6f9a8c4ae960c7d0dbebf81edb8e09a06"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4e04e1a6f9a8c4ae960c7d0dbebf81edb8e09a06", "type": "zip", "shasum": "", "reference": "4e04e1a6f9a8c4ae960c7d0dbebf81edb8e09a06"}, "time": "2024-03-08T12:18:46+00:00", "require-dev": {"fakerphp/faker": "^1.23", "laravel/framework": "^11.0", "laravel/pint": "^1.6", "mockery/mockery": "^1.6", "phpstan/phpstan": "^1.10.50", "phpunit/phpunit": "^10.5 || ^11.0.1", "spatie/laravel-ray": "^1.35", "symfony/process": "^7.0", "symfony/yaml": "^7.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^7.3).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using <PERSON>aker for testing (^1.23).", "laravel/framework": "Required for testing (^11.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.6).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^9.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^10.5).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^7.0).", "symfony/yaml": "Required for Testbench CLI (^7.0).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<7.3.0 || >=8.0.0", "laravel/framework": "<11.0.0 || >=12.0.0", "nunomaduro/collision": "<8.0.0 || >=9.0.0", "phpunit/phpunit": "<10.5.0 || 11.0.0 || >=11.1.0"}}, {"version": "v8.38.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dc71c91df375b192f7e4b1ea35656a6a9d1f1531"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dc71c91df375b192f7e4b1ea35656a6a9d1f1531", "type": "zip", "shasum": "", "reference": "dc71c91df375b192f7e4b1ea35656a6a9d1f1531"}, "time": "2025-06-08T04:15:09+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "orchestra/sidekick": "~1.1.14|^1.2.10", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.29", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1.14", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.40.2", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4|^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.29).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4|^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6|^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0|>=7.0.0 <7.1.4|>=8.0.0", "laravel/framework": "<10.48.29|>=11.0.0", "laravel/serializable-closure": "<1.3.0|>=3.0.0", "nunomaduro/collision": "<6.4.0|>=7.0.0 <7.4.0|>=8.0.0", "orchestra/testbench-dusk": "<8.32.0|>=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0|>=10.3.0 <10.3.3|>=10.6.0"}}, {"version": "v8.37.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3f3b4a01d996c1565518820a721675626540e8e9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3f3b4a01d996c1565518820a721675626540e8e9", "type": "zip", "shasum": "", "reference": "3f3b4a01d996c1565518820a721675626540e8e9"}, "time": "2025-05-12T05:18:31+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5|^1.2.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}}, {"version": "v8.36.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "860b8af379924135b7e55a1d2ebc29ef5f52e4f7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/860b8af379924135b7e55a1d2ebc29ef5f52e4f7", "type": "zip", "shasum": "", "reference": "860b8af379924135b7e55a1d2ebc29ef5f52e4f7"}, "time": "2025-04-27T05:11:32+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.29", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1.12", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.36.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "079d4a7e38324a2d7407c6a1897e8aa081dca59f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/079d4a7e38324a2d7407c6a1897e8aa081dca59f", "type": "zip", "shasum": "", "reference": "079d4a7e38324a2d7407c6a1897e8aa081dca59f"}, "time": "2025-04-06T06:00:41+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.29", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.35.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d31228683544aa204aba237d72c7753783c36ec1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d31228683544aa204aba237d72c7753783c36ec1", "type": "zip", "shasum": "", "reference": "d31228683544aa204aba237d72c7753783c36ec1"}, "time": "2025-03-06T10:00:05+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.28", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3|^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4|^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.28).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4|^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6|^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0|>=7.0.0 <7.1.4|>=8.0.0", "laravel/framework": "<10.48.28|>=11.0.0", "laravel/serializable-closure": "<1.3.0|>=3.0.0", "nunomaduro/collision": "<6.4.0|>=7.0.0 <7.4.0|>=8.0.0", "orchestra/testbench-dusk": "<8.32.0|>=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0|>=10.3.0 <10.3.3|>=10.6.0"}}, {"version": "v8.34.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "37fc97837a35e7db34a02432eaa18a730f6d4210"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/37fc97837a35e7db34a02432eaa18a730f6d4210", "type": "zip", "shasum": "", "reference": "37fc97837a35e7db34a02432eaa18a730f6d4210"}, "time": "2025-02-19T00:02:18+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.2", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.28", "laravel/pint": "^1.20", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.28).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.28 || >=11.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.32.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.3.0 <10.3.3 || >=10.6.0"}}, {"version": "v8.33.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "af9fee1a4d275daeff87023b668be7531edded67"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/af9fee1a4d275daeff87023b668be7531edded67", "type": "zip", "shasum": "", "reference": "af9fee1a4d275daeff87023b668be7531edded67"}, "time": "2025-02-15T03:47:47+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "symfony/polyfill-php83": "^1.31"}}, {"version": "v8.32.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8d7a3e360213ab3d41b874f94b107036223ce429"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8d7a3e360213ab3d41b874f94b107036223ce429", "type": "zip", "shasum": "", "reference": "8d7a3e360213ab3d41b874f94b107036223ce429"}, "time": "2025-02-06T04:48:22+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.28", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.32.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e63e54fc01767114d74633dc68c4b70989c04e25"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e63e54fc01767114d74633dc68c4b70989c04e25", "type": "zip", "shasum": "", "reference": "e63e54fc01767114d74633dc68c4b70989c04e25"}, "time": "2025-02-02T09:46:55+00:00"}, {"version": "v8.32.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "19834b866154e5b30dbead6774ada54f116c59f8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/19834b866154e5b30dbead6774ada54f116c59f8", "type": "zip", "shasum": "", "reference": "19834b866154e5b30dbead6774ada54f116c59f8"}, "time": "2025-01-07T01:46:59+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.25", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.25).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.25 || >=11.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.32.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.3.0 <10.3.3 || >=10.6.0"}}, {"version": "v8.32.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d4d27fb3d4a127f6ea07915a9df7c10a16810c7e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d4d27fb3d4a127f6ea07915a9df7c10a16810c7e", "type": "zip", "shasum": "", "reference": "d4d27fb3d4a127f6ea07915a9df7c10a16810c7e"}, "time": "2024-12-24T01:00:04+00:00"}, {"version": "v8.31.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0047be7fa66d6752f03d1ad1fb5677b816d5c00b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0047be7fa66d6752f03d1ad1fb5677b816d5c00b", "type": "zip", "shasum": "", "reference": "0047be7fa66d6752f03d1ad1fb5677b816d5c00b"}, "time": "2024-12-19T00:09:06+00:00"}, {"version": "v8.31.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c7ff1324cff57adde1b54d7f2aad56dd32ce2890"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c7ff1324cff57adde1b54d7f2aad56dd32ce2890", "type": "zip", "shasum": "", "reference": "c7ff1324cff57adde1b54d7f2aad56dd32ce2890"}, "time": "2024-12-15T14:07:11+00:00"}, {"version": "v8.30.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d0d0badafa2bd2fc3fa7e4e763a9a667ea2c30e2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d0d0badafa2bd2fc3fa7e4e763a9a667ea2c30e2", "type": "zip", "shasum": "", "reference": "d0d0badafa2bd2fc3fa7e4e763a9a667ea2c30e2"}, "time": "2024-12-14T01:11:45+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.25", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.30.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dd5d77d01bc1788cb328016eca6881fe07c2118d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dd5d77d01bc1788cb328016eca6881fe07c2118d", "type": "zip", "shasum": "", "reference": "dd5d77d01bc1788cb328016eca6881fe07c2118d"}, "time": "2024-12-01T09:20:16+00:00"}, {"version": "v8.29.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "55cf0234f9f96590bca4ece7081cc5c328e34e48"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/55cf0234f9f96590bca4ece7081cc5c328e34e48", "type": "zip", "shasum": "", "reference": "55cf0234f9f96590bca4ece7081cc5c328e34e48"}, "time": "2024-11-18T12:42:00+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.23", "laravel/pint": "^1.17", "laravel/serializable-closure": "^1.3 || ^2.0", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.23).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.23 || >=11.0.0", "laravel/serializable-closure": "<1.3.0 || >=3.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.3.0 <10.3.3 || >=10.6.0"}}, {"version": "v8.28.5", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "47374b1f22f520bc9d2eb1709b272c0614f96064"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/47374b1f22f520bc9d2eb1709b272c0614f96064", "type": "zip", "shasum": "", "reference": "47374b1f22f520bc9d2eb1709b272c0614f96064"}, "time": "2024-10-25T14:40:30+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2", "symfony/polyfill-php83": "^1.28"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.22", "laravel/pint": "^1.17", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.20).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.22 || >=11.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.3.0 <10.3.3 || >=10.6.0"}}, {"version": "v8.28.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a3c29070f393702ee099a22c882febcba49f531e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a3c29070f393702ee099a22c882febcba49f531e", "type": "zip", "shasum": "", "reference": "a3c29070f393702ee099a22c882febcba49f531e"}, "time": "2024-10-24T03:18:30+00:00"}, {"version": "v8.28.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "053c8655ae46225f52e69e6d2c1b2313bdf54611"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/053c8655ae46225f52e69e6d2c1b2313bdf54611", "type": "zip", "shasum": "", "reference": "053c8655ae46225f52e69e6d2c1b2313bdf54611"}, "time": "2024-10-06T11:18:11+00:00"}, {"version": "v8.28.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "88acced48640d8b1899040e821ac41311d272226"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/88acced48640d8b1899040e821ac41311d272226", "type": "zip", "shasum": "", "reference": "88acced48640d8b1899040e821ac41311d272226"}, "time": "2024-10-05T14:42:34+00:00"}, {"version": "v8.28.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a5ab70e78378363b69220ae5771dabe8c885a25e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a5ab70e78378363b69220ae5771dabe8c885a25e", "type": "zip", "shasum": "", "reference": "a5ab70e78378363b69220ae5771dabe8c885a25e"}, "time": "2024-09-24T05:26:51+00:00"}, {"version": "v8.28.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "da34fc0fc3afdb4a1beb407fc64731486cc4f418"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/da34fc0fc3afdb4a1beb407fc64731486cc4f418", "type": "zip", "shasum": "", "reference": "da34fc0fc3afdb4a1beb407fc64731486cc4f418"}, "time": "2024-09-23T13:15:59+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.22 || >=11.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.27.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "95185200e654c75e4d5d6626f6ec139c5a4699ca"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/95185200e654c75e4d5d6626f6ec139c5a4699ca", "type": "zip", "shasum": "", "reference": "95185200e654c75e4d5d6626f6ec139c5a4699ca"}, "time": "2024-08-26T04:43:39+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.20", "laravel/pint": "^1.17", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.20 || >=11.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.26.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c3bc4a4440b312035da6659b87973a4cda584476"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c3bc4a4440b312035da6659b87973a4cda584476", "type": "zip", "shasum": "", "reference": "c3bc4a4440b312035da6659b87973a4cda584476"}, "time": "2024-08-14T05:31:02+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.20 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.25.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "df0a606dd557a1e350914be64632cd9040fa4bc0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/df0a606dd557a1e350914be64632cd9040fa4bc0", "type": "zip", "shasum": "", "reference": "df0a606dd557a1e350914be64632cd9040fa4bc0"}, "time": "2024-07-19T10:25:12+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.2", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.48.2).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.48.2 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.25.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "17cbaf568fe6c84039295b3de09a1b049339d928"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/17cbaf568fe6c84039295b3de09a1b049339d928", "type": "zip", "shasum": "", "reference": "17cbaf568fe6c84039295b3de09a1b049339d928"}, "time": "2024-07-13T06:54:00+00:00"}, {"version": "v8.24.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3acbb96840e2bd4acbe670ee95e9bd55f517964c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3acbb96840e2bd4acbe670ee95e9bd55f517964c", "type": "zip", "shasum": "", "reference": "3acbb96840e2bd4acbe670ee95e9bd55f517964c"}, "time": "2024-06-26T12:14:16+00:00"}, {"version": "v8.24.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c4daf2f1929242f4e4cb33b5ebdaaf631df30a46"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c4daf2f1929242f4e4cb33b5ebdaaf631df30a46", "type": "zip", "shasum": "", "reference": "c4daf2f1929242f4e4cb33b5ebdaaf631df30a46"}, "time": "2024-06-04T05:00:04+00:00"}, {"version": "v8.24.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "756519effeac591971c313ee830ed625ffac8681"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/756519effeac591971c313ee830ed625ffac8681", "type": "zip", "shasum": "", "reference": "756519effeac591971c313ee830ed625ffac8681"}, "time": "2024-06-01T09:24:14+00:00"}, {"version": "v8.24.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fa67adb5517553ec8a00804933bb7bb50110137e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fa67adb5517553ec8a00804933bb7bb50110137e", "type": "zip", "shasum": "", "reference": "fa67adb5517553ec8a00804933bb7bb50110137e"}, "time": "2024-05-23T02:08:40+00:00"}, {"version": "v8.24.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0d541a77c8497ef9d80f34322269417dbcbf216e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0d541a77c8497ef9d80f34322269417dbcbf216e", "type": "zip", "shasum": "", "reference": "0d541a77c8497ef9d80f34322269417dbcbf216e"}, "time": "2024-05-21T00:01:19+00:00"}, {"version": "v8.23.10", "version_normalized": "*********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0b4bf76d9ab2b5d6f3a7d9a956e5affbd04bbe4d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0b4bf76d9ab2b5d6f3a7d9a956e5affbd04bbe4d", "type": "zip", "shasum": "", "reference": "0b4bf76d9ab2b5d6f3a7d9a956e5affbd04bbe4d"}, "time": "2024-04-21T08:00:04+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.48.2", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.23.9", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f2b776cf11e821b14dcadae36099ce17ef37f11e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f2b776cf11e821b14dcadae36099ce17ef37f11e", "type": "zip", "shasum": "", "reference": "f2b776cf11e821b14dcadae36099ce17ef37f11e"}, "time": "2024-04-16T07:33:55+00:00"}, {"version": "v8.23.8", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7f429808771deab955c72f58bee4c48d92ce887e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7f429808771deab955c72f58bee4c48d92ce887e", "type": "zip", "shasum": "", "reference": "7f429808771deab955c72f58bee4c48d92ce887e"}, "time": "2024-04-13T08:32:37+00:00"}, {"version": "v8.23.7", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b1c22e92bac8a043cc9270beb97c4cff2a743e09"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b1c22e92bac8a043cc9270beb97c4cff2a743e09", "type": "zip", "shasum": "", "reference": "b1c22e92bac8a043cc9270beb97c4cff2a743e09"}, "time": "2024-04-08T09:59:28+00:00"}, {"version": "v8.23.6", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "84f91d3f883fb3cc2d8079f5839542ca1c8b6ffc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/84f91d3f883fb3cc2d8079f5839542ca1c8b6ffc", "type": "zip", "shasum": "", "reference": "84f91d3f883fb3cc2d8079f5839542ca1c8b6ffc"}, "time": "2024-04-05T13:53:47+00:00"}, {"version": "v8.23.5", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0e5c930d247f50d1d6d5997441e57891af862634"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0e5c930d247f50d1d6d5997441e57891af862634", "type": "zip", "shasum": "", "reference": "0e5c930d247f50d1d6d5997441e57891af862634"}, "time": "2024-03-25T04:32:37+00:00"}, {"version": "v8.23.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a863b355bf73609121c1d2d08b3ffb8b27ecb8d0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a863b355bf73609121c1d2d08b3ffb8b27ecb8d0", "type": "zip", "shasum": "", "reference": "a863b355bf73609121c1d2d08b3ffb8b27ecb8d0"}, "time": "2024-03-19T11:18:56+00:00"}, {"version": "v8.23.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "48bb958a8998f2462c1504b2af194b9dfcbbf63c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/48bb958a8998f2462c1504b2af194b9dfcbbf63c", "type": "zip", "shasum": "", "reference": "48bb958a8998f2462c1504b2af194b9dfcbbf63c"}, "time": "2024-03-19T01:23:20+00:00"}, {"version": "v8.23.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "026e6779571b915c1b16fcaeaf43dd340de5b32f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/026e6779571b915c1b16fcaeaf43dd340de5b32f", "type": "zip", "shasum": "", "reference": "026e6779571b915c1b16fcaeaf43dd340de5b32f"}, "time": "2024-03-18T13:52:09+00:00"}, {"version": "v8.23.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6c1d546c8864b0cb6304f584a709be7b1965e978"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6c1d546c8864b0cb6304f584a709be7b1965e978", "type": "zip", "shasum": "", "reference": "6c1d546c8864b0cb6304f584a709be7b1965e978"}, "time": "2024-03-15T12:13:59+00:00"}, {"version": "v8.23.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "35936ff29b42439fda75dbb635cc57e113f17c98"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/35936ff29b42439fda75dbb635cc57e113f17c98", "type": "zip", "shasum": "", "reference": "35936ff29b42439fda75dbb635cc57e113f17c98"}, "time": "2024-03-13T04:02:37+00:00"}, {"version": "v8.22.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b0006c092694828f4b0fa409a369b798e5e26f8d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b0006c092694828f4b0fa409a369b798e5e26f8d", "type": "zip", "shasum": "", "reference": "b0006c092694828f4b0fa409a369b798e5e26f8d"}, "time": "2024-02-21T23:33:22+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.40", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.40).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.2).", "symfony/yaml": "Required for Testbench CLI (^6.2).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.40 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/testbench-dusk": "<8.21.0 || >=9.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.22.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8936b079c923908d27219b3635c7cb7dbc34a936"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8936b079c923908d27219b3635c7cb7dbc34a936", "type": "zip", "shasum": "", "reference": "8936b079c923908d27219b3635c7cb7dbc34a936"}, "time": "2024-02-21T14:17:33+00:00"}, {"version": "v8.21.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c6bf49a1b7ef4afe58e5bda676158c4de41f9a81"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c6bf49a1b7ef4afe58e5bda676158c4de41f9a81", "type": "zip", "shasum": "", "reference": "c6bf49a1b7ef4afe58e5bda676158c4de41f9a81"}, "time": "2024-01-22T01:45:47+00:00"}, {"version": "v8.21.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "34038bbc26dfb2305297c664efad1d5be3918ee2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/34038bbc26dfb2305297c664efad1d5be3918ee2", "type": "zip", "shasum": "", "reference": "34038bbc26dfb2305297c664efad1d5be3918ee2"}, "time": "2024-01-19T14:40:27+00:00"}, {"version": "v8.20.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "beb3af0737b0ac49c29b4bc26de548845f097abc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/beb3af0737b0ac49c29b4bc26de548845f097abc", "type": "zip", "shasum": "", "reference": "beb3af0737b0ac49c29b4bc26de548845f097abc"}, "time": "2024-01-10T03:05:52+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.40 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.19.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "15645dd792968f48a27a26fc4f542c16d9f07e0d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/15645dd792968f48a27a26fc4f542c16d9f07e0d", "type": "zip", "shasum": "", "reference": "15645dd792968f48a27a26fc4f542c16d9f07e0d"}, "time": "2023-12-28T14:44:29+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.39", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.39).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.39 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || 10.5.4 || >=10.6.0"}}, {"version": "v8.18.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "28ed9383d390cb9913c3daa716f0d7170126b8d5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/28ed9383d390cb9913c3daa716f0d7170126b8d5", "type": "zip", "shasum": "", "reference": "28ed9383d390cb9913c3daa716f0d7170126b8d5"}, "time": "2023-12-19T00:45:02+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Orchestra\\Testbench\\": "src/"}}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.37.3", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.37.3).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.37.3 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.17.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6856e979790c18705812b2b6c3c73bb1f6d8baa8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6856e979790c18705812b2b6c3c73bb1f6d8baa8", "type": "zip", "shasum": "", "reference": "6856e979790c18705812b2b6c3c73bb1f6d8baa8"}, "time": "2023-12-06T08:00:50+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.23", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.23).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.23.1 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.6.0"}}, {"version": "v8.17.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e7d69289282b18b6e078639a839a11ee0345600b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e7d69289282b18b6e078639a839a11ee0345600b", "type": "zip", "shasum": "", "reference": "e7d69289282b18b6e078639a839a11ee0345600b"}, "time": "2023-12-06T02:43:00+00:00"}, {"version": "v8.16.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7bb71b2f2b071a0c514058130495bc03a16e688f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7bb71b2f2b071a0c514058130495bc03a16e688f", "type": "zip", "shasum": "", "reference": "7bb71b2f2b071a0c514058130495bc03a16e688f"}, "time": "2023-12-05T21:55:09+00:00"}, {"version": "v8.16.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a1ac90c5ef869598ce63cf3d2e602f95f9432df9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a1ac90c5ef869598ce63cf3d2e602f95f9432df9", "type": "zip", "shasum": "", "reference": "a1ac90c5ef869598ce63cf3d2e602f95f9432df9"}, "time": "2023-12-04T13:35:57+00:00"}, {"version": "v8.16.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "72d4f240a6a3adb306d4406caea61f8f9799ebd8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/72d4f240a6a3adb306d4406caea61f8f9799ebd8", "type": "zip", "shasum": "", "reference": "72d4f240a6a3adb306d4406caea61f8f9799ebd8"}, "time": "2023-12-04T05:27:42+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.23.1 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "phpunit/phpunit": "<9.6.0 || >=10.5.0"}}, {"version": "v8.15.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1bd2aa77c0fdd674d28e8d6e44fe5f6528ff966a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1bd2aa77c0fdd674d28e8d6e44fe5f6528ff966a", "type": "zip", "shasum": "", "reference": "1bd2aa77c0fdd674d28e8d6e44fe5f6528ff966a"}, "time": "2023-11-20T22:34:56+00:00", "require": {"php": "^8.1", "composer-runtime-api": "^2.2"}}, {"version": "v8.15.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "428628ef2fecffe30c1c922536242ebe0da1c47e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/428628ef2fecffe30c1c922536242ebe0da1c47e", "type": "zip", "shasum": "", "reference": "428628ef2fecffe30c1c922536242ebe0da1c47e"}, "time": "2023-11-10T04:43:17+00:00"}, {"version": "v8.15.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "450bf8f1a85357939c99c4f2aa3584f751bafc76"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/450bf8f1a85357939c99c4f2aa3584f751bafc76", "type": "zip", "shasum": "", "reference": "450bf8f1a85357939c99c4f2aa3584f751bafc76"}, "time": "2023-11-10T01:59:47+00:00"}, {"version": "v8.14.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2501be03ccd199851006b3d286b303b97fc55738"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2501be03ccd199851006b3d286b303b97fc55738", "type": "zip", "shasum": "", "reference": "2501be03ccd199851006b3d286b303b97fc55738"}, "time": "2023-11-02T13:53:10+00:00"}, {"version": "v8.14.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6ebfa94c5d7872c5a6623ed588476771c6b9c124"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6ebfa94c5d7872c5a6623ed588476771c6b9c124", "type": "zip", "shasum": "", "reference": "6ebfa94c5d7872c5a6623ed588476771c6b9c124"}, "time": "2023-10-31T12:58:05+00:00"}, {"version": "v8.14.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "eeb576a1fec22b6baa0868f8abb00af953af65a2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/eeb576a1fec22b6baa0868f8abb00af953af65a2", "type": "zip", "shasum": "", "reference": "eeb576a1fec22b6baa0868f8abb00af953af65a2"}, "time": "2023-10-30T13:09:23+00:00"}, {"version": "v8.14.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5719db860336128ebc4d09e4525c4e0a16e61cb5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5719db860336128ebc4d09e4525c4e0a16e61cb5", "type": "zip", "shasum": "", "reference": "5719db860336128ebc4d09e4525c4e0a16e61cb5"}, "time": "2023-10-24T06:16:05+00:00", "require-dev": {"composer-runtime-api": "^2.2", "fakerphp/faker": "^1.21", "laravel/framework": "^10.23", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.14.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d6cf9e697f59a43484c09e8fedc29330ebde640d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d6cf9e697f59a43484c09e8fedc29330ebde640d", "type": "zip", "shasum": "", "reference": "d6cf9e697f59a43484c09e8fedc29330ebde640d"}, "time": "2023-10-24T04:19:40+00:00"}, {"version": "v8.13.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b03aa317d3c660dd63e4096580d7f713bc2cab15"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b03aa317d3c660dd63e4096580d7f713bc2cab15", "type": "zip", "shasum": "", "reference": "b03aa317d3c660dd63e4096580d7f713bc2cab15"}, "time": "2023-10-09T11:41:27+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.23", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0 <7.1.4 || >=8.0.0", "laravel/framework": "<10.23.1 || >=11.0.0", "nunomaduro/collision": "<6.4.0 || >=7.0.0 <7.4.0 || >=8.0.0", "phpunit/phpunit": "<9.6.0 || >=10.5.0"}}, {"version": "v8.12.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9a7b63f9cd10dd15cf7c9d4aad2ccaa688465a1f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9a7b63f9cd10dd15cf7c9d4aad2ccaa688465a1f", "type": "zip", "shasum": "", "reference": "9a7b63f9cd10dd15cf7c9d4aad2ccaa688465a1f"}, "time": "2023-09-26T12:50:24+00:00", "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.23).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}, "conflict": "__unset"}, {"version": "v8.12.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "11d12200314f86e4c05e719c631fedc94a68a20c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/11d12200314f86e4c05e719c631fedc94a68a20c", "type": "zip", "shasum": "", "reference": "11d12200314f86e4c05e719c631fedc94a68a20c"}, "time": "2023-09-25T13:39:16+00:00"}, {"version": "v8.11.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e484ec06c548e92758824be78eaa4ce004285c30"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e484ec06c548e92758824be78eaa4ce004285c30", "type": "zip", "shasum": "", "reference": "e484ec06c548e92758824be78eaa4ce004285c30"}, "time": "2023-09-25T02:01:35+00:00"}, {"version": "v8.11.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a18dcf6b6e27fc7e7c7017115b6a16bc848b4512"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a18dcf6b6e27fc7e7c7017115b6a16bc848b4512", "type": "zip", "shasum": "", "reference": "a18dcf6b6e27fc7e7c7017115b6a16bc848b4512"}, "time": "2023-09-21T02:56:43+00:00"}, {"version": "v8.11.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1f382c9d55100c2c641d4cba50bbde764aa93054"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1f382c9d55100c2c641d4cba50bbde764aa93054", "type": "zip", "shasum": "", "reference": "1f382c9d55100c2c641d4cba50bbde764aa93054"}, "time": "2023-09-19T10:57:14+00:00"}, {"version": "v8.11.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "570389e87933ce401d1f14e74e433f87c9b4ec4e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/570389e87933ce401d1f14e74e433f87c9b4ec4e", "type": "zip", "shasum": "", "reference": "570389e87933ce401d1f14e74e433f87c9b4ec4e"}, "time": "2023-09-19T04:28:16+00:00"}, {"version": "v8.10.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "54071af200899a22e849aa4abe8019e8a1d8e826"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/54071af200899a22e849aa4abe8019e8a1d8e826", "type": "zip", "shasum": "", "reference": "54071af200899a22e849aa4abe8019e8a1d8e826"}, "time": "2023-09-14T02:44:47+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.17", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.17).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.10.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e8f1a4d3121887e9f5c5607f117701ea7dfa066a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e8f1a4d3121887e9f5c5607f117701ea7dfa066a", "type": "zip", "shasum": "", "reference": "e8f1a4d3121887e9f5c5607f117701ea7dfa066a"}, "time": "2023-09-09T02:26:33+00:00"}, {"version": "v8.10.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "df26b86117831f6b167b00c1d797d2914e99a1f1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/df26b86117831f6b167b00c1d797d2914e99a1f1", "type": "zip", "shasum": "", "reference": "df26b86117831f6b167b00c1d797d2914e99a1f1"}, "time": "2023-08-29T05:40:58+00:00"}, {"version": "v8.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8c6d37b937c8932fda021797ba5fcea036f65eef"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8c6d37b937c8932fda021797ba5fcea036f65eef", "type": "zip", "shasum": "", "reference": "8c6d37b937c8932fda021797ba5fcea036f65eef"}, "time": "2023-08-22T08:49:34+00:00"}, {"version": "v8.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f216306afcce50803562949133a930190d8be408"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f216306afcce50803562949133a930190d8be408", "type": "zip", "shasum": "", "reference": "f216306afcce50803562949133a930190d8be408"}, "time": "2023-08-19T03:43:49+00:00"}, {"version": "v8.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b9f92666a54cebcbb7662db26553386d49f45324"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b9f92666a54cebcbb7662db26553386d49f45324", "type": "zip", "shasum": "", "reference": "b9f92666a54cebcbb7662db26553386d49f45324"}, "time": "2023-08-18T02:47:13+00:00"}, {"version": "v8.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "76b78403e769da02cf4602c1fe673c991ca8ead9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/76b78403e769da02cf4602c1fe673c991ca8ead9", "type": "zip", "shasum": "", "reference": "76b78403e769da02cf4602c1fe673c991ca8ead9"}, "time": "2023-08-17T09:03:11+00:00"}, {"version": "v8.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a2fea94fe56ce587e9cdd61d832d92ed504f0c07"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a2fea94fe56ce587e9cdd61d832d92ed504f0c07", "type": "zip", "shasum": "", "reference": "a2fea94fe56ce587e9cdd61d832d92ed504f0c07"}, "time": "2023-08-16T22:39:24+00:00"}, {"version": "v8.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f550ac3fb0df0429987afbf8a40d8d7a894a6303"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f550ac3fb0df0429987afbf8a40d8d7a894a6303", "type": "zip", "shasum": "", "reference": "f550ac3fb0df0429987afbf8a40d8d7a894a6303"}, "time": "2023-08-16T02:20:24+00:00"}, {"version": "v8.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a4a724e6cefdbdd022a9b9737131c175ac78b76a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a4a724e6cefdbdd022a9b9737131c175ac78b76a", "type": "zip", "shasum": "", "reference": "a4a724e6cefdbdd022a9b9737131c175ac78b76a"}, "time": "2023-08-15T09:05:03+00:00"}, {"version": "v8.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "26ada39cb2c47975bc51188b2f060b092e94d2ef"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/26ada39cb2c47975bc51188b2f060b092e94d2ef", "type": "zip", "shasum": "", "reference": "26ada39cb2c47975bc51188b2f060b092e94d2ef"}, "time": "2023-08-12T10:34:08+00:00"}, {"version": "v8.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d110b67306056925bfb66fcef625667e094a7540"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d110b67306056925bfb66fcef625667e094a7540", "type": "zip", "shasum": "", "reference": "d110b67306056925bfb66fcef625667e094a7540"}, "time": "2023-08-12T04:27:26+00:00"}, {"version": "v8.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2d4230edd4a45da956c7ea49295ca0ceeff6ea07"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2d4230edd4a45da956c7ea49295ca0ceeff6ea07", "type": "zip", "shasum": "", "reference": "2d4230edd4a45da956c7ea49295ca0ceeff6ea07"}, "time": "2023-08-11T08:24:35+00:00"}, {"version": "v8.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b401efab5a5223a41d355daf7efc83a7615c9632"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b401efab5a5223a41d355daf7efc83a7615c9632", "type": "zip", "shasum": "", "reference": "b401efab5a5223a41d355daf7efc83a7615c9632"}, "time": "2023-08-10T02:24:10+00:00"}, {"version": "v8.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "69ca36f0713faad03dffab4708d3381360672019"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/69ca36f0713faad03dffab4708d3381360672019", "type": "zip", "shasum": "", "reference": "69ca36f0713faad03dffab4708d3381360672019"}, "time": "2023-08-09T02:55:32+00:00"}, {"version": "v8.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1c7d8d62a176d3692eb9cbb48f64485d64ed00ec"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1c7d8d62a176d3692eb9cbb48f64485d64ed00ec", "type": "zip", "shasum": "", "reference": "1c7d8d62a176d3692eb9cbb48f64485d64ed00ec"}, "time": "2023-08-08T08:10:31+00:00"}, {"version": "v8.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "03049a27967ab80972a7c0c0a5be87e421b27ff4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/03049a27967ab80972a7c0c0a5be87e421b27ff4", "type": "zip", "shasum": "", "reference": "03049a27967ab80972a7c0c0a5be87e421b27ff4"}, "time": "2023-07-12T00:16:23+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.13.5", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.13.5).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c9c36e8ef9a8b91cbcc85f629be5ff3670133737"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c9c36e8ef9a8b91cbcc85f629be5ff3670133737", "type": "zip", "shasum": "", "reference": "c9c36e8ef9a8b91cbcc85f629be5ff3670133737"}, "time": "2023-06-22T05:28:42+00:00"}, {"version": "v8.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c300e901f434809e414fe83325d9750603a017cb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c300e901f434809e414fe83325d9750603a017cb", "type": "zip", "shasum": "", "reference": "c300e901f434809e414fe83325d9750603a017cb"}, "time": "2023-06-13T06:23:28+00:00"}, {"version": "v8.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b9295f845d376d51e6a1629c116103e7a55c99c8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b9295f845d376d51e6a1629c116103e7a55c99c8", "type": "zip", "shasum": "", "reference": "b9295f845d376d51e6a1629c116103e7a55c99c8"}, "time": "2023-06-08T06:22:10+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.10", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.10).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ea9a5388c124320594ff30384c835cab110fa5ea"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ea9a5388c124320594ff30384c835cab110fa5ea", "type": "zip", "shasum": "", "reference": "ea9a5388c124320594ff30384c835cab110fa5ea"}, "time": "2023-06-07T12:11:33+00:00"}, {"version": "v8.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a65632dbd3c1da8e6686f5b0f304dd5e8ccee598"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a65632dbd3c1da8e6686f5b0f304dd5e8ccee598", "type": "zip", "shasum": "", "reference": "a65632dbd3c1da8e6686f5b0f304dd5e8ccee598"}, "time": "2023-06-06T23:56:22+00:00"}, {"version": "v8.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3575076c6b5115fe109f432652381d3f6b5fdfae"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3575076c6b5115fe109f432652381d3f6b5fdfae", "type": "zip", "shasum": "", "reference": "3575076c6b5115fe109f432652381d3f6b5fdfae"}, "time": "2023-05-26T00:00:34+00:00"}, {"version": "v8.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "bc3331081bf8d85cc0806187427fce35471df6a8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/bc3331081bf8d85cc0806187427fce35471df6a8", "type": "zip", "shasum": "", "reference": "bc3331081bf8d85cc0806187427fce35471df6a8"}, "time": "2023-05-17T00:13:44+00:00"}, {"version": "v8.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "64f87d55a1ad51849558a351e345fd0de4cc7d65"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/64f87d55a1ad51849558a351e345fd0de4cc7d65", "type": "zip", "shasum": "", "reference": "64f87d55a1ad51849558a351e345fd0de4cc7d65"}, "time": "2023-05-09T14:55:44+00:00"}, {"version": "v8.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4d0ed25a83179d8e59487198f619b57b64d4410f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4d0ed25a83179d8e59487198f619b57b64d4410f", "type": "zip", "shasum": "", "reference": "4d0ed25a83179d8e59487198f619b57b64d4410f"}, "time": "2023-04-18T14:07:29+00:00", "extra": {"branch-alias": {"dev-master": "9.0-dev"}}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.8", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.8).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.1).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a244ae2cd9067e280581ae98f010954be73a3b1f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a244ae2cd9067e280581ae98f010954be73a3b1f", "type": "zip", "shasum": "", "reference": "a244ae2cd9067e280581ae98f010954be73a3b1f"}, "time": "2023-04-14T09:30:29+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.6.1", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.1", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.1.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.6.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2a184f5a4e30f3b1e6aad180e6978c38c30fdcfe"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2a184f5a4e30f3b1e6aad180e6978c38c30fdcfe", "type": "zip", "shasum": "", "reference": "2a184f5a4e30f3b1e6aad180e6978c38c30fdcfe"}, "time": "2023-04-11T23:28:32+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.6.1", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.0.7", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.6.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "545d10a6d5e59c4831ff7301c470dc12f58723aa"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/545d10a6d5e59c4831ff7301c470dc12f58723aa", "type": "zip", "shasum": "", "reference": "545d10a6d5e59c4831ff7301c470dc12f58723aa"}, "time": "2023-04-04T23:51:20+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.6.1", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "~10.0.7", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.6.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1b785c2a1a619d5c6751360a1960b8d716d69234"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1b785c2a1a619d5c6751360a1960b8d716d69234", "type": "zip", "shasum": "", "reference": "1b785c2a1a619d5c6751360a1960b8d716d69234"}, "time": "2023-04-02T16:49:31+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.4", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "~10.0.7", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.4).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "01476c2d7b0f1acf2ef5903d7e381155dca668b9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/01476c2d7b0f1acf2ef5903d7e381155dca668b9", "type": "zip", "shasum": "", "reference": "01476c2d7b0f1acf2ef5903d7e381155dca668b9"}, "time": "2023-04-01T14:13:38+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.4", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^10.0.7", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"keywords": ["testing", "BDD", "TDD", "laravel", "orchestra-platform", "orchestral"], "version": "v8.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "80713894335a55e41ddc1a1e5c5cff3d45c36333"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/80713894335a55e41ddc1a1e5c5cff3d45c36333", "type": "zip", "shasum": "", "reference": "80713894335a55e41ddc1a1e5c5cff3d45c36333"}, "time": "2023-03-27T14:55:53+00:00"}, {"version": "v8.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fa72d1867fcd0b1f693e6d8d56a6f24ee27ba459"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fa72d1867fcd0b1f693e6d8d56a6f24ee27ba459", "type": "zip", "shasum": "", "reference": "fa72d1867fcd0b1f693e6d8d56a6f24ee27ba459"}, "time": "2023-03-22T07:28:32+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.4", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^9.6 || ^10.0.7", "spatie/laravel-ray": "^1.32", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v8.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9ec213fb981eda7ba35d1996a2716ee7e24dc0ac"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9ec213fb981eda7ba35d1996a2716ee7e24dc0ac", "type": "zip", "shasum": "", "reference": "9ec213fb981eda7ba35d1996a2716ee7e24dc0ac"}, "time": "2023-03-20T10:00:27+00:00"}, {"version": "v8.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b7785ad15ff83259457b7b70da21b2b89bddfa7b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b7785ad15ff83259457b7b70da21b2b89bddfa7b", "type": "zip", "shasum": "", "reference": "b7785ad15ff83259457b7b70da21b2b89bddfa7b"}, "time": "2023-03-17T23:45:21+00:00"}, {"version": "v8.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b8364097ee6d6d16c59ea86ce9e138b53935fc2f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b8364097ee6d6d16c59ea86ce9e138b53935fc2f", "type": "zip", "shasum": "", "reference": "b8364097ee6d6d16c59ea86ce9e138b53935fc2f"}, "time": "2023-03-10T00:01:53+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.3.3", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.0", "phpstan/phpstan": "^1.10.5", "phpunit/phpunit": "^9.6 || ^10.0.7", "spatie/laravel-ray": "^1.32", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.3.3).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7ca8d678ff5c4aab40af0e72b2592b540602ab5c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7ca8d678ff5c4aab40af0e72b2592b540602ab5c", "type": "zip", "shasum": "", "reference": "7ca8d678ff5c4aab40af0e72b2592b540602ab5c"}, "time": "2023-03-09T03:53:39+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.3.1", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.0", "phpstan/phpstan": "^1.10.5", "phpunit/phpunit": "^9.6 || ^10.0.7", "spatie/laravel-ray": "^1.32", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.3.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "516f1eaa468f447715eee503e59531181ab9552c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/516f1eaa468f447715eee503e59531181ab9552c", "type": "zip", "shasum": "", "reference": "516f1eaa468f447715eee503e59531181ab9552c"}, "time": "2023-02-24T00:36:23+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.1.4", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.0", "phpstan/phpstan": "^1.10.1", "phpunit/phpunit": "^9.6 || ^10.0.7", "spatie/laravel-ray": "^1.32", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.1.4).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dae014dd1e6ceef3ab31708a33496d4060dbe53f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dae014dd1e6ceef3ab31708a33496d4060dbe53f", "type": "zip", "shasum": "", "reference": "dae014dd1e6ceef3ab31708a33496d4060dbe53f"}, "time": "2023-02-21T05:13:12+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.0.3", "laravel/pint": "^1.5", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.0", "phpstan/phpstan": "^1.9.17", "phpunit/phpunit": "^9.6 || ^10.0.7", "spatie/laravel-ray": "^1.32", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.0.3).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v8.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e7a2de7c6cd8fa8f221caa63e64499068a484c9c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e7a2de7c6cd8fa8f221caa63e64499068a484c9c", "type": "zip", "shasum": "", "reference": "e7a2de7c6cd8fa8f221caa63e64499068a484c9c"}, "time": "2023-02-17T09:16:02+00:00"}, {"version": "v8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4b9e7932665c52889a6727e4a6235affea8bb925"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4b9e7932665c52889a6727e4a6235affea8bb925", "type": "zip", "shasum": "", "reference": "4b9e7932665c52889a6727e4a6235affea8bb925"}, "time": "2023-02-14T04:54:05+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^10.0", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^8.0", "phpstan/phpstan": "^1.9.14", "phpunit/phpunit": "^9.6 || ^10.0.7", "spatie/laravel-ray": "^1.32", "symfony/process": "^6.2", "symfony/yaml": "^6.2", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4 || ^7.0).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^10.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.4 || ^7.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^8.0).", "orchestra/testbench-dusk": "Allow using Laravel Du<PERSON> for testing (^8.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.6 || ^10.0.7).", "symfony/yaml": "Required for CLI Commander (^6.2).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"keywords": ["testing", "BDD", "TDD", "laravel", "dev", "laravel-packages"], "version": "v7.57.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4089a22e8ab0b285660a30412454747ec8801aa9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4089a22e8ab0b285660a30412454747ec8801aa9", "type": "zip", "shasum": "", "reference": "4089a22e8ab0b285660a30412454747ec8801aa9"}, "time": "2025-06-08T04:03:03+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Orchestra\\Testbench\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "require": {"php": "^8.0", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.14", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.20", "laravel/pint": "^1.5", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1.14", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.40.2", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.20).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.0.9).", "symfony/yaml": "Required for Testbench CLI (^6.0.9).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0|>=7.0.0", "laravel/framework": "<9.52.20|>=10.0.0", "laravel/serializable-closure": "<1.3.0|>=2.0.0", "orchestra/testbench-dusk": "<7.50.0|>=8.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0|>=7.0.0", "phpunit/phpunit": "<9.5.10|>=10.0.0"}}, {"version": "v7.56.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b5e6e18ebca5864848020b4a85d99b3262a3b690"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b5e6e18ebca5864848020b4a85d99b3262a3b690", "type": "zip", "shasum": "", "reference": "b5e6e18ebca5864848020b4a85d99b3262a3b690"}, "time": "2025-05-12T05:05:15+00:00", "require": {"php": "^8.0", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.32"}}, {"version": "v7.55.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8eee0aaf50423a42941e34e5f4924a44ae734cf6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8eee0aaf50423a42941e34e5f4924a44ae734cf6", "type": "zip", "shasum": "", "reference": "8eee0aaf50423a42941e34e5f4924a44ae734cf6"}, "time": "2025-04-06T05:56:48+00:00", "require": {"php": "^8.0", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.1.0", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.20", "laravel/pint": "^1.5", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.54.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "053fc75d1a3c924c49294981bf30a93ab6c943e1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/053fc75d1a3c924c49294981bf30a93ab6c943e1", "type": "zip", "shasum": "", "reference": "053fc75d1a3c924c49294981bf30a93ab6c943e1"}, "time": "2025-03-06T09:58:19+00:00", "require": {"php": "^8.0", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.5", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-php83": "^1.31"}}, {"version": "v7.53.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "daf2b5b1eac5a721fbb7b2f7166d14d8ebc6f84e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/daf2b5b1eac5a721fbb7b2f7166d14d8ebc6f84e", "type": "zip", "shasum": "", "reference": "daf2b5b1eac5a721fbb7b2f7166d14d8ebc6f84e"}, "time": "2025-02-18T23:52:49+00:00", "require": {"php": "^8.0", "composer-runtime-api": "^2.2", "orchestra/sidekick": "^1.0.2", "symfony/polyfill-php83": "^1.31"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.18", "laravel/pint": "^1.5", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.18).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.0.9).", "symfony/yaml": "Required for Testbench CLI (^6.0.9).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.18 || >=10.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "orchestra/testbench-dusk": "<7.50.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.52.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "659a17e7f440b2a39973ac30e16aa4e1956003ca"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/659a17e7f440b2a39973ac30e16aa4e1956003ca", "type": "zip", "shasum": "", "reference": "659a17e7f440b2a39973ac30e16aa4e1956003ca"}, "time": "2025-02-15T03:41:16+00:00", "require": {"php": "^8.0", "composer-runtime-api": "^2.2", "symfony/polyfill-php83": "^1.31"}}, {"version": "v7.51.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b0ce36e8961f0eb05919e748ad4b2f73eba2fd3b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b0ce36e8961f0eb05919e748ad4b2f73eba2fd3b", "type": "zip", "shasum": "", "reference": "b0ce36e8961f0eb05919e748ad4b2f73eba2fd3b"}, "time": "2025-02-06T04:43:33+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.18", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.39", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.51.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "147bb8eee302f946662b5f860a2d77a2c0399d6b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/147bb8eee302f946662b5f860a2d77a2c0399d6b", "type": "zip", "shasum": "", "reference": "147bb8eee302f946662b5f860a2d77a2c0399d6b"}, "time": "2025-02-02T09:34:20+00:00"}, {"version": "v7.51.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "eb0a8d8724488bbf4835ea964c086ef28e182801"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/eb0a8d8724488bbf4835ea964c086ef28e182801", "type": "zip", "shasum": "", "reference": "eb0a8d8724488bbf4835ea964c086ef28e182801"}, "time": "2025-01-07T01:20:48+00:00"}, {"version": "v7.51.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8a95e117d56924a8cde2de14a94614627a8887ec"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8a95e117d56924a8cde2de14a94614627a8887ec", "type": "zip", "shasum": "", "reference": "8a95e117d56924a8cde2de14a94614627a8887ec"}, "time": "2024-12-24T00:56:58+00:00"}, {"version": "v7.50.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "030ffbc354ddbd0456c552d976307313e93f60ba"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/030ffbc354ddbd0456c552d976307313e93f60ba", "type": "zip", "shasum": "", "reference": "030ffbc354ddbd0456c552d976307313e93f60ba"}, "time": "2024-12-19T00:04:34+00:00"}, {"version": "v7.50.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0c8fb3776c7239ca95adbef5035c0d55719354cb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0c8fb3776c7239ca95adbef5035c0d55719354cb", "type": "zip", "shasum": "", "reference": "0c8fb3776c7239ca95adbef5035c0d55719354cb"}, "time": "2024-12-15T14:03:33+00:00"}, {"version": "v7.49.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e9f7c2f5e5a679ea3367c286e895b429e41d17f0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e9f7c2f5e5a679ea3367c286e895b429e41d17f0", "type": "zip", "shasum": "", "reference": "e9f7c2f5e5a679ea3367c286e895b429e41d17f0"}, "time": "2024-12-14T01:09:14+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.18", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.49.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c76bf8e2ed6dfeb1a2e199242b66b86b4dcb562c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c76bf8e2ed6dfeb1a2e199242b66b86b4dcb562c", "type": "zip", "shasum": "", "reference": "c76bf8e2ed6dfeb1a2e199242b66b86b4dcb562c"}, "time": "2024-12-01T09:05:14+00:00"}, {"version": "v7.48.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ee0cf5f280953b1bc95cd5bc54cdcd0bd722e0db"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ee0cf5f280953b1bc95cd5bc54cdcd0bd722e0db", "type": "zip", "shasum": "", "reference": "ee0cf5f280953b1bc95cd5bc54cdcd0bd722e0db"}, "time": "2024-11-18T12:35:49+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.17", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.17).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.0.9).", "symfony/yaml": "Required for Testbench CLI (^6.0.9).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.17 || >=10.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "orchestra/testbench-dusk": "<7.39.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.47.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "970e1cb332cb0060b724d27e710d94b0f8520038"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/970e1cb332cb0060b724d27e710d94b0f8520038", "type": "zip", "shasum": "", "reference": "970e1cb332cb0060b724d27e710d94b0f8520038"}, "time": "2024-10-25T14:35:48+00:00", "require": {"php": "^8.0", "composer-runtime-api": "^2.2"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.16", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.16).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.0.9).", "symfony/yaml": "Required for Testbench CLI (^6.0.9).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.16 || >=10.0.0", "laravel/serializable-closure": "<1.3.0 || >=2.0.0", "orchestra/testbench-dusk": "<7.39.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.47.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7278024f1353e884a931aae3d38f6cbc748d7cc4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7278024f1353e884a931aae3d38f6cbc748d7cc4", "type": "zip", "shasum": "", "reference": "7278024f1353e884a931aae3d38f6cbc748d7cc4"}, "time": "2024-10-24T02:17:11+00:00"}, {"version": "v7.47.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "de81fd6c5873fa370002a931e45946b3cb6e5dec"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/de81fd6c5873fa370002a931e45946b3cb6e5dec", "type": "zip", "shasum": "", "reference": "de81fd6c5873fa370002a931e45946b3cb6e5dec"}, "time": "2024-10-06T11:16:29+00:00"}, {"version": "v7.47.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cfa1025d3e8052df671475b2c128d4a2e05557ce"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cfa1025d3e8052df671475b2c128d4a2e05557ce", "type": "zip", "shasum": "", "reference": "cfa1025d3e8052df671475b2c128d4a2e05557ce"}, "time": "2024-10-05T14:41:31+00:00"}, {"version": "v7.47.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "620c404531b4e3a339aebbf0d58103191e9b0f7f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/620c404531b4e3a339aebbf0d58103191e9b0f7f", "type": "zip", "shasum": "", "reference": "620c404531b4e3a339aebbf0d58103191e9b0f7f"}, "time": "2024-09-23T13:10:37+00:00", "require": {"php": "^8.0"}, "require-dev": {"composer-runtime-api": "^2.2", "fakerphp/faker": "^1.21", "laravel/framework": "^9.52.16", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.46.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c3861b172a7f912215485f2bd91f93b2870e5df5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c3861b172a7f912215485f2bd91f93b2870e5df5", "type": "zip", "shasum": "", "reference": "c3861b172a7f912215485f2bd91f93b2870e5df5"}, "time": "2024-08-26T04:19:12+00:00"}, {"version": "v7.45.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b239c95e0c99d90d8666a1d3979e4a31c05111cc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b239c95e0c99d90d8666a1d3979e4a31c05111cc", "type": "zip", "shasum": "", "reference": "b239c95e0c99d90d8666a1d3979e4a31c05111cc"}, "time": "2024-08-14T04:54:29+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.16 || >=10.0.0", "orchestra/testbench-dusk": "<7.39.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.44.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ac843b3e05f7e7af56ba3bca50e0592de272100b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ac843b3e05f7e7af56ba3bca50e0592de272100b", "type": "zip", "shasum": "", "reference": "ac843b3e05f7e7af56ba3bca50e0592de272100b"}, "time": "2024-07-19T10:22:28+00:00", "require-dev": {"composer-runtime-api": "^2.2", "fakerphp/faker": "^1.21", "laravel/framework": "^9.52.9", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.9).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^6.0.9).", "symfony/yaml": "Required for Testbench CLI (^6.0.9).", "vlucas/phpdotenv": "Required for Testbench CLI (^5.4.1)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.9 || >=10.0.0", "orchestra/testbench-dusk": "<7.39.0 || >=8.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.44.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5dad4e7070fee4c3bf950588c79f5ded69517e19"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5dad4e7070fee4c3bf950588c79f5ded69517e19", "type": "zip", "shasum": "", "reference": "5dad4e7070fee4c3bf950588c79f5ded69517e19"}, "time": "2024-07-13T06:33:53+00:00"}, {"version": "v7.43.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "74880dc5872e9d3f0e61740fa890d28933e521af"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/74880dc5872e9d3f0e61740fa890d28933e521af", "type": "zip", "shasum": "", "reference": "74880dc5872e9d3f0e61740fa890d28933e521af"}, "time": "2024-06-04T04:54:17+00:00"}, {"version": "v7.43.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e6b44a05163198bc8517b45a8a89c590bc2d2702"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e6b44a05163198bc8517b45a8a89c590bc2d2702", "type": "zip", "shasum": "", "reference": "e6b44a05163198bc8517b45a8a89c590bc2d2702"}, "time": "2024-06-01T09:19:45+00:00"}, {"version": "v7.43.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f96f2de39ea6198666d152297d46972f37dfc72d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f96f2de39ea6198666d152297d46972f37dfc72d", "type": "zip", "shasum": "", "reference": "f96f2de39ea6198666d152297d46972f37dfc72d"}, "time": "2024-05-23T01:45:10+00:00"}, {"version": "v7.43.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "96ca77e5d6fa1a0d371a070be23dbc01cdd595ab"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/96ca77e5d6fa1a0d371a070be23dbc01cdd595ab", "type": "zip", "shasum": "", "reference": "96ca77e5d6fa1a0d371a070be23dbc01cdd595ab"}, "time": "2024-05-20T23:57:49+00:00"}, {"version": "v7.42.7", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0120f5428e6ea9654a7ffe9de1b7d3b48db73375"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0120f5428e6ea9654a7ffe9de1b7d3b48db73375", "type": "zip", "shasum": "", "reference": "0120f5428e6ea9654a7ffe9de1b7d3b48db73375"}, "time": "2024-04-21T07:55:51+00:00", "require-dev": {"composer-runtime-api": "^2.2", "fakerphp/faker": "^1.21", "laravel/framework": "^9.52.9", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.42.6", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "878f0ca5b1a45ba1705d836beaae0ba34cb0e358"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/878f0ca5b1a45ba1705d836beaae0ba34cb0e358", "type": "zip", "shasum": "", "reference": "878f0ca5b1a45ba1705d836beaae0ba34cb0e358"}, "time": "2024-04-13T08:18:48+00:00"}, {"version": "v7.42.5", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "27a892401d2736516c18a8dac91ac4f8616c557c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/27a892401d2736516c18a8dac91ac4f8616c557c", "type": "zip", "shasum": "", "reference": "27a892401d2736516c18a8dac91ac4f8616c557c"}, "time": "2024-03-25T04:21:21+00:00"}, {"version": "v7.42.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "afd77e83c3eb15c9c39cac6e18b2050dd24be252"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/afd77e83c3eb15c9c39cac6e18b2050dd24be252", "type": "zip", "shasum": "", "reference": "afd77e83c3eb15c9c39cac6e18b2050dd24be252"}, "time": "2024-03-19T11:17:24+00:00"}, {"version": "v7.42.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ae93a656c51545834862e7ba3b8cb67d1e6f36a1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ae93a656c51545834862e7ba3b8cb67d1e6f36a1", "type": "zip", "shasum": "", "reference": "ae93a656c51545834862e7ba3b8cb67d1e6f36a1"}, "time": "2024-03-19T01:19:51+00:00"}, {"version": "v7.42.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "99b28f6aaddd47a3ac929c272a5ef22faac5b208"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/99b28f6aaddd47a3ac929c272a5ef22faac5b208", "type": "zip", "shasum": "", "reference": "99b28f6aaddd47a3ac929c272a5ef22faac5b208"}, "time": "2024-03-18T12:10:51+00:00"}, {"version": "v7.42.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cb42d162a42da4614ff69d6d3aff8680896fd699"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cb42d162a42da4614ff69d6d3aff8680896fd699", "type": "zip", "shasum": "", "reference": "cb42d162a42da4614ff69d6d3aff8680896fd699"}, "time": "2024-03-15T12:05:41+00:00"}, {"version": "v7.42.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "595a17f9d3e51d4458eb8ed752c999f857c99309"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/595a17f9d3e51d4458eb8ed752c999f857c99309", "type": "zip", "shasum": "", "reference": "595a17f9d3e51d4458eb8ed752c999f857c99309"}, "time": "2024-03-13T03:48:44+00:00"}, {"version": "v7.41.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4b95a9da5a9b021a7c478edd8a7afc57c84ac16a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4b95a9da5a9b021a7c478edd8a7afc57c84ac16a", "type": "zip", "shasum": "", "reference": "4b95a9da5a9b021a7c478edd8a7afc57c84ac16a"}, "time": "2024-02-21T23:31:00+00:00"}, {"version": "v7.41.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e2f22292a2e1e8987fc6e15cc8a0b2e5f1e035e0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e2f22292a2e1e8987fc6e15cc8a0b2e5f1e035e0", "type": "zip", "shasum": "", "reference": "e2f22292a2e1e8987fc6e15cc8a0b2e5f1e035e0"}, "time": "2024-02-21T14:15:41+00:00"}, {"version": "v7.40.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "768e1942ec3a2138b62c7eb69a5745173e60d519"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/768e1942ec3a2138b62c7eb69a5745173e60d519", "type": "zip", "shasum": "", "reference": "768e1942ec3a2138b62c7eb69a5745173e60d519"}, "time": "2024-01-22T01:40:09+00:00"}, {"version": "v7.40.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "51b88cf7557c2d2e8759f60fd161cfca086f0b19"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/51b88cf7557c2d2e8759f60fd161cfca086f0b19", "type": "zip", "shasum": "", "reference": "51b88cf7557c2d2e8759f60fd161cfca086f0b19"}, "time": "2024-01-19T14:18:44+00:00"}, {"version": "v7.39.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b8b56acd33e75a0d2e502864895e8fa29a197557"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b8b56acd33e75a0d2e502864895e8fa29a197557", "type": "zip", "shasum": "", "reference": "b8b56acd33e75a0d2e502864895e8fa29a197557"}, "time": "2024-01-10T02:57:39+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.9 || >=10.0.0", "orchestra/workbench": "<1.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.39.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0d14148f72bd104ca99f4cbcf19a561ff2fac483"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0d14148f72bd104ca99f4cbcf19a561ff2fac483", "type": "zip", "shasum": "", "reference": "0d14148f72bd104ca99f4cbcf19a561ff2fac483"}, "time": "2023-12-28T13:33:53+00:00", "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "ext-pcntl": "Required to use all features of the console signal trapping.", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.9).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.38.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "826071e0993e0af3f7ac3972ac90e08eb955d469"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/826071e0993e0af3f7ac3972ac90e08eb955d469", "type": "zip", "shasum": "", "reference": "826071e0993e0af3f7ac3972ac90e08eb955d469"}, "time": "2023-12-19T00:32:09+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Orchestra\\Testbench\\": "src/"}}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.9).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.37.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "337d9e7abdb79c1c14a746c79915a1d7f88a662a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/337d9e7abdb79c1c14a746c79915a1d7f88a662a", "type": "zip", "shasum": "", "reference": "337d9e7abdb79c1c14a746c79915a1d7f88a662a"}, "time": "2023-12-06T07:58:07+00:00"}, {"version": "v7.37.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c0b498350d9a869bec58a49b0b6450847295bafb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c0b498350d9a869bec58a49b0b6450847295bafb", "type": "zip", "shasum": "", "reference": "c0b498350d9a869bec58a49b0b6450847295bafb"}, "time": "2023-12-06T02:36:38+00:00"}, {"version": "v7.36.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b019a8cb884e16c7a24e9d3cfa6f8c240b2029ca"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b019a8cb884e16c7a24e9d3cfa6f8c240b2029ca", "type": "zip", "shasum": "", "reference": "b019a8cb884e16c7a24e9d3cfa6f8c240b2029ca"}, "time": "2023-12-04T04:36:03+00:00"}, {"version": "v7.35.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "55677902493d332e838151db81aa55663ed15a71"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/55677902493d332e838151db81aa55663ed15a71", "type": "zip", "shasum": "", "reference": "55677902493d332e838151db81aa55663ed15a71"}, "time": "2023-11-20T22:29:08+00:00"}, {"version": "v7.35.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "218e9c889d08af9c83b10194198329d21adb7af5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/218e9c889d08af9c83b10194198329d21adb7af5", "type": "zip", "shasum": "", "reference": "218e9c889d08af9c83b10194198329d21adb7af5"}, "time": "2023-11-10T04:42:31+00:00"}, {"version": "v7.35.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "04096a2cc23e08351690c13320b862fdd3244317"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/04096a2cc23e08351690c13320b862fdd3244317", "type": "zip", "shasum": "", "reference": "04096a2cc23e08351690c13320b862fdd3244317"}, "time": "2023-11-10T01:55:45+00:00"}, {"version": "v7.34.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4e86414f1e250099d049a5a7784dc56c87975f9c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4e86414f1e250099d049a5a7784dc56c87975f9c", "type": "zip", "shasum": "", "reference": "4e86414f1e250099d049a5a7784dc56c87975f9c"}, "time": "2023-10-30T13:07:43+00:00"}, {"version": "v7.34.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ae742dfa584610b142f3c41284e8268daeb67b23"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ae742dfa584610b142f3c41284e8268daeb67b23", "type": "zip", "shasum": "", "reference": "ae742dfa584610b142f3c41284e8268daeb67b23"}, "time": "2023-10-24T06:15:46+00:00"}, {"version": "v7.34.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5a8f37f9b23107a34dd206aca3c2cf96936a5f49"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5a8f37f9b23107a34dd206aca3c2cf96936a5f49", "type": "zip", "shasum": "", "reference": "5a8f37f9b23107a34dd206aca3c2cf96936a5f49"}, "time": "2023-10-24T04:16:44+00:00"}, {"version": "v7.33.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6792f5d960c77c5746027e06699c48874fe8230f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6792f5d960c77c5746027e06699c48874fe8230f", "type": "zip", "shasum": "", "reference": "6792f5d960c77c5746027e06699c48874fe8230f"}, "time": "2023-10-09T10:51:20+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.9", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<9.52.9 || >=10.0.0", "nunomaduro/collision": "<6.2.0 || >=7.0.0", "phpunit/phpunit": "<9.5.10 || >=10.0.0"}}, {"version": "v7.32.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9443467400b4f848bb5a1649e1b21cf7010604fb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9443467400b4f848bb5a1649e1b21cf7010604fb", "type": "zip", "shasum": "", "reference": "9443467400b4f848bb5a1649e1b21cf7010604fb"}, "time": "2023-09-26T12:49:36+00:00", "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.9).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}, "conflict": "__unset"}, {"version": "v7.32.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ba9a01a94b44e07cceb082e434ef70ebd278021d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ba9a01a94b44e07cceb082e434ef70ebd278021d", "type": "zip", "shasum": "", "reference": "ba9a01a94b44e07cceb082e434ef70ebd278021d"}, "time": "2023-09-25T13:37:52+00:00"}, {"version": "v7.31.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f39b69a106cf69dc012064e608468b982f25e255"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f39b69a106cf69dc012064e608468b982f25e255", "type": "zip", "shasum": "", "reference": "f39b69a106cf69dc012064e608468b982f25e255"}, "time": "2023-09-25T01:54:45+00:00"}, {"version": "v7.31.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4edd3e0fae0ec93ae37d6c3b36b433431211f7b2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4edd3e0fae0ec93ae37d6c3b36b433431211f7b2", "type": "zip", "shasum": "", "reference": "4edd3e0fae0ec93ae37d6c3b36b433431211f7b2"}, "time": "2023-09-21T02:44:14+00:00"}, {"version": "v7.31.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ac2a6f632fc1258ec06f491561a0dcf19511113b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ac2a6f632fc1258ec06f491561a0dcf19511113b", "type": "zip", "shasum": "", "reference": "ac2a6f632fc1258ec06f491561a0dcf19511113b"}, "time": "2023-09-19T10:55:52+00:00"}, {"version": "v7.31.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3a0a051390441730cf7238eddaf76ade28dfbbd2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3a0a051390441730cf7238eddaf76ade28dfbbd2", "type": "zip", "shasum": "", "reference": "3a0a051390441730cf7238eddaf76ade28dfbbd2"}, "time": "2023-09-19T04:26:07+00:00"}, {"version": "v7.30.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d2163a287e9ef3a4471e5564c4d5540af6d7f32d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d2163a287e9ef3a4471e5564c4d5540af6d7f32d", "type": "zip", "shasum": "", "reference": "d2163a287e9ef3a4471e5564c4d5540af6d7f32d"}, "time": "2023-09-09T02:23:26+00:00"}, {"version": "v7.30.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7efc82868714a391cdf05d53f079a2769c62782b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7efc82868714a391cdf05d53f079a2769c62782b", "type": "zip", "shasum": "", "reference": "7efc82868714a391cdf05d53f079a2769c62782b"}, "time": "2023-08-29T05:20:07+00:00"}, {"version": "v7.29.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "45e829fe9e649e5392e4a8237a6ba7921f3f8527"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/45e829fe9e649e5392e4a8237a6ba7921f3f8527", "type": "zip", "shasum": "", "reference": "45e829fe9e649e5392e4a8237a6ba7921f3f8527"}, "time": "2023-08-22T08:46:32+00:00"}, {"version": "v7.29.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7af069e188b7e1a248de4c5de00616b1f38b52c0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7af069e188b7e1a248de4c5de00616b1f38b52c0", "type": "zip", "shasum": "", "reference": "7af069e188b7e1a248de4c5de00616b1f38b52c0"}, "time": "2023-08-19T03:38:07+00:00"}, {"version": "v7.28.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "caefad74423184d6c02566751afef40ba1c289e0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/caefad74423184d6c02566751afef40ba1c289e0", "type": "zip", "shasum": "", "reference": "caefad74423184d6c02566751afef40ba1c289e0"}, "time": "2023-08-18T02:46:13+00:00"}, {"version": "v7.28.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0f16c7c0c78a64c803b7a418893a8ed6895435fc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0f16c7c0c78a64c803b7a418893a8ed6895435fc", "type": "zip", "shasum": "", "reference": "0f16c7c0c78a64c803b7a418893a8ed6895435fc"}, "time": "2023-08-17T09:01:21+00:00"}, {"version": "v7.28.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "328aab2533fc6c5fc10e1d6c6a644381acda6987"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/328aab2533fc6c5fc10e1d6c6a644381acda6987", "type": "zip", "shasum": "", "reference": "328aab2533fc6c5fc10e1d6c6a644381acda6987"}, "time": "2023-08-16T22:34:56+00:00"}, {"version": "v7.28.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7b98009c8b28d0391fb8653fdcce45534e27886e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7b98009c8b28d0391fb8653fdcce45534e27886e", "type": "zip", "shasum": "", "reference": "7b98009c8b28d0391fb8653fdcce45534e27886e"}, "time": "2023-08-16T02:12:56+00:00"}, {"version": "v7.28.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "82b93a39b80d943b062e6b72ce4b683701e8fea6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/82b93a39b80d943b062e6b72ce4b683701e8fea6", "type": "zip", "shasum": "", "reference": "82b93a39b80d943b062e6b72ce4b683701e8fea6"}, "time": "2023-08-15T09:01:27+00:00"}, {"version": "v7.27.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0cbc30e8b5a2a7144b7f872d44b2fbe95c1f4597"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0cbc30e8b5a2a7144b7f872d44b2fbe95c1f4597", "type": "zip", "shasum": "", "reference": "0cbc30e8b5a2a7144b7f872d44b2fbe95c1f4597"}, "time": "2023-08-12T10:31:43+00:00"}, {"version": "v7.27.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "73ba0e80dc7b8905870e2b7c0a1ce4bd87ab1d1a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/73ba0e80dc7b8905870e2b7c0a1ce4bd87ab1d1a", "type": "zip", "shasum": "", "reference": "73ba0e80dc7b8905870e2b7c0a1ce4bd87ab1d1a"}, "time": "2023-08-12T04:24:23+00:00"}, {"version": "v7.26.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cbffadcc4da002b19503ac0e45a145fbc987c0e6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cbffadcc4da002b19503ac0e45a145fbc987c0e6", "type": "zip", "shasum": "", "reference": "cbffadcc4da002b19503ac0e45a145fbc987c0e6"}, "time": "2023-08-11T08:18:39+00:00"}, {"version": "v7.26.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c90a2d7a2da44f0bb9c130a22462682e404b752e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c90a2d7a2da44f0bb9c130a22462682e404b752e", "type": "zip", "shasum": "", "reference": "c90a2d7a2da44f0bb9c130a22462682e404b752e"}, "time": "2023-08-10T02:23:06+00:00"}, {"version": "v7.26.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "89414cb81c09a33038034631dc6c703227a55eaa"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/89414cb81c09a33038034631dc6c703227a55eaa", "type": "zip", "shasum": "", "reference": "89414cb81c09a33038034631dc6c703227a55eaa"}, "time": "2023-08-09T02:49:22+00:00"}, {"version": "v7.26.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c5148b77463b0dff264e534d757d26b508eb84c5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c5148b77463b0dff264e534d757d26b508eb84c5", "type": "zip", "shasum": "", "reference": "c5148b77463b0dff264e534d757d26b508eb84c5"}, "time": "2023-08-08T07:57:38+00:00"}, {"version": "v7.25.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d5da4c5733ede2c39658adedc45fa74e15f8c957"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d5da4c5733ede2c39658adedc45fa74e15f8c957", "type": "zip", "shasum": "", "reference": "d5da4c5733ede2c39658adedc45fa74e15f8c957"}, "time": "2023-06-13T05:37:48+00:00"}, {"version": "v7.24.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "78d2245cd542b0ed69407eaae7e8e42abddb07a2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/78d2245cd542b0ed69407eaae7e8e42abddb07a2", "type": "zip", "shasum": "", "reference": "78d2245cd542b0ed69407eaae7e8e42abddb07a2"}, "time": "2023-04-27T01:11:38+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.4", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.32.4", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.52.4).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.24.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1f29cd3cb23a64d80d8bd6efc33c662efa3cbef0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1f29cd3cb23a64d80d8bd6efc33c662efa3cbef0", "type": "zip", "shasum": "", "reference": "1f29cd3cb23a64d80d8bd6efc33c662efa3cbef0"}, "time": "2023-04-11T08:00:52+00:00"}, {"version": "v7.24.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4d9277728235a7fe3724397b8195dda3d39e1bdf"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4d9277728235a7fe3724397b8195dda3d39e1bdf", "type": "zip", "shasum": "", "reference": "4d9277728235a7fe3724397b8195dda3d39e1bdf"}, "time": "2023-04-02T16:09:59+00:00"}, {"version": "v7.24.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8a9fc7a0b75191dcdf19a0e6c02de683ab858ff3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8a9fc7a0b75191dcdf19a0e6c02de683ab858ff3", "type": "zip", "shasum": "", "reference": "8a9fc7a0b75191dcdf19a0e6c02de683ab858ff3"}, "time": "2023-04-01T14:04:15+00:00"}, {"keywords": ["testing", "BDD", "TDD", "laravel", "orchestra-platform", "orchestral"], "version": "v7.23.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4716f536ff5ea3f7dd09883efbcc158a3002c145"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4716f536ff5ea3f7dd09883efbcc158a3002c145", "type": "zip", "shasum": "", "reference": "4716f536ff5ea3f7dd09883efbcc158a3002c145"}, "time": "2023-03-27T14:27:07+00:00"}, {"version": "v7.22.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c34bcaf4888cb680f76eaca11ec9c4a67c019a0b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c34bcaf4888cb680f76eaca11ec9c4a67c019a0b", "type": "zip", "shasum": "", "reference": "c34bcaf4888cb680f76eaca11ec9c4a67c019a0b"}, "time": "2023-03-23T08:52:15+00:00"}, {"version": "v7.22.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "75b42f9130e7903ffa0ef8dbb466962ca6635261"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/75b42f9130e7903ffa0ef8dbb466962ca6635261", "type": "zip", "shasum": "", "reference": "75b42f9130e7903ffa0ef8dbb466962ca6635261"}, "time": "2023-02-23T12:15:23+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.52.4", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.9.14", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.22.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a6e4240149ffc1bb3068a68fe4261a5b59753079"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a6e4240149ffc1bb3068a68fe4261a5b59753079", "type": "zip", "shasum": "", "reference": "a6e4240149ffc1bb3068a68fe4261a5b59753079"}, "time": "2023-02-08T02:19:30+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.50.2", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.9.14", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.50.2).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.21.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "07d48ce0fd781626e809e1a7591b75a89e6a2911"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/07d48ce0fd781626e809e1a7591b75a89e6a2911", "type": "zip", "shasum": "", "reference": "07d48ce0fd781626e809e1a7591b75a89e6a2911"}, "time": "2023-02-03T02:00:17+00:00"}, {"version": "v7.20.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f37b3ef9a1d679609d87477073836036faf5671b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f37b3ef9a1d679609d87477073836036faf5671b", "type": "zip", "shasum": "", "reference": "f37b3ef9a1d679609d87477073836036faf5671b"}, "time": "2023-02-01T02:08:55+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.45", "laravel/pint": "^1.4", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.9.14", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.45).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.19.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2bfbbd451481cf27c9663060b792487925236500"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2bfbbd451481cf27c9663060b792487925236500", "type": "zip", "shasum": "", "reference": "2bfbbd451481cf27c9663060b792487925236500"}, "time": "2023-01-10T10:14:44+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.45", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.18.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "81edf14237d8ac31ebdb627128b62c08dce5c34a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/81edf14237d8ac31ebdb627128b62c08dce5c34a", "type": "zip", "shasum": "", "reference": "81edf14237d8ac31ebdb627128b62c08dce5c34a"}, "time": "2023-01-04T00:14:05+00:00"}, {"version": "v7.17.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fb2408561c92cf9ef54a8af36e6d8965a9e15e52"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fb2408561c92cf9ef54a8af36e6d8965a9e15e52", "type": "zip", "shasum": "", "reference": "fb2408561c92cf9ef54a8af36e6d8965a9e15e52"}, "time": "2022-12-22T02:11:25+00:00"}, {"version": "v7.16.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "44ad7354f0bde2cd70fbe7daff6274c7e3838a41"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/44ad7354f0bde2cd70fbe7daff6274c7e3838a41", "type": "zip", "shasum": "", "reference": "44ad7354f0bde2cd70fbe7daff6274c7e3838a41"}, "time": "2022-12-17T05:15:50+00:00", "require-dev": {"fakerphp/faker": "^1.21", "laravel/framework": "^9.44", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.21).", "laravel/framework": "Required for testing (^9.44).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.15.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "47fa2d2802ce3fda44d766f8b225effa9a0ea0f8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/47fa2d2802ce3fda44d766f8b225effa9a0ea0f8", "type": "zip", "shasum": "", "reference": "47fa2d2802ce3fda44d766f8b225effa9a0ea0f8"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://liberapay.com/crynobone", "type": "liberapay"}], "time": "2022-11-29T22:30:20+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.41", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.41).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.14.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "aae50b7278bca62e7b46f1ba15fb82c8437a972e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/aae50b7278bca62e7b46f1ba15fb82c8437a972e", "type": "zip", "shasum": "", "reference": "aae50b7278bca62e7b46f1ba15fb82c8437a972e"}, "time": "2022-11-29T04:21:17+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.36", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ray": "^1.28", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.34).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.5.1).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0.9).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.14.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fb8c90c5f5cb9033e085e4fbf581093cd6e32862"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fb8c90c5f5cb9033e085e4fbf581093cd6e32862", "type": "zip", "shasum": "", "reference": "fb8c90c5f5cb9033e085e4fbf581093cd6e32862"}, "time": "2022-11-22T09:06:48+00:00"}, {"version": "v7.13.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9faea2c774e2f7f60277f531ca4852661c1172a7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9faea2c774e2f7f60277f531ca4852661c1172a7", "type": "zip", "shasum": "", "reference": "9faea2c774e2f7f60277f531ca4852661c1172a7"}, "time": "2022-11-14T02:26:50+00:00"}, {"version": "v7.12.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0bad7911de0af7e06ce4d030e84508ddad2b499c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0bad7911de0af7e06ce4d030e84508ddad2b499c", "type": "zip", "shasum": "", "reference": "0bad7911de0af7e06ce4d030e84508ddad2b499c"}, "time": "2022-11-12T11:49:22+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.36", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.10", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.12.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "97533612da9a5ccc7106268072f8fe5d3648f563"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/97533612da9a5ccc7106268072f8fe5d3648f563", "type": "zip", "shasum": "", "reference": "97533612da9a5ccc7106268072f8fe5d3648f563"}, "time": "2022-11-12T04:26:31+00:00"}, {"version": "v7.11.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2406f71150840be63673310b771dca233123876c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2406f71150840be63673310b771dca233123876c", "type": "zip", "shasum": "", "reference": "2406f71150840be63673310b771dca233123876c"}, "time": "2022-11-05T09:47:28+00:00"}, {"version": "v7.11.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e3855e6d24438824119cf225b882209aaea6b35c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e3855e6d24438824119cf225b882209aaea6b35c", "type": "zip", "shasum": "", "reference": "e3855e6d24438824119cf225b882209aaea6b35c"}, "time": "2022-11-05T08:53:42+00:00"}, {"version": "v7.11.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7b67c9e23e9eb6e3e3b336f9ba3afe99072795e2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7b67c9e23e9eb6e3e3b336f9ba3afe99072795e2", "type": "zip", "shasum": "", "reference": "7b67c9e23e9eb6e3e3b336f9ba3afe99072795e2"}, "time": "2022-10-19T11:47:55+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.36", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.10.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ef1c1bfe5f05f96151a5bd9a9ccd7c10650bc5d1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ef1c1bfe5f05f96151a5bd9a9ccd7c10650bc5d1", "type": "zip", "shasum": "", "reference": "ef1c1bfe5f05f96151a5bd9a9ccd7c10650bc5d1"}, "time": "2022-10-14T11:20:52+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.34", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.5.1", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10", "symfony/process": "^6.0.9", "symfony/yaml": "^6.0.9", "vlucas/phpdotenv": "^5.4.1"}}, {"version": "v7.10.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e4065bc5085aa0e643b901171cdb7fd95c0e8f3c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e4065bc5085aa0e643b901171cdb7fd95c0e8f3c", "type": "zip", "shasum": "", "reference": "e4065bc5085aa0e643b901171cdb7fd95c0e8f3c"}, "time": "2022-10-11T12:29:15+00:00"}, {"version": "v7.10.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8f771908aed0822d5de89de35cdb89b45db2a749"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8f771908aed0822d5de89de35cdb89b45db2a749", "type": "zip", "shasum": "", "reference": "8f771908aed0822d5de89de35cdb89b45db2a749"}, "time": "2022-10-11T09:26:55+00:00"}, {"version": "v7.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4580f609511ea16a1e8143bc45322964f05e5d3e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4580f609511ea16a1e8143bc45322964f05e5d3e", "type": "zip", "shasum": "", "reference": "4580f609511ea16a1e8143bc45322964f05e5d3e"}, "time": "2022-10-05T00:30:33+00:00"}, {"version": "v7.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ce07460265ac4dbe84ebaca73fc6b11beb935c69"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ce07460265ac4dbe84ebaca73fc6b11beb935c69", "type": "zip", "shasum": "", "reference": "ce07460265ac4dbe84ebaca73fc6b11beb935c69"}, "time": "2022-10-03T14:09:25+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.32", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.32).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e9f7bf1765825c562182f7a6cbde854ed512a42f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e9f7bf1765825c562182f7a6cbde854ed512a42f", "type": "zip", "shasum": "", "reference": "e9f7bf1765825c562182f7a6cbde854ed512a42f"}, "time": "2022-09-28T05:52:37+00:00"}, {"version": "v7.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c310e732203e3b40d12dcbd91b7e3451053d0cd7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c310e732203e3b40d12dcbd91b7e3451053d0cd7", "type": "zip", "shasum": "", "reference": "c310e732203e3b40d12dcbd91b7e3451053d0cd7"}, "time": "2022-09-28T01:42:50+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.19", "laravel/laravel": "9.x-dev", "laravel/pint": "^1.1", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.19).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4f861e993a990f4ea33d44c82bd69fd463bd769f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4f861e993a990f4ea33d44c82bd69fd463bd769f", "type": "zip", "shasum": "", "reference": "4f861e993a990f4ea33d44c82bd69fd463bd769f"}, "time": "2022-08-24T01:29:09+00:00"}, {"version": "v7.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cc12b2f4ec84d7928538785234f353cfa4bb8009"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cc12b2f4ec84d7928538785234f353cfa4bb8009", "type": "zip", "shasum": "", "reference": "cc12b2f4ec84d7928538785234f353cfa4bb8009"}, "time": "2022-08-10T02:28:59+00:00"}, {"version": "v7.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3e4e7ed08c353f2178b0cbce845ca90c3cb28e08"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3e4e7ed08c353f2178b0cbce845ca90c3cb28e08", "type": "zip", "shasum": "", "reference": "3e4e7ed08c353f2178b0cbce845ca90c3cb28e08"}, "time": "2022-06-30T09:52:25+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.19", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.19).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "72eb3e159d17ff42bf786e9cea83f55e9c297380"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/72eb3e159d17ff42bf786e9cea83f55e9c297380", "type": "zip", "shasum": "", "reference": "72eb3e159d17ff42bf786e9cea83f55e9c297380"}, "time": "2022-05-10T22:45:44+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.12", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.12).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "104ae725e41e49c1c7a2d09ffad7be63e52fe454"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/104ae725e41e49c1c7a2d09ffad7be63e52fe454", "type": "zip", "shasum": "", "reference": "104ae725e41e49c1c7a2d09ffad7be63e52fe454"}, "time": "2022-04-13T00:51:27+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.7", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"brianium/paratest": "Allow using parallel tresting (^6.4).", "fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.6).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^6.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7fb2f5f0809051b46188ef1510cc35c851c01340"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7fb2f5f0809051b46188ef1510cc35c851c01340", "type": "zip", "shasum": "", "reference": "7fb2f5f0809051b46188ef1510cc35c851c01340"}, "time": "2022-03-30T02:26:06+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.6", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.6).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a6f193db075c8d90d39a40cd502bb99113bfb56c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a6f193db075c8d90d39a40cd502bb99113bfb56c", "type": "zip", "shasum": "", "reference": "a6f193db075c8d90d39a40cd502bb99113bfb56c"}, "time": "2022-03-20T07:06:20+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.5.1", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.5.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c44550eae79877d8e23ee5d8d7c49d3b9bf6ded9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c44550eae79877d8e23ee5d8d7c49d3b9bf6ded9", "type": "zip", "shasum": "", "reference": "c44550eae79877d8e23ee5d8d7c49d3b9bf6ded9"}, "time": "2022-02-23T01:10:25+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.2", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.2).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "06fc0055c3e99f469f9f4fa346b7d677974f8fef"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/06fc0055c3e99f469f9f4fa346b7d677974f8fef", "type": "zip", "shasum": "", "reference": "06fc0055c3e99f469f9f4fa346b7d677974f8fef"}, "time": "2022-02-16T00:42:51+00:00", "require-dev": {"fakerphp/faker": "^1.9.2", "laravel/framework": "^9.0", "laravel/laravel": "9.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^7.0", "phpunit/phpunit": "^9.5.10 || ^10.0", "symfony/process": "^6.0", "symfony/yaml": "^6.0", "vlucas/phpdotenv": "^5.4.1"}, "suggest": {"fakerphp/faker": "Allow using Faker for testing (^1.9.2).", "laravel/framework": "Required for testing (^9.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^7.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^7.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^9.5.10|^10.0).", "symfony/yaml": "Required for CLI Commander (^6.0).", "vlucas/phpdotenv": "Required for CLI Commander (^5.4.1)."}}, {"version": "v7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c28b819c77c5dbf4a94196ffce7dc2ff61a28899"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c28b819c77c5dbf4a94196ffce7dc2ff61a28899", "type": "zip", "shasum": "", "reference": "c28b819c77c5dbf4a94196ffce7dc2ff61a28899"}, "time": "2022-02-14T05:57:44+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3b0ae7a9b39c193ac059024ddf76586c49b45887"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3b0ae7a9b39c193ac059024ddf76586c49b45887", "type": "zip", "shasum": "", "reference": "3b0ae7a9b39c193ac059024ddf76586c49b45887"}, "time": "2022-02-08T15:26:01+00:00"}, {"keywords": ["testing", "BDD", "TDD", "laravel", "dev", "laravel-packages"], "version": "v6.53.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "bf53b211ea3e6ebec7a40647c97994632018d60f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/bf53b211ea3e6ebec7a40647c97994632018d60f", "type": "zip", "shasum": "", "reference": "bf53b211ea3e6ebec7a40647c97994632018d60f"}, "funding": [], "time": "2024-10-06T11:15:22+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Orchestra\\Testbench\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "require": {"php": "^7.3 || ^8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^5.0", "vlucas/phpdotenv": "^5.1"}, "require-dev": {"laravel/framework": "^8.83.27", "laravel/tinker": "^2.9", "mockery/mockery": "^1.4.4", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.31", "symfony/process": "^5.0"}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "laravel/framework": "Required for testing (^8.83.27).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^5.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0).", "symfony/process": "Required to use Orchestra\\Testbench\\remote function (^5.0)."}, "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<8.83.27 || >=9.0.0", "nunomaduro/collision": "<5.0.0 || >=6.0.0", "orchestra/testbench-dusk": "<6.43.0 || >=7.0.0", "phpunit/phpunit": "<8.5.21 || >=9.0.0 <9.5.10 || >=10.0.0"}}, {"version": "v6.53.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "bd584be92b7d0466f47373eafa5ec3ce47cf38f9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/bd584be92b7d0466f47373eafa5ec3ce47cf38f9", "type": "zip", "shasum": "", "reference": "bd584be92b7d0466f47373eafa5ec3ce47cf38f9"}, "time": "2024-09-23T13:04:35+00:00"}, {"version": "v6.52.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c5c3006005b18950020de30e49a3e711056f8349"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c5c3006005b18950020de30e49a3e711056f8349", "type": "zip", "shasum": "", "reference": "c5c3006005b18950020de30e49a3e711056f8349"}, "time": "2024-08-26T04:02:09+00:00"}, {"version": "v6.51.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "341f36ba32e71478af56fac236bd340969f4598a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/341f36ba32e71478af56fac236bd340969f4598a", "type": "zip", "shasum": "", "reference": "341f36ba32e71478af56fac236bd340969f4598a"}, "time": "2024-08-14T02:53:32+00:00"}, {"version": "v6.51.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "87d99f0dacf8b8377469f1e4f83692d5dab8e5dd"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/87d99f0dacf8b8377469f1e4f83692d5dab8e5dd", "type": "zip", "shasum": "", "reference": "87d99f0dacf8b8377469f1e4f83692d5dab8e5dd"}, "time": "2024-07-19T10:14:07+00:00"}, {"version": "v6.51.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dcd1ecdfdcad7f7be88fe7fa645cd24caf32a0f2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dcd1ecdfdcad7f7be88fe7fa645cd24caf32a0f2", "type": "zip", "shasum": "", "reference": "dcd1ecdfdcad7f7be88fe7fa645cd24caf32a0f2"}, "time": "2024-07-13T06:21:45+00:00"}, {"version": "v6.50.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5460274d1f85a4880892389ba6e4d0697d4ee7ed"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5460274d1f85a4880892389ba6e4d0697d4ee7ed", "type": "zip", "shasum": "", "reference": "5460274d1f85a4880892389ba6e4d0697d4ee7ed"}, "time": "2024-06-04T04:49:13+00:00", "require-dev": {"laravel/framework": "^8.83.27", "mockery/mockery": "^1.4.4", "phpstan/phpstan": "^1.11", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}}, {"version": "v6.50.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e2f995addf1afc57dea618d0460151dea3a7ad45"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e2f995addf1afc57dea618d0460151dea3a7ad45", "type": "zip", "shasum": "", "reference": "e2f995addf1afc57dea618d0460151dea3a7ad45"}, "time": "2024-06-01T09:13:34+00:00"}, {"version": "v6.50.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "072a7dbf93cb36a4fce441cbd802c7dfc7d598ab"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/072a7dbf93cb36a4fce441cbd802c7dfc7d598ab", "type": "zip", "shasum": "", "reference": "072a7dbf93cb36a4fce441cbd802c7dfc7d598ab"}, "time": "2024-05-20T23:48:06+00:00"}, {"version": "v6.49.7", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6de78f3365823cdd4f9fd3223a340fcae56bcb08"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6de78f3365823cdd4f9fd3223a340fcae56bcb08", "type": "zip", "shasum": "", "reference": "6de78f3365823cdd4f9fd3223a340fcae56bcb08"}, "time": "2024-04-21T05:42:10+00:00", "require-dev": {"laravel/framework": "^8.83.27", "mockery/mockery": "^1.4.4", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}}, {"version": "v6.49.6", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ad49a508f9cf8c577c3f355f28b2308b998c448c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ad49a508f9cf8c577c3f355f28b2308b998c448c", "type": "zip", "shasum": "", "reference": "ad49a508f9cf8c577c3f355f28b2308b998c448c"}, "time": "2024-04-13T08:11:03+00:00"}, {"version": "v6.49.5", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "45418b85d24a146f5a2f5caeff3f85e481d65b6c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/45418b85d24a146f5a2f5caeff3f85e481d65b6c", "type": "zip", "shasum": "", "reference": "45418b85d24a146f5a2f5caeff3f85e481d65b6c"}, "time": "2024-03-25T04:15:06+00:00"}, {"version": "v6.49.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e99d51d095da503373ae47f508d9df426a2fe9d2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e99d51d095da503373ae47f508d9df426a2fe9d2", "type": "zip", "shasum": "", "reference": "e99d51d095da503373ae47f508d9df426a2fe9d2"}, "time": "2024-03-19T11:14:04+00:00"}, {"version": "v6.49.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c9010622e39ad800d1218cdaa95741760be1c0aa"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c9010622e39ad800d1218cdaa95741760be1c0aa", "type": "zip", "shasum": "", "reference": "c9010622e39ad800d1218cdaa95741760be1c0aa"}, "time": "2024-03-19T01:14:57+00:00"}, {"version": "v6.49.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7dcc34504a96c6323a9d646b2f0f0ac31e39ccbf"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7dcc34504a96c6323a9d646b2f0f0ac31e39ccbf", "type": "zip", "shasum": "", "reference": "7dcc34504a96c6323a9d646b2f0f0ac31e39ccbf"}, "time": "2024-03-18T12:06:27+00:00"}, {"version": "v6.49.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "411142fb1f29a75450f78692b205a3042c63f1ac"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/411142fb1f29a75450f78692b205a3042c63f1ac", "type": "zip", "shasum": "", "reference": "411142fb1f29a75450f78692b205a3042c63f1ac"}, "time": "2024-03-15T12:03:52+00:00"}, {"version": "v6.49.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cafd14365102c7703db2a1416d2d0c16c6887106"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cafd14365102c7703db2a1416d2d0c16c6887106", "type": "zip", "shasum": "", "reference": "cafd14365102c7703db2a1416d2d0c16c6887106"}, "time": "2024-03-13T03:36:59+00:00"}, {"version": "v6.48.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ff12425668576635dfb35cf566a7571966b0d70b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ff12425668576635dfb35cf566a7571966b0d70b", "type": "zip", "shasum": "", "reference": "ff12425668576635dfb35cf566a7571966b0d70b"}, "time": "2024-02-21T14:01:47+00:00"}, {"version": "v6.47.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "df273267a0eabe475e87937c5d511cae23e3a4e2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/df273267a0eabe475e87937c5d511cae23e3a4e2", "type": "zip", "shasum": "", "reference": "df273267a0eabe475e87937c5d511cae23e3a4e2"}, "time": "2024-01-22T00:54:20+00:00"}, {"version": "v6.47.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "61654dde8729a4e8173a6bd3112443683fd54cc0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/61654dde8729a4e8173a6bd3112443683fd54cc0", "type": "zip", "shasum": "", "reference": "61654dde8729a4e8173a6bd3112443683fd54cc0"}, "time": "2024-01-19T13:48:01+00:00"}, {"version": "v6.46.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cba9f553c26bc836e185124b09e2647d22f21d5a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cba9f553c26bc836e185124b09e2647d22f21d5a", "type": "zip", "shasum": "", "reference": "cba9f553c26bc836e185124b09e2647d22f21d5a"}, "time": "2024-01-10T02:48:54+00:00", "conflict": {"brianium/paratest": "<6.4.0 || >=7.0.0", "laravel/framework": "<8.83.27 || >=9.0.0", "nunomaduro/collision": "<5.0.0 || >=6.0.0", "phpunit/phpunit": "<8.5.21 || >=9.0.0 <9.5.10 || >=10.0.0"}}, {"version": "v6.45.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b409d050a2366ea2131f919f37c6c0619ccf6395"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b409d050a2366ea2131f919f37c6c0619ccf6395", "type": "zip", "shasum": "", "reference": "b409d050a2366ea2131f919f37c6c0619ccf6395"}, "time": "2023-12-19T00:23:23+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Orchestra\\Testbench\\": "src/"}}, "suggest": {"brianium/paratest": "Allow using parallel testing (^6.4).", "laravel/framework": "Required for testing (^8.83.27).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "nunomaduro/collision": "Allow using Laravel style tests output and parallel testing (^5.0).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0)."}}, {"version": "v6.44.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "11ef38578a113f883543065fb7cdd6638a5a4e17"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/11ef38578a113f883543065fb7cdd6638a5a4e17", "type": "zip", "shasum": "", "reference": "11ef38578a113f883543065fb7cdd6638a5a4e17"}, "time": "2023-12-06T02:33:07+00:00"}, {"version": "v6.43.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0647a1a3df70b6f55719f25c33f4ffeb6db01055"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0647a1a3df70b6f55719f25c33f4ffeb6db01055", "type": "zip", "shasum": "", "reference": "0647a1a3df70b6f55719f25c33f4ffeb6db01055"}, "time": "2023-12-04T04:34:10+00:00"}, {"version": "v6.42.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8e94aa80548be9f1109f70ef87c1e5c6e0bd08cd"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8e94aa80548be9f1109f70ef87c1e5c6e0bd08cd", "type": "zip", "shasum": "", "reference": "8e94aa80548be9f1109f70ef87c1e5c6e0bd08cd"}, "time": "2023-11-20T22:25:35+00:00"}, {"version": "v6.42.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "63f71994b8d9973d331016913a8cfc0c88a0046d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/63f71994b8d9973d331016913a8cfc0c88a0046d", "type": "zip", "shasum": "", "reference": "63f71994b8d9973d331016913a8cfc0c88a0046d"}, "time": "2023-11-10T01:52:26+00:00"}, {"version": "v6.41.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "574e0a7a16f7682f5f6f9ce081636a6d4b5cb9d8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/574e0a7a16f7682f5f6f9ce081636a6d4b5cb9d8", "type": "zip", "shasum": "", "reference": "574e0a7a16f7682f5f6f9ce081636a6d4b5cb9d8"}, "time": "2023-10-24T05:49:11+00:00"}, {"version": "v6.41.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "93fc87de41261f7176d444c30fc7ae82a67d366f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/93fc87de41261f7176d444c30fc7ae82a67d366f", "type": "zip", "shasum": "", "reference": "93fc87de41261f7176d444c30fc7ae82a67d366f"}, "time": "2023-10-24T04:09:37+00:00"}, {"version": "v6.40.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e524b18254d725992caafa96a0b45e8c30227b5b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e524b18254d725992caafa96a0b45e8c30227b5b", "type": "zip", "shasum": "", "reference": "e524b18254d725992caafa96a0b45e8c30227b5b"}, "time": "2023-10-09T10:27:18+00:00"}, {"version": "v6.39.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d009639acd07666ad5c89e458973a2c5f40a5d5a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d009639acd07666ad5c89e458973a2c5f40a5d5a", "type": "zip", "shasum": "", "reference": "d009639acd07666ad5c89e458973a2c5f40a5d5a"}, "time": "2023-09-26T12:46:38+00:00", "suggest": {"laravel/framework": "Required for testing (^8.83.26).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0)."}, "conflict": "__unset"}, {"version": "v6.39.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7ec6fc2a7f52d2ea4c6476fc4ab818da46c6efe8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7ec6fc2a7f52d2ea4c6476fc4ab818da46c6efe8", "type": "zip", "shasum": "", "reference": "7ec6fc2a7f52d2ea4c6476fc4ab818da46c6efe8"}, "time": "2023-09-25T13:36:31+00:00"}, {"version": "v6.38.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "090b3c33f801bf5caab65b4b8f4d0f15dfa9a4a7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/090b3c33f801bf5caab65b4b8f4d0f15dfa9a4a7", "type": "zip", "shasum": "", "reference": "090b3c33f801bf5caab65b4b8f4d0f15dfa9a4a7"}, "time": "2023-09-21T02:33:36+00:00", "require-dev": {"laravel/framework": "^8.83.27", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "phpstan/phpstan": "^1.10.7", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}}, {"version": "v6.38.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b34807ff8f1023480ea845e8bd75486995a39d82"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b34807ff8f1023480ea845e8bd75486995a39d82", "type": "zip", "shasum": "", "reference": "b34807ff8f1023480ea845e8bd75486995a39d82"}, "time": "2023-09-09T02:18:39+00:00"}, {"version": "v6.38.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8117d59e0c425437a60c6777d55cac8fcd4ca12e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8117d59e0c425437a60c6777d55cac8fcd4ca12e", "type": "zip", "shasum": "", "reference": "8117d59e0c425437a60c6777d55cac8fcd4ca12e"}, "time": "2023-08-29T05:13:49+00:00"}, {"version": "v6.37.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7705fd05376c7790192184ef76d4ed972cb9ca2e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7705fd05376c7790192184ef76d4ed972cb9ca2e", "type": "zip", "shasum": "", "reference": "7705fd05376c7790192184ef76d4ed972cb9ca2e"}, "time": "2023-08-22T11:21:25+00:00"}, {"version": "v6.37.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "be9c6ac2a440a091fc3ad635e440dfaccf215c6a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/be9c6ac2a440a091fc3ad635e440dfaccf215c6a", "type": "zip", "shasum": "", "reference": "be9c6ac2a440a091fc3ad635e440dfaccf215c6a"}, "time": "2023-08-22T08:35:19+00:00"}, {"version": "v6.36.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3299a20af39a15471dd49d9f2fe1d2cc7b791d6f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3299a20af39a15471dd49d9f2fe1d2cc7b791d6f", "type": "zip", "shasum": "", "reference": "3299a20af39a15471dd49d9f2fe1d2cc7b791d6f"}, "time": "2023-08-19T03:29:07+00:00"}, {"version": "v6.35.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7b2100ba813887f701c63fde72222f9dcd37ef75"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7b2100ba813887f701c63fde72222f9dcd37ef75", "type": "zip", "shasum": "", "reference": "7b2100ba813887f701c63fde72222f9dcd37ef75"}, "time": "2023-08-17T09:02:16+00:00"}, {"version": "v6.35.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "057526d1cc3b6e52e623fe0ea51bb72b726433cd"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/057526d1cc3b6e52e623fe0ea51bb72b726433cd", "type": "zip", "shasum": "", "reference": "057526d1cc3b6e52e623fe0ea51bb72b726433cd"}, "time": "2023-08-15T08:34:14+00:00"}, {"version": "v6.34.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6fe132b706bf03e88f772da7501cde8a155eb860"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6fe132b706bf03e88f772da7501cde8a155eb860", "type": "zip", "shasum": "", "reference": "6fe132b706bf03e88f772da7501cde8a155eb860"}, "time": "2023-08-12T03:57:19+00:00"}, {"version": "v6.33.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3433f9e836324d5abf9436046b1a9d70f322337a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3433f9e836324d5abf9436046b1a9d70f322337a", "type": "zip", "shasum": "", "reference": "3433f9e836324d5abf9436046b1a9d70f322337a"}, "time": "2023-08-11T08:15:26+00:00"}, {"version": "v6.33.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "77af2590713dcf3da21fe1521a2256634192932c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/77af2590713dcf3da21fe1521a2256634192932c", "type": "zip", "shasum": "", "reference": "77af2590713dcf3da21fe1521a2256634192932c"}, "time": "2023-08-10T01:33:30+00:00"}, {"version": "v6.33.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d586eccf08090cbc95d23bfcc893f076083f44a5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d586eccf08090cbc95d23bfcc893f076083f44a5", "type": "zip", "shasum": "", "reference": "d586eccf08090cbc95d23bfcc893f076083f44a5"}, "time": "2023-08-09T02:39:00+00:00"}, {"version": "v6.33.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6717cbfeaf61a13d8a12ae253c69300cbb95832f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6717cbfeaf61a13d8a12ae253c69300cbb95832f", "type": "zip", "shasum": "", "reference": "6717cbfeaf61a13d8a12ae253c69300cbb95832f"}, "time": "2023-08-08T07:48:29+00:00"}, {"version": "v6.32.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "12baf011c23e64bfe2a6c89f2eb07602e387f259"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/12baf011c23e64bfe2a6c89f2eb07602e387f259", "type": "zip", "shasum": "", "reference": "12baf011c23e64bfe2a6c89f2eb07602e387f259"}, "time": "2023-06-13T04:58:55+00:00", "require-dev": {"laravel/framework": "^8.83.27", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}}, {"version": "v6.31.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a630f963d45a4aeb6a8771093fa029bf2a8a4f15"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a630f963d45a4aeb6a8771093fa029bf2a8a4f15", "type": "zip", "shasum": "", "reference": "a630f963d45a4aeb6a8771093fa029bf2a8a4f15"}, "time": "2023-04-11T07:42:07+00:00", "require-dev": {"laravel/framework": "^8.83.27", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}}, {"version": "v6.31.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8c6dfa9522d68788398d547fcb4af0cb29adb1f2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8c6dfa9522d68788398d547fcb4af0cb29adb1f2", "type": "zip", "shasum": "", "reference": "8c6dfa9522d68788398d547fcb4af0cb29adb1f2"}, "time": "2023-04-02T16:02:01+00:00"}, {"version": "v6.31.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d190e88af47058b594d120f8cf2891067a26c662"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d190e88af47058b594d120f8cf2891067a26c662", "type": "zip", "shasum": "", "reference": "d190e88af47058b594d120f8cf2891067a26c662"}, "time": "2023-04-01T12:18:41+00:00"}, {"keywords": ["testing", "BDD", "TDD", "laravel", "orchestra-platform", "orchestral"], "version": "v6.30.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d6cd256e6afe12a31c7669e5cfe8a689320127e7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d6cd256e6afe12a31c7669e5cfe8a689320127e7", "type": "zip", "shasum": "", "reference": "d6cd256e6afe12a31c7669e5cfe8a689320127e7"}, "time": "2023-03-27T14:14:40+00:00"}, {"version": "v6.29.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "29a7586915885f89b8d2203efe20f76afe9cf956"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/29a7586915885f89b8d2203efe20f76afe9cf956", "type": "zip", "shasum": "", "reference": "29a7586915885f89b8d2203efe20f76afe9cf956"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://liberapay.com/crynobone", "type": "liberapay"}], "time": "2022-10-11T12:12:52+00:00", "require-dev": {"laravel/framework": "^8.75", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.5.21 || ^9.5.10", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}, "suggest": {"laravel/framework": "Required for testing (^8.75).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0)."}}, {"version": "v6.29.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8eeace7d979a7905e6fab77a30a3b05da99459c4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8eeace7d979a7905e6fab77a30a3b05da99459c4", "type": "zip", "shasum": "", "reference": "8eeace7d979a7905e6fab77a30a3b05da99459c4"}, "time": "2022-08-24T00:15:20+00:00"}, {"version": "v6.28.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e66074e825e21b40b3433703dc3f76f2bfebebe0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e66074e825e21b40b3433703dc3f76f2bfebebe0", "type": "zip", "shasum": "", "reference": "e66074e825e21b40b3433703dc3f76f2bfebebe0"}, "time": "2022-02-08T12:50:35+00:00", "require-dev": {"laravel/framework": "^8.75", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.5.21 || ^9.5.10 || ^10.0", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}}, {"version": "v6.28.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "33bc180671f065a52d6d67417b9d38ca7879cc9a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/33bc180671f065a52d6d67417b9d38ca7879cc9a", "type": "zip", "shasum": "", "reference": "33bc180671f065a52d6d67417b9d38ca7879cc9a"}, "time": "2022-01-13T05:00:07+00:00"}, {"version": "v6.27.4", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a7244cb4ad3fa452cd0b0371c52c94c9f24f4541"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a7244cb4ad3fa452cd0b0371c52c94c9f24f4541", "type": "zip", "shasum": "", "reference": "a7244cb4ad3fa452cd0b0371c52c94c9f24f4541"}, "time": "2021-12-23T00:07:34+00:00"}, {"version": "v6.27.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "86867f7853e49a8f516951aa4e303230ebce40ac"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/86867f7853e49a8f516951aa4e303230ebce40ac", "type": "zip", "shasum": "", "reference": "86867f7853e49a8f516951aa4e303230ebce40ac"}, "time": "2021-12-03T22:38:19+00:00", "require-dev": {"laravel/framework": "^8.71", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.5.21 || ^9.5.10 || ^10.0", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}, "suggest": {"laravel/framework": "Required for testing (^8.71).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0)."}}, {"version": "v6.27.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8b6c99281463a037f19c06a910d4f02980103116"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8b6c99281463a037f19c06a910d4f02980103116", "type": "zip", "shasum": "", "reference": "8b6c99281463a037f19c06a910d4f02980103116"}, "time": "2021-12-02T05:39:52+00:00"}, {"version": "v6.27.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d0e98c101bc57e8bb0121603bb84aff7c800a44e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d0e98c101bc57e8bb0121603bb84aff7c800a44e", "type": "zip", "shasum": "", "reference": "d0e98c101bc57e8bb0121603bb84aff7c800a44e"}, "time": "2021-11-17T02:37:48+00:00", "require-dev": {"laravel/framework": "^8.67", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.5.21 || ^9.5.10 || ^10.0", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}, "suggest": {"laravel/framework": "Required for testing (^8.67).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0)."}}, {"version": "v6.27.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5e2c6211090d49e24c292edd2eb4138b7409160e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5e2c6211090d49e24c292edd2eb4138b7409160e", "type": "zip", "shasum": "", "reference": "5e2c6211090d49e24c292edd2eb4138b7409160e"}, "time": "2021-11-09T22:53:16+00:00"}, {"version": "v6.26.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d4df6eda45104043da7a99e0a272b1507c6d4fe0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d4df6eda45104043da7a99e0a272b1507c6d4fe0", "type": "zip", "shasum": "", "reference": "d4df6eda45104043da7a99e0a272b1507c6d4fe0"}, "time": "2021-10-21T06:20:26+00:00", "require-dev": {"laravel/framework": "^8.65", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.4", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.5.21 || ^9.5.10 || ^10.0", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}, "suggest": {"laravel/framework": "Required for testing (^8.65).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.4).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.5.21|^9.5.10|^10.0)."}}, {"version": "v6.25.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "03d8cce6e500a9c64da4cbaa776236c91b2572a1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/03d8cce6e500a9c64da4cbaa776236c91b2572a1", "type": "zip", "shasum": "", "reference": "03d8cce6e500a9c64da4cbaa776236c91b2572a1"}, "time": "2021-09-18T01:47:58+00:00", "require-dev": {"laravel/framework": "^8.54", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.4 || ^9.3.3 || ^10.0", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}, "suggest": {"laravel/framework": "Required for testing (^8.54).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4|^9.3.3)."}}, {"version": "v6.25.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "99fb87c96cc7876176217470f81b447e4deb41e8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/99fb87c96cc7876176217470f81b447e4deb41e8", "type": "zip", "shasum": "", "reference": "99fb87c96cc7876176217470f81b447e4deb41e8"}, "time": "2021-09-10T17:36:06+00:00"}, {"version": "v6.25.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a375a082e371b3f8a4a4682505bbd846bf1e72e5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a375a082e371b3f8a4a4682505bbd846bf1e72e5", "type": "zip", "shasum": "", "reference": "a375a082e371b3f8a4a4682505bbd846bf1e72e5"}, "time": "2021-09-08T07:52:43+00:00"}, {"version": "v6.24.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0e77a2e62cf9be1e541cb44f65d3ac4774015c87"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0e77a2e62cf9be1e541cb44f65d3ac4774015c87", "type": "zip", "shasum": "", "reference": "0e77a2e62cf9be1e541cb44f65d3ac4774015c87"}, "time": "2021-08-25T05:01:24+00:00", "autoload": {"psr-4": {"Orchestra\\Testbench\\": "src/"}}}, {"version": "v6.24.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fdd33d553480ff2192755806fca641bcd79ab04b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fdd33d553480ff2192755806fca641bcd79ab04b", "type": "zip", "shasum": "", "reference": "fdd33d553480ff2192755806fca641bcd79ab04b"}, "time": "2021-08-12T00:45:07+00:00"}, {"version": "v6.23.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "239bc0d99ae44f76c024e74ced0fc5aabfa00be3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/239bc0d99ae44f76c024e74ced0fc5aabfa00be3", "type": "zip", "shasum": "", "reference": "239bc0d99ae44f76c024e74ced0fc5aabfa00be3"}, "time": "2021-07-14T02:34:48+00:00", "require-dev": {"laravel/framework": "^8.26", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.4 || ^9.3.3 || ^10.0", "spatie/laravel-ray": "^1.7.1", "symfony/process": "^5.0"}, "suggest": {"laravel/framework": "Required for testing (^8.26).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4|^9.3.3)."}}, {"version": "v6.23.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6d88802eb6010ab845ec8b3a07895148262297f3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6d88802eb6010ab845ec8b3a07895148262297f3", "type": "zip", "shasum": "", "reference": "6d88802eb6010ab845ec8b3a07895148262297f3"}, "time": "2021-06-15T22:53:33+00:00"}, {"version": "v6.22.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "43a44c90e2fcd4e204057d59294ea5c241d994f7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/43a44c90e2fcd4e204057d59294ea5c241d994f7", "type": "zip", "shasum": "", "reference": "43a44c90e2fcd4e204057d59294ea5c241d994f7"}, "time": "2021-05-25T03:25:38+00:00"}, {"version": "v6.21.3", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e20e4ed5586993940679119c4eeaed3b21037ac5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e20e4ed5586993940679119c4eeaed3b21037ac5", "type": "zip", "shasum": "", "reference": "e20e4ed5586993940679119c4eeaed3b21037ac5"}, "time": "2021-05-18T09:19:25+00:00"}, {"version": "v6.21.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6b536a0f18227a156762e074f12bb17547eaf4f4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6b536a0f18227a156762e074f12bb17547eaf4f4", "type": "zip", "shasum": "", "reference": "6b536a0f18227a156762e074f12bb17547eaf4f4"}, "time": "2021-05-11T20:58:20+00:00"}, {"version": "v6.21.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a4e0660f1cf85760853ae105f937632888b114c6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a4e0660f1cf85760853ae105f937632888b114c6", "type": "zip", "shasum": "", "reference": "a4e0660f1cf85760853ae105f937632888b114c6"}, "time": "2021-04-21T02:44:17+00:00"}, {"version": "v6.21.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8412a8557b822fabd657d6ab5b06ca20c65b1b3e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8412a8557b822fabd657d6ab5b06ca20c65b1b3e", "type": "zip", "shasum": "", "reference": "8412a8557b822fabd657d6ab5b06ca20c65b1b3e"}, "time": "2021-04-06T11:36:25+00:00"}, {"version": "v6.20.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5789b74816e3c3f3d4bd2c419fb08ee2dd66b177"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5789b74816e3c3f3d4bd2c419fb08ee2dd66b177", "type": "zip", "shasum": "", "reference": "5789b74816e3c3f3d4bd2c419fb08ee2dd66b177"}, "time": "2021-03-30T22:52:56+00:00", "require-dev": {"laravel/framework": "^8.26", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.4 || ^9.3.3 || ^10.0", "symfony/process": "^5.0"}}, {"version": "v6.19.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "482ec981f5c812c4ae6798d73cd04c666c7ba2c7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/482ec981f5c812c4ae6798d73cd04c666c7ba2c7", "type": "zip", "shasum": "", "reference": "482ec981f5c812c4ae6798d73cd04c666c7ba2c7"}, "time": "2021-03-23T22:20:57+00:00", "require-dev": {"laravel/framework": "^8.26", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.4 || ^9.3.3 || ^10.0"}}, {"version": "v6.19.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e97234a2cffe510c3d76a4f9f4a5e47b730e5c92"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e97234a2cffe510c3d76a4f9f4a5e47b730e5c92", "type": "zip", "shasum": "", "reference": "e97234a2cffe510c3d76a4f9f4a5e47b730e5c92"}, "time": "2021-03-21T00:28:32+00:00"}, {"version": "v6.18.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "47e425b91c06b53936b05b06a0bc503211643e8d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/47e425b91c06b53936b05b06a0bc503211643e8d", "type": "zip", "shasum": "", "reference": "47e425b91c06b53936b05b06a0bc503211643e8d"}, "time": "2021-03-16T05:44:15+00:00", "extra": {"laravel": {"providers": ["Orchestra\\Testbench\\Foundation\\TestbenchServiceProvider"]}, "branch-alias": {"dev-master": "6.0-dev"}}}, {"version": "v6.17.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a5dc831264d4192954ea5abb9f4323e286429b3d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a5dc831264d4192954ea5abb9f4323e286429b3d", "type": "zip", "shasum": "", "reference": "a5dc831264d4192954ea5abb9f4323e286429b3d"}, "time": "2021-03-09T22:59:25+00:00", "require-dev": {"laravel/framework": "^8.26", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.4 || ^9.3.3"}}, {"version": "v6.17.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a82d3dc04d07620bbddef1dae3cbc105f7ce0181"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a82d3dc04d07620bbddef1dae3cbc105f7ce0181", "type": "zip", "shasum": "", "reference": "a82d3dc04d07620bbddef1dae3cbc105f7ce0181"}, "time": "2021-03-08T02:29:54+00:00"}, {"version": "v6.16.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c0297f31b95ba6e0f0e96d3c67d882d30680c83d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c0297f31b95ba6e0f0e96d3c67d882d30680c83d", "type": "zip", "shasum": "", "reference": "c0297f31b95ba6e0f0e96d3c67d882d30680c83d"}, "time": "2021-02-21T04:20:45+00:00"}, {"version": "v6.15.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9bece6191cd6b48c3d6f3e42a687209102e57cb4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9bece6191cd6b48c3d6f3e42a687209102e57cb4", "type": "zip", "shasum": "", "reference": "9bece6191cd6b48c3d6f3e42a687209102e57cb4"}, "time": "2021-02-13T09:33:45+00:00", "require-dev": {"laravel/framework": "^8.25", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.1", "phpunit/phpunit": "^8.4 || ^9.3"}, "suggest": {"laravel/framework": "Required for testing (^8.25).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v6.15.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3126b3f6ff4338f588c02ea5bd30398444dcb817"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3126b3f6ff4338f588c02ea5bd30398444dcb817", "type": "zip", "shasum": "", "reference": "3126b3f6ff4338f588c02ea5bd30398444dcb817"}, "time": "2021-02-09T13:59:12+00:00"}, {"version": "v6.15.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ee4884fef5bda005dbf873cd7ee9bb8d8f81e1d5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ee4884fef5bda005dbf873cd7ee9bb8d8f81e1d5", "type": "zip", "shasum": "", "reference": "ee4884fef5bda005dbf873cd7ee9bb8d8f81e1d5"}, "time": "2021-02-09T12:23:56+00:00"}, {"version": "v6.14.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1c2eb8c21e0be762adcc4b8ca5d70b4b349c092c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1c2eb8c21e0be762adcc4b8ca5d70b4b349c092c", "type": "zip", "shasum": "", "reference": "1c2eb8c21e0be762adcc4b8ca5d70b4b349c092c"}, "time": "2021-02-07T12:27:34+00:00"}, {"version": "v6.13.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fa99d30b435caa0a6f8d0a92bb1ec6cafbf6409a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fa99d30b435caa0a6f8d0a92bb1ec6cafbf6409a", "type": "zip", "shasum": "", "reference": "fa99d30b435caa0a6f8d0a92bb1ec6cafbf6409a"}, "time": "2021-01-30T02:23:53+00:00"}, {"version": "v6.12.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cb70c41fde7f5f168036ff343ef368f8c653ace0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cb70c41fde7f5f168036ff343ef368f8c653ace0", "type": "zip", "shasum": "", "reference": "cb70c41fde7f5f168036ff343ef368f8c653ace0"}, "time": "2021-01-29T09:01:06+00:00"}, {"homepage": "http://orchestraplatform.com/docs/latest/components/testbench/", "version": "v6.11.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "185a16ffa1ce56001e7a5559e2c1855c17bf5c29"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/185a16ffa1ce56001e7a5559e2c1855c17bf5c29", "type": "zip", "shasum": "", "reference": "185a16ffa1ce56001e7a5559e2c1855c17bf5c29"}, "time": "2021-01-21T01:13:06+00:00", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "require": {"php": ">=7.2.5 || >=8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^5.0", "vlucas/phpdotenv": "^5.1"}, "require-dev": {"laravel/framework": "^8.22.1", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.4.2", "orchestra/canvas": "^6.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^8.22.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.4.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v6.11.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c24ff775c73442e3e132fbcd4bde9116f964a1e3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c24ff775c73442e3e132fbcd4bde9116f964a1e3", "type": "zip", "shasum": "", "reference": "c24ff775c73442e3e132fbcd4bde9116f964a1e3"}, "time": "2021-01-17T23:13:04+00:00"}, {"version": "v6.11.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f130272ccdb956c369a06a02bbb15f485c5fc38c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f130272ccdb956c369a06a02bbb15f485c5fc38c", "type": "zip", "shasum": "", "reference": "f130272ccdb956c369a06a02bbb15f485c5fc38c"}, "time": "2021-01-17T13:14:05+00:00"}, {"version": "v6.10.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d7ef85377d87da59651a0f8f06444687df42224c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d7ef85377d87da59651a0f8f06444687df42224c", "type": "zip", "shasum": "", "reference": "d7ef85377d87da59651a0f8f06444687df42224c"}, "time": "2021-01-17T08:53:58+00:00", "require": {"php": ">=7.2.5 || >=8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0", "vlucas/phpdotenv": "^5.1"}}, {"version": "v6.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "389f145f7afbebc77765f3ef5d5af748fef0eebf"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/389f145f7afbebc77765f3ef5d5af748fef0eebf", "type": "zip", "shasum": "", "reference": "389f145f7afbebc77765f3ef5d5af748fef0eebf"}, "time": "2020-12-30T12:42:02+00:00", "require-dev": {"laravel/framework": "^8.0", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.3.2", "orchestra/canvas": "^6.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^8.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v6.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "65d1fc936c5fc166082a1797351847ef6dadbd29"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/65d1fc936c5fc166082a1797351847ef6dadbd29", "type": "zip", "shasum": "", "reference": "65d1fc936c5fc166082a1797351847ef6dadbd29"}, "time": "2020-12-15T03:18:42+00:00"}, {"version": "v6.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c87a00c2f6deae462b79be102937e8e3dfd52c2f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c87a00c2f6deae462b79be102937e8e3dfd52c2f", "type": "zip", "shasum": "", "reference": "c87a00c2f6deae462b79be102937e8e3dfd52c2f"}, "time": "2020-12-15T02:49:56+00:00", "require": {"php": ">=7.2.5 || >=8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0"}}, {"version": "v6.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7d038b444813cda4ffd637a2008aa487edb5afed"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7d038b444813cda4ffd637a2008aa487edb5afed", "type": "zip", "shasum": "", "reference": "7d038b444813cda4ffd637a2008aa487edb5afed"}, "time": "2020-12-09T14:20:37+00:00", "require-dev": {"laravel/framework": "^8.0", "laravel/laravel": "8.x-dev", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^6.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^8.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.1).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^6.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^6.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v6.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4f9fe7b3f901984c4e012fc29354ccc9eaf9e66f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4f9fe7b3f901984c4e012fc29354ccc9eaf9e66f", "type": "zip", "shasum": "", "reference": "4f9fe7b3f901984c4e012fc29354ccc9eaf9e66f"}, "time": "2020-12-01T11:00:52+00:00"}, {"version": "v6.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "aa570004ea1a16eb03138efe203dea25c16c22bd"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/aa570004ea1a16eb03138efe203dea25c16c22bd", "type": "zip", "shasum": "", "reference": "aa570004ea1a16eb03138efe203dea25c16c22bd"}, "time": "2020-11-25T02:41:48+00:00"}, {"version": "v6.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9f6aa372326f59eee780abfdc9591cd558f010cc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9f6aa372326f59eee780abfdc9591cd558f010cc", "type": "zip", "shasum": "", "reference": "9f6aa372326f59eee780abfdc9591cd558f010cc"}, "time": "2020-11-17T02:48:48+00:00"}, {"version": "v6.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "684ec700cb1fd797be7bedc3b1dc5793c2969ba6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/684ec700cb1fd797be7bedc3b1dc5793c2969ba6", "type": "zip", "shasum": "", "reference": "684ec700cb1fd797be7bedc3b1dc5793c2969ba6"}, "time": "2020-11-07T04:36:27+00:00"}, {"version": "v6.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d65e8aa34a4fb44dd98e7b7ec18dcc8201b5386a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d65e8aa34a4fb44dd98e7b7ec18dcc8201b5386a", "type": "zip", "shasum": "", "reference": "d65e8aa34a4fb44dd98e7b7ec18dcc8201b5386a"}, "time": "2020-10-28T05:55:05+00:00", "require-dev": {"laravel/framework": "^8.0", "laravel/laravel": "dev-master", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^6.0", "phpunit/phpunit": "^8.4 || ^9.0"}}, {"version": "v6.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "837ff047bb023683877b34ae3714081d41eebd10"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/837ff047bb023683877b34ae3714081d41eebd10", "type": "zip", "shasum": "", "reference": "837ff047bb023683877b34ae3714081d41eebd10"}, "time": "2020-10-27T12:55:06+00:00", "require": {"php": ">=7.2.5 || >=8.0", "fzaninotto/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0"}}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a894fa6a904f7920e629d5ede44a003e606819d2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a894fa6a904f7920e629d5ede44a003e606819d2", "type": "zip", "shasum": "", "reference": "a894fa6a904f7920e629d5ede44a003e606819d2"}, "time": "2020-10-19T23:03:23+00:00", "require": {"php": ">=7.3", "fzaninotto/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0"}}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fd79b0d6951d73e400ee9312251d6c327a8f969a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fd79b0d6951d73e400ee9312251d6c327a8f969a", "type": "zip", "shasum": "", "reference": "fd79b0d6951d73e400ee9312251d6c327a8f969a"}, "time": "2020-10-11T07:07:16+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2f62de09d1454420b59d5df5fc9b56efed22b2cc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2f62de09d1454420b59d5df5fc9b56efed22b2cc", "type": "zip", "shasum": "", "reference": "2f62de09d1454420b59d5df5fc9b56efed22b2cc"}, "time": "2020-09-28T00:30:48+00:00"}, {"version": "v6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6338618bb932001cc3857c5b3342956e2ff3dde0"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6338618bb932001cc3857c5b3342956e2ff3dde0", "type": "zip", "shasum": "", "reference": "6338618bb932001cc3857c5b3342956e2ff3dde0"}, "time": "2020-09-26T02:43:25+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f39c4332e9481a05b06868041ab2e82469e857a2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f39c4332e9481a05b06868041ab2e82469e857a2", "type": "zip", "shasum": "", "reference": "f39c4332e9481a05b06868041ab2e82469e857a2"}, "time": "2020-09-24T11:48:27+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f303688555b2d8718be69c82f256ed910d04ab00"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f303688555b2d8718be69c82f256ed910d04ab00", "type": "zip", "shasum": "", "reference": "f303688555b2d8718be69c82f256ed910d04ab00"}, "time": "2020-09-08T23:51:41+00:00", "require": {"php": ">=7.3", "fzaninotto/faker": "^1.9.1"}, "bin": "__unset"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "27d990e6d6790d265db6097036dcce5fe95d1358"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/27d990e6d6790d265db6097036dcce5fe95d1358", "type": "zip", "shasum": "", "reference": "27d990e6d6790d265db6097036dcce5fe95d1358"}, "time": "2020-09-08T14:01:30+00:00", "require-dev": {"laravel/framework": "^8.0", "laravel/laravel": "dev-develop", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^6.0", "phpunit/phpunit": "^8.4 || ^9.0"}}, {"homepage": "https://packages.tools/testbench", "version": "v5.22.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cfcb2be49423945adfc11e5d2ae4ad136388ec07"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cfcb2be49423945adfc11e5d2ae4ad136388ec07", "type": "zip", "shasum": "", "reference": "cfcb2be49423945adfc11e5d2ae4ad136388ec07"}, "time": "2021-09-02T05:59:37+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "bin": ["testbench"], "require": {"php": ">=7.2.5 || >=8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^5.0", "vlucas/phpdotenv": "^4.1"}, "require-dev": {"laravel/framework": "^7.30.3", "laravel/laravel": "7.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.3.3"}, "suggest": {"laravel/framework": "Required for testing (^7.30.3).", "mockery/mockery": "Allow using Mo<PERSON>y for testing (^1.3.3|^1.4.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^5.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^5.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4|^9.3.3)."}}, {"version": "v5.21.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b0b465f06cb7907c7d92fbeb7204443759bfdfaf"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b0b465f06cb7907c7d92fbeb7204443759bfdfaf", "type": "zip", "shasum": "", "reference": "b0b465f06cb7907c7d92fbeb7204443759bfdfaf"}, "time": "2021-05-25T02:59:00+00:00"}, {"version": "v5.20.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "19491e6c523ac6151acfb6e5255645328d107c97"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/19491e6c523ac6151acfb6e5255645328d107c97", "type": "zip", "shasum": "", "reference": "19491e6c523ac6151acfb6e5255645328d107c97"}, "time": "2021-03-07T22:33:07+00:00"}, {"version": "v5.19.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "484d1ec4936f017b06f2e857b8811227e8d0cee8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/484d1ec4936f017b06f2e857b8811227e8d0cee8", "type": "zip", "shasum": "", "reference": "484d1ec4936f017b06f2e857b8811227e8d0cee8"}, "time": "2021-02-21T04:09:07+00:00"}, {"version": "v5.18.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "18e539c6e8fad26af23fbfaf49d329bae806c80e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/18e539c6e8fad26af23fbfaf49d329bae806c80e", "type": "zip", "shasum": "", "reference": "18e539c6e8fad26af23fbfaf49d329bae806c80e"}, "time": "2021-02-13T09:28:18+00:00", "require-dev": {"laravel/framework": "^7.30.3", "laravel/laravel": "7.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.3"}, "suggest": {"laravel/framework": "Required for testing (^7.30.3).", "mockery/mockery": "Allow using Mockery for testing (^1.3.3 || ^1.4.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^5.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^5.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v5.18.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "1671d17bff3151e06079014f002232843c224009"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/1671d17bff3151e06079014f002232843c224009", "type": "zip", "shasum": "", "reference": "1671d17bff3151e06079014f002232843c224009"}, "time": "2021-02-09T11:49:50+00:00"}, {"version": "v5.17.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9f290be7ec6e1187e983b4bcdf1dc5b2dc8503b9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9f290be7ec6e1187e983b4bcdf1dc5b2dc8503b9", "type": "zip", "shasum": "", "reference": "9f290be7ec6e1187e983b4bcdf1dc5b2dc8503b9"}, "time": "2021-02-07T12:20:43+00:00"}, {"version": "v5.16.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d1f208a6a5b5c12969c83efa081995a5847f7f7e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d1f208a6a5b5c12969c83efa081995a5847f7f7e", "type": "zip", "shasum": "", "reference": "d1f208a6a5b5c12969c83efa081995a5847f7f7e"}, "time": "2021-01-30T01:48:27+00:00"}, {"version": "v5.15.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c864f2d235dfa7b7bf61d1730f03ce7998c513ae"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c864f2d235dfa7b7bf61d1730f03ce7998c513ae", "type": "zip", "shasum": "", "reference": "c864f2d235dfa7b7bf61d1730f03ce7998c513ae"}, "time": "2021-01-29T03:10:57+00:00"}, {"homepage": "http://orchestraplatform.com/docs/latest/components/testbench/", "version": "v5.14.2", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "74b593d1a63e03c23844298673dadbba461b6e31"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/74b593d1a63e03c23844298673dadbba461b6e31", "type": "zip", "shasum": "", "reference": "74b593d1a63e03c23844298673dadbba461b6e31"}, "time": "2021-01-21T01:06:16+00:00", "require-dev": {"laravel/framework": "^7.30.3", "laravel/laravel": "7.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}}, {"version": "v5.14.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a26a080eb5a334c68804de292c32473127d2e6b8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a26a080eb5a334c68804de292c32473127d2e6b8", "type": "zip", "shasum": "", "reference": "a26a080eb5a334c68804de292c32473127d2e6b8"}, "time": "2021-01-17T23:03:34+00:00"}, {"version": "v5.14.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f0bffd534f3c870c0b45db82a962850d228626ee"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f0bffd534f3c870c0b45db82a962850d228626ee", "type": "zip", "shasum": "", "reference": "f0bffd534f3c870c0b45db82a962850d228626ee"}, "time": "2021-01-17T13:08:24+00:00"}, {"version": "v5.13.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3c3cc15d5c15e9882d8d601d4da32ba5a9221ffa"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3c3cc15d5c15e9882d8d601d4da32ba5a9221ffa", "type": "zip", "shasum": "", "reference": "3c3cc15d5c15e9882d8d601d4da32ba5a9221ffa"}, "time": "2021-01-17T08:29:15+00:00", "require": {"php": ">=7.2.5 || >=8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0", "vlucas/phpdotenv": "^4.1"}}, {"version": "v5.12.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "445a0b20b9c6da9b6cb9a99bc5948bb364ff326f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/445a0b20b9c6da9b6cb9a99bc5948bb364ff326f", "type": "zip", "shasum": "", "reference": "445a0b20b9c6da9b6cb9a99bc5948bb364ff326f"}, "time": "2020-12-15T01:39:18+00:00", "require-dev": {"laravel/framework": "^7.24", "laravel/laravel": "7.x-dev", "mockery/mockery": "^1.3.2", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^7.24).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.1).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^5.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^5.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v5.12.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5017bc4104903cbbc0c0d3223219873ff525d6a2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5017bc4104903cbbc0c0d3223219873ff525d6a2", "type": "zip", "shasum": "", "reference": "5017bc4104903cbbc0c0d3223219873ff525d6a2"}, "time": "2020-12-15T00:51:35+00:00", "require": {"php": ">=7.2.5 || >=8.0", "fakerphp/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0"}}, {"version": "v5.11.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "074d1f7481fb283959ef2c680a296f1b5d898fc3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/074d1f7481fb283959ef2c680a296f1b5d898fc3", "type": "zip", "shasum": "", "reference": "074d1f7481fb283959ef2c680a296f1b5d898fc3"}, "time": "2020-12-09T14:15:54+00:00", "require-dev": {"laravel/framework": "^7.24", "laravel/laravel": "7.x-dev", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}}, {"version": "v5.10.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ca5866f2dd368d5e6a72b0211d97c237091fa729"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ca5866f2dd368d5e6a72b0211d97c237091fa729", "type": "zip", "shasum": "", "reference": "ca5866f2dd368d5e6a72b0211d97c237091fa729"}, "time": "2020-12-01T10:38:21+00:00"}, {"version": "v5.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "28cce19a0228e50b9fd7309e080d4e698641479f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/28cce19a0228e50b9fd7309e080d4e698641479f", "type": "zip", "shasum": "", "reference": "28cce19a0228e50b9fd7309e080d4e698641479f"}, "time": "2020-11-17T02:33:49+00:00"}, {"version": "v5.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7c324b17d6a6e1d8e1a83f8fbafa55a1004b3d74"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7c324b17d6a6e1d8e1a83f8fbafa55a1004b3d74", "type": "zip", "shasum": "", "reference": "7c324b17d6a6e1d8e1a83f8fbafa55a1004b3d74"}, "time": "2020-11-07T04:27:40+00:00"}, {"version": "v5.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "984e89b5ddfee45c375598d06b77ec733e7109d6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/984e89b5ddfee45c375598d06b77ec733e7109d6", "type": "zip", "shasum": "", "reference": "984e89b5ddfee45c375598d06b77ec733e7109d6"}, "time": "2020-10-28T05:48:49+00:00"}, {"version": "v5.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dff4f03f5b97b03c8ad0ea79592047ca9aea9330"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dff4f03f5b97b03c8ad0ea79592047ca9aea9330", "type": "zip", "shasum": "", "reference": "dff4f03f5b97b03c8ad0ea79592047ca9aea9330"}, "time": "2020-10-27T12:22:50+00:00", "require": {"php": ">=7.2.5 || >=8.0", "fzaninotto/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0"}}, {"version": "v5.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8e866c7f296505a5ccd5a2e7b5555a116194c358"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8e866c7f296505a5ccd5a2e7b5555a116194c358", "type": "zip", "shasum": "", "reference": "8e866c7f296505a5ccd5a2e7b5555a116194c358"}, "time": "2020-10-19T22:44:31+00:00", "require": {"php": ">=7.2.5", "fzaninotto/faker": "^1.9.1", "symfony/yaml": "^4.3 || ^5.0"}}, {"version": "v5.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4ef04d577f9ccbf08b4150886f1bfe30d8c3c9ef"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4ef04d577f9ccbf08b4150886f1bfe30d8c3c9ef", "type": "zip", "shasum": "", "reference": "4ef04d577f9ccbf08b4150886f1bfe30d8c3c9ef"}, "time": "2020-10-11T06:28:32+00:00"}, {"version": "v5.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9f7177629c8be2b6b07b9bf0c030669a09fabf16"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9f7177629c8be2b6b07b9bf0c030669a09fabf16", "type": "zip", "shasum": "", "reference": "9f7177629c8be2b6b07b9bf0c030669a09fabf16"}, "time": "2020-09-27T23:46:39+00:00"}, {"version": "v5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3c0fb87d05c983d81890c79b9420be41b711b4e8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3c0fb87d05c983d81890c79b9420be41b711b4e8", "type": "zip", "shasum": "", "reference": "3c0fb87d05c983d81890c79b9420be41b711b4e8"}, "time": "2020-09-26T01:49:07+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d73bb79208c4aa0754c99108bd5f9f025966428b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d73bb79208c4aa0754c99108bd5f9f025966428b", "type": "zip", "shasum": "", "reference": "d73bb79208c4aa0754c99108bd5f9f025966428b"}, "time": "2020-09-25T01:34:48+00:00"}, {"version": "v5.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "061ec707dbf92271af503abb6d70ba12668e54d1"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/061ec707dbf92271af503abb6d70ba12668e54d1", "type": "zip", "shasum": "", "reference": "061ec707dbf92271af503abb6d70ba12668e54d1"}, "time": "2020-09-02T01:06:48+00:00", "require": {"php": ">=7.2.5", "fzaninotto/faker": "^1.9.1"}, "require-dev": {"laravel/framework": "^7.24", "laravel/laravel": "dev-master", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "bin": "__unset"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2a0d4686e5ad815f8fee5a36eeada0a3c4797e18"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2a0d4686e5ad815f8fee5a36eeada0a3c4797e18", "type": "zip", "shasum": "", "reference": "2a0d4686e5ad815f8fee5a36eeada0a3c4797e18"}, "time": "2020-08-31T05:19:35+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cc82d4034fbc1005851ca9a6c9ea2f316f25624a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cc82d4034fbc1005851ca9a6c9ea2f316f25624a", "type": "zip", "shasum": "", "reference": "cc82d4034fbc1005851ca9a6c9ea2f316f25624a"}, "time": "2020-08-17T22:51:07+00:00", "require-dev": {"laravel/framework": "^7.1", "laravel/laravel": "dev-master", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^7.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.1).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^5.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^5.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v5.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "41ebd765f5b3f1aba366cc6b2f5b3856a1715519"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/41ebd765f5b3f1aba366cc6b2f5b3856a1715519", "type": "zip", "shasum": "", "reference": "41ebd765f5b3f1aba366cc6b2f5b3856a1715519"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://www.patreon.com/crynobone", "type": "patreon"}], "time": "2020-05-02T13:35:10+00:00"}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "22526c9e2ef10551c8032ca27ef3243319d63dd7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/22526c9e2ef10551c8032ca27ef3243319d63dd7", "type": "zip", "shasum": "", "reference": "22526c9e2ef10551c8032ca27ef3243319d63dd7"}, "time": "2020-04-11T10:47:11+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ca909601ac743dbdf6807608bcb33e78a0c3f80a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ca909601ac743dbdf6807608bcb33e78a0c3f80a", "type": "zip", "shasum": "", "reference": "ca909601ac743dbdf6807608bcb33e78a0c3f80a"}, "time": "2020-03-31T01:45:21+00:00"}, {"version": "v5.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "69ddd365c01bb90b89edd9c52e44d0dc5bbc0854"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/69ddd365c01bb90b89edd9c52e44d0dc5bbc0854", "type": "zip", "shasum": "", "reference": "69ddd365c01bb90b89edd9c52e44d0dc5bbc0854"}, "time": "2020-03-16T04:35:18+00:00"}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "14961b8a10e513ebcab7f7548ffbeec663bac5c4"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/14961b8a10e513ebcab7f7548ffbeec663bac5c4", "type": "zip", "shasum": "", "reference": "14961b8a10e513ebcab7f7548ffbeec663bac5c4"}, "time": "2020-03-10T16:58:29+00:00"}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9fac37e442651664cab3025dc482a3933f494422"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9fac37e442651664cab3025dc482a3933f494422", "type": "zip", "shasum": "", "reference": "9fac37e442651664cab3025dc482a3933f494422"}, "time": "2020-03-06T23:32:49+00:00", "require-dev": {"laravel/framework": "^7.0.6", "laravel/laravel": "dev-master", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^7.0.6).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.1).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^5.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^5.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2bf166beb0a7407acd312ffb75bf4917ec9a41e5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2bf166beb0a7407acd312ffb75bf4917ec9a41e5", "type": "zip", "shasum": "", "reference": "2bf166beb0a7407acd312ffb75bf4917ec9a41e5"}, "time": "2020-03-03T14:02:06+00:00", "require-dev": {"laravel/framework": "^7.0", "laravel/laravel": "dev-master", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^7.0).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.1).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^5.0).", "orchestra/testbench-dusk": "Allow using Laravel <PERSON> for testing (^5.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.4 || ^9.0)."}}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dba2299ff449dce2681c21c05b284308bb68e3c2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dba2299ff449dce2681c21c05b284308bb68e3c2", "type": "zip", "shasum": "", "reference": "dba2299ff449dce2681c21c05b284308bb68e3c2"}, "time": "2020-03-02T11:19:35+00:00", "require-dev": {"laravel/framework": "^7.0", "laravel/laravel": "dev-develop", "mockery/mockery": "^1.3.1", "orchestra/canvas": "^5.0", "phpunit/phpunit": "^8.4 || ^9.0"}}, {"version": "v4.18.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6d6ce5e62a25bc796625ed56d4353e8071bf8693"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6d6ce5e62a25bc796625ed56d4353e8071bf8693", "type": "zip", "shasum": "", "reference": "6d6ce5e62a25bc796625ed56d4353e8071bf8693"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://liberapay.com/crynobone", "type": "liberapay"}], "time": "2021-09-02T05:33:20+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=7.2 || >=8.0", "fakerphp/faker": "^1.9.1"}, "require-dev": {"laravel/framework": "^6.20.14", "laravel/laravel": "6.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "phpunit/phpunit": "^7.5.15 || ^8.4 || ^9.3.3"}, "suggest": {"laravel/framework": "Required for testing (^6.20.12).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^7.5.15|^8.4|^9.3.3)."}}, {"version": "v4.17.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "8146ed4368a6728edaa729610c4801a67defb31e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/8146ed4368a6728edaa729610c4801a67defb31e", "type": "zip", "shasum": "", "reference": "8146ed4368a6728edaa729610c4801a67defb31e"}, "time": "2021-05-25T02:10:35+00:00"}, {"version": "v4.16.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ea1ca24779b1a0126feb2d3aa5f7d63c8d54206f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ea1ca24779b1a0126feb2d3aa5f7d63c8d54206f", "type": "zip", "shasum": "", "reference": "ea1ca24779b1a0126feb2d3aa5f7d63c8d54206f"}, "time": "2021-03-06T10:08:28+00:00"}, {"version": "v4.15.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f516bb6eeaacd62518a0ad401a3f644c676c533e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f516bb6eeaacd62518a0ad401a3f644c676c533e", "type": "zip", "shasum": "", "reference": "f516bb6eeaacd62518a0ad401a3f644c676c533e"}, "time": "2021-02-20T04:48:06+00:00"}, {"version": "v4.14.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0874afa95965af0941f43282af15a5756e6ec0ee"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0874afa95965af0941f43282af15a5756e6ec0ee", "type": "zip", "shasum": "", "reference": "0874afa95965af0941f43282af15a5756e6ec0ee"}, "time": "2021-02-09T11:42:02+00:00", "require-dev": {"laravel/framework": "^6.20.14", "laravel/laravel": "6.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "phpunit/phpunit": "^8.3 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^6.20.12).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}}, {"version": "v4.13.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "01e093a4295b5ae48933d60f781c6a2c160a071c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/01e093a4295b5ae48933d60f781c6a2c160a071c", "type": "zip", "shasum": "", "reference": "01e093a4295b5ae48933d60f781c6a2c160a071c"}, "time": "2021-01-17T07:53:53+00:00", "require-dev": {"laravel/framework": "^6.20.12", "laravel/laravel": "6.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "phpunit/phpunit": "^8.3 || ^9.0"}}, {"version": "v4.12.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f8fa8407b1f10884f974e3d334f66ccc9769dbc2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f8fa8407b1f10884f974e3d334f66ccc9769dbc2", "type": "zip", "shasum": "", "reference": "f8fa8407b1f10884f974e3d334f66ccc9769dbc2"}, "time": "2020-12-15T00:14:05+00:00", "require-dev": {"laravel/framework": "^6.18.0", "laravel/laravel": "6.x-dev", "mockery/mockery": "^1.3.2", "phpunit/phpunit": "^8.3 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^6.18).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}}, {"version": "v4.11.1", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a68f22b088684b639a7608b3ea149c6df23e547d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a68f22b088684b639a7608b3ea149c6df23e547d", "type": "zip", "shasum": "", "reference": "a68f22b088684b639a7608b3ea149c6df23e547d"}, "time": "2020-12-10T08:33:12+00:00", "require-dev": {"laravel/framework": "^6.18.0", "laravel/laravel": "6.x-dev", "mockery/mockery": "~1.2.3 || ^1.3.1", "phpunit/phpunit": "^8.3 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^6.18).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.2.3).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}}, {"version": "v4.11.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a0504ab537c0dbfbda2e9def53158094ee8fd2e9"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a0504ab537c0dbfbda2e9def53158094ee8fd2e9", "type": "zip", "shasum": "", "reference": "a0504ab537c0dbfbda2e9def53158094ee8fd2e9"}, "time": "2020-12-09T14:08:04+00:00"}, {"version": "v4.10.0", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "195e03432621f8d8c4f712318f6fa710a5851c6a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/195e03432621f8d8c4f712318f6fa710a5851c6a", "type": "zip", "shasum": "", "reference": "195e03432621f8d8c4f712318f6fa710a5851c6a"}, "time": "2020-12-01T10:30:11+00:00"}, {"version": "v4.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cf155d0e7dbd6bc2f780c11766d4612dd7241410"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cf155d0e7dbd6bc2f780c11766d4612dd7241410", "type": "zip", "shasum": "", "reference": "cf155d0e7dbd6bc2f780c11766d4612dd7241410"}, "time": "2020-11-17T01:31:29+00:00"}, {"version": "v4.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dacfbe6e86e0032935f91228f952e5e7bf5b3e2d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dacfbe6e86e0032935f91228f952e5e7bf5b3e2d", "type": "zip", "shasum": "", "reference": "dacfbe6e86e0032935f91228f952e5e7bf5b3e2d"}, "time": "2020-11-07T04:03:12+00:00"}, {"version": "v4.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "eaca5c42bbd6443d6fd2b89eca00f32315a93f3b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/eaca5c42bbd6443d6fd2b89eca00f32315a93f3b", "type": "zip", "shasum": "", "reference": "eaca5c42bbd6443d6fd2b89eca00f32315a93f3b"}, "time": "2020-10-27T15:24:33+00:00"}, {"version": "v4.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "603233f7d202a19f403db971e0a6fa591bad71c8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/603233f7d202a19f403db971e0a6fa591bad71c8", "type": "zip", "shasum": "", "reference": "603233f7d202a19f403db971e0a6fa591bad71c8"}, "time": "2020-10-25T13:19:00+00:00", "require": {"php": ">=7.2 || >=8.0", "fzaninotto/faker": "^1.9.1"}}, {"version": "v4.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c8e9ce9578fe13c075751d81f23dc2c3bb37a134"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c8e9ce9578fe13c075751d81f23dc2c3bb37a134", "type": "zip", "shasum": "", "reference": "c8e9ce9578fe13c075751d81f23dc2c3bb37a134"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://www.patreon.com/crynobone", "type": "patreon"}], "time": "2020-04-11T10:37:21+00:00", "require": {"php": ">=7.2", "fzaninotto/faker": "^1.9.1"}}, {"version": "v4.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fad3769a7903021f5784d5d76aa6eb3a3904b215"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fad3769a7903021f5784d5d76aa6eb3a3904b215", "type": "zip", "shasum": "", "reference": "fad3769a7903021f5784d5d76aa6eb3a3904b215"}, "time": "2020-03-06T23:07:37+00:00"}, {"version": "v4.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6e6f1c330d37563d2ecca125d6b672e752ab88b5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6e6f1c330d37563d2ecca125d6b672e752ab88b5", "type": "zip", "shasum": "", "reference": "6e6f1c330d37563d2ecca125d6b672e752ab88b5"}, "time": "2020-01-29T22:59:50+00:00", "require-dev": {"laravel/framework": "^6.9", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^6.9).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.2.3).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}, "funding": "__unset"}, {"version": "v4.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "24ae854ca7a9895162557e6d113e64d0cb0667a3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/24ae854ca7a9895162557e6d113e64d0cb0667a3", "type": "zip", "shasum": "", "reference": "24ae854ca7a9895162557e6d113e64d0cb0667a3"}, "time": "2020-01-21T03:11:28+00:00", "require": {"php": ">=7.2", "fzaninotto/faker": "^1.4"}}, {"version": "v4.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b92e31c5b101fc87bc5c3137610151dcae82f0e3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b92e31c5b101fc87bc5c3137610151dcae82f0e3", "type": "zip", "shasum": "", "reference": "b92e31c5b101fc87bc5c3137610151dcae82f0e3"}, "time": "2019-12-31T01:37:59+00:00"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "74e50f799757cfdef2398a9571aae889a5f4d60f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/74e50f799757cfdef2398a9571aae889a5f4d60f", "type": "zip", "shasum": "", "reference": "74e50f799757cfdef2398a9571aae889a5f4d60f"}, "time": "2019-12-10T03:55:50+00:00", "require-dev": {"laravel/framework": "^6.4", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.4).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.2.3).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a72fcc8b9b36cbc5a224f8b18bd0ad65fbe7093b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a72fcc8b9b36cbc5a224f8b18bd0ad65fbe7093b", "type": "zip", "shasum": "", "reference": "a72fcc8b9b36cbc5a224f8b18bd0ad65fbe7093b"}, "time": "2019-11-22T22:33:16+00:00"}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9a0d9a1818d0c65b0087807be5af107b495b2eb5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9a0d9a1818d0c65b0087807be5af107b495b2eb5", "type": "zip", "shasum": "", "reference": "9a0d9a1818d0c65b0087807be5af107b495b2eb5"}, "time": "2019-11-22T01:45:35+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dbc623c46cb839c64f5425daee795ac14214f9bc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dbc623c46cb839c64f5425daee795ac14214f9bc", "type": "zip", "shasum": "", "reference": "dbc623c46cb839c64f5425daee795ac14214f9bc"}, "time": "2019-10-23T22:30:08+00:00"}, {"version": "v4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4f098fe9bc64c23ea0b7c4ae12c0e26cb270e8e7"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4f098fe9bc64c23ea0b7c4ae12c0e26cb270e8e7", "type": "zip", "shasum": "", "reference": "4f098fe9bc64c23ea0b7c4ae12c0e26cb270e8e7"}, "time": "2019-10-11T01:50:36+00:00", "require-dev": {"laravel/framework": "^6.2", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.2).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.2.3).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ce1b8b329967c022a43a5efae4fdd9080eca3a6b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ce1b8b329967c022a43a5efae4fdd9080eca3a6b", "type": "zip", "shasum": "", "reference": "ce1b8b329967c022a43a5efae4fdd9080eca3a6b"}, "time": "2019-10-06T13:17:34+00:00", "require-dev": {"laravel/framework": "^6.1", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.1).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.2.3).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "0091a0f1b6493fa445379c58c4d0b757ea4ee2ee"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/0091a0f1b6493fa445379c58c4d0b757ea4ee2ee", "type": "zip", "shasum": "", "reference": "0091a0f1b6493fa445379c58c4d0b757ea4ee2ee"}, "time": "2019-09-15T08:17:06+00:00", "require-dev": {"laravel/framework": "^6.0", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.2.3).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^8.3)."}}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "39aa19cb23fde3a156217587802acf37b1726d16"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/39aa19cb23fde3a156217587802acf37b1726d16", "type": "zip", "shasum": "", "reference": "39aa19cb23fde3a156217587802acf37b1726d16"}, "time": "2019-09-11T07:31:59+00:00"}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "de4b741bdee89cc8aba5c03fa8dd0e485001a0d5"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/de4b741bdee89cc8aba5c03fa8dd0e485001a0d5", "type": "zip", "shasum": "", "reference": "de4b741bdee89cc8aba5c03fa8dd0e485001a0d5"}, "time": "2019-09-03T20:00:44+00:00", "require-dev": {"laravel/framework": "^6.0", "laravel/laravel": "dev-master", "mockery/mockery": "^1.0", "phpunit/phpunit": "^8.0"}, "suggest": {"laravel/framework": "Required for testing (^6.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^8.0)."}}, {"version": "v3.9.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "13c9de0b7744207146f812b2a0d32f0f063ef1f8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/13c9de0b7744207146f812b2a0d32f0f063ef1f8", "type": "zip", "shasum": "", "reference": "13c9de0b7744207146f812b2a0d32f0f063ef1f8"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://www.patreon.com/crynobone", "type": "patreon"}], "time": "2020-04-11T10:20:23+00:00", "extra": {"branch-alias": {"dev-master": "3.9-dev"}}, "require-dev": {"laravel/framework": "^6.18.0", "laravel/laravel": "6.x-dev", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.18).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.2).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.9).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.9).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^8.0)."}}, {"version": "v3.9.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "43fc24556231898944e906ca7c63fe2fedf63661"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/43fc24556231898944e906ca7c63fe2fedf63661", "type": "zip", "shasum": "", "reference": "43fc24556231898944e906ca7c63fe2fedf63661"}, "time": "2020-03-06T22:57:11+00:00"}, {"version": "v3.9.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "ddf0cb1ff55d02b77fdb38b4b618eb86189527d3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/ddf0cb1ff55d02b77fdb38b4b618eb86189527d3", "type": "zip", "shasum": "", "reference": "ddf0cb1ff55d02b77fdb38b4b618eb86189527d3"}, "time": "2020-01-21T01:14:20+00:00", "require-dev": {"laravel/framework": "^6.2", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.2).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.2).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.9).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.9).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^8.0)."}, "funding": "__unset"}, {"version": "v3.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "41a092c8c6cc4a26f556e8fc4969294c06f50ffc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/41a092c8c6cc4a26f556e8fc4969294c06f50ffc", "type": "zip", "shasum": "", "reference": "41a092c8c6cc4a26f556e8fc4969294c06f50ffc"}, "time": "2019-12-10T03:15:08+00:00"}, {"version": "v3.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5519d4962b992da6f4ea260cb4798008d538953a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5519d4962b992da6f4ea260cb4798008d538953a", "type": "zip", "shasum": "", "reference": "5519d4962b992da6f4ea260cb4798008d538953a"}, "time": "2019-10-23T22:21:22+00:00"}, {"version": "v3.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5629cd2fc10cf7262f43d77fe4fc9008ef73b625"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5629cd2fc10cf7262f43d77fe4fc9008ef73b625", "type": "zip", "shasum": "", "reference": "5629cd2fc10cf7262f43d77fe4fc9008ef73b625"}, "time": "2019-10-11T01:43:51+00:00"}, {"version": "v3.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "4b6b62c5fe8d891107eda65c5237f13932fedb1d"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/4b6b62c5fe8d891107eda65c5237f13932fedb1d", "type": "zip", "shasum": "", "reference": "4b6b62c5fe8d891107eda65c5237f13932fedb1d"}, "time": "2019-09-15T07:07:58+00:00", "require-dev": {"laravel/framework": "^6.0", "laravel/laravel": "dev-master", "mockery/mockery": "^1.2.3", "phpunit/phpunit": "^8.3"}, "suggest": {"laravel/framework": "Required for testing (^6.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.9).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.9).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^8.0)."}}, {"version": "v3.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c95774ba2f8f0bde82b1655b531a5098645f63bf"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c95774ba2f8f0bde82b1655b531a5098645f63bf", "type": "zip", "shasum": "", "reference": "c95774ba2f8f0bde82b1655b531a5098645f63bf"}, "time": "2019-09-11T07:23:27+00:00"}, {"version": "v3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3c34a651dbf7b37ff67d672a007f1852792d3db2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3c34a651dbf7b37ff67d672a007f1852792d3db2", "type": "zip", "shasum": "", "reference": "3c34a651dbf7b37ff67d672a007f1852792d3db2"}, "time": "2019-09-03T19:54:36+00:00", "require-dev": {"laravel/framework": "^6.0", "laravel/laravel": "dev-master", "mockery/mockery": "^1.0", "phpunit/phpunit": "^8.0"}}, {"version": "v3.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fd3436e12ad61547f83a77384679efeaae1aaede"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fd3436e12ad61547f83a77384679efeaae1aaede", "type": "zip", "shasum": "", "reference": "fd3436e12ad61547f83a77384679efeaae1aaede"}, "time": "2019-12-10T02:49:57+00:00", "extra": {"branch-alias": {"dev-master": "3.8-dev"}}, "require": {"php": ">=7.1", "fzaninotto/faker": "^1.4"}, "require-dev": {"laravel/framework": "~5.8.35", "laravel/laravel": "5.8.x-dev", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5 || ^8.0"}, "suggest": {"laravel/framework": "Required for testing (~5.8.35).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.8).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.8).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.5 || ^8.0)."}}, {"version": "v3.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2122fc0c3c4e592ab142786b27d5bd6c60ca7a3c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2122fc0c3c4e592ab142786b27d5bd6c60ca7a3c", "type": "zip", "shasum": "", "reference": "2122fc0c3c4e592ab142786b27d5bd6c60ca7a3c"}, "time": "2019-09-11T07:07:58+00:00"}, {"version": "v3.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "fbdca0c5106ebf6d060c7f1fbdbbeeb3f4a5a725"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/fbdca0c5106ebf6d060c7f1fbdbbeeb3f4a5a725", "type": "zip", "shasum": "", "reference": "fbdca0c5106ebf6d060c7f1fbdbbeeb3f4a5a725"}, "time": "2019-08-04T02:31:04+00:00", "require-dev": {"laravel/framework": "~5.8.3", "laravel/laravel": "dev-master", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5 || ^8.0"}, "suggest": {"laravel/framework": "Required for testing (~5.8.19).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.8).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.8).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.5 || ^8.0)."}}, {"version": "v3.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b81c24ef1036cb1f8ff0ca711535cea3f0281848"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b81c24ef1036cb1f8ff0ca711535cea3f0281848", "type": "zip", "shasum": "", "reference": "b81c24ef1036cb1f8ff0ca711535cea3f0281848"}, "time": "2019-06-10T08:29:56+00:00"}, {"version": "v3.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "27923e6e44d73474deac5d8d1be673524d60dceb"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/27923e6e44d73474deac5d8d1be673524d60dceb", "type": "zip", "shasum": "", "reference": "27923e6e44d73474deac5d8d1be673524d60dceb"}, "time": "2019-05-29T22:54:52+00:00"}, {"version": "v3.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b8f827ace31a15d5343301daf8eb6ce2db00726b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b8f827ace31a15d5343301daf8eb6ce2db00726b", "type": "zip", "shasum": "", "reference": "b8f827ace31a15d5343301daf8eb6ce2db00726b"}, "time": "2019-05-13T02:02:41+00:00", "suggest": {"laravel/framework": "Required for testing (~5.8.15).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.8).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.8).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.5 || ^8.0)."}}, {"version": "v3.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "598caa6f7813b8a29b2f1b69912b40659dcef79b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/598caa6f7813b8a29b2f1b69912b40659dcef79b", "type": "zip", "shasum": "", "reference": "598caa6f7813b8a29b2f1b69912b40659dcef79b"}, "time": "2019-04-10T12:50:29+00:00", "suggest": {"laravel/framework": "Required for testing (~5.8.3).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.8).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.8).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.5 || ^8.0)."}}, {"version": "v3.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "51192972746beb3766327bb84838998d3a59e99c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/51192972746beb3766327bb84838998d3a59e99c", "type": "zip", "shasum": "", "reference": "51192972746beb3766327bb84838998d3a59e99c"}, "time": "2019-02-28T00:40:46+00:00", "require-dev": {"laravel/framework": "~5.8.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5 || ^8.0"}, "suggest": {"laravel/framework": "Required for testing (~5.8.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (^3.8).", "orchestra/testbench-dusk": "Allow to use <PERSON><PERSON> for testing (^3.8).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.5 || ^8.0)."}}, {"version": "v3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e4ed701ebf92ddbe42dbdeb494039c7586daf41c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e4ed701ebf92ddbe42dbdeb494039c7586daf41c", "type": "zip", "shasum": "", "reference": "e4ed701ebf92ddbe42dbdeb494039c7586daf41c"}, "time": "2019-02-26T21:52:43+00:00"}, {"version": "v3.7.10", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "dd61b35b36f788b6bd20bb1827cd17f128364847"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/dd61b35b36f788b6bd20bb1827cd17f128364847", "type": "zip", "shasum": "", "reference": "dd61b35b36f788b6bd20bb1827cd17f128364847"}, "time": "2019-12-10T01:27:25+00:00", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}, "require-dev": {"laravel/framework": "~5.7.28", "laravel/laravel": "5.7.x-dev", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.7.28).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.7).", "orchestra/testbench-dusk": "Allow to use Laravel <PERSON> for testing (~3.7).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.0)."}}, {"version": "v3.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "55cdd1943eb2a244dc4e83f4d252050189686bbe"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/55cdd1943eb2a244dc4e83f4d252050189686bbe", "type": "zip", "shasum": "", "reference": "55cdd1943eb2a244dc4e83f4d252050189686bbe"}, "time": "2019-06-10T04:24:59+00:00"}, {"version": "v3.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "11b9043c243ee95018bc24d70ea555c087d1559c"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/11b9043c243ee95018bc24d70ea555c087d1559c", "type": "zip", "shasum": "", "reference": "11b9043c243ee95018bc24d70ea555c087d1559c"}, "time": "2019-03-16T06:55:15+00:00"}, {"version": "v3.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "b5c97f3f3ae488c9e92196c2a7fcf3def9d23e36"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/b5c97f3f3ae488c9e92196c2a7fcf3def9d23e36", "type": "zip", "shasum": "", "reference": "b5c97f3f3ae488c9e92196c2a7fcf3def9d23e36"}, "time": "2018-12-04T00:47:44+00:00", "require-dev": {"laravel/framework": "~5.7.14", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.7.14).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.7).", "orchestra/testbench-dusk": "Allow to use Laravel <PERSON> for testing (~3.7).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.0)."}}, {"version": "v3.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6cdbb96c26b0532c3ff659a0eab9cac5a114dd95"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6cdbb96c26b0532c3ff659a0eab9cac5a114dd95", "type": "zip", "shasum": "", "reference": "6cdbb96c26b0532c3ff659a0eab9cac5a114dd95"}, "time": "2018-11-13T02:38:23+00:00", "require-dev": {"laravel/framework": "~5.7.4", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.7.4).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.7).", "orchestra/testbench-dusk": "Allow to use Laravel <PERSON> for testing (~3.7).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.0)."}}, {"version": "v3.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "9ef7319cc288a613e38f456f0349907dfeb2587f"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/9ef7319cc288a613e38f456f0349907dfeb2587f", "type": "zip", "shasum": "", "reference": "9ef7319cc288a613e38f456f0349907dfeb2587f"}, "time": "2018-10-07T01:22:19+00:00"}, {"version": "v3.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a8b9fc170115333240eb0f6507b456bbe09c1121"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a8b9fc170115333240eb0f6507b456bbe09c1121", "type": "zip", "shasum": "", "reference": "a8b9fc170115333240eb0f6507b456bbe09c1121"}, "time": "2018-10-03T07:44:35+00:00"}, {"version": "v3.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cfddff2e51f868b09aa9e66c67664c0cd32438cc"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cfddff2e51f868b09aa9e66c67664c0cd32438cc", "type": "zip", "shasum": "", "reference": "cfddff2e51f868b09aa9e66c67664c0cd32438cc"}, "time": "2018-09-13T09:05:08+00:00"}, {"version": "v3.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "72579b24435463dd846e323d097b6ddc8af257d8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/72579b24435463dd846e323d097b6ddc8af257d8", "type": "zip", "shasum": "", "reference": "72579b24435463dd846e323d097b6ddc8af257d8"}, "time": "2018-09-12T09:41:23+00:00"}, {"version": "v3.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f9cf6adc935a5cc5e59ecf241b27900ee04aec16"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f9cf6adc935a5cc5e59ecf241b27900ee04aec16", "type": "zip", "shasum": "", "reference": "f9cf6adc935a5cc5e59ecf241b27900ee04aec16"}, "time": "2018-09-07T01:54:44+00:00", "require-dev": {"laravel/framework": "~5.7.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.7.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.7).", "orchestra/testbench-dusk": "Allow to use Laravel <PERSON> for testing (~3.7).", "phpunit/phpunit": "Allow to use PHPUnit for testing (^7.0)."}}, {"version": "v3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "21d142febca221382f4a18d3ba7590bf5ed824b8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/21d142febca221382f4a18d3ba7590bf5ed824b8", "type": "zip", "shasum": "", "reference": "21d142febca221382f4a18d3ba7590bf5ed824b8"}, "time": "2018-09-04T15:43:36+00:00"}, {"version": "v3.6.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f7387ce80641af0972de912da373a3dae4faf15e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f7387ce80641af0972de912da373a3dae4faf15e", "type": "zip", "shasum": "", "reference": "f7387ce80641af0972de912da373a3dae4faf15e"}, "time": "2019-12-10T01:15:50+00:00", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "require-dev": {"laravel/framework": "~5.6.39", "laravel/laravel": "5.6.x-dev", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.6.39).", "mockery/mockery": "Allow to use <PERSON><PERSON>y for testing (~1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.6).", "orchestra/testbench-dusk": "Allow to use <PERSON>vel <PERSON> for testing (~3.6).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~7.0)."}}, {"version": "v3.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "46b08bf7f651df23ab0db8c762ac15c790da1c10"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/46b08bf7f651df23ab0db8c762ac15c790da1c10", "type": "zip", "shasum": "", "reference": "46b08bf7f651df23ab0db8c762ac15c790da1c10"}, "time": "2019-06-10T02:37:26+00:00"}, {"version": "v3.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "cd0ead2c66877bed5e82e7cb7eea91a8168c29ba"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/cd0ead2c66877bed5e82e7cb7eea91a8168c29ba", "type": "zip", "shasum": "", "reference": "cd0ead2c66877bed5e82e7cb7eea91a8168c29ba"}, "time": "2018-07-12T01:01:15+00:00", "require-dev": {"laravel/framework": "~5.6.13", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.6.13).", "mockery/mockery": "Allow to use <PERSON><PERSON>y for testing (~1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.6).", "orchestra/testbench-dusk": "Allow to use <PERSON>vel <PERSON> for testing (~3.6).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~7.0)."}}, {"version": "v3.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d089f0fd32a81764fbd98044148a193db828dd52"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d089f0fd32a81764fbd98044148a193db828dd52", "type": "zip", "shasum": "", "reference": "d089f0fd32a81764fbd98044148a193db828dd52"}, "time": "2018-03-27T08:00:28+00:00", "require": {"php": ">=7.1", "fzaninotto/faker": "~1.4"}, "require-dev": {"laravel/framework": "~5.6.13", "mockery/mockery": "~1.0", "phpunit/phpunit": "~7.0"}}, {"version": "v3.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "65c97dbefd2c68aa7d5dfcfdaa5a15184cb7b7e2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/65c97dbefd2c68aa7d5dfcfdaa5a15184cb7b7e2", "type": "zip", "shasum": "", "reference": "65c97dbefd2c68aa7d5dfcfdaa5a15184cb7b7e2"}, "time": "2018-03-13T02:45:12+00:00", "require-dev": {"laravel/framework": "~5.6.0", "mockery/mockery": "~1.0", "phpunit/phpunit": "~7.0"}, "suggest": {"laravel/framework": "Required for testing (~5.6.0).", "mockery/mockery": "Allow to use <PERSON><PERSON>y for testing (~1.0).", "orchestra/testbench-browser-kit": "Allow to use legacy Laravel BrowserKit for testing (~3.6).", "orchestra/testbench-dusk": "Allow to use <PERSON>vel <PERSON> for testing (~3.6).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~7.0)."}}, {"version": "v3.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "e07a2dd5b9399d56f487379ac3af515a4635bd6a"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/e07a2dd5b9399d56f487379ac3af515a4635bd6a", "type": "zip", "shasum": "", "reference": "e07a2dd5b9399d56f487379ac3af515a4635bd6a"}, "time": "2018-02-20T04:08:58+00:00"}, {"version": "v3.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "371844653ef672df963975aae64afa66f21ef6d6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/371844653ef672df963975aae64afa66f21ef6d6", "type": "zip", "shasum": "", "reference": "371844653ef672df963975aae64afa66f21ef6d6"}, "time": "2018-02-18T10:00:41+00:00"}, {"version": "v3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "601d2fc829379485e3ed56249631755a6ca99206"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/601d2fc829379485e3ed56249631755a6ca99206", "type": "zip", "shasum": "", "reference": "601d2fc829379485e3ed56249631755a6ca99206"}, "time": "2018-02-15T02:35:59+00:00"}, {"version": "v3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7dcbff4c5d3e0038c04a4c37b874936ea6986c67"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7dcbff4c5d3e0038c04a4c37b874936ea6986c67", "type": "zip", "shasum": "", "reference": "7dcbff4c5d3e0038c04a4c37b874936ea6986c67"}, "time": "2018-02-07T22:49:44+00:00"}, {"version": "v3.5.11", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "a9c3625a5234ea478546fc0711216d6707ca2509"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/a9c3625a5234ea478546fc0711216d6707ca2509", "type": "zip", "shasum": "", "reference": "a9c3625a5234ea478546fc0711216d6707ca2509"}, "time": "2019-12-10T01:08:48+00:00", "extra": {"branch-alias": {"dev-master": "3.5-dev"}}, "require": {"php": ">=7.0", "fzaninotto/faker": "~1.4"}, "require-dev": {"laravel/framework": "~5.5.0", "mockery/mockery": "~1.0", "orchestra/database": "~3.5.0", "phpunit/phpunit": "~6.0"}, "suggest": {"laravel/framework": "Required for testing (~5.5.0).", "mockery/mockery": "Allow to use <PERSON><PERSON>y for testing (~1.0).", "orchestra/database": "Allow to use --realpath migration for testing (~3.5).", "orchestra/testbench-browser-kit": "Allow to use legacy BrowserKit for testing (~3.5).", "orchestra/testbench-dusk": "Allow to use Laravel <PERSON> for testing (~3.5).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~6.0)."}}, {"version": "v3.5.10", "version_normalized": "********", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "09a57dc446a24fd19a75ff57b679b86094251f8e"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/09a57dc446a24fd19a75ff57b679b86094251f8e", "type": "zip", "shasum": "", "reference": "09a57dc446a24fd19a75ff57b679b86094251f8e"}, "time": "2018-03-13T02:35:58+00:00"}, {"version": "v3.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "997a5dfade0cfb9c88d5e1f2bf4ff76900311333"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/997a5dfade0cfb9c88d5e1f2bf4ff76900311333", "type": "zip", "shasum": "", "reference": "997a5dfade0cfb9c88d5e1f2bf4ff76900311333"}, "time": "2018-02-20T04:07:40+00:00"}, {"version": "v3.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "db38993c4fc2d54684418c224cd7edfba7770031"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/db38993c4fc2d54684418c224cd7edfba7770031", "type": "zip", "shasum": "", "reference": "db38993c4fc2d54684418c224cd7edfba7770031"}, "time": "2018-02-18T09:54:23+00:00"}, {"version": "v3.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5ce308f91e2178cd1c03da02445f4093d4384fd6"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5ce308f91e2178cd1c03da02445f4093d4384fd6", "type": "zip", "shasum": "", "reference": "5ce308f91e2178cd1c03da02445f4093d4384fd6"}, "time": "2018-02-07T08:39:46+00:00"}, {"version": "v3.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "98664ac917c10f744181ca50796faea264f1efb2"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/98664ac917c10f744181ca50796faea264f1efb2", "type": "zip", "shasum": "", "reference": "98664ac917c10f744181ca50796faea264f1efb2"}, "time": "2018-01-06T15:40:19+00:00"}, {"version": "v3.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5fa8871651d054bd1f6eb23bb56c1ec6a5622078"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5fa8871651d054bd1f6eb23bb56c1ec6a5622078", "type": "zip", "shasum": "", "reference": "5fa8871651d054bd1f6eb23bb56c1ec6a5622078"}, "time": "2017-12-25T05:21:42+00:00"}, {"version": "v3.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "afecbf0d68c43f0fd7ae53447bb28bcf08faf0ad"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/afecbf0d68c43f0fd7ae53447bb28bcf08faf0ad", "type": "zip", "shasum": "", "reference": "afecbf0d68c43f0fd7ae53447bb28bcf08faf0ad"}, "time": "2017-10-08T07:47:55+00:00", "require-dev": {"laravel/framework": "~5.5.0", "mockery/mockery": "^0.9.4", "orchestra/database": "~3.5.0", "phpunit/phpunit": "~6.0"}, "suggest": {"laravel/framework": "Required for testing (~5.5.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^0.9.4).", "orchestra/database": "Allow to use --realpath migration for testing (~3.5).", "orchestra/testbench-browser-kit": "Allow to use legacy BrowserKit for testing (~3.5).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~6.0)."}}, {"version": "v3.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "67502c4a5f2836681afa35e00ef1c91be5ff94f8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/67502c4a5f2836681afa35e00ef1c91be5ff94f8", "type": "zip", "shasum": "", "reference": "67502c4a5f2836681afa35e00ef1c91be5ff94f8"}, "time": "2017-10-07T12:09:21+00:00"}, {"version": "v3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "c1ccd1bde213ebf739bfbcbc0e743f1d1f306aa8"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/c1ccd1bde213ebf739bfbcbc0e743f1d1f306aa8", "type": "zip", "shasum": "", "reference": "c1ccd1bde213ebf739bfbcbc0e743f1d1f306aa8"}, "time": "2017-09-28T15:25:06+00:00"}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "58b168a1e3220540dfc6b58739c0f75758579026"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/58b168a1e3220540dfc6b58739c0f75758579026", "type": "zip", "shasum": "", "reference": "58b168a1e3220540dfc6b58739c0f75758579026"}, "time": "2017-09-05T08:25:02+00:00"}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "7afedcd1153e768dad7c832e268079d0d6a60f62"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/7afedcd1153e768dad7c832e268079d0d6a60f62", "type": "zip", "shasum": "", "reference": "7afedcd1153e768dad7c832e268079d0d6a60f62"}, "time": "2017-08-25T10:40:49+00:00"}, {"version": "v3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "6a7ed6b65942c9b1bffc0bf8f0eab442ccb30e5b"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/6a7ed6b65942c9b1bffc0bf8f0eab442ccb30e5b", "type": "zip", "shasum": "", "reference": "6a7ed6b65942c9b1bffc0bf8f0eab442ccb30e5b"}, "time": "2019-12-10T00:56:53+00:00", "require": {"php": ">=5.6.0", "fzaninotto/faker": "~1.4"}, "require-dev": {"mockery/mockery": "^0.9.4", "laravel/framework": "~5.4.17", "phpunit/phpunit": "~5.7 || ~6.0", "orchestra/database": "~3.4.0"}, "suggest": {"laravel/framework": "Required for testing (~5.4.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^0.9.4).", "orchestra/database": "Allow to use --realpath migration for testing (~3.4).", "orchestra/testbench-browser-kit": "Allow to use legacy BrowserKit for testing (~3.4).", "orchestra/testbench-dusk": "Allow to use <PERSON>vel <PERSON> for testing (~3.4).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~6.0)."}}, {"version": "v3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "07f5fca738b7f1a5745e4f9e4049df048019f790"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/07f5fca738b7f1a5745e4f9e4049df048019f790", "type": "zip", "shasum": "", "reference": "07f5fca738b7f1a5745e4f9e4049df048019f790"}, "time": "2018-02-20T04:05:07+00:00"}, {"version": "v3.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "5ad2937cefded830e06c024cf1fc91bbf8bddc76"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/5ad2937cefded830e06c024cf1fc91bbf8bddc76", "type": "zip", "shasum": "", "reference": "5ad2937cefded830e06c024cf1fc91bbf8bddc76"}, "time": "2018-02-18T09:45:32+00:00"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "3533c7477b077afa8fc069979e3afde72a806698"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/3533c7477b077afa8fc069979e3afde72a806698", "type": "zip", "shasum": "", "reference": "3533c7477b077afa8fc069979e3afde72a806698"}, "time": "2017-10-08T07:39:49+00:00", "suggest": {"laravel/framework": "Required for testing (~5.4.0).", "mockery/mockery": "Allow to use <PERSON><PERSON><PERSON> for testing (^0.9.4).", "orchestra/database": "Allow to use --realpath migration for testing (~3.4).", "orchestra/testbench-browser-kit": "Allow to use legacy BrowserKit for testing (~3.4).", "phpunit/phpunit": "Allow to use PHPUnit for testing (~6.0)."}}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "d80d403465017c951fda3feb096ca105bdfcc5ff"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/d80d403465017c951fda3feb096ca105bdfcc5ff", "type": "zip", "shasum": "", "reference": "d80d403465017c951fda3feb096ca105bdfcc5ff"}, "time": "2017-10-07T12:01:03+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f3f615f02fa4c8be972952f954f83cbadae69e41"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f3f615f02fa4c8be972952f954f83cbadae69e41", "type": "zip", "shasum": "", "reference": "f3f615f02fa4c8be972952f954f83cbadae69e41"}, "time": "2017-09-18T22:58:34+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "2a5bafeaac992c4c89da371f5bdd76cb99abb837"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/2a5bafeaac992c4c89da371f5bdd76cb99abb837", "type": "zip", "shasum": "", "reference": "2a5bafeaac992c4c89da371f5bdd76cb99abb837"}, "time": "2017-08-19T03:07:38+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/orchestral/testbench-core.git", "type": "git", "reference": "f26a7c6432995c3ef0a9ee575ed3c48141f12dd3"}, "dist": {"url": "https://api.github.com/repos/orchestral/testbench-core/zipball/f26a7c6432995c3ef0a9ee575ed3c48141f12dd3", "type": "zip", "shasum": "", "reference": "f26a7c6432995c3ef0a9ee575ed3c48141f12dd3"}, "support": {"issues": "https://github.com/orchestral/testbench-core/issues", "source": "https://github.com/orchestral/testbench-core/tree/3.4"}, "time": "2017-06-07T00:49:49+00:00", "require-dev": {"mockery/mockery": "^0.9.4", "laravel/framework": "~5.4.17", "phpunit/phpunit": "~5.7", "orchestra/database": "~3.4.0"}}]}, "security-advisories": [], "last-modified": "Sun, 08 Jun 2025 04:41:15 GMT"}