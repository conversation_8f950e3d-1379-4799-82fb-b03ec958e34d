{"minified": "composer/2.0", "packages": {"symfony/polyfill-uuid": [{"name": "symfony/polyfill-uuid", "description": "Symfony polyfill for uuid functions", "keywords": ["uuid", "compatibility", "portable", "polyfill"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "type": "zip", "shasum": "", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}, "suggest": {"ext-uuid": "For best performance"}, "provide": {"ext-uuid": "*"}}, {"version": "v1.31.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.31.0"}}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "2ba1f33797470debcda07fe9dce20a0003df18e9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/2ba1f33797470debcda07fe9dce20a0003df18e9", "type": "zip", "shasum": "", "reference": "2ba1f33797470debcda07fe9dce20a0003df18e9"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "3abdd21b0ceaa3000ee950097bc3cf9efc137853"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/3abdd21b0ceaa3000ee950097bc3cf9efc137853", "type": "zip", "shasum": "", "reference": "3abdd21b0ceaa3000ee950097bc3cf9efc137853"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "9c44518a5aff8da565c8a55dbe85d2769e6f630e"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/9c44518a5aff8da565c8a55dbe85d2769e6f630e", "type": "zip", "shasum": "", "reference": "9c44518a5aff8da565c8a55dbe85d2769e6f630e"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.28.0"}, "time": "2023-01-26T09:26:14+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "f3cf1a645c2734236ed1e2e671e273eeb3586166"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/f3cf1a645c2734236ed1e2e671e273eeb3586166", "type": "zip", "shasum": "", "reference": "f3cf1a645c2734236ed1e2e671e273eeb3586166"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "a41886c1c81dc075a09c71fe6db5b9d68c79de23"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/a41886c1c81dc075a09c71fe6db5b9d68c79de23", "type": "zip", "shasum": "", "reference": "a41886c1c81dc075a09c71fe6db5b9d68c79de23"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "7529922412d23ac44413d0f308861d50cf68d3ee"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/7529922412d23ac44413d0f308861d50cf68d3ee", "type": "zip", "shasum": "", "reference": "7529922412d23ac44413d0f308861d50cf68d3ee"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.25.0"}, "time": "2021-10-20T20:35:02+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.24.0"}}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "9165effa2eb8a31bb3fa608df9d529920d21ddd9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/9165effa2eb8a31bb3fa608df9d529920d21ddd9", "type": "zip", "shasum": "", "reference": "9165effa2eb8a31bb3fa608df9d529920d21ddd9"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.23.0"}, "time": "2021-02-19T12:13:01+00:00", "provide": "__unset"}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "9773608c15d3fe6ba2b6456a124777a7b8ffee2a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/9773608c15d3fe6ba2b6456a124777a7b8ffee2a", "type": "zip", "shasum": "", "reference": "9773608c15d3fe6ba2b6456a124777a7b8ffee2a"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.22.1"}, "time": "2021-01-22T09:19:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "17e0611d2e180a91d02b4fa8b03aab0368b661bc"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/17e0611d2e180a91d02b4fa8b03aab0368b661bc", "type": "zip", "shasum": "", "reference": "17e0611d2e180a91d02b4fa8b03aab0368b661bc"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.22.0"}, "time": "2021-01-07T16:49:33+00:00"}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "7095799250ff244f3015dc492480175a249e7b55"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/7095799250ff244f3015dc492480175a249e7b55", "type": "zip", "shasum": "", "reference": "7095799250ff244f3015dc492480175a249e7b55"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "0cb03887bd447b892a8db9504b62659bf375056d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/0cb03887bd447b892a8db9504b62659bf375056d", "type": "zip", "shasum": "", "reference": "0cb03887bd447b892a8db9504b62659bf375056d"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.19.0"}, "time": "2020-10-21T09:57:48+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=5.3.3", "paragonie/random_compat": "~1.0|~2.0|~9.99"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "da48e2cccd323e48c16c26481bf5800f6ab1c49d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/da48e2cccd323e48c16c26481bf5800f6ab1c49d", "type": "zip", "shasum": "", "reference": "da48e2cccd323e48c16c26481bf5800f6ab1c49d"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.18.1"}, "time": "2020-07-14T12:35:20+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/master"}}, {"version": "v1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "5db3e66818f1f768b585220438436ea6c1e0b874"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/5db3e66818f1f768b585220438436ea6c1e0b874", "type": "zip", "shasum": "", "reference": "5db3e66818f1f768b585220438436ea6c1e0b874"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.17.1"}, "time": "2020-06-06T08:46:27+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "6dbf0269e8aeab8253a5059c51c1760fb4034e87"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/6dbf0269e8aeab8253a5059c51c1760fb4034e87", "type": "zip", "shasum": "", "reference": "6dbf0269e8aeab8253a5059c51c1760fb4034e87"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.17.0"}, "time": "2020-05-12T16:47:27+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "6bb7ba3890d777d69d529000bf8dd2948a56c14c"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/6bb7ba3890d777d69d529000bf8dd2948a56c14c", "type": "zip", "shasum": "", "reference": "6bb7ba3890d777d69d529000bf8dd2948a56c14c"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.16.0"}, "time": "2020-05-08T17:27:24+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "2318f7f470a892867f3de602e403d006b1b9c9aa"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/2318f7f470a892867f3de602e403d006b1b9c9aa", "type": "zip", "shasum": "", "reference": "2318f7f470a892867f3de602e403d006b1b9c9aa"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/master"}, "time": "2020-03-23T13:44:10+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "80781e68dbd85373eb36a1b67608b1a731931000"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/80781e68dbd85373eb36a1b67608b1a731931000", "type": "zip", "shasum": "", "reference": "80781e68dbd85373eb36a1b67608b1a731931000"}, "time": "2020-01-13T11:15:53+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}, {"version": "v1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "7d4215b6944add5073f0ec313a21e1bc2520520d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/7d4215b6944add5073f0ec313a21e1bc2520520d", "type": "zip", "shasum": "", "reference": "7d4215b6944add5073f0ec313a21e1bc2520520d"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.13.1"}, "time": "2019-11-30T08:58:35+00:00", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}}, {"version": "v1.13.1", "version_normalized": "********"}, {"version": "v1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-uuid.git", "type": "git", "reference": "01b3f51e6c13448e09291a133319dee2df13e20a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/01b3f51e6c13448e09291a133319dee2df13e20a", "type": "zip", "shasum": "", "reference": "01b3f51e6c13448e09291a133319dee2df13e20a"}, "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/master"}, "time": "2019-11-27T15:11:23+00:00", "require": {"php": ">=7"}}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 09:41:39 GMT"}