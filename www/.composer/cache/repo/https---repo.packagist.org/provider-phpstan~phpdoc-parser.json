{"minified": "composer/2.0", "packages": {"phpstan/phpdoc-parser": [{"name": "phpstan/phpdoc-parser", "description": "PHPDoc parser with support for nullable, intersection and generic types", "keywords": [], "homepage": "", "version": "2.1.0", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "type": "zip", "shasum": "", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "type": "library", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "funding": [], "time": "2025-02-19T13:28:12+00:00", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "51087f87dcce2663e1fed4dfd4e56eccd580297e"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/51087f87dcce2663e1fed4dfd4e56eccd580297e", "type": "zip", "shasum": "", "reference": "51087f87dcce2663e1fed4dfd4e56eccd580297e"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.0.2"}, "time": "2025-02-17T20:25:51+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "72e51f7c32c5aef7c8b462195b8c599b11199893"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/72e51f7c32c5aef7c8b462195b8c599b11199893", "type": "zip", "shasum": "", "reference": "72e51f7c32c5aef7c8b462195b8c599b11199893"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.0.1"}, "time": "2025-02-13T12:25:43+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "c00d78fb6b29658347f9d37ebe104bffadf36299"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/c00d78fb6b29658347f9d37ebe104bffadf36299", "type": "zip", "shasum": "", "reference": "c00d78fb6b29658347f9d37ebe104bffadf36299"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.0.0"}, "time": "2024-10-13T11:29:49+00:00"}, {"version": "1.33.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "82a311fd3690fb2bf7b64d5c98f912b3dd746140"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/82a311fd3690fb2bf7b64d5c98f912b3dd746140", "type": "zip", "shasum": "", "reference": "82a311fd3690fb2bf7b64d5c98f912b3dd746140"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.33.0"}, "time": "2024-10-13T11:25:22+00:00", "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}}, {"version": "1.32.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "6ca22b154efdd9e3c68c56f5d94670920a1c19a4"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/6ca22b154efdd9e3c68c56f5d94670920a1c19a4", "type": "zip", "shasum": "", "reference": "6ca22b154efdd9e3c68c56f5d94670920a1c19a4"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.32.0"}, "time": "2024-09-26T07:23:32+00:00"}, {"version": "1.31.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "249f15fb843bf240cf058372dad29e100cee6c17"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/249f15fb843bf240cf058372dad29e100cee6c17", "type": "zip", "shasum": "", "reference": "249f15fb843bf240cf058372dad29e100cee6c17"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.31.0"}, "time": "2024-09-22T11:32:18+00:00"}, {"version": "1.30.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "51b95ec8670af41009e2b2b56873bad96682413e"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/51b95ec8670af41009e2b2b56873bad96682413e", "type": "zip", "shasum": "", "reference": "51b95ec8670af41009e2b2b56873bad96682413e"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.30.1"}, "time": "2024-09-07T20:13:05+00:00"}, {"version": "1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "5ceb0e384997db59f38774bf79c2a6134252c08f"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/5ceb0e384997db59f38774bf79c2a6134252c08f", "type": "zip", "shasum": "", "reference": "5ceb0e384997db59f38774bf79c2a6134252c08f"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.30.0"}, "time": "2024-08-29T09:54:52+00:00"}, {"version": "1.29.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fcaefacf2d5c417e928405b71b400d4ce10daaf4", "type": "zip", "shasum": "", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.29.1"}, "time": "2024-05-31T08:52:43+00:00"}, {"version": "1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "536889f2b340489d328f5ffb7b02bb6b183ddedc"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/536889f2b340489d328f5ffb7b02bb6b183ddedc", "type": "zip", "shasum": "", "reference": "536889f2b340489d328f5ffb7b02bb6b183ddedc"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.29.0"}, "time": "2024-05-06T12:04:23+00:00"}, {"version": "1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "cd06d6b1a1b3c75b0b83f97577869fd85a3cd4fb"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/cd06d6b1a1b3c75b0b83f97577869fd85a3cd4fb", "type": "zip", "shasum": "", "reference": "cd06d6b1a1b3c75b0b83f97577869fd85a3cd4fb"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.28.0"}, "time": "2024-04-03T18:51:33+00:00"}, {"version": "1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "86e4d5a4b036f8f0be1464522f4c6b584c452757"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/86e4d5a4b036f8f0be1464522f4c6b584c452757", "type": "zip", "shasum": "", "reference": "86e4d5a4b036f8f0be1464522f4c6b584c452757"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.27.0"}, "time": "2024-03-21T13:14:53+00:00"}, {"version": "1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "231e3186624c03d7e7c890ec662b81e6b0405227"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/231e3186624c03d7e7c890ec662b81e6b0405227", "type": "zip", "shasum": "", "reference": "231e3186624c03d7e7c890ec662b81e6b0405227"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.26.0"}, "time": "2024-02-23T16:05:55+00:00"}, {"version": "1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "bd84b629c8de41aa2ae82c067c955e06f1b00240"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/bd84b629c8de41aa2ae82c067c955e06f1b00240", "type": "zip", "shasum": "", "reference": "bd84b629c8de41aa2ae82c067c955e06f1b00240"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.25.0"}, "time": "2024-01-04T17:06:16+00:00"}, {"version": "1.24.5", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "fedf211ff14ec8381c9bf5714e33a7a552dd1acc"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fedf211ff14ec8381c9bf5714e33a7a552dd1acc", "type": "zip", "shasum": "", "reference": "fedf211ff14ec8381c9bf5714e33a7a552dd1acc"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.5"}, "time": "2023-12-16T09:33:33+00:00"}, {"version": "1.24.4", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "6bd0c26f3786cd9b7c359675cb789e35a8e07496"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/6bd0c26f3786cd9b7c359675cb789e35a8e07496", "type": "zip", "shasum": "", "reference": "6bd0c26f3786cd9b7c359675cb789e35a8e07496"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.4"}, "time": "2023-11-26T18:29:22+00:00"}, {"version": "1.24.3", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "12f01d214f1c73b9c91fdb3b1c415e4c70652083"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/12f01d214f1c73b9c91fdb3b1c415e4c70652083", "type": "zip", "shasum": "", "reference": "12f01d214f1c73b9c91fdb3b1c415e4c70652083"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.3"}, "time": "2023-11-18T20:15:32+00:00"}, {"version": "1.24.2", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "bcad8d995980440892759db0c32acae7c8e79442"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/bcad8d995980440892759db0c32acae7c8e79442", "type": "zip", "shasum": "", "reference": "bcad8d995980440892759db0c32acae7c8e79442"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.2"}, "time": "2023-09-26T12:28:12+00:00"}, {"version": "1.24.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "9f854d275c2dbf84915a5c0ec9a2d17d2cd86b01"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9f854d275c2dbf84915a5c0ec9a2d17d2cd86b01", "type": "zip", "shasum": "", "reference": "9f854d275c2dbf84915a5c0ec9a2d17d2cd86b01"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.1"}, "time": "2023-09-18T12:18:02+00:00"}, {"version": "1.24.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "3510b0a6274cc42f7219367cb3abfc123ffa09d6"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/3510b0a6274cc42f7219367cb3abfc123ffa09d6", "type": "zip", "shasum": "", "reference": "3510b0a6274cc42f7219367cb3abfc123ffa09d6"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.24.0"}, "time": "2023-09-07T20:46:32+00:00"}, {"version": "1.23.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "846ae76eef31c6d7790fac9bc399ecee45160b26"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/846ae76eef31c6d7790fac9bc399ecee45160b26", "type": "zip", "shasum": "", "reference": "846ae76eef31c6d7790fac9bc399ecee45160b26"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.23.1"}, "time": "2023-08-03T16:32:59+00:00"}, {"version": "1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "a2b24135c35852b348894320d47b3902a94bc494"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/a2b24135c35852b348894320d47b3902a94bc494", "type": "zip", "shasum": "", "reference": "a2b24135c35852b348894320d47b3902a94bc494"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.23.0"}, "time": "2023-07-23T22:17:56+00:00"}, {"version": "1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "65c39594fbd8c67abfc68bb323f86447bab79cc0"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/65c39594fbd8c67abfc68bb323f86447bab79cc0", "type": "zip", "shasum": "", "reference": "65c39594fbd8c67abfc68bb323f86447bab79cc0"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.22.1"}, "time": "2023-06-29T20:46:06+00:00"}, {"version": "1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "ec58baf7b3c7f1c81b3b00617c953249fb8cf30c"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/ec58baf7b3c7f1c81b3b00617c953249fb8cf30c", "type": "zip", "shasum": "", "reference": "ec58baf7b3c7f1c81b3b00617c953249fb8cf30c"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.22.0"}, "time": "2023-06-01T12:35:21+00:00"}, {"version": "1.21.3", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "b0c366dd2cea79407d635839d25423ba07c55dd6"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/b0c366dd2cea79407d635839d25423ba07c55dd6", "type": "zip", "shasum": "", "reference": "b0c366dd2cea79407d635839d25423ba07c55dd6"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.21.3"}, "time": "2023-05-29T19:31:28+00:00", "require-dev": {"nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}}, {"version": "1.21.2", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "a8094fafabf0d7c7b7337a9c6496f4d8fbc718f1"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/a8094fafabf0d7c7b7337a9c6496f4d8fbc718f1", "type": "zip", "shasum": "", "reference": "a8094fafabf0d7c7b7337a9c6496f4d8fbc718f1"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.21.2"}, "time": "2023-05-29T15:20:08+00:00"}, {"version": "1.21.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "e560a3eb5e76b35d6d92377e5abb6887c1c13c95"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/e560a3eb5e76b35d6d92377e5abb6887c1c13c95", "type": "zip", "shasum": "", "reference": "e560a3eb5e76b35d6d92377e5abb6887c1c13c95"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.21.1"}, "time": "2023-05-29T11:55:57+00:00"}, {"version": "1.21.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "6df62b08faef4f899772bc7c3bbabb93d2b7a21c"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/6df62b08faef4f899772bc7c3bbabb93d2b7a21c", "type": "zip", "shasum": "", "reference": "6df62b08faef4f899772bc7c3bbabb93d2b7a21c"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.21.0"}, "time": "2023-05-17T13:13:44+00:00"}, {"version": "1.20.4", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "7d568c87a9df9c5f7e8b5f075fc469aa8cb0a4cd"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/7d568c87a9df9c5f7e8b5f075fc469aa8cb0a4cd", "type": "zip", "shasum": "", "reference": "7d568c87a9df9c5f7e8b5f075fc469aa8cb0a4cd"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.20.4"}, "time": "2023-05-02T09:19:37+00:00", "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}}, {"version": "1.20.3", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "6c04009f6cae6eda2f040745b6b846080ef069c2"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/6c04009f6cae6eda2f040745b6b846080ef069c2", "type": "zip", "shasum": "", "reference": "6c04009f6cae6eda2f040745b6b846080ef069c2"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.20.3"}, "time": "2023-04-25T09:01:03+00:00"}, {"version": "1.20.2", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "90490bd8fd8530a272043c4950c180b6d0cf5f81"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/90490bd8fd8530a272043c4950c180b6d0cf5f81", "type": "zip", "shasum": "", "reference": "90490bd8fd8530a272043c4950c180b6d0cf5f81"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.20.2"}, "time": "2023-04-22T12:59:35+00:00"}, {"version": "1.20.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "57f6787f0bb6431905a18aa7caea25dcd2bd59e0"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/57f6787f0bb6431905a18aa7caea25dcd2bd59e0", "type": "zip", "shasum": "", "reference": "57f6787f0bb6431905a18aa7caea25dcd2bd59e0"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.20.1"}, "time": "2023-04-22T09:05:52+00:00"}, {"version": "1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "10553ab3f0337ff1a71433c3417d7eb2a3eec1fd"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/10553ab3f0337ff1a71433c3417d7eb2a3eec1fd", "type": "zip", "shasum": "", "reference": "10553ab3f0337ff1a71433c3417d7eb2a3eec1fd"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.20.0"}, "time": "2023-04-20T11:18:07+00:00"}, {"version": "1.19.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "f545fc30978190a056832aa7ed995e36a66267f3"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/f545fc30978190a056832aa7ed995e36a66267f3", "type": "zip", "shasum": "", "reference": "f545fc30978190a056832aa7ed995e36a66267f3"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.19.1"}, "time": "2023-04-18T11:30:56+00:00"}, {"version": "1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "178b33aa1c8b8d7725f0abee618ef47337e607ce"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/178b33aa1c8b8d7725f0abee618ef47337e607ce", "type": "zip", "shasum": "", "reference": "178b33aa1c8b8d7725f0abee618ef47337e607ce"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.19.0"}, "time": "2023-04-17T13:16:52+00:00"}, {"version": "1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "22dcdfd725ddf99583bfe398fc624ad6c5004a0f"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/22dcdfd725ddf99583bfe398fc624ad6c5004a0f", "type": "zip", "shasum": "", "reference": "22dcdfd725ddf99583bfe398fc624ad6c5004a0f"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.18.1"}, "time": "2023-04-07T11:51:11+00:00"}, {"version": "1.18.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "882eabc9b6a12e25c27091a261397f9c8792e722"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/882eabc9b6a12e25c27091a261397f9c8792e722", "type": "zip", "shasum": "", "reference": "882eabc9b6a12e25c27091a261397f9c8792e722"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.18.0"}, "time": "2023-04-06T07:26:43+00:00"}, {"version": "1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "d3753fcb3abc6f78f5de6f72153d4b9c99c72dee"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/d3753fcb3abc6f78f5de6f72153d4b9c99c72dee", "type": "zip", "shasum": "", "reference": "d3753fcb3abc6f78f5de6f72153d4b9c99c72dee"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.17.1"}, "time": "2023-04-04T11:11:22+00:00"}, {"version": "1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "bfec8729f7e23c40670f98e27e694cfdb13fc12a"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/bfec8729f7e23c40670f98e27e694cfdb13fc12a", "type": "zip", "shasum": "", "reference": "bfec8729f7e23c40670f98e27e694cfdb13fc12a"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.17.0"}, "time": "2023-04-04T08:27:19+00:00"}, {"version": "1.16.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "e27e92d939e2e3636f0a1f0afaba59692c0bf571"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/e27e92d939e2e3636f0a1f0afaba59692c0bf571", "type": "zip", "shasum": "", "reference": "e27e92d939e2e3636f0a1f0afaba59692c0bf571"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.16.1"}, "time": "2023-02-07T18:11:17+00:00"}, {"version": "1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "57090cfccbfaa639e703c007486d605a6e80f56d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/57090cfccbfaa639e703c007486d605a6e80f56d", "type": "zip", "shasum": "", "reference": "57090cfccbfaa639e703c007486d605a6e80f56d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.16.0"}, "time": "2023-01-29T14:41:23+00:00"}, {"version": "1.15.3", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "61800f71a5526081d1b5633766aa88341f1ade76"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/61800f71a5526081d1b5633766aa88341f1ade76", "type": "zip", "shasum": "", "reference": "61800f71a5526081d1b5633766aa88341f1ade76"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.15.3"}, "time": "2022-12-20T20:56:55+00:00"}, {"version": "1.15.2", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "5941477f100993652218928039d530b75a13a9ca"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/5941477f100993652218928039d530b75a13a9ca", "type": "zip", "shasum": "", "reference": "5941477f100993652218928039d530b75a13a9ca"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.15.2"}, "time": "2022-12-16T06:42:48+00:00"}, {"version": "1.15.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "950bddf2c4203fa08ad27b8349bba1f32a8a7ebe"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/950bddf2c4203fa08ad27b8349bba1f32a8a7ebe", "type": "zip", "shasum": "", "reference": "950bddf2c4203fa08ad27b8349bba1f32a8a7ebe"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.15.1"}, "time": "2022-12-15T16:40:13+00:00"}, {"version": "1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "6ff970a7101acfe99b3048e4bbfbc094e55c5b04"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/6ff970a7101acfe99b3048e4bbfbc094e55c5b04", "type": "zip", "shasum": "", "reference": "6ff970a7101acfe99b3048e4bbfbc094e55c5b04"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.15.0"}, "time": "2022-12-07T16:12:39+00:00"}, {"version": "1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "df1a79430e14e30cd192fe6c05842133d076e58d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/df1a79430e14e30cd192fe6c05842133d076e58d", "type": "zip", "shasum": "", "reference": "df1a79430e14e30cd192fe6c05842133d076e58d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.14.0"}, "time": "2022-11-27T19:09:16+00:00"}, {"version": "1.13.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "aac44118344d197e6d5f7c6cee91885f0a89acdd"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/aac44118344d197e6d5f7c6cee91885f0a89acdd", "type": "zip", "shasum": "", "reference": "aac44118344d197e6d5f7c6cee91885f0a89acdd"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.13.1"}, "time": "2022-11-20T08:52:26+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "33aefcdab42900e36366d0feab6206e2dd68f947"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/33aefcdab42900e36366d0feab6206e2dd68f947", "type": "zip", "shasum": "", "reference": "33aefcdab42900e36366d0feab6206e2dd68f947"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.13.0"}, "time": "2022-10-21T09:57:39+00:00"}, {"version": "1.12.1", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "ae85d4b73c768cefe5c889bf9d772e7e534ac620"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/ae85d4b73c768cefe5c889bf9d772e7e534ac620", "type": "zip", "shasum": "", "reference": "ae85d4b73c768cefe5c889bf9d772e7e534ac620"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.12.1"}, "time": "2022-10-21T07:19:41+00:00"}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "5f13698464773fa6f5392a9e311f81e23e9c3ba7"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/5f13698464773fa6f5392a9e311f81e23e9c3ba7", "type": "zip", "shasum": "", "reference": "5f13698464773fa6f5392a9e311f81e23e9c3ba7"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.12.0"}, "time": "2022-10-20T07:49:58+00:00"}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "7d1e81213b0c7eb8d5a9f524456cbc2778ed5c65"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/7d1e81213b0c7eb8d5a9f524456cbc2778ed5c65", "type": "zip", "shasum": "", "reference": "7d1e81213b0c7eb8d5a9f524456cbc2778ed5c65"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.11.0"}, "time": "2022-10-14T13:32:28+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "87fa2d526e56737a2ae8fa201a61b15efae59b8a"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/87fa2d526e56737a2ae8fa201a61b15efae59b8a", "type": "zip", "shasum": "", "reference": "87fa2d526e56737a2ae8fa201a61b15efae59b8a"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.10.0"}, "time": "2022-10-12T19:19:18+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "5f7eb9724b0ae386b922f34b62b3b55fee3b26cd"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/5f7eb9724b0ae386b922f34b62b3b55fee3b26cd", "type": "zip", "shasum": "", "reference": "5f7eb9724b0ae386b922f34b62b3b55fee3b26cd"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.9.0"}, "time": "2022-10-06T11:32:36+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "8dd908dd6156e974b9a0f8bb4cd5ad0707830f04"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/8dd908dd6156e974b9a0f8bb4cd5ad0707830f04", "type": "zip", "shasum": "", "reference": "8dd908dd6156e974b9a0f8bb4cd5ad0707830f04"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.8.0"}, "time": "2022-09-04T18:59:06+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "367a8d9d5f7da2a0136422d27ce8840583926955"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/367a8d9d5f7da2a0136422d27ce8840583926955", "type": "zip", "shasum": "", "reference": "367a8d9d5f7da2a0136422d27ce8840583926955"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.7.0"}, "time": "2022-08-09T12:23:23+00:00"}, {"version": "1.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "135607f9ccc297d6923d49c2bcf309f509413215"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/135607f9ccc297d6923d49c2bcf309f509413215", "type": "zip", "shasum": "", "reference": "135607f9ccc297d6923d49c2bcf309f509413215"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.6.4"}, "time": "2022-06-26T13:09:08+00:00"}, {"version": "1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "4a07085f74cb1f3fc7103efa537d9f00ebb74ec7"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/4a07085f74cb1f3fc7103efa537d9f00ebb74ec7", "type": "zip", "shasum": "", "reference": "4a07085f74cb1f3fc7103efa537d9f00ebb74ec7"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.6.3"}, "time": "2022-06-14T11:40:08+00:00"}, {"version": "1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "76150ae7512439b4e6903db834e4a327596b617d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/76150ae7512439b4e6903db834e4a327596b617d", "type": "zip", "shasum": "", "reference": "76150ae7512439b4e6903db834e4a327596b617d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.6.2"}, "time": "2022-06-10T09:29:33+00:00"}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "5aaeb83195d6d96515329d14688547d7c81e59e9"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/5aaeb83195d6d96515329d14688547d7c81e59e9", "type": "zip", "shasum": "", "reference": "5aaeb83195d6d96515329d14688547d7c81e59e9"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.6.1"}, "time": "2022-06-10T08:12:59+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "3cb62d10845338136ff4ba299ab0986bbf84ba77"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/3cb62d10845338136ff4ba299ab0986bbf84ba77", "type": "zip", "shasum": "", "reference": "3cb62d10845338136ff4ba299ab0986bbf84ba77"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.6.0"}, "time": "2022-06-09T09:45:59+00:00", "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "981cc368a216c988e862a75e526b6076987d1b50"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/981cc368a216c988e862a75e526b6076987d1b50", "type": "zip", "shasum": "", "reference": "981cc368a216c988e862a75e526b6076987d1b50"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.5.1"}, "time": "2022-05-05T11:32:40+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "6cafed9212aa56aaa3aaf3b67c0fd1e5de53f50c"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/6cafed9212aa56aaa3aaf3b67c0fd1e5de53f50c", "type": "zip", "shasum": "", "reference": "6cafed9212aa56aaa3aaf3b67c0fd1e5de53f50c"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.5.0"}, "time": "2022-05-04T07:46:54+00:00"}, {"version": "1.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "129a63b3bc7caeb593c224c41f420675e63cfefc"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/129a63b3bc7caeb593c224c41f420675e63cfefc", "type": "zip", "shasum": "", "reference": "129a63b3bc7caeb593c224c41f420675e63cfefc"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.5"}, "time": "2022-04-22T11:11:01+00:00"}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "d8e9fd97ca11f2f24fc1aafbcfb1f78bce762267"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/d8e9fd97ca11f2f24fc1aafbcfb1f78bce762267", "type": "zip", "shasum": "", "reference": "d8e9fd97ca11f2f24fc1aafbcfb1f78bce762267"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.4"}, "time": "2022-04-14T12:24:06+00:00"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "34545bb30a6f8bd86cfa371dbd42140a657bbf0d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/34545bb30a6f8bd86cfa371dbd42140a657bbf0d", "type": "zip", "shasum": "", "reference": "34545bb30a6f8bd86cfa371dbd42140a657bbf0d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.3"}, "time": "2022-04-08T11:30:34+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "4cb3021a4e10ffe3d5f94a4c34cf4b3f6de2fa3d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/4cb3021a4e10ffe3d5f94a4c34cf4b3f6de2fa3d", "type": "zip", "shasum": "", "reference": "4cb3021a4e10ffe3d5f94a4c34cf4b3f6de2fa3d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.2"}, "time": "2022-03-30T13:33:37+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "05333065c88f9518e08b094bdd10a4a9e538b2d4"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/05333065c88f9518e08b094bdd10a4a9e538b2d4", "type": "zip", "shasum": "", "reference": "05333065c88f9518e08b094bdd10a4a9e538b2d4"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.1"}, "time": "2022-03-29T09:28:16+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "aa111aa1fecbc436f01e5439d88906bd3b612027"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/aa111aa1fecbc436f01e5439d88906bd3b612027", "type": "zip", "shasum": "", "reference": "aa111aa1fecbc436f01e5439d88906bd3b612027"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.0"}, "time": "2022-03-28T10:52:48+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "c08946968bda74869e696982b0af40a9a8784c84"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/c08946968bda74869e696982b0af40a9a8784c84", "type": "zip", "shasum": "", "reference": "c08946968bda74869e696982b0af40a9a8784c84"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.3.0"}, "time": "2022-03-28T07:53:31+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "dbc093d7af60eff5cd575d2ed761b15ed40bd08e"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/dbc093d7af60eff5cd575d2ed761b15ed40bd08e", "type": "zip", "shasum": "", "reference": "dbc093d7af60eff5cd575d2ed761b15ed40bd08e"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.2.0"}, "time": "2021-09-16T20:46:02+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "605e3697d7e521a8c7f032662e13a8c104d60fbf"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/605e3697d7e521a8c7f032662e13a8c104d60fbf", "type": "zip", "shasum": "", "reference": "605e3697d7e521a8c7f032662e13a8c104d60fbf"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.1.0"}, "time": "2021-09-16T20:28:27+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "9e8e6e79f86c2e4fdddb189e569276ce693b6dd7"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9e8e6e79f86c2e4fdddb189e569276ce693b6dd7", "type": "zip", "shasum": "", "reference": "9e8e6e79f86c2e4fdddb189e569276ce693b6dd7"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.0.0"}, "time": "2021-09-12T20:00:31+00:00", "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.87", "phpstan/phpstan-strict-rules": "^0.12.5", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}}, {"version": "0.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "816e826ce0b7fb32098d8cb6de62511ce6021cea"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/816e826ce0b7fb32098d8cb6de62511ce6021cea", "type": "zip", "shasum": "", "reference": "816e826ce0b7fb32098d8cb6de62511ce6021cea"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.7"}, "time": "2021-09-12T11:52:00+00:00", "extra": {"branch-alias": {"dev-master": "0.5-dev"}}}, {"version": "0.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "fac86158ffc7392e49636f77e63684c026df43b8"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fac86158ffc7392e49636f77e63684c026df43b8", "type": "zip", "shasum": "", "reference": "fac86158ffc7392e49636f77e63684c026df43b8"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.6"}, "time": "2021-08-31T08:08:22+00:00"}, {"version": "0.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "ea0b17460ec38e20d7eb64e7ec49b5d44af5d28c"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/ea0b17460ec38e20d7eb64e7ec49b5d44af5d28c", "type": "zip", "shasum": "", "reference": "ea0b17460ec38e20d7eb64e7ec49b5d44af5d28c"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.5"}, "time": "2021-06-11T13:24:46+00:00"}, {"version": "0.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "e352d065af1ae9b41c12d1dfd309e90f7b1f55c9"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/e352d065af1ae9b41c12d1dfd309e90f7b1f55c9", "type": "zip", "shasum": "", "reference": "e352d065af1ae9b41c12d1dfd309e90f7b1f55c9"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.4"}, "time": "2021-04-03T14:46:19+00:00", "require-dev": {"phing/phing": "^2.16.3", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.60", "phpstan/phpstan-strict-rules": "^0.12.5", "phpunit/phpunit": "^7.5.20", "symfony/process": "^5.2"}}, {"version": "0.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "7c3115eeb29211577a64a7a5757abbb2d20a652d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/7c3115eeb29211577a64a7a5757abbb2d20a652d", "type": "zip", "shasum": "", "reference": "7c3115eeb29211577a64a7a5757abbb2d20a652d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.3"}, "time": "2021-03-27T11:40:37+00:00"}, {"version": "0.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "2269e476268a46e1a9b71fef61a6e5584b819b74"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/2269e476268a46e1a9b71fef61a6e5584b819b74", "type": "zip", "shasum": "", "reference": "2269e476268a46e1a9b71fef61a6e5584b819b74"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.2"}, "time": "2021-03-22T17:32:47+00:00"}, {"version": "0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "b2169b6c87a97f509893323c448aceb9429357c8"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/b2169b6c87a97f509893323c448aceb9429357c8", "type": "zip", "shasum": "", "reference": "b2169b6c87a97f509893323c448aceb9429357c8"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.1"}, "time": "2021-03-19T10:54:26+00:00"}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "cfe3c783bc6230aeda50dc1737ae56f829aeb18a"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/cfe3c783bc6230aeda50dc1737ae56f829aeb18a", "type": "zip", "shasum": "", "reference": "cfe3c783bc6230aeda50dc1737ae56f829aeb18a"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.5.0"}, "time": "2021-03-18T21:19:05+00:00"}, {"version": "0.4.14", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "cf4fc7d2aeca6910fba061901ffd7d107ccfdbcc"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/cf4fc7d2aeca6910fba061901ffd7d107ccfdbcc", "type": "zip", "shasum": "", "reference": "cf4fc7d2aeca6910fba061901ffd7d107ccfdbcc"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.14"}, "time": "2021-03-19T10:54:14+00:00", "extra": {"branch-alias": {"dev-master": "0.4-dev"}}}, {"version": "0.4.13", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "4136e0056ac6d37b96eefe7ea33490258a874651"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/4136e0056ac6d37b96eefe7ea33490258a874651", "type": "zip", "shasum": "", "reference": "4136e0056ac6d37b96eefe7ea33490258a874651"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.13"}, "time": "2021-03-18T21:02:48+00:00"}, {"version": "0.4.12", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "2e17e4a90702d8b7ead58f4e08478a8e819ba6b8"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/2e17e4a90702d8b7ead58f4e08478a8e819ba6b8", "type": "zip", "shasum": "", "reference": "2e17e4a90702d8b7ead58f4e08478a8e819ba6b8"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.12"}, "time": "2021-02-28T14:30:10+00:00"}, {"version": "0.4.11", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "2ce4c6623376d2613cf02e055ea1a926d906cef7"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/2ce4c6623376d2613cf02e055ea1a926d906cef7", "type": "zip", "shasum": "", "reference": "2ce4c6623376d2613cf02e055ea1a926d906cef7"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.11"}, "time": "2021-02-19T11:57:27+00:00"}, {"version": "0.4.10", "version_normalized": "********", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "5c1eb9aac80cb236f1b7fbe52e691afe4cc9f430"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/5c1eb9aac80cb236f1b7fbe52e691afe4cc9f430", "type": "zip", "shasum": "", "reference": "5c1eb9aac80cb236f1b7fbe52e691afe4cc9f430"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.10"}, "time": "2020-12-12T15:45:28+00:00", "require-dev": {"phing/phing": "^2.16.3", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.60", "phpstan/phpstan-strict-rules": "^0.12.5", "phpunit/phpunit": "^7.5.20", "symfony/process": "^4.0"}}, {"version": "0.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "98a088b17966bdf6ee25c8a4b634df313d8aa531"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/98a088b17966bdf6ee25c8a4b634df313d8aa531", "type": "zip", "shasum": "", "reference": "98a088b17966bdf6ee25c8a4b634df313d8aa531"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/master"}, "time": "2020-08-03T20:32:43+00:00", "require-dev": {"consistence/coding-standard": "^3.5", "ergebnis/composer-normalize": "^2.0.2", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.26", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "symfony/process": "^4.0"}}, {"version": "0.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "1948aa842a94170b408963a9768a102f19cbf1b7"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/1948aa842a94170b408963a9768a102f19cbf1b7", "type": "zip", "shasum": "", "reference": "1948aa842a94170b408963a9768a102f19cbf1b7"}, "time": "2020-06-10T05:04:41+00:00", "require": {"php": "~7.1"}}, {"version": "0.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "44500ca1b77f52a623bd4ad9216a68f6e0aaa127"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/44500ca1b77f52a623bd4ad9216a68f6e0aaa127", "type": "zip", "shasum": "", "reference": "44500ca1b77f52a623bd4ad9216a68f6e0aaa127"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.7"}, "time": "2020-05-06T06:52:35+00:00", "require-dev": {"consistence/coding-standard": "^3.5", "ergebnis/composer-normalize": "^2.0.2", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.23", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "symfony/process": "^4.0"}}, {"version": "0.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "7093385390a708ace8a859524689a0d9fb7b4dd5"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/7093385390a708ace8a859524689a0d9fb7b4dd5", "type": "zip", "shasum": "", "reference": "7093385390a708ace8a859524689a0d9fb7b4dd5"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.6"}, "time": "2020-05-04T20:59:45+00:00"}, {"version": "0.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "956e7215c7c740d1226e7c03677140063918ec7d"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/956e7215c7c740d1226e7c03677140063918ec7d", "type": "zip", "shasum": "", "reference": "956e7215c7c740d1226e7c03677140063918ec7d"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/master"}, "time": "2020-05-02T07:04:38+00:00", "require-dev": {"consistence/coding-standard": "^3.5", "ergebnis/composer-normalize": "^2.0.2", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.20", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "symfony/process": "^4.0"}}, {"version": "0.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "d8d9d4645379e677466d407034436bb155b11c65"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/d8d9d4645379e677466d407034436bb155b11c65", "type": "zip", "shasum": "", "reference": "d8d9d4645379e677466d407034436bb155b11c65"}, "time": "2020-04-13T16:28:46+00:00", "require-dev": {"consistence/coding-standard": "^3.5", "ergebnis/composer-normalize": "^2.0.2", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.19", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "symfony/process": "^4.0"}}, {"version": "0.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "928179efc5368145a8b03cb20d58cb3f3136afae"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/928179efc5368145a8b03cb20d58cb3f3136afae", "type": "zip", "shasum": "", "reference": "928179efc5368145a8b03cb20d58cb3f3136afae"}, "time": "2020-01-25T20:42:48+00:00", "require-dev": {"consistence/coding-standard": "^3.5", "ergebnis/composer-normalize": "^2.0.2", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "symfony/process": "^4.0"}, "funding": "__unset"}, {"version": "0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "a6d13524641bb780efc821d9e0a1e1bfb23cbd0e"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/a6d13524641bb780efc821d9e0a1e1bfb23cbd0e", "type": "zip", "shasum": "", "reference": "a6d13524641bb780efc821d9e0a1e1bfb23cbd0e"}, "time": "2019-12-13T12:03:22+00:00", "require-dev": {"consistence/coding-standard": "^3.5", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "symfony/process": "^4.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-strict-rules": "^0.12"}}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "0331e880c0a1eb8e10d1af2fcab90fa1dd6a07ee"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/0331e880c0a1eb8e10d1af2fcab90fa1dd6a07ee", "type": "zip", "shasum": "", "reference": "0331e880c0a1eb8e10d1af2fcab90fa1dd6a07ee"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.1"}, "time": "2019-12-05T20:43:52+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "51a9bf6f5612a8a19f1e1e03385bd03cea62c7b1"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/51a9bf6f5612a8a19f1e1e03385bd03cea62c7b1", "type": "zip", "shasum": "", "reference": "51a9bf6f5612a8a19f1e1e03385bd03cea62c7b1"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.4.0"}, "time": "2019-11-21T20:52:51+00:00"}, {"version": "0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "8c4ef2aefd9788238897b678a985e1d5c8df6db4"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/8c4ef2aefd9788238897b678a985e1d5c8df6db4", "type": "zip", "shasum": "", "reference": "8c4ef2aefd9788238897b678a985e1d5c8df6db4"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/master"}, "time": "2019-06-07T19:13:52+00:00", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "require-dev": {"consistence/coding-standard": "^3.5", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/phpstan": "^0.10", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "squizlabs/php_codesniffer": "^3.3.2", "symfony/process": "^3.4 || ^4.0"}}, {"version": "0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "ab518a5fc8f1d90f58bd2c5552ba915e2c477b66"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/ab518a5fc8f1d90f58bd2c5552ba915e2c477b66", "type": "zip", "shasum": "", "reference": "ab518a5fc8f1d90f58bd2c5552ba915e2c477b66"}, "time": "2019-05-28T11:40:00+00:00"}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "472d3161d289f652713a5e353532fa4592663a57"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/472d3161d289f652713a5e353532fa4592663a57", "type": "zip", "shasum": "", "reference": "472d3161d289f652713a5e353532fa4592663a57"}, "time": "2019-04-23T20:26:19+00:00"}, {"version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "9558a70daa339b523042ed39053de5f27a53dec9"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9558a70daa339b523042ed39053de5f27a53dec9", "type": "zip", "shasum": "", "reference": "9558a70daa339b523042ed39053de5f27a53dec9"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/0.3.2"}, "time": "2019-04-23T19:35:00+00:00"}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "2cc49f47c69b023eaf05b48e6529389893b13d74"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/2cc49f47c69b023eaf05b48e6529389893b13d74", "type": "zip", "shasum": "", "reference": "2cc49f47c69b023eaf05b48e6529389893b13d74"}, "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/master"}, "time": "2019-01-14T12:26:23+00:00", "require-dev": {"consistence/coding-standard": "^2.0.0", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/phpstan": "^0.10", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^3.3.0", "symfony/process": "^3.4 || ^4.0"}}, {"version": "0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "ed3223362174b8067729930439e139794e9e514a"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/ed3223362174b8067729930439e139794e9e514a", "type": "zip", "shasum": "", "reference": "ed3223362174b8067729930439e139794e9e514a"}, "time": "2018-06-20T17:48:01+00:00", "require-dev": {"consistence/coding-standard": "^2.0.0", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/phpstan": "^0.10@dev", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^3.3.0", "symfony/process": "^3.4 || ^4.0"}}, {"version": "0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "02f909f134fe06f0cd4790d8627ee24efbe84d6a"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/02f909f134fe06f0cd4790d8627ee24efbe84d6a", "type": "zip", "shasum": "", "reference": "02f909f134fe06f0cd4790d8627ee24efbe84d6a"}, "time": "2018-01-13T18:19:41+00:00", "extra": {"branch-alias": {"dev-master": "0.1-dev"}}, "require": {"php": "~7.0"}, "require-dev": {"consistence/coding-standard": "^2.0.0", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/phpstan": "^0.9", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^3.3.0"}}, {"version": "0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpstan/phpdoc-parser.git", "type": "git", "reference": "08d714b2f0bc0a2bf9407255d5bb634669b7065c"}, "dist": {"url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/08d714b2f0bc0a2bf9407255d5bb634669b7065c", "type": "zip", "shasum": "", "reference": "08d714b2f0bc0a2bf9407255d5bb634669b7065c"}, "time": "2017-11-22T10:46:07+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Wed, 19 Feb 2025 13:45:59 GMT"}