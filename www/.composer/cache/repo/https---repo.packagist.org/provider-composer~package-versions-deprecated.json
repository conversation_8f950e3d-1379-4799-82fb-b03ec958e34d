{"minified": "composer/2.0", "packages": {"composer/package-versions-deprecated": [{"name": "composer/package-versions-deprecated", "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "keywords": [], "homepage": "", "version": "*********", "version_normalized": "*********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "type": "zip", "shasum": "", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "type": "composer-plugin", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00", "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "require": {"php": "^7 || ^8", "composer-plugin-api": "^1.1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^7", "composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13"}, "replace": {"ocramius/package-versions": "1.11.99"}}, {"version": "*********", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "b174585d1fe49ceed21928a945138948cb394600"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b174585d1fe49ceed21928a945138948cb394600", "type": "zip", "shasum": "", "reference": "b174585d1fe49ceed21928a945138948cb394600"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "time": "2021-09-13T08:41:34+00:00"}, {"version": "*********", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "fff576ac850c045158a250e7e27666e146e78d18"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/fff576ac850c045158a250e7e27666e146e78d18", "type": "zip", "shasum": "", "reference": "fff576ac850c045158a250e7e27666e146e78d18"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "time": "2021-08-17T13:49:14+00:00"}, {"version": "*********", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "c6522afe5540d5fc46675043d3ed5a45a740b27c"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/c6522afe5540d5fc46675043d3ed5a45a740b27c", "type": "zip", "shasum": "", "reference": "c6522afe5540d5fc46675043d3ed5a45a740b27c"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "time": "2021-05-24T07:46:03+00:00"}, {"version": "*********", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "7413f0b55a051e89485c5cb9f765fe24bb02a7b6"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/7413f0b55a051e89485c5cb9f765fe24bb02a7b6", "type": "zip", "shasum": "", "reference": "7413f0b55a051e89485c5cb9f765fe24bb02a7b6"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "time": "2020-11-11T10:22:58+00:00"}, {"version": "1.11.99", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "c8c9aa8a14cc3d3bec86d0a8c3fa52ea79936855"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/c8c9aa8a14cc3d3bec86d0a8c3fa52ea79936855", "type": "zip", "shasum": "", "reference": "c8c9aa8a14cc3d3bec86d0a8c3fa52ea79936855"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/master"}, "time": "2020-08-25T05:50:16+00:00"}, {"version": "*********", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "68c9b502036e820c33445ff4d174327f6bb87486"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/68c9b502036e820c33445ff4d174327f6bb87486", "type": "zip", "shasum": "", "reference": "68c9b502036e820c33445ff4d174327f6bb87486"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "time": "2020-08-13T12:55:41+00:00", "replace": {"ocramius/package-versions": "1.10.99"}}, {"version": "1.10.99", "version_normalized": "*********", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "dd51b4443d58b34b6d9344cf4c288e621c9a826f"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/dd51b4443d58b34b6d9344cf4c288e621c9a826f", "type": "zip", "shasum": "", "reference": "dd51b4443d58b34b6d9344cf4c288e621c9a826f"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/1.10.99"}, "time": "2020-07-15T08:39:18+00:00", "require": {"php": "^7", "composer-plugin-api": "^1.1.0 || ^2.0"}}, {"version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "7a8001fe2c9befad9d001bf54ef0b4a17d950d0f"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/7a8001fe2c9befad9d001bf54ef0b4a17d950d0f", "type": "zip", "shasum": "", "reference": "7a8001fe2c9befad9d001bf54ef0b4a17d950d0f"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/1.8.2"}, "time": "2020-07-10T14:10:26+00:00", "replace": {"ocramius/package-versions": "1.8.99"}}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "b9805885293f3957ee0dd42616ac6915c4ac9a4b"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b9805885293f3957ee0dd42616ac6915c4ac9a4b", "type": "zip", "shasum": "", "reference": "b9805885293f3957ee0dd42616ac6915c4ac9a4b"}, "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/1.8.1"}, "time": "2020-06-19T07:59:31+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/package-versions-deprecated.git", "type": "git", "reference": "98df7f1b293c0550bd5b1ce6b60b59bdda23aa47"}, "dist": {"url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/98df7f1b293c0550bd5b1ce6b60b59bdda23aa47", "type": "zip", "shasum": "", "reference": "98df7f1b293c0550bd5b1ce6b60b59bdda23aa47"}, "support": {"source": "https://github.com/composer/package-versions-deprecated/tree/1.8.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-04-23T11:49:37+00:00", "replace": {"ocramius/package-versions": "1.2 - 1.8.99"}}]}, "security-advisories": [], "last-modified": "Thu, 04 Apr 2024 18:48:33 GMT"}