{"minified": "composer/2.0", "packages": {"fruitcake/php-cors": [{"name": "fruitcake/php-cors", "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "keywords": ["symfony", "cors", "laravel"], "homepage": "https://github.com/fruitcake/php-cors", "version": "v1.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b", "type": "zip", "shasum": "", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "type": "library", "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-10-12T05:21:21+00:00", "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5", "phpstan/phpstan": "^1.4"}}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "58571acbaa5f9f462c9c77e911700ac66f446d4e"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/58571acbaa5f9f462c9c77e911700ac66f446d4e", "type": "zip", "shasum": "", "reference": "58571acbaa5f9f462c9c77e911700ac66f446d4e"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.2.0"}, "time": "2022-02-20T15:07:15+00:00", "extra": {"branch-alias": {"dev-main": "1.1-dev"}}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6"}}, {"version": "v1.1.0", "version_normalized": "*******", "authors": [{"name": "Barryvdh", "email": "<EMAIL>"}], "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "507d1de41251b5c22eaf361df3e362b84da8d83e"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/507d1de41251b5c22eaf361df3e362b84da8d83e", "type": "zip", "shasum": "", "reference": "507d1de41251b5c22eaf361df3e362b84da8d83e"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.1.0"}, "time": "2022-02-20T11:19:41+00:00", "require": {"php": "^7.2|^8.0", "symfony/http-foundation": "^4|^5|^6"}, "require-dev": {"phpunit/phpunit": "^7|^9", "squizlabs/php_codesniffer": "^3.5", "phpstan/phpstan": "^1.4"}}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "07611b4d603a72f097aeeeeb1e67d5ae7a6d0798"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/07611b4d603a72f097aeeeeb1e67d5ae7a6d0798", "type": "zip", "shasum": "", "reference": "07611b4d603a72f097aeeeeb1e67d5ae7a6d0798"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.0.1"}, "time": "2022-02-19T16:21:54+00:00", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "a9c5d1e86391b17f2cedcdf5ea2c7580c38b9c92"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/a9c5d1e86391b17f2cedcdf5ea2c7580c38b9c92", "type": "zip", "shasum": "", "reference": "a9c5d1e86391b17f2cedcdf5ea2c7580c38b9c92"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.0.0"}, "time": "2022-02-19T14:14:57+00:00"}, {"version": "v1.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "006045966c440300e6f7fa37c0f040ec70053859"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/006045966c440300e6f7fa37c0f040ec70053859", "type": "zip", "shasum": "", "reference": "006045966c440300e6f7fa37c0f040ec70053859"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.0-beta1"}, "funding": [], "time": "2022-02-19T13:29:22+00:00"}, {"version": "v1.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "125c6d1903bcdcbf60875607974930d0c70a2d93"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/125c6d1903bcdcbf60875607974930d0c70a2d93", "type": "zip", "shasum": "", "reference": "125c6d1903bcdcbf60875607974930d0c70a2d93"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.0-alpha2"}, "time": "2022-02-19T11:07:24+00:00"}, {"version": "v1.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "8b9a8c1b01ece9bd81f33d7e697dc0951e4dca58"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/8b9a8c1b01ece9bd81f33d7e697dc0951e4dca58", "type": "zip", "shasum": "", "reference": "8b9a8c1b01ece9bd81f33d7e697dc0951e4dca58"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.0-alpha1"}, "time": "2022-02-18T19:42:56+00:00", "require-dev": {"phpunit/phpunit": "^7|^9", "squizlabs/php_codesniffer": "^3.5"}}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "bb56f94ba331b09e5b5d4b76332a2ab54c9f7083"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/bb56f94ba331b09e5b5d4b76332a2ab54c9f7083", "type": "zip", "shasum": "", "reference": "bb56f94ba331b09e5b5d4b76332a2ab54c9f7083"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v0.1.2"}, "time": "2022-02-19T11:01:01+00:00", "require-dev": {"phpunit/phpunit": "^7|^9", "squizlabs/php_codesniffer": "^3.5", "phpstan/phpstan": "^1.4"}}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "277ac8de1b1965250ddbe98b1788ee43afd038e0"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/277ac8de1b1965250ddbe98b1788ee43afd038e0", "type": "zip", "shasum": "", "reference": "277ac8de1b1965250ddbe98b1788ee43afd038e0"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v0.1.1"}, "time": "2022-02-19T10:32:13+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/fruitcake/php-cors.git", "type": "git", "reference": "46d32c99be9402902fcdb8ee447c8284b0057ec0"}, "dist": {"url": "https://api.github.com/repos/fruitcake/php-cors/zipball/46d32c99be9402902fcdb8ee447c8284b0057ec0", "type": "zip", "shasum": "", "reference": "46d32c99be9402902fcdb8ee447c8284b0057ec0"}, "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v0.1.0"}, "time": "2022-02-18T20:05:43+00:00", "require-dev": {"phpunit/phpunit": "^7|^9", "squizlabs/php_codesniffer": "^3.5"}}]}, "security-advisories": [], "last-modified": "Tu<PERSON>, 09 Apr 2024 08:00:13 GMT"}