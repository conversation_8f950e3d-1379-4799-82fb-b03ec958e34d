{"minified": "composer/2.0", "packages": {"spiral/roadrunner-worker": [{"name": "spiral/roadrunner-worker", "description": "RoadRunner: PHP worker", "keywords": [], "homepage": "https://spiral.dev/", "version": "v3.6.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "8d9905b1e6677f34ff8623893f35b5e2fa828e37"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/8d9905b1e6677f34ff8623893f35b5e2fa828e37", "type": "zip", "shasum": "", "reference": "8d9905b1e6677f34ff8623893f35b5e2fa828e37"}, "type": "library", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/v3.6.2"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-05T12:34:50+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\": "src"}}, "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "psr/log": "^2.0 || ^3.0", "spiral/goridge": "^4.1.0", "spiral/roadrunner": "^2023.1 || ^2024.1 || ^2025.1", "composer-runtime-api": "^2.0"}, "require-dev": {"buggregator/trap": "^1.13", "jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.5.45", "spiral/code-style": "^2.2", "vimeo/psalm": "^6.0"}, "suggest": {"spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools"}}, {"version": "v3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "dd0571a84b432077447ece947e5f2b19edde574f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/dd0571a84b432077447ece947e5f2b19edde574f", "type": "zip", "shasum": "", "reference": "dd0571a84b432077447ece947e5f2b19edde574f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/v3.6.1"}, "time": "2024-11-23T08:32:13+00:00", "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "psr/log": "^2.0 || ^3.0", "spiral/goridge": "^4.1.0", "spiral/roadrunner": "^2023.1 || ^2024.1", "composer-runtime-api": "^2.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.9", "symfony/var-dumper": "^6.3 || ^7.0"}}, {"version": "v3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "44c6f37c6abc25175c2723bd6daaa17651b18036"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/44c6f37c6abc25175c2723bd6daaa17651b18036", "type": "zip", "shasum": "", "reference": "44c6f37c6abc25175c2723bd6daaa17651b18036"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/v3.6.0"}, "time": "2024-06-03T15:30:19+00:00"}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "7c7411a301b9dade863634ab17ef01929adada6b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/7c7411a301b9dade863634ab17ef01929adada6b", "type": "zip", "shasum": "", "reference": "7c7411a301b9dade863634ab17ef01929adada6b"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/v3.5.0"}, "time": "2024-03-29T08:06:36+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "8efc721a3ced27fd6ef6bca347a69010ff7930cb"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/8efc721a3ced27fd6ef6bca347a69010ff7930cb", "type": "zip", "shasum": "", "reference": "8efc721a3ced27fd6ef6bca347a69010ff7930cb"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/v3.4.0"}, "time": "2024-02-26T11:59:06+00:00", "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "psr/log": "^2.0|^3.0", "spiral/goridge": "^4.1.0", "spiral/roadrunner": "^2023.1", "composer-runtime-api": "^2.0"}}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "53f81c3e138a650c4605fcd60cae7c89ef24061f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/53f81c3e138a650c4605fcd60cae7c89ef24061f", "type": "zip", "shasum": "", "reference": "53f81c3e138a650c4605fcd60cae7c89ef24061f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/3.3.0"}, "time": "2023-12-05T19:53:37+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "88104bfc20c0e8fd90535259fcac69aedec98871"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/88104bfc20c0e8fd90535259fcac69aedec98871", "type": "zip", "shasum": "", "reference": "88104bfc20c0e8fd90535259fcac69aedec98871"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/3.2.0"}, "time": "2023-10-10T10:14:17+00:00", "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.9", "symfony/var-dumper": "^6.3"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "5c22fabc3081399e760fa44082e5a0887ff94f04"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/5c22fabc3081399e760fa44082e5a0887ff94f04", "type": "zip", "shasum": "", "reference": "5c22fabc3081399e760fa44082e5a0887ff94f04"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/3.1.0"}, "time": "2023-10-03T19:14:20+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/worker.git", "type": "git", "reference": "97f966a6685809c7fa024b022f953bd1ae3b6135"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/worker/zipball/97f966a6685809c7fa024b022f953bd1ae3b6135", "type": "zip", "shasum": "", "reference": "97f966a6685809c7fa024b022f953bd1ae3b6135"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/3.0.0"}, "time": "2023-04-12T11:16:47+00:00", "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "psr/log": "^2.0|^3.0", "spiral/goridge": "^4.0", "spiral/roadrunner": "^2023.1", "composer-runtime-api": "^2.0"}}, {"homepage": "", "version": "v2.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "028adbbd43566b264fa3e3edb47cfbc3fb2cd11e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/028adbbd43566b264fa3e3edb47cfbc3fb2cd11e", "type": "zip", "shasum": "", "reference": "028adbbd43566b264fa3e3edb47cfbc3fb2cd11e"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.3.0"}, "funding": [], "time": "2022-10-28T09:36:29+00:00", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*", "psr/log": "^1.0|^2.0|^3.0", "spiral/goridge": "^3.2.0", "symfony/polyfill-php80": "^1.23", "composer-runtime-api": "^2.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "vimeo/psalm": "^4.4", "symfony/var-dumper": "^5.1"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "97399e1f45b188e4288817672df858908b641401"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/97399e1f45b188e4288817672df858908b641401", "type": "zip", "shasum": "", "reference": "97399e1f45b188e4288817672df858908b641401"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.2.0"}, "time": "2022-03-22T11:03:47+00:00"}, {"version": "v2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "2247e374736506f5cf32295e15bee74b8e582031"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/2247e374736506f5cf32295e15bee74b8e582031", "type": "zip", "shasum": "", "reference": "2247e374736506f5cf32295e15bee74b8e582031"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.1.5"}, "time": "2021-11-30T08:54:52+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*", "psr/log": "^1.0|^2.0|^3.0", "spiral/goridge": "^3.0", "symfony/polyfill-php80": "^1.23", "composer-runtime-api": "^2.0"}}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "a127bc8f06faeb0ef7d09c5ecfee78bab54b4d8c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/a127bc8f06faeb0ef7d09c5ecfee78bab54b4d8c", "type": "zip", "shasum": "", "reference": "a127bc8f06faeb0ef7d09c5ecfee78bab54b4d8c"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.1.4"}, "time": "2021-11-16T10:40:29+00:00"}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "210cbb1318edb3eb7319f5bbd86ba3faaa385207"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/210cbb1318edb3eb7319f5bbd86ba3faaa385207", "type": "zip", "shasum": "", "reference": "210cbb1318edb3eb7319f5bbd86ba3faaa385207"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.1.3"}, "time": "2021-10-04T15:41:30+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "99ef87da636ea81bca53ff583172d6fb7f684473"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/99ef87da636ea81bca53ff583172d6fb7f684473", "type": "zip", "shasum": "", "reference": "99ef87da636ea81bca53ff583172d6fb7f684473"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.1.2"}, "time": "2021-10-01T08:41:33+00:00", "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*", "psr/log": "^1.0", "spiral/goridge": "^3.0", "composer-runtime-api": "^2.0"}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "08db1f0f6fe9aa012684206cfd9e9f48c3c926cd"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/08db1f0f6fe9aa012684206cfd9e9f48c3c926cd", "type": "zip", "shasum": "", "reference": "08db1f0f6fe9aa012684206cfd9e9f48c3c926cd"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.1.1"}, "time": "2021-09-09T20:05:15+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "5fb0deff302198ff9f2935e20889d63702cc9f0e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/5fb0deff302198ff9f2935e20889d63702cc9f0e", "type": "zip", "shasum": "", "reference": "5fb0deff302198ff9f2935e20889d63702cc9f0e"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.1.0"}, "time": "2021-08-20T13:15:09+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "1a10a7641ff045fff2570b5ec1a265713e5a1bd5"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/1a10a7641ff045fff2570b5ec1a265713e5a1bd5", "type": "zip", "shasum": "", "reference": "1a10a7641ff045fff2570b5ec1a265713e5a1bd5"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.0.3"}, "time": "2021-02-19T13:15:37+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "67c19979f2b27a9bc9944accea3c206ea57d7af5"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/67c19979f2b27a9bc9944accea3c206ea57d7af5", "type": "zip", "shasum": "", "reference": "67c19979f2b27a9bc9944accea3c206ea57d7af5"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.0.2"}, "time": "2021-02-18T11:08:38+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "a900d1d5137e0035801c5d768c86d93d3921023d"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/a900d1d5137e0035801c5d768c86d93d3921023d", "type": "zip", "shasum": "", "reference": "a900d1d5137e0035801c5d768c86d93d3921023d"}, "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.0.1"}, "time": "2021-02-01T09:23:15+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "bin": ["bin/rr"], "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*", "symfony/console": "^5.1", "spiral/goridge": ">=3.0", "symfony/http-client": "^5.1", "symfony/polyfill-php80": "^1.22", "composer/semver": "^3.2", "composer-runtime-api": "^2.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "phpstan/phpstan": "~0.12", "symfony/var-dumper": "^5.1"}, "suggest": "__unset"}, {"version": "v2.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/spiral/roadrunner-worker.git", "type": "git", "reference": "73f5f88e6cce987aaa37ae32751ce0f60b47a135"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-worker/zipball/73f5f88e6cce987aaa37ae32751ce0f60b47a135", "type": "zip", "shasum": "", "reference": "73f5f88e6cce987aaa37ae32751ce0f60b47a135"}, "type": "server", "support": {"issues": "https://github.com/spiral/roadrunner-worker/issues", "source": "https://github.com/spiral/roadrunner-worker/tree/v2.0.0"}, "time": "2021-03-10T14:52:21+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\": "src/"}}, "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*", "spiral/goridge": ">=3.0", "composer-runtime-api": "^2.0"}, "require-dev": {"phpstan/phpstan": "~0.12"}, "extra": "__unset", "bin": "__unset"}]}, "security-advisories": [], "last-modified": "Mon, 05 May 2025 12:36:06 GMT"}