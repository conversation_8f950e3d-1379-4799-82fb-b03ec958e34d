{"minified": "composer/2.0", "packages": {"amphp/parser": [{"name": "amphp/parser", "description": "A generator parser to make streaming parsers simple.", "keywords": ["stream", "parser", "async", "non-blocking"], "homepage": "https://github.com/amphp/parser", "version": "v1.1.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/parser.git", "type": "git", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7"}, "dist": {"url": "https://api.github.com/repos/amphp/parser/zipball/3cf1f8b32a0171d4b1bed93d25617637a77cded7", "type": "zip", "shasum": "", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7"}, "type": "library", "support": {"issues": "https://github.com/amphp/parser/issues", "source": "https://github.com/amphp/parser/tree/v1.1.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T19:16:53+00:00", "autoload": {"psr-4": {"Amp\\Parser\\": "src"}}, "require": {"php": ">=7.4"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.4"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parser.git", "type": "git", "reference": "ff1de4144726c5dad5fab97f66692ebe8de3e151"}, "dist": {"url": "https://api.github.com/repos/amphp/parser/zipball/ff1de4144726c5dad5fab97f66692ebe8de3e151", "type": "zip", "shasum": "", "reference": "ff1de4144726c5dad5fab97f66692ebe8de3e151"}, "support": {"issues": "https://github.com/amphp/parser/issues", "source": "https://github.com/amphp/parser/tree/v1.1.0"}, "time": "2022-12-30T18:08:47+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/parser.git", "type": "git", "reference": "f83e68f03d5b8e8e0365b8792985a7f341c57ae1"}, "dist": {"url": "https://api.github.com/repos/amphp/parser/zipball/f83e68f03d5b8e8e0365b8792985a7f341c57ae1", "type": "zip", "shasum": "", "reference": "f83e68f03d5b8e8e0365b8792985a7f341c57ae1"}, "support": {"issues": "https://github.com/amphp/parser/issues", "source": "https://github.com/amphp/parser/tree/is-valid"}, "time": "2017-06-06T05:29:10+00:00", "autoload": {"psr-4": {"Amp\\Parser\\": "lib"}}, "require": {"php": ">=7"}, "require-dev": {"phpunit/phpunit": "^6", "friendsofphp/php-cs-fixer": "^2.3"}, "funding": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 21 Mar 2024 19:18:31 GMT"}