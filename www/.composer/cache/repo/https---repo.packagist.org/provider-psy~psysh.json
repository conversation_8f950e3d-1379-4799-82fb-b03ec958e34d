{"minified": "composer/2.0", "packages": {"psy/psysh": [{"name": "psy/psysh", "description": "An interactive shell for modern PHP.", "keywords": ["console", "shell", "interactive", "REPL"], "homepage": "http://psysh.org", "version": "v0.12.9", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1b801844becfe648985372cb4b12ad6840245ace"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1b801844becfe648985372cb4b12ad6840245ace", "type": "zip", "shasum": "", "reference": "1b801844becfe648985372cb4b12ad6840245ace"}, "type": "library", "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.9"}, "funding": [], "time": "2025-06-23T02:35:06+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "bin": ["bin/psysh"], "require": {"php": "^8.0 || ^7.4", "ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-pdo-sqlite": "The doc command requires SQLite to work."}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}}, {"version": "v0.12.8", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/85057ceedee50c49d4f6ecaff73ee96adb3b3625", "type": "zip", "shasum": "", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.8"}, "time": "2025-03-16T03:05:19+00:00"}, {"version": "v0.12.7", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "d73fa3c74918ef4522bb8a3bf9cab39161c4b57c"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/d73fa3c74918ef4522bb8a3bf9cab39161c4b57c", "type": "zip", "shasum": "", "reference": "d73fa3c74918ef4522bb8a3bf9cab39161c4b57c"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.7"}, "time": "2024-12-10T01:58:33+00:00"}, {"version": "v0.12.6", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "3b5ea0efaa791cd1c65ecc493aec3e2aa55ff57c"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/3b5ea0efaa791cd1c65ecc493aec3e2aa55ff57c", "type": "zip", "shasum": "", "reference": "3b5ea0efaa791cd1c65ecc493aec3e2aa55ff57c"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.6"}, "time": "2024-12-07T20:08:52+00:00"}, {"version": "v0.12.5", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "36a03ff27986682c22985e56aabaf840dd173cb5"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/36a03ff27986682c22985e56aabaf840dd173cb5", "type": "zip", "shasum": "", "reference": "36a03ff27986682c22985e56aabaf840dd173cb5"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.5"}, "time": "2024-11-29T06:14:30+00:00"}, {"version": "v0.12.4", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "2fd717afa05341b4f8152547f142cd2f130f6818"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/2fd717afa05341b4f8152547f142cd2f130f6818", "type": "zip", "shasum": "", "reference": "2fd717afa05341b4f8152547f142cd2f130f6818"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.4"}, "time": "2024-06-10T01:18:23+00:00"}, {"version": "v0.12.3", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73", "type": "zip", "shasum": "", "reference": "b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.3"}, "time": "2024-04-02T15:57:53+00:00"}, {"version": "v0.12.2", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "9185c66c2165bbf4d71de78a69dccf4974f9538d"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/9185c66c2165bbf4d71de78a69dccf4974f9538d", "type": "zip", "shasum": "", "reference": "9185c66c2165bbf4d71de78a69dccf4974f9538d"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.2"}, "time": "2024-03-17T01:53:00+00:00"}, {"version": "v0.12.1", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "39621c73e0754328252f032c6997b983afc50431"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/39621c73e0754328252f032c6997b983afc50431", "type": "zip", "shasum": "", "reference": "39621c73e0754328252f032c6997b983afc50431"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.1"}, "time": "2024-03-15T03:22:57+00:00"}, {"version": "v0.12.0", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "750bf031a48fd07c673dbe3f11f72362ea306d0d"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/750bf031a48fd07c673dbe3f11f72362ea306d0d", "type": "zip", "shasum": "", "reference": "750bf031a48fd07c673dbe3f11f72362ea306d0d"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.0"}, "time": "2023-12-20T15:28:09+00:00"}, {"version": "v0.11.22", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "128fa1b608be651999ed9789c95e6e2a31b5802b"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/128fa1b608be651999ed9789c95e6e2a31b5802b", "type": "zip", "shasum": "", "reference": "128fa1b608be651999ed9789c95e6e2a31b5802b"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.22"}, "time": "2023-10-14T21:56:36+00:00", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-0.11": "0.11.x-dev"}}, "require": {"php": "^8.0 || ^7.0.8", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^6.0 || ^5.0 || ^4.0 || ^3.4", "nikic/php-parser": "^4.0 || ^3.1"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "ext-pdo-sqlite": "The doc command requires SQLite to work."}}, {"version": "v0.11.21", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "bcb22101107f3bf770523b65630c9d547f60c540"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/bcb22101107f3bf770523b65630c9d547f60c540", "type": "zip", "shasum": "", "reference": "bcb22101107f3bf770523b65630c9d547f60c540"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.21"}, "time": "2023-09-17T21:15:54+00:00", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.11.x-dev"}}}, {"version": "v0.11.20", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "0fa27040553d1d280a67a4393194df5228afea5b"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/0fa27040553d1d280a67a4393194df5228afea5b", "type": "zip", "shasum": "", "reference": "0fa27040553d1d280a67a4393194df5228afea5b"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.20"}, "time": "2023-07-31T14:32:22+00:00", "extra": {"branch-alias": {"dev-main": "0.11.x-dev"}}}, {"version": "v0.11.19", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1724ceff278daeeac5a006744633bacbb2dc4706"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1724ceff278daeeac5a006744633bacbb2dc4706", "type": "zip", "shasum": "", "reference": "1724ceff278daeeac5a006744633bacbb2dc4706"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.19"}, "time": "2023-07-15T19:42:19+00:00"}, {"version": "v0.11.18", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4f00ee9e236fa6a48f4560d1300b9c961a70a7ec"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4f00ee9e236fa6a48f4560d1300b9c961a70a7ec", "type": "zip", "shasum": "", "reference": "4f00ee9e236fa6a48f4560d1300b9c961a70a7ec"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.18"}, "time": "2023-05-23T02:31:11+00:00"}, {"version": "v0.11.17", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "3dc5d4018dabd80bceb8fe1e3191ba8460569f0a"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/3dc5d4018dabd80bceb8fe1e3191ba8460569f0a", "type": "zip", "shasum": "", "reference": "3dc5d4018dabd80bceb8fe1e3191ba8460569f0a"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.17"}, "time": "2023-05-05T20:02:42+00:00"}, {"version": "v0.11.16", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "151b145906804eea8e5d71fea23bfb470c904bfb"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/151b145906804eea8e5d71fea23bfb470c904bfb", "type": "zip", "shasum": "", "reference": "151b145906804eea8e5d71fea23bfb470c904bfb"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.16"}, "time": "2023-04-26T12:53:57+00:00"}, {"version": "v0.11.15", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "5350ce0ec8ecf2c5b5cf554cd2496f97b444af85"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/5350ce0ec8ecf2c5b5cf554cd2496f97b444af85", "type": "zip", "shasum": "", "reference": "5350ce0ec8ecf2c5b5cf554cd2496f97b444af85"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.15"}, "time": "2023-04-07T21:57:09+00:00"}, {"version": "v0.11.14", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "8c2e264def7a8263a68ef6f0b55ce90b77d41e17"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/8c2e264def7a8263a68ef6f0b55ce90b77d41e17", "type": "zip", "shasum": "", "reference": "8c2e264def7a8263a68ef6f0b55ce90b77d41e17"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.14"}, "time": "2023-03-28T03:41:01+00:00"}, {"version": "v0.11.13", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "722317c9f5627e588788e340f29b923e58f92f54"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/722317c9f5627e588788e340f29b923e58f92f54", "type": "zip", "shasum": "", "reference": "722317c9f5627e588788e340f29b923e58f92f54"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.13"}, "time": "2023-03-21T14:22:44+00:00"}, {"version": "v0.11.12", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "52cb7c47d403c31c0adc9bf7710fc355f93c20f7"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/52cb7c47d403c31c0adc9bf7710fc355f93c20f7", "type": "zip", "shasum": "", "reference": "52cb7c47d403c31c0adc9bf7710fc355f93c20f7"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.12"}, "time": "2023-01-29T21:24:40+00:00"}, {"version": "v0.11.11", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "ba67f2d26278ec9266a5cfe0acba33a8ca1277ae"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/ba67f2d26278ec9266a5cfe0acba33a8ca1277ae", "type": "zip", "shasum": "", "reference": "ba67f2d26278ec9266a5cfe0acba33a8ca1277ae"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.11"}, "time": "2023-01-23T16:14:59+00:00"}, {"version": "v0.11.10", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "e9eadffbed9c9deb5426fd107faae0452bf20a36"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/e9eadffbed9c9deb5426fd107faae0452bf20a36", "type": "zip", "shasum": "", "reference": "e9eadffbed9c9deb5426fd107faae0452bf20a36"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.10"}, "time": "2022-12-23T17:47:18+00:00"}, {"version": "v0.11.9", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1acec99d6684a54ff92f8b548a4e41b566963778"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1acec99d6684a54ff92f8b548a4e41b566963778", "type": "zip", "shasum": "", "reference": "1acec99d6684a54ff92f8b548a4e41b566963778"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.9"}, "time": "2022-11-06T15:29:46+00:00"}, {"version": "v0.11.8", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "f455acf3645262ae389b10e9beba0c358aa6994e"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/f455acf3645262ae389b10e9beba0c358aa6994e", "type": "zip", "shasum": "", "reference": "f455acf3645262ae389b10e9beba0c358aa6994e"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.8"}, "time": "2022-07-28T14:25:11+00:00"}, {"version": "v0.11.7", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "77fc7270031fbc28f9a7bea31385da5c4855cb7a"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/77fc7270031fbc28f9a7bea31385da5c4855cb7a", "type": "zip", "shasum": "", "reference": "77fc7270031fbc28f9a7bea31385da5c4855cb7a"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.7"}, "time": "2022-07-07T13:49:11+00:00"}, {"version": "v0.11.6", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "3f5b5f8aaa979fbd0d1783173f4c82ad529fe621"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/3f5b5f8aaa979fbd0d1783173f4c82ad529fe621", "type": "zip", "shasum": "", "reference": "3f5b5f8aaa979fbd0d1783173f4c82ad529fe621"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.6"}, "time": "2022-07-03T16:40:23+00:00"}, {"version": "v0.11.5", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "c23686f9c48ca202710dbb967df8385a952a2daf"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/c23686f9c48ca202710dbb967df8385a952a2daf", "type": "zip", "shasum": "", "reference": "c23686f9c48ca202710dbb967df8385a952a2daf"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.5"}, "time": "2022-05-27T18:03:49+00:00"}, {"version": "v0.11.4", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "05c544b339b112226ad14803e1e5b09a61957454"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/05c544b339b112226ad14803e1e5b09a61957454", "type": "zip", "shasum": "", "reference": "05c544b339b112226ad14803e1e5b09a61957454"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.4"}, "time": "2022-05-06T12:49:14+00:00"}, {"version": "v0.11.3", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "6833626ee48ef9bcc8aca8f9f166760441c12573"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/6833626ee48ef9bcc8aca8f9f166760441c12573", "type": "zip", "shasum": "", "reference": "6833626ee48ef9bcc8aca8f9f166760441c12573"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.3"}, "time": "2022-05-05T02:19:43+00:00"}, {"version": "v0.11.2", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "7f7da640d68b9c9fec819caae7c744a213df6514"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/7f7da640d68b9c9fec819caae7c744a213df6514", "type": "zip", "shasum": "", "reference": "7f7da640d68b9c9fec819caae7c744a213df6514"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.2"}, "time": "2022-02-28T15:28:54+00:00", "require-dev": {"hoa/console": "**********", "bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}}, {"version": "v0.11.1", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "570292577277f06f590635381a7f761a6cf4f026"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/570292577277f06f590635381a7f761a6cf4f026", "type": "zip", "shasum": "", "reference": "570292577277f06f590635381a7f761a6cf4f026"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.1"}, "time": "2022-01-03T13:58:38+00:00", "conflict": "__unset"}, {"version": "v0.11.0", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "c9a85cd388afde68721d304bbb3257a068f5ab05"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/c9a85cd388afde68721d304bbb3257a068f5ab05", "type": "zip", "shasum": "", "reference": "c9a85cd388afde68721d304bbb3257a068f5ab05"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.0"}, "time": "2021-12-05T06:09:05+00:00"}, {"version": "v0.10.12", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "a0d9981aa07ecfcbea28e4bfa868031cca121e7d"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/a0d9981aa07ecfcbea28e4bfa868031cca121e7d", "type": "zip", "shasum": "", "reference": "a0d9981aa07ecfcbea28e4bfa868031cca121e7d"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.12"}, "time": "2021-11-30T14:05:36+00:00", "extra": {"branch-alias": {"dev-main": "0.10.x-dev"}}, "require": {"php": "^8.0 || ^7.0 || ^5.5.9", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3"}, "require-dev": {"hoa/console": "3.17.*", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "v0.10.11", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "38017532bba35d15d28dcc001b4274df0251c4a1"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/38017532bba35d15d28dcc001b4274df0251c4a1", "type": "zip", "shasum": "", "reference": "38017532bba35d15d28dcc001b4274df0251c4a1"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.11"}, "time": "2021-11-23T15:02:17+00:00"}, {"version": "v0.10.10", "version_normalized": "*********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "31ca69c825c228f0e35c049c71ff722020ef9c58"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/31ca69c825c228f0e35c049c71ff722020ef9c58", "type": "zip", "shasum": "", "reference": "31ca69c825c228f0e35c049c71ff722020ef9c58"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.10"}, "time": "2021-11-23T14:53:14+00:00"}, {"version": "v0.10.9", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "01281336c4ae557fe4a994544f30d3a1bc204375"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/01281336c4ae557fe4a994544f30d3a1bc204375", "type": "zip", "shasum": "", "reference": "01281336c4ae557fe4a994544f30d3a1bc204375"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.9"}, "time": "2021-10-10T13:37:39+00:00"}, {"version": "v0.10.8", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "e4573f47750dd6c92dca5aee543fa77513cbd8d3"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/e4573f47750dd6c92dca5aee543fa77513cbd8d3", "type": "zip", "shasum": "", "reference": "e4573f47750dd6c92dca5aee543fa77513cbd8d3"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.8"}, "time": "2021-04-10T16:23:39+00:00"}, {"version": "v0.10.7", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "a395af46999a12006213c0c8346c9445eb31640c"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/a395af46999a12006213c0c8346c9445eb31640c", "type": "zip", "shasum": "", "reference": "a395af46999a12006213c0c8346c9445eb31640c"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.7"}, "time": "2021-03-14T02:14:56+00:00", "require": {"php": "^8.0 || ^7.0 || ^5.5.9", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "dnoegel/php-xdg-base-dir": "0.1.*"}}, {"version": "v0.10.6", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "6f990c19f91729de8b31e639d6e204ea59f19cf3"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/6f990c19f91729de8b31e639d6e204ea59f19cf3", "type": "zip", "shasum": "", "reference": "6f990c19f91729de8b31e639d6e204ea59f19cf3"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.6"}, "time": "2021-01-18T15:53:43+00:00"}, {"version": "v0.10.5", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "7c710551d4a2653afa259c544508dc18a9098956"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/7c710551d4a2653afa259c544508dc18a9098956", "type": "zip", "shasum": "", "reference": "7c710551d4a2653afa259c544508dc18a9098956"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.5"}, "time": "2020-12-04T02:51:30+00:00", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}}, {"version": "v0.10.4", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/a8aec1b2981ab66882a01cce36a49b6317dc3560", "type": "zip", "shasum": "", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2020-05-03T19:32:03+00:00"}, {"version": "v0.10.3", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "2bde2fa03e05dff0aee834598b951d6fc7c6fe02"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/2bde2fa03e05dff0aee834598b951d6fc7c6fe02", "type": "zip", "shasum": "", "reference": "2bde2fa03e05dff0aee834598b951d6fc7c6fe02"}, "time": "2020-04-07T06:44:48+00:00"}, {"version": "v0.10.2", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "573c2362c3cdebe846b4adae4b630eecb350afd8"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/573c2362c3cdebe846b4adae4b630eecb350afd8", "type": "zip", "shasum": "", "reference": "573c2362c3cdebe846b4adae4b630eecb350afd8"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.2"}, "time": "2020-03-21T06:55:27+00:00", "require": {"php": "^8.0 || ^7.0 || ^5.5.9", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "dnoegel/php-xdg-base-dir": "0.1.*", "jakub-onderka/php-console-highlighter": "0.4.*|0.3.*"}, "require-dev": {"hoa/console": "~3.16|~2.15", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "v0.10.0", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "e361c8b7e5114534078e0ce04ddc442b39018a3c"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/e361c8b7e5114534078e0ce04ddc442b39018a3c", "type": "zip", "shasum": "", "reference": "e361c8b7e5114534078e0ce04ddc442b39018a3c"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.0"}, "time": "2020-03-15T22:52:37+00:00"}, {"version": "v0.9.12", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "90da7f37568aee36b116a030c5f99c915267edd4"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/90da7f37568aee36b116a030c5f99c915267edd4", "type": "zip", "shasum": "", "reference": "90da7f37568aee36b116a030c5f99c915267edd4"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.9.12"}, "time": "2019-12-06T14:19:43+00:00", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}, "require": {"php": ">=5.4.0", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1.*", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*"}, "require-dev": {"phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0", "hoa/console": "~2.15|~3.16", "bamarni/composer-bin-plugin": "^1.2"}, "funding": "__unset"}, {"version": "v0.9.11", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "75d9ac1c16db676de27ab554a4152b594be4748e"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/75d9ac1c16db676de27ab554a4152b594be4748e", "type": "zip", "shasum": "", "reference": "75d9ac1c16db676de27ab554a4152b594be4748e"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.9.11"}, "time": "2019-11-27T22:44:29+00:00", "require": {"php": ">=5.4.0", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*"}}, {"version": "v0.9.9", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "9aaf29575bb8293206bb0420c1e1c87ff2ffa94e"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/9aaf29575bb8293206bb0420c1e1c87ff2ffa94e", "type": "zip", "shasum": "", "reference": "9aaf29575bb8293206bb0420c1e1c87ff2ffa94e"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2018-10-13T15:16:03+00:00", "require": {"php": ">=5.4.0", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0", "symfony/var-dumper": "~2.7|~3.0|~4.0", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*"}}, {"version": "v0.9.8", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "ed3c32c4304e1a678a6e0f9dc11dd2d927d89555"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/ed3c32c4304e1a678a6e0f9dc11dd2d927d89555", "type": "zip", "shasum": "", "reference": "ed3c32c4304e1a678a6e0f9dc11dd2d927d89555"}, "time": "2018-09-05T11:40:09+00:00", "require": {"php": ">=5.4.0", "ext-json": "*", "ext-tokenizer": "*", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0", "symfony/var-dumper": "~2.7|~3.0|~4.0", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}}, {"version": "v0.9.7", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4f5b6c090948773a8bfeea6a0f07ab7d0b24e932"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4f5b6c090948773a8bfeea6a0f07ab7d0b24e932", "type": "zip", "shasum": "", "reference": "4f5b6c090948773a8bfeea6a0f07ab7d0b24e932"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/develop"}, "time": "2018-08-11T15:54:43+00:00"}, {"version": "v0.9.6", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4a2ce86f199d51b6e2524214dc06835e872f4fce"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4a2ce86f199d51b6e2524214dc06835e872f4fce", "type": "zip", "shasum": "", "reference": "4a2ce86f199d51b6e2524214dc06835e872f4fce"}, "time": "2018-06-10T17:57:20+00:00", "require": {"php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0", "symfony/var-dumper": "~2.7|~3.0|~4.0", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}}, {"version": "v0.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "0951e91ac04ca28cf317f3997a0adfc319e80106"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/0951e91ac04ca28cf317f3997a0adfc319e80106", "type": "zip", "shasum": "", "reference": "0951e91ac04ca28cf317f3997a0adfc319e80106"}, "time": "2018-06-02T16:39:22+00:00"}, {"version": "v0.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4d969a0e08e1e05e7207c07cb4207017ecc9a331"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4d969a0e08e1e05e7207c07cb4207017ecc9a331", "type": "zip", "shasum": "", "reference": "4d969a0e08e1e05e7207c07cb4207017ecc9a331"}, "time": "2018-05-22T06:48:07+00:00"}, {"version": "v0.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "79c280013cf0b30fa23f3ba8bd3649218075adf4"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/79c280013cf0b30fa23f3ba8bd3649218075adf4", "type": "zip", "shasum": "", "reference": "79c280013cf0b30fa23f3ba8bd3649218075adf4"}, "time": "2018-04-18T12:32:50+00:00", "require-dev": {"phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0", "symfony/finder": "~2.1|~3.0|~4.0", "hoa/console": "~2.15|~3.16"}}, {"version": "v0.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4d542c66d93a4ac47e006ad25fb1f058a73b9b10"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4d542c66d93a4ac47e006ad25fb1f058a73b9b10", "type": "zip", "shasum": "", "reference": "4d542c66d93a4ac47e006ad25fb1f058a73b9b10"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2018-04-17T13:27:48+00:00"}, {"version": "v0.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "bcdf0b50395750bc916a0685e5a570dca0487dbf"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/bcdf0b50395750bc916a0685e5a570dca0487dbf", "type": "zip", "shasum": "", "reference": "bcdf0b50395750bc916a0685e5a570dca0487dbf"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/develop"}, "time": "2018-04-15T22:53:05+00:00", "require": {"php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0", "symfony/var-dumper": "~2.7|~3.0|~4.0", "nikic/php-parser": "~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}, "require-dev": {"phpunit/phpunit": "~4.4|~5.0|~6.0|~7.0", "symfony/finder": "~2.1|~3.0|~4.0", "hoa/console": "~2.15|~3.16"}}, {"version": "v0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b8ea8820cd6ea4ea60a40b86ca16aa4c1a237152"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b8ea8820cd6ea4ea60a40b86ca16aa4c1a237152", "type": "zip", "shasum": "", "reference": "b8ea8820cd6ea4ea60a40b86ca16aa4c1a237152"}, "time": "2018-04-15T21:07:52+00:00", "require": {"php": ">=5.4.0", "symfony/console": "^2.4.2|~3.0|~4.0", "symfony/var-dumper": "~2.7|~3.0|~4.0", "nikic/php-parser": "~2.0|~3.0|~4.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}}, {"version": "v0.8.18", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "5357b1cffc8fb375d6a9e3c86d5c82dd38a40834"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/5357b1cffc8fb375d6a9e3c86d5c82dd38a40834", "type": "zip", "shasum": "", "reference": "5357b1cffc8fb375d6a9e3c86d5c82dd38a40834"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2018-04-02T05:41:44+00:00", "autoload": {"files": ["src/Psy/functions.php"], "psr-4": {"Psy\\": "src/Psy/"}}, "extra": {"branch-alias": {"dev-develop": "0.8.x-dev"}}, "require": {"php": ">=5.3.9", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0", "symfony/var-dumper": "~2.7|~3.0|~4.0", "nikic/php-parser": "~1.3|~2.0|~3.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3", "symfony/finder": "~2.1|~3.0|~4.0", "hoa/console": "~3.16|~1.14"}}, {"version": "v0.8.17", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "5069b70e8c4ea492c2b5939b6eddc78bfe41cfec"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/5069b70e8c4ea492c2b5939b6eddc78bfe41cfec", "type": "zip", "shasum": "", "reference": "5069b70e8c4ea492c2b5939b6eddc78bfe41cfec"}, "time": "2017-12-28T16:14:16+00:00"}, {"version": "v0.8.16", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "d4c8eab0683dc056f2ca54ca67f5388527c068b1"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/d4c8eab0683dc056f2ca54ca67f5388527c068b1", "type": "zip", "shasum": "", "reference": "d4c8eab0683dc056f2ca54ca67f5388527c068b1"}, "time": "2017-12-10T21:49:27+00:00"}, {"version": "v0.8.15", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b1d289c2cb03a2f8249912c53e96ced38f879926"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b1d289c2cb03a2f8249912c53e96ced38f879926", "type": "zip", "shasum": "", "reference": "b1d289c2cb03a2f8249912c53e96ced38f879926"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/develop"}, "time": "2017-11-16T14:29:51+00:00", "require": {"php": ">=5.3.9", "symfony/console": "~2.3.10|^2.4.2|~3.0", "symfony/var-dumper": "~2.7|~3.0", "nikic/php-parser": "~1.3|~2.0|~3.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3", "symfony/finder": "~2.1|~3.0", "friendsofphp/php-cs-fixer": "~1.11", "hoa/console": "~3.16|~1.14"}}, {"version": "v0.8.14", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "91e53c16560bdb8b9592544bb38429ae00d6baee"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/91e53c16560bdb8b9592544bb38429ae00d6baee", "type": "zip", "shasum": "", "reference": "91e53c16560bdb8b9592544bb38429ae00d6baee"}, "time": "2017-11-04T16:06:49+00:00", "require-dev": {"phpunit/phpunit": "~4.4|~5.0", "symfony/finder": "~2.1|~3.0", "friendsofphp/php-cs-fixer": "~1.11", "hoa/console": "~3.16|~1.14"}}, {"version": "v0.8.13", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "cdb5593c3684bab74e10fcfffe4a0c8d1c39695d"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/cdb5593c3684bab74e10fcfffe4a0c8d1c39695d", "type": "zip", "shasum": "", "reference": "cdb5593c3684bab74e10fcfffe4a0c8d1c39695d"}, "time": "2017-10-19T06:13:20+00:00"}, {"version": "v0.8.12", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1502354ebc70d59d8e3a87c325b0eb78a79da25b"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1502354ebc70d59d8e3a87c325b0eb78a79da25b", "type": "zip", "shasum": "", "reference": "1502354ebc70d59d8e3a87c325b0eb78a79da25b"}, "time": "2017-10-14T17:14:13+00:00"}, {"version": "v0.8.11", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b193cd020e8c6b66cea6457826ae005e94e6d2c0"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b193cd020e8c6b66cea6457826ae005e94e6d2c0", "type": "zip", "shasum": "", "reference": "b193cd020e8c6b66cea6457826ae005e94e6d2c0"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2017-07-29T19:30:02+00:00"}, {"version": "v0.8.10", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "7ab97e5a32202585309f3ee35a0c08d2a8e588b1"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/7ab97e5a32202585309f3ee35a0c08d2a8e588b1", "type": "zip", "shasum": "", "reference": "7ab97e5a32202585309f3ee35a0c08d2a8e588b1"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.8.10"}, "time": "2017-07-22T15:14:19+00:00"}, {"version": "v0.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "58a31cc4404c8f632d8c557bc72056af2d3a83db"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/58a31cc4404c8f632d8c557bc72056af2d3a83db", "type": "zip", "shasum": "", "reference": "58a31cc4404c8f632d8c557bc72056af2d3a83db"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/develop"}, "time": "2017-07-06T14:53:52+00:00"}, {"version": "v0.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "fe65c30cbc55c71e61ba3a38b5a581149be31b8e"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/fe65c30cbc55c71e61ba3a38b5a581149be31b8e", "type": "zip", "shasum": "", "reference": "fe65c30cbc55c71e61ba3a38b5a581149be31b8e"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2017-06-24T06:16:19+00:00"}, {"version": "v0.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "be969b9dc89dcaefdb9a3117fa91fa38bca19f50"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/be969b9dc89dcaefdb9a3117fa91fa38bca19f50", "type": "zip", "shasum": "", "reference": "be969b9dc89dcaefdb9a3117fa91fa38bca19f50"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/develop"}, "time": "2017-06-20T12:51:31+00:00"}, {"version": "v0.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "7028d6d525fb183d50b249b7c07598e3d386b27d"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/7028d6d525fb183d50b249b7c07598e3d386b27d", "type": "zip", "shasum": "", "reference": "7028d6d525fb183d50b249b7c07598e3d386b27d"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.8.6"}, "time": "2017-06-04T10:34:20+00:00"}, {"version": "v0.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "38a9d21406597a0440690eafbcb0222f528c8ac6"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/38a9d21406597a0440690eafbcb0222f528c8ac6", "type": "zip", "shasum": "", "reference": "38a9d21406597a0440690eafbcb0222f528c8ac6"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2017-05-17T06:49:19+00:00", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}}, {"version": "v0.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "17cf432bb80d9198df239b80d00a29155bd74d82"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/17cf432bb80d9198df239b80d00a29155bd74d82", "type": "zip", "shasum": "", "reference": "17cf432bb80d9198df239b80d00a29155bd74d82"}, "time": "2017-05-12T15:41:55+00:00"}, {"version": "v0.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1dd4bbbc64d71e7ec075ffe82b42d9e096dc8d5e"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1dd4bbbc64d71e7ec075ffe82b42d9e096dc8d5e", "type": "zip", "shasum": "", "reference": "1dd4bbbc64d71e7ec075ffe82b42d9e096dc8d5e"}, "time": "2017-03-19T21:40:44+00:00"}, {"version": "v0.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "97113db4107a4126bef933b60fea6dbc9f615d41"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/97113db4107a4126bef933b60fea6dbc9f615d41", "type": "zip", "shasum": "", "reference": "97113db4107a4126bef933b60fea6dbc9f615d41"}, "time": "2017-03-01T00:13:29+00:00"}, {"version": "v0.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "701e8a1cc426ee170f1296f5d9f6b8a26ad25c4a"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/701e8a1cc426ee170f1296f5d9f6b8a26ad25c4a", "type": "zip", "shasum": "", "reference": "701e8a1cc426ee170f1296f5d9f6b8a26ad25c4a"}, "time": "2017-01-15T17:54:13+00:00"}, {"version": "v0.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4a8860e13aa68a4bbf2476c014f8a1f14f1bf991"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4a8860e13aa68a4bbf2476c014f8a1f14f1bf991", "type": "zip", "shasum": "", "reference": "4a8860e13aa68a4bbf2476c014f8a1f14f1bf991"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.8.0"}, "time": "2016-12-07T17:15:07+00:00", "extra": {"branch-alias": {"dev-develop": "0.8.x-dev"}}}, {"version": "v0.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "e64e10b20f8d229cac76399e1f3edddb57a0f280"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/e64e10b20f8d229cac76399e1f3edddb57a0f280", "type": "zip", "shasum": "", "reference": "e64e10b20f8d229cac76399e1f3edddb57a0f280"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2016-03-09T05:03:14+00:00", "require": {"php": ">=5.3.9", "symfony/console": "~2.3.10|^2.4.2|~3.0", "symfony/var-dumper": "~2.7|~3.0", "nikic/php-parser": "^1.2.1|~2.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}, "require-dev": {"phpunit/phpunit": "~3.7|~4.0|~5.0", "symfony/finder": "~2.1|~3.0", "squizlabs/php_codesniffer": "~2.0", "fabpot/php-cs-fixer": "~1.5"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "ext-pdo-sqlite": "The doc command requires SQLite to work."}}, {"version": "v0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "5e8cedbe0a3681f18782594eefc78423f8401fc8"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/5e8cedbe0a3681f18782594eefc78423f8401fc8", "type": "zip", "shasum": "", "reference": "5e8cedbe0a3681f18782594eefc78423f8401fc8"}, "time": "2016-02-27T18:59:18+00:00"}, {"version": "v0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1573b36948a32bdaf43db525edf5e95d5a08ed06"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1573b36948a32bdaf43db525edf5e95d5a08ed06", "type": "zip", "shasum": "", "reference": "1573b36948a32bdaf43db525edf5e95d5a08ed06"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.7.0"}, "time": "2016-02-20T16:27:05+00:00", "extra": {"branch-alias": {"dev-develop": "0.7.x-dev"}}}, {"version": "v0.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "0f04df0b23663799a8941fae13cd8e6299bde3ed"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/0f04df0b23663799a8941fae13cd8e6299bde3ed", "type": "zip", "shasum": "", "reference": "0f04df0b23663799a8941fae13cd8e6299bde3ed"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2015-11-12T16:18:56+00:00"}, {"version": "v0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "cab3aa3750d451c6e8d002433af583d88fd64862"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/cab3aa3750d451c6e8d002433af583d88fd64862", "type": "zip", "shasum": "", "reference": "cab3aa3750d451c6e8d002433af583d88fd64862"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.6.0"}, "time": "2015-11-08T22:24:35+00:00"}, {"version": "v0.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "aaf8772ade08b5f0f6830774a5d5c2f800415975"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/aaf8772ade08b5f0f6830774a5d5c2f800415975", "type": "zip", "shasum": "", "reference": "aaf8772ade08b5f0f6830774a5d5c2f800415975"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.5.2"}, "time": "2015-07-16T15:26:57+00:00", "autoload": {"files": ["src/Psy/functions.php"], "psr-0": {"Psy\\": "src/"}}, "extra": {"branch-alias": {"dev-develop": "0.6.x-dev"}}, "require": {"php": ">=5.3.9", "symfony/console": "~2.3.10|^2.4.2|~3.0", "symfony/var-dumper": "~2.7|~3.0", "nikic/php-parser": "^1.2.1", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}, "require-dev": {"phpunit/phpunit": "~3.7|~4.0", "symfony/finder": "~2.1|~3.0", "squizlabs/php_codesniffer": "~2.0", "fabpot/php-cs-fixer": "~1.5"}}, {"version": "v0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "e5a46a767928e85f1f47dda654beda8c680ddd94"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/e5a46a767928e85f1f47dda654beda8c680ddd94", "type": "zip", "shasum": "", "reference": "e5a46a767928e85f1f47dda654beda8c680ddd94"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.5.1"}, "time": "2015-07-03T16:48:00+00:00"}, {"version": "v0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "a626a965f4f0154b37b002d986212687db785608"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/a626a965f4f0154b37b002d986212687db785608", "type": "zip", "shasum": "", "reference": "a626a965f4f0154b37b002d986212687db785608"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.5.0"}, "time": "2015-07-03T16:18:32+00:00", "require": {"php": ">=5.3.9", "symfony/console": "~2.3.10|^2.4.2|~3.0", "symfony/var-dumper": "~2.7|~3.0", "nikic/php-parser": "~1.2.1", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}}, {"version": "v0.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "489816db71649bd95b416e3ed9062d40528ab0ac"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/489816db71649bd95b416e3ed9062d40528ab0ac", "type": "zip", "shasum": "", "reference": "489816db71649bd95b416e3ed9062d40528ab0ac"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.4.4"}, "time": "2015-03-26T18:43:54+00:00", "extra": {"branch-alias": {"dev-develop": "0.4.x-dev"}}, "require": {"php": ">=5.3.0", "symfony/console": "~2.3.10|~2.4.2|~2.5", "nikic/php-parser": "~1.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "0.3.*"}}, {"version": "v0.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b5dd7660c0ef38e96cd8cd8fa924b6be0e53906a"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b5dd7660c0ef38e96cd8cd8fa924b6be0e53906a", "type": "zip", "shasum": "", "reference": "b5dd7660c0ef38e96cd8cd8fa924b6be0e53906a"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.4.3"}, "time": "2015-03-17T15:17:33+00:00"}, {"version": "v0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "e50a63b4e4971041fda993b0dd6977fc60bc39d4"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/e50a63b4e4971041fda993b0dd6977fc60bc39d4", "type": "zip", "shasum": "", "reference": "e50a63b4e4971041fda993b0dd6977fc60bc39d4"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.4.2"}, "time": "2015-03-14T17:29:14+00:00"}, {"version": "v0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "3787f3436f4fd898e0ac1eb6f5abd2ab6b24085b"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/3787f3436f4fd898e0ac1eb6f5abd2ab6b24085b", "type": "zip", "shasum": "", "reference": "3787f3436f4fd898e0ac1eb6f5abd2ab6b24085b"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2015-02-25T20:35:54+00:00", "extra": {"branch-alias": {"dev-develop": "0.3.x-dev"}}, "require-dev": {"phpunit/phpunit": "~3.7|~4.0", "symfony/finder": "~2.1", "squizlabs/php_codesniffer": "~2.0", "fabpot/php-cs-fixer": "~1.3"}}, {"version": "v0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "eee16e9525405b32646b5a0f8a12fb84d566905d"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/eee16e9525405b32646b5a0f8a12fb84d566905d", "type": "zip", "shasum": "", "reference": "eee16e9525405b32646b5a0f8a12fb84d566905d"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.4.0"}, "time": "2015-02-23T21:21:17+00:00"}, {"version": "v0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "2ce0e8f3020fd292c2ad2a496c5d83939823f642"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/2ce0e8f3020fd292c2ad2a496c5d83939823f642", "type": "zip", "shasum": "", "reference": "2ce0e8f3020fd292c2ad2a496c5d83939823f642"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2015-02-12T19:35:52+00:00", "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history."}}, {"homepage": "https://github.com/bobthecow/psysh", "version": "v0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "218ea9e49c9fe21e28fce6cf77f22615af92daef"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/218ea9e49c9fe21e28fce6cf77f22615af92daef", "type": "zip", "shasum": "", "reference": "218ea9e49c9fe21e28fce6cf77f22615af92daef"}, "time": "2015-02-12T05:21:32+00:00"}, {"version": "v0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "0355cecab0591c83fed019a9572c510b0da29174"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/0355cecab0591c83fed019a9572c510b0da29174", "type": "zip", "shasum": "", "reference": "0355cecab0591c83fed019a9572c510b0da29174"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.3.3"}, "time": "2015-01-20T21:18:03+00:00"}, {"version": "v0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "0fc04a5d68ae53f496d45e2add601e2eac9cf4dd"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/0fc04a5d68ae53f496d45e2add601e2eac9cf4dd", "type": "zip", "shasum": "", "reference": "0fc04a5d68ae53f496d45e2add601e2eac9cf4dd"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.3.2"}, "time": "2014-12-27T15:19:19+00:00"}, {"version": "v0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b2c76a66e8d75b8ef10847f20bc69ea9bb55bf43"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b2c76a66e8d75b8ef10847f20bc69ea9bb55bf43", "type": "zip", "shasum": "", "reference": "b2c76a66e8d75b8ef10847f20bc69ea9bb55bf43"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.3.1"}, "time": "2014-12-27T15:07:09+00:00"}, {"version": "v0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "bbd62e4c5cd2e3635d7b1ecb6b617d8daa9ea1ad"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/bbd62e4c5cd2e3635d7b1ecb6b617d8daa9ea1ad", "type": "zip", "shasum": "", "reference": "bbd62e4c5cd2e3635d7b1ecb6b617d8daa9ea1ad"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.3.0"}, "time": "2014-12-16T06:16:40+00:00", "extra": {"branch-alias": {"dev-develop": "0.2.x-dev"}}, "require": {"php": ">=5.3.0", "symfony/console": "~2.3.10|~2.4.2|~2.5", "nikic/php-parser": "~1.0", "dnoegel/php-xdg-base-dir": "0.1", "jakub-onderka/php-console-highlighter": "dev-master"}, "require-dev": {"phpunit/phpunit": ">=3.7, <4.3", "symfony/finder": "~2.1", "squizlabs/php_codesniffer": "~2.0", "fabpot/php-cs-fixer": "2.0.*@dev"}}, {"version": "v0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "6be7c757c04508280af1e289768c6788726558ff"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/6be7c757c04508280af1e289768c6788726558ff", "type": "zip", "shasum": "", "reference": "6be7c757c04508280af1e289768c6788726558ff"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2014-10-24T15:08:55+00:00", "autoload": {"psr-0": {"Psy\\": "src/"}}, "require": {"php": ">=5.3.0", "symfony/console": ">=2.3.10, <2.6.0", "nikic/php-parser": "~1.0", "dnoegel/php-xdg-base-dir": "0.1"}, "require-dev": {"phpunit/phpunit": ">=3.7, <4.3", "symfony/finder": "~2.1"}}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "6672a73232c69e2da53fb09746ec7887c90eace6"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/6672a73232c69e2da53fb09746ec7887c90eace6", "type": "zip", "shasum": "", "reference": "6672a73232c69e2da53fb09746ec7887c90eace6"}, "time": "2014-09-10T05:21:45+00:00", "extra": {"branch-alias": {"dev-master": "0.2.0-dev"}}, "require": {"php": ">=5.3.0", "symfony/console": ">=2.3.10, <2.6.0", "nikic/php-parser": "~1.0@dev", "dnoegel/php-xdg-base-dir": "dev-master@dev"}}, {"version": "v0.1.12", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "b69799e14bea3c0e3d0f3e99dabe2026303ef21f"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/b69799e14bea3c0e3d0f3e99dabe2026303ef21f", "type": "zip", "shasum": "", "reference": "b69799e14bea3c0e3d0f3e99dabe2026303ef21f"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.1.12"}, "time": "2014-08-16T06:49:39+00:00", "extra": {"branch-alias": {"dev-master": "0.1.0-dev"}}, "require": {"php": ">=5.3.0", "symfony/console": ">=2.3.10, <2.6.0", "nikic/php-parser": "~0.9.3"}, "require-dev": {"phpunit/phpunit": ">=3.7, <4.1", "symfony/finder": "~2.1"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)"}}, {"version": "v0.1.11", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "4c61cc135eda07a37d582a389b453cd4a6843834"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/4c61cc135eda07a37d582a389b453cd4a6843834", "type": "zip", "shasum": "", "reference": "4c61cc135eda07a37d582a389b453cd4a6843834"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.1.11"}, "time": "2014-06-21T03:00:51+00:00", "require": {"php": ">=5.3.0", "symfony/console": "~2.3, >=2.3.10", "nikic/php-parser": "~0.9.3"}}, {"version": "v0.1.10", "version_normalized": "********", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "60bf0ad0c64c3ebc753a372428a3f3a1acff18b8"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/60bf0ad0c64c3ebc753a372428a3f3a1acff18b8", "type": "zip", "shasum": "", "reference": "60bf0ad0c64c3ebc753a372428a3f3a1acff18b8"}, "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/master"}, "time": "2014-06-19T19:12:44+00:00"}, {"version": "v0.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "c290226c6cf155c31a2b9866561a45ba61b99f95"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/c290226c6cf155c31a2b9866561a45ba61b99f95", "type": "zip", "shasum": "", "reference": "c290226c6cf155c31a2b9866561a45ba61b99f95"}, "time": "2014-06-08T15:58:25+00:00"}, {"version": "v0.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "33db0d52546a2b543739eee0ec62235fa7813d35"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/33db0d52546a2b543739eee0ec62235fa7813d35", "type": "zip", "shasum": "", "reference": "33db0d52546a2b543739eee0ec62235fa7813d35"}, "time": "2014-04-13T01:58:29+00:00"}, {"version": "v0.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "636ea9b98ae228fadc3b500cb858eef0c6b63194"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/636ea9b98ae228fadc3b500cb858eef0c6b63194", "type": "zip", "shasum": "", "reference": "636ea9b98ae228fadc3b500cb858eef0c6b63194"}, "time": "2014-03-18T17:26:21+00:00", "require-dev": {"phpunit/phpunit": "~3.7", "symfony/finder": "~2.1"}}, {"version": "v0.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "2543c2870cda81f47eef7620693c932487f7a389"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/2543c2870cda81f47eef7620693c932487f7a389", "type": "zip", "shasum": "", "reference": "2543c2870cda81f47eef7620693c932487f7a389"}, "time": "2014-03-18T15:09:26+00:00"}, {"version": "v0.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "232077017a22a8941e28c6ed771330868202d8c8"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/232077017a22a8941e28c6ed771330868202d8c8", "type": "zip", "shasum": "", "reference": "232077017a22a8941e28c6ed771330868202d8c8"}, "time": "2014-03-14T20:20:23+00:00"}, {"version": "v0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "c88d6ab9423884f9bf7ea6eff42b949126fa5e87"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/c88d6ab9423884f9bf7ea6eff42b949126fa5e87", "type": "zip", "shasum": "", "reference": "c88d6ab9423884f9bf7ea6eff42b949126fa5e87"}, "time": "2014-03-11T18:22:27+00:00"}, {"version": "v0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "8e69a26ea07a6fd98bf7a11020779a8a4c246e88"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/8e69a26ea07a6fd98bf7a11020779a8a4c246e88", "type": "zip", "shasum": "", "reference": "8e69a26ea07a6fd98bf7a11020779a8a4c246e88"}, "time": "2014-03-11T15:51:12+00:00"}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "1fc3294b7c1cbd5e89738bc2ec40b53c59551c0a"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/1fc3294b7c1cbd5e89738bc2ec40b53c59551c0a", "type": "zip", "shasum": "", "reference": "1fc3294b7c1cbd5e89738bc2ec40b53c59551c0a"}, "time": "2014-03-08T15:57:43+00:00"}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "f0d6d0d44640e0be732fdbfc9593c2e2012366c5"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/f0d6d0d44640e0be732fdbfc9593c2e2012366c5", "type": "zip", "shasum": "", "reference": "f0d6d0d44640e0be732fdbfc9593c2e2012366c5"}, "time": "2014-02-26T17:10:13+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/bobthecow/psysh.git", "type": "git", "reference": "61b321a77f4ed2e338fb6af00b5b8b899017bd94"}, "dist": {"url": "https://api.github.com/repos/bobthecow/psysh/zipball/61b321a77f4ed2e338fb6af00b5b8b899017bd94", "type": "zip", "shasum": "", "reference": "61b321a77f4ed2e338fb6af00b5b8b899017bd94"}, "time": "2014-02-20T00:21:04+00:00"}]}, "security-advisories": [], "last-modified": "Mon, 23 Jun 2025 02:36:11 GMT"}