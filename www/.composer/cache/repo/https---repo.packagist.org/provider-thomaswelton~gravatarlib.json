{"minified": "composer/2.0", "packages": {"thomaswelton/gravatarlib": [{"name": "thomas<PERSON><PERSON>/gravatarlib", "description": "A lightweight PHP 5.3 OOP library providing easy gravatar integration.", "keywords": ["templating", "gravatar", "twig"], "homepage": "", "version": "0.1.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "thomas<PERSON><PERSON>@me.com"}], "source": {"url": "https://github.com/thomaswelton/gravatarlib.git", "type": "git", "reference": "8cb3b8c4b6c98881c666d7d09641e3cc4a5896d8"}, "dist": {"url": "https://api.github.com/repos/thomaswelton/gravatarlib/zipball/8cb3b8c4b6c98881c666d7d09641e3cc4a5896d8", "type": "zip", "shasum": "", "reference": "8cb3b8c4b6c98881c666d7d09641e3cc4a5896d8"}, "type": "library", "support": {"issues": "https://github.com/thomaswelton/gravatarlib/issues", "source": "https://github.com/thomaswelton/gravatarlib/tree/0.1.1"}, "funding": [], "time": "2024-08-18T14:10:18+00:00", "autoload": {"psr-0": {"thomaswelton\\GravatarLib\\": ""}}, "target-dir": "thomas<PERSON>lton/GravatarLib", "require": {"php": ">=5.3.0"}, "suggest": {"twig/twig": ">=1.4.0"}}, {"version": "0.1.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "thomas<PERSON><PERSON>@me.com", "homepage": "https://github.com/thomaswelton", "role": "Developer"}], "source": {"url": "https://github.com/thomaswelton/gravatarlib.git", "type": "git", "reference": "8a4e829c53ca2abb51ef2e514f696938a9bdbd0c"}, "dist": {"url": "https://api.github.com/repos/thomaswelton/gravatarlib/zipball/8a4e829c53ca2abb51ef2e514f696938a9bdbd0c", "type": "zip", "shasum": "", "reference": "8a4e829c53ca2abb51ef2e514f696938a9bdbd0c"}, "support": {"issues": "https://github.com/thomaswelton/gravatarlib/issues", "source": "https://github.com/thomaswelton/gravatarlib/tree/master"}, "time": "2014-03-02T18:29:18+00:00", "funding": "__unset"}]}, "security-advisories": [], "last-modified": "Sun, 18 Aug 2024 14:10:45 GMT"}