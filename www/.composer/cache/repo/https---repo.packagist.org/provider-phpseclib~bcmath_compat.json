{"minified": "composer/2.0", "packages": {"phpseclib/bcmath_compat": [{"name": "phpseclib/bcmath_compat", "description": "PHP 5.x-8.x polyfill for bcmath extension", "keywords": ["math", "BigInteger", "bcmath", "bigdecimal", "polyfill"], "homepage": "", "version": "2.0.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://phpseclib.sourceforge.net"}], "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "ae8f87ea0c96b2ef08ecf0d291d45372d0f7bc5a"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/ae8f87ea0c96b2ef08ecf0d291d45372d0f7bc5a", "type": "zip", "shasum": "", "reference": "ae8f87ea0c96b2ef08ecf0d291d45372d0f7bc5a"}, "type": "library", "support": {"email": "<EMAIL>", "issues": "https://github.com/phpseclib/bcmath_compat/issues", "source": "https://github.com/phpseclib/bcmath_compat"}, "funding": [], "time": "2024-06-06T14:17:54+00:00", "autoload": {"files": ["lib/bcmath.php"], "psr-4": {"bcmath_compat\\": "src"}}, "require": {"phpseclib/phpseclib": "^3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "^3.0"}, "suggest": {"ext-gmp": "Will enable faster math operations"}, "provide": {"ext-bcmath": "8.1.0"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "29bbf07a7039ff65ce7daa44502ba34baf1512ec"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/29bbf07a7039ff65ce7daa44502ba34baf1512ec", "type": "zip", "shasum": "", "reference": "29bbf07a7039ff65ce7daa44502ba34baf1512ec"}, "time": "2024-02-21T10:30:36+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "2ffea8bfe1702b4535a7b3c2649c4301968e9a3c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/2ffea8bfe1702b4535a7b3c2649c4301968e9a3c", "type": "zip", "shasum": "", "reference": "2ffea8bfe1702b4535a7b3c2649c4301968e9a3c"}, "time": "2021-12-16T02:35:52+00:00"}, {"description": "PHP 5.x/7.x polyfill for bcmath extension", "version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "fd896dfceffc13d8cf45d2ee3470777a70026f3c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/fd896dfceffc13d8cf45d2ee3470777a70026f3c", "type": "zip", "shasum": "", "reference": "fd896dfceffc13d8cf45d2ee3470777a70026f3c"}, "time": "2020-12-22T16:38:51+00:00", "provide": {"ext-bcmath": "8.0.0"}}, {"description": "PHP 5.x-8.x polyfill for bcmath extension", "version": "1.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "52af8637a01609800e606e78869ed268d3d976d7"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/52af8637a01609800e606e78869ed268d3d976d7", "type": "zip", "shasum": "", "reference": "52af8637a01609800e606e78869ed268d3d976d7"}, "time": "2024-06-04T14:25:51+00:00", "require": {"phpseclib/phpseclib": ">=2.0.19 <3.0.0"}, "provide": {"ext-bcmath": "8.1.0"}}, {"version": "1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "09ae1d61a6f2346530ee8c4f81041218c7ea098e"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/09ae1d61a6f2346530ee8c4f81041218c7ea098e", "type": "zip", "shasum": "", "reference": "09ae1d61a6f2346530ee8c4f81041218c7ea098e"}, "time": "2024-02-21T10:30:04+00:00"}, {"version": "1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "b3c522368f70414441ab34573d034c610f121582"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/b3c522368f70414441ab34573d034c610f121582", "type": "zip", "shasum": "", "reference": "b3c522368f70414441ab34573d034c610f121582"}, "time": "2021-12-16T02:35:38+00:00"}, {"description": "PHP 5.x/7.x polyfill for bcmath extension", "version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "f6f03d3af2ef95fc35b30abdd29f20ee11276a65"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/f6f03d3af2ef95fc35b30abdd29f20ee11276a65", "type": "zip", "shasum": "", "reference": "f6f03d3af2ef95fc35b30abdd29f20ee11276a65"}, "time": "2020-12-22T16:34:18+00:00", "provide": {"ext-bcmath": "8.0.0"}}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "89cbb63742a32730b7187773a60b6b12b9db4479"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/89cbb63742a32730b7187773a60b6b12b9db4479", "type": "zip", "shasum": "", "reference": "89cbb63742a32730b7187773a60b6b12b9db4479"}, "time": "2020-04-26T16:34:33+00:00", "require": {"phpseclib/phpseclib": ">=2.0.19"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "provide": {"ext-bcmath": "7.3.5"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "f805922db4b3d8c1e174dafb74ac7374264e8880"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/f805922db4b3d8c1e174dafb74ac7374264e8880", "type": "zip", "shasum": "", "reference": "f805922db4b3d8c1e174dafb74ac7374264e8880"}, "time": "2020-01-10T11:44:43+00:00", "funding": "__unset", "provide": "__unset"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "c394fcdf4e3f84438aac24252e21710c21cd59a5"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/c394fcdf4e3f84438aac24252e21710c21cd59a5", "type": "zip", "shasum": "", "reference": "c394fcdf4e3f84438aac24252e21710c21cd59a5"}, "time": "2019-06-20T12:36:23+00:00", "provide": {"ext-bcmath": "7.3.5"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "3955984fc133aac4fc40e9c2691cd80e1f3e29d9"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/3955984fc133aac4fc40e9c2691cd80e1f3e29d9", "type": "zip", "shasum": "", "reference": "3955984fc133aac4fc40e9c2691cd80e1f3e29d9"}, "time": "2019-06-17T05:43:24+00:00", "require": {"phpseclib/phpseclib": ">=2.0.17"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "3e6a947b9945e34562d1800a9b234e4a28902465"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/3e6a947b9945e34562d1800a9b234e4a28902465", "type": "zip", "shasum": "", "reference": "3e6a947b9945e34562d1800a9b234e4a28902465"}, "time": "2019-05-26T23:49:33+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/bcmath_compat.git", "type": "git", "reference": "f7466bb28db35def18b497aaf51f3d63f4f89169"}, "dist": {"url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/f7466bb28db35def18b497aaf51f3d63f4f89169", "type": "zip", "shasum": "", "reference": "f7466bb28db35def18b497aaf51f3d63f4f89169"}, "time": "2019-05-26T06:20:14+00:00", "require": {"phpseclib/phpseclib": ">=2.0.0"}}]}, "security-advisories": [], "last-modified": "Mon, 02 Sep 2024 20:14:36 GMT"}