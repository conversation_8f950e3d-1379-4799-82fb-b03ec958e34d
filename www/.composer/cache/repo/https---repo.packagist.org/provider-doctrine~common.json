{"minified": "composer/2.0", "packages": {"doctrine/common": [{"name": "doctrine/common", "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, proxies and much more.", "keywords": ["php", "doctrine", "common"], "homepage": "https://www.doctrine-project.org/projects/common.html", "version": "3.5.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/d9ea4a54ca2586db781f0265d36bea731ac66ec5", "type": "zip", "shasum": "", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5"}, "type": "library", "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2025-01-01T22:12:03+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "require": {"php": "^7.1 || ^8.0", "doctrine/persistence": "^2.0 || ^3.0 || ^4.0"}, "require-dev": {"doctrine/collections": "^1", "phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "doctrine/coding-standard": "^9.0 || ^10.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^6.1", "vimeo/psalm": "^4.4"}}, {"version": "3.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "6c8fef961f67b8bc802ce3e32e3ebd1022907286"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/6c8fef961f67b8bc802ce3e32e3ebd1022907286", "type": "zip", "shasum": "", "reference": "6c8fef961f67b8bc802ce3e32e3ebd1022907286"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.4.5"}, "time": "2024-10-08T15:53:43+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/persistence": "^2.0 || ^3.0"}}, {"version": "3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "0aad4b7ab7ce8c6602dfbb1e1a24581275fb9d1a"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/0aad4b7ab7ce8c6602dfbb1e1a24581275fb9d1a", "type": "zip", "shasum": "", "reference": "0aad4b7ab7ce8c6602dfbb1e1a24581275fb9d1a"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.4.4"}, "time": "2024-04-16T13:35:33+00:00"}, {"version": "3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "8b5e5650391f851ed58910b3e3d48a71062eeced"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/8b5e5650391f851ed58910b3e3d48a71062eeced", "type": "zip", "shasum": "", "reference": "8b5e5650391f851ed58910b3e3d48a71062eeced"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.4.3"}, "time": "2022-10-09T11:47:59+00:00"}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "609c3a7b6af49a7b4b13945ca2fdf4af801946af"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/609c3a7b6af49a7b4b13945ca2fdf4af801946af", "type": "zip", "shasum": "", "reference": "609c3a7b6af49a7b4b13945ca2fdf4af801946af"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.4.2"}, "time": "2022-09-28T14:20:50+00:00"}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "616d5fca274ea66b969cdebbbfd1dc33a87d461b"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/616d5fca274ea66b969cdebbbfd1dc33a87d461b", "type": "zip", "shasum": "", "reference": "616d5fca274ea66b969cdebbbfd1dc33a87d461b"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.4.1"}, "time": "2022-09-26T17:15:48+00:00"}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "e09556bbdf95b8420e649162b19ae9da2d1a80f3"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/e09556bbdf95b8420e649162b19ae9da2d1a80f3", "type": "zip", "shasum": "", "reference": "e09556bbdf95b8420e649162b19ae9da2d1a80f3"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.4.0"}, "time": "2022-08-23T19:46:56+00:00", "require-dev": {"doctrine/collections": "^1", "phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "doctrine/coding-standard": "^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5", "vimeo/psalm": "^4.4"}}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "6a76bd25b1030d35d6ba2bf2f69ca858a41fc580"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/6a76bd25b1030d35d6ba2bf2f69ca858a41fc580", "type": "zip", "shasum": "", "reference": "6a76bd25b1030d35d6ba2bf2f69ca858a41fc580"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.3.1"}, "time": "2022-08-20T10:48:54+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "c824e95d4c83b7102d8bc60595445a6f7d540f96"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/c824e95d4c83b7102d8bc60595445a6f7d540f96", "type": "zip", "shasum": "", "reference": "c824e95d4c83b7102d8bc60595445a6f7d540f96"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.3.0"}, "time": "2022-02-05T18:28:51+00:00", "require-dev": {"phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "doctrine/coding-standard": "^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5", "vimeo/psalm": "^4.4"}}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "295082d3750987065912816a9d536c2df735f637"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/295082d3750987065912816a9d536c2df735f637", "type": "zip", "shasum": "", "reference": "295082d3750987065912816a9d536c2df735f637"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.2.2"}, "time": "2022-02-02T09:15:57+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/persistence": "^2.0"}}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "e927fc2410c8723d053b8032e591cdff76587cdb"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/e927fc2410c8723d053b8032e591cdff76587cdb", "type": "zip", "shasum": "", "reference": "e927fc2410c8723d053b8032e591cdff76587cdb"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.2.1"}, "time": "2021-12-26T22:39:45+00:00", "require-dev": {"phpstan/phpstan": "^1.2.0", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "doctrine/coding-standard": "^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5", "vimeo/psalm": "^4.4"}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "6d970a11479275300b5144e9373ce5feacfa9b91"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/6d970a11479275300b5144e9373ce5feacfa9b91", "type": "zip", "shasum": "", "reference": "6d970a11479275300b5144e9373ce5feacfa9b91"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.2.0"}, "time": "2021-10-19T06:47:22+00:00", "require-dev": {"phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "doctrine/coding-standard": "^6.0 || ^8.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5", "vimeo/psalm": "^4.4"}}, {"version": "3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "a036d90c303f3163b5be8b8fde9b6755b2be4a3a"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/a036d90c303f3163b5be8b8fde9b6755b2be4a3a", "type": "zip", "shasum": "", "reference": "a036d90c303f3163b5be8b8fde9b6755b2be4a3a"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.1.2"}, "time": "2021-02-10T20:18:51+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "2afde5a9844126bc311cd5f548b5475e75f800d3"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/2afde5a9844126bc311cd5f548b5475e75f800d3", "type": "zip", "shasum": "", "reference": "2afde5a9844126bc311cd5f548b5475e75f800d3"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.1.1"}, "time": "2021-01-20T19:58:05+00:00", "require-dev": {"phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "doctrine/coding-standard": "^6.0 || ^8.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "9f3e3f3cc5399604c0325d5ffa92609d694d950d"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/9f3e3f3cc5399604c0325d5ffa92609d694d950d", "type": "zip", "shasum": "", "reference": "9f3e3f3cc5399604c0325d5ffa92609d694d950d"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.1.0"}, "time": "2020-12-03T21:02:31+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "16d5332b66d8e37045d759b31b2c2159df6fc7df"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/16d5332b66d8e37045d759b31b2c2159df6fc7df", "type": "zip", "shasum": "", "reference": "16d5332b66d8e37045d759b31b2c2159df6fc7df"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.0.3"}, "time": "2020-11-30T18:18:13+00:00", "require-dev": {"phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "doctrine/coding-standard": "^6.0 || ^8.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}}, {"description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "a3c6479858989e242a2465972b4f7a8642baf0d4"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/a3c6479858989e242a2465972b4f7a8642baf0d4", "type": "zip", "shasum": "", "reference": "a3c6479858989e242a2465972b4f7a8642baf0d4"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.0.2"}, "time": "2020-06-05T16:59:53+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require-dev": {"phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "doctrine/coding-standard": "^1.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "402a424f1e7dc39ce8fa7901e56d25835978387c"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/402a424f1e7dc39ce8fa7901e56d25835978387c", "type": "zip", "shasum": "", "reference": "402a424f1e7dc39ce8fa7901e56d25835978387c"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.0.x"}, "time": "2020-05-30T15:08:34+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "05ab20457d332cc0eef880b17cdbbffcd6af526f"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/05ab20457d332cc0eef880b17cdbbffcd6af526f", "type": "zip", "shasum": "", "reference": "05ab20457d332cc0eef880b17cdbbffcd6af526f"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/master"}, "time": "2020-05-25T20:38:25+00:00"}, {"version": "2.13.3", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "type": "zip", "shasum": "", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "time": "2020-06-05T16:46:05+00:00", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "require": {"php": "^7.1 || ^8.0", "doctrine/inflector": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/lexer": "^1.0", "doctrine/annotations": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.0", "doctrine/persistence": "^1.3.3"}}, {"version": "2.13.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "6902fafacb43333d9dc3d949c0a06048a31549ac"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/6902fafacb43333d9dc3d949c0a06048a31549ac", "type": "zip", "shasum": "", "reference": "6902fafacb43333d9dc3d949c0a06048a31549ac"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.2"}, "time": "2020-05-29T17:35:20+00:00"}, {"version": "2.13.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "fb00cd761126b11d8f334c09cf5b1f3f83fefc17"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/fb00cd761126b11d8f334c09cf5b1f3f83fefc17", "type": "zip", "shasum": "", "reference": "fb00cd761126b11d8f334c09cf5b1f3f83fefc17"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "time": "2020-05-25T20:05:47+00:00"}, {"version": "2.13.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "308728eae8d90412d850c155d40b1cfbede549da"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/308728eae8d90412d850c155d40b1cfbede549da", "type": "zip", "shasum": "", "reference": "308728eae8d90412d850c155d40b1cfbede549da"}, "time": "2020-05-15T05:51:54+00:00", "require": {"php": "^7.1", "doctrine/inflector": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/lexer": "^1.0", "doctrine/annotations": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.0", "doctrine/persistence": "^1.3.3"}}, {"version": "2.12.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "2053eafdf60c2172ee1373d1b9289ba1db7f1fc6"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/2053eafdf60c2172ee1373d1b9289ba1db7f1fc6", "type": "zip", "shasum": "", "reference": "2053eafdf60c2172ee1373d1b9289ba1db7f1fc6"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.12.0"}, "time": "2020-01-10T15:49:25+00:00", "require": {"php": "^7.1", "doctrine/inflector": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/lexer": "^1.0", "doctrine/annotations": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.0", "doctrine/persistence": "^1.1"}, "funding": "__unset"}, {"version": "v2.11.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "b8ca1dcf6b0dc8a2af7a09baac8d0c48345df4ff"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/b8ca1dcf6b0dc8a2af7a09baac8d0c48345df4ff", "type": "zip", "shasum": "", "reference": "b8ca1dcf6b0dc8a2af7a09baac8d0c48345df4ff"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.11"}, "time": "2019-09-10T10:10:14+00:00"}, {"version": "v2.10.0", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "30e33f60f64deec87df728c02b107f82cdafad9d"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/30e33f60f64deec87df728c02b107f82cdafad9d", "type": "zip", "shasum": "", "reference": "30e33f60f64deec87df728c02b107f82cdafad9d"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/master"}, "time": "2018-11-21T01:24:55+00:00", "extra": {"branch-alias": {"dev-master": "2.10.x-dev"}}, "require-dev": {"phpunit/phpunit": "^6.3", "doctrine/coding-standard": "^1.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}}, {"description": "Common Library for Doctrine projects", "keywords": ["collections", "spl", "eventmanager", "annotations", "persistence"], "homepage": "https://www.doctrine-project.org", "version": "v2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "a210246d286c77d2b89040f8691ba7b3a713d2c1"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/a210246d286c77d2b89040f8691ba7b3a713d2c1", "type": "zip", "shasum": "", "reference": "a210246d286c77d2b89040f8691ba7b3a713d2c1"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.9"}, "time": "2018-07-12T21:16:12+00:00", "extra": {"branch-alias": {"dev-master": "2.9.x-dev"}}, "require": {"php": "^7.1", "doctrine/inflector": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/lexer": "^1.0", "doctrine/annotations": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.0", "doctrine/persistence": "^1.0"}}, {"homepage": "http://www.doctrine-project.org", "version": "v2.8.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "f68c297ce6455e8fd794aa8ffaf9fa458f6ade66"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/f68c297ce6455e8fd794aa8ffaf9fa458f6ade66", "type": "zip", "shasum": "", "reference": "f68c297ce6455e8fd794aa8ffaf9fa458f6ade66"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.8.1"}, "time": "2017-08-31T08:43:38+00:00", "extra": {"branch-alias": {"dev-master": "2.8.x-dev"}}, "require": {"php": "~7.1", "doctrine/inflector": "1.*", "doctrine/cache": "1.*", "doctrine/collections": "1.*", "doctrine/lexer": "1.*", "doctrine/annotations": "1.*"}, "require-dev": {"phpunit/phpunit": "^5.7"}}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "ed349f953d443963c590b008b37b864b8a3c4b21"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/ed349f953d443963c590b008b37b864b8a3c4b21", "type": "zip", "shasum": "", "reference": "ed349f953d443963c590b008b37b864b8a3c4b21"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.8.0"}, "time": "2017-07-22T09:01:43+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "4acb8f89626baafede6ee5475bc5844096eba8a9"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/4acb8f89626baafede6ee5475bc5844096eba8a9", "type": "zip", "shasum": "", "reference": "4acb8f89626baafede6ee5475bc5844096eba8a9"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.7.3"}, "time": "2017-07-22T08:35:12+00:00", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "require": {"php": "~5.6|~7.0", "doctrine/inflector": "1.*", "doctrine/cache": "1.*", "doctrine/collections": "1.*", "doctrine/lexer": "1.*", "doctrine/annotations": "1.*"}, "require-dev": {"phpunit/phpunit": "^5.4.6"}}, {"version": "v2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "930297026c8009a567ac051fd545bf6124150347"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/930297026c8009a567ac051fd545bf6124150347", "type": "zip", "shasum": "", "reference": "930297026c8009a567ac051fd545bf6124150347"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.7"}, "time": "2017-01-13T14:02:13+00:00"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "5954c297e9d93ff84554906c2fbbc2a133c43941"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/5954c297e9d93ff84554906c2fbbc2a133c43941", "type": "zip", "shasum": "", "reference": "5954c297e9d93ff84554906c2fbbc2a133c43941"}, "time": "2016-12-03T08:15:01+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "223a2925acb40578fdc338deddbd461b091501ac"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/223a2925acb40578fdc338deddbd461b091501ac", "type": "zip", "shasum": "", "reference": "223a2925acb40578fdc338deddbd461b091501ac"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/master"}, "time": "2016-11-30T16:40:55+00:00"}, {"version": "v2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "7bce00698899aa2c06fe7365c76e4d78ddb15fa3"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/7bce00698899aa2c06fe7365c76e4d78ddb15fa3", "type": "zip", "shasum": "", "reference": "7bce00698899aa2c06fe7365c76e4d78ddb15fa3"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.6"}, "time": "2016-11-30T16:50:46+00:00", "require": {"php": "~5.5|~7.0", "doctrine/inflector": "1.*", "doctrine/cache": "1.*", "doctrine/collections": "1.*", "doctrine/lexer": "1.*", "doctrine/annotations": "1.*"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.0"}}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "a579557bc689580c19fee4e27487a67fe60defc0"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/a579557bc689580c19fee4e27487a67fe60defc0", "type": "zip", "shasum": "", "reference": "a579557bc689580c19fee4e27487a67fe60defc0"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.6.1"}, "time": "2015-12-25T13:18:31+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "3cb33d19beb3c62f76c55e7e9683fff12e242bc8"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/3cb33d19beb3c62f76c55e7e9683fff12e242bc8", "type": "zip", "shasum": "", "reference": "3cb33d19beb3c62f76c55e7e9683fff12e242bc8"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.6.0"}, "time": "2015-12-04T13:06:46+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "10f1f19651343f87573129ca970aef1a47a6f29e"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/10f1f19651343f87573129ca970aef1a47a6f29e", "type": "zip", "shasum": "", "reference": "10f1f19651343f87573129ca970aef1a47a6f29e"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.5.3"}, "time": "2015-12-25T13:10:16+00:00", "autoload": {"psr-0": {"Doctrine\\Common\\": "lib/"}}, "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "require": {"php": ">=5.3.2", "doctrine/inflector": "1.*", "doctrine/cache": "1.*", "doctrine/collections": "1.*", "doctrine/lexer": "1.*", "doctrine/annotations": "1.*"}, "require-dev": {"phpunit/phpunit": "~3.7"}}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "311001fd9865a4d0d59efff4eac6d7dcb3f5270c"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/311001fd9865a4d0d59efff4eac6d7dcb3f5270c", "type": "zip", "shasum": "", "reference": "311001fd9865a4d0d59efff4eac6d7dcb3f5270c"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.5.2"}, "time": "2015-12-04T12:49:42+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "0009b8f0d4a917aabc971fb089eba80e872f83f9"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/0009b8f0d4a917aabc971fb089eba80e872f83f9", "type": "zip", "shasum": "", "reference": "0009b8f0d4a917aabc971fb089eba80e872f83f9"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/master"}, "time": "2015-08-31T13:00:22+00:00", "extra": {"branch-alias": {"dev-master": "2.6.x-dev"}}}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "cd8daf2501e10c63dced7b8b9b905844316ae9d3"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/cd8daf2501e10c63dced7b8b9b905844316ae9d3", "type": "zip", "shasum": "", "reference": "cd8daf2501e10c63dced7b8b9b905844316ae9d3"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.5.0"}, "time": "2015-04-02T19:55:44+00:00"}, {"version": "v2.5.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "1348ca4bc50bc6959ac7197896daf1fc9059aa0d"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/1348ca4bc50bc6959ac7197896daf1fc9059aa0d", "type": "zip", "shasum": "", "reference": "1348ca4bc50bc6959ac7197896daf1fc9059aa0d"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.5.0-beta1"}, "time": "2015-03-26T00:26:38+00:00", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "4824569127daa9784bf35219a1cd49306c795389"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/4824569127daa9784bf35219a1cd49306c795389", "type": "zip", "shasum": "", "reference": "4824569127daa9784bf35219a1cd49306c795389"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.4.3"}, "time": "2015-08-31T14:38:45+00:00", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}}, {"version": "v2.4.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.jwage.com/", "role": "Creator"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>", "homepage": "http://www.instaclick.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "5db6ab40e4c531f14dad4ca96a394dfce5d4255b"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/5db6ab40e4c531f14dad4ca96a394dfce5d4255b", "type": "zip", "shasum": "", "reference": "5db6ab40e4c531f14dad4ca96a394dfce5d4255b"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.4.2"}, "time": "2014-05-21T19:28:51+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "ceb18cf9b0230f3ea208b6238130fd415abda0a7"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/ceb18cf9b0230f3ea208b6238130fd415abda0a7", "type": "zip", "shasum": "", "reference": "ceb18cf9b0230f3ea208b6238130fd415abda0a7"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/v2.4.1"}, "time": "2013-09-07T10:20:34+00:00", "require-dev": "__unset"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "7877938b8972be0653cc8e71803df6bcb5443a1f"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/7877938b8972be0653cc8e71803df6bcb5443a1f", "type": "zip", "shasum": "", "reference": "7877938b8972be0653cc8e71803df6bcb5443a1f"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.4"}, "time": "2013-08-20T18:10:52+00:00"}, {"version": "2.4.0-RC4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "c4255b9fbd63ee1fe52697839318af5937fced9b"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/c4255b9fbd63ee1fe52697839318af5937fced9b", "type": "zip", "shasum": "", "reference": "c4255b9fbd63ee1fe52697839318af5937fced9b"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/master"}, "time": "2013-06-21T12:11:28+00:00"}, {"version": "2.4.0-RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "c5174badcdea873d9535395b3a96fd440a30c8b6"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/c5174badcdea873d9535395b3a96fd440a30c8b6", "type": "zip", "shasum": "", "reference": "c5174badcdea873d9535395b3a96fd440a30c8b6"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.4.0-RC3"}, "time": "2013-05-27T19:11:46+00:00"}, {"version": "2.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "ce87784a45f93c73a4035748fd98ecbbda3098fb"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/ce87784a45f93c73a4035748fd98ecbbda3098fb", "type": "zip", "shasum": "", "reference": "ce87784a45f93c73a4035748fd98ecbbda3098fb"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/master"}, "time": "2013-05-09T21:35:24+00:00"}, {"version": "2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "15df2070954dfc9addbeb7ade1a150a2c04607ae"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/15df2070954dfc9addbeb7ade1a150a2c04607ae", "type": "zip", "shasum": "", "reference": "15df2070954dfc9addbeb7ade1a150a2c04607ae"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.4.0-RC1"}, "time": "2013-03-24T17:52:34+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "d1c7d4334e38cad603a5c863d4c7b91bb04ec6b2"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/d1c7d4334e38cad603a5c863d4c7b91bb04ec6b2", "type": "zip", "shasum": "", "reference": "d1c7d4334e38cad603a5c863d4c7b91bb04ec6b2"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.3.0"}, "time": "2012-09-20T05:55:18+00:00", "autoload": {"psr-0": {"Doctrine\\Common": "lib/"}}, "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "require": {"php": ">=5.3.2"}}, {"version": "2.3.0-RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "d0818fb3c86f743eda0a13d35bba4e3660acddde"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/d0818fb3c86f743eda0a13d35bba4e3660acddde", "type": "zip", "shasum": "", "reference": "d0818fb3c86f743eda0a13d35bba4e3660acddde"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.3.0-RC3"}, "time": "2012-09-17T11:47:57+00:00"}, {"version": "2.3.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "90be80017a43253e23ef792158f9b627c699c578"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/90be80017a43253e23ef792158f9b627c699c578", "type": "zip", "shasum": "", "reference": "90be80017a43253e23ef792158f9b627c699c578"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.3.0-RC2"}, "time": "2012-08-29T14:06:32+00:00"}, {"version": "2.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "9b67267f19eee00cbf0ffb489b36977d4e205e36"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/9b67267f19eee00cbf0ffb489b36977d4e205e36", "type": "zip", "shasum": "", "reference": "9b67267f19eee00cbf0ffb489b36977d4e205e36"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.3.0-RC1"}, "time": "2012-07-29T10:44:28+00:00"}, {"version": "2.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "e0bb65229e6dfc1f012a0eef9e23016308652e46"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/e0bb65229e6dfc1f012a0eef9e23016308652e46", "type": "zip", "shasum": "", "reference": "e0bb65229e6dfc1f012a0eef9e23016308652e46"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.3.0-BETA1"}, "time": "2012-07-16T12:58:02+00:00"}, {"version": "2.2.3", "version_normalized": "*******", "license": ["LGPL"], "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "9e13facbacb001d324d71a0f6d25d8d7f12346b6"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/9e13facbacb001d324d71a0f6d25d8d7f12346b6", "type": "zip", "shasum": "", "reference": "9e13facbacb001d324d71a0f6d25d8d7f12346b6"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.3"}, "time": "2012-08-29T08:04:14+00:00", "extra": "__unset"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "14eb4d62b6e0a87cd99200078c38644bbd216e42"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/14eb4d62b6e0a87cd99200078c38644bbd216e42", "type": "zip", "shasum": "", "reference": "14eb4d62b6e0a87cd99200078c38644bbd216e42"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.2"}, "time": "2012-04-13T07:46:44+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "6c7f9cfd4664dc4f8056e4cd24b2c31d874ff6e9"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/6c7f9cfd4664dc4f8056e4cd24b2c31d874ff6e9", "type": "zip", "shasum": "", "reference": "6c7f9cfd4664dc4f8056e4cd24b2c31d874ff6e9"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.1"}, "time": "2012-03-03T21:05:52+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "84838bb8c9896cea6210050a2e1264ee9592ca1b"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/84838bb8c9896cea6210050a2e1264ee9592ca1b", "type": "zip", "shasum": "", "reference": "84838bb8c9896cea6210050a2e1264ee9592ca1b"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0"}, "time": "2012-01-29T08:29:07+00:00"}, {"version": "2.2.0-RC5", "version_normalized": "*******-RC5", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "0548a9cfd9620184ed865c85c12e3030c5a31f85"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/0548a9cfd9620184ed865c85c12e3030c5a31f85", "type": "zip", "shasum": "", "reference": "0548a9cfd9620184ed865c85c12e3030c5a31f85"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0-RC5"}, "time": "2012-01-16T12:41:14+00:00"}, {"version": "2.2.0-RC4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "17e774007b98beb2e253e645260e0f9c32f4c936"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/17e774007b98beb2e253e645260e0f9c32f4c936", "type": "zip", "shasum": "", "reference": "17e774007b98beb2e253e645260e0f9c32f4c936"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0-RC4"}, "time": "2012-01-03T20:23:45+00:00"}, {"version": "2.2.0-RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "56a320092fb056a40b472308cb41284f25ff401d"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/56a320092fb056a40b472308cb41284f25ff401d", "type": "zip", "shasum": "", "reference": "56a320092fb056a40b472308cb41284f25ff401d"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0-RC3"}, "time": "2011-12-19T13:02:01+00:00"}, {"version": "2.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "fad3388998bcab2f0efc74b63150a2b504448f98"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/fad3388998bcab2f0efc74b63150a2b504448f98", "type": "zip", "shasum": "", "reference": "fad3388998bcab2f0efc74b63150a2b504448f98"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0-RC1"}, "time": "2011-12-19T11:59:11+00:00"}, {"version": "2.2.0BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "21eccbc1b5d39c95c09466604454606c152ca4ef"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/21eccbc1b5d39c95c09466604454606c152ca4ef", "type": "zip", "shasum": "", "reference": "21eccbc1b5d39c95c09466604454606c152ca4ef"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0BETA2"}, "time": "2011-12-12T19:47:43+00:00"}, {"version": "2.2.0BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "eabf442d226384a3e4f7d02ee37eaf2adfa6f298"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/eabf442d226384a3e4f7d02ee37eaf2adfa6f298", "type": "zip", "shasum": "", "reference": "eabf442d226384a3e4f7d02ee37eaf2adfa6f298"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.2.0BETA1"}, "time": "2011-12-03T10:32:55+00:00"}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "b886898821288d305862ee9c567cc5b5cbb4c0dc"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/b886898821288d305862ee9c567cc5b5cbb4c0dc", "type": "zip", "shasum": "", "reference": "b886898821288d305862ee9c567cc5b5cbb4c0dc"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.1.4"}, "time": "2011-11-26T08:10:50+00:00"}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/common.git", "type": "git", "reference": "1c9d2ab329746637d1c6c058523ec1ee769da31c"}, "dist": {"url": "https://api.github.com/repos/doctrine/common/zipball/1c9d2ab329746637d1c6c058523ec1ee769da31c", "type": "zip", "shasum": "", "reference": "1c9d2ab329746637d1c6c058523ec1ee769da31c"}, "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.1.3"}, "time": "2011-11-21T14:50:00+00:00", "autoload": "__unset"}]}, "security-advisories": [{"advisoryId": "PKSA-gsgw-t7tw-12tc", "affectedVersions": ">=2.0.0,<2.4.3|>=2.5.0,<2.5.1"}], "last-modified": "Wed, 01 Jan 2025 22:13:22 GMT"}