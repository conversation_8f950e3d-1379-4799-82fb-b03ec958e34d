{"minified": "composer/2.0", "packages": {"symfony/polyfill-php83": [{"name": "symfony/polyfill-php83", "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "keywords": ["compatibility", "portable", "polyfill", "shim"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-php83.git", "type": "git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "type": "zip", "shasum": "", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}}, {"version": "v1.31.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php83.git", "type": "git", "reference": "dbdcdf1a4dcc2743591f1079d0c35ab1e2dcbbc9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/dbdcdf1a4dcc2743591f1079d0c35ab1e2dcbbc9", "type": "zip", "shasum": "", "reference": "dbdcdf1a4dcc2743591f1079d0c35ab1e2dcbbc9"}, "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.30.0"}, "time": "2024-06-19T12:35:24+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php83.git", "type": "git", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/86fcae159633351e5fd145d1c47de6c528f8caff", "type": "zip", "shasum": "", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00", "require": {"php": ">=7.1", "symfony/polyfill-php80": "^1.14"}}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php83.git", "type": "git", "reference": "b0f46ebbeeeda3e9d2faebdfbf4b4eae9b59fa11"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/b0f46ebbeeeda3e9d2faebdfbf4b4eae9b59fa11", "type": "zip", "shasum": "", "reference": "b0f46ebbeeeda3e9d2faebdfbf4b4eae9b59fa11"}, "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.28.0"}, "time": "2023-08-16T06:22:46+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php83.git", "type": "git", "reference": "508c652ba3ccf69f8c97f251534f229791b52a57"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/508c652ba3ccf69f8c97f251534f229791b52a57", "type": "zip", "shasum": "", "reference": "508c652ba3ccf69f8c97f251534f229791b52a57"}, "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 09:41:35 GMT"}