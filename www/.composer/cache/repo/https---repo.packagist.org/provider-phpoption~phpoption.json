{"minified": "composer/2.0", "packages": {"phpoption/phpoption": [{"name": "phpoption/phpoption", "description": "Option Type for PHP", "keywords": ["php", "language", "type", "option"], "homepage": "", "version": "1.9.3", "version_normalized": "*******", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "type": "zip", "shasum": "", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "type": "library", "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00", "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}}, {"version": "1.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/80735db690fe4fc5c76dfa7f9b770634285fa820", "type": "zip", "shasum": "", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.2"}, "time": "2023-11-12T21:59:55+00:00", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}}, {"version": "1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "dd3a383e599f49777d8b628dadbb90cae435b87e"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/dd3a383e599f49777d8b628dadbb90cae435b87e", "type": "zip", "shasum": "", "reference": "dd3a383e599f49777d8b628dadbb90cae435b87e"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.1"}, "time": "2023-02-25T19:38:58+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.32 || ^9.6.3 || ^10.0.12"}}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "dc5ff11e274a90cc1c743f66c9ad700ce50db9ab"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/dc5ff11e274a90cc1c743f66c9ad700ce50db9ab", "type": "zip", "shasum": "", "reference": "dc5ff11e274a90cc1c743f66c9ad700ce50db9ab"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.0"}, "time": "2022-07-30T15:51:26+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.8", "phpunit/phpunit": "^8.5.28 || ^9.5.21"}}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "type": "zip", "shasum": "", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.8.1"}, "time": "2021-12-04T23:24:31+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "require": {"php": "^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}}, {"version": "1.8.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "5455cb38aed4523f99977c4a12ef19da4bfe2a28"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/5455cb38aed4523f99977c4a12ef19da4bfe2a28", "type": "zip", "shasum": "", "reference": "5455cb38aed4523f99977c4a12ef19da4bfe2a28"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.8.0"}, "time": "2021-08-28T21:27:29+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^6.5.14 || ^7.0.20 || ^8.5.19 || ^9.5.8"}}, {"version": "1.7.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/994ecccd8f3283ecf5ac33254543eb0ac946d525", "type": "zip", "shasum": "", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.7.5"}, "time": "2020-07-20T17:29:33+00:00", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0 || ^8.0 || ^9.0"}}, {"version": "1.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "b2ada2ad5d8a32b89088b8adc31ecd2e3a13baf3"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/b2ada2ad5d8a32b89088b8adc31ecd2e3a13baf3", "type": "zip", "shasum": "", "reference": "b2ada2ad5d8a32b89088b8adc31ecd2e3a13baf3"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.7.4"}, "time": "2020-06-07T10:40:07+00:00", "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0", "bamarni/composer-bin-plugin": "^1.3"}}, {"version": "1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "4acfd6a4b33a509d8c88f50e5222f734b6aeebae"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/4acfd6a4b33a509d8c88f50e5222f734b6aeebae", "type": "zip", "shasum": "", "reference": "4acfd6a4b33a509d8c88f50e5222f734b6aeebae"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/master"}, "funding": [], "time": "2020-03-21T18:07:53+00:00"}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "77f7c4d2e65413aff5b5a8cc8b3caf7a28d81959"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/77f7c4d2e65413aff5b5a8cc8b3caf7a28d81959", "type": "zip", "shasum": "", "reference": "77f7c4d2e65413aff5b5a8cc8b3caf7a28d81959"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.7.2"}, "time": "2019-12-15T19:35:24+00:00", "require": {"php": "^5.5.9 || ^7.0"}, "funding": "__unset"}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "100a25207566930efd926cf205542946aa692e01"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/100a25207566930efd926cf205542946aa692e01", "type": "zip", "shasum": "", "reference": "100a25207566930efd926cf205542946aa692e01"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/master"}, "time": "2019-12-14T13:46:39+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "318ad673ba36e53e10ca510aec1c54b4f2a5f2ae"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/318ad673ba36e53e10ca510aec1c54b4f2a5f2ae", "type": "zip", "shasum": "", "reference": "318ad673ba36e53e10ca510aec1c54b4f2a5f2ae"}, "time": "2019-12-13T00:10:22+00:00"}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "a8593bf5176bf3d3f2966942c530be19b44ec87c"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/a8593bf5176bf3d3f2966942c530be19b44ec87c", "type": "zip", "shasum": "", "reference": "a8593bf5176bf3d3f2966942c530be19b44ec87c"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.6.1"}, "time": "2019-12-11T13:45:14+00:00", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "f4e7a6a1382183412246f0d361078c29fb85089e"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/f4e7a6a1382183412246f0d361078c29fb85089e", "type": "zip", "shasum": "", "reference": "f4e7a6a1382183412246f0d361078c29fb85089e"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.6.0"}, "time": "2019-11-30T20:20:49+00:00"}, {"version": "1.5.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "77fbb6dc0ba4fd330caaa97468556bb67185216f"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/77fbb6dc0ba4fd330caaa97468556bb67185216f", "type": "zip", "shasum": "", "reference": "77fbb6dc0ba4fd330caaa97468556bb67185216f"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.5.3"}, "time": "2019-12-11T13:35:56+00:00", "autoload": {"psr-0": {"PhpOption\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.7|^5.0"}}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "2ba2586380f8d2b44ad1b9feb61c371020b27793"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/2ba2586380f8d2b44ad1b9feb61c371020b27793", "type": "zip", "shasum": "", "reference": "2ba2586380f8d2b44ad1b9feb61c371020b27793"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.5.2"}, "time": "2019-11-06T22:27:00+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "894e8f93890b79f29911cce497fe811fe9d931ba"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/894e8f93890b79f29911cce497fe811fe9d931ba", "type": "zip", "shasum": "", "reference": "894e8f93890b79f29911cce497fe811fe9d931ba"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.5.1"}, "time": "2019-11-06T12:42:47+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "require-dev": {"phpunit/phpunit": "4.7.*"}}, {"version": "1.5.0", "version_normalized": "*******", "license": ["Apache2"], "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/94e644f7d2051a5f0fcf77d81605f152eecff0ed", "type": "zip", "shasum": "", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/master"}, "time": "2015-07-25T16:39:46+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "5d099bcf0393908bf4ad69cc47dafb785d51f7f5"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/5d099bcf0393908bf4ad69cc47dafb785d51f7f5", "type": "zip", "shasum": "", "reference": "5d099bcf0393908bf4ad69cc47dafb785d51f7f5"}, "time": "2014-01-09T22:37:17+00:00", "require-dev": "__unset"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "1c7e8016289d17d83ced49c56d0f266fd0568941"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/1c7e8016289d17d83ced49c56d0f266fd0568941", "type": "zip", "shasum": "", "reference": "1c7e8016289d17d83ced49c56d0f266fd0568941"}, "time": "2013-05-19T11:09:35+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "24e55357ced5bb041da1416711737b9e144505b4"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/24e55357ced5bb041da1416711737b9e144505b4", "type": "zip", "shasum": "", "reference": "24e55357ced5bb041da1416711737b9e144505b4"}, "time": "2013-03-25T02:51:40+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "617bd84bf0d918da79b06ac6765b5390b83b1321"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/617bd84bf0d918da79b06ac6765b5390b83b1321", "type": "zip", "shasum": "", "reference": "617bd84bf0d918da79b06ac6765b5390b83b1321"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.1.0"}, "time": "2013-01-19T11:01:32+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "b9c60ebf8242cf409d8734b6a757ba0ce1691493"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/b9c60ebf8242cf409d8734b6a757ba0ce1691493", "type": "zip", "shasum": "", "reference": "b9c60ebf8242cf409d8734b6a757ba0ce1691493"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.0.0"}, "time": "2012-11-18T14:14:12+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/schmittjoh/php-option.git", "type": "git", "reference": "fb0ac454d6eaa0f808e49d74f484f6a01bfd1aa1"}, "dist": {"url": "https://api.github.com/repos/schmittjoh/php-option/zipball/fb0ac454d6eaa0f808e49d74f484f6a01bfd1aa1", "type": "zip", "shasum": "", "reference": "fb0ac454d6eaa0f808e49d74f484f6a01bfd1aa1"}, "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/0.9.0"}, "time": "2012-11-13T07:58:52+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Sat, 20 Jul 2024 21:42:22 GMT"}