{"minified": "composer/2.0", "packages": {"laravel/telescope": [{"name": "laravel/telescope", "description": "An elegant debug assistant for the Laravel framework.", "keywords": ["debugging", "monitoring", "laravel"], "homepage": "", "version": "5.x-dev", "version_normalized": "5.9999999.9999999.9999999-dev", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "489b84ebb78cc990bfec129e0f828e2fcaea17c0"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/489b84ebb78cc990bfec129e0f828e2fcaea17c0", "type": "zip", "shasum": "", "reference": "489b84ebb78cc990bfec129e0f828e2fcaea17c0"}, "type": "library", "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/5.x"}, "funding": [], "time": "2025-06-19T17:20:42+00:00", "autoload": {"psr-4": {"Laravel\\Telescope\\": "src/", "Laravel\\Telescope\\Database\\Factories\\": "database/factories/"}}, "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}}, "default-branch": true, "require": {"php": "^8.0", "ext-json": "*", "symfony/var-dumper": "^5.0|^6.0|^7.0", "symfony/console": "^5.3|^6.0|^7.0", "laravel/framework": "^8.37|^9.0|^10.0|^11.0|^12.0"}, "require-dev": {"ext-gd": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "laravel/octane": "^1.4|^2.0|dev-develop", "phpstan/phpstan": "^1.10", "orchestra/testbench": "^6.40|^7.37|^8.17|^9.0|^10.0", "phpunit/phpunit": "^9.0|^10.5|^11.5"}}, {"version": "4.x-dev", "version_normalized": "4.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "1f47307083d48aa2b8b4caef934609b555c8173e"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/1f47307083d48aa2b8b4caef934609b555c8173e", "type": "zip", "shasum": "", "reference": "1f47307083d48aa2b8b4caef934609b555c8173e"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/4.x"}, "time": "2024-02-13T17:24:45+00:00", "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}, "branch-alias": {"dev-master": "4.x-dev"}}, "require": {"ext-json": "*", "symfony/var-dumper": "^5.0|^6.0", "php": "^8.0", "laravel/framework": "^8.37|^9.0|^10.0"}, "require-dev": {"ext-gd": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^9.0", "laravel/octane": "^1.4", "phpstan/phpstan": "^1.10"}}, {"version": "3.x-dev", "version_normalized": "3.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "5c00216c00d649cd29a2b5fbebaa83e13f7054a2"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/5c00216c00d649cd29a2b5fbebaa83e13f7054a2", "type": "zip", "shasum": "", "reference": "5c00216c00d649cd29a2b5fbebaa83e13f7054a2"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/3.x"}, "time": "2020-09-07T12:54:34+00:00", "autoload": {"psr-4": {"Laravel\\Telescope\\": "src/"}}, "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}, "branch-alias": {"dev-master": "3.x-dev"}}, "require": {"php": "^7.2", "ext-json": "*", "laravel/framework": "^6.0|^7.0", "moontoast/math": "^1.1", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"ext-gd": "*", "orchestra/testbench": "^4.0|^5.0"}}, {"version": "2.0.x-dev", "version_normalized": "2.0.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "7be8ea02b04aa3a755da532e91a2628c20d72114"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/7be8ea02b04aa3a755da532e91a2628c20d72114", "type": "zip", "shasum": "", "reference": "7be8ea02b04aa3a755da532e91a2628c20d72114"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/2.0"}, "time": "2020-02-18T19:42:41+00:00", "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}, "branch-alias": {"dev-master": "2.x-dev"}}, "require": {"php": "^7.1.3", "ext-json": "*", "moontoast/math": "^1.1", "laravel/framework": "~5.8.0|^6.0", "symfony/var-dumper": "^4.1"}, "require-dev": {"ext-gd": "*", "orchestra/testbench": "^3.8|^4.0"}, "default-branch": "__unset"}, {"version": "1.0.x-dev", "version_normalized": "1.0.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "f27a8698cee210e766cce93bed51b5a625dd4b02"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/f27a8698cee210e766cce93bed51b5a625dd4b02", "type": "zip", "shasum": "", "reference": "f27a8698cee210e766cce93bed51b5a625dd4b02"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/1.0"}, "time": "2019-12-09T17:43:04+00:00", "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}, "branch-alias": {"dev-master": "1.x-dev"}}, "require": {"moontoast/math": "^1.1", "symfony/var-dumper": "^4.1", "php": "^7.1.3", "ext-json": "*", "laravel/framework": "~5.7.7"}, "require-dev": {"orchestra/testbench": "~3.7", "nunomaduro/larastan": "^0.3.7"}, "funding": "__unset"}, {"version": "dev-test-environment", "version_normalized": "dev-test-environment", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "6318b49d427893bf8548df06ba9d5246da2489d0"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/6318b49d427893bf8548df06ba9d5246da2489d0", "type": "zip", "shasum": "", "reference": "6318b49d427893bf8548df06ba9d5246da2489d0"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/test-environment"}, "funding": [], "time": "2025-06-19T12:13:44+00:00", "autoload": {"psr-4": {"Laravel\\Telescope\\": "src/", "Laravel\\Telescope\\Database\\Factories\\": "database/factories/"}}, "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}}, "require": {"php": "^8.0", "ext-json": "*", "laravel/framework": "^8.37|^9.0|^10.0|^11.0|^12.0", "symfony/console": "^5.3|^6.0|^7.0", "symfony/var-dumper": "^5.0|^6.0|^7.0"}, "require-dev": {"ext-gd": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "laravel/octane": "^1.4|^2.0|dev-develop", "orchestra/testbench": "^6.40|^7.37|^8.17|^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0|^10.5|^11.5"}}, {"version": "dev-dependabot/npm_and_yarn/multi-a91bf2f4f6", "version_normalized": "dev-dependabot/npm_and_yarn/multi-a91bf2f4f6", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "99f3b479ca1344861c8002b243ac623dd76e165c"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/99f3b479ca1344861c8002b243ac623dd76e165c", "type": "zip", "shasum": "", "reference": "99f3b479ca1344861c8002b243ac623dd76e165c"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/dependabot/npm_and_yarn/multi-a91bf2f4f6"}, "time": "2025-06-18T20:02:01+00:00"}, {"version": "dev-vite", "version_normalized": "dev-vite", "source": {"url": "https://github.com/laravel/telescope.git", "type": "git", "reference": "9ff214b5c15da57da8eda6b5a7b84fdc63bd2410"}, "dist": {"url": "https://api.github.com/repos/laravel/telescope/zipball/9ff214b5c15da57da8eda6b5a7b84fdc63bd2410", "type": "zip", "shasum": "", "reference": "9ff214b5c15da57da8eda6b5a7b84fdc63bd2410"}, "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/vite"}, "time": "2023-10-26T14:25:37+00:00", "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}, "branch-alias": {"dev-master": "4.x-dev"}}, "require": {"php": "^8.0", "ext-json": "*", "laravel/framework": "^8.37|^9.0|^10.0", "symfony/var-dumper": "^5.0|^6.0"}, "require-dev": {"ext-gd": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "laravel/octane": "^1.4", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0"}}]}, "last-modified": "Thu, 19 Jun 2025 17:20:49 GMT"}