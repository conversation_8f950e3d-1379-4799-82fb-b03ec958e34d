{"minified": "composer/2.0", "packages": {"carbonphp/carbon-doctrine-types": [{"name": "carbonphp/carbon-doctrine-types", "description": "Types to use Carbon in Doctrine", "keywords": ["time", "date", "doctrine", "datetime", "carbon"], "homepage": "", "version": "3.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "source": {"url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "type": "git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "type": "zip", "shasum": "", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "type": "library", "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "type": "git", "reference": "a31d3358a2a5d6ae947df1691d1f321418a5f3d5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/a31d3358a2a5d6ae947df1691d1f321418a5f3d5", "type": "zip", "shasum": "", "reference": "a31d3358a2a5d6ae947df1691d1f321418a5f3d5"}, "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.1.0"}, "time": "2023-12-10T15:33:53+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "type": "git", "reference": "49856fbc09fe91b5433381faec60e3620ad364ad"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/49856fbc09fe91b5433381faec60e3620ad364ad", "type": "zip", "shasum": "", "reference": "49856fbc09fe91b5433381faec60e3620ad364ad"}, "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.0.0"}, "time": "2023-10-01T14:36:55+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "type": "git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "type": "zip", "shasum": "", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "time": "2023-12-11T17:09:12+00:00", "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "type": "git", "reference": "67a77972b9f398ae7068dabacc39c08aeee170d5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/67a77972b9f398ae7068dabacc39c08aeee170d5", "type": "zip", "shasum": "", "reference": "67a77972b9f398ae7068dabacc39c08aeee170d5"}, "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.0.0"}, "time": "2023-10-01T14:29:01+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "type": "git", "reference": "3c430083d0b41ceed84ecccf9dac613241d7305d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/3c430083d0b41ceed84ecccf9dac613241d7305d", "type": "zip", "shasum": "", "reference": "3c430083d0b41ceed84ecccf9dac613241d7305d"}, "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/1.0.0"}, "time": "2023-10-01T12:35:29+00:00", "require": {"php": "^7.1.8 || ^8.0"}, "require-dev": {"doctrine/dbal": ">=2.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "conflict": {"doctrine/dbal": ">=3.7.0"}}]}, "security-advisories": [], "last-modified": "Sun, 07 Apr 2024 15:16:45 GMT"}