{"minified": "composer/2.0", "packages": {"symfony/psr-http-message-bridge": [{"name": "symfony/psr-http-message-bridge", "description": "PSR HTTP message bridge", "keywords": ["http", "psr-7", "http-message", "psr-17"], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "03f2f72319e7acaf2a9f6fcbe30ef17eec51594f"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/03f2f72319e7acaf2a9f6fcbe30ef17eec51594f", "type": "zip", "shasum": "", "reference": "03f2f72319e7acaf2a9f6fcbe30ef17eec51594f"}, "type": "symfony-bridge", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-26T08:57:56+00:00", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^6.4|^7.0"}, "require-dev": {"symfony/browser-kit": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.4"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.3.0-BETA1"}}, {"version": "v7.2.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.2.0"}}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.2.0-BETA1"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "f16471bb19f6685b9ccf0a2c03c213840ae68cd6"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/f16471bb19f6685b9ccf0a2c03c213840ae68cd6", "type": "zip", "shasum": "", "reference": "f16471bb19f6685b9ccf0a2c03c213840ae68cd6"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "405a7bcd872f1563966f64be19f1362d94ce71ab"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/405a7bcd872f1563966f64be19f1362d94ce71ab", "type": "zip", "shasum": "", "reference": "405a7bcd872f1563966f64be19f1362d94ce71ab"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.4"}, "time": "2024-08-15T22:48:53+00:00"}, {"version": "v7.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "1365d10f5476f74a27cf9c2d1eee70c069019db0"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/1365d10f5476f74a27cf9c2d1eee70c069019db0", "type": "zip", "shasum": "", "reference": "1365d10f5476f74a27cf9c2d1eee70c069019db0"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.3"}, "time": "2024-07-17T06:10:24+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "9a5dbb606da711f5d40a7596ad577856f9402140"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/9a5dbb606da711f5d40a7596ad577856f9402140", "type": "zip", "shasum": "", "reference": "9a5dbb606da711f5d40a7596ad577856f9402140"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "db9f8f3077068f1fbcdd84c428241dc0f7bd87fb"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/db9f8f3077068f1fbcdd84c428241dc0f7bd87fb", "type": "zip", "shasum": "", "reference": "db9f8f3077068f1fbcdd84c428241dc0f7bd87fb"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.1.0-BETA1"}}, {"version": "v7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "d9b90feef625bdaea8bd351c543f9ee020a00c5a"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/d9b90feef625bdaea8bd351c543f9ee020a00c5a", "type": "zip", "shasum": "", "reference": "d9b90feef625bdaea8bd351c543f9ee020a00c5a"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.10"}, "time": "2024-07-17T06:06:58+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "1f1c2116a08387d4754590af7dd3f1674ebf47a6"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/1f1c2116a08387d4754590af7dd3f1674ebf47a6", "type": "zip", "shasum": "", "reference": "1f1c2116a08387d4754590af7dd3f1674ebf47a6"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "727befd41438a8feb64066871d3656d8cbdcdbe2"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/727befd41438a8feb64066871d3656d8cbdcdbe2", "type": "zip", "shasum": "", "reference": "727befd41438a8feb64066871d3656d8cbdcdbe2"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "fbc500cbcb64d3ea7469f019ab7aa717b320ff3f"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/fbc500cbcb64d3ea7469f019ab7aa717b320ff3f", "type": "zip", "shasum": "", "reference": "fbc500cbcb64d3ea7469f019ab7aa717b320ff3f"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.6"}, "time": "2024-03-28T09:20:36+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "d9fadaf9541d7c01c307e48905d7ce1dbee6bf38"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/d9fadaf9541d7c01c307e48905d7ce1dbee6bf38", "type": "zip", "shasum": "", "reference": "d9fadaf9541d7c01c307e48905d7ce1dbee6bf38"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "c5e973032e9a32c6f1bfa87d7832853b84cbaf22"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/c5e973032e9a32c6f1bfa87d7832853b84cbaf22", "type": "zip", "shasum": "", "reference": "c5e973032e9a32c6f1bfa87d7832853b84cbaf22"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.2"}, "time": "2023-12-28T19:18:20+00:00"}, {"homepage": "http://symfony.com", "version": "v7.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "b15eac617f772a497ba0c953111149de85e8a0d9"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/b15eac617f772a497ba0c953111149de85e8a0d9", "type": "zip", "shasum": "", "reference": "b15eac617f772a497ba0c953111149de85e8a0d9"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.0"}, "time": "2023-10-31T17:59:56+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "947fd4239686bf0c33b33319cd53886ad9643e87"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/947fd4239686bf0c33b33319cd53886ad9643e87", "type": "zip", "shasum": "", "reference": "947fd4239686bf0c33b33319cd53886ad9643e87"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.0.0-BETA1"}, "time": "2023-09-27T14:05:33+00:00"}, {"homepage": "https://symfony.com", "version": "v6.4.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "c9cf83326a1074f83a738fc5320945abf7fb7fec"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/c9cf83326a1074f83a738fc5320945abf7fb7fec", "type": "zip", "shasum": "", "reference": "c9cf83326a1074f83a738fc5320945abf7fb7fec"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00", "require": {"php": ">=8.1", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/framework-bundle": "^6.2|^7.0", "symfony/http-kernel": "^6.2|^7.0", "nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.2"}}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "74835ba54eca99a38f374f7a6d932fa510124773"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/74835ba54eca99a38f374f7a6d932fa510124773", "type": "zip", "shasum": "", "reference": "74835ba54eca99a38f374f7a6d932fa510124773"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.11"}, "time": "2024-08-14T13:55:58+00:00"}, {"version": "v6.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "89a24648d73e4eee30893b0da16abc454a65c53b"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/89a24648d73e4eee30893b0da16abc454a65c53b", "type": "zip", "shasum": "", "reference": "89a24648d73e4eee30893b0da16abc454a65c53b"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.10"}, "time": "2024-07-15T09:36:38+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "23a162bd446b93948a2c2f6909d80ad06195be10"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/23a162bd446b93948a2c2f6909d80ad06195be10", "type": "zip", "shasum": "", "reference": "23a162bd446b93948a2c2f6909d80ad06195be10"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.8"}, "time": "2024-05-31T14:51:39+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "e8adf6b1b46d9115f5d9247fa74bbefc459680c0"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/e8adf6b1b46d9115f5d9247fa74bbefc459680c0", "type": "zip", "shasum": "", "reference": "e8adf6b1b46d9115f5d9247fa74bbefc459680c0"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "98059dd19bae6579a294e0fe5b3dfdbeb409845a"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/98059dd19bae6579a294e0fe5b3dfdbeb409845a", "type": "zip", "shasum": "", "reference": "98059dd19bae6579a294e0fe5b3dfdbeb409845a"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.6"}, "time": "2024-03-27T22:00:14+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "49cfb0223ec64379f7154214dcc1f7c46f3c7a47"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/49cfb0223ec64379f7154214dcc1f7c46f3c7a47", "type": "zip", "shasum": "", "reference": "49cfb0223ec64379f7154214dcc1f7c46f3c7a47"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "d32f5898f163266230182432b877ab7623ff252d"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/d32f5898f163266230182432b877ab7623ff252d", "type": "zip", "shasum": "", "reference": "d32f5898f163266230182432b877ab7623ff252d"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.2"}, "time": "2023-12-28T07:55:26+00:00"}, {"homepage": "http://symfony.com", "version": "v6.4.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "3c0a6ea372085754232b502146192c069ae2c5a1"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/3c0a6ea372085754232b502146192c069ae2c5a1", "type": "zip", "shasum": "", "reference": "3c0a6ea372085754232b502146192c069ae2c5a1"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.0"}, "time": "2023-10-31T08:40:20+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "a04a95019570cc044661a643eeab56089fc6f0e3"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/a04a95019570cc044661a643eeab56089fc6f0e3", "type": "zip", "shasum": "", "reference": "a04a95019570cc044661a643eeab56089fc6f0e3"}, "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.0-BETA1"}, "time": "2023-09-23T08:58:19+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "581ca6067eb62640de5ff08ee1ba6850a0ee472e"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/581ca6067eb62640de5ff08ee1ba6850a0ee472e", "type": "zip", "shasum": "", "reference": "581ca6067eb62640de5ff08ee1ba6850a0ee472e"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.3.1"}, "time": "2023-07-26T11:53:26+00:00", "extra": {"branch-alias": {"dev-main": "2.3-dev"}}, "require": {"php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.5 || ^3.0", "symfony/http-foundation": "^5.4 || ^6.0"}, "require-dev": {"symfony/browser-kit": "^5.4 || ^6.0", "symfony/config": "^5.4 || ^6.0", "symfony/event-dispatcher": "^5.4 || ^6.0", "symfony/framework-bundle": "^5.4 || ^6.0", "symfony/http-kernel": "^5.4 || ^6.0", "symfony/phpunit-bridge": "^6.2", "nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "conflict": "__unset"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "3c62b814477165dbf07c9c18ddb413ccdadaa93c"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/3c62b814477165dbf07c9c18ddb413ccdadaa93c", "type": "zip", "shasum": "", "reference": "3c62b814477165dbf07c9c18ddb413ccdadaa93c"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.3.0"}, "time": "2023-07-25T14:44:36+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "28a732c05bbad801304ad5a5c674cf2970508993"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/28a732c05bbad801304ad5a5c674cf2970508993", "type": "zip", "shasum": "", "reference": "28a732c05bbad801304ad5a5c674cf2970508993"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.2.0"}, "time": "2023-04-21T08:40:19+00:00", "extra": {"branch-alias": {"dev-main": "2.2-dev"}}, "require": {"php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0", "symfony/http-foundation": "^5.4 || ^6.0"}}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "a125b93ef378c492e274f217874906fb9babdebb"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/a125b93ef378c492e274f217874906fb9babdebb", "type": "zip", "shasum": "", "reference": "a125b93ef378c492e274f217874906fb9babdebb"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.4"}, "time": "2022-11-28T22:46:34+00:00", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"symfony/browser-kit": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4@dev || ^6.0", "nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3"}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "d444f85dddf65c7e57c58d8e5b3a4dbb593b1840"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/d444f85dddf65c7e57c58d8e5b3a4dbb593b1840", "type": "zip", "shasum": "", "reference": "d444f85dddf65c7e57c58d8e5b3a4dbb593b1840"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.3"}, "time": "2022-09-05T10:34:54+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "type": "zip", "shasum": "", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.2"}, "time": "2021-11-05T13:13:39+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "c9012994c4b4fb23e7c57dd86b763a417a04feba"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/c9012994c4b4fb23e7c57dd86b763a417a04feba", "type": "zip", "shasum": "", "reference": "c9012994c4b4fb23e7c57dd86b763a417a04feba"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.1"}, "time": "2021-07-27T17:25:39+00:00", "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0"}, "require-dev": {"symfony/browser-kit": "^4.4 || ^5.0", "symfony/config": "^4.4 || ^5.0", "symfony/event-dispatcher": "^4.4 || ^5.0", "symfony/framework-bundle": "^4.4 || ^5.0", "symfony/http-kernel": "^4.4 || ^5.0", "symfony/phpunit-bridge": "^4.4.19 || ^5.2", "nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "81db2d4ae86e9f0049828d9343a72b9523884e5d"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/81db2d4ae86e9f0049828d9343a72b9523884e5d", "type": "zip", "shasum": "", "reference": "81db2d4ae86e9f0049828d9343a72b9523884e5d"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.0"}, "time": "2021-02-17T10:35:25+00:00", "require-dev": {"symfony/browser-kit": "^4.4 || ^5.0", "symfony/config": "^4.4 || ^5.0", "symfony/event-dispatcher": "^4.4 || ^5.0", "symfony/framework-bundle": "^4.4 || ^5.0", "symfony/http-kernel": "^4.4 || ^5.0", "symfony/phpunit-bridge": "^4.4.19 || ^5.2", "nyholm/psr7": "^1.1", "psr/log": "^1.1"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "51a21cb3ba3927d4b4bf8f25cc55763351af5f2e"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/51a21cb3ba3927d4b4bf8f25cc55763351af5f2e", "type": "zip", "shasum": "", "reference": "51a21cb3ba3927d4b4bf8f25cc55763351af5f2e"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.0.2"}, "time": "2020-09-29T08:17:46+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.0", "nyholm/psr7": "^1.1"}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "e44f249afab496b4e8c0f7461fb8140eaa4b24d2"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/e44f249afab496b4e8c0f7461fb8140eaa4b24d2", "type": "zip", "shasum": "", "reference": "e44f249afab496b4e8c0f7461fb8140eaa4b24d2"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/master"}, "time": "2020-06-25T08:21:47+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "ce709cd9c90872c08c2427b45739d5f3c781ab4f"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/ce709cd9c90872c08c2427b45739d5f3c781ab4f", "type": "zip", "shasum": "", "reference": "ce709cd9c90872c08c2427b45739d5f3c781ab4f"}, "time": "2020-01-02T08:07:11+00:00", "require": {"php": "^7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0"}, "funding": "__unset"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "9d3e80d54d9ae747ad573cad796e8e247df7b796"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/9d3e80d54d9ae747ad573cad796e8e247df7b796", "type": "zip", "shasum": "", "reference": "9d3e80d54d9ae747ad573cad796e8e247df7b796"}, "time": "2019-11-25T19:33:50+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.0", "nyholm/psr7": "^1.1", "zendframework/zend-diactoros": "^1.4.1 || ^2.0"}}, {"version": "v1.2.0", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "9ab9d71f97d5c7d35a121a7fb69f74fee95cd0ad"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/9ab9d71f97d5c7d35a121a7fb69f74fee95cd0ad", "type": "zip", "shasum": "", "reference": "9ab9d71f97d5c7d35a121a7fb69f74fee95cd0ad"}, "time": "2019-03-11T18:22:33+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "require": {"php": "^7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^3.4 || ^4.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4.20 || ^4.0", "nyholm/psr7": "^1.1", "zendframework/zend-diactoros": "^1.4.1 || ^2.0"}}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "a33352af16f78a5ff4f9d90811536abf210df12b"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/a33352af16f78a5ff4f9d90811536abf210df12b", "type": "zip", "shasum": "", "reference": "a33352af16f78a5ff4f9d90811536abf210df12b"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v1.1.2"}, "time": "2019-04-03T17:09:40+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": "^5.3.3 || ^7.0", "psr/http-message": "^1.0", "symfony/http-foundation": "^2.3.42 || ^3.4 || ^4.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4 || ^4.0"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "921f8669c36ea0148d2520c0bb7838cda14879e0"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/921f8669c36ea0148d2520c0bb7838cda14879e0", "type": "zip", "shasum": "", "reference": "921f8669c36ea0148d2520c0bb7838cda14879e0"}, "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/master"}, "time": "2019-03-11T15:23:15+00:00", "require-dev": {"symfony/phpunit-bridge": "^3.4 || ^4.0", "nyholm/psr7": "^1.1"}}, {"keywords": ["http", "psr-7", "http-message"], "version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "53c15a6a7918e6c2ab16ae370ea607fb40cab196"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/53c15a6a7918e6c2ab16ae370ea607fb40cab196", "type": "zip", "shasum": "", "reference": "53c15a6a7918e6c2ab16ae370ea607fb40cab196"}, "time": "2018-08-30T16:28:28+00:00", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}}, "require-dev": {"symfony/phpunit-bridge": "^3.4 || 4.0"}, "suggest": {"psr/http-message-implementation": "To use the HttpFoundation factory", "zendframework/zend-diactoros": "To use the Zend Diactoros factory", "psr/http-factory-implementation": "To use the PSR-17 factory"}}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "c2b757934f2d9681a287e662efbc27c41fe8ef86"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/c2b757934f2d9681a287e662efbc27c41fe8ef86", "type": "zip", "shasum": "", "reference": "c2b757934f2d9681a287e662efbc27c41fe8ef86"}, "time": "2017-12-19T00:31:44+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=5.3.3", "psr/http-message": "~1.0", "symfony/http-foundation": "~2.3|~3.0|~4.0"}, "require-dev": {"symfony/phpunit-bridge": "~3.2|4.0"}, "suggest": {"psr/http-message-implementation": "To use the HttpFoundation factory", "zendframework/zend-diactoros": "To use the Zend Diactoros factory"}}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "b2098405d8644f6dc4c36febcee6a77c0fdecdff"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/b2098405d8644f6dc4c36febcee6a77c0fdecdff", "type": "zip", "shasum": "", "reference": "b2098405d8644f6dc4c36febcee6a77c0fdecdff"}, "time": "2017-07-23T09:13:43+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "66085f246d3893cbdbcec5f5ad15ac60546cf0de"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/66085f246d3893cbdbcec5f5ad15ac60546cf0de", "type": "zip", "shasum": "", "reference": "66085f246d3893cbdbcec5f5ad15ac60546cf0de"}, "time": "2016-09-14T18:37:20+00:00", "require": {"php": ">=5.3.3", "psr/http-message": "~1.0", "symfony/http-foundation": "~2.3|~3.0"}, "require-dev": {"symfony/phpunit-bridge": "~2.7|~3.0"}}, {"version": "v0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "1c30b1781a5497ba0330769c7ddeb649d9e2f51e"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/1c30b1781a5497ba0330769c7ddeb649d9e2f51e", "type": "zip", "shasum": "", "reference": "1c30b1781a5497ba0330769c7ddeb649d9e2f51e"}, "time": "2016-09-13T23:40:43+00:00", "extra": "__unset"}, {"version": "v0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "7a5aa925c686fb8a6de909d0562fdb17502dcc76"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/7a5aa925c686fb8a6de909d0562fdb17502dcc76", "type": "zip", "shasum": "", "reference": "7a5aa925c686fb8a6de909d0562fdb17502dcc76"}, "time": "2016-08-18T14:44:25+00:00"}, {"version": "v0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/psr-http-message-bridge.git", "type": "git", "reference": "dc7e308e1dc2898a46776e2221a643cb08315453"}, "dist": {"url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/dc7e308e1dc2898a46776e2221a643cb08315453", "type": "zip", "shasum": "", "reference": "dc7e308e1dc2898a46776e2221a643cb08315453"}, "time": "2015-05-29T17:57:12+00:00", "suggest": {"zendframework/zend-diactoros": "To use the Zend Diactoros factory"}}, {"version": "v0.1", "version_normalized": "*******"}]}, "security-advisories": [], "last-modified": "Thu, 29 May 2025 07:50:00 GMT"}