{"minified": "composer/2.0", "packages": {"monolog/monolog": [{"name": "monolog/monolog", "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "keywords": ["log", "logging", "psr-3"], "homepage": "https://github.com/Seldaek/monolog", "version": "3.9.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "type": "zip", "shasum": "", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "type": "library", "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-openssl": "Required to send log messages using SSL"}, "provide": {"psr/log-implementation": "3.0.0"}}, {"version": "3.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/aef6ee73a77a66e404dd6540934a9ef1b3c855b4", "type": "zip", "shasum": "", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.8.1"}, "time": "2024-12-05T17:15:07+00:00"}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "32e515fdc02cdafbe4593e30a9350d486b125b67"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/32e515fdc02cdafbe4593e30a9350d486b125b67", "type": "zip", "shasum": "", "reference": "32e515fdc02cdafbe4593e30a9350d486b125b67"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.8.0"}, "time": "2024-11-12T13:57:08+00:00"}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "f4393b648b78a5408747de94fca38beb5f7e9ef8"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/f4393b648b78a5408747de94fca38beb5f7e9ef8", "type": "zip", "shasum": "", "reference": "f4393b648b78a5408747de94fca38beb5f7e9ef8"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.7.0"}, "time": "2024-06-28T09:40:51+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^10.5.17", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "4b18b21a5527a3d5ffdac2fd35d3ab25a9597654"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/4b18b21a5527a3d5ffdac2fd35d3ab25a9597654", "type": "zip", "shasum": "", "reference": "4b18b21a5527a3d5ffdac2fd35d3ab25a9597654"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.6.0"}, "time": "2024-04-12T21:02:21+00:00"}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c915e2634718dbc8a4a15c61b0e62e7a44e14448", "type": "zip", "shasum": "", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.5.0"}, "time": "2023-10-27T15:32:31+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^10.1", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "e2392369686d420ca32df3803de28b5d6f76867d"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/e2392369686d420ca32df3803de28b5d6f76867d", "type": "zip", "shasum": "", "reference": "e2392369686d420ca32df3803de28b5d6f76867d"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.4.0"}, "time": "2023-06-21T08:46:11+00:00"}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "9b5daeaffce5b926cac47923798bba91059e60e2"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/9b5daeaffce5b926cac47923798bba91059e60e2", "type": "zip", "shasum": "", "reference": "9b5daeaffce5b926cac47923798bba91059e60e2"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.3.1"}, "time": "2023-02-06T13:46:10+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^9.5.26", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "852643b696e755f7936e80afffc6721a20f0d15c"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/852643b696e755f7936e80afffc6721a20f0d15c", "type": "zip", "shasum": "", "reference": "852643b696e755f7936e80afffc6721a20f0d15c"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.3.0"}, "time": "2023-02-06T13:12:20+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "305444bc6fb6c89e490f4b34fa6e979584d7fa81"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/305444bc6fb6c89e490f4b34fa6e979584d7fa81", "type": "zip", "shasum": "", "reference": "305444bc6fb6c89e490f4b34fa6e979584d7fa81"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.2.0"}, "time": "2022-07-24T12:00:55+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.4", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^9.5.16", "predis/predis": "^1.1", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "0c375495d40df0207e5833dca333f963b171ff43"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/0c375495d40df0207e5833dca333f963b171ff43", "type": "zip", "shasum": "", "reference": "0c375495d40df0207e5833dca333f963b171ff43"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.1.0"}, "time": "2022-06-09T09:09:00+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^1.4", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^9.5.16", "predis/predis": "^1.1", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-openssl": "Required to send log messages using SSL"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "60ad5183b5e5d6c9d4047e9f3072d36071dcc161"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/60ad5183b5e5d6c9d4047e9f3072d36071dcc161", "type": "zip", "shasum": "", "reference": "60ad5183b5e5d6c9d4047e9f3072d36071dcc161"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.0.0"}, "time": "2022-05-10T10:39:55+00:00"}, {"version": "3.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "a71c4e02502dd04d91f1c7d72ffccf0bd11310eb"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/a71c4e02502dd04d91f1c7d72ffccf0bd11310eb", "type": "zip", "shasum": "", "reference": "a71c4e02502dd04d91f1c7d72ffccf0bd11310eb"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.0.0-RC1"}, "time": "2022-05-08T21:50:49+00:00"}, {"version": "2.10.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/5cf826f2991858b54d5c3809bee745560a1042a7", "type": "zip", "shasum": "", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.10.0"}, "time": "2024-11-12T12:43:37+00:00", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-openssl": "Required to send log messages using SSL"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}}, {"version": "2.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "a30bfe2e142720dfa990d0a7e573997f5d884215"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/a30bfe2e142720dfa990d0a7e573997f5d884215", "type": "zip", "shasum": "", "reference": "a30bfe2e142720dfa990d0a7e573997f5d884215"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.3"}, "time": "2024-04-12T20:52:51+00:00"}, {"version": "2.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "437cb3628f4cf6042cc10ae97fc2b8472e48ca1f"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/437cb3628f4cf6042cc10ae97fc2b8472e48ca1f", "type": "zip", "shasum": "", "reference": "437cb3628f4cf6042cc10ae97fc2b8472e48ca1f"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.2"}, "time": "2023-10-27T15:25:26+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}}, {"version": "2.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "f259e2b15fb95494c83f52d3caad003bbf5ffaa1"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/f259e2b15fb95494c83f52d3caad003bbf5ffaa1", "type": "zip", "shasum": "", "reference": "f259e2b15fb95494c83f52d3caad003bbf5ffaa1"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.1"}, "time": "2023-02-06T13:44:46+00:00"}, {"version": "2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "e1c0ae1528ce313a450e5e1ad782765c4a8dd3cb"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/e1c0ae1528ce313a450e5e1ad782765c4a8dd3cb", "type": "zip", "shasum": "", "reference": "e1c0ae1528ce313a450e5e1ad782765c4a8dd3cb"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.0"}, "time": "2023-02-05T13:07:32+00:00"}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "720488632c590286b88b80e62aa3d3d551ad4a50"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/720488632c590286b88b80e62aa3d3d551ad4a50", "type": "zip", "shasum": "", "reference": "720488632c590286b88b80e62aa3d3d551ad4a50"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.8.0"}, "time": "2022-07-24T11:55:47+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "5579edf28aee1190a798bfa5be8bc16c563bd524"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/5579edf28aee1190a798bfa5be8bc16c563bd524", "type": "zip", "shasum": "", "reference": "5579edf28aee1190a798bfa5be8bc16c563bd524"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.7.0"}, "time": "2022-06-09T08:59:12+00:00", "require-dev": {"ext-json": "*", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "graylog2/gelf-php": "^1.4.2", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-openssl": "Required to send log messages using SSL"}}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "247918972acd74356b0a91dfaa5adcaec069b6c0"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/247918972acd74356b0a91dfaa5adcaec069b6c0", "type": "zip", "shasum": "", "reference": "247918972acd74356b0a91dfaa5adcaec069b6c0"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.6.0"}, "time": "2022-05-10T09:36:00+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "4192345e260f1d51b365536199744b987e160edc"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/4192345e260f1d51b365536199744b987e160edc", "type": "zip", "shasum": "", "reference": "4192345e260f1d51b365536199744b987e160edc"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.5.0"}, "time": "2022-04-08T15:43:54+00:00", "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "mongodb/mongodb": "^1.8", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0", "phpstan/phpstan": "^0.12.91"}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "d7fd7450628561ba697b7097d86db72662f54aef"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/d7fd7450628561ba697b7097d86db72662f54aef", "type": "zip", "shasum": "", "reference": "d7fd7450628561ba697b7097d86db72662f54aef"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.4.0"}, "time": "2022-03-14T12:44:37+00:00"}, {"version": "2.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd4380d6fc37626e2f799f29d91195040137eba9", "type": "zip", "shasum": "", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.5"}, "time": "2021-10-01T21:08:31+00:00", "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "mongodb/mongodb": "^1.8", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0", "phpstan/phpstan": "^0.12.91"}}, {"version": "2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "437e7a1c50044b92773b361af77620efb76fff59"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/437e7a1c50044b92773b361af77620efb76fff59", "type": "zip", "shasum": "", "reference": "437e7a1c50044b92773b361af77620efb76fff59"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.4"}, "time": "2021-09-15T11:27:21+00:00", "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "mongodb/mongodb": "^1.8", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0", "phpstan/phpstan": "^0.12.91"}}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "3962ebfe206ac7ce6c754c79e2fee0c64bf1818d"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/3962ebfe206ac7ce6c754c79e2fee0c64bf1818d", "type": "zip", "shasum": "", "reference": "3962ebfe206ac7ce6c754c79e2fee0c64bf1818d"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.3"}, "time": "2021-09-14T18:40:13+00:00", "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "mongodb/mongodb": "^1.8", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0", "phpstan/phpstan": "^0.12.91"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0"}}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "71312564759a7db5b789296369c1a264efc43aad"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/71312564759a7db5b789296369c1a264efc43aad", "type": "zip", "shasum": "", "reference": "71312564759a7db5b789296369c1a264efc43aad"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.2"}, "time": "2021-07-23T07:42:52+00:00", "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "mongodb/mongodb": "^1.8", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0", "phpstan/phpstan": "^0.12.91"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome", "ext-mbstring": "Allow to work properly with unicode symbols"}, "provide": {"psr/log-implementation": "1.0.0"}}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "9738e495f288eec0b187e310b7cdbbb285777dbe"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/9738e495f288eec0b187e310b7cdbbb285777dbe", "type": "zip", "shasum": "", "reference": "9738e495f288eec0b187e310b7cdbbb285777dbe"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.1"}, "time": "2021-07-14T11:56:39+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "df991fd88693ab703aa403413d83e15f688dae33"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/df991fd88693ab703aa403413d83e15f688dae33", "type": "zip", "shasum": "", "reference": "df991fd88693ab703aa403413d83e15f688dae33"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.0"}, "time": "2021-07-05T11:34:13+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "type": "zip", "shasum": "", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.2.0"}, "time": "2020-12-14T13:15:25+00:00", "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "mongodb/mongodb": "^1.8", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0", "phpstan/phpstan": "^0.12.59"}}, {"homepage": "http://github.com/Seldaek/monolog", "version": "2.1.1", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "f9eee5cec93dfb313a38b6b288741e84e53f02d5"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/f9eee5cec93dfb313a38b6b288741e84e53f02d5", "type": "zip", "shasum": "", "reference": "f9eee5cec93dfb313a38b6b288741e84e53f02d5"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.1.1"}, "time": "2020-07-23T08:41:23+00:00", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^6.0", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "^5.3|^6.0"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "38914429aac460e8e4616c8cb486ecb40ec90bb1"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/38914429aac460e8e4616c8cb486ecb40ec90bb1", "type": "zip", "shasum": "", "reference": "38914429aac460e8e4616c8cb486ecb40ec90bb1"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.1.0"}, "time": "2020-05-22T08:12:19+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c861fcba2ca29404dc9e617eedd9eff4616986b8"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c861fcba2ca29404dc9e617eedd9eff4616986b8", "type": "zip", "shasum": "", "reference": "c861fcba2ca29404dc9e617eedd9eff4616986b8"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.0.2"}, "time": "2019-12-20T14:22:59+00:00", "require": {"php": "^7.2", "psr/log": "^1.0.1"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^6.0", "graylog2/gelf-php": "^1.4.2", "jakub-onderka/php-parallel-lint": "^0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.3", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "^5.3|^6.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "f9d56fd2f5533322caccdfcddbb56aedd622ef1c"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/f9d56fd2f5533322caccdfcddbb56aedd622ef1c", "type": "zip", "shasum": "", "reference": "f9d56fd2f5533322caccdfcddbb56aedd622ef1c"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.0.1"}, "time": "2019-11-13T10:27:43+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "68545165e19249013afd1d6f7485aecff07a2d22"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/68545165e19249013afd1d6f7485aecff07a2d22", "type": "zip", "shasum": "", "reference": "68545165e19249013afd1d6f7485aecff07a2d22"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.0.0"}, "time": "2019-08-30T09:56:44+00:00"}, {"version": "2.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "bf486002a08ca7cb156540e1a38c7be70fa8ed59"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/bf486002a08ca7cb156540e1a38c7be70fa8ed59", "type": "zip", "shasum": "", "reference": "bf486002a08ca7cb156540e1a38c7be70fa8ed59"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.0.0-beta2"}, "time": "2019-07-06T13:17:41+00:00", "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^6.0", "graylog2/gelf-php": "^1.4.2", "jakub-onderka/php-parallel-lint": "^0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^7.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}}, {"version": "2.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "0ad73a526f4b5e67312e94fb7f60c1bdefc284b9"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/0ad73a526f4b5e67312e94fb7f60c1bdefc284b9", "type": "zip", "shasum": "", "reference": "0ad73a526f4b5e67312e94fb7f60c1bdefc284b9"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.0.0-beta1"}, "time": "2018-12-08T17:16:32+00:00", "require": {"php": "^7.1", "psr/log": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^7.3", "graylog2/gelf-php": "^1.4.2", "sentry/sentry": "^1.9", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "^5.3|^6.0", "php-console/php-console": "^3.1.3", "jakub-onderka/php-parallel-lint": "^0.9", "predis/predis": "^1.1", "phpspec/prophecy": "^1.6.1", "elasticsearch/elasticsearch": "^6.0", "rollbar/rollbar": "^1.3"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "sentry/sentry": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}}, {"version": "1.27.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "type": "zip", "shasum": "", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "time": "2022-06-09T08:53:42+00:00", "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "sentry/sentry": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "^5.3|^6.0", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "sentry/sentry": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}, "extra": "__unset"}, {"version": "1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "52ebd235c1f7e0d5e1b16464b695a28335f8e44a"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/52ebd235c1f7e0d5e1b16464b695a28335f8e44a", "type": "zip", "shasum": "", "reference": "52ebd235c1f7e0d5e1b16464b695a28335f8e44a"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.0"}, "time": "2022-03-13T20:29:46+00:00"}, {"version": "1.26.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c6b00f05152ae2c9b04a448f99c7590beb6042f5"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c6b00f05152ae2c9b04a448f99c7590beb6042f5", "type": "zip", "shasum": "", "reference": "c6b00f05152ae2c9b04a448f99c7590beb6042f5"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.26.1"}, "time": "2021-05-28T08:32:12+00:00"}, {"version": "1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "2209ddd84e7ef1256b7af205d0717fb62cfc9c33"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/2209ddd84e7ef1256b7af205d0717fb62cfc9c33", "type": "zip", "shasum": "", "reference": "2209ddd84e7ef1256b7af205d0717fb62cfc9c33"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.26.0"}, "time": "2020-12-14T12:56:38+00:00"}, {"version": "1.25.5", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "1817faadd1846cd08be9a49e905dc68823bc38c0"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/1817faadd1846cd08be9a49e905dc68823bc38c0", "type": "zip", "shasum": "", "reference": "1817faadd1846cd08be9a49e905dc68823bc38c0"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.25.5"}, "time": "2020-07-23T08:35:51+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "sentry/sentry": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "^5.3|^6.0", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0"}}, {"version": "1.25.4", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "3022efff205e2448b560c833c6fbbf91c3139168"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/3022efff205e2448b560c833c6fbbf91c3139168", "type": "zip", "shasum": "", "reference": "3022efff205e2448b560c833c6fbbf91c3139168"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.25.4"}, "time": "2020-05-22T07:31:27+00:00"}, {"version": "1.25.3", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "fa82921994db851a8becaf3787a9e73c5976b6f1"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/fa82921994db851a8becaf3787a9e73c5976b6f1", "type": "zip", "shasum": "", "reference": "fa82921994db851a8becaf3787a9e73c5976b6f1"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.25.3"}, "time": "2019-12-20T14:15:16+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "sentry/sentry": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "^5.3|^6.0", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0", "jakub-onderka/php-parallel-lint": "0.9"}}, {"version": "1.25.2", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "d5e2fb341cb44f7e2ab639d12a1e5901091ec287"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/d5e2fb341cb44f7e2ab639d12a1e5901091ec287", "type": "zip", "shasum": "", "reference": "d5e2fb341cb44f7e2ab639d12a1e5901091ec287"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.25.2"}, "time": "2019-11-13T10:00:05+00:00"}, {"version": "1.25.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "70e65a5470a42cfec1a7da00d30edb6e617e8dcf"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/70e65a5470a42cfec1a7da00d30edb6e617e8dcf", "type": "zip", "shasum": "", "reference": "70e65a5470a42cfec1a7da00d30edb6e617e8dcf"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.25.1"}, "time": "2019-09-06T13:49:17+00:00"}, {"version": "1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c5dcc05defbaf8780c728c1ea31b1a0704d44f56"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c5dcc05defbaf8780c728c1ea31b1a0704d44f56", "type": "zip", "shasum": "", "reference": "c5dcc05defbaf8780c728c1ea31b1a0704d44f56"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.25.0"}, "time": "2019-09-06T12:21:24+00:00"}, {"version": "1.24.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "bfc9ebb28f97e7a24c45bdc3f0ff482e47bb0266"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/bfc9ebb28f97e7a24c45bdc3f0ff482e47bb0266", "type": "zip", "shasum": "", "reference": "bfc9ebb28f97e7a24c45bdc3f0ff482e47bb0266"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.24.0"}, "time": "2018-11-05T09:00:11+00:00"}, {"version": "1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd8c787753b3a2ad11bc60c063cff1358a32a3b4", "type": "zip", "shasum": "", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.23.0"}, "time": "2017-06-19T01:22:40+00:00"}, {"version": "1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "1e044bc4b34e91743943479f1be7a1d5eb93add0"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/1e044bc4b34e91743943479f1be7a1d5eb93add0", "type": "zip", "shasum": "", "reference": "1e044bc4b34e91743943479f1be7a1d5eb93add0"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.22.1"}, "time": "2017-03-13T07:08:03+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "sentry/sentry": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0", "jakub-onderka/php-parallel-lint": "0.9"}}, {"version": "1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "bad29cb8d18ab0315e6c477751418a82c850d558"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/bad29cb8d18ab0315e6c477751418a82c850d558", "type": "zip", "shasum": "", "reference": "bad29cb8d18ab0315e6c477751418a82c850d558"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.22.0"}, "time": "2016-11-26T00:15:39+00:00"}, {"version": "1.21.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "f42fbdfd53e306bda545845e4dbfd3e72edb4952"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/f42fbdfd53e306bda545845e4dbfd3e72edb4952", "type": "zip", "shasum": "", "reference": "f42fbdfd53e306bda545845e4dbfd3e72edb4952"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.21.0"}, "time": "2016-07-29T03:23:52+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "sentry/sentry": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0", "jakub-onderka/php-parallel-lint": "0.9"}}, {"version": "1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "55841909e2bcde01b5318c35f2b74f8ecc86e037"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/55841909e2bcde01b5318c35f2b74f8ecc86e037", "type": "zip", "shasum": "", "reference": "55841909e2bcde01b5318c35f2b74f8ecc86e037"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.20.0"}, "time": "2016-07-02T14:02:10+00:00"}, {"version": "1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "5f56ed5212dc509c8dc8caeba2715732abb32dbf"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/5f56ed5212dc509c8dc8caeba2715732abb32dbf", "type": "zip", "shasum": "", "reference": "5f56ed5212dc509c8dc8caeba2715732abb32dbf"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.19.0"}, "time": "2016-04-12T18:29:35+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "raven/raven": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0", "jakub-onderka/php-parallel-lint": "0.9"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}}, {"version": "1.18.2", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "064b38c16790249488e7a8b987acf1c9d7383c09"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/064b38c16790249488e7a8b987acf1c9d7383c09", "type": "zip", "shasum": "", "reference": "064b38c16790249488e7a8b987acf1c9d7383c09"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.18.2"}, "time": "2016-04-02T13:12:58+00:00"}, {"version": "1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "a5f2734e8c16f3aa21b3da09715d10e15b4d2d45"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/a5f2734e8c16f3aa21b3da09715d10e15b4d2d45", "type": "zip", "shasum": "", "reference": "a5f2734e8c16f3aa21b3da09715d10e15b4d2d45"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.18.1"}, "time": "2016-03-13T16:08:35+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "raven/raven": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9", "videlalvaro/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0", "jakub-onderka/php-parallel-lint": "0.9"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "videlalvaro/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}}, {"version": "1.18.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "e19b764b5c855580e8ffa7e615f72c10fd2f99cc"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/e19b764b5c855580e8ffa7e615f72c10fd2f99cc", "type": "zip", "shasum": "", "reference": "e19b764b5c855580e8ffa7e615f72c10fd2f99cc"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.18.0"}, "time": "2016-03-01T18:00:40+00:00"}, {"version": "1.17.2", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "bee7f0dc9c3e0b69a6039697533dca1e845c8c24"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/bee7f0dc9c3e0b69a6039697533dca1e845c8c24", "type": "zip", "shasum": "", "reference": "bee7f0dc9c3e0b69a6039697533dca1e845c8c24"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.17.2"}, "time": "2015-10-14T12:51:02+00:00", "extra": {"branch-alias": {"dev-master": "1.16.x-dev"}}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "videlalvaro/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}}, {"version": "1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "0524c87587ab85bc4c2d6f5b41253ccb930a5422"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/0524c87587ab85bc4c2d6f5b41253ccb930a5422", "type": "zip", "shasum": "", "reference": "0524c87587ab85bc4c2d6f5b41253ccb930a5422"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.17.1"}, "time": "2015-08-31T09:17:37+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "raven/raven": "~0.11", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9", "videlalvaro/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0"}}, {"version": "1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "877ae631713cc961952df713ae785735b90df682"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/877ae631713cc961952df713ae785735b90df682", "type": "zip", "shasum": "", "reference": "877ae631713cc961952df713ae785735b90df682"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.17.0"}, "time": "2015-08-30T11:40:25+00:00"}, {"version": "1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c0c0b4bee3aabce7182876b0d912ef2595563db7"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c0c0b4bee3aabce7182876b0d912ef2595563db7", "type": "zip", "shasum": "", "reference": "c0c0b4bee3aabce7182876b0d912ef2595563db7"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.16.0"}, "time": "2015-08-09T17:44:44+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "raven/raven": "~0.8", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9", "videlalvaro/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0"}}, {"version": "1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "dc5150cc608f2334c72c3b6a553ec9668a4156b0"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/dc5150cc608f2334c72c3b6a553ec9668a4156b0", "type": "zip", "shasum": "", "reference": "dc5150cc608f2334c72c3b6a553ec9668a4156b0"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.15.0"}, "time": "2015-07-12T13:54:09+00:00", "extra": {"branch-alias": {"dev-master": "1.15.x-dev"}}}, {"version": "1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "b287fbbe1ca27847064beff2bad7fb6920bf08cc"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/b287fbbe1ca27847064beff2bad7fb6920bf08cc", "type": "zip", "shasum": "", "reference": "b287fbbe1ca27847064beff2bad7fb6920bf08cc"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.14.0"}, "time": "2015-06-19T13:29:54+00:00", "extra": {"branch-alias": {"dev-master": "1.14.x-dev"}}}, {"version": "1.13.1", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c31a2c4e8db5da8b46c74cf275d7f109c0f249ac"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c31a2c4e8db5da8b46c74cf275d7f109c0f249ac", "type": "zip", "shasum": "", "reference": "c31a2c4e8db5da8b46c74cf275d7f109c0f249ac"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.13.1"}, "time": "2015-03-09T09:58:04+00:00", "extra": {"branch-alias": {"dev-master": "1.13.x-dev"}}, "require-dev": {"phpunit/phpunit": "~4.0", "graylog2/gelf-php": "~1.0", "raven/raven": "~0.5", "ruflin/elastica": "0.90.*", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "~2.4, >2.4.8", "videlalvaro/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "~5.3"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "videlalvaro/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar"}}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "c41c218e239b50446fd883acb1ecfd4b770caeae"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/c41c218e239b50446fd883acb1ecfd4b770caeae", "type": "zip", "shasum": "", "reference": "c41c218e239b50446fd883acb1ecfd4b770caeae"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.13.0"}, "time": "2015-03-05T01:12:12+00:00"}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "1fbe8c2641f2b163addf49cc5e18f144bec6b19f"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/1fbe8c2641f2b163addf49cc5e18f144bec6b19f", "type": "zip", "shasum": "", "reference": "1fbe8c2641f2b163addf49cc5e18f144bec6b19f"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.12.0"}, "time": "2014-12-29T21:29:35+00:00", "extra": {"branch-alias": {"dev-master": "1.12.x-dev"}}, "require-dev": {"phpunit/phpunit": "~4.0", "graylog2/gelf-php": "~1.0", "raven/raven": "~0.5", "ruflin/elastica": "0.90.*", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "~2.4, >2.4.8", "videlalvaro/php-amqplib": "~2.4"}}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "ec3961874c43840e96da3a8a1ed20d8c73d7e5aa"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/ec3961874c43840e96da3a8a1ed20d8c73d7e5aa", "type": "zip", "shasum": "", "reference": "ec3961874c43840e96da3a8a1ed20d8c73d7e5aa"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.11.0"}, "time": "2014-09-30T13:30:58+00:00", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "require-dev": {"phpunit/phpunit": "~3.7.0", "graylog2/gelf-php": "~1.0", "raven/raven": "~0.5", "ruflin/elastica": "0.90.*", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "~2.4, >2.4.8", "videlalvaro/php-amqplib": "~2.4"}}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "25b16e801979098cb2f120e697bfce454b18bf23"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/25b16e801979098cb2f120e697bfce454b18bf23", "type": "zip", "shasum": "", "reference": "25b16e801979098cb2f120e697bfce454b18bf23"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.10.0"}, "time": "2014-06-04T16:30:04+00:00", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "require-dev": {"phpunit/phpunit": "~3.7.0", "graylog2/gelf-php": "~1.0", "raven/raven": "~0.5", "ruflin/elastica": "0.90.*", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "~2.4, >2.4.8"}, "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar"}, "provide": "__unset"}, {"version": "1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "65026b610f8c19e61d7242f600530677b0466aac"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/65026b610f8c19e61d7242f600530677b0466aac", "type": "zip", "shasum": "", "reference": "65026b610f8c19e61d7242f600530677b0466aac"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.9.1"}, "time": "2014-04-24T13:29:03+00:00", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "1afc39690e7414412face1f8cbf67b73db34485c"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/1afc39690e7414412face1f8cbf67b73db34485c", "type": "zip", "shasum": "", "reference": "1afc39690e7414412face1f8cbf67b73db34485c"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.9.0"}, "time": "2014-04-20T16:41:26+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "392ef35fd470638e08d0160d6b1cbab63cb23174"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/392ef35fd470638e08d0160d6b1cbab63cb23174", "type": "zip", "shasum": "", "reference": "392ef35fd470638e08d0160d6b1cbab63cb23174"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.8.0"}, "time": "2014-03-23T19:50:26+00:00", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "6225b22de9dcf36546be3a0b2fa8e3d986153f57"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/6225b22de9dcf36546be3a0b2fa8e3d986153f57", "type": "zip", "shasum": "", "reference": "6225b22de9dcf36546be3a0b2fa8e3d986153f57"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.7.0"}, "time": "2013-11-14T19:48:31+00:00", "autoload": {"psr-0": {"Monolog": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "require-dev": {"phpunit/phpunit": "~3.7.0", "mlehner/gelf-php": "1.0.*", "raven/raven": "0.5.*", "ruflin/elastica": "0.90.*", "doctrine/couchdb": "dev-master", "aws/aws-sdk-php": "~2.4.8"}, "suggest": {"mlehner/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "f72392d0e6eb855118f5a84e89ac2d257c704abd"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/f72392d0e6eb855118f5a84e89ac2d257c704abd", "type": "zip", "shasum": "", "reference": "f72392d0e6eb855118f5a84e89ac2d257c704abd"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.6.0"}, "time": "2013-07-28T22:38:30+00:00", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "require-dev": {"mlehner/gelf-php": "1.0.*", "raven/raven": "0.5.*", "doctrine/couchdb": "dev-master"}, "suggest": {"mlehner/gelf-php": "Allow sending log messages to a GrayLog2 server", "raven/raven": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "583618d5cd2115a52101694aca87afb182b3e567"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/583618d5cd2115a52101694aca87afb182b3e567", "type": "zip", "shasum": "", "reference": "583618d5cd2115a52101694aca87afb182b3e567"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.5.0"}, "time": "2013-04-23T10:09:48+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "require-dev": {"mlehner/gelf-php": "1.0.*", "raven/raven": "0.3.*", "doctrine/couchdb": "dev-master"}}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "3295de82be06b3bbcd336983ddf8c50724430180"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/3295de82be06b3bbcd336983ddf8c50724430180", "type": "zip", "shasum": "", "reference": "3295de82be06b3bbcd336983ddf8c50724430180"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.4.1"}, "time": "2013-04-01T10:04:58+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "32fe28af60b4da9a5b0cef024138afacc0c01eeb"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/32fe28af60b4da9a5b0cef024138afacc0c01eeb", "type": "zip", "shasum": "", "reference": "32fe28af60b4da9a5b0cef024138afacc0c01eeb"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.4.0"}, "time": "2013-02-13T18:06:51+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "47eb599b4aad36b66e818ed72ebf939e2fb311be"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/47eb599b4aad36b66e818ed72ebf939e2fb311be", "type": "zip", "shasum": "", "reference": "47eb599b4aad36b66e818ed72ebf939e2fb311be"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.3.1"}, "time": "2013-01-11T10:23:20+00:00", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "25a97abf904120a386c546be11c3b58f1f9e6f37"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/25a97abf904120a386c546be11c3b58f1f9e6f37", "type": "zip", "shasum": "", "reference": "25a97abf904120a386c546be11c3b58f1f9e6f37"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.3.0"}, "time": "2013-01-07T20:26:46+00:00"}, {"description": "Logging for PHP 5.3", "keywords": ["log", "logging"], "version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "d16496318c3e08e3bccfc3866e104e49cf25488a"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/d16496318c3e08e3bccfc3866e104e49cf25488a", "type": "zip", "shasum": "", "reference": "d16496318c3e08e3bccfc3866e104e49cf25488a"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.2.1"}, "time": "2012-08-29T11:53:20+00:00", "require": {"php": ">=5.3.0"}, "require-dev": {"mlehner/gelf-php": "1.0.*"}, "suggest": {"mlehner/gelf-php": "Allow sending log messages to a GrayLog2 server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "7940ae31ce4687d875d2bb5aa277bb3802203fe1"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/7940ae31ce4687d875d2bb5aa277bb3802203fe1", "type": "zip", "shasum": "", "reference": "7940ae31ce4687d875d2bb5aa277bb3802203fe1"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.2.0"}, "time": "2012-08-18T17:27:13+00:00", "extra": "__unset"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "abc80e0db8ad31c03b373977fc997e980800f9c2"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/abc80e0db8ad31c03b373977fc997e980800f9c2", "type": "zip", "shasum": "", "reference": "abc80e0db8ad31c03b373977fc997e980800f9c2"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.1.0"}, "time": "2012-04-23T16:27:40+00:00", "suggest": {"mlehner/gelf-php": "Allow sending log messages to a GrayLog2 server"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "b704c49a3051536f67f2d39f13568f74615b9922"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/b704c49a3051536f67f2d39f13568f74615b9922", "type": "zip", "shasum": "", "reference": "b704c49a3051536f67f2d39f13568f74615b9922"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.0.2"}, "time": "2011-10-24T09:39:02+00:00", "require-dev": "__unset", "suggest": "__unset"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "303b8a83c87d5c6d749926cf02620465a5dcd0f2"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/303b8a83c87d5c6d749926cf02620465a5dcd0f2", "type": "zip", "shasum": "", "reference": "303b8a83c87d5c6d749926cf02620465a5dcd0f2"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.0.1"}, "time": "2011-08-25T20:42:58+00:00", "autoload": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "433b98d4218c181bae01865901aac045585e8a1a"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/433b98d4218c181bae01865901aac045585e8a1a", "type": "zip", "shasum": "", "reference": "433b98d4218c181bae01865901aac045585e8a1a"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.0.0"}, "time": "2011-07-07T16:21:02+00:00"}, {"version": "1.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/Seldaek/monolog.git", "type": "git", "reference": "5e651a82b4b03d267da6084720ada0cd398c8d16"}, "dist": {"url": "https://api.github.com/repos/Seldaek/monolog/zipball/5e651a82b4b03d267da6084720ada0cd398c8d16", "type": "zip", "shasum": "", "reference": "5e651a82b4b03d267da6084720ada0cd398c8d16"}, "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.0.0-RC1"}, "time": "2011-07-01T19:29:40+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-dmw8-jd8k-q3c6", "affectedVersions": ">=1.8.0,<1.12.0"}], "last-modified": "Wed, 26 Mar 2025 23:59:45 GMT"}