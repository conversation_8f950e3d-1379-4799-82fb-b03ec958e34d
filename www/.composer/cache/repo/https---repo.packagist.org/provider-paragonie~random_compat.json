{"minified": "composer/2.0", "packages": {"paragonie/random_compat": [{"name": "paragonie/random_compat", "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["random", "polyfill", "csprng", "pseudorandom"], "homepage": "", "version": "v9.99.100", "version_normalized": "**********", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "type": "zip", "shasum": "", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "type": "library", "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "funding": [], "time": "2020-10-15T08:29:30+00:00", "require": {"php": ">= 7"}, "require-dev": {"vimeo/psalm": "^1", "phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}}, {"version": "v9.99.99", "version_normalized": "*********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "type": "zip", "shasum": "", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "time": "2018-07-02T15:55:56+00:00", "require": {"php": "^7"}, "funding": "__unset"}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "type": "zip", "shasum": "", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae"}, "funding": [], "time": "2022-02-16T17:07:03+00:00", "autoload": {"files": ["lib/random.php"]}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}}, {"version": "v2.0.20", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "0f1f60250fccffeaf5dda91eea1c018aed1adc2a"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/0f1f60250fccffeaf5dda91eea1c018aed1adc2a", "type": "zip", "shasum": "", "reference": "0f1f60250fccffeaf5dda91eea1c018aed1adc2a"}, "time": "2021-04-17T09:33:01+00:00", "require-dev": {"phpunit/phpunit": "4.*|5.*"}}, {"version": "v2.0.19", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "446fc9faa5c2a9ddf65eb7121c0af7e857295241"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/446fc9faa5c2a9ddf65eb7121c0af7e857295241", "type": "zip", "shasum": "", "reference": "446fc9faa5c2a9ddf65eb7121c0af7e857295241"}, "time": "2020-10-15T10:06:57+00:00"}, {"version": "v2.0.18", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "type": "zip", "shasum": "", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db"}, "time": "2019-01-03T20:59:08+00:00", "funding": "__unset"}, {"version": "v2.0.17", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "29af24f25bab834fcbb38ad2a69fa93b867e070d"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/29af24f25bab834fcbb38ad2a69fa93b867e070d", "type": "zip", "shasum": "", "reference": "29af24f25bab834fcbb38ad2a69fa93b867e070d"}, "time": "2018-07-04T16:31:37+00:00"}, {"version": "v2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "7fd9ad98e675e88bb5c1c0aa350762433f608a92"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/7fd9ad98e675e88bb5c1c0aa350762433f608a92", "type": "zip", "shasum": "", "reference": "7fd9ad98e675e88bb5c1c0aa350762433f608a92"}, "time": "2018-07-04T16:14:12+00:00"}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "10bcb46e8f3d365170f6de9d05245aa066b81f09"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/10bcb46e8f3d365170f6de9d05245aa066b81f09", "type": "zip", "shasum": "", "reference": "10bcb46e8f3d365170f6de9d05245aa066b81f09"}, "time": "2018-06-08T15:26:40+00:00"}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "f6ce7dd93628088e1017fb5dd73b0b9fec7df9e5"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/f6ce7dd93628088e1017fb5dd73b0b9fec7df9e5", "type": "zip", "shasum": "", "reference": "f6ce7dd93628088e1017fb5dd73b0b9fec7df9e5"}, "time": "2018-06-06T17:40:22+00:00"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "cbe0b11b78140bc62a921fec33a730fdaa6540d6"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/cbe0b11b78140bc62a921fec33a730fdaa6540d6", "type": "zip", "shasum": "", "reference": "cbe0b11b78140bc62a921fec33a730fdaa6540d6"}, "time": "2018-06-06T05:04:48+00:00"}, {"keywords": ["random", "csprng", "pseudorandom"], "version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "258c89a6b97de7dfaf5b8c7607d0478e236b04fb"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/258c89a6b97de7dfaf5b8c7607d0478e236b04fb", "type": "zip", "shasum": "", "reference": "258c89a6b97de7dfaf5b8c7607d0478e236b04fb"}, "time": "2018-04-04T21:24:14+00:00"}, {"version": "v2.0.11", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "5da4d3c796c275c55f057af5a643ae297d96b4d8"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/5da4d3c796c275c55f057af5a643ae297d96b4d8", "type": "zip", "shasum": "", "reference": "5da4d3c796c275c55f057af5a643ae297d96b4d8"}, "time": "2017-09-27T21:40:39+00:00"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "634bae8e911eefa89c1abfbf1b66da679ac8f54d"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/634bae8e911eefa89c1abfbf1b66da679ac8f54d", "type": "zip", "shasum": "", "reference": "634bae8e911eefa89c1abfbf1b66da679ac8f54d"}, "time": "2017-03-13T16:27:32+00:00"}, {"version": "v2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "6968531206671f94377b01dc7888d5d1b858a01b"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/6968531206671f94377b01dc7888d5d1b858a01b", "type": "zip", "shasum": "", "reference": "6968531206671f94377b01dc7888d5d1b858a01b"}, "time": "2017-03-03T20:43:42+00:00"}, {"version": "v2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "eeb4c23c7b84c1dc87db3c92856bd57ff1844ca4"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/eeb4c23c7b84c1dc87db3c92856bd57ff1844ca4", "type": "zip", "shasum": "", "reference": "eeb4c23c7b84c1dc87db3c92856bd57ff1844ca4"}, "time": "2017-03-03T14:11:40+00:00"}, {"version": "v2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "b5ea1ef3d8ff10c307ba8c5945c2f134e503278f"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/b5ea1ef3d8ff10c307ba8c5945c2f134e503278f", "type": "zip", "shasum": "", "reference": "b5ea1ef3d8ff10c307ba8c5945c2f134e503278f"}, "time": "2017-02-27T17:11:23+00:00"}, {"version": "v2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "2bccb674209ed6f08412637b0c49456566c135d4"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/2bccb674209ed6f08412637b0c49456566c135d4", "type": "zip", "shasum": "", "reference": "2bccb674209ed6f08412637b0c49456566c135d4"}, "time": "2017-02-27T17:05:44+00:00"}, {"version": "v2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "411e7526015651c64887eb0bfe5d56f528a7c7e1"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/411e7526015651c64887eb0bfe5d56f528a7c7e1", "type": "zip", "shasum": "", "reference": "411e7526015651c64887eb0bfe5d56f528a7c7e1"}, "time": "2017-02-27T16:48:24+00:00"}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "a9b97968bcde1c4de2a5ec6cbd06a0f6c919b46e"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/a9b97968bcde1c4de2a5ec6cbd06a0f6c919b46e", "type": "zip", "shasum": "", "reference": "a9b97968bcde1c4de2a5ec6cbd06a0f6c919b46e"}, "time": "2016-11-07T23:38:38+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "c0125896dbb151380ab47e96c621741e79623beb"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/c0125896dbb151380ab47e96c621741e79623beb", "type": "zip", "shasum": "", "reference": "c0125896dbb151380ab47e96c621741e79623beb"}, "time": "2016-10-17T15:23:22+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "088c04e2f261c33bed6ca5245491cfca69195ccf"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/088c04e2f261c33bed6ca5245491cfca69195ccf", "type": "zip", "shasum": "", "reference": "088c04e2f261c33bed6ca5245491cfca69195ccf"}, "time": "2016-04-03T06:00:07+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "76e90f747b769b347fe584e8015a014549107d35"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/76e90f747b769b347fe584e8015a014549107d35", "type": "zip", "shasum": "", "reference": "76e90f747b769b347fe584e8015a014549107d35"}, "time": "2016-03-18T20:36:13+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "321a59fed499a5624b0e40cb5c824ae6116e0c18"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/321a59fed499a5624b0e40cb5c824ae6116e0c18", "type": "zip", "shasum": "", "reference": "321a59fed499a5624b0e40cb5c824ae6116e0c18"}, "time": "2016-03-18T17:17:33+00:00"}, {"version": "v1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "9b3899e3c3ddde89016f576edb8c489708ad64cd"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/9b3899e3c3ddde89016f576edb8c489708ad64cd", "type": "zip", "shasum": "", "reference": "9b3899e3c3ddde89016f576edb8c489708ad64cd"}, "time": "2018-04-04T21:48:54+00:00"}, {"version": "v1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "965cdeb01fdcab7653253aa81d40441d261f1e66"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/965cdeb01fdcab7653253aa81d40441d261f1e66", "type": "zip", "shasum": "", "reference": "965cdeb01fdcab7653253aa81d40441d261f1e66"}, "time": "2017-03-13T16:22:52+00:00"}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "c7e26a21ba357863de030f0b9e701c7d04593774"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/c7e26a21ba357863de030f0b9e701c7d04593774", "type": "zip", "shasum": "", "reference": "c7e26a21ba357863de030f0b9e701c7d04593774"}, "time": "2016-03-18T20:34:03+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "a93889dfb1c0ae465b62236ede8d00d3ea260952"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/a93889dfb1c0ae465b62236ede8d00d3ea260952", "type": "zip", "shasum": "", "reference": "a93889dfb1c0ae465b62236ede8d00d3ea260952"}, "time": "2016-03-18T17:16:04+00:00"}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "7088908cca6bad27b3105c86b1fb013f1e383f6f"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/7088908cca6bad27b3105c86b1fb013f1e383f6f", "type": "zip", "shasum": "", "reference": "7088908cca6bad27b3105c86b1fb013f1e383f6f"}, "time": "2016-03-18T15:57:52+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "ff76e1a660669bb56ba74ba0583fd8d41417bf39"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/ff76e1a660669bb56ba74ba0583fd8d41417bf39", "type": "zip", "shasum": "", "reference": "ff76e1a660669bb56ba74ba0583fd8d41417bf39"}, "time": "2016-03-17T16:11:57+00:00"}, {"version": "v1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "0b441c047be7a1e829fb106687284b0f8cf77db7"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/0b441c047be7a1e829fb106687284b0f8cf77db7", "type": "zip", "shasum": "", "reference": "0b441c047be7a1e829fb106687284b0f8cf77db7"}, "time": "2016-03-18T15:55:46+00:00"}, {"version": "v1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "b3313b618f4edd76523572531d5d7e22fe747430"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/b3313b618f4edd76523572531d5d7e22fe747430", "type": "zip", "shasum": "", "reference": "b3313b618f4edd76523572531d5d7e22fe747430"}, "time": "2016-03-11T19:54:08+00:00"}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "f078eba3bcf140fd69b5fcc3ea5ac809abf729dc"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/f078eba3bcf140fd69b5fcc3ea5ac809abf729dc", "type": "zip", "shasum": "", "reference": "f078eba3bcf140fd69b5fcc3ea5ac809abf729dc"}, "time": "2016-02-29T17:25:04+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "b0e69d10852716b2ccbdff69c75c477637220790"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/b0e69d10852716b2ccbdff69c75c477637220790", "type": "zip", "shasum": "", "reference": "b0e69d10852716b2ccbdff69c75c477637220790"}, "time": "2016-02-06T03:52:05+00:00"}, {"version": "1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "e6f80ab77885151908d0ec743689ca700886e8b0"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/e6f80ab77885151908d0ec743689ca700886e8b0", "type": "zip", "shasum": "", "reference": "e6f80ab77885151908d0ec743689ca700886e8b0"}, "time": "2016-01-29T16:19:52+00:00"}, {"version": "1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "dd8998b7c846f6909f4e7a5f67fabebfc412a4f7"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/dd8998b7c846f6909f4e7a5f67fabebfc412a4f7", "type": "zip", "shasum": "", "reference": "dd8998b7c846f6909f4e7a5f67fabebfc412a4f7"}, "time": "2016-01-06T13:31:20+00:00"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "d762ee5b099a29044603cd4649851e81aa66cb47"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/d762ee5b099a29044603cd4649851e81aa66cb47", "type": "zip", "shasum": "", "reference": "d762ee5b099a29044603cd4649851e81aa66cb47"}, "time": "2015-12-10T14:48:13+00:00"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "b3cbb3782fc25f0b3154a89d896d81d99b87cfa3"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/b3cbb3782fc25f0b3154a89d896d81d99b87cfa3", "type": "zip", "shasum": "", "reference": "b3cbb3782fc25f0b3154a89d896d81d99b87cfa3"}, "time": "2015-12-09T19:35:44+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "5f727f8859ceb0f6ccf5d5e73a382b501abf5d43"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/5f727f8859ceb0f6ccf5d5e73a382b501abf5d43", "type": "zip", "shasum": "", "reference": "5f727f8859ceb0f6ccf5d5e73a382b501abf5d43"}, "time": "2015-12-09T17:01:19+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "a208865a5aeffc2dbbef2a5b3409887272d93f32"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/a208865a5aeffc2dbbef2a5b3409887272d93f32", "type": "zip", "shasum": "", "reference": "a208865a5aeffc2dbbef2a5b3409887272d93f32"}, "time": "2015-12-01T02:52:15+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "19f765b66c6fbb56ee3b11bc16d52e38eebdc295"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/19f765b66c6fbb56ee3b11bc16d52e38eebdc295", "type": "zip", "shasum": "", "reference": "19f765b66c6fbb56ee3b11bc16d52e38eebdc295"}, "time": "2015-11-10T00:45:41+00:00"}, {"version": "1.0.10", "version_normalized": "********", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "2fa50aa2f17066fa74ba00d943e8cee1a98284af"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/2fa50aa2f17066fa74ba00d943e8cee1a98284af", "type": "zip", "shasum": "", "reference": "2fa50aa2f17066fa74ba00d943e8cee1a98284af"}, "time": "2015-10-23T13:21:37+00:00", "require-dev": "__unset"}, {"version": "1.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "7647ac58c35ece044b939e01c85dbaa73ed26351"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/7647ac58c35ece044b939e01c85dbaa73ed26351", "type": "zip", "shasum": "", "reference": "7647ac58c35ece044b939e01c85dbaa73ed26351"}, "time": "2015-10-20T15:22:41+00:00"}, {"version": "1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "7fe2420f8ad46aa08d5b317c92a148611d366d88"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/7fe2420f8ad46aa08d5b317c92a148611d366d88", "type": "zip", "shasum": "", "reference": "7fe2420f8ad46aa08d5b317c92a148611d366d88"}, "time": "2015-10-18T21:39:26+00:00"}, {"version": "1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "df1207683dfbd1399f0fa931a368082a28fed50b"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/df1207683dfbd1399f0fa931a368082a28fed50b", "type": "zip", "shasum": "", "reference": "df1207683dfbd1399f0fa931a368082a28fed50b"}, "time": "2015-10-16T14:10:15+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "9a0ce69360aa22413a96f2bf93f61235e2c18486"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/9a0ce69360aa22413a96f2bf93f61235e2c18486", "type": "zip", "shasum": "", "reference": "9a0ce69360aa22413a96f2bf93f61235e2c18486"}, "time": "2015-10-16T03:43:13+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "f667aa2000a192adfa53332335a2ae0c1cb9ea6e"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/f667aa2000a192adfa53332335a2ae0c1cb9ea6e", "type": "zip", "shasum": "", "reference": "f667aa2000a192adfa53332335a2ae0c1cb9ea6e"}, "time": "2015-10-08T12:57:25+00:00", "suggest": "__unset"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "456d84c8823207a18fb146c29f8b9e3aa0a9a31c"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/456d84c8823207a18fb146c29f8b9e3aa0a9a31c", "type": "zip", "shasum": "", "reference": "456d84c8823207a18fb146c29f8b9e3aa0a9a31c"}, "time": "2015-10-02T22:19:38+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "369ecee47fb94326bbe9fba785dc081331d80a89"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/369ecee47fb94326bbe9fba785dc081331d80a89", "type": "zip", "shasum": "", "reference": "369ecee47fb94326bbe9fba785dc081331d80a89"}, "time": "2015-10-02T14:15:52+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "298476979e3e014af0b8d7d1c04c3f1915bffd8f"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/298476979e3e014af0b8d7d1c04c3f1915bffd8f", "type": "zip", "shasum": "", "reference": "298476979e3e014af0b8d7d1c04c3f1915bffd8f"}, "time": "2015-09-23T04:09:57+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "fdc471fc186e13c8ac5aa8bd90fb3f4f8853637c"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/fdc471fc186e13c8ac5aa8bd90fb3f4f8853637c", "type": "zip", "shasum": "", "reference": "fdc471fc186e13c8ac5aa8bd90fb3f4f8853637c"}, "time": "2015-09-10T18:40:52+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "a1d9f267eb8b8ad560e54e397a5ed1e3b78097d1"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/a1d9f267eb8b8ad560e54e397a5ed1e3b78097d1", "type": "zip", "shasum": "", "reference": "a1d9f267eb8b8ad560e54e397a5ed1e3b78097d1"}, "time": "2015-09-07T01:49:23+00:00"}, {"version": "v0.9.7", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "f3419a01144c55acbb2c4ab86bdc55e65f23cfed"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/f3419a01144c55acbb2c4ab86bdc55e65f23cfed", "type": "zip", "shasum": "", "reference": "f3419a01144c55acbb2c4ab86bdc55e65f23cfed"}, "time": "2015-09-01T06:49:33+00:00", "require": "__unset"}, {"version": "0.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "d6efc0a466f973cdf8235fc3a53dfc022354c32c"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/d6efc0a466f973cdf8235fc3a53dfc022354c32c", "type": "zip", "shasum": "", "reference": "d6efc0a466f973cdf8235fc3a53dfc022354c32c"}, "time": "2015-07-31T10:15:59+00:00"}, {"version": "0.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "4a004b95a0f0f32f24935ddcc845b6c0a4fd0a84"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/4a004b95a0f0f32f24935ddcc845b6c0a4fd0a84", "type": "zip", "shasum": "", "reference": "4a004b95a0f0f32f24935ddcc845b6c0a4fd0a84"}, "time": "2015-07-27T17:14:01+00:00"}, {"version": "0.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "1a7b60368f25784c5156dcdca2c7201c8c9eadb1"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/1a7b60368f25784c5156dcdca2c7201c8c9eadb1", "type": "zip", "shasum": "", "reference": "1a7b60368f25784c5156dcdca2c7201c8c9eadb1"}, "time": "2015-07-22T16:57:56+00:00"}, {"description": "", "version": "0.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "c55702685e7ad6735b2fef15ca3da188ee147901"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/c55702685e7ad6735b2fef15ca3da188ee147901", "type": "zip", "shasum": "", "reference": "c55702685e7ad6735b2fef15ca3da188ee147901"}, "time": "2015-07-17T02:09:05+00:00"}, {"version": "0.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "92b380c7a2afd43b03658bf09b8551c00bee11e2"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/92b380c7a2afd43b03658bf09b8551c00bee11e2", "type": "zip", "shasum": "", "reference": "92b380c7a2afd43b03658bf09b8551c00bee11e2"}, "time": "2015-07-10T00:00:20+00:00"}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/random_compat.git", "type": "git", "reference": "4e803df23a557891fedf52c5c9af700a50dd1805"}, "dist": {"url": "https://api.github.com/repos/paragonie/random_compat/zipball/4e803df23a557891fedf52c5c9af700a50dd1805", "type": "zip", "shasum": "", "reference": "4e803df23a557891fedf52c5c9af700a50dd1805"}, "time": "2015-07-07T21:23:16+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-cncr-q695-v65g", "affectedVersions": "<2.0"}], "last-modified": "Fri, 17 May 2024 23:30:51 GMT"}