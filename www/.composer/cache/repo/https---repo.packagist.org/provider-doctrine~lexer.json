{"minified": "composer/2.0", "packages": {"doctrine/lexer": [{"name": "doctrine/lexer", "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "keywords": ["annotations", "php", "parser", "doc<PERSON>", "lexer"], "homepage": "https://www.doctrine-project.org/projects/lexer.html", "version": "3.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "type": "zip", "shasum": "", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "type": "library", "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "84a527db05647743d50373e0ec53a152f2cde568"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/84a527db05647743d50373e0ec53a152f2cde568", "type": "zip", "shasum": "", "reference": "84a527db05647743d50373e0ec53a152f2cde568"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.0"}, "time": "2022-12-15T16:57:16+00:00", "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.0"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "type": "zip", "shasum": "", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "time": "2024-02-05T11:35:39+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/deprecations": "^1.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "type": "zip", "shasum": "", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.0"}, "time": "2022-12-14T08:49:07+00:00", "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "3cf140b81e55d5d640f73367d829db7e3023ef69"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/3cf140b81e55d5d640f73367d829db7e3023ef69", "type": "zip", "shasum": "", "reference": "3cf140b81e55d5d640f73367d829db7e3023ef69"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.0.0"}, "time": "2022-12-11T10:51:23+00:00"}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "type": "zip", "shasum": "", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "time": "2022-02-28T11:07:21+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "9c50f840f257bbb941e6f4a0e94ccf5db5c3f76c"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/9c50f840f257bbb941e6f4a0e94ccf5db5c3f76c", "type": "zip", "shasum": "", "reference": "9c50f840f257bbb941e6f4a0e94ccf5db5c3f76c"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.2"}, "time": "2022-01-12T08:27:12+00:00", "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "type": "zip", "shasum": "", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.1"}, "time": "2020-05-25T17:44:05+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6", "type": "zip", "shasum": "", "reference": "5242d66dbeb21a30dd8a3e66bf7a73b66e05e1f6"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.0"}, "time": "2019-10-30T14:39:59+00:00", "require": {"php": "^7.2"}, "funding": "__unset"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "e17f069ede36f7534b95adec71910ed1b49c74ea"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/e17f069ede36f7534b95adec71910ed1b49c74ea", "type": "zip", "shasum": "", "reference": "e17f069ede36f7534b95adec71910ed1b49c74ea"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.1.0"}, "time": "2019-07-30T19:33:28+00:00", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}}, {"version": "1.0.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/1febd6c3ef84253d7c815bed85fc622ad207a9f8", "type": "zip", "shasum": "", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8"}, "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.0.2"}, "time": "2019-06-08T11:03:04+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.5"}}, {"description": "Base library for a lexer that can be used in Top-Down, Recursive Descent Parsers.", "keywords": ["parser", "lexer"], "homepage": "http://www.doctrine-project.org", "version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/83893c552fd2045dd78aef794c31e694c37c0b8c", "type": "zip", "shasum": "", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "support": {"source": "https://github.com/doctrine/lexer/tree/master"}, "time": "2014-09-09T13:34:57+00:00", "autoload": {"psr-0": {"Doctrine\\Common\\Lexer\\": "lib/"}}, "require-dev": "__unset"}, {"version": "v1.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>", "homepage": "http://www.instaclick.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/doctrine/lexer.git", "type": "git", "reference": "2f708a85bb3aab5d99dab8be435abd73e0b18acb"}, "dist": {"url": "https://api.github.com/repos/doctrine/lexer/zipball/2f708a85bb3aab5d99dab8be435abd73e0b18acb", "type": "zip", "shasum": "", "reference": "2f708a85bb3aab5d99dab8be435abd73e0b18acb"}, "support": {"source": "https://github.com/doctrine/lexer/tree/v1.0"}, "time": "2013-01-12T18:59:04+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Fri, 05 Apr 2024 12:18:44 GMT"}