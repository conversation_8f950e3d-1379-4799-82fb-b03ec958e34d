{"minified": "composer/2.0", "packages": {"doctrine/annotations": [{"name": "doctrine/annotations", "description": "Docblock Annotations Parser", "keywords": ["annotations", "parser", "doc<PERSON>"], "homepage": "https://www.doctrine-project.org/projects/annotations.html", "version": "2.0.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/901c2ee5d26eb64ff43c47976e114bf00843acf7", "type": "zip", "shasum": "", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7"}, "type": "library", "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.2"}, "funding": [], "time": "2024-09-05T10:17:24+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "require": {"php": "^7.2 || ^8.0", "ext-tokenizer": "*", "doctrine/lexer": "^2 || ^3", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "type": "zip", "shasum": "", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.1"}, "time": "2023-02-02T22:02:53+00:00", "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6", "vimeo/psalm": "^4.10"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "d02c9f3742044e17d5fa8d28d8402a2d95c33302"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/d02c9f3742044e17d5fa8d28d8402a2d95c33302", "type": "zip", "shasum": "", "reference": "d02c9f3742044e17d5fa8d28d8402a2d95c33302"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.0"}, "time": "2022-12-19T18:17:20+00:00"}, {"version": "1.14.4", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "type": "zip", "shasum": "", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00", "require": {"php": "^7.1 || ^8.0", "ext-tokenizer": "*", "doctrine/lexer": "^1 || ^2", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}}, {"version": "1.14.3", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "type": "zip", "shasum": "", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00", "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}}, {"version": "1.14.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "ad785217c1e9555a7d6c6c8c9f406395a5e2882b"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/ad785217c1e9555a7d6c6c8c9f406395a5e2882b", "type": "zip", "shasum": "", "reference": "ad785217c1e9555a7d6c6c8c9f406395a5e2882b"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.2"}, "time": "2022-12-15T06:48:22+00:00"}, {"version": "1.14.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "9e034d7a70032d422169f27d8759e8d84abb4f51"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/9e034d7a70032d422169f27d8759e8d84abb4f51", "type": "zip", "shasum": "", "reference": "9e034d7a70032d422169f27d8759e8d84abb4f51"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.1"}, "time": "2022-12-12T12:46:12+00:00"}, {"version": "1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "3587ab58646bc515b2e03bbd3cfcd3682e8df5bf"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/3587ab58646bc515b2e03bbd3cfcd3682e8df5bf", "type": "zip", "shasum": "", "reference": "3587ab58646bc515b2e03bbd3cfcd3682e8df5bf"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.0"}, "time": "2022-12-11T18:25:48+00:00"}, {"version": "1.13.3", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "648b0343343565c4a056bfc8392201385e8d89f0"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/648b0343343565c4a056bfc8392201385e8d89f0", "type": "zip", "shasum": "", "reference": "648b0343343565c4a056bfc8392201385e8d89f0"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.3"}, "time": "2022-07-02T10:48:51+00:00", "require": {"php": "^7.1 || ^8.0", "ext-tokenizer": "*", "doctrine/lexer": "1.*", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2", "vimeo/psalm": "^4.10"}, "suggest": "__unset"}, {"version": "1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "5b668aef16090008790395c02c893b1ba13f7e08"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/5b668aef16090008790395c02c893b1ba13f7e08", "type": "zip", "shasum": "", "reference": "5b668aef16090008790395c02c893b1ba13f7e08"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.2"}, "time": "2021-08-05T19:00:23+00:00", "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}}, {"version": "1.13.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "e6e7b7d5b45a2f2abc5460cc6396480b2b1d321f"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/e6e7b7d5b45a2f2abc5460cc6396480b2b1d321f", "type": "zip", "shasum": "", "reference": "e6e7b7d5b45a2f2abc5460cc6396480b2b1d321f"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.1"}, "time": "2021-05-16T18:07:53+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "03cb2123a67d4be806554fe670d0adc298199808"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/03cb2123a67d4be806554fe670d0adc298199808", "type": "zip", "shasum": "", "reference": "03cb2123a67d4be806554fe670d0adc298199808"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.0"}, "time": "2021-04-29T07:39:39+00:00", "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}}, {"version": "1.12.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/b17c5014ef81d212ac539f07a1001832df1b6d3b", "type": "zip", "shasum": "", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.12.1"}, "time": "2021-02-21T21:00:45+00:00", "require": {"php": "^7.1 || ^8.0", "ext-tokenizer": "*", "doctrine/lexer": "1.*"}, "require-dev": {"doctrine/cache": "1.*", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^9.1.5"}}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "ebec9b1708c95d7602245c8172773f6b4e0c3be1"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/ebec9b1708c95d7602245c8172773f6b4e0c3be1", "type": "zip", "shasum": "", "reference": "ebec9b1708c95d7602245c8172773f6b4e0c3be1"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.12.0"}, "time": "2021-02-14T13:22:18+00:00"}, {"version": "1.11.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "b81688bc95a16b76b2849bb67004d8f34ddfffd7"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/b81688bc95a16b76b2849bb67004d8f34ddfffd7", "type": "zip", "shasum": "", "reference": "b81688bc95a16b76b2849bb67004d8f34ddfffd7"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.11.2"}, "time": "2021-01-12T23:01:21+00:00"}, {"version": "1.11.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "ce77a7ba1770462cd705a91a151b6c3746f9c6ad"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/ce77a7ba1770462cd705a91a151b6c3746f9c6ad", "type": "zip", "shasum": "", "reference": "ce77a7ba1770462cd705a91a151b6c3746f9c6ad"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.11.1"}, "time": "2020-10-26T10:28:16+00:00", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "88fb6fb1dae011de24dd6b632811c1ff5c2928f5"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/88fb6fb1dae011de24dd6b632811c1ff5c2928f5", "type": "zip", "shasum": "", "reference": "88fb6fb1dae011de24dd6b632811c1ff5c2928f5"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.11.0"}, "time": "2020-10-17T22:05:33+00:00"}, {"homepage": "http://www.doctrine-project.org", "version": "1.10.4", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "bfe91e31984e2ba76df1c1339681770401ec262f"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/bfe91e31984e2ba76df1c1339681770401ec262f", "type": "zip", "shasum": "", "reference": "bfe91e31984e2ba76df1c1339681770401ec262f"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.10.x"}, "time": "2020-08-10T19:35:50+00:00", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "require-dev": {"doctrine/cache": "1.*", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^9.1.5"}}, {"version": "1.10.3", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "5db60a4969eba0e0c197a19c077780aadbc43c5d"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/5db60a4969eba0e0c197a19c077780aadbc43c5d", "type": "zip", "shasum": "", "reference": "5db60a4969eba0e0c197a19c077780aadbc43c5d"}, "time": "2020-05-25T17:24:27+00:00", "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^7.5"}}, {"version": "1.10.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "b9d758e831c70751155c698c2f7df4665314a1cb"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/b9d758e831c70751155c698c2f7df4665314a1cb", "type": "zip", "shasum": "", "reference": "b9d758e831c70751155c698c2f7df4665314a1cb"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.10.2"}, "time": "2020-04-20T09:18:32+00:00", "require": {"php": "^7.1", "ext-tokenizer": "*", "doctrine/lexer": "1.*"}}, {"version": "1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "5eb79f3dbdffed6544e1fc287572c0f462bd29bb"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/5eb79f3dbdffed6544e1fc287572c0f462bd29bb", "type": "zip", "shasum": "", "reference": "5eb79f3dbdffed6544e1fc287572c0f462bd29bb"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.10.x"}, "time": "2020-04-02T12:33:25+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "233e36632422cfc5cc499f14ad6c1476b50cb7b1"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/233e36632422cfc5cc499f14ad6c1476b50cb7b1", "type": "zip", "shasum": "", "reference": "233e36632422cfc5cc499f14ad6c1476b50cb7b1"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/master"}, "time": "2020-04-02T08:15:55+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "3b95eb9761ef97e2239c9834908dd3c2003e2352"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/3b95eb9761ef97e2239c9834908dd3c2003e2352", "type": "zip", "shasum": "", "reference": "3b95eb9761ef97e2239c9834908dd3c2003e2352"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.9"}, "time": "2020-04-01T10:56:29+00:00", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "904dca4eb10715b92569fbcd79e201d5c349b6bc"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/904dca4eb10715b92569fbcd79e201d5c349b6bc", "type": "zip", "shasum": "", "reference": "904dca4eb10715b92569fbcd79e201d5c349b6bc"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.8"}, "time": "2019-10-01T18:55:10+00:00", "require": {"php": "^7.1", "doctrine/lexer": "1.*"}, "funding": "__unset"}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "fa4c4e861e809d6a1103bd620cce63ed91aedfeb"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/fa4c4e861e809d6a1103bd620cce63ed91aedfeb", "type": "zip", "shasum": "", "reference": "fa4c4e861e809d6a1103bd620cce63ed91aedfeb"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/v1.7.0"}, "time": "2019-08-08T18:11:40+00:00", "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^7.5@dev"}}, {"version": "v1.6.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "53120e0eb10355388d6ccbe462f1fea34ddadb24"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/53120e0eb10355388d6ccbe462f1fea34ddadb24", "type": "zip", "shasum": "", "reference": "53120e0eb10355388d6ccbe462f1fea34ddadb24"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/v1.6.1"}, "time": "2019-03-25T19:12:02+00:00", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^6.4"}}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "c7f2050c68a9ab0bdb0f98567ec08d80ea7d24d5"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/c7f2050c68a9ab0bdb0f98567ec08d80ea7d24d5", "type": "zip", "shasum": "", "reference": "c7f2050c68a9ab0bdb0f98567ec08d80ea7d24d5"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/master"}, "time": "2017-12-06T07:11:42+00:00"}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "5beebb01b025c94e93686b7a0ed3edae81fe3e7f"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/5beebb01b025c94e93686b7a0ed3edae81fe3e7f", "type": "zip", "shasum": "", "reference": "5beebb01b025c94e93686b7a0ed3edae81fe3e7f"}, "time": "2017-07-22T10:58:02+00:00", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^5.7"}}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "54cacc9b81758b14e3ce750f205a393d52339e97"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/54cacc9b81758b14e3ce750f205a393d52339e97", "type": "zip", "shasum": "", "reference": "54cacc9b81758b14e3ce750f205a393d52339e97"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/v1.4.0"}, "time": "2017-02-24T16:22:25+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "require": {"php": "^5.6 || ^7.0", "doctrine/lexer": "1.*"}}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "bd4461328621bde0ae6b1b2675fbc6aca4ceb558"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/bd4461328621bde0ae6b1b2675fbc6aca4ceb558", "type": "zip", "shasum": "", "reference": "bd4461328621bde0ae6b1b2675fbc6aca4ceb558"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/v1.3.1"}, "time": "2016-12-30T15:59:45+00:00", "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^5.6.1"}}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "30e07cf03edc3cd3ef579d0dd4dd8c58250799a5"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/30e07cf03edc3cd3ef579d0dd4dd8c58250799a5", "type": "zip", "shasum": "", "reference": "30e07cf03edc3cd3ef579d0dd4dd8c58250799a5"}, "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/master"}, "time": "2016-10-24T11:45:47+00:00"}, {"version": "v1.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "type": "zip", "shasum": "", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535"}, "support": {"source": "https://github.com/doctrine/annotations/tree/master"}, "time": "2015-08-31T12:32:49+00:00", "autoload": {"psr-0": {"Doctrine\\Common\\Annotations\\": "lib/"}}, "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "require": {"php": ">=5.3.2", "doctrine/lexer": "1.*"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "4.*"}}, {"version": "v1.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "f4a91702ca3cd2e568c3736aa031ed00c3752af4"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/f4a91702ca3cd2e568c3736aa031ed00c3752af4", "type": "zip", "shasum": "", "reference": "f4a91702ca3cd2e568c3736aa031ed00c3752af4"}, "time": "2015-06-17T12:21:22+00:00"}, {"version": "v1.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "6eeadf5fd5d714065a59d5da8cbcd24196da667a"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/6eeadf5fd5d714065a59d5da8cbcd24196da667a", "type": "zip", "shasum": "", "reference": "6eeadf5fd5d714065a59d5da8cbcd24196da667a"}, "time": "2015-06-17T12:13:27+00:00"}, {"version": "v1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "b5202eb9e83f8db52e0e58867e0a46e63be8332e"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/b5202eb9e83f8db52e0e58867e0a46e63be8332e", "type": "zip", "shasum": "", "reference": "b5202eb9e83f8db52e0e58867e0a46e63be8332e"}, "time": "2014-12-23T22:40:37+00:00"}, {"version": "v1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "eeda578cbe24a170331a1cfdf78be723412df7a4"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/eeda578cbe24a170331a1cfdf78be723412df7a4", "type": "zip", "shasum": "", "reference": "eeda578cbe24a170331a1cfdf78be723412df7a4"}, "time": "2014-12-20T20:49:38+00:00"}, {"version": "v1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "d63108433190110c0ceb8be6460dda6f78348950"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/d63108433190110c0ceb8be6460dda6f78348950", "type": "zip", "shasum": "", "reference": "d63108433190110c0ceb8be6460dda6f78348950"}, "time": "2014-12-18T18:30:08+00:00"}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "6a6bec0670bb6e71a263b08bc1b98ea242928633"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/6a6bec0670bb6e71a263b08bc1b98ea242928633", "type": "zip", "shasum": "", "reference": "6a6bec0670bb6e71a263b08bc1b98ea242928633"}, "time": "2014-09-25T16:45:30+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.jwage.com/", "role": "Creator"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>", "homepage": "http://www.instaclick.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "d9b1a37e9351ddde1f19f09a02e3d6ee92e82efd"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/d9b1a37e9351ddde1f19f09a02e3d6ee92e82efd", "type": "zip", "shasum": "", "reference": "d9b1a37e9351ddde1f19f09a02e3d6ee92e82efd"}, "time": "2014-07-06T15:52:21+00:00"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "40db0c96985aab2822edbc4848b3bd2429e02670"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/40db0c96985aab2822edbc4848b3bd2429e02670", "type": "zip", "shasum": "", "reference": "40db0c96985aab2822edbc4848b3bd2429e02670"}, "time": "2013-06-16T21:33:03+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require-dev": {"doctrine/cache": "1.*"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "7d728453a367178a644d83d5f3e86bdcb816c4e0"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/7d728453a367178a644d83d5f3e86bdcb816c4e0", "type": "zip", "shasum": "", "reference": "7d728453a367178a644d83d5f3e86bdcb816c4e0"}, "time": "2013-04-20T08:30:17+00:00"}, {"version": "v1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "d2bb4eaf82f74fd6964503b4826d3963e134e400"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/d2bb4eaf82f74fd6964503b4826d3963e134e400", "type": "zip", "shasum": "", "reference": "d2bb4eaf82f74fd6964503b4826d3963e134e400"}, "support": {"source": "https://github.com/doctrine/annotations/tree/v1.1"}, "time": "2013-03-17T14:03:10+00:00", "extra": "__unset"}, {"version": "v1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/annotations.git", "type": "git", "reference": "fae359b3efd908e407a0105ff8956b5c94ddca8e"}, "dist": {"url": "https://api.github.com/repos/doctrine/annotations/zipball/fae359b3efd908e407a0105ff8956b5c94ddca8e", "type": "zip", "shasum": "", "reference": "fae359b3efd908e407a0105ff8956b5c94ddca8e"}, "support": {"source": "https://github.com/doctrine/annotations/tree/v1.0"}, "time": "2013-01-12T19:23:32+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-1cs3-hr6c-fftt", "affectedVersions": ">=1.0.0,<1.2.7"}], "last-modified": "Thu, 05 Sep 2024 14:54:54 GMT"}