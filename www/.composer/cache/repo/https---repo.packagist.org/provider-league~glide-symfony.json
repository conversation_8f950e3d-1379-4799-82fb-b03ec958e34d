{"minified": "composer/2.0", "packages": {"league/glide-symfony": [{"name": "league/glide-symfony", "description": "Glide adapter for Symfony", "keywords": [], "homepage": "http://glide.thephpleague.com", "version": "2.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}], "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "c88eaeb89b5ff964ded3c26f19080010d0118cd7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/c88eaeb89b5ff964ded3c26f19080010d0118cd7", "type": "zip", "shasum": "", "reference": "c88eaeb89b5ff964ded3c26f19080010d0118cd7"}, "type": "library", "support": {"issues": "https://github.com/thephpleague/glide-symfony/issues", "source": "https://github.com/thephpleague/glide-symfony/tree/2.0.1"}, "funding": [], "time": "2023-12-14T15:52:00+00:00", "autoload": {"psr-4": {"League\\Glide\\": "src/"}}, "require": {"league/glide": "^2.0", "symfony/http-foundation": "^2.3|^3.0|^4.0|^5.0|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "phpunit/phpunit": "^8.5|^9.4"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "8d0b117d5335699c374288a7cd6a041b7612e198"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/8d0b117d5335699c374288a7cd6a041b7612e198", "type": "zip", "shasum": "", "reference": "8d0b117d5335699c374288a7cd6a041b7612e198"}, "support": {"issues": "https://github.com/thephpleague/glide-symfony/issues", "source": "https://github.com/thephpleague/glide-symfony/tree/2.0.0"}, "time": "2021-12-01T13:25:09+00:00", "require": {"league/glide": "^2.0", "symfony/http-foundation": "^2.3|^3.0|^4.0|^5.0|^6.0"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "8162ec0e0b070e53e88a840a67208ec4baec9291"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/8162ec0e0b070e53e88a840a67208ec4baec9291", "type": "zip", "shasum": "", "reference": "8162ec0e0b070e53e88a840a67208ec4baec9291"}, "support": {"issues": "https://github.com/thephpleague/glide-symfony/issues", "source": "https://github.com/thephpleague/glide-symfony/tree/1.0.4"}, "time": "2020-03-05T12:38:10+00:00", "require": {"league/glide": "^1.0", "symfony/http-foundation": "^2.3|^3.0|^4.0|^5.0"}, "require-dev": {"mockery/mockery": "^0.9", "phpunit/phpunit": "^4.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "8d0b117d5335699c374288a7cd6a041b7612e198"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/8d0b117d5335699c374288a7cd6a041b7612e198", "type": "zip", "shasum": "", "reference": "8d0b117d5335699c374288a7cd6a041b7612e198"}, "support": {"issues": "https://github.com/thephpleague/glide-symfony/issues", "source": "https://github.com/thephpleague/glide-symfony/tree/1.1.0"}, "time": "2021-12-01T13:25:09+00:00", "require": {"league/glide": "^2.0", "symfony/http-foundation": "^2.3|^3.0|^4.0|^5.0|^6.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "phpunit/phpunit": "^8.5|^9.4"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "8162ec0e0b070e53e88a840a67208ec4baec9291"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/8162ec0e0b070e53e88a840a67208ec4baec9291", "type": "zip", "shasum": "", "reference": "8162ec0e0b070e53e88a840a67208ec4baec9291"}, "support": {"issues": "https://github.com/thephpleague/glide-symfony/issues", "source": "https://github.com/thephpleague/glide-symfony/tree/master"}, "time": "2020-03-05T12:38:10+00:00", "require": {"league/glide": "^1.0", "symfony/http-foundation": "^2.3|^3.0|^4.0|^5.0"}, "require-dev": {"mockery/mockery": "^0.9", "phpunit/phpunit": "^4.0"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "94b43e1d34e6be11f2996254d33748f4da123ec5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/94b43e1d34e6be11f2996254d33748f4da123ec5", "type": "zip", "shasum": "", "reference": "94b43e1d34e6be11f2996254d33748f4da123ec5"}, "time": "2018-02-10T14:10:07+00:00", "require": {"league/glide": "^1.0", "symfony/http-foundation": "^2.3|^3.0|^4.0"}, "funding": "__unset"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "2dc271c959aa86c060261e2a93c493a54f98efbb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/2dc271c959aa86c060261e2a93c493a54f98efbb", "type": "zip", "shasum": "", "reference": "2dc271c959aa86c060261e2a93c493a54f98efbb"}, "time": "2016-07-01T16:05:08+00:00", "require": {"league/glide": "^1.0", "symfony/http-foundation": "^2.3|^3.0"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "6cfc12039976fc77483667073d5d242756990b65"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/6cfc12039976fc77483667073d5d242756990b65", "type": "zip", "shasum": "", "reference": "6cfc12039976fc77483667073d5d242756990b65"}, "time": "2015-12-26T16:11:01+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide-symfony.git", "type": "git", "reference": "9e26a004f646b375b3bbb154044bb8e98a9fbf86"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-symfony/zipball/9e26a004f646b375b3bbb154044bb8e98a9fbf86", "type": "zip", "shasum": "", "reference": "9e26a004f646b375b3bbb154044bb8e98a9fbf86"}, "time": "2015-12-26T15:15:02+00:00", "require": {"league/glide": "^1.0", "symfony/http-foundation": "^2.3"}}]}, "security-advisories": [], "last-modified": "Sun, 14 Apr 2024 16:35:19 GMT"}