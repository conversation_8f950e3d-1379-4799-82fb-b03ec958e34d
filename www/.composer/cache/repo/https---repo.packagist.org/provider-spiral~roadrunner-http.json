{"minified": "composer/2.0", "packages": {"spiral/roadrunner-http": [{"name": "spiral/roadrunner-http", "description": "RoadRunner: HTTP and PSR-7 worker", "keywords": [], "homepage": "https://spiral.dev/", "version": "v3.5.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "c00ab7afd289df7a6b49f9ef07ce57dcb8020df1"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/c00ab7afd289df7a6b49f9ef07ce57dcb8020df1", "type": "zip", "shasum": "", "reference": "c00ab7afd289df7a6b49f9ef07ce57dcb8020df1"}, "type": "library", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/v3.5.2"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-13T09:40:10+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Http\\": "src"}}, "require": {"php": ">=8.1", "ext-json": "*", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1 || ^2.0", "spiral/roadrunner": "^2023.3 || ^2024.1 || ^2025.1", "spiral/roadrunner-worker": "^3.5", "roadrunner-php/roadrunner-api-dto": "^1.6", "symfony/polyfill-php83": "^1.29"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^10.0", "symfony/process": "^6.2 || ^7.0", "vimeo/psalm": "^5.9"}, "suggest": {"spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools", "ext-protobuf": "Provides Protocol Buffers support. Without it, performance will be lower."}}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "213cd0d5c0fba1548f22a5f5ff333afa88fe24ae"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/213cd0d5c0fba1548f22a5f5ff333afa88fe24ae", "type": "zip", "shasum": "", "reference": "213cd0d5c0fba1548f22a5f5ff333afa88fe24ae"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/v3.5.1"}, "time": "2024-04-26T11:16:10+00:00", "require": {"php": ">=8.1", "ext-json": "*", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1 || ^2.0", "spiral/roadrunner": "^2023.3 || ^2024.1", "spiral/roadrunner-worker": "^3.5", "roadrunner-php/roadrunner-api-dto": "^1.6", "symfony/polyfill-php83": "^1.29"}}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "f5c329b3bce485fbc7080bef1d37ff130985de50"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/f5c329b3bce485fbc7080bef1d37ff130985de50", "type": "zip", "shasum": "", "reference": "f5c329b3bce485fbc7080bef1d37ff130985de50"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/v3.5.0"}, "time": "2024-04-01T17:33:48+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "c6fb29998a1074c82038cb280c2c6bb61b2d178f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/c6fb29998a1074c82038cb280c2c6bb61b2d178f", "type": "zip", "shasum": "", "reference": "c6fb29998a1074c82038cb280c2c6bb61b2d178f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/v3.4.0"}, "time": "2024-02-05T13:06:59+00:00", "require": {"php": ">=8.1", "ext-json": "*", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1 || ^2.0", "spiral/roadrunner": "^2023.3", "spiral/roadrunner-worker": "^3.1.0"}, "require-dev": {"buggregator/trap": "^1.0", "jetbrains/phpstorm-attributes": "^1.0", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^10.0", "symfony/process": "^6.2 || ^7.0", "vimeo/psalm": "^5.9"}, "suggest": {"spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools"}}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "7a1f7d0729ad483e9c637ee276f6e910def33f4d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/7a1f7d0729ad483e9c637ee276f6e910def33f4d", "type": "zip", "shasum": "", "reference": "7a1f7d0729ad483e9c637ee276f6e910def33f4d"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/3.3.0"}, "time": "2023-12-05T20:45:38+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "c6aef2a4da22a210aa77539ace4caab8a8bcd337"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/c6aef2a4da22a210aa77539ace4caab8a8bcd337", "type": "zip", "shasum": "", "reference": "c6aef2a4da22a210aa77539ace4caab8a8bcd337"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/3.2.0"}, "time": "2023-10-04T09:40:13+00:00", "require-dev": {"buggregator/trap": "^1.0", "jetbrains/phpstorm-attributes": "^1.0", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^10.0", "symfony/process": "^6.2", "symfony/var-dumper": "^6.3", "vimeo/psalm": "^5.9"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "c6e6f0a547dbde79408d57e92e0711dd42082c0e"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/c6e6f0a547dbde79408d57e92e0711dd42082c0e", "type": "zip", "shasum": "", "reference": "c6e6f0a547dbde79408d57e92e0711dd42082c0e"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/3.1.0"}, "time": "2023-07-17T16:19:35+00:00", "require": {"php": ">=8.1", "ext-json": "*", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1 || ^2.0", "spiral/roadrunner": "^2023.1", "spiral/roadrunner-worker": "^3.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^10.0", "symfony/process": "^6.2", "vimeo/psalm": "^5.9"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "ab62a2dbe09d62bcb42a297b19a25c7a913683f9"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/ab62a2dbe09d62bcb42a297b19a25c7a913683f9", "type": "zip", "shasum": "", "reference": "ab62a2dbe09d62bcb42a297b19a25c7a913683f9"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/3.0.1"}, "time": "2023-04-24T06:50:34+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/http.git", "type": "git", "reference": "99727fb7cea244b01e20db65d58249f23f55c535"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/http/zipball/99727fb7cea244b01e20db65d58249f23f55c535", "type": "zip", "shasum": "", "reference": "99727fb7cea244b01e20db65d58249f23f55c535"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/3.0.0"}, "time": "2023-04-13T15:10:53+00:00", "require": {"php": ">=8.1", "ext-json": "*", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1", "spiral/roadrunner": "^2023.1", "spiral/roadrunner-worker": "^3.0"}}, {"homepage": "", "version": "v2.2.0", "version_normalized": "*******", "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "2b397a27f19cbf8934c20b9e691e0f2edfd2d7b9"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/2b397a27f19cbf8934c20b9e691e0f2edfd2d7b9", "type": "zip", "shasum": "", "reference": "2b397a27f19cbf8934c20b9e691e0f2edfd2d7b9"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.2.0"}, "funding": [], "time": "2022-11-30T11:40:04+00:00", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "require": {"php": ">=7.4", "ext-json": "*", "spiral/roadrunner-worker": "^2.2.0", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1"}, "require-dev": {"nyholm/psr7": "^1.3", "phpstan/phpstan": "~0.12", "phpunit/phpunit": "~8.0", "jetbrains/phpstorm-attributes": "^1.0", "vimeo/psalm": "^4.22", "symfony/var-dumper": "^5.1"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "3be8bac365d436028a9583e7d438bf6e7183e599"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/3be8bac365d436028a9583e7d438bf6e7183e599", "type": "zip", "shasum": "", "reference": "3be8bac365d436028a9583e7d438bf6e7183e599"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.1.0"}, "time": "2022-03-22T14:48:00+00:00"}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "2d76b779fba35036f3e8861dec2dad200a557970"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/2d76b779fba35036f3e8861dec2dad200a557970", "type": "zip", "shasum": "", "reference": "2d76b779fba35036f3e8861dec2dad200a557970"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.4"}, "time": "2021-09-29T11:28:39+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "require": {"php": ">=7.4", "ext-json": "*", "spiral/roadrunner-worker": "^2.0", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1"}, "require-dev": {"nyholm/psr7": "^1.3", "phpstan/phpstan": "~0.12", "phpunit/phpunit": "~8.0", "jetbrains/phpstorm-attributes": "^1.0", "vimeo/psalm": "^4.4", "symfony/var-dumper": "^5.1"}}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "5ad9b269708374583ecb2c4aeb82dc2d1d6453a2"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/5ad9b269708374583ecb2c4aeb82dc2d1d6453a2", "type": "zip", "shasum": "", "reference": "5ad9b269708374583ecb2c4aeb82dc2d1d6453a2"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.3"}, "time": "2021-09-11T21:20:58+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "62f9784e00413d38a0de7318aa092f2adf169421"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/62f9784e00413d38a0de7318aa092f2adf169421", "type": "zip", "shasum": "", "reference": "62f9784e00413d38a0de7318aa092f2adf169421"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.2"}, "time": "2021-09-02T08:58:12+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "41308582dd678434375991928990a6a2cd7a198b"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/41308582dd678434375991928990a6a2cd7a198b", "type": "zip", "shasum": "", "reference": "41308582dd678434375991928990a6a2cd7a198b"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.1"}, "time": "2021-08-05T12:31:10+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "0eb0bc93f16ff8130fd0aaf31f6d87000bf2a605"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/0eb0bc93f16ff8130fd0aaf31f6d87000bf2a605", "type": "zip", "shasum": "", "reference": "0eb0bc93f16ff8130fd0aaf31f6d87000bf2a605"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.0"}, "time": "2021-02-18T11:09:37+00:00"}, {"version": "v2.0.0-beta10", "version_normalized": "*******-beta10", "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.0-beta10"}}, {"version": "v2.0.0-beta9", "version_normalized": "*******-beta9", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "c0e86f7fd55a620ffc3bc230ef545f64a11ab639"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/c0e86f7fd55a620ffc3bc230ef545f64a11ab639", "type": "zip", "shasum": "", "reference": "c0e86f7fd55a620ffc3bc230ef545f64a11ab639"}, "type": "server", "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.0-beta9"}, "time": "2021-01-21T08:37:38+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Http\\": "src/"}}, "require": {"php": ">=7.4", "spiral/roadrunner": ">=2.0", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1"}, "require-dev": {"nyholm/psr7": "^1.3", "phpstan/phpstan": "~0.12", "phpunit/phpunit": "~8.0"}, "suggest": "__unset", "extra": "__unset"}, {"version": "v2.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner-http.git", "type": "git", "reference": "56945052a1df579cef3fb63a0554ebd1493b1928"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-http/zipball/56945052a1df579cef3fb63a0554ebd1493b1928", "type": "zip", "shasum": "", "reference": "56945052a1df579cef3fb63a0554ebd1493b1928"}, "support": {"issues": "https://github.com/spiral/roadrunner-http/issues", "source": "https://github.com/spiral/roadrunner-http/tree/v2.0.0-beta1"}, "time": "2020-12-16T13:31:05+00:00"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 13 May 2025 09:40:54 GMT"}