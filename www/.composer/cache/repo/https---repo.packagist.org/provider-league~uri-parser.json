{"minified": "composer/2.0", "packages": {"league/uri-parser": [{"name": "league/uri-parser", "description": "userland URI parser RFC 3986 compliant", "keywords": ["parser", "url", "uri", "rfc3986", "parse_url", "rfc3987"], "homepage": "https://github.com/thephpleague/uri-parser", "version": "1.4.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/671548427e4c932352d9b9279fdfa345bf63fa00", "type": "zip", "shasum": "", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00"}, "type": "library", "support": {"issues": "https://github.com/thephpleague/uri-parser/issues", "source": "https://github.com/thephpleague/uri-parser/tree/master"}, "time": "2018-11-22T07:55:51+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Uri\\": "src"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "require": {"php": ">=7.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^6.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-strict-rules": "^0.9.0", "phpstan/phpstan-phpunit": "^0.9.4"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-schemes": "Allow validating and normalizing URI parsing results"}, "abandoned": "league/uri-interfaces"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "8beb28540744a5ad728aee7060100002f9196f46"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/8beb28540744a5ad728aee7060100002f9196f46", "type": "zip", "shasum": "", "reference": "8beb28540744a5ad728aee7060100002f9196f46"}, "time": "2018-03-13T21:13:33+00:00"}, {"keywords": ["parser", "url", "uri", "rfc3986", "parse_url"], "version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "f3c99b77f0cba4446dad2eca8c57227fcda0f39b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/f3c99b77f0cba4446dad2eca8c57227fcda0f39b", "type": "zip", "shasum": "", "reference": "f3c99b77f0cba4446dad2eca8c57227fcda0f39b"}, "time": "2017-12-01T11:53:01+00:00", "require": {"ext-intl": "*", "php": ">=7.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^6.0"}, "suggest": {"league/uri-schemes": "Allow validating and normalizing URI parsing results"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "5d0beab08f29079fac1bb76dbcb2336ef10af8b8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/5d0beab08f29079fac1bb76dbcb2336ef10af8b8", "type": "zip", "shasum": "", "reference": "5d0beab08f29079fac1bb76dbcb2336ef10af8b8"}, "time": "2017-10-20T10:37:29+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "4e01703f2d0dc0cf3f177dd89831ecc4dfad2999"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/4e01703f2d0dc0cf3f177dd89831ecc4dfad2999", "type": "zip", "shasum": "", "reference": "4e01703f2d0dc0cf3f177dd89831ecc4dfad2999"}, "time": "2017-09-25T10:46:56+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "2b756258c42f489eec5b89bf4030065070ee685b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/2b756258c42f489eec5b89bf4030065070ee685b", "type": "zip", "shasum": "", "reference": "2b756258c42f489eec5b89bf4030065070ee685b"}, "time": "2017-04-19T07:41:41+00:00", "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.9", "phpunit/phpunit": "^6.0", "phpbench/phpbench": "^0.12.2"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "86d0d40761bd43dc31b8e47ee5035abbe9a90e33"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/86d0d40761bd43dc31b8e47ee5035abbe9a90e33", "type": "zip", "shasum": "", "reference": "86d0d40761bd43dc31b8e47ee5035abbe9a90e33"}, "time": "2017-03-01T10:38:20+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "129e088392cb80cf5431fe9d3b384fc0d06f3a2c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/129e088392cb80cf5431fe9d3b384fc0d06f3a2c", "type": "zip", "shasum": "", "reference": "129e088392cb80cf5431fe9d3b384fc0d06f3a2c"}, "time": "2017-02-06T08:25:03+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "6cbb098f50e1c3db11a0c12cf9b0838f7f0f102f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/6cbb098f50e1c3db11a0c12cf9b0838f7f0f102f", "type": "zip", "shasum": "", "reference": "6cbb098f50e1c3db11a0c12cf9b0838f7f0f102f"}, "time": "2017-01-19T08:04:36+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^1.9", "phpunit/phpunit": "^5.0", "phpbench/phpbench": "^0.12.2"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "cc611f7e7729267e51f6a02a7c1375e1608ecb5c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/cc611f7e7729267e51f6a02a7c1375e1608ecb5c", "type": "zip", "shasum": "", "reference": "cc611f7e7729267e51f6a02a7c1375e1608ecb5c"}, "time": "2017-01-13T11:09:38+00:00", "suggest": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "66fb87efc632cfd976c7dca1fe8d435ea877d88a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/66fb87efc632cfd976c7dca1fe8d435ea877d88a", "type": "zip", "shasum": "", "reference": "66fb87efc632cfd976c7dca1fe8d435ea877d88a"}, "time": "2017-01-04T08:05:38+00:00", "require": {"ext-intl": "*", "php": ">=5.6.0"}}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "d174be7bbea3e20804c43b7c0d78d7129f83538f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/d174be7bbea3e20804c43b7c0d78d7129f83538f", "type": "zip", "shasum": "", "reference": "d174be7bbea3e20804c43b7c0d78d7129f83538f"}, "time": "2016-12-09T07:59:58+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "17c5ec8972979f7e4336902512aa7f5a4ce0406e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/17c5ec8972979f7e4336902512aa7f5a4ce0406e", "type": "zip", "shasum": "", "reference": "17c5ec8972979f7e4336902512aa7f5a4ce0406e"}, "time": "2016-11-02T22:09:07+00:00", "require": {"ext-mbstring": "*", "ext-intl": "*", "php": ">=5.6.0"}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-parser.git", "type": "git", "reference": "61846aac7a1aadc15cfe3b1767be9c37dcb7db9d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/61846aac7a1aadc15cfe3b1767be9c37dcb7db9d", "type": "zip", "shasum": "", "reference": "61846aac7a1aadc15cfe3b1767be9c37dcb7db9d"}, "support": {"issues": "https://github.com/thephpleague/uri-parser/issues", "source": "https://github.com/thephpleague/uri-parser/tree/0.1.0"}, "time": "2016-10-17T14:40:34+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^1.9", "phpunit/phpunit": "^5.0"}}]}, "security-advisories": [], "last-modified": "Fri, 28 Jun 2024 06:51:57 GMT"}