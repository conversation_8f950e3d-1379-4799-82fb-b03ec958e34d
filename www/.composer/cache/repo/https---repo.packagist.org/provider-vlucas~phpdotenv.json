{"minified": "composer/2.0", "packages": {"vlucas/phpdotenv": [{"name": "vlucas/phpdotenv", "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["environment", "env", "dotenv"], "homepage": "", "version": "v5.6.2", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "type": "zip", "shasum": "", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af"}, "type": "library", "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2025-04-30T23:37:27+00:00", "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "require": {"php": "^7.2.5 || ^8.0", "ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"ext-filter": "*", "bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}}, {"version": "v5.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2", "type": "zip", "shasum": "", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.1"}, "time": "2024-07-20T21:52:34+00:00"}, {"version": "v5.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "type": "zip", "shasum": "", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.0"}, "time": "2023-11-12T22:43:29+00:00", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.6-dev"}}, "require": {"php": "^7.2.5 || ^8.0", "ext-pcre": "*", "graham-campbell/result-type": "^1.1.2", "phpoption/phpoption": "^1.9.2", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}}, {"version": "v5.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "type": "zip", "shasum": "", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.5.0"}, "time": "2022-10-16T01:01:54+00:00", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.5-dev"}}, "require": {"php": "^7.1.3 || ^8.0", "ext-pcre": "*", "graham-campbell/result-type": "^1.0.2", "phpoption/phpoption": "^1.8", "symfony/polyfill-ctype": "^1.23", "symfony/polyfill-mbstring": "^1.23.1", "symfony/polyfill-php80": "^1.23.1"}, "require-dev": {"ext-filter": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^7.5.20 || ^8.5.30 || ^9.5.25"}}, {"version": "v5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "264dce589e7ce37a7ba99cb901eed8249fbec92f"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/264dce589e7ce37a7ba99cb901eed8249fbec92f", "type": "zip", "shasum": "", "reference": "264dce589e7ce37a7ba99cb901eed8249fbec92f"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.4.1"}, "time": "2021-12-12T23:22:04+00:00", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "require-dev": {"ext-filter": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^7.5.20 || ^8.5.21 || ^9.5.10"}}, {"version": "v5.4.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "d4394d044ed69a8f244f3445bcedf8a0d7fe2403"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/d4394d044ed69a8f244f3445bcedf8a0d7fe2403", "type": "zip", "shasum": "", "reference": "d4394d044ed69a8f244f3445bcedf8a0d7fe2403"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.4.0"}, "time": "2021-11-10T01:08:39+00:00"}, {"version": "v5.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "accaddf133651d4b5cf81a119f25296736ffc850"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/accaddf133651d4b5cf81a119f25296736ffc850", "type": "zip", "shasum": "", "reference": "accaddf133651d4b5cf81a119f25296736ffc850"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.3.1"}, "time": "2021-10-02T19:24:42+00:00", "extra": {"branch-alias": {"dev-master": "5.3-dev"}}}, {"version": "v5.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "b3eac5c7ac896e52deab4a99068e3f4ab12d9e56"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/b3eac5c7ac896e52deab4a99068e3f4ab12d9e56", "type": "zip", "shasum": "", "reference": "b3eac5c7ac896e52deab4a99068e3f4ab12d9e56"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.3.0"}, "time": "2021-01-20T15:23:13+00:00", "require": {"php": "^7.1.3 || ^8.0", "ext-pcre": "*", "graham-campbell/result-type": "^1.0.1", "phpoption/phpoption": "^1.7.4", "symfony/polyfill-ctype": "^1.17", "symfony/polyfill-mbstring": "^1.17", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"ext-filter": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^7.5.20 || ^8.5.14 || ^9.5.1"}}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "fba64139db67123c7a57072e5f8d3db10d160b66"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/fba64139db67123c7a57072e5f8d3db10d160b66", "type": "zip", "shasum": "", "reference": "fba64139db67123c7a57072e5f8d3db10d160b66"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.2.0"}, "time": "2020-09-14T15:57:31+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "require-dev": {"ext-filter": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^7.5.20 || ^8.5.2 || ^9.0"}}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "448c76d7a9e30c341ff5bc367a923af74ae18467"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/448c76d7a9e30c341ff5bc367a923af74ae18467", "type": "zip", "shasum": "", "reference": "448c76d7a9e30c341ff5bc367a923af74ae18467"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2020-07-14T19:26:25+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "7e6837db20ebb360f2b97fa87411d11a4b500bf6"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/7e6837db20ebb360f2b97fa87411d11a4b500bf6", "type": "zip", "shasum": "", "reference": "7e6837db20ebb360f2b97fa87411d11a4b500bf6"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.0.1"}, "time": "2020-07-14T19:26:07+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "fbb6a5f65512f21d0db9e21bd49e67f70a9bbd5e"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/fbb6a5f65512f21d0db9e21bd49e67f70a9bbd5e", "type": "zip", "shasum": "", "reference": "fbb6a5f65512f21d0db9e21bd49e67f70a9bbd5e"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2020-06-07T19:04:14+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "67a491df68208bef8c37092db11fa3885008efcf"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/67a491df68208bef8c37092db11fa3885008efcf", "type": "zip", "shasum": "", "reference": "67a491df68208bef8c37092db11fa3885008efcf"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.3.0"}, "time": "2022-10-16T00:51:09+00:00", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "4.3-dev"}}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.7.3", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.30"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "77e974614d2ead521f18069dccc571696f52b8dc"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/77e974614d2ead521f18069dccc571696f52b8dc", "type": "zip", "shasum": "", "reference": "77e974614d2ead521f18069dccc571696f52b8dc"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.2.2"}, "time": "2021-12-12T23:07:53+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}}, {"version": "v4.2.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "d38f4d1edcbe32515a0ad593cbd4c858e337263c"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/d38f4d1edcbe32515a0ad593cbd4c858e337263c", "type": "zip", "shasum": "", "reference": "d38f4d1edcbe32515a0ad593cbd4c858e337263c"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.2.1"}, "time": "2021-10-02T19:17:08+00:00"}, {"version": "v4.2.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "da64796370fc4eb03cc277088f6fede9fde88482"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/da64796370fc4eb03cc277088f6fede9fde88482", "type": "zip", "shasum": "", "reference": "da64796370fc4eb03cc277088f6fede9fde88482"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.2.0"}, "time": "2021-01-20T15:11:48+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}}, {"version": "v4.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "572af79d913627a9d70374d27a6f5d689a35de32"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/572af79d913627a9d70374d27a6f5d689a35de32", "type": "zip", "shasum": "", "reference": "572af79d913627a9d70374d27a6f5d689a35de32"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/4.1"}, "time": "2020-07-14T19:22:52+00:00", "require-dev": {"ext-filter": "*", "ext-pcre": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0"}}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "db63b2ea280fdcf13c4ca392121b0b2450b51193"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/db63b2ea280fdcf13c4ca392121b0b2450b51193", "type": "zip", "shasum": "", "reference": "db63b2ea280fdcf13c4ca392121b0b2450b51193"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.1.7"}, "time": "2020-06-07T18:25:35+00:00", "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.7.3", "symfony/polyfill-ctype": "^1.16"}}, {"version": "v4.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "0b32505d67c1abbfa829283c86bfc0642a661bf6"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/0b32505d67c1abbfa829283c86bfc0642a661bf6", "type": "zip", "shasum": "", "reference": "0b32505d67c1abbfa829283c86bfc0642a661bf6"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/4.1"}, "time": "2020-05-23T09:43:32+00:00", "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.7.2", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "bamarni/composer-bin-plugin": "^1.3", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "539bb6927c101a5605d31d11a2d17185a2ce2bf1"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/539bb6927c101a5605d31d11a2d17185a2ce2bf1", "type": "zip", "shasum": "", "reference": "539bb6927c101a5605d31d11a2d17185a2ce2bf1"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2020-05-02T14:08:57+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "feb6dad5ae24b1380827aee1629b730080fde500"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/feb6dad5ae24b1380827aee1629b730080fde500", "type": "zip", "shasum": "", "reference": "feb6dad5ae24b1380827aee1629b730080fde500"}, "time": "2020-04-12T15:20:09+00:00", "require": {"php": "^5.5.9 || ^7.0", "phpoption/phpoption": "^1.7.2", "symfony/polyfill-ctype": "^1.9"}}, {"version": "v4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "88f7acc95150bca002a498899f8b52f318e444c2"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/88f7acc95150bca002a498899f8b52f318e444c2", "type": "zip", "shasum": "", "reference": "88f7acc95150bca002a498899f8b52f318e444c2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2020-03-27T23:37:15+00:00"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "939dfda2d7267ac8fc53ac3d642b5de357554c39"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/939dfda2d7267ac8fc53ac3d642b5de357554c39", "type": "zip", "shasum": "", "reference": "939dfda2d7267ac8fc53ac3d642b5de357554c39"}, "time": "2020-03-12T13:44:15+00:00", "require-dev": {"ext-filter": "*", "bamarni/composer-bin-plugin": "^1.3", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"ext-filter": "Required to use the boolean validator."}}, {"version": "v4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "32bd5ca5a4170f88e27073353013d210a3354ae9"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/32bd5ca5a4170f88e27073353013d210a3354ae9", "type": "zip", "shasum": "", "reference": "32bd5ca5a4170f88e27073353013d210a3354ae9"}, "time": "2020-03-01T23:56:01+00:00"}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "0176075a1b7ee9cf86f70143ec79edf7072c975a"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/0176075a1b7ee9cf86f70143ec79edf7072c975a", "type": "zip", "shasum": "", "reference": "0176075a1b7ee9cf86f70143ec79edf7072c975a"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.1.0"}, "time": "2019-12-14T13:59:29+00:00", "require": {"php": "^5.5.9 || ^7.0", "phpoption/phpoption": "^1.7.1", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0", "bamarni/composer-bin-plugin": "^1.3"}, "funding": "__unset", "suggest": "__unset"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "55dc9010920405c6fcf440667ab70a6a6f9a2ac7"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/55dc9010920405c6fcf440667ab70a6a6f9a2ac7", "type": "zip", "shasum": "", "reference": "55dc9010920405c6fcf440667ab70a6a6f9a2ac7"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.0.1"}, "time": "2019-12-08T15:09:05+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": "^5.5.9 || ^7.0", "phpoption/phpoption": "^1.6", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "3aa2821b7f117e235af45ef057e41112a25bf9b1"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/3aa2821b7f117e235af45ef057e41112a25bf9b1", "type": "zip", "shasum": "", "reference": "3aa2821b7f117e235af45ef057e41112a25bf9b1"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v4.0.0"}, "time": "2019-11-30T20:25:09+00:00"}, {"version": "v3.6.10", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5b547cdb25825f10251370f57ba5d9d924e6f68e", "type": "zip", "shasum": "", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.10"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T23:02:06+00:00", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}}, {"version": "v3.6.9", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "a1bf4c9853d90ade427b4efe35355fc41b3d6988"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a1bf4c9853d90ade427b4efe35355fc41b3d6988", "type": "zip", "shasum": "", "reference": "a1bf4c9853d90ade427b4efe35355fc41b3d6988"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.9"}, "time": "2021-10-02T19:07:56+00:00"}, {"version": "v3.6.8", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "5e679f7616db829358341e2d5cccbd18773bdab8"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5e679f7616db829358341e2d5cccbd18773bdab8", "type": "zip", "shasum": "", "reference": "5e679f7616db829358341e2d5cccbd18773bdab8"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.8"}, "time": "2021-01-20T14:39:46+00:00", "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}}, {"version": "v3.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "2065beda6cbe75e2603686907b2e45f6f3a5ad82"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2065beda6cbe75e2603686907b2e45f6f3a5ad82", "type": "zip", "shasum": "", "reference": "2065beda6cbe75e2603686907b2e45f6f3a5ad82"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.7"}, "time": "2020-07-14T19:04:52+00:00", "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0"}}, {"version": "v3.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "4669484ccbc38fe7c4e0c50456778f2010566aad"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/4669484ccbc38fe7c4e0c50456778f2010566aad", "type": "zip", "shasum": "", "reference": "4669484ccbc38fe7c4e0c50456778f2010566aad"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/3.6"}, "time": "2020-06-02T14:08:54+00:00", "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.16"}}, {"version": "v3.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "8b64814b356b96a90d2bc942b152c80d8888b8d4"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/8b64814b356b96a90d2bc942b152c80d8888b8d4", "type": "zip", "shasum": "", "reference": "8b64814b356b96a90d2bc942b152c80d8888b8d4"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.5"}, "time": "2020-05-23T09:42:03+00:00", "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}}, {"version": "v3.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "10d3f853fdf1f3a6b3c7ea0c4620d2f699713db5"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/10d3f853fdf1f3a6b3c7ea0c4620d2f699713db5", "type": "zip", "shasum": "", "reference": "10d3f853fdf1f3a6b3c7ea0c4620d2f699713db5"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.4"}, "time": "2020-05-02T13:46:13+00:00"}, {"version": "v3.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "1b3103013797f04521c6cae5560f604649484066"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1b3103013797f04521c6cae5560f604649484066", "type": "zip", "shasum": "", "reference": "1b3103013797f04521c6cae5560f604649484066"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/3.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2020-04-12T15:18:03+00:00", "require": {"php": "^5.4 || ^7.0", "phpoption/phpoption": "^1.5", "symfony/polyfill-ctype": "^1.9"}}, {"version": "v3.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "786a947e57086cf236cefdee80784634224b99fa"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/786a947e57086cf236cefdee80784634224b99fa", "type": "zip", "shasum": "", "reference": "786a947e57086cf236cefdee80784634224b99fa"}, "time": "2020-03-27T23:36:02+00:00"}, {"version": "v3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "8f7961f7b9deb3b432452c18093cf16f88205902"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/8f7961f7b9deb3b432452c18093cf16f88205902", "type": "zip", "shasum": "", "reference": "8f7961f7b9deb3b432452c18093cf16f88205902"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.1"}, "time": "2020-03-12T13:44:00+00:00", "require-dev": {"ext-filter": "*", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"ext-filter": "Required to use the boolean validator."}}, {"version": "v3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "1bdf24f065975594f6a117f0f1f6cabf1333b156"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1bdf24f065975594f6a117f0f1f6cabf1333b156", "type": "zip", "shasum": "", "reference": "1bdf24f065975594f6a117f0f1f6cabf1333b156"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.0"}, "time": "2019-09-10T21:37:39+00:00", "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "funding": "__unset", "suggest": "__unset"}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "95cb0fa6c025f7f0db7fc60f81e9fb231eb2d222"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/95cb0fa6c025f7f0db7fc60f81e9fb231eb2d222", "type": "zip", "shasum": "", "reference": "95cb0fa6c025f7f0db7fc60f81e9fb231eb2d222"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2019-08-27T17:00:38+00:00", "extra": {"branch-alias": {"dev-master": "3.5-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0"}}, {"version": "v3.4.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "5084b23845c24dbff8ac6c204290c341e4776c92"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5084b23845c24dbff8ac6c204290c341e4776c92", "type": "zip", "shasum": "", "reference": "5084b23845c24dbff8ac6c204290c341e4776c92"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.4.0"}, "time": "2019-06-15T22:40:20+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "dbcc609971dd9b55f48b8008b553d79fd372ddde"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/dbcc609971dd9b55f48b8008b553d79fd372ddde", "type": "zip", "shasum": "", "reference": "dbcc609971dd9b55f48b8008b553d79fd372ddde"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.3.3"}, "time": "2019-03-06T09:39:45+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "1ee9369cfbf26cfcf1f2515d98f15fab54e9647a"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1ee9369cfbf26cfcf1f2515d98f15fab54e9647a", "type": "zip", "shasum": "", "reference": "1ee9369cfbf26cfcf1f2515d98f15fab54e9647a"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2019-01-30T10:43:17+00:00"}, {"version": "v3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "22165d04b0a047abbfe35fd7b10e7707ad9efe5b"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/22165d04b0a047abbfe35fd7b10e7707ad9efe5b", "type": "zip", "shasum": "", "reference": "22165d04b0a047abbfe35fd7b10e7707ad9efe5b"}, "time": "2019-01-29T11:15:07+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "21c540a936c0185ac4148988be3ead7f39069ae6"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/21c540a936c0185ac4148988be3ead7f39069ae6", "type": "zip", "shasum": "", "reference": "21c540a936c0185ac4148988be3ead7f39069ae6"}, "time": "2019-01-26T16:57:11+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "736602fab067eee2e6fa1145f011e6798313d9d9"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/736602fab067eee2e6fa1145f011e6798313d9d9", "type": "zip", "shasum": "", "reference": "736602fab067eee2e6fa1145f011e6798313d9d9"}, "time": "2019-01-24T20:33:37+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require": {"php": "^5.4 || ^7.0", "phpoption/phpoption": "^1.5"}}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "13be13df7df1ba7bca099b16943a8ba025a38a02"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/13be13df7df1ba7bca099b16943a8ba025a38a02", "type": "zip", "shasum": "", "reference": "13be13df7df1ba7bca099b16943a8ba025a38a02"}, "time": "2019-01-23T18:32:42+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "bf58b9c61ba37ef1deaa29fe5dbb30222c948541"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/bf58b9c61ba37ef1deaa29fe5dbb30222c948541", "type": "zip", "shasum": "", "reference": "bf58b9c61ba37ef1deaa29fe5dbb30222c948541"}, "time": "2019-01-04T23:43:30+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "0136b7a71f7496cc5402587b6fd4089f31a27c14"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/0136b7a71f7496cc5402587b6fd4089f31a27c14", "type": "zip", "shasum": "", "reference": "0136b7a71f7496cc5402587b6fd4089f31a27c14"}, "time": "2019-01-03T13:50:26+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v2.6.9", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "type": "zip", "shasum": "", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v2.6.9"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T22:59:22+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}}, {"version": "v2.6.8", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "f1e2a35e53abe9322f0ab9ada689967e30055d40"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/f1e2a35e53abe9322f0ab9ada689967e30055d40", "type": "zip", "shasum": "", "reference": "f1e2a35e53abe9322f0ab9ada689967e30055d40"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v2.6.8"}, "time": "2021-10-02T19:02:17+00:00"}, {"version": "v2.6.7", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "b786088918a884258c9e3e27405c6a4cf2ee246e"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/b786088918a884258c9e3e27405c6a4cf2ee246e", "type": "zip", "shasum": "", "reference": "b786088918a884258c9e3e27405c6a4cf2ee246e"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v2.6.7"}, "time": "2021-01-20T14:39:13+00:00", "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}}, {"version": "v2.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "e1d57f62db3db00d9139078cbedf262280701479"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/e1d57f62db3db00d9139078cbedf262280701479", "type": "zip", "shasum": "", "reference": "e1d57f62db3db00d9139078cbedf262280701479"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v2.6.6"}, "time": "2020-07-14T17:54:18+00:00", "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.35 || ^5.7.27"}}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "2e977311ffb17b2f82028a9c36824647789c6365"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2e977311ffb17b2f82028a9c36824647789c6365", "type": "zip", "shasum": "", "reference": "2e977311ffb17b2f82028a9c36824647789c6365"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/2.6"}, "time": "2020-06-02T14:06:52+00:00", "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.16"}}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "67d472b1794c986381a8950e4958e1adb779d561"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/67d472b1794c986381a8950e4958e1adb779d561", "type": "zip", "shasum": "", "reference": "67d472b1794c986381a8950e4958e1adb779d561"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v2.6.4"}, "time": "2020-05-02T13:38:00+00:00", "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.35 || ^5.0"}}, {"version": "v2.6.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "df4c4d08a639be4ef5d6d1322868f9e477553679"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/df4c4d08a639be4ef5d6d1322868f9e477553679", "type": "zip", "shasum": "", "reference": "df4c4d08a639be4ef5d6d1322868f9e477553679"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/2.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2020-04-12T15:11:38+00:00", "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "^1.9"}}, {"version": "v2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "c4a653ed3f1ff900baa15b4130c8770b57285b62"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/c4a653ed3f1ff900baa15b4130c8770b57285b62", "type": "zip", "shasum": "", "reference": "c4a653ed3f1ff900baa15b4130c8770b57285b62"}, "time": "2020-03-27T23:16:19+00:00"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "2a7dcf7e3e02dc5e701004e51a6f304b713107d5"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2a7dcf7e3e02dc5e701004e51a6f304b713107d5", "type": "zip", "shasum": "", "reference": "2a7dcf7e3e02dc5e701004e51a6f304b713107d5"}, "time": "2019-01-29T11:11:52+00:00", "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0"}, "funding": "__unset", "suggest": "__unset"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "f3aae2877ecf916ee802b7a5b249d36658171df6"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/f3aae2877ecf916ee802b7a5b249d36658171df6", "type": "zip", "shasum": "", "reference": "f3aae2877ecf916ee802b7a5b249d36658171df6"}, "time": "2019-01-28T20:57:27+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "cfd5dc225767ca154853752abc93aeec040fcf36"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/cfd5dc225767ca154853752abc93aeec040fcf36", "type": "zip", "shasum": "", "reference": "cfd5dc225767ca154853752abc93aeec040fcf36"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2018-10-30T17:29:25+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "require": {"php": ">=5.3.9"}}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "8abb4f9aa89ddea9d52112c65bbe8d0125e2fa8e"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/8abb4f9aa89ddea9d52112c65bbe8d0125e2fa8e", "type": "zip", "shasum": "", "reference": "8abb4f9aa89ddea9d52112c65bbe8d0125e2fa8e"}, "time": "2018-07-29T20:33:41+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "6ae3e2e6494bb5e58c2decadafc3de7f1453f70a"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/6ae3e2e6494bb5e58c2decadafc3de7f1453f70a", "type": "zip", "shasum": "", "reference": "6ae3e2e6494bb5e58c2decadafc3de7f1453f70a"}, "time": "2018-07-01T10:25:50+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "license": ["BSD-3-Clause-Attribution"], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "3cc116adbe4b11be5ec557bf1d24dc5e3a21d18c"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/3cc116adbe4b11be5ec557bf1d24dc5e3a21d18c", "type": "zip", "shasum": "", "reference": "3cc116adbe4b11be5ec557bf1d24dc5e3a21d18c"}, "time": "2016-09-01T10:05:43+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "9ca5644c536654e9509b9d257f53c58630eb2a6a"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/9ca5644c536654e9509b9d257f53c58630eb2a6a", "type": "zip", "shasum": "", "reference": "9ca5644c536654e9509b9d257f53c58630eb2a6a"}, "time": "2016-06-14T14:14:52+00:00", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "63f37b9395e8041cd4313129c08ece896d06ca8e"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/63f37b9395e8041cd4313129c08ece896d06ca8e", "type": "zip", "shasum": "", "reference": "63f37b9395e8041cd4313129c08ece896d06ca8e"}, "time": "2016-04-15T10:48:49+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}, {"homepage": "http://github.com/vlucas/phpdotenv", "version": "v2.2.0", "version_normalized": "*******", "license": ["BSD"], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "9caf304153dc2288e4970caec6f1f3b3bc205412"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/9caf304153dc2288e4970caec6f1f3b3bc205412", "type": "zip", "shasum": "", "reference": "9caf304153dc2288e4970caec6f1f3b3bc205412"}, "time": "2015-12-29T15:10:30+00:00", "require-dev": {"phpunit/phpunit": "^4.8|^5.0"}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "101d5d7498bcd827e73c20da2f687e79d2d139f9"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/101d5d7498bcd827e73c20da2f687e79d2d139f9", "type": "zip", "shasum": "", "reference": "101d5d7498bcd827e73c20da2f687e79d2d139f9"}, "time": "2015-11-19T09:09:30+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "c10040e0df17d2ee88e9212b50cbe9319e878f59"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/c10040e0df17d2ee88e9212b50cbe9319e878f59", "type": "zip", "shasum": "", "reference": "c10040e0df17d2ee88e9212b50cbe9319e878f59"}, "time": "2015-10-28T18:53:35+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "91064290f5b53a09bdff1b939d7f69fb0e7531b5"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/91064290f5b53a09bdff1b939d7f69fb0e7531b5", "type": "zip", "shasum": "", "reference": "91064290f5b53a09bdff1b939d7f69fb0e7531b5"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/2.0"}, "time": "2015-05-30T16:15:01+00:00", "require": {"php": ">=5.3.2"}, "extra": "__unset"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "058b9b03b945b6c3eced241935c45822fd2bf4e8"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/058b9b03b945b6c3eced241935c45822fd2bf4e8", "type": "zip", "shasum": "", "reference": "058b9b03b945b6c3eced241935c45822fd2bf4e8"}, "time": "2015-05-29T20:43:19+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "0cac554ce06277e33ddf9f0b7ade4b8bbf2af3fa"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/0cac554ce06277e33ddf9f0b7ade4b8bbf2af3fa", "type": "zip", "shasum": "", "reference": "0cac554ce06277e33ddf9f0b7ade4b8bbf2af3fa"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/1.1"}, "time": "2015-05-30T15:59:26+00:00", "autoload": {"psr-0": {"Dotenv": "src/"}}, "extra": "__unset"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "732d2adb7d916c9593b9d58c3b0d9ebefead07aa"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/732d2adb7d916c9593b9d58c3b0d9ebefead07aa", "type": "zip", "shasum": "", "reference": "732d2adb7d916c9593b9d58c3b0d9ebefead07aa"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2014-12-05T15:19:21+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "v1.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "56c252d48dce336da97926591aed71805203815b"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/56c252d48dce336da97926591aed71805203815b", "type": "zip", "shasum": "", "reference": "56c252d48dce336da97926591aed71805203815b"}, "time": "2014-10-16T14:50:21+00:00"}, {"version": "v1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "e783b688cf5bc29a4ffd1b79c92eb4dd2a2e2237"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/e783b688cf5bc29a4ffd1b79c92eb4dd2a2e2237", "type": "zip", "shasum": "", "reference": "e783b688cf5bc29a4ffd1b79c92eb4dd2a2e2237"}, "time": "2014-10-14T15:06:44+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com", "role": "Developer"}], "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "2821c024b4c14c7e6d1ff99e063ae9532620beb2"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2821c024b4c14c7e6d1ff99e063ae9532620beb2", "type": "zip", "shasum": "", "reference": "2821c024b4c14c7e6d1ff99e063ae9532620beb2"}, "time": "2014-01-31T16:18:39+00:00", "require-dev": {"phpunit/phpunit": "*"}, "extra": "__unset"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "d19becb682abb5954b71a6ef2e93804302484f39"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/d19becb682abb5954b71a6ef2e93804302484f39", "type": "zip", "shasum": "", "reference": "d19becb682abb5954b71a6ef2e93804302484f39"}, "time": "2014-01-14T16:37:15+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "cc85696655c5c0989b3f86ee41e3a42dd5e39b4b"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/cc85696655c5c0989b3f86ee41e3a42dd5e39b4b", "type": "zip", "shasum": "", "reference": "cc85696655c5c0989b3f86ee41e3a42dd5e39b4b"}, "time": "2014-01-06T19:57:38+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "f7a87e775cdcfd4ca7ea2649061e5f9f24291621"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/f7a87e775cdcfd4ca7ea2649061e5f9f24291621", "type": "zip", "shasum": "", "reference": "f7a87e775cdcfd4ca7ea2649061e5f9f24291621"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v1.0.3"}, "time": "2013-12-29T22:16:26+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "da70617b13f1c5d1a89357e275097f24e7c089e0"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/da70617b13f1c5d1a89357e275097f24e7c089e0", "type": "zip", "shasum": "", "reference": "da70617b13f1c5d1a89357e275097f24e7c089e0"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/master"}, "time": "2013-08-12T19:16:15+00:00", "require-dev": "__unset"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "590ffa3408f1fb2321ec574131bcde73c340627b"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/590ffa3408f1fb2321ec574131bcde73c340627b", "type": "zip", "shasum": "", "reference": "590ffa3408f1fb2321ec574131bcde73c340627b"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v1.0.1"}, "time": "2013-01-23T16:24:47+00:00"}, {"description": "Loads environment variables from a .env file", "keywords": ["environment", "env"], "version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vlucas/phpdotenv.git", "type": "git", "reference": "f64f14291cb7d0f2085b9aa5a9333f514a8ec387"}, "dist": {"url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/f64f14291cb7d0f2085b9aa5a9333f514a8ec387", "type": "zip", "shasum": "", "reference": "f64f14291cb7d0f2085b9aa5a9333f514a8ec387"}, "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v1.0.0"}, "time": "2013-01-23T06:55:23+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 30 Apr 2025 23:40:51 GMT"}