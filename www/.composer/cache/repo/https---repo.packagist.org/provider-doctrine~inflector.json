{"minified": "composer/2.0", "packages": {"doctrine/inflector": [{"name": "doctrine/inflector", "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "keywords": ["php", "inflector", "inflection", "strings", "lowercase", "manipulation", "words", "plural", "singular", "uppercase"], "homepage": "https://www.doctrine-project.org/projects/inflector.html", "version": "2.0.10", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "type": "zip", "shasum": "", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "type": "library", "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "2930cd5ef353871c821d5c43ed030d39ac8cfe65"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/2930cd5ef353871c821d5c43ed030d39ac8cfe65", "type": "zip", "shasum": "", "reference": "2930cd5ef353871c821d5c43ed030d39ac8cfe65"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.9"}, "time": "2024-01-15T18:05:13+00:00"}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "f9301a5b2fb1216b2b08f02ba04dc45423db6bff"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/f9301a5b2fb1216b2b08f02ba04dc45423db6bff", "type": "zip", "shasum": "", "reference": "f9301a5b2fb1216b2b08f02ba04dc45423db6bff"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.8"}, "time": "2023-06-16T13:40:37+00:00"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "27c555ebd2ae8fde9e6e8cb84e6fbea57d067634"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/27c555ebd2ae8fde9e6e8cb84e6fbea57d067634", "type": "zip", "shasum": "", "reference": "27c555ebd2ae8fde9e6e8cb84e6fbea57d067634"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.7"}, "time": "2023-06-16T10:07:19+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "d9d313a36c872fd6ee06d9a6cbcf713eaa40f024"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/d9d313a36c872fd6ee06d9a6cbcf713eaa40f024", "type": "zip", "shasum": "", "reference": "d9d313a36c872fd6ee06d9a6cbcf713eaa40f024"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.6"}, "time": "2022-10-20T09:10:12+00:00", "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "ade2b3bbfb776f27f0558e26eed43b5d9fe1b392"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/ade2b3bbfb776f27f0558e26eed43b5d9fe1b392", "type": "zip", "shasum": "", "reference": "ade2b3bbfb776f27f0558e26eed43b5d9fe1b392"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.5"}, "time": "2022-09-07T09:01:28+00:00", "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "type": "zip", "shasum": "", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.4"}, "time": "2021-10-22T20:16:43+00:00", "require-dev": {"doctrine/coding-standard": "^8.2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^4.10"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "type": "zip", "shasum": "", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.x"}, "time": "2020-05-29T15:13:26+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "3fc171224a316569faad2df6b18a1fd8cce5a56d"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/3fc171224a316569faad2df6b18a1fd8cce5a56d", "type": "zip", "shasum": "", "reference": "3fc171224a316569faad2df6b18a1fd8cce5a56d"}, "time": "2020-05-25T20:08:47+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "18b995743e7ec8b15fd6efc594f0fa3de4bfe6d7"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/18b995743e7ec8b15fd6efc594f0fa3de4bfe6d7", "type": "zip", "shasum": "", "reference": "18b995743e7ec8b15fd6efc594f0fa3de4bfe6d7"}, "time": "2020-05-11T11:25:59+00:00", "require": {"php": "^7.2"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "712e2e45e01bdb11f9694689320bc99c8638427f"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/712e2e45e01bdb11f9694689320bc99c8638427f", "type": "zip", "shasum": "", "reference": "712e2e45e01bdb11f9694689320bc99c8638427f"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.0"}, "time": "2020-05-06T14:40:58+00:00"}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "type": "zip", "shasum": "", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "time": "2021-04-16T17:34:40+00:00", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "4650c8b30c753a76bf44fb2ed00117d6f367490c"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/4650c8b30c753a76bf44fb2ed00117d6f367490c", "type": "zip", "shasum": "", "reference": "4650c8b30c753a76bf44fb2ed00117d6f367490c"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.x"}, "time": "2020-05-29T07:19:59+00:00", "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "889b42b8155f2aa274596b6e0424371b1e3c51bb"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/889b42b8155f2aa274596b6e0424371b1e3c51bb", "type": "zip", "shasum": "", "reference": "889b42b8155f2aa274596b6e0424371b1e3c51bb"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.2"}, "time": "2020-05-25T20:02:40+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "4111f6853aea6f28b2b1dcfdde83d12dd3d5e6e3"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/4111f6853aea6f28b2b1dcfdde83d12dd3d5e6e3", "type": "zip", "shasum": "", "reference": "4111f6853aea6f28b2b1dcfdde83d12dd3d5e6e3"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.1"}, "time": "2020-05-09T15:09:09+00:00", "require": {"php": "^7.2"}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "ab5de36233a1995f9c776c741b803eb8207aebef"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/ab5de36233a1995f9c776c741b803eb8207aebef", "type": "zip", "shasum": "", "reference": "ab5de36233a1995f9c776c741b803eb8207aebef"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.x"}, "time": "2020-05-06T11:01:57+00:00"}, {"description": "Common String Manipulations with regard to casing and singular/plural rules.", "keywords": ["string", "inflection", "singularize", "pluralize"], "homepage": "http://www.doctrine-project.org", "version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "type": "zip", "shasum": "", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.3.1"}, "time": "2019-10-30T19:59:35+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "funding": "__unset"}, {"version": "v1.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "5527a48b7313d15261292c149e55e26eae771b0a"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/5527a48b7313d15261292c149e55e26eae771b0a", "type": "zip", "shasum": "", "reference": "5527a48b7313d15261292c149e55e26eae771b0a"}, "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/master"}, "time": "2018-01-09T20:05:19+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "e11d84c6e018beedd929cff5220969a3c6d1d462"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/e11d84c6e018beedd929cff5220969a3c6d1d462", "type": "zip", "shasum": "", "reference": "e11d84c6e018beedd929cff5220969a3c6d1d462"}, "support": {"source": "https://github.com/doctrine/inflector/tree/master"}, "time": "2017-07-22T12:18:28+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": "^7.0"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "90b2128806bfde671b6952ab8bea493942c1fdae"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/90b2128806bfde671b6952ab8bea493942c1fdae", "type": "zip", "shasum": "", "reference": "90b2128806bfde671b6952ab8bea493942c1fdae"}, "time": "2015-11-06T14:35:42+00:00", "autoload": {"psr-0": {"Doctrine\\Common\\Inflector\\": "lib/"}}, "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "4.*"}}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "0bcb2e79d8571787f18b7eb036ed3d004908e604"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/0bcb2e79d8571787f18b7eb036ed3d004908e604", "type": "zip", "shasum": "", "reference": "0bcb2e79d8571787f18b7eb036ed3d004908e604"}, "time": "2014-12-20T21:24:13+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"keywords": ["string", "inflection", "singuarlize", "pluarlize"], "version": "v1.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.jwage.com/", "role": "Creator"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>", "homepage": "http://www.instaclick.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/doctrine/inflector.git", "type": "git", "reference": "54b8333d2a5682afdc690060c1cf384ba9f47f08"}, "dist": {"url": "https://api.github.com/repos/doctrine/inflector/zipball/54b8333d2a5682afdc690060c1cf384ba9f47f08", "type": "zip", "shasum": "", "reference": "54b8333d2a5682afdc690060c1cf384ba9f47f08"}, "support": {"source": "https://github.com/doctrine/inflector/tree/v1.0"}, "time": "2013-01-10T21:49:15+00:00", "require-dev": "__unset", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 18 Apr 2024 22:14:10 GMT"}