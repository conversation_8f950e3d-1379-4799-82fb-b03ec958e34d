{"minified": "composer/2.0", "packages": {"mockery/mockery": [{"name": "mockery/mockery", "description": "Mockery is a simple yet flexible PHP mock object framework", "keywords": ["testing", "library", "BDD", "TDD", "test", "mockery", "mock", "stub", "test double", "mock objects"], "homepage": "https://github.com/mockery/mockery", "version": "1.6.12", "version_normalized": "********", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "type": "zip", "shasum": "", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "type": "library", "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "funding": [], "time": "2024-05-16T03:13:13+00:00", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "require": {"php": ">=7.3", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "conflict": {"phpunit/phpunit": "<8.0"}}, {"version": "1.6.11", "version_normalized": "********", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "81a161d0b135df89951abd52296adf97deb0723d"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/81a161d0b135df89951abd52296adf97deb0723d", "type": "zip", "shasum": "", "reference": "81a161d0b135df89951abd52296adf97deb0723d"}, "time": "2024-03-21T18:34:15+00:00"}, {"version": "1.6.10", "version_normalized": "********", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "47065d1be1fa05def58dc14c03cf831d3884ef0b"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/47065d1be1fa05def58dc14c03cf831d3884ef0b", "type": "zip", "shasum": "", "reference": "47065d1be1fa05def58dc14c03cf831d3884ef0b"}, "time": "2024-03-19T16:15:45+00:00"}, {"version": "1.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06", "type": "zip", "shasum": "", "reference": "0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06"}, "time": "2023-12-10T02:24:34+00:00", "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.10", "symplify/easy-coding-standard": "^12.0.8"}}, {"version": "1.6.8", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "415720b7e3052f42de05c384134387771d3e183b"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/415720b7e3052f42de05c384134387771d3e183b", "type": "zip", "shasum": "", "reference": "415720b7e3052f42de05c384134387771d3e183b"}, "time": "2024-03-12T12:33:41+00:00", "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}}, {"version": "1.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06", "type": "zip", "shasum": "", "reference": "0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06"}, "time": "2023-12-10T02:24:34+00:00", "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.10", "symplify/easy-coding-standard": "^12.0.8"}}, {"version": "1.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "b8e0bb7d8c604046539c1115994632c74dcb361e"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/b8e0bb7d8c604046539c1115994632c74dcb361e", "type": "zip", "shasum": "", "reference": "b8e0bb7d8c604046539c1115994632c74dcb361e"}, "time": "2023-08-09T00:03:52+00:00", "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.10", "psalm/plugin-phpunit": "^0.18.4", "symplify/easy-coding-standard": "^11.5.0", "vimeo/psalm": "^4.30"}}, {"version": "1.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "68782e943f9ffcbc72bda08aedabe73fecb50041"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/68782e943f9ffcbc72bda08aedabe73fecb50041", "type": "zip", "shasum": "", "reference": "68782e943f9ffcbc72bda08aedabe73fecb50041"}, "time": "2023-08-06T00:30:34+00:00"}, {"version": "1.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "d1413755e26fe56a63455f7753221c86cbb88f66"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/d1413755e26fe56a63455f7753221c86cbb88f66", "type": "zip", "shasum": "", "reference": "d1413755e26fe56a63455f7753221c86cbb88f66"}, "time": "2023-07-19T15:51:02+00:00", "require": {"php": ">=7.4,<8.3", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3", "psalm/plugin-phpunit": "^0.18.4", "symplify/easy-coding-standard": "^11.5.0", "vimeo/psalm": "^5.13.1"}}, {"version": "1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "b1be135c1ba7632f0248e07ee5e6e412576a309d"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/b1be135c1ba7632f0248e07ee5e6e412576a309d", "type": "zip", "shasum": "", "reference": "b1be135c1ba7632f0248e07ee5e6e412576a309d"}, "time": "2023-07-18T17:47:29+00:00", "autoload": {"files": ["src/helpers.php", "src/Mockery.php"], "psr-4": {"Mockery\\": "src/Mo<PERSON>y"}}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.13.1"}}, {"version": "1.6.2", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "13a7fa2642c76c58fa2806ef7f565344c817a191"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/13a7fa2642c76c58fa2806ef7f565344c817a191", "type": "zip", "shasum": "", "reference": "13a7fa2642c76c58fa2806ef7f565344c817a191"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.6.2"}, "time": "2023-06-07T09:07:52+00:00", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "extra": {"branch-alias": {"dev-main": "1.6.x-dev"}}, "require": {"php": "^7.4 || ^8.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3", "psalm/plugin-phpunit": "^0.18", "vimeo/psalm": "^5.9"}}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "a8dd186f07ea667c1e3abd2176bfab0ab161ea94"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/a8dd186f07ea667c1e3abd2176bfab0ab161ea94", "type": "zip", "shasum": "", "reference": "a8dd186f07ea667c1e3abd2176bfab0ab161ea94"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.6.1"}, "time": "2023-06-05T13:59:03+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "e92dcc83d5a51851baf5f5591d32cb2b16e3684e"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/e92dcc83d5a51851baf5f5591d32cb2b16e3684e", "type": "zip", "shasum": "", "reference": "e92dcc83d5a51851baf5f5591d32cb2b16e3684e"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.5.1"}, "time": "2022-09-07T15:32:08+00:00", "autoload": {"psr-0": {"Mockery": "library/"}}, "require": {"php": "^7.3 || ^8.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac", "type": "zip", "shasum": "", "reference": "c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.5.0"}, "time": "2022-01-20T13:18:17+00:00"}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "e01123a0e847d52d186c5eb4b9bf58b0c6d00346"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/e01123a0e847d52d186c5eb4b9bf58b0c6d00346", "type": "zip", "shasum": "", "reference": "e01123a0e847d52d186c5eb4b9bf58b0c6d00346"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.4.4"}, "time": "2021-09-13T15:28:59+00:00"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "d1339f64479af1bee0e82a0413813fe5345a54ea"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/d1339f64479af1bee0e82a0413813fe5345a54ea", "type": "zip", "shasum": "", "reference": "d1339f64479af1bee0e82a0413813fe5345a54ea"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.4.3"}, "time": "2021-02-24T09:51:49+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "20cab678faed06fac225193be281ea0fddb43b93"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/20cab678faed06fac225193be281ea0fddb43b93", "type": "zip", "shasum": "", "reference": "20cab678faed06fac225193be281ea0fddb43b93"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/master"}, "time": "2020-08-11T18:10:13+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "1404386ca3410b04fe58b9517e85d702ab33b2c6"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/1404386ca3410b04fe58b9517e85d702ab33b2c6", "type": "zip", "shasum": "", "reference": "1404386ca3410b04fe58b9517e85d702ab33b2c6"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.4.1"}, "time": "2020-07-09T08:31:54+00:00", "require-dev": {"phpunit/phpunit": "^8.5 || ^9.0"}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "6c6a7c533469873deacf998237e7649fc6b36223"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/6c6a7c533469873deacf998237e7649fc6b36223", "type": "zip", "shasum": "", "reference": "6c6a7c533469873deacf998237e7649fc6b36223"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/master"}, "time": "2020-05-19T14:25:16+00:00", "require": {"php": "^7.3.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "~2.0"}, "require-dev": {"phpunit/phpunit": "^8.0.0 || ^9.0.0"}}, {"version": "1.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "type": "zip", "shasum": "", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.6"}, "time": "2022-09-07T15:05:49+00:00", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "require": {"php": ">=5.6.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "conflict": "__unset"}, {"version": "1.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "472fa8ca4e55483d55ee1e73c963718c4393791d"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/472fa8ca4e55483d55ee1e73c963718c4393791d", "type": "zip", "shasum": "", "reference": "472fa8ca4e55483d55ee1e73c963718c4393791d"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.5"}, "time": "2021-09-13T15:33:03+00:00"}, {"version": "1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "31467aeb3ca3188158613322d66df81cedd86626"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/31467aeb3ca3188158613322d66df81cedd86626", "type": "zip", "shasum": "", "reference": "31467aeb3ca3188158613322d66df81cedd86626"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.4"}, "time": "2021-02-24T09:51:00+00:00"}, {"version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "60fa2f67f6e4d3634bb4a45ff3171fa52215800d"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/60fa2f67f6e4d3634bb4a45ff3171fa52215800d", "type": "zip", "shasum": "", "reference": "60fa2f67f6e4d3634bb4a45ff3171fa52215800d"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.3"}, "time": "2020-08-11T18:10:21+00:00"}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "9b6f117dd7d36dc3858d8d8ddf9b3d584fcae283"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/9b6f117dd7d36dc3858d8d8ddf9b3d584fcae283", "type": "zip", "shasum": "", "reference": "9b6f117dd7d36dc3858d8d8ddf9b3d584fcae283"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.2"}, "time": "2020-07-09T08:23:05+00:00", "require-dev": {"phpunit/phpunit": "~5.7.10|~6.5|~7.0|~8.0|~9.0"}}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "f69bbde7d7a75d6b2862d9ca8fab1cd28014b4be"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/f69bbde7d7a75d6b2862d9ca8fab1cd28014b4be", "type": "zip", "shasum": "", "reference": "f69bbde7d7a75d6b2862d9ca8fab1cd28014b4be"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/master"}, "time": "2019-12-26T09:49:15+00:00", "require": {"php": ">=5.6.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5.7.10|~6.5|~7.0|~8.0"}, "funding": "__unset"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "5571962a4f733fbb57bede39778f71647fae8e66"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/5571962a4f733fbb57bede39778f71647fae8e66", "type": "zip", "shasum": "", "reference": "5571962a4f733fbb57bede39778f71647fae8e66"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.0"}, "time": "2019-11-24T07:54:50+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": ">=5.6.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "~2.0", "sebastian/comparator": "^1.2.4|^3.0"}}, {"version": "1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "b3453f75fd23d9fd41685f2148f4abeacabc6405"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/b3453f75fd23d9fd41685f2148f4abeacabc6405", "type": "zip", "shasum": "", "reference": "b3453f75fd23d9fd41685f2148f4abeacabc6405"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.2.4"}, "time": "2019-09-30T08:30:27+00:00", "require": {"php": ">=5.6.0", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "~2.0"}}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "4eff936d83eb809bde2c57a3cea0ee9643769031"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/4eff936d83eb809bde2c57a3cea0ee9643769031", "type": "zip", "shasum": "", "reference": "4eff936d83eb809bde2c57a3cea0ee9643769031"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/master"}, "time": "2019-08-07T15:01:07+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "0eb0b48c3f07b3b89f5169ce005b7d05b18cf1d2"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/0eb0b48c3f07b3b89f5169ce005b7d05b18cf1d2", "type": "zip", "shasum": "", "reference": "0eb0b48c3f07b3b89f5169ce005b7d05b18cf1d2"}, "time": "2019-02-13T09:37:52+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "dc4f10b6b1148744facb784015e4b339d7feec23"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/dc4f10b6b1148744facb784015e4b339d7feec23", "type": "zip", "shasum": "", "reference": "dc4f10b6b1148744facb784015e4b339d7feec23"}, "time": "2019-02-08T14:43:54+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "100633629bf76d57430b86b7098cd6beb996a35a"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/100633629bf76d57430b86b7098cd6beb996a35a", "type": "zip", "shasum": "", "reference": "100633629bf76d57430b86b7098cd6beb996a35a"}, "time": "2018-10-02T21:52:37+00:00", "require-dev": {"phpunit/phpunit": "~5.7.10|~6.5|~7.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "99e29d3596b16dabe4982548527d5ddf90232e99"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/99e29d3596b16dabe4982548527d5ddf90232e99", "type": "zip", "shasum": "", "reference": "99e29d3596b16dabe4982548527d5ddf90232e99"}, "time": "2018-05-08T08:54:48+00:00", "require-dev": {"phpunit/phpunit": "~5.7.10|~6.5", "phpdocumentor/phpdocumentor": "^2.9"}}, {"description": "Mockery is a simple yet flexible PHP mock object framework for use in unit testing with PHPUnit, PHPSpec or any other testing framework. Its core goal is to offer a test double framework with a succinct API capable of clearly defining all possible object operations and interactions using a human readable Domain Specific Language (DSL). Designed as a drop in alternative to PHPUnit's phpunit-mock-objects library, Mockery is easy to integrate with PHPUnit and can operate alongside phpunit-mock-objects without the World ending.", "homepage": "http://github.com/mockery/mockery", "version": "1.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "1bac8c362b12f522fdd1f1fa3556284c91affa38"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/1bac8c362b12f522fdd1f1fa3556284c91affa38", "type": "zip", "shasum": "", "reference": "1bac8c362b12f522fdd1f1fa3556284c91affa38"}, "time": "2017-10-06T16:20:43+00:00", "require-dev": {"phpunit/phpunit": "~5.7|~6.1"}}, {"homepage": "http://github.com/padraic/mockery", "version": "1.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "0b23f71799b3e43406f4ce7fef112ac3f0b7a385"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/0b23f71799b3e43406f4ce7fef112ac3f0b7a385", "type": "zip", "shasum": "", "reference": "0b23f71799b3e43406f4ce7fef112ac3f0b7a385"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.0.0-alpha1"}, "time": "2017-02-02T08:52:46+00:00", "require-dev": {"phpunit/phpunit": "~5.7"}}, {"version": "0.9.11", "version_normalized": "********", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "be9bf28d8e57d67883cba9fcadfcff8caab667f8"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/be9bf28d8e57d67883cba9fcadfcff8caab667f8", "type": "zip", "shasum": "", "reference": "be9bf28d8e57d67883cba9fcadfcff8caab667f8"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9"}, "time": "2019-02-12T16:07:13+00:00", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "require": {"php": ">=5.3.2", "lib-pcre": ">=7.0", "hamcrest/hamcrest-php": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "0.9.10", "version_normalized": "********", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "4876fc0c7d9e5da49712554a35c94d84ed1e9ee5"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/4876fc0c7d9e5da49712554a35c94d84ed1e9ee5", "type": "zip", "shasum": "", "reference": "4876fc0c7d9e5da49712554a35c94d84ed1e9ee5"}, "time": "2018-11-13T20:50:16+00:00"}, {"version": "0.9.9", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "6fdb61243844dc924071d3404bb23994ea0b6856"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/6fdb61243844dc924071d3404bb23994ea0b6856", "type": "zip", "shasum": "", "reference": "6fdb61243844dc924071d3404bb23994ea0b6856"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.9"}, "time": "2017-02-28T12:52:32+00:00"}, {"version": "0.9.8", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "1e5e2ffdc4d71d7358ed58a6fdd30a4a0c506855"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/1e5e2ffdc4d71d7358ed58a6fdd30a4a0c506855", "type": "zip", "shasum": "", "reference": "1e5e2ffdc4d71d7358ed58a6fdd30a4a0c506855"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.8"}, "time": "2017-02-09T13:29:38+00:00"}, {"version": "0.9.7", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "4de7969f4664da3cef1ccd83866c9f59378c3371"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/4de7969f4664da3cef1ccd83866c9f59378c3371", "type": "zip", "shasum": "", "reference": "4de7969f4664da3cef1ccd83866c9f59378c3371"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.7"}, "time": "2016-12-19T14:50:55+00:00"}, {"version": "0.9.6", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "65d4ca18e15cb02eeb1e5336f884e46b9b905be0"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/65d4ca18e15cb02eeb1e5336f884e46b9b905be0", "type": "zip", "shasum": "", "reference": "65d4ca18e15cb02eeb1e5336f884e46b9b905be0"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.6"}, "time": "2016-09-30T12:09:40+00:00"}, {"version": "0.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "4db079511a283e5aba1b3c2fb19037c645e70fc2"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/4db079511a283e5aba1b3c2fb19037c645e70fc2", "type": "zip", "shasum": "", "reference": "4db079511a283e5aba1b3c2fb19037c645e70fc2"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.5"}, "time": "2016-05-22T21:52:33+00:00"}, {"version": "0.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "70bba85e4aabc9449626651f48b9018ede04f86b"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/70bba85e4aabc9449626651f48b9018ede04f86b", "type": "zip", "shasum": "", "reference": "70bba85e4aabc9449626651f48b9018ede04f86b"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.4"}, "time": "2015-04-02T19:54:00+00:00"}, {"description": "Mockery is a simple yet flexible PHP mock object framework for use in unit testing with PHPUnit, PHPSpec or any other testing framework. Its core goal is to offer a test double framework with a succint API capable of clearly defining all possible object operations and interactions using a human readable Domain Specific Language (DSL). Designed as a drop in alternative to PHPUnit's phpunit-mock-objects library, Mockery is easy to integrate with PHPUnit and can operate alongside phpunit-mock-objects without the World ending.", "version": "0.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "686f85fa5b3b079cc0157d7cd3e9adb97f0b41e1"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/686f85fa5b3b079cc0157d7cd3e9adb97f0b41e1", "type": "zip", "shasum": "", "reference": "686f85fa5b3b079cc0157d7cd3e9adb97f0b41e1"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.3"}, "time": "2014-12-22T10:06:19+00:00", "require": {"php": ">=5.3.2", "lib-pcre": ">=7.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "hamcrest/hamcrest-php": "~1.1", "satooshi/php-coveralls": "~0.7@dev"}}, {"version": "0.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "95a4855380dc70176c51807c678fb3bd6198529a"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/95a4855380dc70176c51807c678fb3bd6198529a", "type": "zip", "shasum": "", "reference": "95a4855380dc70176c51807c678fb3bd6198529a"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.2"}, "time": "2014-09-03T10:11:10+00:00"}, {"version": "0.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "17f63ee40ed14a8afb7ba1f0ae15cc4491d719d1"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/17f63ee40ed14a8afb7ba1f0ae15cc4491d719d1", "type": "zip", "shasum": "", "reference": "17f63ee40ed14a8afb7ba1f0ae15cc4491d719d1"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.1"}, "time": "2014-05-02T12:16:45+00:00"}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "0fd49fd3f6c984409802004b1dd7b55d0f35ce4a"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/0fd49fd3f6c984409802004b1dd7b55d0f35ce4a", "type": "zip", "shasum": "", "reference": "0fd49fd3f6c984409802004b1dd7b55d0f35ce4a"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9.0"}, "time": "2014-02-05T21:30:08+00:00", "require-dev": {"hamcrest/hamcrest-php": "~1.1", "satooshi/php-coveralls": "dev-master"}}, {"version": "0.8.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}], "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "35f0e18022f5538df9df8920a3d96c1761d63220"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/35f0e18022f5538df9df8920a3d96c1761d63220", "type": "zip", "shasum": "", "reference": "35f0e18022f5538df9df8920a3d96c1761d63220"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.8.0"}, "time": "2013-04-01T12:13:17+00:00", "require": {"php": ">=5.3.2"}, "require-dev": {"hamcrest/hamcrest": "1.1.0"}, "extra": "__unset"}, {"version": "0.7.2", "version_normalized": "*******", "license": ["New BSD"], "source": {"url": "https://github.com/mockery/mockery.git", "type": "git", "reference": "10ef0f8a63392f244e5b19de261b6a08eb8e4109"}, "dist": {"url": "https://api.github.com/repos/mockery/mockery/zipball/10ef0f8a63392f244e5b19de261b6a08eb8e4109", "type": "zip", "shasum": "", "reference": "10ef0f8a63392f244e5b19de261b6a08eb8e4109"}, "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.7.2"}, "time": "2012-01-24T20:22:39+00:00", "suggest": {"Hamcrest": "1.0.0"}, "require-dev": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 16 May 2024 03:30:52 GMT"}