{"minified": "composer/2.0", "packages": {"psr/container": [{"name": "psr/container", "description": "Common Container Interface (PHP FIG PSR-11)", "keywords": ["container", "psr", "container-interop", "PSR-11", "container-interface"], "homepage": "https://github.com/php-fig/container", "version": "2.0.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "type": "zip", "shasum": "", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "type": "library", "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "funding": [], "time": "2021-11-05T16:47:00+00:00", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=7.4.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "2ae37329ee82f91efadc282cc2d527fd6065a5ef"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/2ae37329ee82f91efadc282cc2d527fd6065a5ef", "type": "zip", "shasum": "", "reference": "2ae37329ee82f91efadc282cc2d527fd6065a5ef"}, "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.1"}, "time": "2021-03-24T13:40:57+00:00", "require": {"php": ">=7.2.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "68f5200c33f18c018db17eb869d4b0f97da17cad"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/68f5200c33f18c018db17eb869d4b0f97da17cad", "type": "zip", "shasum": "", "reference": "68f5200c33f18c018db17eb869d4b0f97da17cad"}, "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.0"}, "time": "2021-03-05T16:02:18+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "type": "zip", "shasum": "", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00", "require": {"php": ">=7.4.0"}, "extra": "__unset"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "type": "zip", "shasum": "", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00", "require": {"php": ">=7.2.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "9fc7aab7a78057a124384358ebae8a1711b6f6fc"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/9fc7aab7a78057a124384358ebae8a1711b6f6fc", "type": "zip", "shasum": "", "reference": "9fc7aab7a78057a124384358ebae8a1711b6f6fc"}, "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.0"}, "time": "2021-03-05T15:48:30+00:00", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}}, {"version": "1.0.0", "version_normalized": "*******", "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/container.git", "type": "git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "type": "zip", "shasum": "", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/master"}, "time": "2017-02-14T16:28:37+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=5.3.0"}, "funding": "__unset"}]}, "security-advisories": [], "last-modified": "Fri, 22 Mar 2024 12:22:52 GMT"}