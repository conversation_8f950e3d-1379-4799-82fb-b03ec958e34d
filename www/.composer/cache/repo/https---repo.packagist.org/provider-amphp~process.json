{"minified": "composer/2.0", "packages": {"amphp/process": [{"name": "amphp/process", "description": "A fiber-aware process manager based on Amp and Revolt.", "keywords": [], "homepage": "https://amphp.org/process", "version": "v2.0.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d", "type": "zip", "shasum": "", "reference": "52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d"}, "type": "library", "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.3"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-19T03:13:44+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Process\\": "src"}}, "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/sync": "^2", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.4"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "a79dc87100be857db2c4bbfd5369585a6d1e658c"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/a79dc87100be857db2c4bbfd5369585a6d1e658c", "type": "zip", "shasum": "", "reference": "a79dc87100be857db2c4bbfd5369585a6d1e658c"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.2"}, "time": "2024-02-13T20:38:21+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "a65d3bc1f36ef12d44df42a68f0f0643183f1052"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/a65d3bc1f36ef12d44df42a68f0f0643183f1052", "type": "zip", "shasum": "", "reference": "a65d3bc1f36ef12d44df42a68f0f0643183f1052"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.1"}, "time": "2023-01-15T16:00:57+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "9c9247fe8283b22e00a254746bf822da4b0b5ea4"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/9c9247fe8283b22e00a254746bf822da4b0b5ea4", "type": "zip", "shasum": "", "reference": "9c9247fe8283b22e00a254746bf822da4b0b5ea4"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0"}, "time": "2023-01-09T21:32:38+00:00"}, {"homepage": "https://github.com/amphp/process", "version": "v2.0.0-beta.7", "version_normalized": "*******-beta7", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "ad46c8b2fbea65d9ef09fd473b4477cfd33d102e"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/ad46c8b2fbea65d9ef09fd473b4477cfd33d102e", "type": "zip", "shasum": "", "reference": "ad46c8b2fbea65d9ef09fd473b4477cfd33d102e"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.7"}, "time": "2022-11-07T22:07:42+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2-dev", "psalm/phar": "^4.15", "jetbrains/phpstorm-attributes": "^1.0"}}, {"version": "v2.0.0-beta.6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "270f828a7dc85c7bac75499ce2970a93932557f6"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/270f828a7dc85c7bac75499ce2970a93932557f6", "type": "zip", "shasum": "", "reference": "270f828a7dc85c7bac75499ce2970a93932557f6"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.6"}, "time": "2022-09-26T19:20:38+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "revolt/event-loop": "^0.2"}}, {"version": "v2.0.0-beta.5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "ad241280d9ec9fa3cefa4f5dade74014ef9e6a3f"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/ad241280d9ec9fa3cefa4f5dade74014ef9e6a3f", "type": "zip", "shasum": "", "reference": "ad241280d9ec9fa3cefa4f5dade74014ef9e6a3f"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.5"}, "time": "2022-07-12T20:32:51+00:00"}, {"version": "v2.0.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "432c72e21bfca2a45a323ed152fcf05a0b847f6d"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/432c72e21bfca2a45a323ed152fcf05a0b847f6d", "type": "zip", "shasum": "", "reference": "432c72e21bfca2a45a323ed152fcf05a0b847f6d"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.4"}, "time": "2022-07-07T03:28:18+00:00"}, {"version": "v2.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "509742a0ac6e9253cfd38f10039ddd8768300d57"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/509742a0ac6e9253cfd38f10039ddd8768300d57", "type": "zip", "shasum": "", "reference": "509742a0ac6e9253cfd38f10039ddd8768300d57"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.3"}, "time": "2022-02-02T23:08:25+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/byte-stream": "^2", "revolt/event-loop": "^0.2"}}, {"version": "v2.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "4a2090e52737d6af841c891660e73badc111502d"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/4a2090e52737d6af841c891660e73badc111502d", "type": "zip", "shasum": "", "reference": "4a2090e52737d6af841c891660e73badc111502d"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.2"}, "time": "2021-12-17T22:54:46+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/sync": "^2", "revolt/event-loop": "^0.1.1"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "dev-master", "psalm/phar": "^4.15", "jetbrains/phpstorm-attributes": "^1.0"}}, {"version": "v2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "aa3fb025b51496485c93ae49e2af7d0a3689ec85"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/aa3fb025b51496485c93ae49e2af7d0a3689ec85", "type": "zip", "shasum": "", "reference": "aa3fb025b51496485c93ae49e2af7d0a3689ec85"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.0-beta.1"}, "time": "2021-12-14T00:17:54+00:00"}, {"description": "Asynchronous process manager.", "version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "55b837d4f1857b9bd7efb7bb859ae6b0e804f13f"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/55b837d4f1857b9bd7efb7bb859ae6b0e804f13f", "type": "zip", "shasum": "", "reference": "55b837d4f1857b9bd7efb7bb859ae6b0e804f13f"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.9"}, "time": "2024-12-13T17:38:25+00:00", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\Process\\": "lib"}}, "require": {"php": ">=7.1", "amphp/amp": "^2", "amphp/byte-stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^6", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master"}}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "187cdad6375dd7680f58556fa139a87c00787d61"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/187cdad6375dd7680f58556fa139a87c00787d61", "type": "zip", "shasum": "", "reference": "187cdad6375dd7680f58556fa139a87c00787d61"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.8"}, "time": "2024-12-13T17:31:18+00:00"}, {"version": "v1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "1949d85b6d71af2818ff68144304a98495628f19"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/1949d85b6d71af2818ff68144304a98495628f19", "type": "zip", "shasum": "", "reference": "1949d85b6d71af2818ff68144304a98495628f19"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.7"}, "time": "2024-04-19T03:00:28+00:00"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "2cd38052ddb200dcd73d34d8e06654dadb101e7c"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/2cd38052ddb200dcd73d34d8e06654dadb101e7c", "type": "zip", "shasum": "", "reference": "2cd38052ddb200dcd73d34d8e06654dadb101e7c"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.6"}, "time": "2024-03-21T19:24:36+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "04b4517bbfe436ab822b853d511165dafbfe115a"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/04b4517bbfe436ab822b853d511165dafbfe115a", "type": "zip", "shasum": "", "reference": "04b4517bbfe436ab822b853d511165dafbfe115a"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.5"}, "time": "2024-02-24T21:06:11+00:00", "require": {"php": ">=7", "amphp/amp": "^2", "amphp/byte-stream": "^1.4"}}, {"version": "v1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "76e9495fd6818b43a20167cb11d8a67f7744ee0f"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/76e9495fd6818b43a20167cb11d8a67f7744ee0f", "type": "zip", "shasum": "", "reference": "76e9495fd6818b43a20167cb11d8a67f7744ee0f"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.4"}, "time": "2022-07-06T23:50:12+00:00"}, {"version": "v1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "f09e3ed3b0a953ccbfff1140f12be4a884f0aa83"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/f09e3ed3b0a953ccbfff1140f12be4a884f0aa83", "type": "zip", "shasum": "", "reference": "f09e3ed3b0a953ccbfff1140f12be4a884f0aa83"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.3"}, "time": "2021-12-17T19:09:33+00:00"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "3d36327bf9b4c158cf3010f8f65c00854ec3a8b7"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/3d36327bf9b4c158cf3010f8f65c00854ec3a8b7", "type": "zip", "shasum": "", "reference": "3d36327bf9b4c158cf3010f8f65c00854ec3a8b7"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.2"}, "time": "2021-10-08T15:55:53+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "b88c6aef75c0b22f6f021141dd2d5e7c5db4c124"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/b88c6aef75c0b22f6f021141dd2d5e7c5db4c124", "type": "zip", "shasum": "", "reference": "b88c6aef75c0b22f6f021141dd2d5e7c5db4c124"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.1"}, "time": "2021-03-30T20:04:22+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "355b1e561b01c16ab3d78fada1ad47ccc96df70e"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/355b1e561b01c16ab3d78fada1ad47ccc96df70e", "type": "zip", "shasum": "", "reference": "355b1e561b01c16ab3d78fada1ad47ccc96df70e"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/master"}, "time": "2019-02-26T16:33:03+00:00", "funding": "__unset"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "e7e28219da753f865f9055e87d0b9b110ea607ba"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/e7e28219da753f865f9055e87d0b9b110ea607ba", "type": "zip", "shasum": "", "reference": "e7e28219da753f865f9055e87d0b9b110ea607ba"}, "time": "2019-01-15T21:42:43+00:00", "autoload": {"files": ["lib/constants.php"], "psr-4": {"Amp\\Process\\": "lib"}}}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "90f3f31cbd0eee7fa5ae03ce588b1fc8f122997c"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/90f3f31cbd0eee7fa5ae03ce588b1fc8f122997c", "type": "zip", "shasum": "", "reference": "90f3f31cbd0eee7fa5ae03ce588b1fc8f122997c"}, "time": "2018-11-09T00:02:00+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "51874750e3a39fe72926c52547d67f2c4d532781"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/51874750e3a39fe72926c52547d67f2c4d532781", "type": "zip", "shasum": "", "reference": "51874750e3a39fe72926c52547d67f2c4d532781"}, "time": "2018-11-08T04:47:07+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "9d0d7ab3601272ec66d2b13f6d606257926b2c40"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/9d0d7ab3601272ec66d2b13f6d606257926b2c40", "type": "zip", "shasum": "", "reference": "9d0d7ab3601272ec66d2b13f6d606257926b2c40"}, "time": "2018-10-20T16:34:05+00:00"}, {"version": "v0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "b795d20a7f6d5a0637128a02be613f520f1705fc"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/b795d20a7f6d5a0637128a02be613f520f1705fc", "type": "zip", "shasum": "", "reference": "b795d20a7f6d5a0637128a02be613f520f1705fc"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/windows-phar"}, "time": "2018-04-08T18:55:42+00:00", "require": {"php": ">=7", "amphp/amp": "^2", "amphp/byte-stream": "^1"}, "require-dev": {"phpunit/phpunit": "^6", "amphp/phpunit-util": "^1", "friendsofphp/php-cs-fixer": "^2.3"}}, {"version": "v0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "239605b2e8a9a7958563119e9b03fdabeb0ad4cc"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/239605b2e8a9a7958563119e9b03fdabeb0ad4cc", "type": "zip", "shasum": "", "reference": "239605b2e8a9a7958563119e9b03fdabeb0ad4cc"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/master"}, "time": "2018-03-08T15:09:10+00:00"}, {"version": "v0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "55bf8487677c0d2e14b46a78eea61a259a04b24d"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/55bf8487677c0d2e14b46a78eea61a259a04b24d", "type": "zip", "shasum": "", "reference": "55bf8487677c0d2e14b46a78eea61a259a04b24d"}, "time": "2017-12-05T21:53:41+00:00"}, {"version": "v0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "f1bf550910256741b79ff71b143f93edadec50ab"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/f1bf550910256741b79ff71b143f93edadec50ab", "type": "zip", "shasum": "", "reference": "f1bf550910256741b79ff71b143f93edadec50ab"}, "time": "2017-11-25T01:11:21+00:00"}, {"description": "Asynchronous process manager", "version": "v0.2.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "5aa6040fcf5c98bfb4f4a8e68305cb6cd6a3d37a"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/5aa6040fcf5c98bfb4f4a8e68305cb6cd6a3d37a", "type": "zip", "shasum": "", "reference": "5aa6040fcf5c98bfb4f4a8e68305cb6cd6a3d37a"}, "time": "2017-07-18T03:37:19+00:00", "autoload": {"psr-4": {"Amp\\Process\\": "lib"}}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1"}, "require-dev": {"phpunit/phpunit": "^6", "amphp/phpunit-util": "^1", "friendsofphp/php-cs-fixer": "^2.3", "kelunik/fqn-check": "^0.1.3"}}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "cc85c607b90ae33f97f3630ede5477cc7fa29a99"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/cc85c607b90ae33f97f3630ede5477cc7fa29a99", "type": "zip", "shasum": "", "reference": "cc85c607b90ae33f97f3630ede5477cc7fa29a99"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v0.2.x"}, "time": "2017-06-16T03:53:33+00:00"}, {"version": "v0.1.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "f22cca2af36e442b771c0de2e24e8025550d8ffc"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/f22cca2af36e442b771c0de2e24e8025550d8ffc", "type": "zip", "shasum": "", "reference": "f22cca2af36e442b771c0de2e24e8025550d8ffc"}, "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/master"}, "time": "2016-09-24T10:49:26+00:00", "autoload": {"classmap": [{"Amp\\Process": "Process.php"}]}, "require": {"amphp/amp": "^1"}, "require-dev": {"phpunit/phpunit": "^4.8", "fabpot/php-cs-fixer": "~1.9"}}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "255325ca82a92b05974efce27e9b92b2155fc408"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/255325ca82a92b05974efce27e9b92b2155fc408", "type": "zip", "shasum": "", "reference": "255325ca82a92b05974efce27e9b92b2155fc408"}, "time": "2015-12-24T12:44:49+00:00"}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "6addcdddee0b5c29c7f47e13e798c552c4fbe74b"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/6addcdddee0b5c29c7f47e13e798c552c4fbe74b", "type": "zip", "shasum": "", "reference": "6addcdddee0b5c29c7f47e13e798c552c4fbe74b"}, "time": "2015-09-10T22:59:23+00:00", "require-dev": "__unset"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/process.git", "type": "git", "reference": "e7a1d052fb75aa6adb16815aee4aca4bd3880f4f"}, "dist": {"url": "https://api.github.com/repos/amphp/process/zipball/e7a1d052fb75aa6adb16815aee4aca4bd3880f4f", "type": "zip", "shasum": "", "reference": "e7a1d052fb75aa6adb16815aee4aca4bd3880f4f"}, "time": "2015-09-10T21:53:51+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 13 Dec 2024 17:41:06 GMT"}