{"minified": "composer/2.0", "packages": {"nette/utils": [{"name": "nette/utils", "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "keywords": ["json", "paginator", "validation", "array", "utility", "password", "nette", "images", "slugify", "utf-8", "string", "core", "unicode", "datetime"], "homepage": "https://nette.org", "version": "v4.0.7", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "e67c4061eb40b9c113b218214e42cb5a0dda28f2"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/e67c4061eb40b9c113b218214e42cb5a0dda28f2", "type": "zip", "shasum": "", "reference": "e67c4061eb40b9c113b218214e42cb5a0dda28f2"}, "type": "library", "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.7"}, "funding": [], "time": "2025-06-03T04:55:08+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.5", "tracy/tracy": "^2.9", "phpstan/phpstan": "^1.0", "jetbrains/phpstorm-attributes": "dev-master"}, "suggest": {"ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-mbstring": "to use Strings::lower() etc...", "ext-gd": "to use Image", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}}, {"version": "v4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "ce708655043c7050eb050df361c5e313cf708309"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/ce708655043c7050eb050df361c5e313cf708309", "type": "zip", "shasum": "", "reference": "ce708655043c7050eb050df361c5e313cf708309"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.6"}, "time": "2025-03-30T21:06:30+00:00"}, {"version": "v4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "type": "zip", "shasum": "", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.5"}, "time": "2024-08-07T15:39:19+00:00"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "type": "zip", "shasum": "", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.4"}, "time": "2024-01-17T16:50:36+00:00", "require": {"php": ">=8.0 <8.4"}}, {"version": "v4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "a9d127dd6a203ce6d255b2e2db49759f7506e015"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/a9d127dd6a203ce6d255b2e2db49759f7506e015", "type": "zip", "shasum": "", "reference": "a9d127dd6a203ce6d255b2e2db49759f7506e015"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.3"}, "time": "2023-10-29T21:02:13+00:00"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "cead6637226456b35e1175cc53797dd585d85545"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/cead6637226456b35e1175cc53797dd585d85545", "type": "zip", "shasum": "", "reference": "cead6637226456b35e1175cc53797dd585d85545"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.2"}, "time": "2023-09-19T11:58:07+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "9124157137da01b1f5a5a22d6486cb975f26db7e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/9124157137da01b1f5a5a22d6486cb975f26db7e", "type": "zip", "shasum": "", "reference": "9124157137da01b1f5a5a22d6486cb975f26db7e"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.1"}, "time": "2023-07-30T15:42:21+00:00", "suggest": {"ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "cacdbf5a91a657ede665c541eda28941d4b09c1e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/cacdbf5a91a657ede665c541eda28941d4b09c1e", "type": "zip", "shasum": "", "reference": "cacdbf5a91a657ede665c541eda28941d4b09c1e"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.0"}, "time": "2023-02-02T10:41:53+00:00", "require": {"php": ">=8.0 <8.3"}, "require-dev": {"nette/tester": "^2.4", "tracy/tracy": "^2.9", "phpstan/phpstan": "^1.0", "jetbrains/phpstorm-attributes": "dev-master"}}, {"version": "v3.2.10", "version_normalized": "********", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/a4175c62652f2300c8017fb7e640f9ccb11648d2", "type": "zip", "shasum": "", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.10"}, "time": "2023-07-30T15:38:18+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require": {"php": ">=7.2 <8.4"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3", "phpstan/phpstan": "^1.0", "jetbrains/phpstorm-attributes": "dev-master"}, "conflict": {"nette/di": "<3.0.6"}}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "c91bac3470c34b2ecd5400f6e6fdf0b64a836a5c"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/c91bac3470c34b2ecd5400f6e6fdf0b64a836a5c", "type": "zip", "shasum": "", "reference": "c91bac3470c34b2ecd5400f6e6fdf0b64a836a5c"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.9"}, "time": "2023-01-18T03:26:20+00:00", "require": {"php": ">=7.2 <8.3"}}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "02a54c4c872b99e4ec05c4aec54b5a06eb0f6368"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/02a54c4c872b99e4ec05c4aec54b5a06eb0f6368", "type": "zip", "shasum": "", "reference": "02a54c4c872b99e4ec05c4aec54b5a06eb0f6368"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.8"}, "time": "2022-09-12T23:36:20+00:00", "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3", "phpstan/phpstan": "^1.0"}}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "0af4e3de4df9f1543534beab255ccf459e7a2c99"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/0af4e3de4df9f1543534beab255ccf459e7a2c99", "type": "zip", "shasum": "", "reference": "0af4e3de4df9f1543534beab255ccf459e7a2c99"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.7"}, "time": "2022-01-24T11:29:14+00:00", "require": {"php": ">=7.2 <8.2"}}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "2f261e55bd6a12057442045bf2c249806abc1d02"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/2f261e55bd6a12057442045bf2c249806abc1d02", "type": "zip", "shasum": "", "reference": "2f261e55bd6a12057442045bf2c249806abc1d02"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.6"}, "time": "2021-11-24T15:47:23+00:00"}, {"version": "v3.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "9cd80396ca58d7969ab44fc7afcf03624dfa526e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/9cd80396ca58d7969ab44fc7afcf03624dfa526e", "type": "zip", "shasum": "", "reference": "9cd80396ca58d7969ab44fc7afcf03624dfa526e"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.5"}, "time": "2021-09-20T10:50:11+00:00", "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3", "phpstan/phpstan": "^0.12"}}, {"version": "v3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822", "type": "zip", "shasum": "", "reference": "5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.3"}, "time": "2021-08-16T21:05:00+00:00", "require": {"php": ">=7.2 <8.1"}}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "967cfc4f9a1acd5f1058d76715a424c53343c20c"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/967cfc4f9a1acd5f1058d76715a424c53343c20c", "type": "zip", "shasum": "", "reference": "967cfc4f9a1acd5f1058d76715a424c53343c20c"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.2"}, "time": "2021-03-03T22:53:25+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "2bc2f58079c920c2ecbb6935645abf6f2f5f94ba"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/2bc2f58079c920c2ecbb6935645abf6f2f5f94ba", "type": "zip", "shasum": "", "reference": "2bc2f58079c920c2ecbb6935645abf6f2f5f94ba"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.1"}, "time": "2021-01-11T03:05:59+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "d0427c1811462dbb6c503143eabe5478b26685f7"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/d0427c1811462dbb6c503143eabe5478b26685f7", "type": "zip", "shasum": "", "reference": "d0427c1811462dbb6c503143eabe5478b26685f7"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.0"}, "time": "2020-11-25T23:47:50+00:00"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "2c8d1628317fddc692d90fd7732e3dd98327dbf0"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/2c8d1628317fddc692d90fd7732e3dd98327dbf0", "type": "zip", "shasum": "", "reference": "2c8d1628317fddc692d90fd7732e3dd98327dbf0"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.1.6"}, "time": "2021-09-20T10:38:05+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "require": {"php": ">=7.1"}, "conflict": "__unset"}, {"version": "v3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "19b3057d44cf5306c64f3c51a7fb77fa834fda1c"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/19b3057d44cf5306c64f3c51a7fb77fa834fda1c", "type": "zip", "shasum": "", "reference": "19b3057d44cf5306c64f3c51a7fb77fa834fda1c"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.1.4"}, "time": "2020-11-25T20:53:17+00:00"}, {"description": "🛠 Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "c09937fbb24987b2a41c6022ebe84f4f1b8eec0f"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/c09937fbb24987b2a41c6022ebe84f4f1b8eec0f", "type": "zip", "shasum": "", "reference": "c09937fbb24987b2a41c6022ebe84f4f1b8eec0f"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.1.3"}, "time": "2020-08-07T10:34:21+00:00"}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "488f58378bba71767e7831c83f9e0fa808bf83b9"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/488f58378bba71767e7831c83f9e0fa808bf83b9", "type": "zip", "shasum": "", "reference": "488f58378bba71767e7831c83f9e0fa808bf83b9"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.1.2"}, "time": "2020-05-27T09:58:51+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "2c17d16d8887579ae1c0898ff94a3668997fd3eb"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/2c17d16d8887579ae1c0898ff94a3668997fd3eb", "type": "zip", "shasum": "", "reference": "2c17d16d8887579ae1c0898ff94a3668997fd3eb"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.1.1"}, "time": "2020-02-09T14:10:55+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "funding": "__unset"}, {"version": "v3.1.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "d6cd63d77dd9a85c3a2fae707e1255e44c2bc182"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/d6cd63d77dd9a85c3a2fae707e1255e44c2bc182", "type": "zip", "shasum": "", "reference": "d6cd63d77dd9a85c3a2fae707e1255e44c2bc182"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/master"}, "time": "2020-01-03T18:13:31+00:00"}, {"version": "v3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "4fa14b593a26aaaf1e2c4842aaff731ecca1fc4a"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/4fa14b593a26aaaf1e2c4842aaff731ecca1fc4a", "type": "zip", "shasum": "", "reference": "4fa14b593a26aaaf1e2c4842aaff731ecca1fc4a"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.0.4"}, "funding": [], "time": "2020-11-05T23:46:36+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "f1b5ce0fae07f13e066e64f9a8f59e53b8f4982e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/f1b5ce0fae07f13e066e64f9a8f59e53b8f4982e", "type": "zip", "shasum": "", "reference": "f1b5ce0fae07f13e066e64f9a8f59e53b8f4982e"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.0.3"}, "time": "2019-12-27T03:47:50+00:00", "funding": "__unset"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "c133e18c922dcf3ad07673077d92d92cef25a148"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/c133e18c922dcf3ad07673077d92d92cef25a148", "type": "zip", "shasum": "", "reference": "c133e18c922dcf3ad07673077d92d92cef25a148"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.0.2"}, "time": "2019-10-21T20:40:16+00:00", "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "bd961f49b211997202bda1d0fbc410905be370d4"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/bd961f49b211997202bda1d0fbc410905be370d4", "type": "zip", "shasum": "", "reference": "bd961f49b211997202bda1d0fbc410905be370d4"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.0.1"}, "time": "2019-03-22T01:00:30+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image"}}, {"description": "? Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "ec1e4055c295d73bb9e8ce27be859f434a6f6806"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/ec1e4055c295d73bb9e8ce27be859f434a6f6806", "type": "zip", "shasum": "", "reference": "ec1e4055c295d73bb9e8ce27be859f434a6f6806"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/master"}, "time": "2019-02-05T21:43:00+00:00", "conflict": {"nette/nette": "<2.2"}}, {"description": "🛠 Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "d272f87cd6491377231702b1ccd920b6e981b713"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/d272f87cd6491377231702b1ccd920b6e981b713", "type": "zip", "shasum": "", "reference": "d272f87cd6491377231702b1ccd920b6e981b713"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5.7"}, "funding": [], "time": "2020-12-13T14:12:17+00:00", "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "require": {"php": ">=5.6.0"}, "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image"}}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "5915eeca39f2773f2c4bc2e9e8b3118aeedf7ad9"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/5915eeca39f2773f2c4bc2e9e8b3118aeedf7ad9", "type": "zip", "shasum": "", "reference": "5915eeca39f2773f2c4bc2e9e8b3118aeedf7ad9"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5.6"}, "time": "2020-11-26T00:13:25+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "fa89fb64e3cce9b9b072f8c58fa74542507d727d"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/fa89fb64e3cce9b9b072f8c58fa74542507d727d", "type": "zip", "shasum": "", "reference": "fa89fb64e3cce9b9b072f8c58fa74542507d727d"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5.5"}, "time": "2020-11-05T23:45:34+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "b343b5749f8c2daa0d15f30380ccdba0579c2ed1"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/b343b5749f8c2daa0d15f30380ccdba0579c2ed1", "type": "zip", "shasum": "", "reference": "b343b5749f8c2daa0d15f30380ccdba0579c2ed1"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5"}, "time": "2019-11-19T00:32:02+00:00", "funding": "__unset"}, {"description": "? Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "17b9f76f2abd0c943adfb556e56f2165460b15ce"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/17b9f76f2abd0c943adfb556e56f2165460b15ce", "type": "zip", "shasum": "", "reference": "17b9f76f2abd0c943adfb556e56f2165460b15ce"}, "time": "2018-09-18T10:22:16+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "183069866dc477fcfbac393ed486aaa6d93d19a5"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/183069866dc477fcfbac393ed486aaa6d93d19a5", "type": "zip", "shasum": "", "reference": "183069866dc477fcfbac393ed486aaa6d93d19a5"}, "time": "2018-05-02T17:16:08+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "8a85ce76298c8a8941f912b8fa3ee93ca17d2ebc"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/8a85ce76298c8a8941f912b8fa3ee93ca17d2ebc", "type": "zip", "shasum": "", "reference": "8a85ce76298c8a8941f912b8fa3ee93ca17d2ebc"}, "time": "2018-02-19T14:42:42+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "b7b7d583eb04d7a8fe787e40cf12e14cfa61520e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/b7b7d583eb04d7a8fe787e40cf12e14cfa61520e", "type": "zip", "shasum": "", "reference": "b7b7d583eb04d7a8fe787e40cf12e14cfa61520e"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5.0"}, "time": "2018-02-17T17:33:12+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "89a88fc36f5bb76f9355ed8a09820cefcbb0af39"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/89a88fc36f5bb76f9355ed8a09820cefcbb0af39", "type": "zip", "shasum": "", "reference": "89a88fc36f5bb76f9355ed8a09820cefcbb0af39"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.4"}, "time": "2018-09-18T10:09:08+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "1e08eb4c9d26ae5aedced8260e8b4ed951ee4aa6"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/1e08eb4c9d26ae5aedced8260e8b4ed951ee4aa6", "type": "zip", "shasum": "", "reference": "1e08eb4c9d26ae5aedced8260e8b4ed951ee4aa6"}, "time": "2018-02-06T15:40:36+00:00"}, {"version": "v2.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "f1584033b5af945b470533b466b81a789d532034"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/f1584033b5af945b470533b466b81a789d532034", "type": "zip", "shasum": "", "reference": "f1584033b5af945b470533b466b81a789d532034"}, "time": "2017-08-20T17:32:29+00:00"}, {"description": "Nette Utility Classes", "keywords": [], "version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "2750d3948e255f59715a5e01c7453a1fec610996"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/2750d3948e255f59715a5e01c7453a1fec610996", "type": "zip", "shasum": "", "reference": "2750d3948e255f59715a5e01c7453a1fec610996"}, "time": "2017-07-11T19:25:29+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "266160aec0d99516e0ea510de1dfa24a0dc1e76e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/266160aec0d99516e0ea510de1dfa24a0dc1e76e", "type": "zip", "shasum": "", "reference": "266160aec0d99516e0ea510de1dfa24a0dc1e76e"}, "time": "2017-04-26T10:04:49+00:00"}, {"version": "v2.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "28ad4e2a6dcf143c23bde969a825f10a5d513602"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/28ad4e2a6dcf143c23bde969a825f10a5d513602", "type": "zip", "shasum": "", "reference": "28ad4e2a6dcf143c23bde969a825f10a5d513602"}, "time": "2017-03-29T16:55:54+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image", "https://nette.org/donate": "\u001b[1;37;42m Please consider supporting <PERSON><PERSON> via a donation \u001b[0m"}}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "714afb64d0da07d0da7178cbf5680008e4b0180b"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/714afb64d0da07d0da7178cbf5680008e4b0180b", "type": "zip", "shasum": "", "reference": "714afb64d0da07d0da7178cbf5680008e4b0180b"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.4.4"}, "time": "2017-01-16T12:30:14+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image"}}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "d552403bd9337eafd646a7e252c0256e281115dc"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/d552403bd9337eafd646a7e252c0256e281115dc", "type": "zip", "shasum": "", "reference": "d552403bd9337eafd646a7e252c0256e281115dc"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.4"}, "time": "2017-01-09T20:22:19+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "fd2e67c2ce28da409864507d8d124621780d036d"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/fd2e67c2ce28da409864507d8d124621780d036d", "type": "zip", "shasum": "", "reference": "fd2e67c2ce28da409864507d8d124621780d036d"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/master"}, "time": "2016-12-19T22:01:55+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "a2c86f48afab41e4a68bd161aab6b14e2458ab8a"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/a2c86f48afab41e4a68bd161aab6b14e2458ab8a", "type": "zip", "shasum": "", "reference": "a2c86f48afab41e4a68bd161aab6b14e2458ab8a"}, "time": "2016-09-28T17:57:14+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "c455ade9f24a1f99aa81772516764045296b8ca0"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/c455ade9f24a1f99aa81772516764045296b8ca0", "type": "zip", "shasum": "", "reference": "c455ade9f24a1f99aa81772516764045296b8ca0"}, "time": "2016-06-17T13:15:10+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-gd": "to use Image"}}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "f213ad4d9dfb7221322bd2808e2004b61c8ece8e"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/f213ad4d9dfb7221322bd2808e2004b61c8ece8e", "type": "zip", "shasum": "", "reference": "f213ad4d9dfb7221322bd2808e2004b61c8ece8e"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.3"}, "time": "2016-12-12T12:20:10+00:00", "require": {"php": ">=5.3.1"}, "require-dev": {"nette/tester": "~1.0"}, "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available", "ext-gd": "to use Image"}, "extra": "__unset"}, {"version": "v2.3.10", "version_normalized": "********", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "66ceba196e4535ca3bf8f835db50d7e02339fdb0"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/66ceba196e4535ca3bf8f835db50d7e02339fdb0", "type": "zip", "shasum": "", "reference": "66ceba196e4535ca3bf8f835db50d7e02339fdb0"}, "time": "2016-06-17T13:28:41+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-gd": "to use Image"}}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "f6586f827292bd35c8593df943437f2247ba5337"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/f6586f827292bd35c8593df943437f2247ba5337", "type": "zip", "shasum": "", "reference": "f6586f827292bd35c8593df943437f2247ba5337"}, "time": "2016-04-21T09:36:23+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "34ca4cd67bb768199aae2879eb8ad0ab016f2d8a"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/34ca4cd67bb768199aae2879eb8ad0ab016f2d8a", "type": "zip", "shasum": "", "reference": "34ca4cd67bb768199aae2879eb8ad0ab016f2d8a"}, "time": "2016-03-18T17:36:54+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "6f1ed73088c28a24acc9657ca14b3418a270e24b"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/6f1ed73088c28a24acc9657ca14b3418a270e24b", "type": "zip", "shasum": "", "reference": "6f1ed73088c28a24acc9657ca14b3418a270e24b"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.3.7"}, "time": "2015-11-30T00:11:35+00:00"}, {"version": "v2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "c9dfaec788eb65d5ef10cefed0ae63bc76febaa8"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/c9dfaec788eb65d5ef10cefed0ae63bc76febaa8", "type": "zip", "shasum": "", "reference": "c9dfaec788eb65d5ef10cefed0ae63bc76febaa8"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.3"}, "time": "2015-10-05T12:18:24+00:00"}, {"homepage": "http://nette.org", "version": "v2.3.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "298e955ad903c212dc766aa3f9f8ca840b6936eb"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/298e955ad903c212dc766aa3f9f8ca840b6936eb", "type": "zip", "shasum": "", "reference": "298e955ad903c212dc766aa3f9f8ca840b6936eb"}, "time": "2015-09-09T00:59:33+00:00"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "89a7973a4da73b47a13e23f13207d53d18c4de32"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/89a7973a4da73b47a13e23f13207d53d18c4de32", "type": "zip", "shasum": "", "reference": "89a7973a4da73b47a13e23f13207d53d18c4de32"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.3.4"}, "time": "2015-08-23T12:31:04+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "ff80fce39fdc381e7e0db6cc5ffc82162e59f6bb"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/ff80fce39fdc381e7e0db6cc5ffc82162e59f6bb", "type": "zip", "shasum": "", "reference": "ff80fce39fdc381e7e0db6cc5ffc82162e59f6bb"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.3"}, "time": "2015-07-13T22:30:00+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "1754fccaa3577b6d88abffcd883939da334a3b8a"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/1754fccaa3577b6d88abffcd883939da334a3b8a", "type": "zip", "shasum": "", "reference": "1754fccaa3577b6d88abffcd883939da334a3b8a"}, "time": "2015-06-17T17:12:44+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "cda96ff718e7bad5e949527dbfa1d6d0498181d9"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/cda96ff718e7bad5e949527dbfa1d6d0498181d9", "type": "zip", "shasum": "", "reference": "cda96ff718e7bad5e949527dbfa1d6d0498181d9"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/master"}, "time": "2015-03-17T21:49:28+00:00", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "29bfb4424689014e0749e14eef8de18c4b0fd020"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/29bfb4424689014e0749e14eef8de18c4b0fd020", "type": "zip", "shasum": "", "reference": "29bfb4424689014e0749e14eef8de18c4b0fd020"}, "time": "2015-02-24T21:21:17+00:00"}, {"homepage": "https://nette.org", "version": "v2.2.8", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "6f48d69b24a84c92bf4ef2179c0dc8647220c5a0"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/6f48d69b24a84c92bf4ef2179c0dc8647220c5a0", "type": "zip", "shasum": "", "reference": "6f48d69b24a84c92bf4ef2179c0dc8647220c5a0"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2.8"}, "time": "2015-12-03T01:32:27+00:00", "suggest": {"ext-iconv": "to use Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc...", "ext-gd": "to use Image"}, "extra": "__unset"}, {"homepage": "http://nette.org", "version": "v2.2.7", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "1d980850f42652bc41cdc2b46789b015d772871b"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/1d980850f42652bc41cdc2b46789b015d772871b", "type": "zip", "shasum": "", "reference": "1d980850f42652bc41cdc2b46789b015d772871b"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2"}, "time": "2015-08-05T10:38:03+00:00"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "734282780a2ec40fb4b3269ba9f8c3762a5bdab0"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/734282780a2ec40fb4b3269ba9f8c3762a5bdab0", "type": "zip", "shasum": "", "reference": "734282780a2ec40fb4b3269ba9f8c3762a5bdab0"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2.6"}, "time": "2015-03-26T17:45:08+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "ca58dcb965b5eb9b86ea3e852a349481c7240173"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/ca58dcb965b5eb9b86ea3e852a349481c7240173", "type": "zip", "shasum": "", "reference": "ca58dcb965b5eb9b86ea3e852a349481c7240173"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2"}, "time": "2015-03-09T23:43:12+00:00"}, {"version": "v2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "063460f08b9ca38b1609a15ed95d18bfaa6e3b90"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/063460f08b9ca38b1609a15ed95d18bfaa6e3b90", "type": "zip", "shasum": "", "reference": "063460f08b9ca38b1609a15ed95d18bfaa6e3b90"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2.4"}, "time": "2014-12-30T09:55:38+00:00"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "09161a4a6311ead542ce1f925b976c6525028e66"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/09161a4a6311ead542ce1f925b976c6525028e66", "type": "zip", "shasum": "", "reference": "09161a4a6311ead542ce1f925b976c6525028e66"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2.3-RC"}, "time": "2014-10-26T23:12:20+00:00"}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "9166030811103af7101630bddc0bb1b91a7e36f7"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/9166030811103af7101630bddc0bb1b91a7e36f7", "type": "zip", "shasum": "", "reference": "9166030811103af7101630bddc0bb1b91a7e36f7"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2"}, "time": "2014-08-24T23:29:12+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "918a80c857ae0856f908084b02e9b9ff3f02c11f"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/918a80c857ae0856f908084b02e9b9ff3f02c11f", "type": "zip", "shasum": "", "reference": "918a80c857ae0856f908084b02e9b9ff3f02c11f"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2.1"}, "time": "2014-05-25T06:15:25+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/utils.git", "type": "git", "reference": "57aef4e01083dd1b3b7e3bdd7006659a56fb23b3"}, "dist": {"url": "https://api.github.com/repos/nette/utils/zipball/57aef4e01083dd1b3b7e3bdd7006659a56fb23b3", "type": "zip", "shasum": "", "reference": "57aef4e01083dd1b3b7e3bdd7006659a56fb23b3"}, "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.2.0"}, "time": "2014-05-25T06:14:53+00:00"}]}, "security-advisories": [], "last-modified": "Tue, 03 Jun 2025 05:01:09 GMT"}