{"minified": "composer/2.0", "packages": {"spatie/invade": [{"name": "spatie/invade", "description": "A PHP function to work with private properties and methods", "keywords": ["spatie", "invade"], "homepage": "https://github.com/spatie/invade", "version": "2.1.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "b920f6411d21df4e8610a138e2e87ae4957d7f63"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/b920f6411d21df4e8610a138e2e87ae4957d7f63", "type": "zip", "shasum": "", "reference": "b920f6411d21df4e8610a138e2e87ae4957d7f63"}, "type": "library", "support": {"source": "https://github.com/spatie/invade/tree/2.1.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-05-17T09:06:10+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Spatie\\Invade\\": "src"}}, "require": {"php": "^8.0"}, "require-dev": {"pestphp/pest": "^1.20", "phpstan/phpstan": "^1.4", "spatie/ray": "^1.28"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "7b20a25486de69198e402da20dc924d8bcc8024a"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/7b20a25486de69198e402da20dc924d8bcc8024a", "type": "zip", "shasum": "", "reference": "7b20a25486de69198e402da20dc924d8bcc8024a"}, "support": {"source": "https://github.com/spatie/invade/tree/2.0.0"}, "time": "2023-07-19T18:55:36+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "d0a9c895a96152549d478a7e3420e19039eef038"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/d0a9c895a96152549d478a7e3420e19039eef038", "type": "zip", "shasum": "", "reference": "d0a9c895a96152549d478a7e3420e19039eef038"}, "support": {"source": "https://github.com/spatie/invade/tree/1.1.1"}, "time": "2022-07-05T09:31:00+00:00", "extra": {"phpstan": {"includes": ["phpstan-extension.neon"]}}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "916f63fbdf36aa21105cba5508b519338888c42c"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/916f63fbdf36aa21105cba5508b519338888c42c", "type": "zip", "shasum": "", "reference": "916f63fbdf36aa21105cba5508b519338888c42c"}, "support": {"source": "https://github.com/spatie/invade/tree/1.1.0"}, "time": "2022-07-02T20:29:03+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "43a5381888c724b4c5fcf237a306b20a35d4770e"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/43a5381888c724b4c5fcf237a306b20a35d4770e", "type": "zip", "shasum": "", "reference": "43a5381888c724b4c5fcf237a306b20a35d4770e"}, "support": {"source": "https://github.com/spatie/invade/tree/1.0.3"}, "time": "2022-06-06T14:01:57+00:00", "extra": "__unset"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "1d04b57490ecf86daf686f83a27b33bea91dac61"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/1d04b57490ecf86daf686f83a27b33bea91dac61", "type": "zip", "shasum": "", "reference": "1d04b57490ecf86daf686f83a27b33bea91dac61"}, "support": {"source": "https://github.com/spatie/invade/tree/1.0.2"}, "time": "2022-02-21T10:39:13+00:00", "require-dev": {"pestphp/pest": "^1.20", "spatie/ray": "^1.28"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "fba27b11e7a328d7ef6dd092b8c66f402cf00b18"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/fba27b11e7a328d7ef6dd092b8c66f402cf00b18", "type": "zip", "shasum": "", "reference": "fba27b11e7a328d7ef6dd092b8c66f402cf00b18"}, "support": {"source": "https://github.com/spatie/invade/tree/1.0.1"}, "time": "2022-02-11T20:32:06+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "ecb0dac2b196886d92daa2e2ba8905b75a102b5c"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/ecb0dac2b196886d92daa2e2ba8905b75a102b5c", "type": "zip", "shasum": "", "reference": "ecb0dac2b196886d92daa2e2ba8905b75a102b5c"}, "support": {"source": "https://github.com/spatie/invade/tree/1.0.0"}, "time": "2022-02-11T16:05:34+00:00"}, {"version": "0.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "79e3a98914e3630dfde533437579e90bf46b4eef"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/79e3a98914e3630dfde533437579e90bf46b4eef", "type": "zip", "shasum": "", "reference": "79e3a98914e3630dfde533437579e90bf46b4eef"}, "support": {"source": "https://github.com/spatie/invade/tree/0.0.2"}, "time": "2022-02-11T16:04:24+00:00"}, {"description": "A PHP function to read private properties", "version": "0.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/invade.git", "type": "git", "reference": "807ed3a7fcd813102deb185cf97eafd5448f1bc9"}, "dist": {"url": "https://api.github.com/repos/spatie/invade/zipball/807ed3a7fcd813102deb185cf97eafd5448f1bc9", "type": "zip", "shasum": "", "reference": "807ed3a7fcd813102deb185cf97eafd5448f1bc9"}, "support": {"issues": "https://github.com/spatie/invade/issues", "source": "https://github.com/spatie/invade/tree/0.0.1"}, "time": "2022-02-11T15:46:43+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 17 May 2024 09:06:30 GMT"}