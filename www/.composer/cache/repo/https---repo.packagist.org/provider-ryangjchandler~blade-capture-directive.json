{"minified": "composer/2.0", "packages": {"ryangjchandler/blade-capture-directive": [{"name": "ryangjchandler/blade-capture-directive", "description": "Create inline partials in your Blade templates with ease.", "keywords": ["laravel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blade-capture-directive"], "homepage": "https://github.com/ryangjchandler/blade-capture-directive", "version": "v1.1.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "bbb1513dfd89eaec87a47fe0c449a7e3d4a1976d"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/bbb1513dfd89eaec87a47fe0c449a7e3d4a1976d", "type": "zip", "shasum": "", "reference": "bbb1513dfd89eaec87a47fe0c449a7e3d4a1976d"}, "type": "library", "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v1.1.0"}, "funding": [{"url": "https://github.com/ryang<PERSON><PERSON><PERSON>", "type": "github"}], "time": "2025-02-25T09:09:36+00:00", "autoload": {"psr-4": {"RyanChandler\\BladeCaptureDirective\\": "src", "RyanChandler\\BladeCaptureDirective\\Database\\Factories\\": "database/factories"}}, "extra": {"laravel": {"aliases": {"BladeCaptureDirective": "RyanChandler\\BladeCaptureDirective\\Facades\\BladeCaptureDirective"}, "providers": ["RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider"]}}, "require": {"php": "^8.1", "spatie/laravel-package-tools": "^1.9.2", "illuminate/contracts": "^10.0|^11.0|^12.0"}, "require-dev": {"nunomaduro/collision": "^7.0|^8.0", "nunomaduro/larastan": "^2.0|^3.0", "orchestra/testbench": "^8.0|^9.0|^10.0", "pestphp/pest": "^2.0|^3.7", "pestphp/pest-plugin-laravel": "^2.0|^3.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0|^2.0", "phpstan/phpstan-phpunit": "^1.0|^2.0", "phpunit/phpunit": "^10.0|^11.5.3", "spatie/laravel-ray": "^1.26"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "cb6f58663d97f17bece176295240b740835e14f1"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/cb6f58663d97f17bece176295240b740835e14f1", "type": "zip", "shasum": "", "reference": "cb6f58663d97f17bece176295240b740835e14f1"}, "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v1.0.0"}, "time": "2024-02-26T18:08:49+00:00", "require": {"php": "^8.1", "spatie/laravel-package-tools": "^1.9.2", "illuminate/contracts": "^10.0|^11.0"}, "require-dev": {"nunomaduro/collision": "^7.0|^8.0", "nunomaduro/larastan": "^2.0", "orchestra/testbench": "^8.0|^9.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ray": "^1.26"}}, {"version": "v0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "62fd2ecb50b938a46025093bcb64fcaddd531f89"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/62fd2ecb50b938a46025093bcb64fcaddd531f89", "type": "zip", "shasum": "", "reference": "62fd2ecb50b938a46025093bcb64fcaddd531f89"}, "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v0.3.0"}, "time": "2023-02-14T16:54:54+00:00", "require": {"php": "^8.0", "spatie/laravel-package-tools": "^1.9.2", "illuminate/contracts": "^9.0|^10.0"}, "require-dev": {"nunomaduro/collision": "^6.0|^7.0", "nunomaduro/larastan": "^2.0", "orchestra/testbench": "^7.22|^8.0", "pestphp/pest": "^1.21", "pestphp/pest-plugin-laravel": "^1.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "spatie/laravel-ray": "^1.26"}}, {"version": "v0.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "be41afbd86057989d84f1aaea8d00f3b1e5c50e1"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/be41afbd86057989d84f1aaea8d00f3b1e5c50e1", "type": "zip", "shasum": "", "reference": "be41afbd86057989d84f1aaea8d00f3b1e5c50e1"}, "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v0.2.2"}, "time": "2022-09-02T11:04:28+00:00", "require": {"php": "^8.0", "spatie/laravel-package-tools": "^1.9.2", "illuminate/contracts": "^8.0|^9.0"}, "require-dev": {"nunomaduro/collision": "^5.0|^6.0", "nunomaduro/larastan": "^1.0", "orchestra/testbench": "^6.23|^7.0", "pestphp/pest": "^1.21", "pestphp/pest-plugin-laravel": "^1.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "spatie/laravel-ray": "^1.26"}}, {"version": "v0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "80addaf4a498b7c0217ad1eba8195bd31e606962"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/80addaf4a498b7c0217ad1eba8195bd31e606962", "type": "zip", "shasum": "", "reference": "80addaf4a498b7c0217ad1eba8195bd31e606962"}, "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v0.2.1"}, "time": "2022-08-23T09:44:35+00:00", "require": {"php": "^8.0", "spatie/laravel-package-tools": "^1.9.2", "illuminate/contracts": "^9.0"}, "require-dev": {"nunomaduro/collision": "^6.0", "nunomaduro/larastan": "^2.0.1", "orchestra/testbench": "^7.0", "pestphp/pest": "^1.21", "pestphp/pest-plugin-laravel": "^1.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "spatie/laravel-ray": "^1.26"}}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "ad2b0de77a09cf9c4c775f7ebf3b323f83ec53b5"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/ad2b0de77a09cf9c4c775f7ebf3b323f83ec53b5", "type": "zip", "shasum": "", "reference": "ad2b0de77a09cf9c4c775f7ebf3b323f83ec53b5"}, "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v0.2.0"}, "time": "2022-03-11T01:14:12+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ryangjchandler/blade-capture-directive.git", "type": "git", "reference": "44227c5128dc064b91a4ec5d056d154217eb2137"}, "dist": {"url": "https://api.github.com/repos/ryangjchandler/blade-capture-directive/zipball/44227c5128dc064b91a4ec5d056d154217eb2137", "type": "zip", "shasum": "", "reference": "44227c5128dc064b91a4ec5d056d154217eb2137"}, "support": {"issues": "https://github.com/ryangjchandler/blade-capture-directive/issues", "source": "https://github.com/ryangjchandler/blade-capture-directive/tree/v0.1.0"}, "time": "2022-03-10T21:59:19+00:00"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 25 Feb 2025 09:10:01 GMT"}