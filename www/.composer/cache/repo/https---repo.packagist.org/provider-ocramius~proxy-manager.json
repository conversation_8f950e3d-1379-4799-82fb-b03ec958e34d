{"minified": "composer/2.0", "packages": {"ocramius/proxy-manager": [{"name": "ocramius/proxy-manager", "description": "A library providing utilities to generate, instantiate and generally operate with Object Proxies", "keywords": ["aop", "proxy", "proxy pattern", "service proxies", "lazy loading"], "homepage": "https://github.com/Ocramius/ProxyManager", "version": "2.14.1", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "3990d60ef79001badbab4927a6a811682274a0d1"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/3990d60ef79001badbab4927a6a811682274a0d1", "type": "zip", "shasum": "", "reference": "3990d60ef79001badbab4927a6a811682274a0d1"}, "type": "library", "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.14.1"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:43:14+00:00", "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "require": {"php": "~8.0.0", "composer-runtime-api": "^2.1.0", "laminas/laminas-code": "^4.4.2", "webimpress/safe-writer": "^2.2.0"}, "require-dev": {"ext-phar": "*", "codelicia/xulieta": "^0.1.6", "doctrine/coding-standard": "^9.0.0", "phpbench/phpbench": "^1.0.3", "phpunit/phpunit": "^9.5.6", "roave/infection-static-analysis-plugin": "^1.8", "squizlabs/php_codesniffer": "^3.6.0", "vimeo/psalm": "^4.8.1"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "laminas/laminas-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)", "laminas/laminas-json": "To have the JsonRpc adapter (Remote Object feature)", "laminas/laminas-soap": "To have the Soap adapter (Remote Object feature)"}, "conflict": {"thecodingmachine/safe": "<1.3.3"}}, {"version": "2.14.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "dbcdf2c925c774d3521107174672665a2f0f7255"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/dbcdf2c925c774d3521107174672665a2f0f7255", "type": "zip", "shasum": "", "reference": "dbcdf2c925c774d3521107174672665a2f0f7255"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.14.0"}, "time": "2022-02-28T13:55:36+00:00"}, {"version": "2.13.1", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "e32bc1986fb6fa318ce35e573c654e3ddfb4848d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/e32bc1986fb6fa318ce35e573c654e3ddfb4848d", "type": "zip", "shasum": "", "reference": "e32bc1986fb6fa318ce35e573c654e3ddfb4848d"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.13.1"}, "time": "2022-03-05T18:42:03+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require": {"php": "~7.4.1 || ~8.0.0", "laminas/laminas-code": "^4.3.0", "composer-runtime-api": "^2.1.0", "webimpress/safe-writer": "^2.2.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^9.5.4", "squizlabs/php_codesniffer": "^3.6.0", "slevomat/coding-standard": "^6.3.10", "doctrine/coding-standard": "^8.2.1", "nikic/php-parser": "^4.10.5", "phpbench/phpbench": "^0.17.1 || 1.0.0-alpha2", "infection/infection": "^0.21.5", "vimeo/psalm": "^4.4.1", "codelicia/xulieta": "^0.1.6"}, "conflict": {"zendframework/zend-stdlib": "<3.2.1", "laminas/laminas-stdlib": "<3.2.1", "doctrine/annotations": "<1.6.1", "thecodingmachine/safe": "<1.3.3"}}, {"version": "2.13.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "21e2b4aa7d7661e7641cc6362fc8635ddcfa8464"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/21e2b4aa7d7661e7641cc6362fc8635ddcfa8464", "type": "zip", "shasum": "", "reference": "21e2b4aa7d7661e7641cc6362fc8635ddcfa8464"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.13.0"}, "time": "2021-06-09T10:16:06+00:00"}, {"version": "2.12.1", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "37b219af4c150ff8a30ab0d10d2d68be1148ecc9"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/37b219af4c150ff8a30ab0d10d2d68be1148ecc9", "type": "zip", "shasum": "", "reference": "37b219af4c150ff8a30ab0d10d2d68be1148ecc9"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.12.1"}, "time": "2022-03-05T18:39:36+00:00", "require": {"php": "~7.4.1 || ~8.0.0", "laminas/laminas-code": "^4.3.0", "composer-runtime-api": "^2.0.0", "webimpress/safe-writer": "^2.2.0"}, "conflict": {"composer/composer": "<2.0.14", "zendframework/zend-stdlib": "<3.2.1", "laminas/laminas-stdlib": "<3.2.1", "doctrine/annotations": "<1.6.1", "thecodingmachine/safe": "<1.3.3"}}, {"version": "2.12.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "6046a5730a8d329d7f3d9c5239bc1c806512a854"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/6046a5730a8d329d7f3d9c5239bc1c806512a854", "type": "zip", "shasum": "", "reference": "6046a5730a8d329d7f3d9c5239bc1c806512a854"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.12.0"}, "time": "2021-05-25T11:42:44+00:00"}, {"version": "2.11.2", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "d9c9ecfc01f8f84ba067836c944e28172a214128"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/d9c9ecfc01f8f84ba067836c944e28172a214128", "type": "zip", "shasum": "", "reference": "d9c9ecfc01f8f84ba067836c944e28172a214128"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.11.2"}, "time": "2022-03-05T18:38:36+00:00", "require": {"php": "~7.4.1 || ~8.0.0", "laminas/laminas-code": "^4.0.0", "composer-runtime-api": "^2.0.0", "webimpress/safe-writer": "^2.0.1"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^9.4.1", "squizlabs/php_codesniffer": "^3.5.5", "slevomat/coding-standard": "^6.3.10", "doctrine/coding-standard": "^8.1.0", "nikic/php-parser": "^4.6.0", "phpbench/phpbench": "^0.17.1 || 1.0.0-alpha2", "infection/infection": "^0.20.2", "vimeo/psalm": "^4.3.2", "codelicia/xulieta": "^0.1.5"}, "conflict": {"zendframework/zend-stdlib": "<3.2.1", "laminas/laminas-stdlib": "<3.2.1", "doctrine/annotations": "<1.6.1", "thecodingmachine/safe": "<1.3.3"}}, {"version": "2.11.1", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "96bb91b7b52324080accf1d0137f491ff378ecf1"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/96bb91b7b52324080accf1d0137f491ff378ecf1", "type": "zip", "shasum": "", "reference": "96bb91b7b52324080accf1d0137f491ff378ecf1"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.11.1"}, "time": "2021-01-10T16:12:59+00:00"}, {"version": "2.11.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "0e23c12a51df3f6e225ae26a7bfe3d8dae4658e4"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/0e23c12a51df3f6e225ae26a7bfe3d8dae4658e4", "type": "zip", "shasum": "", "reference": "0e23c12a51df3f6e225ae26a7bfe3d8dae4658e4"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.11.0"}, "time": "2020-12-30T16:40:17+00:00"}, {"version": "2.10.2", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "13b37152a00a1237a45104c270eb750508bcfa87"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/13b37152a00a1237a45104c270eb750508bcfa87", "type": "zip", "shasum": "", "reference": "13b37152a00a1237a45104c270eb750508bcfa87"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.10.2"}, "time": "2022-03-05T18:37:20+00:00", "require": {"php": "~7.4.1", "laminas/laminas-code": "^3.4.1", "composer-runtime-api": "^2.0.0", "webimpress/safe-writer": "^2.0.1"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^9.2.5", "squizlabs/php_codesniffer": "^3.5.5", "slevomat/coding-standard": "^6.3.10", "doctrine/coding-standard": "^8.1.0", "nikic/php-parser": "^4.6.0", "phpbench/phpbench": "^0.17.1", "infection/infection": "^0.16.4", "vimeo/psalm": "^3.12.2", "codelicia/xulieta": "^0.1.2"}, "conflict": {"zendframework/zend-stdlib": "<3.2.1", "laminas/laminas-stdlib": "<3.2.1", "doctrine/annotations": "<1.6.1"}}, {"version": "2.10.1", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "8e85357b800125dea30b7083a6bf1800801ad98b"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/8e85357b800125dea30b7083a6bf1800801ad98b", "type": "zip", "shasum": "", "reference": "8e85357b800125dea30b7083a6bf1800801ad98b"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.10.1"}, "time": "2020-12-30T14:27:27+00:00"}, {"version": "2.10.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "f65ae0f9dcbdd9d6ad3abb721a9e09c3d7d868a4"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/f65ae0f9dcbdd9d6ad3abb721a9e09c3d7d868a4", "type": "zip", "shasum": "", "reference": "f65ae0f9dcbdd9d6ad3abb721a9e09c3d7d868a4"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.10.0"}, "time": "2020-11-12T17:04:46+00:00"}, {"version": "2.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "10e74313255447b592f23fa94b181dfba9c94b6b"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/10e74313255447b592f23fa94b181dfba9c94b6b", "type": "zip", "shasum": "", "reference": "10e74313255447b592f23fa94b181dfba9c94b6b"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.9.2"}, "time": "2022-03-05T18:36:03+00:00"}, {"version": "2.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "7452942d38ae36223b0f8408619181f69799eb5c"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/7452942d38ae36223b0f8408619181f69799eb5c", "type": "zip", "shasum": "", "reference": "7452942d38ae36223b0f8408619181f69799eb5c"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.9.1"}, "time": "2020-08-26T16:19:12+00:00"}, {"version": "2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "9475248b6521f3029b973c4cd9aad31022bfd793"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/9475248b6521f3029b973c4cd9aad31022bfd793", "type": "zip", "shasum": "", "reference": "9475248b6521f3029b973c4cd9aad31022bfd793"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2020-07-13T20:10:37+00:00"}, {"version": "2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "b71118057e5c51d7a63f5ac7f5e5d89e5dc0734c"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/b71118057e5c51d7a63f5ac7f5e5d89e5dc0734c", "type": "zip", "shasum": "", "reference": "b71118057e5c51d7a63f5ac7f5e5d89e5dc0734c"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.8.2"}, "time": "2022-03-05T18:32:59+00:00", "require": {"php": "~7.4.1", "laminas/laminas-code": "^3.4.1", "ocramius/package-versions": "^1.8.0,<1.10.0", "webimpress/safe-writer": "^2.0.1"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^9.1.1", "squizlabs/php_codesniffer": "^3.5.4", "slevomat/coding-standard": "^5.0.4", "doctrine/coding-standard": "^6.0.0", "nikic/php-parser": "^4.4.0", "phpbench/phpbench": "^0.17.0", "infection/infection": "^0.16.2", "vimeo/psalm": "^3.11.1"}}, {"version": "2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "371c8f2d9d1e888ce1f8f2137d9187252b07ee94"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/371c8f2d9d1e888ce1f8f2137d9187252b07ee94", "type": "zip", "shasum": "", "reference": "371c8f2d9d1e888ce1f8f2137d9187252b07ee94"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.8.1"}, "time": "2020-07-13T19:23:57+00:00"}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "ac1dd414fd114cfc0da9930e0ab46063c2f5e62a"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/ac1dd414fd114cfc0da9930e0ab46063c2f5e62a", "type": "zip", "shasum": "", "reference": "ac1dd414fd114cfc0da9930e0ab46063c2f5e62a"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2020-04-13T14:42:16+00:00", "require": {"php": "~7.4.1", "laminas/laminas-code": "^3.4.1", "ocramius/package-versions": "^1.8.0", "webimpress/safe-writer": "^2.0.1"}}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "4097748928b316b68a8c9fdbfb2b7b7b7027ce86"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/4097748928b316b68a8c9fdbfb2b7b7b7027ce86", "type": "zip", "shasum": "", "reference": "4097748928b316b68a8c9fdbfb2b7b7b7027ce86"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.7.2"}, "time": "2022-03-05T18:30:26+00:00", "require": {"php": "7.4.0", "laminas/laminas-code": "^3.4.1", "ocramius/package-versions": "^1.5.1", "webimpress/safe-writer": "^2.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^8.5.2", "squizlabs/php_codesniffer": "^3.5.4", "slevomat/coding-standard": "^5.0.4", "doctrine/coding-standard": "^6.0.0", "nikic/php-parser": "^4.3.0", "phpbench/phpbench": "^0.17.0", "infection/infection": "^0.15.0", "vimeo/psalm": "^3.8.5"}}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "22eadabbaa2e01a2144e23ac27869c231ecfd34e"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/22eadabbaa2e01a2144e23ac27869c231ecfd34e", "type": "zip", "shasum": "", "reference": "22eadabbaa2e01a2144e23ac27869c231ecfd34e"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.7.1"}, "time": "2020-04-07T19:06:30+00:00"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "e77928e8f11ea36b388f87e75c993d05f5da6bf1"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/e77928e8f11ea36b388f87e75c993d05f5da6bf1", "type": "zip", "shasum": "", "reference": "e77928e8f11ea36b388f87e75c993d05f5da6bf1"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2020-02-08T19:22:03+00:00", "require": {"php": "7.4.*", "laminas/laminas-code": "^3.4.1", "ocramius/package-versions": "^1.5.1", "webimpress/safe-writer": "^2.0"}, "funding": "__unset"}, {"version": "2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "4411933a8ecc4741c3e057c9755b9095a6c2484a"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/4411933a8ecc4741c3e057c9755b9095a6c2484a", "type": "zip", "shasum": "", "reference": "4411933a8ecc4741c3e057c9755b9095a6c2484a"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.6.2"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:25:24+00:00", "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^8.5.1", "squizlabs/php_codesniffer": "^3.5.3", "slevomat/coding-standard": "^5.0.4", "doctrine/coding-standard": "^6.0.0", "nikic/php-parser": "^4.3.0", "phpbench/phpbench": "^0.16.10", "infection/infection": "^0.15.0", "symfony/console": "^4.4.2", "vimeo/psalm": "3.7.0", "mikey179/vfsstream": "^1.6.8"}}, {"version": "2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "dec37bfb3c3594440ee4fa263494189344787d22"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/dec37bfb3c3594440ee4fa263494189344787d22", "type": "zip", "shasum": "", "reference": "dec37bfb3c3594440ee4fa263494189344787d22"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.6.1"}, "time": "2020-01-27T09:25:51+00:00", "funding": "__unset"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "406ca12cabfb43e3b11c994bcbeea8e358c65af9"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/406ca12cabfb43e3b11c994bcbeea8e358c65af9", "type": "zip", "shasum": "", "reference": "406ca12cabfb43e3b11c994bcbeea8e358c65af9"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2020-01-07T08:10:36+00:00", "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^8.5.1", "squizlabs/php_codesniffer": "^3.5.3", "slevomat/coding-standard": "^5.0.4", "doctrine/coding-standard": "^6.0.0", "nikic/php-parser": "^4.3.0", "phpbench/phpbench": "^0.16.10", "infection/infection": "^0.15.0", "symfony/console": "^4.4.2", "vimeo/psalm": "3.7.0"}}, {"version": "2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "8d9a9224d43c837ea0ba644463f2f87bea1a700a"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/8d9a9224d43c837ea0ba644463f2f87bea1a700a", "type": "zip", "shasum": "", "reference": "8d9a9224d43c837ea0ba644463f2f87bea1a700a"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.5.3"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:24:11+00:00", "require": {"php": "^7.4.0", "zendframework/zend-code": "^3.3.1", "ocramius/package-versions": "^1.5.1"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^8.3.3", "squizlabs/php_codesniffer": "^3.4.2", "slevomat/coding-standard": "^5.0.4", "doctrine/coding-standard": "^6.0.0", "couscous/couscous": "^1.7.2", "nikic/php-parser": "^4.2.2", "phpbench/phpbench": "^0.16.9", "infection/infection": "^0.13.4", "symfony/console": "^4.3.3", "vimeo/psalm": "3.4.11"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)"}, "conflict": {"zendframework/zend-stdlib": "<3.2.1", "doctrine/annotations": "<1.6.1"}}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "fea82fb0d4a02ef5ffd512afba26a803f3dc638a"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/fea82fb0d4a02ef5ffd512afba26a803f3dc638a", "type": "zip", "shasum": "", "reference": "fea82fb0d4a02ef5ffd512afba26a803f3dc638a"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.5.2"}, "time": "2020-01-07T07:47:30+00:00", "funding": "__unset"}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "b7bac968eef945322d1e28d7e833b6a549a6da27"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/b7bac968eef945322d1e28d7e833b6a549a6da27", "type": "zip", "shasum": "", "reference": "b7bac968eef945322d1e28d7e833b6a549a6da27"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.5.1"}, "time": "2019-12-17T09:22:43+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "8eb0084cef8e4982c937b8dd1bdfd488a80cc64f"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/8eb0084cef8e4982c937b8dd1bdfd488a80cc64f", "type": "zip", "shasum": "", "reference": "8eb0084cef8e4982c937b8dd1bdfd488a80cc64f"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.5.0"}, "time": "2019-08-10T10:43:36+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "6fa3be937e6214db0af58a9ef9b09e0a4b27b612"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/6fa3be937e6214db0af58a9ef9b09e0a4b27b612", "type": "zip", "shasum": "", "reference": "6fa3be937e6214db0af58a9ef9b09e0a4b27b612"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.4.1"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:22:55+00:00", "require": {"php": "^7.4.0", "zendframework/zend-code": "^3.3.1", "ocramius/package-versions": "^1.4.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^8.0.5", "squizlabs/php_codesniffer": "^3.4.0", "slevomat/coding-standard": "^5.0.2", "doctrine/coding-standard": "^6.0.0", "couscous/couscous": "^1.7.0", "nikic/php-parser": "^4.2.1", "phpbench/phpbench": "^0.16.9", "infection/infection": "^0.12.2", "symfony/console": "^4.2.4", "vimeo/psalm": "^3.4.0"}, "conflict": {"zendframework/zend-stdlib": "<3.2.1"}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "7a133e8d6196bc7e17937c6431cc3954dee0886f"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/7a133e8d6196bc7e17937c6431cc3954dee0886f", "type": "zip", "shasum": "", "reference": "7a133e8d6196bc7e17937c6431cc3954dee0886f"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2019-06-03T16:41:32+00:00", "funding": "__unset"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "8f8c8e5d1787af4fe794687f19e015e93657c7e2"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/8f8c8e5d1787af4fe794687f19e015e93657c7e2", "type": "zip", "shasum": "", "reference": "8f8c8e5d1787af4fe794687f19e015e93657c7e2"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.3.2"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:21:58+00:00", "require": {"php": "^7.4.0", "zendframework/zend-code": "^3.3.1", "ocramius/package-versions": "^1.3.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^8.0.0", "squizlabs/php_codesniffer": "^3.4.0", "slevomat/coding-standard": "^4.8.7", "doctrine/coding-standard": "^5.0.1", "couscous/couscous": "^1.7.0", "nikic/php-parser": "^4.2.1", "phpbench/phpbench": "^0.15", "phpstan/phpstan": "^0.11.2", "phpstan/phpstan-phpunit": "^0.11", "infection/infection": "^0.12.2", "symfony/console": "^4.2.3"}}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "3be07227a0a67232a44852f6ebbb1745bc4d3bb8"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/3be07227a0a67232a44852f6ebbb1745bc4d3bb8", "type": "zip", "shasum": "", "reference": "3be07227a0a67232a44852f6ebbb1745bc4d3bb8"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2019-03-19T09:57:01+00:00", "funding": "__unset"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "10e0eb0d78f485f349243044fcc8a8ddc52801c7"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/10e0eb0d78f485f349243044fcc8a8ddc52801c7", "type": "zip", "shasum": "", "reference": "10e0eb0d78f485f349243044fcc8a8ddc52801c7"}, "time": "2019-03-16T20:18:37+00:00"}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "2d7cd2a79cd3ade90c46211baae1b88d47683917"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/2d7cd2a79cd3ade90c46211baae1b88d47683917", "type": "zip", "shasum": "", "reference": "2d7cd2a79cd3ade90c46211baae1b88d47683917"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.2.4"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-05T18:15:28+00:00", "autoload": {"psr-0": {"ProxyManager\\": "src"}}, "require": {"php": "^7.2.0", "zendframework/zend-code": "^3.3.0", "ocramius/package-versions": "^1.1.3"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^6.4.3", "squizlabs/php_codesniffer": "^2.9.1", "couscous/couscous": "^1.6.1", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "dev-master#856eb10a81c1d27c701a83f167dc870fd8f4236a as 0.9.999", "phpstan/phpstan-phpunit": "dev-master#5629c0a1f4a9c417cb1077cf6693ad9753895761", "nikic/php-parser": "^3.1.1", "humbug/humbug": "1.0.0-RC.0@RC", "padraic/phpunit-accelerator": "dev-master@DEV"}, "conflict": "__unset"}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "4d154742e31c35137d5374c998e8f86b54db2e2f"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/4d154742e31c35137d5374c998e8f86b54db2e2f", "type": "zip", "shasum": "", "reference": "4d154742e31c35137d5374c998e8f86b54db2e2f"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.2.x"}, "time": "2019-08-10T08:37:15+00:00", "funding": "__unset"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "14b137b06b0f911944132df9d51e445a35920ab1"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/14b137b06b0f911944132df9d51e445a35920ab1", "type": "zip", "shasum": "", "reference": "14b137b06b0f911944132df9d51e445a35920ab1"}, "time": "2018-09-27T13:45:01+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "306da837ddf12aa5a85a8ca343587ec822802ac3"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/306da837ddf12aa5a85a8ca343587ec822802ac3", "type": "zip", "shasum": "", "reference": "306da837ddf12aa5a85a8ca343587ec822802ac3"}, "time": "2018-08-26T15:07:25+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "81d53b2878f1d1c40ad27270e64b51798485dfc5"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/81d53b2878f1d1c40ad27270e64b51798485dfc5", "type": "zip", "shasum": "", "reference": "81d53b2878f1d1c40ad27270e64b51798485dfc5"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.2.0"}, "time": "2017-11-16T23:22:31+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "e18ac876b2e4819c76349de8f78ccc8ef1554cd7"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/e18ac876b2e4819c76349de8f78ccc8ef1554cd7", "type": "zip", "shasum": "", "reference": "e18ac876b2e4819c76349de8f78ccc8ef1554cd7"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2017-05-04T11:12:50+00:00", "require": {"php": "^7.1.0", "zendframework/zend-code": "^3.1.0", "ocramius/package-versions": "^1.1.1"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^5.6.4", "phpunit/phpunit-mock-objects": "^3.4.1", "squizlabs/php_codesniffer": "^2.7.0", "couscous/couscous": "^1.5.2", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "^0.6.4", "nikic/php-parser": "^3.0.4", "humbug/humbug": "dev-master@DEV"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "d9e5a00ca2d87b7e0f1bff36b897e02afd7d5435"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/d9e5a00ca2d87b7e0f1bff36b897e02afd7d5435", "type": "zip", "shasum": "", "reference": "d9e5a00ca2d87b7e0f1bff36b897e02afd7d5435"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.1.0"}, "time": "2016-11-30T15:45:00+00:00", "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^5.6.4", "phpunit/phpunit-mock-objects": "^3.4.1", "squizlabs/php_codesniffer": "^2.7.0", "couscous/couscous": "^1.5.2", "phpbench/phpbench": "^0.12.2", "humbug/humbug": "dev-master@DEV"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "a55d08229f4f614bf335759ed0cf63378feeb2e6"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/a55d08229f4f614bf335759ed0cf63378feeb2e6", "type": "zip", "shasum": "", "reference": "a55d08229f4f614bf335759ed0cf63378feeb2e6"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.0.4"}, "time": "2016-11-04T15:53:15+00:00", "require": {"php": "7.0.0 - 7.0.5 || ^7.0.7", "zendframework/zend-code": "3.0.0 - 3.0.2 || ^3.0.4", "ocramius/package-versions": "^1.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^5.4.6", "squizlabs/php_codesniffer": "^2.6.0", "couscous/couscous": "^1.4.0", "phpbench/phpbench": "^0.11.2"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "51c7fdd99dba53808aaab21b34f7a55b302c160c"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/51c7fdd99dba53808aaab21b34f7a55b302c160c", "type": "zip", "shasum": "", "reference": "51c7fdd99dba53808aaab21b34f7a55b302c160c"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.0.x"}, "time": "2016-07-01T12:11:54+00:00", "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^5.3.4", "squizlabs/php_codesniffer": "^2.6.0", "couscous/couscous": "^1.4.0"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "001e730968f17cb36816ad68914994341d16e029"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/001e730968f17cb36816ad68914994341d16e029", "type": "zip", "shasum": "", "reference": "001e730968f17cb36816ad68914994341d16e029"}, "time": "2016-05-27T01:59:56+00:00", "require": {"php": "7.0.0 - 7.0.5 || ^7.0.7", "zendframework/zend-code": "~3.0", "ocramius/package-versions": "^1.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^5.1.3", "squizlabs/php_codesniffer": "^2.5.1", "couscous/couscous": "^1.4.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "6c89b7bd6039d8047b1473e2074cb56baa4bc15d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/6c89b7bd6039d8047b1473e2074cb56baa4bc15d", "type": "zip", "shasum": "", "reference": "6c89b7bd6039d8047b1473e2074cb56baa4bc15d"}, "time": "2016-02-08T13:50:05+00:00", "require": {"php": "~7.0", "zendframework/zend-code": "~3.0", "ocramius/package-versions": "^1.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "40e205f0a371777ebf297593abd881ad11199548"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/40e205f0a371777ebf297593abd881ad11199548", "type": "zip", "shasum": "", "reference": "40e205f0a371777ebf297593abd881ad11199548"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.0.0"}, "time": "2016-01-30T09:38:56+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "^5.1.3", "squizlabs/php_codesniffer": "^2.5.0", "couscous/couscous": "^1.4.0"}}, {"version": "2.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "e10083f9a42f67cc119c820ee349afc5bf23de5a"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/e10083f9a42f67cc119c820ee349afc5bf23de5a", "type": "zip", "shasum": "", "reference": "e10083f9a42f67cc119c820ee349afc5bf23de5a"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2016-01-29T10:18:25+00:00"}, {"version": "2.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "57bf01cd5960073e0f42ef5c325ef1bbede27c7d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/57bf01cd5960073e0f42ef5c325ef1bbede27c7d", "type": "zip", "shasum": "", "reference": "57bf01cd5960073e0f42ef5c325ef1bbede27c7d"}, "time": "2016-01-26T02:49:48+00:00"}, {"version": "2.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "73e1aa6bc7a4b866d45a119cf246a685d1a4cbf0"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/73e1aa6bc7a4b866d45a119cf246a685d1a4cbf0", "type": "zip", "shasum": "", "reference": "73e1aa6bc7a4b866d45a119cf246a685d1a4cbf0"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/2.0.0-BETA1"}, "time": "2016-01-23T02:08:36+00:00", "require": {"php": "~7.0", "zendframework/zend-code": "~3.0"}}, {"version": "1.0.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "57e9272ec0e8deccf09421596e0e2252df440e11"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/57e9272ec0e8deccf09421596e0e2252df440e11", "type": "zip", "shasum": "", "reference": "57e9272ec0e8deccf09421596e0e2252df440e11"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/1.0.x"}, "time": "2015-08-09T04:28:19+00:00", "require": {"php": ">=5.3.3", "zendframework/zend-code": ">2.2.5,<3.0"}, "require-dev": {"ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "1.5.*"}, "suggest": {"zendframework/zend-stdlib": "To use the hydrator proxy", "ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "a80a39fac4fbd771aea7d3871929933a3a1bbf3e"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/a80a39fac4fbd771aea7d3871929933a3a1bbf3e", "type": "zip", "shasum": "", "reference": "a80a39fac4fbd771aea7d3871929933a3a1bbf3e"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/1.0.0"}, "time": "2014-12-12T10:59:05+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}}, {"version": "1.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "9ac714e5988d3ee242fb735e70b872f235a49765"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/9ac714e5988d3ee242fb735e70b872f235a49765", "type": "zip", "shasum": "", "reference": "9ac714e5988d3ee242fb735e70b872f235a49765"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/1.0.0-beta3"}, "time": "2014-11-08T08:44:15+00:00"}, {"version": "1.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "dd6153defd58a4ac2374e9b283f1e5567b2481e4"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/dd6153defd58a4ac2374e9b283f1e5567b2481e4", "type": "zip", "shasum": "", "reference": "dd6153defd58a4ac2374e9b283f1e5567b2481e4"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2014-10-23T21:35:11+00:00"}, {"version": "1.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "63cd826711347732e4f313d3674932955b2c7232"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/63cd826711347732e4f313d3674932955b2c7232", "type": "zip", "shasum": "", "reference": "63cd826711347732e4f313d3674932955b2c7232"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/1.0.0-beta1"}, "time": "2014-10-08T01:56:07+00:00"}, {"version": "0.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "0ac0eb3e8e04c7fa75caaf1a43c5405623abf8f5"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/0ac0eb3e8e04c7fa75caaf1a43c5405623abf8f5", "type": "zip", "shasum": "", "reference": "0ac0eb3e8e04c7fa75caaf1a43c5405623abf8f5"}, "support": {"issues": "https://github.com/Ocramius/ProxyManager/issues", "source": "https://github.com/Ocramius/ProxyManager/tree/master"}, "time": "2014-09-28T14:18:11+00:00", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}}, {"version": "0.5.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/", "role": "Developer"}], "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "eaaf10f9a24ffd79a3e388809f6d61f229e261bd"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/eaaf10f9a24ffd79a3e388809f6d61f229e261bd", "type": "zip", "shasum": "", "reference": "eaaf10f9a24ffd79a3e388809f6d61f229e261bd"}, "time": "2014-03-12T14:00:19+00:00", "require-dev": {"ext-phar": "*", "phpunit/phpunit": "~3.7", "squizlabs/php_codesniffer": "1.5.*"}}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "3b3b7101b1eea62afe039db50faee0d3a9e3e330"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/3b3b7101b1eea62afe039db50faee0d3a9e3e330", "type": "zip", "shasum": "", "reference": "3b3b7101b1eea62afe039db50faee0d3a9e3e330"}, "time": "2013-11-30T15:00:46+00:00", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "require": {"php": ">=5.3.3", "zendframework/zend-code": "2.*"}, "require-dev": {"phpunit/phpunit": ">=3.7", "phpmd/phpmd": "1.4.*", "squizlabs/php_codesniffer": "1.4.*", "satooshi/php-coveralls": "~0.6"}}, {"version": "0.5.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "3308d60a13ebe4b87fbd5be0b0b6fe1a7b9ddb91"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/3308d60a13ebe4b87fbd5be0b0b6fe1a7b9ddb91", "type": "zip", "shasum": "", "reference": "3308d60a13ebe4b87fbd5be0b0b6fe1a7b9ddb91"}, "time": "2013-11-30T01:48:49+00:00"}, {"version": "0.5.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "3f62454d433cd2deed716c09acc91c3e49588d33"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/3f62454d433cd2deed716c09acc91c3e49588d33", "type": "zip", "shasum": "", "reference": "3f62454d433cd2deed716c09acc91c3e49588d33"}, "time": "2013-11-29T00:01:15+00:00"}, {"version": "0.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "9fa5bde511897c639e5843b5dfbdaecefa0d50de"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/9fa5bde511897c639e5843b5dfbdaecefa0d50de", "type": "zip", "shasum": "", "reference": "9fa5bde511897c639e5843b5dfbdaecefa0d50de"}, "time": "2013-10-20T13:21:15+00:00"}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "880ea3f2458d21781bcf3319b6756e69e9b322c4"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/880ea3f2458d21781bcf3319b6756e69e9b322c4", "type": "zip", "shasum": "", "reference": "880ea3f2458d21781bcf3319b6756e69e9b322c4"}, "time": "2013-06-30T16:44:31+00:00", "extra": {"branch-alias": {"dev-master": "0.4.x-dev"}}, "require-dev": {"phpunit/phpunit": ">=3.7", "phpmd/phpmd": "1.4.*", "squizlabs/php_codesniffer": "1.4.*", "zendframework/zend-stdlib": "2.*", "satooshi/php-coveralls": "~0.6"}, "suggest": {"zendframework/zend-stdlib": "To use the hydrator proxy"}}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "442bfd3b66c602acc2f194ab7f9c90dc28eefb17"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/442bfd3b66c602acc2f194ab7f9c90dc28eefb17", "type": "zip", "shasum": "", "reference": "442bfd3b66c602acc2f194ab7f9c90dc28eefb17"}, "time": "2013-06-28T11:09:28+00:00"}, {"version": "0.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "24949099abe5c66e01bf8e1a666dc0a87dc55cd2"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/24949099abe5c66e01bf8e1a666dc0a87dc55cd2", "type": "zip", "shasum": "", "reference": "24949099abe5c66e01bf8e1a666dc0a87dc55cd2"}, "time": "2013-06-04T11:15:50+00:00"}, {"version": "0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "136369568151970ab54044c6ec49b1c53cccd32d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/136369568151970ab54044c6ec49b1c53cccd32d", "type": "zip", "shasum": "", "reference": "136369568151970ab54044c6ec49b1c53cccd32d"}, "time": "2013-05-13T22:59:56+00:00"}, {"version": "0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "86d87be97aa749425a46251944cc7cbe1ed0eb25"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/86d87be97aa749425a46251944cc7cbe1ed0eb25", "type": "zip", "shasum": "", "reference": "86d87be97aa749425a46251944cc7cbe1ed0eb25"}, "time": "2013-05-01T17:42:27+00:00", "require-dev": {"phpunit/phpunit": ">=3.7", "phpmd/phpmd": "1.4.*", "squizlabs/php_codesniffer": "1.4.*", "zendframework/zend-stdlib": "2.*"}}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "cd4e9d7bfcd8cd8ad2ddc337003040c19b6fdf13"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/cd4e9d7bfcd8cd8ad2ddc337003040c19b6fdf13", "type": "zip", "shasum": "", "reference": "cd4e9d7bfcd8cd8ad2ddc337003040c19b6fdf13"}, "time": "2013-04-20T20:37:08+00:00"}, {"keywords": [], "version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "c07890ff997b96fd8d464e8754711cd05c173a02"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/c07890ff997b96fd8d464e8754711cd05c173a02", "type": "zip", "shasum": "", "reference": "c07890ff997b96fd8d464e8754711cd05c173a02"}, "time": "2013-04-19T20:23:17+00:00", "require-dev": {"phpunit/phpunit": ">=3.7", "phpmd/phpmd": "1.4.*", "squizlabs/php_codesniffer": "1.4.*"}, "suggest": "__unset"}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "704262b131fc9e4dc060b416aae9852ed1657cd2"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/704262b131fc9e4dc060b416aae9852ed1657cd2", "type": "zip", "shasum": "", "reference": "704262b131fc9e4dc060b416aae9852ed1657cd2"}, "time": "2013-03-31T04:41:30+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "ea44d1164e09e539bbaca5b5baaaeff5ed8bea51"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/ea44d1164e09e539bbaca5b5baaaeff5ed8bea51", "type": "zip", "shasum": "", "reference": "ea44d1164e09e539bbaca5b5baaaeff5ed8bea51"}, "time": "2013-03-30T10:52:22+00:00", "extra": {"branch-alias": {"dev-master": "0.3.x-dev"}}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "ebd1ca769a12b665b11c04d147464fa88519b0f2"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/ebd1ca769a12b665b11c04d147464fa88519b0f2", "type": "zip", "shasum": "", "reference": "ebd1ca769a12b665b11c04d147464fa88519b0f2"}, "time": "2013-03-27T07:46:55+00:00", "require": {"php": ">=5.3.3", "jms/cg": "dev-master@dev"}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/ProxyManager.git", "type": "git", "reference": "49e24a74f494191cf77ade04dcf474d8ad46fd3c"}, "dist": {"url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/49e24a74f494191cf77ade04dcf474d8ad46fd3c", "type": "zip", "shasum": "", "reference": "49e24a74f494191cf77ade04dcf474d8ad46fd3c"}, "time": "2013-03-23T06:34:46+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 21 Mar 2024 14:21:27 GMT"}