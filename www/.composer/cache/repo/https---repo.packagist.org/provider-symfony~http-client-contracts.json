{"minified": "composer/2.0", "packages": {"symfony/http-client-contracts": [{"name": "symfony/http-client-contracts", "description": "Generic abstractions related to HTTP clients", "keywords": ["interfaces", "standards", "interoperability", "contracts", "abstractions", "decoupling"], "homepage": "https://symfony.com", "version": "v3.6.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "75d7043853a42837e68111812f4d964b01e5101c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/75d7043853a42837e68111812f4d964b01e5101c", "type": "zip", "shasum": "", "reference": "75d7043853a42837e68111812f4d964b01e5101c"}, "type": "library", "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-29T11:18:49+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "require": {"php": ">=8.1"}}, {"version": "v3.6.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0-BETA1"}}, {"version": "v3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ee8d807ab20fcb51267fdace50fbe3494c31e645", "type": "zip", "shasum": "", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.5.2"}, "time": "2024-12-07T08:49:48+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "c2f3ad828596624ca39ea40f83617ef51ca8bbf9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/c2f3ad828596624ca39ea40f83617ef51ca8bbf9", "type": "zip", "shasum": "", "reference": "c2f3ad828596624ca39ea40f83617ef51ca8bbf9"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.5.1"}, "time": "2024-11-25T12:02:18+00:00"}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "20414d96f391677bf80078aa55baece78b82647d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/20414d96f391677bf80078aa55baece78b82647d", "type": "zip", "shasum": "", "reference": "20414d96f391677bf80078aa55baece78b82647d"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.5.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "a9ba2374583cf6cb157aca5e3b1f10ec83e5d814"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/a9ba2374583cf6cb157aca5e3b1f10ec83e5d814", "type": "zip", "shasum": "", "reference": "a9ba2374583cf6cb157aca5e3b1f10ec83e5d814"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.4"}, "time": "2024-12-09T16:20:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.4-dev"}}}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "21dd77dfcefaf8007de8e6354998940989ac3532"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/21dd77dfcefaf8007de8e6354998940989ac3532", "type": "zip", "shasum": "", "reference": "21dd77dfcefaf8007de8e6354998940989ac3532"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.3"}, "time": "2024-11-25T11:59:11+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "b6b5c876b3a4ed74460e2c5ac53bbce2f12e2a7e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/b6b5c876b3a4ed74460e2c5ac53bbce2f12e2a7e", "type": "zip", "shasum": "", "reference": "b6b5c876b3a4ed74460e2c5ac53bbce2f12e2a7e"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.2"}, "time": "2024-04-01T18:51:09+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "1ee70e699b41909c209a0c930f11034b93578654"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/1ee70e699b41909c209a0c930f11034b93578654", "type": "zip", "shasum": "", "reference": "1ee70e699b41909c209a0c930f11034b93578654"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.0"}, "time": "2023-07-30T20:28:31+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "3b66325d0176b4ec826bffab57c9037d759c31fb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/3b66325d0176b4ec826bffab57c9037d759c31fb", "type": "zip", "shasum": "", "reference": "3b66325d0176b4ec826bffab57c9037d759c31fb"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.3.0"}, "time": "2023-05-23T14:45:45+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "df2ecd6cb70e73c1080e6478aea85f5f4da2c48b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/df2ecd6cb70e73c1080e6478aea85f5f4da2c48b", "type": "zip", "shasum": "", "reference": "df2ecd6cb70e73c1080e6478aea85f5f4da2c48b"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.2.1"}, "time": "2023-03-01T10:32:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.3-dev"}}, "suggest": {"symfony/http-client-implementation": ""}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "c5f587eb445224ddfeb05b5ee703476742d730bf"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/c5f587eb445224ddfeb05b5ee703476742d730bf", "type": "zip", "shasum": "", "reference": "c5f587eb445224ddfeb05b5ee703476742d730bf"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.2.0"}, "time": "2022-11-25T10:21:52+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "fd038f08c623ab5d22b26e9ba35afe8c79071800"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/fd038f08c623ab5d22b26e9ba35afe8c79071800", "type": "zip", "shasum": "", "reference": "fd038f08c623ab5d22b26e9ba35afe8c79071800"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.1.1"}, "time": "2022-04-22T07:30:54+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.1-dev"}}}, {"version": "v3.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.1.0"}}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "4184b9b63af1edaf35b6a7974c6f1f9f33294129"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/4184b9b63af1edaf35b6a7974c6f1f9f33294129", "type": "zip", "shasum": "", "reference": "4184b9b63af1edaf35b6a7974c6f1f9f33294129"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.0.2"}, "time": "2022-04-12T16:11:42+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.0.2"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "f7525778c712be78ad5b6ca31f47fdcfd404c280"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/f7525778c712be78ad5b6ca31f47fdcfd404c280", "type": "zip", "shasum": "", "reference": "f7525778c712be78ad5b6ca31f47fdcfd404c280"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.0.1"}, "time": "2022-03-13T20:10:05+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "265f03fed057044a8e4dc159aa33596d0f48ed3f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/265f03fed057044a8e4dc159aa33596d0f48ed3f", "type": "zip", "shasum": "", "reference": "265f03fed057044a8e4dc159aa33596d0f48ed3f"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.0.0"}, "time": "2021-11-03T13:44:55+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "48ef1d0a082885877b664332b9427662065a360c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/48ef1d0a082885877b664332b9427662065a360c", "type": "zip", "shasum": "", "reference": "48ef1d0a082885877b664332b9427662065a360c"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.5"}, "time": "2024-11-28T08:37:04+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "require": {"php": ">=7.2.5"}}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "fbfd73095ae958935396cf2243c47b01c677750c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/fbfd73095ae958935396cf2243c47b01c677750c", "type": "zip", "shasum": "", "reference": "fbfd73095ae958935396cf2243c47b01c677750c"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.4"}, "time": "2024-11-25T09:13:00+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "type": "zip", "shasum": "", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.3"}, "time": "2024-03-26T19:42:53+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70", "type": "zip", "shasum": "", "reference": "ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.2"}, "time": "2022-04-12T15:48:08+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "1a4f708e4e87f335d1b1be6148060739152f0bd5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/1a4f708e4e87f335d1b1be6148060739152f0bd5", "type": "zip", "shasum": "", "reference": "1a4f708e4e87f335d1b1be6148060739152f0bd5"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.1"}, "time": "2022-03-13T20:07:29+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "ec82e57b5b714dbb69300d348bd840b345e24166"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ec82e57b5b714dbb69300d348bd840b345e24166", "type": "zip", "shasum": "", "reference": "ec82e57b5b714dbb69300d348bd840b345e24166"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.0"}, "time": "2021-11-03T09:24:47+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "a0c4f981a04c3b715b2d832dfdbbb5c25a9d455d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/a0c4f981a04c3b715b2d832dfdbbb5c25a9d455d", "type": "zip", "shasum": "", "reference": "a0c4f981a04c3b715b2d832dfdbbb5c25a9d455d"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.4.1"}, "time": "2021-09-07T07:41:40+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.4-dev"}}}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "7e82f6084d7cae521a75ef2cb5c9457bbda785f4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/7e82f6084d7cae521a75ef2cb5c9457bbda785f4", "type": "zip", "shasum": "", "reference": "7e82f6084d7cae521a75ef2cb5c9457bbda785f4"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.4.0"}, "time": "2021-04-11T23:07:08+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "41db680a15018f9c1d4b23516059633ce280ca33"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/41db680a15018f9c1d4b23516059633ce280ca33", "type": "zip", "shasum": "", "reference": "41db680a15018f9c1d4b23516059633ce280ca33"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.3.1"}, "time": "2020-10-14T17:08:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.3-dev"}, "branch-version": "2.3"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "3a5d0fe7908daaa23e3dbf4cee3ba4bfbb19fdd3"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/3a5d0fe7908daaa23e3dbf4cee3ba4bfbb19fdd3", "type": "zip", "shasum": "", "reference": "3a5d0fe7908daaa23e3dbf4cee3ba4bfbb19fdd3"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.2.0"}, "time": "2020-09-07T11:33:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "cd88921e9add61f2064c9c6b30de4f589db42962"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/cd88921e9add61f2064c9c6b30de4f589db42962", "type": "zip", "shasum": "", "reference": "cd88921e9add61f2064c9c6b30de4f589db42962"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.1.3"}, "time": "2020-07-06T13:23:11+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "f8bed25edc964d015bcd87f1fec5734963931910"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/f8bed25edc964d015bcd87f1fec5734963931910", "type": "zip", "shasum": "", "reference": "f8bed25edc964d015bcd87f1fec5734963931910"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/master"}, "time": "2020-05-25T17:37:45+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "e3ba2688594d8ef284f40348f7efb72cba4edec4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e3ba2688594d8ef284f40348f7efb72cba4edec4", "type": "zip", "shasum": "", "reference": "e3ba2688594d8ef284f40348f7efb72cba4edec4"}, "time": "2020-05-09T18:37:03+00:00", "require": {"php": "^7.2.5"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "a734cf7ac927f62e6ee87368f4ecddbe4b93762c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/a734cf7ac927f62e6ee87368f4ecddbe4b93762c", "type": "zip", "shasum": "", "reference": "a734cf7ac927f62e6ee87368f4ecddbe4b93762c"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.1.0"}, "time": "2020-03-15T14:51:35+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "378868b61b85c5cac6822d4f84e26999c9f2e881"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/378868b61b85c5cac6822d4f84e26999c9f2e881", "type": "zip", "shasum": "", "reference": "378868b61b85c5cac6822d4f84e26999c9f2e881"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.0.1"}, "time": "2019-11-26T23:25:11+00:00", "funding": "__unset"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "f1aa62591e44a737d4589a0913ac49b84313c19d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/f1aa62591e44a737d4589a0913ac49b84313c19d", "type": "zip", "shasum": "", "reference": "f1aa62591e44a737d4589a0913ac49b84313c19d"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.0.0"}, "time": "2019-11-09T09:18:34+00:00", "require": {"php": "^7.2.9"}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "59f37624a82635962f04c98f31aed122e539a89e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/59f37624a82635962f04c98f31aed122e539a89e", "type": "zip", "shasum": "", "reference": "59f37624a82635962f04c98f31aed122e539a89e"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-11T14:52:04+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "1.1-dev"}}, "require": {"php": ">=7.1.3"}}, {"version": "v1.1.13", "version_normalized": "********", "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.13"}}, {"version": "v1.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "89e01dc0f8677ee843b7c46f12624cac7fe4292b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/89e01dc0f8677ee843b7c46f12624cac7fe4292b", "type": "zip", "shasum": "", "reference": "89e01dc0f8677ee843b7c46f12624cac7fe4292b"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.12"}, "time": "2022-03-08T18:43:16+00:00"}, {"version": "v1.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "8a497ee1712c2507eaccd31aca00dd603f93d25d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/8a497ee1712c2507eaccd31aca00dd603f93d25d", "type": "zip", "shasum": "", "reference": "8a497ee1712c2507eaccd31aca00dd603f93d25d"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.11"}, "time": "2021-09-06T10:00:00+00:00"}, {"version": "v1.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "7e86f903f9720d0caa7688f5c29a2de2d77cbb89"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/7e86f903f9720d0caa7688f5c29a2de2d77cbb89", "type": "zip", "shasum": "", "reference": "7e86f903f9720d0caa7688f5c29a2de2d77cbb89"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.10"}, "time": "2020-08-17T09:35:39+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "3281d24779c56fecc265a93126f1a02feced96eb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/3281d24779c56fecc265a93126f1a02feced96eb", "type": "zip", "shasum": "", "reference": "3281d24779c56fecc265a93126f1a02feced96eb"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.9"}, "time": "2020-07-06T13:19:58+00:00"}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "088bae75cfa2ec5eb6d33dce17dbd8613150ce6e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/088bae75cfa2ec5eb6d33dce17dbd8613150ce6e", "type": "zip", "shasum": "", "reference": "088bae75cfa2ec5eb6d33dce17dbd8613150ce6e"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.8"}, "time": "2019-11-07T12:44:51+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": "^7.1.3"}, "funding": "__unset"}, {"version": "v1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "353b2a3e907e5c34cf8f74827a4b21eb745aab1d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/353b2a3e907e5c34cf8f74827a4b21eb745aab1d", "type": "zip", "shasum": "", "reference": "353b2a3e907e5c34cf8f74827a4b21eb745aab1d"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.7"}, "time": "2019-09-26T22:09:58+00:00"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "6005fe61a33724405d56eb5b055d5d370192a1bd"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/6005fe61a33724405d56eb5b055d5d370192a1bd", "type": "zip", "shasum": "", "reference": "6005fe61a33724405d56eb5b055d5d370192a1bd"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/master"}, "time": "2019-08-08T10:05:21+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "e1924aea9c70ae3e69fff05afa3cb8ce541bf3bb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e1924aea9c70ae3e69fff05afa3cb8ce541bf3bb", "type": "zip", "shasum": "", "reference": "e1924aea9c70ae3e69fff05afa3cb8ce541bf3bb"}, "time": "2019-06-17T17:43:54+00:00"}, {"version": "v1.1.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.4"}}, {"version": "v1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "05201f91d4f03c58f7324d019bdd791c8e8e418b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/05201f91d4f03c58f7324d019bdd791c8e8e418b", "type": "zip", "shasum": "", "reference": "05201f91d4f03c58f7324d019bdd791c8e8e418b"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.3"}, "time": "2019-06-05T13:28:50+00:00"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client-contracts.git", "type": "git", "reference": "c87486c0fa65ae04736140389037fcbdd149ddd6"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/c87486c0fa65ae04736140389037fcbdd149ddd6", "type": "zip", "shasum": "", "reference": "c87486c0fa65ae04736140389037fcbdd149ddd6"}, "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2019-05-22T12:23:29+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v1.1.1"}, "funding": "__unset"}]}, "security-advisories": [], "last-modified": "Sun, 25 May 2025 20:28:20 GMT"}