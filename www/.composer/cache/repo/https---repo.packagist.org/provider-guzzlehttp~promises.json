{"minified": "composer/2.0", "packages": {"guzzlehttp/promises": [{"name": "guzzlehttp/promises", "description": "Guzzle promises library", "keywords": ["promise"], "homepage": "", "version": "2.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "type": "zip", "shasum": "", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "type": "library", "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "2bbd39386fb5863ee5ce56883e3cca391bf5a672"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/2bbd39386fb5863ee5ce56883e3cca391bf5a672", "type": "zip", "shasum": "", "reference": "2bbd39386fb5863ee5ce56883e3cca391bf5a672"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.1.0"}, "time": "2025-03-27T12:09:23+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "type": "zip", "shasum": "", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "time": "2024-10-17T10:06:22+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "type": "zip", "shasum": "", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.3"}, "time": "2024-07-18T10:29:17+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "type": "zip", "shasum": "", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "time": "2023-12-03T20:19:20+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/111166291a0f8130081195ac4556a5587d7f1b5d", "type": "zip", "shasum": "", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.1"}, "time": "2023-08-03T15:11:55+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "3a494dc7dc1d7d12e511890177ae2d0e6c107da6"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/3a494dc7dc1d7d12e511890177ae2d0e6c107da6", "type": "zip", "shasum": "", "reference": "3a494dc7dc1d7d12e511890177ae2d0e6c107da6"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.0"}, "time": "2023-05-21T13:50:22+00:00"}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "type": "zip", "shasum": "", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "time": "2023-05-21T12:31:43+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "extra": "__unset"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/b94b2807d85443f9719887892882d0329d1e2598", "type": "zip", "shasum": "", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.2"}, "time": "2022-08-28T14:55:35+00:00", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "type": "zip", "shasum": "", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "time": "2021-10-22T20:56:57+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "136a635e2b4a49b9d79e9c8fee267ffb257fdba0"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/136a635e2b4a49b9d79e9c8fee267ffb257fdba0", "type": "zip", "shasum": "", "reference": "136a635e2b4a49b9d79e9c8fee267ffb257fdba0"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.0"}, "time": "2021-10-07T13:05:22+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/8e7d04f1f6450fef59366c399cfad4b9383aa30d", "type": "zip", "shasum": "", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.4.1"}, "funding": [], "time": "2021-03-07T09:25:29+00:00", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "60d379c243457e073cff02bc323a2a86cb355631"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/60d379c243457e073cff02bc323a2a86cb355631", "type": "zip", "shasum": "", "reference": "60d379c243457e073cff02bc323a2a86cb355631"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.4.0"}, "time": "2020-09-30T07:37:28+00:00"}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "type": "zip", "shasum": "", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/master"}, "time": "2016-12-20T10:07:11+00:00", "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "funding": "__unset"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "2693c101803ca78b27972d84081d027fca790a1e"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/2693c101803ca78b27972d84081d027fca790a1e", "type": "zip", "shasum": "", "reference": "2693c101803ca78b27972d84081d027fca790a1e"}, "time": "2016-11-18T17:47:58+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "c10d860e2a9595f8883527fa0021c7da9e65f579"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/c10d860e2a9595f8883527fa0021c7da9e65f579", "type": "zip", "shasum": "", "reference": "c10d860e2a9595f8883527fa0021c7da9e65f579"}, "time": "2016-05-18T16:56:05+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "bb9024c526b22f3fe6ae55a561fd70653d470aa8"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/bb9024c526b22f3fe6ae55a561fd70653d470aa8", "type": "zip", "shasum": "", "reference": "bb9024c526b22f3fe6ae55a561fd70653d470aa8"}, "time": "2016-03-08T01:15:46+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "b1e1c0d55f8083c71eda2c28c12a228d708294ea"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/b1e1c0d55f8083c71eda2c28c12a228d708294ea", "type": "zip", "shasum": "", "reference": "b1e1c0d55f8083c71eda2c28c12a228d708294ea"}, "time": "2015-10-15T22:28:00+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "97fe7210def29451ec74923b27e552238defd75a"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/97fe7210def29451ec74923b27e552238defd75a", "type": "zip", "shasum": "", "reference": "97fe7210def29451ec74923b27e552238defd75a"}, "time": "2015-08-15T19:37:21+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "2ee5bc7f1a92efecc90da7f6711a53a7be26b5b7"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/2ee5bc7f1a92efecc90da7f6711a53a7be26b5b7", "type": "zip", "shasum": "", "reference": "2ee5bc7f1a92efecc90da7f6711a53a7be26b5b7"}, "time": "2015-06-24T16:16:25+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "01abc3232138f330d8a1eaa808fcbdf9b4292f47"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/01abc3232138f330d8a1eaa808fcbdf9b4292f47", "type": "zip", "shasum": "", "reference": "01abc3232138f330d8a1eaa808fcbdf9b4292f47"}, "time": "2015-05-13T05:05:10+00:00", "require-dev": {"phpunit/phpunit": "^4.0"}}, {"version": "0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "84d1ab08f78da16bba59e043753762e06d2f0763"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/84d1ab08f78da16bba59e043753762e06d2f0763", "type": "zip", "shasum": "", "reference": "84d1ab08f78da16bba59e043753762e06d2f0763"}, "time": "2015-05-10T02:19:10+00:00", "extra": {"branch-alias": {"dev-master": "0.1-dev"}}}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "89e850a66126a06a1088b9d47bc1d5761461dafe"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/89e850a66126a06a1088b9d47bc1d5761461dafe", "type": "zip", "shasum": "", "reference": "89e850a66126a06a1088b9d47bc1d5761461dafe"}, "time": "2015-04-21T23:29:41+00:00", "require": {"php": ">=5.4.0"}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/promises.git", "type": "git", "reference": "2421bef1daa35ffeab75cac1a636309ca841718d"}, "dist": {"url": "https://api.github.com/repos/guzzle/promises/zipball/2421bef1daa35ffeab75cac1a636309ca841718d", "type": "zip", "shasum": "", "reference": "2421bef1daa35ffeab75cac1a636309ca841718d"}, "time": "2015-04-18T22:12:21+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 27 Mar 2025 18:51:24 GMT"}