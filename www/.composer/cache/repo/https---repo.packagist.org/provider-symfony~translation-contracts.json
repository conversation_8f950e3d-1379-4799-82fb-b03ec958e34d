{"minified": "composer/2.0", "packages": {"symfony/translation-contracts": [{"name": "symfony/translation-contracts", "description": "Generic abstractions related to translation", "keywords": ["interfaces", "standards", "interoperability", "contracts", "abstractions", "decoupling"], "homepage": "https://symfony.com", "version": "v3.6.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "type": "zip", "shasum": "", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "type": "library", "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "require": {"php": ">=8.1"}}, {"version": "v3.6.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0-BETA1"}}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "type": "zip", "shasum": "", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "b9d2189887bb6b2e0367a9fc7136c5239ab9b05a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b9d2189887bb6b2e0367a9fc7136c5239ab9b05a", "type": "zip", "shasum": "", "reference": "b9d2189887bb6b2e0367a9fc7136c5239ab9b05a"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "6e864bb54a5a94c490e2e0ae0822f16592c06740"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/6e864bb54a5a94c490e2e0ae0822f16592c06740", "type": "zip", "shasum": "", "reference": "6e864bb54a5a94c490e2e0ae0822f16592c06740"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.3"}, "time": "2024-09-25T14:18:03+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.4-dev"}}}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "43810bdb2ddb5400e5c5e778e27b210a0ca83b6b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/43810bdb2ddb5400e5c5e778e27b210a0ca83b6b", "type": "zip", "shasum": "", "reference": "43810bdb2ddb5400e5c5e778e27b210a0ca83b6b"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.2"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "06450585bf65e978026bda220cdebca3f867fde7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/06450585bf65e978026bda220cdebca3f867fde7", "type": "zip", "shasum": "", "reference": "06450585bf65e978026bda220cdebca3f867fde7"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.1"}, "time": "2023-12-26T14:02:43+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "dee0c6e5b4c07ce851b462530088e64b255ac9c5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/dee0c6e5b4c07ce851b462530088e64b255ac9c5", "type": "zip", "shasum": "", "reference": "dee0c6e5b4c07ce851b462530088e64b255ac9c5"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.0"}, "time": "2023-07-25T15:08:44+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "02c24deb352fb0d79db5486c0c79905a85e37e86"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/02c24deb352fb0d79db5486c0c79905a85e37e86", "type": "zip", "shasum": "", "reference": "02c24deb352fb0d79db5486c0c79905a85e37e86"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.3.0"}, "time": "2023-05-30T17:17:10+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "dfec258b9dd17a6b24420d464c43bffe347441c8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/dfec258b9dd17a6b24420d464c43bffe347441c8", "type": "zip", "shasum": "", "reference": "dfec258b9dd17a6b24420d464c43bffe347441c8"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.2.1"}, "time": "2023-03-01T10:32:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.3-dev"}}, "suggest": {"symfony/translation-implementation": ""}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "68cce71402305a015f8c1589bfada1280dc64fe7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/68cce71402305a015f8c1589bfada1280dc64fe7", "type": "zip", "shasum": "", "reference": "68cce71402305a015f8c1589bfada1280dc64fe7"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.2.0"}, "time": "2022-11-25T10:21:52+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "606be0f48e05116baef052f7f3abdb345c8e02cc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/606be0f48e05116baef052f7f3abdb345c8e02cc", "type": "zip", "shasum": "", "reference": "606be0f48e05116baef052f7f3abdb345c8e02cc"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.1.1"}, "time": "2022-06-27T17:24:16+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.1-dev"}}}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "bfddd2a1faa271b782b791c361cc16e2dd49dfaa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/bfddd2a1faa271b782b791c361cc16e2dd49dfaa", "type": "zip", "shasum": "", "reference": "bfddd2a1faa271b782b791c361cc16e2dd49dfaa"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.1.0"}, "time": "2022-04-22T07:30:54+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/acbfbb274e730e5a0236f619b6168d9dedb3e282", "type": "zip", "shasum": "", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.0.2"}, "time": "2022-06-27T17:10:44+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.0.2"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "c4183fc3ef0f0510893cbeedc7718fb5cafc9ac9"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/c4183fc3ef0f0510893cbeedc7718fb5cafc9ac9", "type": "zip", "shasum": "", "reference": "c4183fc3ef0f0510893cbeedc7718fb5cafc9ac9"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.0.1"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "1b6ea5a7442af5a12dba3dbd6d71034b5b234e77"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/1b6ea5a7442af5a12dba3dbd6d71034b5b234e77", "type": "zip", "shasum": "", "reference": "1b6ea5a7442af5a12dba3dbd6d71034b5b234e77"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.0.0"}, "time": "2021-09-07T12:43:40+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/450d4172653f38818657022252f9d81be89ee9a8", "type": "zip", "shasum": "", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.4"}, "time": "2024-09-25T14:11:13+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "require": {"php": ">=7.2.5"}}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b0073a77ac0b7ea55131020e87b1e3af540f4664", "type": "zip", "shasum": "", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.3"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "type": "zip", "shasum": "", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.2"}, "time": "2022-06-27T16:58:25+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "1211df0afa701e45a04253110e959d4af4ef0f07"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/1211df0afa701e45a04253110e959d4af4ef0f07", "type": "zip", "shasum": "", "reference": "1211df0afa701e45a04253110e959d4af4ef0f07"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.1"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "d28150f0f44ce854e942b671fc2620a98aae1b1e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/d28150f0f44ce854e942b671fc2620a98aae1b1e", "type": "zip", "shasum": "", "reference": "d28150f0f44ce854e942b671fc2620a98aae1b1e"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.0"}, "time": "2021-08-17T14:20:01+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "6ccdf9ca8d74bd590dd4953a38b5b04234139295"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/6ccdf9ca8d74bd590dd4953a38b5b04234139295", "type": "zip", "shasum": "", "reference": "6ccdf9ca8d74bd590dd4953a38b5b04234139295"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.4.1"}, "time": "2021-07-13T10:04:56+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.4-dev"}}}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "95c812666f3e91db75385749fe219c5e494c7f95"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/95c812666f3e91db75385749fe219c5e494c7f95", "type": "zip", "shasum": "", "reference": "95c812666f3e91db75385749fe219c5e494c7f95"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.4.0"}, "time": "2021-03-23T23:28:01+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e2eaa60b558f26a4b0354e1bbb25636efaaad105", "type": "zip", "shasum": "", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.3.0"}, "time": "2020-09-28T13:05:58+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "77ce1c3627c9f39643acd9af086631f842c50c4d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/77ce1c3627c9f39643acd9af086631f842c50c4d", "type": "zip", "shasum": "", "reference": "77ce1c3627c9f39643acd9af086631f842c50c4d"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/master"}, "time": "2020-09-07T11:33:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "616a9773c853097607cf9dd6577d5b143ffdcd63"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/616a9773c853097607cf9dd6577d5b143ffdcd63", "type": "zip", "shasum": "", "reference": "616a9773c853097607cf9dd6577d5b143ffdcd63"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.1.3"}, "time": "2020-07-06T13:23:11+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "e5ca07c8f817f865f618aa072c2fe8e0e637340e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e5ca07c8f817f865f618aa072c2fe8e0e637340e", "type": "zip", "shasum": "", "reference": "e5ca07c8f817f865f618aa072c2fe8e0e637340e"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.1.2"}, "time": "2020-05-20T17:43:50+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "cfa7f3f5bda0cffdb415d41dd86aa980a3837218"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/cfa7f3f5bda0cffdb415d41dd86aa980a3837218", "type": "zip", "shasum": "", "reference": "cfa7f3f5bda0cffdb415d41dd86aa980a3837218"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.1.1"}, "time": "2020-05-08T21:41:03+00:00", "require": {"php": "^7.2.5"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "fa3238c55d9d4d8f78b4bc5f17374b017e07dea7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/fa3238c55d9d4d8f78b4bc5f17374b017e07dea7", "type": "zip", "shasum": "", "reference": "fa3238c55d9d4d8f78b4bc5f17374b017e07dea7"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.1.0"}, "time": "2020-02-14T07:31:56+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "8cc682ac458d75557203b2f2f14b0b92e1c744ed"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/8cc682ac458d75557203b2f2f14b0b92e1c744ed", "type": "zip", "shasum": "", "reference": "8cc682ac458d75557203b2f2f14b0b92e1c744ed"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.0.1"}, "time": "2019-11-18T17:27:11+00:00", "funding": "__unset"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "8feb81e6bb1a42d6a3b1429c751d291eb6d05297"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/8feb81e6bb1a42d6a3b1429c751d291eb6d05297", "type": "zip", "shasum": "", "reference": "8feb81e6bb1a42d6a3b1429c751d291eb6d05297"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.0.0"}, "time": "2019-11-09T09:18:34+00:00", "require": {"php": "^7.2.9"}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "7462e5c4cb8b9cd152f992e8f10963b5641921f6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/7462e5c4cb8b9cd152f992e8f10963b5641921f6", "type": "zip", "shasum": "", "reference": "7462e5c4cb8b9cd152f992e8f10963b5641921f6"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "1.1-dev"}}, "require": {"php": ">=7.1.3"}}, {"version": "v1.1.13", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.13"}}, {"version": "v1.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "c04dc8a7873a2a9196f038e99342df46b6661a29"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/c04dc8a7873a2a9196f038e99342df46b6661a29", "type": "zip", "shasum": "", "reference": "c04dc8a7873a2a9196f038e99342df46b6661a29"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.12"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v1.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "58ae23095ffdea045725dda70752566aa2908be6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/58ae23095ffdea045725dda70752566aa2908be6", "type": "zip", "shasum": "", "reference": "58ae23095ffdea045725dda70752566aa2908be6"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.11"}, "time": "2021-07-13T10:01:39+00:00"}, {"version": "v1.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "84180a25fad31e23bebd26ca09d89464f082cacc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/84180a25fad31e23bebd26ca09d89464f082cacc", "type": "zip", "shasum": "", "reference": "84180a25fad31e23bebd26ca09d89464f082cacc"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.10"}, "time": "2020-09-02T16:08:58+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "a5db6f7707fd35d137b1398734f2d745c8616ea2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/a5db6f7707fd35d137b1398734f2d745c8616ea2", "type": "zip", "shasum": "", "reference": "a5db6f7707fd35d137b1398734f2d745c8616ea2"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.9"}, "time": "2020-07-06T13:19:58+00:00"}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "364518c132c95642e530d9b2d217acbc2ccac3e6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/364518c132c95642e530d9b2d217acbc2ccac3e6", "type": "zip", "shasum": "", "reference": "364518c132c95642e530d9b2d217acbc2ccac3e6"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.8"}, "time": "2019-09-17T11:12:18+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": "^7.1.3"}}, {"version": "v1.1.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.7"}, "funding": "__unset"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "325b17c24f3ee23cbecfa63ba809c6d89b5fa04a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/325b17c24f3ee23cbecfa63ba809c6d89b5fa04a", "type": "zip", "shasum": "", "reference": "325b17c24f3ee23cbecfa63ba809c6d89b5fa04a"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/master"}, "time": "2019-08-02T12:15:04+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "cb4b18ad7b92a26e83b65dde940fab78339e6f3c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/cb4b18ad7b92a26e83b65dde940fab78339e6f3c", "type": "zip", "shasum": "", "reference": "cb4b18ad7b92a26e83b65dde940fab78339e6f3c"}, "time": "2019-06-13T11:15:36+00:00"}, {"version": "v1.1.4", "version_normalized": "*******"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation-contracts.git", "type": "git", "reference": "93597ce975d91c52ebfaca1253343cd9ccb7916d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation-contracts/zipball/93597ce975d91c52ebfaca1253343cd9ccb7916d", "type": "zip", "shasum": "", "reference": "93597ce975d91c52ebfaca1253343cd9ccb7916d"}, "support": {"source": "https://github.com/symfony/translation-contracts/tree/v1.1.2"}, "time": "2019-05-27T08:16:38+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}]}]}, "security-advisories": [], "last-modified": "Sun, 25 May 2025 20:28:21 GMT"}