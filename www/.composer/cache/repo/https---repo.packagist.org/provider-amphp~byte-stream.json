{"minified": "composer/2.0", "packages": {"amphp/byte-stream": [{"name": "amphp/byte-stream", "description": "A stream abstraction to make working with non-blocking I/O simple.", "keywords": ["stream", "io", "async", "non-blocking", "amp", "amphp"], "homepage": "https://amphp.org/byte-stream", "version": "v2.1.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "55a6bd071aec26fa2a3e002618c20c35e3df1b46"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/55a6bd071aec26fa2a3e002618c20c35e3df1b46", "type": "zip", "shasum": "", "reference": "55a6bd071aec26fa2a3e002618c20c35e3df1b46"}, "type": "library", "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.1.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-03-16T17:10:27+00:00", "autoload": {"files": ["src/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\ByteStream\\": "src"}}, "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/parser": "^1.1", "amphp/serialization": "^1", "amphp/sync": "^2", "revolt/event-loop": "^1 || ^0.2.3"}, "require-dev": {"amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "5.22.1"}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "daa00f2efdbd71565bf64ffefa89e37542addf93"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/daa00f2efdbd71565bf64ffefa89e37542addf93", "type": "zip", "shasum": "", "reference": "daa00f2efdbd71565bf64ffefa89e37542addf93"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.1.1"}, "time": "2024-02-17T04:49:38+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "0a4b0e80dad92c75e6131f8ad253919211540338"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/0a4b0e80dad92c75e6131f8ad253919211540338", "type": "zip", "shasum": "", "reference": "0a4b0e80dad92c75e6131f8ad253919211540338"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.1.0"}, "time": "2023-11-19T14:34:16+00:00", "require-dev": {"amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.4"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "408a3b4fc4f4c7604575dc8704f18c1bd91c3ceb"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/408a3b4fc4f4c7604575dc8704f18c1bd91c3ceb", "type": "zip", "shasum": "", "reference": "408a3b4fc4f4c7604575dc8704f18c1bd91c3ceb"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.2"}, "time": "2023-09-01T04:41:26+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "7e7a77579f3e90c6fbd56e49628e6ace02d8f88a"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/7e7a77579f3e90c6fbd56e49628e6ace02d8f88a", "type": "zip", "shasum": "", "reference": "7e7a77579f3e90c6fbd56e49628e6ace02d8f88a"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.1"}, "time": "2023-02-03T04:06:20+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\ByteStream\\": "src"}}}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "c102b620c14f44e83436b6f9bced6f40ca482bff"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/c102b620c14f44e83436b6f9bced6f40ca482bff", "type": "zip", "shasum": "", "reference": "c102b620c14f44e83436b6f9bced6f40ca482bff"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0"}, "time": "2023-01-07T16:05:54+00:00"}, {"version": "v2.0.0-beta.14", "version_normalized": "*******-beta14", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "970327a54d6132a2f671c3987fed23370bfbecb4"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/970327a54d6132a2f671c3987fed23370bfbecb4", "type": "zip", "shasum": "", "reference": "970327a54d6132a2f671c3987fed23370bfbecb4"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.14"}, "time": "2022-12-23T23:07:07+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/parser": "^1", "amphp/serialization": "^1", "amphp/sync": "^2", "revolt/event-loop": "^1 || ^0.2.3"}, "require-dev": {"amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "amphp/php-cs-fixer-config": "^2-dev", "psalm/phar": "^4.27", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.0.0-beta.13", "version_normalized": "*******-beta13", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "12a5d46d2f4ed55321cbe0d53e471107d2ddc046"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/12a5d46d2f4ed55321cbe0d53e471107d2ddc046", "type": "zip", "shasum": "", "reference": "12a5d46d2f4ed55321cbe0d53e471107d2ddc046"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.13"}, "time": "2022-11-16T16:46:11+00:00", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}}, {"version": "v2.0.0-beta.12", "version_normalized": "*******-beta12", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "a4aa2af367f655188b3924a403ee9748098f23d0"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/a4aa2af367f655188b3924a403ee9748098f23d0", "type": "zip", "shasum": "", "reference": "a4aa2af367f655188b3924a403ee9748098f23d0"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.12"}, "time": "2022-11-07T21:02:34+00:00"}, {"version": "v2.0.0-beta.11", "version_normalized": "*******-beta11", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "198ea3dae45de95880aa0b0fb9ec2dcc3a03c42b"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/198ea3dae45de95880aa0b0fb9ec2dcc3a03c42b", "type": "zip", "shasum": "", "reference": "198ea3dae45de95880aa0b0fb9ec2dcc3a03c42b"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.11"}, "time": "2022-09-11T20:16:57+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/parser": "^1", "amphp/serialization": "^1", "amphp/sync": "^2", "revolt/event-loop": "^0.2.3"}}, {"version": "v2.0.0-beta.10", "version_normalized": "*******-beta10", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "0c2b2ab89517fa0fcc50f91400cc66275af03301"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/0c2b2ab89517fa0fcc50f91400cc66275af03301", "type": "zip", "shasum": "", "reference": "0c2b2ab89517fa0fcc50f91400cc66275af03301"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.10"}, "time": "2022-06-25T17:15:00+00:00", "require-dev": {"amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "amphp/php-cs-fixer-config": "^2-dev", "psalm/phar": "^4.15", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.0.0-beta.9", "version_normalized": "*******-beta9", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "8dd48cc4352173e44ee4e1f3fe8e4d1df21f1305"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/8dd48cc4352173e44ee4e1f3fe8e4d1df21f1305", "type": "zip", "shasum": "", "reference": "8dd48cc4352173e44ee4e1f3fe8e4d1df21f1305"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.9"}, "time": "2022-06-05T16:03:22+00:00"}, {"version": "v2.0.0-beta.8", "version_normalized": "*******-beta8", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "00adeed1340e79ce5851c4b0d275afb2004854a1"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/00adeed1340e79ce5851c4b0d275afb2004854a1", "type": "zip", "shasum": "", "reference": "00adeed1340e79ce5851c4b0d275afb2004854a1"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.8"}, "time": "2022-04-03T17:06:46+00:00"}, {"version": "v2.0.0-beta.7", "version_normalized": "*******-beta7", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "5dfd811650cc14b7e3331319c0dff7ab73673be4"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/5dfd811650cc14b7e3331319c0dff7ab73673be4", "type": "zip", "shasum": "", "reference": "5dfd811650cc14b7e3331319c0dff7ab73673be4"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.7"}, "time": "2022-03-26T15:12:48+00:00"}, {"version": "v2.0.0-beta.6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "c4fef68f475b1503ade4ec6b6778c7ad3b3b44d6"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/c4fef68f475b1503ade4ec6b6778c7ad3b3b44d6", "type": "zip", "shasum": "", "reference": "c4fef68f475b1503ade4ec6b6778c7ad3b3b44d6"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.6"}, "time": "2022-03-05T15:42:14+00:00"}, {"version": "v2.0.0-beta.5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "a63cf29bf682629f190b59b1fedd156861f15070"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/a63cf29bf682629f190b59b1fedd156861f15070", "type": "zip", "shasum": "", "reference": "a63cf29bf682629f190b59b1fedd156861f15070"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.5"}, "time": "2022-02-07T04:53:41+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/parser": "^1", "amphp/serialization": "^1", "amphp/sync": "^2", "revolt/event-loop": "^0.2.0"}}, {"version": "v2.0.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "0f34e7fb66ec66e5a41f490e489bb967882e27b6"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/0f34e7fb66ec66e5a41f490e489bb967882e27b6", "type": "zip", "shasum": "", "reference": "0f34e7fb66ec66e5a41f490e489bb967882e27b6"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.4"}, "time": "2022-02-01T20:18:39+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/pipeline": "^1", "revolt/event-loop": "^0.2.0"}}, {"version": "v2.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "537d55c8731b5c055b719ac6df236cc11911dba1"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/537d55c8731b5c055b719ac6df236cc11911dba1", "type": "zip", "shasum": "", "reference": "537d55c8731b5c055b719ac6df236cc11911dba1"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.3"}, "time": "2021-12-18T17:17:25+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/pipeline": "^1", "revolt/event-loop": "^0.1.0"}, "require-dev": {"amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "friendsofphp/php-cs-fixer": "^2.19", "amphp/php-cs-fixer-config": "dev-master", "psalm/phar": "^4.15", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "80f6bbf6faedc88cbd39883c909334f6ebaf2d7d"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/80f6bbf6faedc88cbd39883c909334f6ebaf2d7d", "type": "zip", "shasum": "", "reference": "80f6bbf6faedc88cbd39883c909334f6ebaf2d7d"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.2"}, "time": "2021-12-17T21:44:52+00:00"}, {"version": "v2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "2c3f3383bc7587ae1997ad3da40486e83351ff0d"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/2c3f3383bc7587ae1997ad3da40486e83351ff0d", "type": "zip", "shasum": "", "reference": "2c3f3383bc7587ae1997ad3da40486e83351ff0d"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.0.0-beta.1"}, "time": "2021-12-11T00:03:40+00:00"}, {"version": "v1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/4f0e968ba3798a423730f567b1b50d3441c16ddc", "type": "zip", "shasum": "", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc"}, "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.8.2"}, "time": "2024-04-13T18:00:56+00:00", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\ByteStream\\": "lib"}}, "require": {"php": ">=7.1", "amphp/amp": "^2"}, "require-dev": {"amphp/phpunit-util": "^1.4", "phpunit/phpunit": "^6 || ^7 || ^8", "friendsofphp/php-cs-fixer": "^2.3", "amphp/php-cs-fixer-config": "dev-master", "psalm/phar": "^3.11.4", "jetbrains/phpstorm-stubs": "^2019.3"}, "extra": "__unset"}, {"homepage": "http://amphp.org/byte-stream", "version": "v1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "acbd8002b3536485c997c4e019206b3f10ca15bd"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/acbd8002b3536485c997c4e019206b3f10ca15bd", "type": "zip", "shasum": "", "reference": "acbd8002b3536485c997c4e019206b3f10ca15bd"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.8.1"}, "time": "2021-03-30T17:13:30+00:00", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "f0c20cf598a958ba2aa8c6e5a71c697d652c7088"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/f0c20cf598a958ba2aa8c6e5a71c697d652c7088", "type": "zip", "shasum": "", "reference": "f0c20cf598a958ba2aa8c6e5a71c697d652c7088"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/master"}, "funding": [], "time": "2020-06-29T18:35:05+00:00"}, {"version": "v1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "b867505edb79dda8f253ca3c3a2bbadae4b16592"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/b867505edb79dda8f253ca3c3a2bbadae4b16592", "type": "zip", "shasum": "", "reference": "b867505edb79dda8f253ca3c3a2bbadae4b16592"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.7.3"}, "time": "2020-04-04T16:56:54+00:00", "require": {"amphp/amp": "^2"}, "require-dev": {"amphp/phpunit-util": "^1", "phpunit/phpunit": "^6 || ^7 || ^8", "friendsofphp/php-cs-fixer": "^2.3", "amphp/php-cs-fixer-config": "dev-master", "vimeo/psalm": "^3.9@dev", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "1e52f1752b2e20e2a7e464476ef887a2388e3832"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/1e52f1752b2e20e2a7e464476ef887a2388e3832", "type": "zip", "shasum": "", "reference": "1e52f1752b2e20e2a7e464476ef887a2388e3832"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/master"}, "time": "2020-01-29T18:22:23+00:00", "require-dev": {"amphp/phpunit-util": "^1", "phpunit/phpunit": "^6", "friendsofphp/php-cs-fixer": "^2.3", "amphp/php-cs-fixer-config": "dev-master", "infection/infection": "^0.9.3"}, "funding": "__unset", "extra": "__unset"}, {"version": "v1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "9d8205686a004948475dc43f8a88d2fa5e75a113"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/9d8205686a004948475dc43f8a88d2fa5e75a113", "type": "zip", "shasum": "", "reference": "9d8205686a004948475dc43f8a88d2fa5e75a113"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.7.1"}, "time": "2019-10-27T14:33:41+00:00"}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "3d457738751295b9bf6b15036578e7823919f724"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/3d457738751295b9bf6b15036578e7823919f724", "type": "zip", "shasum": "", "reference": "3d457738751295b9bf6b15036578e7823919f724"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/master"}, "time": "2019-08-24T17:01:13+00:00"}, {"version": "v1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "47908f8e8bb2da8af4e59028200758477bc927ea"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/47908f8e8bb2da8af4e59028200758477bc927ea", "type": "zip", "shasum": "", "reference": "47908f8e8bb2da8af4e59028200758477bc927ea"}, "time": "2019-07-26T21:22:49+00:00"}, {"version": "v1.6.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "c75af3778b5511deb1d760e4709cade2dabc2295"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/c75af3778b5511deb1d760e4709cade2dabc2295", "type": "zip", "shasum": "", "reference": "c75af3778b5511deb1d760e4709cade2dabc2295"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.6.0"}, "time": "2019-06-03T21:45:17+00:00"}, {"version": "v1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "6bbfcb6f47e92577e739586ba0c87e867be70a23"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/6bbfcb6f47e92577e739586ba0c87e867be70a23", "type": "zip", "shasum": "", "reference": "6bbfcb6f47e92577e739586ba0c87e867be70a23"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/master"}, "time": "2018-12-27T18:08:06+00:00"}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "37b9ab16bb8f69c825c3c4e553fe00da73dd6926"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/37b9ab16bb8f69c825c3c4e553fe00da73dd6926", "type": "zip", "shasum": "", "reference": "37b9ab16bb8f69c825c3c4e553fe00da73dd6926"}, "time": "2018-10-22T19:37:37+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "17fae3bcecf9b4e7854c4ee41e8d6f58dac5d98d"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/17fae3bcecf9b4e7854c4ee41e8d6f58dac5d98d", "type": "zip", "shasum": "", "reference": "17fae3bcecf9b4e7854c4ee41e8d6f58dac5d98d"}, "time": "2018-10-03T15:37:23+00:00"}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "1b75b122e6f069e7d102eef065dc192119d99ca7"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/1b75b122e6f069e7d102eef065dc192119d99ca7", "type": "zip", "shasum": "", "reference": "1b75b122e6f069e7d102eef065dc192119d99ca7"}, "time": "2018-04-04T05:33:09+00:00", "require-dev": {"amphp/phpunit-util": "^1", "phpunit/phpunit": "^6", "friendsofphp/php-cs-fixer": "^2.3"}}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "6d6c89f58c213e600e2ab5e3b1678fb61333eeb7"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/6d6c89f58c213e600e2ab5e3b1678fb61333eeb7", "type": "zip", "shasum": "", "reference": "6d6c89f58c213e600e2ab5e3b1678fb61333eeb7"}, "time": "2018-03-13T15:32:38+00:00"}, {"version": "v1.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "9f6097488121abd4817906da05474b85d9a106b4"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/9f6097488121abd4817906da05474b85d9a106b4", "type": "zip", "shasum": "", "reference": "9f6097488121abd4817906da05474b85d9a106b4"}, "time": "2018-03-11T09:19:43+00:00"}, {"version": "v1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "062f16d0c5bdaedcf9495928aa9324d6b028fe24"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/062f16d0c5bdaedcf9495928aa9324d6b028fe24", "type": "zip", "shasum": "", "reference": "062f16d0c5bdaedcf9495928aa9324d6b028fe24"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/fix-immediate"}, "time": "2018-03-09T16:40:36+00:00"}, {"version": "v1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "a4739c8a6dd96d772cfe49a95d5dfda8c7417595"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/a4739c8a6dd96d772cfe49a95d5dfda8c7417595", "type": "zip", "shasum": "", "reference": "a4739c8a6dd96d772cfe49a95d5dfda8c7417595"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/master"}, "time": "2018-03-08T10:38:16+00:00"}, {"version": "v1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "1b0b8daed484b070a9ee329ba78675288d291558"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/1b0b8daed484b070a9ee329ba78675288d291558", "type": "zip", "shasum": "", "reference": "1b0b8daed484b070a9ee329ba78675288d291558"}, "time": "2018-01-12T19:43:00+00:00"}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "0c059ba0e6aa7a1ebdce0ba92c77be1ec5dd9143"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/0c059ba0e6aa7a1ebdce0ba92c77be1ec5dd9143", "type": "zip", "shasum": "", "reference": "0c059ba0e6aa7a1ebdce0ba92c77be1ec5dd9143"}, "time": "2017-12-10T16:40:58+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "3c249038497b5b66a497807618aaf3f71af589f1"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/3c249038497b5b66a497807618aaf3f71af589f1", "type": "zip", "shasum": "", "reference": "3c249038497b5b66a497807618aaf3f71af589f1"}, "time": "2017-12-05T18:57:30+00:00"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "f63b98990edc8c4e401fc8ba2834197acf2df8e2"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/f63b98990edc8c4e401fc8ba2834197acf2df8e2", "type": "zip", "shasum": "", "reference": "f63b98990edc8c4e401fc8ba2834197acf2df8e2"}, "time": "2017-10-17T07:22:17+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "d60ba033c41d3329419575f2fbb02230855c258e"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/d60ba033c41d3329419575f2fbb02230855c258e", "type": "zip", "shasum": "", "reference": "d60ba033c41d3329419575f2fbb02230855c258e"}, "time": "2017-10-08T10:38:01+00:00"}, {"version": "v1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "00643414ac831e2c2a630df77d8dd4d85e395440"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/00643414ac831e2c2a630df77d8dd4d85e395440", "type": "zip", "shasum": "", "reference": "00643414ac831e2c2a630df77d8dd4d85e395440"}, "time": "2017-10-07T08:32:29+00:00"}, {"version": "v1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "fdcf400f19af26d06d183127d7cce57b24c574cf"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/fdcf400f19af26d06d183127d7cce57b24c574cf", "type": "zip", "shasum": "", "reference": "fdcf400f19af26d06d183127d7cce57b24c574cf"}, "time": "2017-09-15T05:17:42+00:00"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "6b9074fbf28fc871581204f5106c819004ed3aa3"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/6b9074fbf28fc871581204f5106c819004ed3aa3", "type": "zip", "shasum": "", "reference": "6b9074fbf28fc871581204f5106c819004ed3aa3"}, "time": "2017-07-15T08:11:27+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "47775086376fd2f82fc70b3862aa82e64a5ea667"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/47775086376fd2f82fc70b3862aa82e64a5ea667", "type": "zip", "shasum": "", "reference": "47775086376fd2f82fc70b3862aa82e64a5ea667"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/issue-18"}, "time": "2017-06-28T09:35:12+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "704adf70cfa7cd34172203bf6abae226323a77d1"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/704adf70cfa7cd34172203bf6abae226323a77d1", "type": "zip", "shasum": "", "reference": "704adf70cfa7cd34172203bf6abae226323a77d1"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/master"}, "time": "2017-06-23T14:33:13+00:00"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "b67358ad91e53b26c663c909050122f6f2ff6377"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/b67358ad91e53b26c663c909050122f6f2ff6377", "type": "zip", "shasum": "", "reference": "b67358ad91e53b26c663c909050122f6f2ff6377"}, "time": "2017-06-19T06:43:07+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "48c4e122d86bb1808b5ecb4659ba2e8165811fb1"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/48c4e122d86bb1808b5ecb4659ba2e8165811fb1", "type": "zip", "shasum": "", "reference": "48c4e122d86bb1808b5ecb4659ba2e8165811fb1"}, "time": "2017-06-19T04:14:59+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "8b20bf90b8390ce482fde6999b5f7c8b4c2740f5"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/8b20bf90b8390ce482fde6999b5f7c8b4c2740f5", "type": "zip", "shasum": "", "reference": "8b20bf90b8390ce482fde6999b5f7c8b4c2740f5"}, "time": "2017-06-17T15:46:18+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/byte-stream.git", "type": "git", "reference": "03ea1409457d02bcb96045b0e0845653b017bc60"}, "dist": {"url": "https://api.github.com/repos/amphp/byte-stream/zipball/03ea1409457d02bcb96045b0e0845653b017bc60", "type": "zip", "shasum": "", "reference": "03ea1409457d02bcb96045b0e0845653b017bc60"}, "time": "2017-06-15T15:58:59+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 16 Mar 2025 17:22:22 GMT"}