{"minified": "composer/2.0", "packages": {"mpociot/reflection-docblock": [{"name": "mpociot/reflection-docblock", "description": "", "keywords": [], "homepage": "", "version": "1.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/mpociot/reflection-docblock.git", "type": "git", "reference": "c8b2e2b1f5cebbb06e2b5ccbf2958f2198867587"}, "dist": {"url": "https://api.github.com/repos/mpociot/reflection-docblock/zipball/c8b2e2b1f5cebbb06e2b5ccbf2958f2198867587", "type": "zip", "shasum": "", "reference": "c8b2e2b1f5cebbb06e2b5ccbf2958f2198867587"}, "type": "library", "support": {"issues": "https://github.com/mpociot/reflection-docblock/issues", "source": "https://github.com/mpociot/reflection-docblock/tree/master"}, "time": "2016-06-20T20:53:12+00:00", "autoload": {"psr-0": {"Mpociot": ["src/"]}}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/mpociot/reflection-docblock.git", "type": "git", "reference": "abce9055dbc91e220acd2f999944d87bb8b42d43"}, "dist": {"url": "https://api.github.com/repos/mpociot/reflection-docblock/zipball/abce9055dbc91e220acd2f999944d87bb8b42d43", "type": "zip", "shasum": "", "reference": "abce9055dbc91e220acd2f999944d87bb8b42d43"}, "support": {"issues": "https://github.com/mpociot/reflection-docblock/issues", "source": "https://github.com/mpociot/reflection-docblock/tree/1.0.0"}, "time": "2016-06-20T07:38:56+00:00", "autoload": {"psr-0": {"phpDocumentor": ["src/"]}}}]}, "security-advisories": [], "last-modified": "Sun, 24 Mar 2024 09:24:12 GMT"}