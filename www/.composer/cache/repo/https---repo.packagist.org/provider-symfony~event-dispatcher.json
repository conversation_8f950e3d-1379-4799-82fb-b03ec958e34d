{"minified": "composer/2.0", "packages": {"symfony/event-dispatcher": [{"name": "symfony/event-dispatcher", "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "keywords": [], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/497f73ac996a598c92409b44ac43b6690c4f666d", "type": "zip", "shasum": "", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"}, "type": "library", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-22T09:11:45+00:00", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "require-dev": {"symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0-BETA1"}}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1", "type": "zip", "shasum": "", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"}, "time": "2024-09-25T14:21:43+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0-BETA1"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "87254c78dd50721cfd015b62277a8281c5589702"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/87254c78dd50721cfd015b62277a8281c5589702", "type": "zip", "shasum": "", "reference": "87254c78dd50721cfd015b62277a8281c5589702"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7", "type": "zip", "shasum": "", "reference": "9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "522d2772d6c7bab843b0c52466dc7844622bacc2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/522d2772d6c7bab843b0c52466dc7844622bacc2", "type": "zip", "shasum": "", "reference": "522d2772d6c7bab843b0c52466dc7844622bacc2"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.0-BETA1"}}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5c30c7fc4ccf847e4dd8a18b6158cb1f77702550"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5c30c7fc4ccf847e4dd8a18b6158cb1f77702550", "type": "zip", "shasum": "", "reference": "5c30c7fc4ccf847e4dd8a18b6158cb1f77702550"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "db2a7fab994d67d92356bb39c367db115d9d30f9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/db2a7fab994d67d92356bb39c367db115d9d30f9", "type": "zip", "shasum": "", "reference": "db2a7fab994d67d92356bb39c367db115d9d30f9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "834c28d533dd0636f910909d01b9ff45cc094b5e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/834c28d533dd0636f910909d01b9ff45cc094b5e", "type": "zip", "shasum": "", "reference": "834c28d533dd0636f910909d01b9ff45cc094b5e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "098b62ae81fdd6cbf941f355059f617db28f4f9a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/098b62ae81fdd6cbf941f355059f617db28f4f9a", "type": "zip", "shasum": "", "reference": "098b62ae81fdd6cbf941f355059f617db28f4f9a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.2"}, "time": "2023-12-27T22:24:19+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c459b40ffe67c49af6fd392aac374c9edf8a027e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c459b40ffe67c49af6fd392aac374c9edf8a027e", "type": "zip", "shasum": "", "reference": "c459b40ffe67c49af6fd392aac374c9edf8a027e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.0"}, "time": "2023-07-27T16:29:09+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.0.0-BETA1"}}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "type": "zip", "shasum": "", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00", "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "require-dev": {"symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "8d7507f02b06e06815e56bb39aa0128e3806208b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8d7507f02b06e06815e56bb39aa0128e3806208b", "type": "zip", "shasum": "", "reference": "8d7507f02b06e06815e56bb39aa0128e3806208b"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d84384f3f67de3cb650db64d685d70395dacfc3f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d84384f3f67de3cb650db64d685d70395dacfc3f", "type": "zip", "shasum": "", "reference": "d84384f3f67de3cb650db64d685d70395dacfc3f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "type": "zip", "shasum": "", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e95216850555cd55e71b857eb9d6c2674124603a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e95216850555cd55e71b857eb9d6c2674124603a", "type": "zip", "shasum": "", "reference": "e95216850555cd55e71b857eb9d6c2674124603a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.2"}, "time": "2023-12-27T22:16:42+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d76d2632cfc2206eecb5ad2b26cd5934082941b6"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d76d2632cfc2206eecb5ad2b26cd5934082941b6", "type": "zip", "shasum": "", "reference": "d76d2632cfc2206eecb5ad2b26cd5934082941b6"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.0"}, "time": "2023-07-27T06:52:43+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.0-BETA1"}}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6e344ddd3c18c525b5e5a4e996f3debda48e3078"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6e344ddd3c18c525b5e5a4e996f3debda48e3078", "type": "zip", "shasum": "", "reference": "6e344ddd3c18c525b5e5a4e996f3debda48e3078"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.3.12"}, "time": "2024-01-23T14:35:58+00:00", "require-dev": {"symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/config": "^5.4|^6.0", "symfony/error-handler": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4b4738c49f4dc2f6cf750301c7781dd0d715c0b8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4b4738c49f4dc2f6cf750301c7781dd0d715c0b8", "type": "zip", "shasum": "", "reference": "4b4738c49f4dc2f6cf750301c7781dd0d715c0b8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.3.11"}, "time": "2023-12-27T22:16:07+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "adb01fe097a4ee930db9258a3cc906b5beb5cf2e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/adb01fe097a4ee930db9258a3cc906b5beb5cf2e", "type": "zip", "shasum": "", "reference": "adb01fe097a4ee930db9258a3cc906b5beb5cf2e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.3.2"}, "time": "2023-07-06T06:56:43+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3af8ac1a3f98f6dbc55e10ae59c9e44bfc38dfaa"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3af8ac1a3f98f6dbc55e10ae59c9e44bfc38dfaa", "type": "zip", "shasum": "", "reference": "3af8ac1a3f98f6dbc55e10ae59c9e44bfc38dfaa"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.3.0"}, "time": "2023-04-21T14:41:17+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.3.0-BETA1"}}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a6e533212c6c298c6a4d1e892e4cdbf45ea21f1c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a6e533212c6c298c6a4d1e892e4cdbf45ea21f1c", "type": "zip", "shasum": "", "reference": "a6e533212c6c298c6a4d1e892e4cdbf45ea21f1c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.13"}, "time": "2023-07-06T06:53:05+00:00", "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2|^3"}, "require-dev": {"symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/config": "^5.4|^6.0", "symfony/error-handler": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^5.4|^6.0", "psr/log": "^1|^2|^3"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "conflict": {"symfony/dependency-injection": "<5.4"}}, {"version": "v6.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "04046f35fd7d72f9646e721fc2ecb8f9c67d3339"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/04046f35fd7d72f9646e721fc2ecb8f9c67d3339", "type": "zip", "shasum": "", "reference": "04046f35fd7d72f9646e721fc2ecb8f9c67d3339"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.8"}, "time": "2023-03-20T16:06:02+00:00"}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "404b307de426c1c488e5afad64403e5f145e82a5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/404b307de426c1c488e5afad64403e5f145e82a5", "type": "zip", "shasum": "", "reference": "404b307de426c1c488e5afad64403e5f145e82a5"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.7"}, "time": "2023-02-14T08:44:56+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f02d108b5e9fd4a6245aa73a9d2df2ec060c3e68"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f02d108b5e9fd4a6245aa73a9d2df2ec060c3e68", "type": "zip", "shasum": "", "reference": "f02d108b5e9fd4a6245aa73a9d2df2ec060c3e68"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.5"}, "time": "2023-01-01T08:38:09+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3ffeb31139b49bf6ef0bc09d1db95eac053388d1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3ffeb31139b49bf6ef0bc09d1db95eac053388d1", "type": "zip", "shasum": "", "reference": "3ffeb31139b49bf6ef0bc09d1db95eac053388d1"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.2"}, "time": "2022-12-14T16:11:27+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9efb1618fabee89515fe031314e8ed5625f85a53"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9efb1618fabee89515fe031314e8ed5625f85a53", "type": "zip", "shasum": "", "reference": "9efb1618fabee89515fe031314e8ed5625f85a53"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.0"}, "time": "2022-11-02T09:08:04+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7e0b66a9f3b02a166140603af1b2253827c750fe"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7e0b66a9f3b02a166140603af1b2253827c750fe", "type": "zip", "shasum": "", "reference": "7e0b66a9f3b02a166140603af1b2253827c750fe"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.2.0-BETA1"}, "time": "2022-09-30T20:34:56+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c961e9b08b1e800eb19fccefe2b8f6091b62a472"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c961e9b08b1e800eb19fccefe2b8f6091b62a472", "type": "zip", "shasum": "", "reference": "c961e9b08b1e800eb19fccefe2b8f6091b62a472"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.1.11"}, "time": "2023-01-01T08:36:55+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ca91778337cd851c33e41e46ae831cfb1e1e36cf"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ca91778337cd851c33e41e46ae831cfb1e1e36cf", "type": "zip", "shasum": "", "reference": "ca91778337cd851c33e41e46ae831cfb1e1e36cf"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.1.9"}, "time": "2022-12-14T16:05:20+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a0449a7ad7daa0f7c0acd508259f80544ab5a347"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a0449a7ad7daa0f7c0acd508259f80544ab5a347", "type": "zip", "shasum": "", "reference": "a0449a7ad7daa0f7c0acd508259f80544ab5a347"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.1.0"}, "time": "2022-05-05T16:51:07+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "00cd271920f06eba10c6be1f7dff7453d08e85ae"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/00cd271920f06eba10c6be1f7dff7453d08e85ae", "type": "zip", "shasum": "", "reference": "00cd271920f06eba10c6be1f7dff7453d08e85ae"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.1.0-BETA2"}, "time": "2022-04-15T14:25:02+00:00"}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f24d4329e3a595e2843234bb14935a2b41f350d9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f24d4329e3a595e2843234bb14935a2b41f350d9", "type": "zip", "shasum": "", "reference": "f24d4329e3a595e2843234bb14935a2b41f350d9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.1.0-BETA1"}, "time": "2022-02-25T11:15:52+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2eaf8e63bc5b8cefabd4a800157f0d0c094f677a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "type": "zip", "shasum": "", "reference": "2eaf8e63bc5b8cefabd4a800157f0d0c094f677a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.19"}, "time": "2023-01-01T08:36:10+00:00", "require": {"php": ">=8.0.2", "symfony/event-dispatcher-contracts": "^2|^3"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "42b3985aa07837c9df36013ec5b965e9f2d480bc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/42b3985aa07837c9df36013ec5b965e9f2d480bc", "type": "zip", "shasum": "", "reference": "42b3985aa07837c9df36013ec5b965e9f2d480bc"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.17"}, "time": "2022-12-14T15:52:41+00:00"}, {"version": "v6.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5c85b58422865d42c6eb46f7693339056db098a8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5c85b58422865d42c6eb46f7693339056db098a8", "type": "zip", "shasum": "", "reference": "5c85b58422865d42c6eb46f7693339056db098a8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.9"}, "time": "2022-05-05T16:45:52+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6472ea2dd415e925b90ca82be64b8bc6157f3934"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6472ea2dd415e925b90ca82be64b8bc6157f3934", "type": "zip", "shasum": "", "reference": "6472ea2dd415e925b90ca82be64b8bc6157f3934"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.3"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7093f25359e2750bfe86842c80c4e4a6a852d05c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7093f25359e2750bfe86842c80c4e4a6a852d05c", "type": "zip", "shasum": "", "reference": "7093f25359e2750bfe86842c80c4e4a6a852d05c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.2"}, "time": "2021-12-21T10:43:13+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4f06d19a5f78087061f9de6df3269c139c3d289d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4f06d19a5f78087061f9de6df3269c139c3d289d", "type": "zip", "shasum": "", "reference": "4f06d19a5f78087061f9de6df3269c139c3d289d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2774908d5ae32fd94e363e7cbbd87462712c4576"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2774908d5ae32fd94e363e7cbbd87462712c4576", "type": "zip", "shasum": "", "reference": "2774908d5ae32fd94e363e7cbbd87462712c4576"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.0"}, "time": "2021-11-23T19:05:29+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "631d4b465c2ee87df9f6c2c2438fbcd1a3e15d79"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/631d4b465c2ee87df9f6c2c2438fbcd1a3e15d79", "type": "zip", "shasum": "", "reference": "631d4b465c2ee87df9f6c2c2438fbcd1a3e15d79"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.0-BETA3"}, "time": "2021-11-17T12:23:22+00:00", "require": {"php": ">=8.0.2", "symfony/event-dispatcher-contracts": "^2.0|^3.0"}, "require-dev": {"symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/config": "^5.4|^6.0", "symfony/error-handler": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2.0|^3.0", "symfony/stopwatch": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a0bf29b4c7d88b7e491fe060b0f34a9357d1af2e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a0bf29b4c7d88b7e491fe060b0f34a9357d1af2e", "type": "zip", "shasum": "", "reference": "a0bf29b4c7d88b7e491fe060b0f34a9357d1af2e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.0-BETA1"}, "time": "2021-11-03T13:44:55+00:00"}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/72982eb416f61003e9bb6e91f8b3213600dcf9e9", "type": "zip", "shasum": "", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a54e2a8a114065f31020d6a89ede83e34c3b27a4"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a54e2a8a114065f31020d6a89ede83e34c3b27a4", "type": "zip", "shasum": "", "reference": "a54e2a8a114065f31020d6a89ede83e34c3b27a4"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d40fae9fd85c762b6ba378152fdd1157a85d7e4f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d40fae9fd85c762b6ba378152fdd1157a85d7e4f", "type": "zip", "shasum": "", "reference": "d40fae9fd85c762b6ba378152fdd1157a85d7e4f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7a69a85c7ea5bdd1e875806a99c51a87d3a74b38"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7a69a85c7ea5bdd1e875806a99c51a87d3a74b38", "type": "zip", "shasum": "", "reference": "7a69a85c7ea5bdd1e875806a99c51a87d3a74b38"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.35"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v5.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e3bca343efeb613f843c254e7718ef17c9bdf7a3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e3bca343efeb613f843c254e7718ef17c9bdf7a3", "type": "zip", "shasum": "", "reference": "e3bca343efeb613f843c254e7718ef17c9bdf7a3"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.34"}, "time": "2023-12-27T21:12:56+00:00"}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5dcc00e03413f05c1e7900090927bb7247cb0aac"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5dcc00e03413f05c1e7900090927bb7247cb0aac", "type": "zip", "shasum": "", "reference": "5dcc00e03413f05c1e7900090927bb7247cb0aac"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.26"}, "time": "2023-07-06T06:34:20+00:00"}, {"version": "v5.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "1df20e45d56da29a4b1d8259dd6e950acbf1b13f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1df20e45d56da29a4b1d8259dd6e950acbf1b13f", "type": "zip", "shasum": "", "reference": "1df20e45d56da29a4b1d8259dd6e950acbf1b13f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.22"}, "time": "2023-03-17T11:31:58+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f0ae1383a8285dfc6752b8d8602790953118ff5a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f0ae1383a8285dfc6752b8d8602790953118ff5a", "type": "zip", "shasum": "", "reference": "f0ae1383a8285dfc6752b8d8602790953118ff5a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.21"}, "time": "2023-02-14T08:03:56+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "abf49cc084c087d94b4cb939c3f3672971784e0c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/abf49cc084c087d94b4cb939c3f3672971784e0c", "type": "zip", "shasum": "", "reference": "abf49cc084c087d94b4cb939c3f3672971784e0c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.19"}, "time": "2023-01-01T08:32:19+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "8e18a9d559eb8ebc2220588f1faa726a2fcd31c9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e18a9d559eb8ebc2220588f1faa726a2fcd31c9", "type": "zip", "shasum": "", "reference": "8e18a9d559eb8ebc2220588f1faa726a2fcd31c9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.17"}, "time": "2022-12-12T15:54:21+00:00"}, {"version": "v5.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "type": "zip", "shasum": "", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.9"}, "time": "2022-05-05T16:45:39+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "dec8a9f58d20df252b9cd89f1c6c1530f747685d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/dec8a9f58d20df252b9cd89f1c6c1530f747685d", "type": "zip", "shasum": "", "reference": "dec8a9f58d20df252b9cd89f1c6c1530f747685d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.3"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "27d39ae126352b9fa3be5e196ccf4617897be3eb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/27d39ae126352b9fa3be5e196ccf4617897be3eb", "type": "zip", "shasum": "", "reference": "27d39ae126352b9fa3be5e196ccf4617897be3eb"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.0"}, "time": "2021-11-23T10:19:22+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9748a8d4529750bc22e9e9ec6d8597caa147952d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9748a8d4529750bc22e9e9ec6d8597caa147952d", "type": "zip", "shasum": "", "reference": "9748a8d4529750bc22e9e9ec6d8597caa147952d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.0-BETA3"}, "time": "2021-11-17T12:18:18+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9c8d6999c25b66f23fe2cb6f801c67b353abb88c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9c8d6999c25b66f23fe2cb6f801c67b353abb88c", "type": "zip", "shasum": "", "reference": "9c8d6999c25b66f23fe2cb6f801c67b353abb88c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.0-BETA1"}, "time": "2021-11-03T13:16:59+00:00"}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6dc2d5b31cdf84fa6344f44056c32f939fcb8c4a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6dc2d5b31cdf84fa6344f44056c32f939fcb8c4a", "type": "zip", "shasum": "", "reference": "6dc2d5b31cdf84fa6344f44056c32f939fcb8c4a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00", "require-dev": {"symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0", "psr/log": "^1|^2|^3"}}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "661a7a6e085394f8513945669e31f7c1338a7e69"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/661a7a6e085394f8513945669e31f7c1338a7e69", "type": "zip", "shasum": "", "reference": "661a7a6e085394f8513945669e31f7c1338a7e69"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.11"}, "time": "2021-11-17T12:16:12+00:00"}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ce7b20d69c66a20939d8952b617506a44d102130"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ce7b20d69c66a20939d8952b617506a44d102130", "type": "zip", "shasum": "", "reference": "ce7b20d69c66a20939d8952b617506a44d102130"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.7"}, "time": "2021-08-04T21:20:46+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f2fd2208157553874560f3645d4594303058c4bd"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f2fd2208157553874560f3645d4594303058c4bd", "type": "zip", "shasum": "", "reference": "f2fd2208157553874560f3645d4594303058c4bd"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.4"}, "time": "2021-07-23T15:55:36+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "67a5f354afa8e2f231081b3fa11a5912f933c3ce"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/67a5f354afa8e2f231081b3fa11a5912f933c3ce", "type": "zip", "shasum": "", "reference": "67a5f354afa8e2f231081b3fa11a5912f933c3ce"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0", "psr/log": "~1.0"}}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ff708cb53cbd8ff44d1f327d3169a4a75f0a7647"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ff708cb53cbd8ff44d1f327d3169a4a75f0a7647", "type": "zip", "shasum": "", "reference": "ff708cb53cbd8ff44d1f327d3169a4a75f0a7647"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.0-RC1"}, "time": "2021-03-23T17:57:16+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.3.0-BETA1"}}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6a32cd803f8ff33d84b709de3978f44a62e42961"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6a32cd803f8ff33d84b709de3978f44a62e42961", "type": "zip", "shasum": "", "reference": "6a32cd803f8ff33d84b709de3978f44a62e42961"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.12"}, "time": "2021-07-23T15:54:19+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0", "psr/log": "^1|^2|^3"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2ffa4bf7469317e23cc5e3f716db6071e6525f5a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2ffa4bf7469317e23cc5e3f716db6071e6525f5a", "type": "zip", "shasum": "", "reference": "2ffa4bf7469317e23cc5e3f716db6071e6525f5a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.10"}, "time": "2021-05-26T17:40:38+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0", "psr/log": "~1.0"}}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d08d6ec121a425897951900ab692b612a61d6240"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d08d6ec121a425897951900ab692b612a61d6240", "type": "zip", "shasum": "", "reference": "d08d6ec121a425897951900ab692b612a61d6240"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.4"}, "time": "2021-02-18T17:12:37+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4f9760f8074978ad82e2ce854dff79a71fe45367"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4f9760f8074978ad82e2ce854dff79a71fe45367", "type": "zip", "shasum": "", "reference": "4f9760f8074978ad82e2ce854dff79a71fe45367"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.3"}, "time": "2021-01-27T10:36:42+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.2"}}, {"description": "Symfony EventDispatcher Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "1c93f7a1dff592c252574c79a8635a8a80856042"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1c93f7a1dff592c252574c79a8635a8a80856042", "type": "zip", "shasum": "", "reference": "1c93f7a1dff592c252574c79a8635a8a80856042"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.1"}, "time": "2020-12-18T08:03:05+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "aa13a09811e6d2ad43f8fb336bebdb7691d85d3c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/aa13a09811e6d2ad43f8fb336bebdb7691d85d3c", "type": "zip", "shasum": "", "reference": "aa13a09811e6d2ad43f8fb336bebdb7691d85d3c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.0"}, "time": "2020-11-01T16:14:45+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.0-RC2"}}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4493f163b24f5bfbd783e7bafde43f828ad6e4d2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4493f163b24f5bfbd783e7bafde43f828ad6e4d2", "type": "zip", "shasum": "", "reference": "4493f163b24f5bfbd783e7bafde43f828ad6e4d2"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:08:07+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "30555fd464b9065c7bbf7a2a18d34207ea6c2be3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/30555fd464b9065c7bbf7a2a18d34207ea6c2be3", "type": "zip", "shasum": "", "reference": "30555fd464b9065c7bbf7a2a18d34207ea6c2be3"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "cf7666f330065dad9f0471e293421acf0565e3be"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cf7666f330065dad9f0471e293421acf0565e3be", "type": "zip", "shasum": "", "reference": "cf7666f330065dad9f0471e293421acf0565e3be"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.0-BETA1"}, "time": "2020-09-20T07:20:17+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c00f3aae24577a991ae97d34c7033b2e84f1cfa0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c00f3aae24577a991ae97d34c7033b2e84f1cfa0", "type": "zip", "shasum": "", "reference": "c00f3aae24577a991ae97d34c7033b2e84f1cfa0"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.11"}, "time": "2021-01-27T10:36:24+00:00", "extra": "__unset"}, {"description": "Symfony EventDispatcher Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2c8d69babd624ed845d6c7a21f0f13a720ae0096"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2c8d69babd624ed845d6c7a21f0f13a720ae0096", "type": "zip", "shasum": "", "reference": "2c8d69babd624ed845d6c7a21f0f13a720ae0096"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.10"}, "time": "2020-12-18T08:02:46+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2c660884ec9413455af753515140ce696913693c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2c660884ec9413455af753515140ce696913693c", "type": "zip", "shasum": "", "reference": "2c660884ec9413455af753515140ce696913693c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.9"}, "time": "2020-11-01T15:43:26+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "26f4edae48c913fc183a3da0553fe63bdfbd361a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/26f4edae48c913fc183a3da0553fe63bdfbd361a", "type": "zip", "shasum": "", "reference": "26f4edae48c913fc183a3da0553fe63bdfbd361a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d5de97d6af175a9e8131c546db054ca32842dd0f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d5de97d6af175a9e8131c546db054ca32842dd0f", "type": "zip", "shasum": "", "reference": "d5de97d6af175a9e8131c546db054ca32842dd0f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.7"}, "time": "2020-09-18T14:27:32+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.6"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "94871fc0a69c3c5da57764187724cdce0755899c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/94871fc0a69c3c5da57764187724cdce0755899c", "type": "zip", "shasum": "", "reference": "94871fc0a69c3c5da57764187724cdce0755899c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.4"}, "time": "2020-08-13T14:19:42+00:00", "require-dev": {"symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0", "psr/log": "~1.0"}}, {"version": "v5.1.4", "version_normalized": "*******"}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7827d55911f91c070fc293ea51a06eec80797d76"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7827d55911f91c070fc293ea51a06eec80797d76", "type": "zip", "shasum": "", "reference": "7827d55911f91c070fc293ea51a06eec80797d76"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.3"}, "time": "2020-06-18T18:24:02+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "cc0d059e2e997e79ca34125a52f3e33de4424ac7"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cc0d059e2e997e79ca34125a52f3e33de4424ac7", "type": "zip", "shasum": "", "reference": "cc0d059e2e997e79ca34125a52f3e33de4424ac7"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.1.0-RC2"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.1", "version_normalized": "*******"}, {"version": "v5.1.0", "version_normalized": "*******"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "203100f0815173176606309a168afca7ec20e88b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/203100f0815173176606309a168afca7ec20e88b", "type": "zip", "shasum": "", "reference": "203100f0815173176606309a168afca7ec20e88b"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2020-05-16T09:12:54+00:00", "require": {"php": "^7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.15"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bc8f774a88ff35f65cb91bc38ef3e31d27d24eb5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bc8f774a88ff35f65cb91bc38ef3e31d27d24eb5", "type": "zip", "shasum": "", "reference": "bc8f774a88ff35f65cb91bc38ef3e31d27d24eb5"}, "time": "2020-04-23T21:57:44+00:00"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5c5dd86c7a7962d28c48351c7dd83c9266e4d19d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5c5dd86c7a7962d28c48351c7dd83c9266e4d19d", "type": "zip", "shasum": "", "reference": "5c5dd86c7a7962d28c48351c7dd83c9266e4d19d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/5.0"}, "time": "2020-06-18T18:18:56+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "symfony/event-dispatcher-contracts": "^2"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9f2efcbb6f7bc86d7cb0ca55c5ea105a2a4ed105"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9f2efcbb6f7bc86d7cb0ca55c5ea105a2a4ed105", "type": "zip", "shasum": "", "reference": "9f2efcbb6f7bc86d7cb0ca55c5ea105a2a4ed105"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.0.9"}, "time": "2020-05-20T17:38:26+00:00"}, {"version": "v5.0.9", "version_normalized": "*******"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "24f40d95385774ed5c71dbf014edd047e2f2f3dc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/24f40d95385774ed5c71dbf014edd047e2f2f3dc", "type": "zip", "shasum": "", "reference": "24f40d95385774ed5c71dbf014edd047e2f2f3dc"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.0.7"}, "time": "2020-03-27T16:56:45+00:00", "require": {"php": "^7.2.5", "symfony/event-dispatcher-contracts": "^2"}}, {"version": "v5.0.7", "version_normalized": "*******"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b45ad88b253c5a9702ce218e201d89c85d148cea"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b45ad88b253c5a9702ce218e201d89c85d148cea", "type": "zip", "shasum": "", "reference": "b45ad88b253c5a9702ce218e201d89c85d148cea"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/5.0"}, "funding": [], "time": "2020-02-22T20:09:08+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.0.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}]}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4a7a8cdca1120c091b4797f0e5bba69c1e783224"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4a7a8cdca1120c091b4797f0e5bba69c1e783224", "type": "zip", "shasum": "", "reference": "4a7a8cdca1120c091b4797f0e5bba69c1e783224"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/5.0"}, "time": "2020-01-10T21:57:37+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.0.3"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7b738a51645e10f864cc25c24d232fb03f37b475"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7b738a51645e10f864cc25c24d232fb03f37b475", "type": "zip", "shasum": "", "reference": "7b738a51645e10f864cc25c24d232fb03f37b475"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.0.0"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.1", "version_normalized": "*******"}, {"version": "v5.0.0", "version_normalized": "*******"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9f3b0b5c5185892003f7c2368e6b091cf278c05e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9f3b0b5c5185892003f7c2368e6b091cf278c05e", "type": "zip", "shasum": "", "reference": "9f3b0b5c5185892003f7c2368e6b091cf278c05e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2019-11-09T09:18:34+00:00", "require": {"php": "^7.2.9", "symfony/event-dispatcher-contracts": "^2"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.0.0-BETA2"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}}, {"description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "type": "zip", "shasum": "", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00", "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "extra": "__unset"}, {"version": "v4.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "708e761740c16b02c86e3f0c932018a06b895d40"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/708e761740c16b02c86e3f0c932018a06b895d40", "type": "zip", "shasum": "", "reference": "708e761740c16b02c86e3f0c932018a06b895d40"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.42"}, "time": "2022-05-05T15:33:49+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3ccfcfb96ecce1217d7b0875a0736976bc6e63dc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3ccfcfb96ecce1217d7b0875a0736976bc6e63dc", "type": "zip", "shasum": "", "reference": "3ccfcfb96ecce1217d7b0875a0736976bc6e63dc"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8", "type": "zip", "shasum": "", "reference": "1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.34"}, "time": "2021-11-15T14:42:25+00:00"}, {"version": "v4.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2fe81680070043c4c80e7cedceb797e34f377bac"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2fe81680070043c4c80e7cedceb797e34f377bac", "type": "zip", "shasum": "", "reference": "2fe81680070043c4c80e7cedceb797e34f377bac"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.30"}, "time": "2021-08-04T20:31:23+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "958a128b184fcf0ba45ec90c0e88554c9327c2e9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/958a128b184fcf0ba45ec90c0e88554c9327c2e9", "type": "zip", "shasum": "", "reference": "958a128b184fcf0ba45ec90c0e88554c9327c2e9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.27"}, "time": "2021-07-23T15:41:52+00:00"}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "047773e7016e4fd45102cedf4bd2558ae0d0c32f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/047773e7016e4fd45102cedf4bd2558ae0d0c32f", "type": "zip", "shasum": "", "reference": "047773e7016e4fd45102cedf4bd2558ae0d0c32f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.25"}, "time": "2021-05-26T17:39:37+00:00", "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "require-dev": {"symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0", "psr/log": "~1.0"}}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c352647244bd376bf7d31efbd5401f13f50dad0c", "type": "zip", "shasum": "", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.20"}, "time": "2021-01-27T09:09:26+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.19"}}, {"description": "Symfony EventDispatcher Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5d4c874b0eb1c32d40328a09dbc37307a5a910b0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5d4c874b0eb1c32d40328a09dbc37307a5a910b0", "type": "zip", "shasum": "", "reference": "5d4c874b0eb1c32d40328a09dbc37307a5a910b0"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.18"}, "time": "2020-12-18T07:41:31+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f029d6f21eac61ab23198e7aca40e7638e8c8924"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f029d6f21eac61ab23198e7aca40e7638e8c8924", "type": "zip", "shasum": "", "reference": "f029d6f21eac61ab23198e7aca40e7638e8c8924"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.17"}, "time": "2020-10-31T22:44:29+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4204f13d2d0b7ad09454f221bb2195fccdf1fe98"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4204f13d2d0b7ad09454f221bb2195fccdf1fe98", "type": "zip", "shasum": "", "reference": "4204f13d2d0b7ad09454f221bb2195fccdf1fe98"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e17bb5e0663dc725f7cdcafc932132735b4725cd"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e17bb5e0663dc725f7cdcafc932132735b4725cd", "type": "zip", "shasum": "", "reference": "e17bb5e0663dc725f7cdcafc932132735b4725cd"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.15"}, "time": "2020-09-18T14:07:46+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.14"}}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3e8ea5ccddd00556b86d69d42f99f1061a704030"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3e8ea5ccddd00556b86d69d42f99f1061a704030", "type": "zip", "shasum": "", "reference": "3e8ea5ccddd00556b86d69d42f99f1061a704030"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.13"}, "time": "2020-08-13T14:18:44+00:00", "require-dev": {"symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0", "psr/log": "~1.0"}}, {"version": "v4.4.12", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.4"}}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6140fc7047dafc5abbe84ba16a34a86c0b0229b8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6140fc7047dafc5abbe84ba16a34a86c0b0229b8", "type": "zip", "shasum": "", "reference": "6140fc7047dafc5abbe84ba16a34a86c0b0229b8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.11"}, "time": "2020-06-18T17:59:13+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a5370aaa7807c7a439b21386661ffccf3dff2866"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a5370aaa7807c7a439b21386661ffccf3dff2866", "type": "zip", "shasum": "", "reference": "a5370aaa7807c7a439b21386661ffccf3dff2866"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.4"}, "time": "2020-05-20T08:37:50+00:00"}, {"version": "v4.4.9", "version_normalized": "*******"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "abc8e3618bfdb55e44c8c6a00abd333f831bbfed"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/abc8e3618bfdb55e44c8c6a00abd333f831bbfed", "type": "zip", "shasum": "", "reference": "abc8e3618bfdb55e44c8c6a00abd333f831bbfed"}, "time": "2020-03-27T16:54:36+00:00", "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}}, {"version": "v4.4.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.7"}}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "cf57788d1ca64ee7e689698dc0295d25c9fe3780"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cf57788d1ca64ee7e689698dc0295d25c9fe3780", "type": "zip", "shasum": "", "reference": "cf57788d1ca64ee7e689698dc0295d25c9fe3780"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.4"}, "time": "2020-03-16T11:24:17+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4ad8e149799d3128621a3a1f70e92b9897a8930d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4ad8e149799d3128621a3a1f70e92b9897a8930d", "type": "zip", "shasum": "", "reference": "4ad8e149799d3128621a3a1f70e92b9897a8930d"}, "funding": [], "time": "2020-02-04T09:32:40+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9e3de195e5bc301704dd6915df55892f6dfc208b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9e3de195e5bc301704dd6915df55892f6dfc208b", "type": "zip", "shasum": "", "reference": "9e3de195e5bc301704dd6915df55892f6dfc208b"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.3"}, "time": "2020-01-10T21:54:01+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b3c3068a72623287550fe20b84a2b01dcba2686f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b3c3068a72623287550fe20b84a2b01dcba2686f", "type": "zip", "shasum": "", "reference": "b3c3068a72623287550fe20b84a2b01dcba2686f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.2"}, "time": "2019-11-28T13:33:56+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.4"}}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ab1c43e17fff802bef0a898f3bc088ac33b8e0e1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ab1c43e17fff802bef0a898f3bc088ac33b8e0e1", "type": "zip", "shasum": "", "reference": "ab1c43e17fff802bef0a898f3bc088ac33b8e0e1"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.0-BETA1"}, "time": "2019-11-08T22:40:51+00:00"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "75f99d7489409207d09c6cd75a6c773ccbb516d5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/75f99d7489409207d09c6cd75a6c773ccbb516d5", "type": "zip", "shasum": "", "reference": "75f99d7489409207d09c6cd75a6c773ccbb516d5"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.10"}, "time": "2020-01-09T13:17:05+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require-dev": {"symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/config": "~3.4|~4.0", "symfony/http-foundation": "^3.4|^4.0", "symfony/service-contracts": "^1.1", "symfony/stopwatch": "~3.4|~4.0", "psr/log": "~1.0"}}, {"version": "v4.3.10", "version_normalized": "********"}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "87a1ae7480f2020818013605a65776b9033bcc4f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/87a1ae7480f2020818013605a65776b9033bcc4f", "type": "zip", "shasum": "", "reference": "87a1ae7480f2020818013605a65776b9033bcc4f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.3"}, "time": "2019-11-28T13:25:45+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0df002fd4f500392eabd243c2947061a50937287"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0df002fd4f500392eabd243c2947061a50937287", "type": "zip", "shasum": "", "reference": "0df002fd4f500392eabd243c2947061a50937287"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.8"}, "time": "2019-11-03T09:04:05+00:00"}, {"version": "v4.3.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.3"}}, {"version": "v4.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6229f58993e5a157f6096fc7145c0717d0be8807"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6229f58993e5a157f6096fc7145c0717d0be8807", "type": "zip", "shasum": "", "reference": "6229f58993e5a157f6096fc7145c0717d0be8807"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.5"}, "time": "2019-10-01T16:40:32+00:00"}, {"version": "v4.3.5", "version_normalized": "*******"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "429d0a1451d4c9c4abe1959b2986b88794b9b7d2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/429d0a1451d4c9c4abe1959b2986b88794b9b7d2", "type": "zip", "shasum": "", "reference": "429d0a1451d4c9c4abe1959b2986b88794b9b7d2"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.4"}, "time": "2019-08-26T08:55:16+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "212b020949331b6531250584531363844b34a94e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/212b020949331b6531250584531363844b34a94e", "type": "zip", "shasum": "", "reference": "212b020949331b6531250584531363844b34a94e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.3"}, "time": "2019-06-27T06:42:14+00:00"}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d257021c1ab28d48d24a16de79dfab445ce93398"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d257021c1ab28d48d24a16de79dfab445ce93398", "type": "zip", "shasum": "", "reference": "d257021c1ab28d48d24a16de79dfab445ce93398"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.2"}, "time": "2019-06-13T11:03:18+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4e6c670af81c4fb0b6c08b035530a9915d0b691f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4e6c670af81c4fb0b6c08b035530a9915d0b691f", "type": "zip", "shasum": "", "reference": "4e6c670af81c4fb0b6c08b035530a9915d0b691f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.1"}, "time": "2019-05-30T16:10:05+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c71314cd3b9420b732e1526f33a24eff5430b5b3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c71314cd3b9420b732e1526f33a24eff5430b5b3", "type": "zip", "shasum": "", "reference": "c71314cd3b9420b732e1526f33a24eff5430b5b3"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.3"}, "time": "2019-05-28T07:50:59+00:00"}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.0-RC1"}}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "fe7614934eb936c3d6d063896bfbdcf22c356d0d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/fe7614934eb936c3d6d063896bfbdcf22c356d0d", "type": "zip", "shasum": "", "reference": "fe7614934eb936c3d6d063896bfbdcf22c356d0d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.3"}, "time": "2019-05-17T13:28:19+00:00", "require": {"php": "^7.1.3", "symfony/contracts": "^1.1"}, "require-dev": {"symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/config": "~3.4|~4.0", "symfony/http-foundation": "^3.4|^4.0", "symfony/stopwatch": "~3.4|~4.0", "psr/log": "~1.0"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "861c10069b0a073618c7c2d60eacbccafa72f697"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/861c10069b0a073618c7c2d60eacbccafa72f697", "type": "zip", "shasum": "", "reference": "861c10069b0a073618c7c2d60eacbccafa72f697"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.3.0-BETA1"}, "time": "2019-04-27T13:05:39+00:00"}, {"version": "v4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "852548c7c704f14d2f6700c8d872a05bd2028732"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/852548c7c704f14d2f6700c8d872a05bd2028732", "type": "zip", "shasum": "", "reference": "852548c7c704f14d2f6700c8d872a05bd2028732"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.2"}, "time": "2019-06-26T06:46:55+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0"}, "require-dev": {"symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/config": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0", "psr/log": "~1.0"}, "provide": "__unset"}, {"version": "v4.2.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.2.11"}}, {"version": "v4.2.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.2"}}, {"version": "v4.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "fbce53cd74ac509cbe74b6f227622650ab759b02"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/fbce53cd74ac509cbe74b6f227622650ab759b02", "type": "zip", "shasum": "", "reference": "fbce53cd74ac509cbe74b6f227622650ab759b02"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.2.8"}, "time": "2019-04-06T13:51:08+00:00"}, {"version": "v4.2.8", "version_normalized": "*******"}, {"version": "v4.2.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.2"}}, {"version": "v4.2.6", "version_normalized": "*******"}, {"version": "v4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ca5af306fbc37f3cf597e91bc9cfa0c7d3f33544"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ca5af306fbc37f3cf597e91bc9cfa0c7d3f33544", "type": "zip", "shasum": "", "reference": "ca5af306fbc37f3cf597e91bc9cfa0c7d3f33544"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.2.5"}, "time": "2019-03-30T15:58:42+00:00"}, {"version": "v4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3354d2e6af986dd71f68b4e5cf4a933ab58697fb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3354d2e6af986dd71f68b4e5cf4a933ab58697fb", "type": "zip", "shasum": "", "reference": "3354d2e6af986dd71f68b4e5cf4a933ab58697fb"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.2"}, "time": "2019-02-23T15:17:42+00:00"}, {"version": "v4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bd09ad265cd50b2b9d09d65ce6aba2d29bc81fe1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bd09ad265cd50b2b9d09d65ce6aba2d29bc81fe1", "type": "zip", "shasum": "", "reference": "bd09ad265cd50b2b9d09d65ce6aba2d29bc81fe1"}, "time": "2019-01-16T20:35:37+00:00"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "887de6d34c86cf0cb6cbf910afb170cdb743cb5e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/887de6d34c86cf0cb6cbf910afb170cdb743cb5e", "type": "zip", "shasum": "", "reference": "887de6d34c86cf0cb6cbf910afb170cdb743cb5e"}, "time": "2019-01-05T16:37:49+00:00"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "921f49c3158a276d27c0d770a5a347a3b718b328"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/921f49c3158a276d27c0d770a5a347a3b718b328", "type": "zip", "shasum": "", "reference": "921f49c3158a276d27c0d770a5a347a3b718b328"}, "time": "2018-12-01T08:52:38+00:00"}, {"version": "v4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9b788b5f7cd6be22918f3d18ee3ff0a78e060137"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9b788b5f7cd6be22918f3d18ee3ff0a78e060137", "type": "zip", "shasum": "", "reference": "9b788b5f7cd6be22918f3d18ee3ff0a78e060137"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2018-11-26T10:55:26+00:00"}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d39d7a05414c4462abd8c077322b4811549f705c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d39d7a05414c4462abd8c077322b4811549f705c", "type": "zip", "shasum": "", "reference": "d39d7a05414c4462abd8c077322b4811549f705c"}, "time": "2018-11-11T19:52:12+00:00"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c8bbac387c9b4ba48586e2f839303274274600fe"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c8bbac387c9b4ba48586e2f839303274274600fe", "type": "zip", "shasum": "", "reference": "c8bbac387c9b4ba48586e2f839303274274600fe"}, "time": "2018-10-10T13:54:27+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "51be1b61dfe04d64a260223f2b81475fa8066b97"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/51be1b61dfe04d64a260223f2b81475fa8066b97", "type": "zip", "shasum": "", "reference": "51be1b61dfe04d64a260223f2b81475fa8066b97"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.1.12"}, "time": "2019-01-16T18:35:49+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require": {"php": "^7.1.3"}}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9da78639776ee15285a1505f1dc405a6e6844a87"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9da78639776ee15285a1505f1dc405a6e6844a87", "type": "zip", "shasum": "", "reference": "9da78639776ee15285a1505f1dc405a6e6844a87"}, "time": "2019-01-04T15:09:47+00:00"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c4a3b5d70c05e5e7de4f22a3e840cdb173ccd7bf"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c4a3b5d70c05e5e7de4f22a3e840cdb173ccd7bf", "type": "zip", "shasum": "", "reference": "c4a3b5d70c05e5e7de4f22a3e840cdb173ccd7bf"}, "time": "2018-12-01T08:51:37+00:00"}, {"version": "v4.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "8b93ce06506d58485893e2da366767dcc5390862"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8b93ce06506d58485893e2da366767dcc5390862", "type": "zip", "shasum": "", "reference": "8b93ce06506d58485893e2da366767dcc5390862"}, "time": "2018-11-26T10:26:29+00:00"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "552541dad078c85d9414b09c041ede488b456cd5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/552541dad078c85d9414b09c041ede488b456cd5", "type": "zip", "shasum": "", "reference": "552541dad078c85d9414b09c041ede488b456cd5"}, "time": "2018-10-10T13:52:42+00:00"}, {"version": "v4.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bfb30c2ad377615a463ebbc875eba64a99f6aa3e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bfb30c2ad377615a463ebbc875eba64a99f6aa3e", "type": "zip", "shasum": "", "reference": "bfb30c2ad377615a463ebbc875eba64a99f6aa3e"}, "time": "2018-07-26T09:10:45+00:00"}, {"version": "v4.1.5", "version_normalized": "*******"}, {"version": "v4.1.4", "version_normalized": "*******"}, {"version": "v4.1.3", "version_normalized": "*******"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "00d64638e4f0703a00ab7fc2c8ae5f75f3b4020f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/00d64638e4f0703a00ab7fc2c8ae5f75f3b4020f", "type": "zip", "shasum": "", "reference": "00d64638e4f0703a00ab7fc2c8ae5f75f3b4020f"}, "time": "2018-07-10T11:02:47+00:00"}, {"version": "v4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2391ed210a239868e7256eb6921b1bd83f3087b5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2391ed210a239868e7256eb6921b1bd83f3087b5", "type": "zip", "shasum": "", "reference": "2391ed210a239868e7256eb6921b1bd83f3087b5"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2018-04-06T07:35:57+00:00"}, {"version": "v4.1.0", "version_normalized": "*******"}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2f3ae32c31b3655597ce1ea9ef09b20e825b16e9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2f3ae32c31b3655597ce1ea9ef09b20e825b16e9", "type": "zip", "shasum": "", "reference": "2f3ae32c31b3655597ce1ea9ef09b20e825b16e9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/4.0"}, "time": "2018-07-26T09:08:35+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "63353a71073faf08f62caab4e6889b06a787f07b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/63353a71073faf08f62caab4e6889b06a787f07b", "type": "zip", "shasum": "", "reference": "63353a71073faf08f62caab4e6889b06a787f07b"}, "time": "2018-04-06T07:35:43+00:00"}, {"version": "v4.0.12", "version_normalized": "********"}, {"version": "v4.0.11", "version_normalized": "********"}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******"}, {"version": "v4.0.8", "version_normalized": "*******"}, {"version": "v4.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "85eaf6a8ec915487abac52e133efc4a268204428"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/85eaf6a8ec915487abac52e133efc4a268204428", "type": "zip", "shasum": "", "reference": "85eaf6a8ec915487abac52e133efc4a268204428"}, "time": "2018-02-14T14:11:10+00:00"}, {"version": "v4.0.6", "version_normalized": "*******"}, {"version": "v4.0.5", "version_normalized": "*******"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "74d33aac36208c4d6757807d9f598f0133a3a4eb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/74d33aac36208c4d6757807d9f598f0133a3a4eb", "type": "zip", "shasum": "", "reference": "74d33aac36208c4d6757807d9f598f0133a3a4eb"}, "time": "2018-01-03T07:38:00+00:00"}, {"version": "v4.0.3", "version_normalized": "*******"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d4face19ed8002eec8280bc1c5ec18130472bf43"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d4face19ed8002eec8280bc1c5ec18130472bf43", "type": "zip", "shasum": "", "reference": "d4face19ed8002eec8280bc1c5ec18130472bf43"}, "time": "2017-12-14T19:48:22+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6223fb2b68e7059e8d5843c0103999a84e7275cf"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6223fb2b68e7059e8d5843c0103999a84e7275cf", "type": "zip", "shasum": "", "reference": "6223fb2b68e7059e8d5843c0103999a84e7275cf"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2017-11-09T17:30:28+00:00"}, {"version": "v4.0.0", "version_normalized": "*******"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0528d15f9ec51c3f464b8a05b0357eedc243be51"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0528d15f9ec51c3f464b8a05b0357eedc243be51", "type": "zip", "shasum": "", "reference": "0528d15f9ec51c3f464b8a05b0357eedc243be51"}, "time": "2017-11-05T16:26:21+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "8e047168f4b63290138194aa8ec884538f8c6f9a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e047168f4b63290138194aa8ec884538f8c6f9a", "type": "zip", "shasum": "", "reference": "8e047168f4b63290138194aa8ec884538f8c6f9a"}, "time": "2017-10-24T14:16:56+00:00"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "064cb53204a8197d882c7d078bdabd2e9b706c2d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/064cb53204a8197d882c7d078bdabd2e9b706c2d", "type": "zip", "shasum": "", "reference": "064cb53204a8197d882c7d078bdabd2e9b706c2d"}, "time": "2017-10-10T11:05:33+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "31fde73757b6bad247c54597beef974919ec6860"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/31fde73757b6bad247c54597beef974919ec6860", "type": "zip", "shasum": "", "reference": "31fde73757b6bad247c54597beef974919ec6860"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00", "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/debug": "~3.4|~4.4", "symfony/stopwatch": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.46"}}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0bb9ea263b39fce3a12ac9f78ef576bdd80dacb8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0bb9ea263b39fce3a12ac9f78ef576bdd80dacb8", "type": "zip", "shasum": "", "reference": "0bb9ea263b39fce3a12ac9f78ef576bdd80dacb8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.45"}, "time": "2020-09-18T12:06:50+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a0f6858fbf7a524747e87a4c336cab7d0b67c802"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a0f6858fbf7a524747e87a4c336cab7d0b67c802", "type": "zip", "shasum": "", "reference": "a0f6858fbf7a524747e87a4c336cab7d0b67c802"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.44"}, "time": "2020-08-12T14:55:37+00:00", "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}}, {"version": "v3.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "14d978f8e8555f2de719c00eb65376be7d2e9081"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/14d978f8e8555f2de719c00eb65376be7d2e9081", "type": "zip", "shasum": "", "reference": "14d978f8e8555f2de719c00eb65376be7d2e9081"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.43"}, "time": "2020-05-05T15:06:23+00:00"}, {"version": "v3.4.42", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}}, {"version": "v3.4.41", "version_normalized": "********"}, {"version": "v3.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9d4e22943b73acc1ba50595b7de1a01fe9dbad48"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9d4e22943b73acc1ba50595b7de1a01fe9dbad48", "type": "zip", "shasum": "", "reference": "9d4e22943b73acc1ba50595b7de1a01fe9dbad48"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.40"}, "time": "2020-03-15T09:38:08+00:00"}, {"version": "v3.4.39", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}}, {"version": "v3.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2f67a869aef3eecf42e7f8be4a8b86c92308686c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2f67a869aef3eecf42e7f8be4a8b86c92308686c", "type": "zip", "shasum": "", "reference": "2f67a869aef3eecf42e7f8be4a8b86c92308686c"}, "funding": [], "time": "2020-02-04T08:04:52+00:00"}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "79ede8f2836e5ec910ebb325bde40f987244baa8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/79ede8f2836e5ec910ebb325bde40f987244baa8", "type": "zip", "shasum": "", "reference": "79ede8f2836e5ec910ebb325bde40f987244baa8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.37"}, "time": "2020-01-04T12:05:51+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f9031c22ec127d4a2450760f81a8677fe8a10177"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f9031c22ec127d4a2450760f81a8677fe8a10177", "type": "zip", "shasum": "", "reference": "f9031c22ec127d4a2450760f81a8677fe8a10177"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}, "time": "2019-10-24T15:33:53+00:00"}, {"version": "v3.4.35", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.35"}}, {"version": "v3.4.34", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}}, {"version": "v3.4.33", "version_normalized": "********"}, {"version": "v3.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3e922c4c3430b9de624e8a285dada5e61e230959"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3e922c4c3430b9de624e8a285dada5e61e230959", "type": "zip", "shasum": "", "reference": "3e922c4c3430b9de624e8a285dada5e61e230959"}, "time": "2019-08-23T08:05:57+00:00"}, {"version": "v3.4.31", "version_normalized": "********"}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f18fdd6cc7006441865e698420cee26bac94741f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f18fdd6cc7006441865e698420cee26bac94741f", "type": "zip", "shasum": "", "reference": "f18fdd6cc7006441865e698420cee26bac94741f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.29"}, "time": "2019-06-25T07:45:31+00:00"}, {"version": "v3.4.29", "version_normalized": "********"}, {"version": "v3.4.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a088aafcefb4eef2520a290ed82e4374092a6dff"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a088aafcefb4eef2520a290ed82e4374092a6dff", "type": "zip", "shasum": "", "reference": "a088aafcefb4eef2520a290ed82e4374092a6dff"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}, "time": "2019-04-02T08:51:52+00:00"}, {"version": "v3.4.27", "version_normalized": "********"}, {"version": "v3.4.26", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.24"}}, {"version": "v3.4.25", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}}, {"version": "v3.4.24", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v3.4.24"}}, {"version": "v3.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ec625e2fff7f584eeb91754821807317b2e79236"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ec625e2fff7f584eeb91754821807317b2e79236", "type": "zip", "shasum": "", "reference": "ec625e2fff7f584eeb91754821807317b2e79236"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.4"}, "time": "2019-02-23T15:06:07+00:00"}, {"version": "v3.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ed5be1663fa66623b3a7004d5d51a14c4045399b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ed5be1663fa66623b3a7004d5d51a14c4045399b", "type": "zip", "shasum": "", "reference": "ed5be1663fa66623b3a7004d5d51a14c4045399b"}, "time": "2019-01-16T13:27:11+00:00"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d1cdd46c53c264a2bd42505bd0e8ce21423bd0e2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d1cdd46c53c264a2bd42505bd0e8ce21423bd0e2", "type": "zip", "shasum": "", "reference": "d1cdd46c53c264a2bd42505bd0e8ce21423bd0e2"}, "time": "2019-01-01T18:08:36+00:00"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "cc35e84adbb15c26ae6868e1efbf705a917be6b5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cc35e84adbb15c26ae6868e1efbf705a917be6b5", "type": "zip", "shasum": "", "reference": "cc35e84adbb15c26ae6868e1efbf705a917be6b5"}, "time": "2018-11-30T18:07:24+00:00"}, {"version": "v3.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d365fc4416ec4980825873962ea5d1b1bca46f1a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d365fc4416ec4980825873962ea5d1b1bca46f1a", "type": "zip", "shasum": "", "reference": "d365fc4416ec4980825873962ea5d1b1bca46f1a"}, "time": "2018-11-26T10:17:44+00:00"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "db9e829c8f34c3d35cf37fcd4cdb4293bc4a2f14"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/db9e829c8f34c3d35cf37fcd4cdb4293bc4a2f14", "type": "zip", "shasum": "", "reference": "db9e829c8f34c3d35cf37fcd4cdb4293bc4a2f14"}, "time": "2018-10-30T16:50:50+00:00"}, {"version": "v3.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b2e1f19280c09a42dc64c0b72b80fe44dd6e88fb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b2e1f19280c09a42dc64c0b72b80fe44dd6e88fb", "type": "zip", "shasum": "", "reference": "b2e1f19280c09a42dc64c0b72b80fe44dd6e88fb"}, "time": "2018-07-26T09:06:28+00:00"}, {"version": "v3.4.16", "version_normalized": "********"}, {"version": "v3.4.15", "version_normalized": "********"}, {"version": "v3.4.14", "version_normalized": "********"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "fdd5abcebd1061ec647089c6c41a07ed60af09f8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/fdd5abcebd1061ec647089c6c41a07ed60af09f8", "type": "zip", "shasum": "", "reference": "fdd5abcebd1061ec647089c6c41a07ed60af09f8"}, "time": "2018-04-06T07:35:25+00:00"}, {"version": "v3.4.12", "version_normalized": "********"}, {"version": "v3.4.11", "version_normalized": "********"}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******"}, {"version": "v3.4.8", "version_normalized": "*******"}, {"version": "v3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "58990682ac3fdc1f563b7e705452921372aad11d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/58990682ac3fdc1f563b7e705452921372aad11d", "type": "zip", "shasum": "", "reference": "58990682ac3fdc1f563b7e705452921372aad11d"}, "time": "2018-02-14T10:03:57+00:00"}, {"version": "v3.4.6", "version_normalized": "*******"}, {"version": "v3.4.5", "version_normalized": "*******"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "26b87b6bca8f8f797331a30b76fdae5342dc26ca"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/26b87b6bca8f8f797331a30b76fdae5342dc26ca", "type": "zip", "shasum": "", "reference": "26b87b6bca8f8f797331a30b76fdae5342dc26ca"}, "time": "2018-01-03T07:37:34+00:00"}, {"version": "v3.4.3", "version_normalized": "*******"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b869cbf8a15ca6261689de2c28a7d7f2d0706835"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b869cbf8a15ca6261689de2c28a7d7f2d0706835", "type": "zip", "shasum": "", "reference": "b869cbf8a15ca6261689de2c28a7d7f2d0706835"}, "time": "2017-12-14T19:40:10+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ca20b8f9ef149f40ff656d52965f240d85f7a8e4"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ca20b8f9ef149f40ff656d52965f240d85f7a8e4", "type": "zip", "shasum": "", "reference": "ca20b8f9ef149f40ff656d52965f240d85f7a8e4"}, "time": "2017-11-09T14:14:31+00:00"}, {"version": "v3.4.0", "version_normalized": "*******"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "10d9a1b5dbf23dfaa38737b69a09f8882821f666"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/10d9a1b5dbf23dfaa38737b69a09f8882821f666", "type": "zip", "shasum": "", "reference": "10d9a1b5dbf23dfaa38737b69a09f8882821f666"}, "time": "2017-11-05T16:10:10+00:00"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "494b359278b7f3a88527f40deda3227df301330a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/494b359278b7f3a88527f40deda3227df301330a", "type": "zip", "shasum": "", "reference": "494b359278b7f3a88527f40deda3227df301330a"}, "time": "2017-10-24T14:12:06+00:00"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0f8e4151b4a471df3efe1c18c95d10500dde7591"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0f8e4151b4a471df3efe1c18c95d10500dde7591", "type": "zip", "shasum": "", "reference": "0f8e4151b4a471df3efe1c18c95d10500dde7591"}, "time": "2017-10-05T10:20:28+00:00"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5c17d24e049437c22d9f670eeabb631f3d966379"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5c17d24e049437c22d9f670eeabb631f3d966379", "type": "zip", "shasum": "", "reference": "5c17d24e049437c22d9f670eeabb631f3d966379"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.3"}, "time": "2018-01-03T07:37:11+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "require-dev": {"symfony/dependency-injection": "~3.3", "symfony/expression-language": "~2.8|~3.0", "symfony/config": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0", "psr/log": "~1.0"}}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "271d8c27c3ec5ecee6e2ac06016232e249d638d9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/271d8c27c3ec5ecee6e2ac06016232e249d638d9", "type": "zip", "shasum": "", "reference": "271d8c27c3ec5ecee6e2ac06016232e249d638d9"}, "time": "2017-11-05T15:47:03+00:00"}, {"version": "v3.3.13", "version_normalized": "********"}, {"version": "v3.3.12", "version_normalized": "********"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d7ba037e4b8221956ab1e221c73c9e27e05dd423"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d7ba037e4b8221956ab1e221c73c9e27e05dd423", "type": "zip", "shasum": "", "reference": "d7ba037e4b8221956ab1e221c73c9e27e05dd423"}, "time": "2017-10-02T06:42:24+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "54ca9520a00386f83bca145819ad3b619aaa2485"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/54ca9520a00386f83bca145819ad3b619aaa2485", "type": "zip", "shasum": "", "reference": "54ca9520a00386f83bca145819ad3b619aaa2485"}, "time": "2017-07-29T21:54:42+00:00"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "67535f1e3fd662bdc68d7ba317c93eecd973617e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/67535f1e3fd662bdc68d7ba317c93eecd973617e", "type": "zip", "shasum": "", "reference": "67535f1e3fd662bdc68d7ba317c93eecd973617e"}, "time": "2017-06-09T14:53:08+00:00", "require": {"php": ">=5.5.9"}}, {"version": "v3.3.5", "version_normalized": "*******"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4054a102470665451108f9b59305c79176ef98f0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4054a102470665451108f9b59305c79176ef98f0", "type": "zip", "shasum": "", "reference": "4054a102470665451108f9b59305c79176ef98f0"}, "time": "2017-06-04T18:15:29+00:00"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a9f8b02b0ef07302eca92cd4bba73200b7980e9c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a9f8b02b0ef07302eca92cd4bba73200b7980e9c", "type": "zip", "shasum": "", "reference": "a9f8b02b0ef07302eca92cd4bba73200b7980e9c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2017-05-04T12:23:07+00:00"}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "145ec57e6d74270696e85a95e632227e8bde2faf"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/145ec57e6d74270696e85a95e632227e8bde2faf", "type": "zip", "shasum": "", "reference": "145ec57e6d74270696e85a95e632227e8bde2faf"}, "time": "2017-05-01T15:01:29+00:00"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b8de6ee252af19330dd72ad5fc0dd4658a1d6325"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b8de6ee252af19330dd72ad5fc0dd4658a1d6325", "type": "zip", "shasum": "", "reference": "b8de6ee252af19330dd72ad5fc0dd4658a1d6325"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.2"}, "time": "2017-06-02T08:26:05+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require-dev": {"symfony/dependency-injection": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/config": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0", "psr/log": "~1.0"}, "conflict": "__unset"}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9cde4022164ade1aeed083ce399fc9fbb57e5846"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9cde4022164ade1aeed083ce399fc9fbb57e5846", "type": "zip", "shasum": "", "reference": "9cde4022164ade1aeed083ce399fc9fbb57e5846"}, "time": "2017-05-01T18:34:19+00:00"}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b8a401f733b43251e1d088c589368b2a94155e40"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b8a401f733b43251e1d088c589368b2a94155e40", "type": "zip", "shasum": "", "reference": "b8a401f733b43251e1d088c589368b2a94155e40"}, "time": "2017-05-01T14:58:48+00:00"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "154bb1ef7b0e42ccc792bd53edbce18ed73440ca"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/154bb1ef7b0e42ccc792bd53edbce18ed73440ca", "type": "zip", "shasum": "", "reference": "154bb1ef7b0e42ccc792bd53edbce18ed73440ca"}, "time": "2017-04-04T07:26:27+00:00"}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b7a1b9e0a0f623ce43b4c8d775eb138f190c9d8d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b7a1b9e0a0f623ce43b4c8d775eb138f190c9d8d", "type": "zip", "shasum": "", "reference": "b7a1b9e0a0f623ce43b4c8d775eb138f190c9d8d"}, "time": "2017-02-21T09:12:04+00:00"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9137eb3a3328e413212826d63eeeb0217836e2b6"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9137eb3a3328e413212826d63eeeb0217836e2b6", "type": "zip", "shasum": "", "reference": "9137eb3a3328e413212826d63eeeb0217836e2b6"}, "time": "2017-01-02T20:32:22+00:00"}, {"version": "v3.2.3", "version_normalized": "*******"}, {"version": "v3.2.2", "version_normalized": "*******"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e8f47a327c2f0fd5aa04fa60af2b693006ed7283"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e8f47a327c2f0fd5aa04fa60af2b693006ed7283", "type": "zip", "shasum": "", "reference": "e8f47a327c2f0fd5aa04fa60af2b693006ed7283"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2016-10-13T06:29:04+00:00"}, {"version": "v3.2.0", "version_normalized": "*******"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "652fe5441bd5699654662e11defa73e779eacca1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/652fe5441bd5699654662e11defa73e779eacca1", "type": "zip", "shasum": "", "reference": "652fe5441bd5699654662e11defa73e779eacca1"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.1"}, "time": "2017-01-02T20:31:54+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}, {"version": "v3.1.9", "version_normalized": "*******"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "28b0832b2553ffb80cabef6a7a812ff1e670c0bc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/28b0832b2553ffb80cabef6a7a812ff1e670c0bc", "type": "zip", "shasum": "", "reference": "28b0832b2553ffb80cabef6a7a812ff1e670c0bc"}, "time": "2016-10-13T06:28:43+00:00"}, {"version": "v3.1.7", "version_normalized": "*******"}, {"version": "v3.1.6", "version_normalized": "*******"}, {"version": "v3.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c0c00c80b3a69132c4e55c3e7db32b4a387615e5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c0c00c80b3a69132c4e55c3e7db32b4a387615e5", "type": "zip", "shasum": "", "reference": "c0c00c80b3a69132c4e55c3e7db32b4a387615e5"}, "time": "2016-07-19T10:45:57+00:00"}, {"version": "v3.1.4", "version_normalized": "*******"}, {"version": "v3.1.3", "version_normalized": "*******"}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7f9839ede2070f53e7e2f0849b9bd14748c434c5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7f9839ede2070f53e7e2f0849b9bd14748c434c5", "type": "zip", "shasum": "", "reference": "7f9839ede2070f53e7e2f0849b9bd14748c434c5"}, "time": "2016-06-29T05:41:56+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f5b7563f67779c6d3d5370e23448e707c858df3e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f5b7563f67779c6d3d5370e23448e707c858df3e", "type": "zip", "shasum": "", "reference": "f5b7563f67779c6d3d5370e23448e707c858df3e"}, "time": "2016-06-06T11:42:41+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0343b2cedd0edb26cdc791212a8eb645c406018b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0343b2cedd0edb26cdc791212a8eb645c406018b", "type": "zip", "shasum": "", "reference": "0343b2cedd0edb26cdc791212a8eb645c406018b"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2016-04-12T18:27:47+00:00"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "54da3ff63dec3c9c0e32ec3f95a7d94ef64baa00"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/54da3ff63dec3c9c0e32ec3f95a7d94ef64baa00", "type": "zip", "shasum": "", "reference": "54da3ff63dec3c9c0e32ec3f95a7d94ef64baa00"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/3.0"}, "time": "2016-07-19T10:44:15+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "43e9a14f71db135da5c75dd5d383ecff6ada920e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/43e9a14f71db135da5c75dd5d383ecff6ada920e", "type": "zip", "shasum": "", "reference": "43e9a14f71db135da5c75dd5d383ecff6ada920e"}, "time": "2016-06-29T05:40:00+00:00"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2bd63cc8b94882084565d2ec3fae5b8d6878398b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2bd63cc8b94882084565d2ec3fae5b8d6878398b", "type": "zip", "shasum": "", "reference": "2bd63cc8b94882084565d2ec3fae5b8d6878398b"}, "time": "2016-06-06T11:33:26+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "807dde98589f9b2b00624dca326740380d78dbbc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/807dde98589f9b2b00624dca326740380d78dbbc", "type": "zip", "shasum": "", "reference": "807dde98589f9b2b00624dca326740380d78dbbc"}, "time": "2016-05-05T06:56:13+00:00"}, {"version": "v3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "17b04e6b1ede45b57d3ad5146abe50df6c3968b4"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/17b04e6b1ede45b57d3ad5146abe50df6c3968b4", "type": "zip", "shasum": "", "reference": "17b04e6b1ede45b57d3ad5146abe50df6c3968b4"}, "time": "2016-04-12T18:09:53+00:00"}, {"version": "v3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9002dcf018d884d294b1ef20a6f968efc1128f39"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9002dcf018d884d294b1ef20a6f968efc1128f39", "type": "zip", "shasum": "", "reference": "9002dcf018d884d294b1ef20a6f968efc1128f39"}, "time": "2016-03-10T10:34:12+00:00"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4dd5df31a28c0f82b41cb1e1599b74b5dcdbdafa"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4dd5df31a28c0f82b41cb1e1599b74b5dcdbdafa", "type": "zip", "shasum": "", "reference": "4dd5df31a28c0f82b41cb1e1599b74b5dcdbdafa"}, "time": "2016-01-27T05:14:46+00:00"}, {"version": "v3.0.2", "version_normalized": "*******"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d36355e026905fa5229e1ed7b4e9eda2e67adfcf"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d36355e026905fa5229e1ed7b4e9eda2e67adfcf", "type": "zip", "shasum": "", "reference": "d36355e026905fa5229e1ed7b4e9eda2e67adfcf"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/master"}, "time": "2015-10-30T23:35:59+00:00"}, {"version": "v3.0.0", "version_normalized": "*******"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.8.52", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a77e974a5fecb4398833b0709210e3d5e334ffb0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a77e974a5fecb4398833b0709210e3d5e334ffb0", "type": "zip", "shasum": "", "reference": "a77e974a5fecb4398833b0709210e3d5e334ffb0"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.8.50"}, "time": "2018-11-21T14:20:20+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9"}, "require-dev": {"symfony/dependency-injection": "~2.6|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/config": "^2.0.5|~3.0.0", "symfony/stopwatch": "~2.3|~3.0.0", "psr/log": "~1.0"}}, {"version": "v2.8.50", "version_normalized": "********"}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "76494bc38ff38d90d01913d23b5271acd4d78dd3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/76494bc38ff38d90d01913d23b5271acd4d78dd3", "type": "zip", "shasum": "", "reference": "76494bc38ff38d90d01913d23b5271acd4d78dd3"}, "time": "2018-10-20T23:16:31+00:00"}, {"version": "v2.8.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "84ae343f39947aa084426ed1138bb96bf94d1f12"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/84ae343f39947aa084426ed1138bb96bf94d1f12", "type": "zip", "shasum": "", "reference": "84ae343f39947aa084426ed1138bb96bf94d1f12"}, "time": "2018-07-26T09:03:18+00:00"}, {"version": "v2.8.45", "version_normalized": "********"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9b69aad7d4c086dc94ebade2d5eb9145da5dac8c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9b69aad7d4c086dc94ebade2d5eb9145da5dac8c", "type": "zip", "shasum": "", "reference": "9b69aad7d4c086dc94ebade2d5eb9145da5dac8c"}, "time": "2018-04-06T07:35:03+00:00"}, {"version": "v2.8.42", "version_normalized": "********"}, {"version": "v2.8.41", "version_normalized": "********"}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********"}, {"version": "v2.8.38", "version_normalized": "********"}, {"version": "v2.8.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f5d2d7dcc33b89e20c2696ea9afcbddf6540081c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f5d2d7dcc33b89e20c2696ea9afcbddf6540081c", "type": "zip", "shasum": "", "reference": "f5d2d7dcc33b89e20c2696ea9afcbddf6540081c"}, "time": "2018-02-11T16:53:59+00:00"}, {"version": "v2.8.36", "version_normalized": "********"}, {"version": "v2.8.35", "version_normalized": "********"}, {"version": "v2.8.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d64be24fc1eba62f9daace8a8918f797fc8e87cc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d64be24fc1eba62f9daace8a8918f797fc8e87cc", "type": "zip", "shasum": "", "reference": "d64be24fc1eba62f9daace8a8918f797fc8e87cc"}, "time": "2018-01-03T07:36:31+00:00"}, {"version": "v2.8.33", "version_normalized": "********"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b59aacf238fadda50d612c9de73b74751872a903"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b59aacf238fadda50d612c9de73b74751872a903", "type": "zip", "shasum": "", "reference": "b59aacf238fadda50d612c9de73b74751872a903"}, "time": "2017-11-05T15:25:56+00:00"}, {"version": "v2.8.31", "version_normalized": "********"}, {"version": "v2.8.30", "version_normalized": "********"}, {"version": "v2.8.29", "version_normalized": "********"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7fe089232554357efb8d4af65ce209fc6e5a2186"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7fe089232554357efb8d4af65ce209fc6e5a2186", "type": "zip", "shasum": "", "reference": "7fe089232554357efb8d4af65ce209fc6e5a2186"}, "time": "2017-10-01T21:00:16+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "1377400fd641d7d1935981546aaef780ecd5bf6d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1377400fd641d7d1935981546aaef780ecd5bf6d", "type": "zip", "shasum": "", "reference": "1377400fd641d7d1935981546aaef780ecd5bf6d"}, "time": "2017-06-02T07:47:27+00:00"}, {"version": "v2.8.26", "version_normalized": "********"}, {"version": "v2.8.25", "version_normalized": "********"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********"}, {"version": "v2.8.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7fc8e2b4118ff316550596357325dfd92a51f531"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7fc8e2b4118ff316550596357325dfd92a51f531", "type": "zip", "shasum": "", "reference": "7fc8e2b4118ff316550596357325dfd92a51f531"}, "time": "2017-04-26T16:56:54+00:00"}, {"version": "v2.8.20", "version_normalized": "********"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "88b65f0ac25355090e524aba4ceb066025df8bd2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/88b65f0ac25355090e524aba4ceb066025df8bd2", "type": "zip", "shasum": "", "reference": "88b65f0ac25355090e524aba4ceb066025df8bd2"}, "time": "2017-04-03T20:37:06+00:00"}, {"version": "v2.8.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bb4ec47e8e109c1c1172145732d0aa468d967cd0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bb4ec47e8e109c1c1172145732d0aa468d967cd0", "type": "zip", "shasum": "", "reference": "bb4ec47e8e109c1c1172145732d0aa468d967cd0"}, "time": "2017-02-21T08:33:48+00:00"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "74877977f90fb9c3e46378d5764217c55f32df34"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/74877977f90fb9c3e46378d5764217c55f32df34", "type": "zip", "shasum": "", "reference": "74877977f90fb9c3e46378d5764217c55f32df34"}, "time": "2017-01-02T20:30:24+00:00", "require-dev": {"symfony/dependency-injection": "~2.6|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/config": "~2.0,>=2.0.5|~3.0.0", "symfony/stopwatch": "~2.3|~3.0.0", "psr/log": "~1.0"}}, {"version": "v2.8.16", "version_normalized": "********"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "25c576abd4e0f212e678fe8b2bd9a9a98c7ea934"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/25c576abd4e0f212e678fe8b2bd9a9a98c7ea934", "type": "zip", "shasum": "", "reference": "25c576abd4e0f212e678fe8b2bd9a9a98c7ea934"}, "time": "2016-10-13T01:43:15+00:00"}, {"version": "v2.8.14", "version_normalized": "********"}, {"version": "v2.8.13", "version_normalized": "********"}, {"version": "v2.8.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "889983a79a043dfda68f38c38b6dba092dd49cd8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/889983a79a043dfda68f38c38b6dba092dd49cd8", "type": "zip", "shasum": "", "reference": "889983a79a043dfda68f38c38b6dba092dd49cd8"}, "time": "2016-07-28T16:56:28+00:00"}, {"version": "v2.8.11", "version_normalized": "********"}, {"version": "v2.8.10", "version_normalized": "********"}, {"version": "v2.8.9", "version_normalized": "*******"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b180b70439dca70049b6b9b7e21d75e6e5d7aca9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b180b70439dca70049b6b9b7e21d75e6e5d7aca9", "type": "zip", "shasum": "", "reference": "b180b70439dca70049b6b9b7e21d75e6e5d7aca9"}, "time": "2016-06-29T05:29:29+00:00"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2a6b8713f8bdb582058cfda463527f195b066110"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2a6b8713f8bdb582058cfda463527f195b066110", "type": "zip", "shasum": "", "reference": "2a6b8713f8bdb582058cfda463527f195b066110"}, "time": "2016-06-06T11:11:27+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a158f13992a3147d466af7a23b564ac719a4ddd8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a158f13992a3147d466af7a23b564ac719a4ddd8", "type": "zip", "shasum": "", "reference": "a158f13992a3147d466af7a23b564ac719a4ddd8"}, "time": "2016-05-03T18:59:18+00:00"}, {"version": "v2.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "81c4c51f7fd6d0d40961bd53dd60cade32db6ed6"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/81c4c51f7fd6d0d40961bd53dd60cade32db6ed6", "type": "zip", "shasum": "", "reference": "81c4c51f7fd6d0d40961bd53dd60cade32db6ed6"}, "time": "2016-04-05T16:36:54+00:00"}, {"version": "v2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "47d2d8cade9b1c3987573d2943bb9352536cdb87"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/47d2d8cade9b1c3987573d2943bb9352536cdb87", "type": "zip", "shasum": "", "reference": "47d2d8cade9b1c3987573d2943bb9352536cdb87"}, "time": "2016-03-07T14:04:32+00:00"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "78c468665c9568c3faaa9c416a7134308f2d85c3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/78c468665c9568c3faaa9c416a7134308f2d85c3", "type": "zip", "shasum": "", "reference": "78c468665c9568c3faaa9c416a7134308f2d85c3"}, "time": "2016-01-27T05:14:19+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ee278f7c851533e58ca307f66305ccb9188aceda"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ee278f7c851533e58ca307f66305ccb9188aceda", "type": "zip", "shasum": "", "reference": "ee278f7c851533e58ca307f66305ccb9188aceda"}, "time": "2016-01-13T10:28:07+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a5eb815363c0388e83247e7e9853e5dbc14999cc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a5eb815363c0388e83247e7e9853e5dbc14999cc", "type": "zip", "shasum": "", "reference": "a5eb815363c0388e83247e7e9853e5dbc14999cc"}, "time": "2015-10-30T20:15:42+00:00"}, {"version": "v2.8.0", "version_normalized": "*******"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "04dc8ab78c0a755c0f6074a48066c459500b653a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/04dc8ab78c0a755c0f6074a48066c459500b653a", "type": "zip", "shasum": "", "reference": "04dc8ab78c0a755c0f6074a48066c459500b653a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.7.51"}, "time": "2018-04-05T14:23:45+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require-dev": {"symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/config": "^2.0.5", "symfony/stopwatch": "~2.3", "psr/log": "~1.0"}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "********"}, {"version": "v2.7.47", "version_normalized": "********"}, {"version": "v2.7.46", "version_normalized": "********"}, {"version": "v2.7.45", "version_normalized": "********"}, {"version": "v2.7.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "aac14321ed6cdb3bdf361af2c15e11a5f026d9f8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/aac14321ed6cdb3bdf361af2c15e11a5f026d9f8", "type": "zip", "shasum": "", "reference": "aac14321ed6cdb3bdf361af2c15e11a5f026d9f8"}, "time": "2018-02-08T08:15:30+00:00"}, {"version": "v2.7.43", "version_normalized": "********"}, {"version": "v2.7.42", "version_normalized": "********"}, {"version": "v2.7.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "12b1bce8cdefa9e1dcd80527bcb67386c8350701"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/12b1bce8cdefa9e1dcd80527bcb67386c8350701", "type": "zip", "shasum": "", "reference": "12b1bce8cdefa9e1dcd80527bcb67386c8350701"}, "time": "2018-01-03T07:23:28+00:00"}, {"version": "v2.7.40", "version_normalized": "********"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "10c217c902d65365c52dd853f262fd9574b29aaf"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/10c217c902d65365c52dd853f262fd9574b29aaf", "type": "zip", "shasum": "", "reference": "10c217c902d65365c52dd853f262fd9574b29aaf"}, "time": "2017-10-29T09:49:53+00:00"}, {"version": "v2.7.38", "version_normalized": "********"}, {"version": "v2.7.37", "version_normalized": "********"}, {"version": "v2.7.36", "version_normalized": "********"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "26ebb4640243ebc417cfb895ca62bac02d6c53ea"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/26ebb4640243ebc417cfb895ca62bac02d6c53ea", "type": "zip", "shasum": "", "reference": "26ebb4640243ebc417cfb895ca62bac02d6c53ea"}, "time": "2017-09-30T14:00:25+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bd2f6d73a0234e271fa06f9f5f523da290e1d60c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bd2f6d73a0234e271fa06f9f5f523da290e1d60c", "type": "zip", "shasum": "", "reference": "bd2f6d73a0234e271fa06f9f5f523da290e1d60c"}, "time": "2017-06-01T21:49:12+00:00"}, {"version": "v2.7.33", "version_normalized": "********"}, {"version": "v2.7.32", "version_normalized": "********"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********"}, {"version": "v2.7.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2bc450c3a061cbd717b6786f5cde31004a460a82"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2bc450c3a061cbd717b6786f5cde31004a460a82", "type": "zip", "shasum": "", "reference": "2bc450c3a061cbd717b6786f5cde31004a460a82"}, "time": "2017-04-12T07:39:27+00:00"}, {"version": "v2.7.27", "version_normalized": "********"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9eb609d686a1a32e34231771099d6e61ca00d1a6"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9eb609d686a1a32e34231771099d6e61ca00d1a6", "type": "zip", "shasum": "", "reference": "9eb609d686a1a32e34231771099d6e61ca00d1a6"}, "time": "2017-03-31T22:21:40+00:00"}, {"version": "v2.7.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c1b7824f138b2dea858db1916848b88f1dedb5b9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c1b7824f138b2dea858db1916848b88f1dedb5b9", "type": "zip", "shasum": "", "reference": "c1b7824f138b2dea858db1916848b88f1dedb5b9"}, "time": "2017-02-21T08:32:25+00:00"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "205fd73ec9b24c22e07728279691d47ae79f9fe2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/205fd73ec9b24c22e07728279691d47ae79f9fe2", "type": "zip", "shasum": "", "reference": "205fd73ec9b24c22e07728279691d47ae79f9fe2"}, "time": "2017-01-02T20:30:00+00:00", "require-dev": {"symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/config": "~2.0,>=2.0.5", "symfony/stopwatch": "~2.3", "psr/log": "~1.0"}}, {"version": "v2.7.23", "version_normalized": "********"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "798ece958a575f654b42e61922efd3fadbf31689"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/798ece958a575f654b42e61922efd3fadbf31689", "type": "zip", "shasum": "", "reference": "798ece958a575f654b42e61922efd3fadbf31689"}, "time": "2016-11-03T07:49:30+00:00"}, {"version": "v2.7.21", "version_normalized": "********"}, {"version": "v2.7.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2b096845cbe49f4616dc90494e1a68d48e9bba23"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2b096845cbe49f4616dc90494e1a68d48e9bba23", "type": "zip", "shasum": "", "reference": "2b096845cbe49f4616dc90494e1a68d48e9bba23"}, "time": "2016-08-09T09:00:18+00:00"}, {"version": "v2.7.19", "version_normalized": "********"}, {"version": "v2.7.18", "version_normalized": "********"}, {"version": "v2.7.17", "version_normalized": "********"}, {"version": "v2.7.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "633a3b9755b8446e3bb2330ba17fbf42b58979eb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/633a3b9755b8446e3bb2330ba17fbf42b58979eb", "type": "zip", "shasum": "", "reference": "633a3b9755b8446e3bb2330ba17fbf42b58979eb"}, "time": "2016-07-28T07:50:15+00:00"}, {"version": "v2.7.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9397f2ac2fd1ef5534d140035bd18b44fe2fcb92"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9397f2ac2fd1ef5534d140035bd18b44fe2fcb92", "type": "zip", "shasum": "", "reference": "9397f2ac2fd1ef5534d140035bd18b44fe2fcb92"}, "time": "2016-06-28T06:24:06+00:00"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d3e09ed1224503791f31b913d22196f65f9afed5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d3e09ed1224503791f31b913d22196f65f9afed5", "type": "zip", "shasum": "", "reference": "d3e09ed1224503791f31b913d22196f65f9afed5"}, "time": "2016-06-06T11:03:51+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e86b8282381f4fa7732f3847e152c01a32d721d7"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e86b8282381f4fa7732f3847e152c01a32d721d7", "type": "zip", "shasum": "", "reference": "e86b8282381f4fa7732f3847e152c01a32d721d7"}, "time": "2016-04-04T17:08:16+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b7c955d4a2fa79fca9caccdaaa5434e3799e3b51"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b7c955d4a2fa79fca9caccdaaa5434e3799e3b51", "type": "zip", "shasum": "", "reference": "b7c955d4a2fa79fca9caccdaaa5434e3799e3b51"}, "time": "2016-03-06T17:01:13+00:00"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b68c0348ba5b3927a6c8e413cc7d594f3ccff3ce"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b68c0348ba5b3927a6c8e413cc7d594f3ccff3ce", "type": "zip", "shasum": "", "reference": "b68c0348ba5b3927a6c8e413cc7d594f3ccff3ce"}, "time": "2016-01-27T05:09:39+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "79493b6421786e5a0fc2d161410aa86f400bcaea"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/79493b6421786e5a0fc2d161410aa86f400bcaea", "type": "zip", "shasum": "", "reference": "79493b6421786e5a0fc2d161410aa86f400bcaea"}, "time": "2016-01-13T10:26:43+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7e2f9c31645680026c2372edf66f863fc7757af5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7e2f9c31645680026c2372edf66f863fc7757af5", "type": "zip", "shasum": "", "reference": "7e2f9c31645680026c2372edf66f863fc7757af5"}, "time": "2015-10-30T20:10:21+00:00"}, {"version": "v2.7.7", "version_normalized": "*******"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "87a5db5ea887763fa3a31a5471b512ff1596d9b8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/87a5db5ea887763fa3a31a5471b512ff1596d9b8", "type": "zip", "shasum": "", "reference": "87a5db5ea887763fa3a31a5471b512ff1596d9b8"}, "time": "2015-10-11T09:39:48+00:00", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "ae4dcc2a8d3de98bd794167a3ccda1311597c5d9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ae4dcc2a8d3de98bd794167a3ccda1311597c5d9", "type": "zip", "shasum": "", "reference": "ae4dcc2a8d3de98bd794167a3ccda1311597c5d9"}, "time": "2015-09-22T13:49:29+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/config": "~2.0,>=2.0.5", "symfony/stopwatch": "~2.3", "psr/log": "~1.0"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b58c916f1db03a611b72dd702564f30ad8fe83fa"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b58c916f1db03a611b72dd702564f30ad8fe83fa", "type": "zip", "shasum": "", "reference": "b58c916f1db03a611b72dd702564f30ad8fe83fa"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.7.4"}, "time": "2015-08-24T07:13:45+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9310b5f9a87ec2ea75d20fec0b0017c77c66dac3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9310b5f9a87ec2ea75d20fec0b0017c77c66dac3", "type": "zip", "shasum": "", "reference": "9310b5f9a87ec2ea75d20fec0b0017c77c66dac3"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.7.3"}, "time": "2015-06-18T19:21:56+00:00"}, {"version": "v2.7.2", "version_normalized": "*******"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "be3c5ff8d503c46768aeb78ce6333051aa6f26d9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/be3c5ff8d503c46768aeb78ce6333051aa6f26d9", "type": "zip", "shasum": "", "reference": "be3c5ff8d503c46768aeb78ce6333051aa6f26d9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.7.1"}, "time": "2015-06-08T09:37:21+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "687039686d0e923429ba6e958d0baa920cd5d458"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/687039686d0e923429ba6e958d0baa920cd5d458", "type": "zip", "shasum": "", "reference": "687039686d0e923429ba6e958d0baa920cd5d458"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.7.0-BETA2"}, "time": "2015-05-02T15:21:08+00:00"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "28008406abd5385496fb2cb79bdd0f8b8a8bd44d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/28008406abd5385496fb2cb79bdd0f8b8a8bd44d", "type": "zip", "shasum": "", "reference": "28008406abd5385496fb2cb79bdd0f8b8a8bd44d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.7.0-BETA1"}, "time": "2015-04-10T07:23:38+00:00", "autoload": {"psr-0": {"Symfony\\Component\\EventDispatcher\\": ""}}, "target-dir": "Symfony/Component/EventDispatcher"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "672593bc4b0043a0acf91903bb75a1c82d8f2e02"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/672593bc4b0043a0acf91903bb75a1c82d8f2e02", "type": "zip", "shasum": "", "reference": "672593bc4b0043a0acf91903bb75a1c82d8f2e02"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.11"}, "time": "2015-05-02T15:18:45+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********"}, {"version": "v2.6.10", "version_normalized": "********"}, {"version": "v2.6.9", "version_normalized": "*******"}, {"version": "v2.6.8", "version_normalized": "*******"}, {"version": "v2.6.7", "version_normalized": "*******"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "70f7c8478739ad21e3deef0d977b38c77f1fb284"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/70f7c8478739ad21e3deef0d977b38c77f1fb284", "type": "zip", "shasum": "", "reference": "70f7c8478739ad21e3deef0d977b38c77f1fb284"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.6"}, "time": "2015-03-13T17:37:22+00:00"}, {"version": "v2.6.5", "version_normalized": "*******"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f75989f3ab2743a82fe0b03ded2598a2b1546813"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f75989f3ab2743a82fe0b03ded2598a2b1546813", "type": "zip", "shasum": "", "reference": "f75989f3ab2743a82fe0b03ded2598a2b1546813"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.4"}, "time": "2015-02-01T16:10:57+00:00", "require-dev": {"symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/config": "~2.0,>=2.0.5", "symfony/stopwatch": "~2.3", "psr/log": "~1.0"}}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "40ff70cadea3785d83cac1c8309514b36113064e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/40ff70cadea3785d83cac1c8309514b36113064e", "type": "zip", "shasum": "", "reference": "40ff70cadea3785d83cac1c8309514b36113064e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.3"}, "time": "2015-01-05T14:28:40+00:00"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "720fe9bca893df7ad1b4546649473b5afddf0216"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/720fe9bca893df7ad1b4546649473b5afddf0216", "type": "zip", "shasum": "", "reference": "720fe9bca893df7ad1b4546649473b5afddf0216"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.1"}, "time": "2014-12-02T20:19:20+00:00", "require-dev": {"symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/config": "~2.0", "symfony/stopwatch": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "697331d4ac78668cf6d21a2bb009b3faae92814f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/697331d4ac78668cf6d21a2bb009b3faae92814f", "type": "zip", "shasum": "", "reference": "697331d4ac78668cf6d21a2bb009b3faae92814f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.0"}, "time": "2014-11-28T10:00:40+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "0070add44a6d53f764f8254ed6e82078c3b07b14"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0070add44a6d53f764f8254ed6e82078c3b07b14", "type": "zip", "shasum": "", "reference": "0070add44a6d53f764f8254ed6e82078c3b07b14"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.0-BETA2"}, "time": "2014-11-20T12:16:05+00:00"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "dcf345d5ed96bc6c3b4521c1989670d2c9e5014e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/dcf345d5ed96bc6c3b4521c1989670d2c9e5014e", "type": "zip", "shasum": "", "reference": "dcf345d5ed96bc6c3b4521c1989670d2c9e5014e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.6.0-BETA1"}, "time": "2014-11-03T03:55:50+00:00"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "af6eb6a9a1a3b411facfd8e7e3f82a6be7919c04"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/af6eb6a9a1a3b411facfd8e7e3f82a6be7919c04", "type": "zip", "shasum": "", "reference": "af6eb6a9a1a3b411facfd8e7e3f82a6be7919c04"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.12"}, "time": "2015-01-29T18:20:43+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "require-dev": {"symfony/dependency-injection": "~2.0,>=2.0.5,<2.6.0", "symfony/config": "~2.0,>=2.0.5", "symfony/stopwatch": "~2.3", "psr/log": "~1.0"}}, {"version": "v2.5.11", "version_normalized": "********"}, {"version": "v2.5.10", "version_normalized": "********"}, {"version": "v2.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3694afc8bcddabc37e1f1ab76e6fd93e0f187415"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3694afc8bcddabc37e1f1ab76e6fd93e0f187415", "type": "zip", "shasum": "", "reference": "3694afc8bcddabc37e1f1ab76e6fd93e0f187415"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.9"}, "time": "2015-01-05T08:51:41+00:00"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "c0a15f7c45efc6506077c728658c16b579929bf8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c0a15f7c45efc6506077c728658c16b579929bf8", "type": "zip", "shasum": "", "reference": "c0a15f7c45efc6506077c728658c16b579929bf8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.8"}, "time": "2014-12-02T20:15:53+00:00", "require-dev": {"symfony/dependency-injection": "~2.0,<2.6.0", "symfony/config": "~2.0", "symfony/stopwatch": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bb6fc12085cd195dceaf48016087b12b632df497"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bb6fc12085cd195dceaf48016087b12b632df497", "type": "zip", "shasum": "", "reference": "bb6fc12085cd195dceaf48016087b12b632df497"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.7"}, "time": "2014-10-30T20:17:55+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "804eb28dbbfba9ffdab21fe2066744906cea2212"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/804eb28dbbfba9ffdab21fe2066744906cea2212", "type": "zip", "shasum": "", "reference": "804eb28dbbfba9ffdab21fe2066744906cea2212"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.6"}, "time": "2014-10-01T15:43:05+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f6281337bf5f985f585d1db6a83adb05ce531f46"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f6281337bf5f985f585d1db6a83adb05ce531f46", "type": "zip", "shasum": "", "reference": "f6281337bf5f985f585d1db6a83adb05ce531f46"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.5"}, "time": "2014-09-28T15:56:11+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "8faf5cc7e80fde74a650a36e60d32ce3c3e0457b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8faf5cc7e80fde74a650a36e60d32ce3c3e0457b", "type": "zip", "shasum": "", "reference": "8faf5cc7e80fde74a650a36e60d32ce3c3e0457b"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.4"}, "time": "2014-07-28T13:20:46+00:00", "require-dev": {"symfony/dependency-injection": "~2.0", "symfony/config": "~2.0", "symfony/stopwatch": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.5.3", "version_normalized": "*******"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2215d2ef6fd7ab24d55576a3d924df575c741762"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2215d2ef6fd7ab24d55576a3d924df575c741762", "type": "zip", "shasum": "", "reference": "2215d2ef6fd7ab24d55576a3d924df575c741762"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.2"}, "time": "2014-07-09T09:05:48+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "84533bf98da5486b9395a1d95e9184e04e14aad3"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/84533bf98da5486b9395a1d95e9184e04e14aad3", "type": "zip", "shasum": "", "reference": "84533bf98da5486b9395a1d95e9184e04e14aad3"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.1"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "cb62ec8dd05893fc8e4f0e6e21e326e1fc731fe8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cb62ec8dd05893fc8e4f0e6e21e326e1fc731fe8", "type": "zip", "shasum": "", "reference": "cb62ec8dd05893fc8e4f0e6e21e326e1fc731fe8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.0-RC1"}, "time": "2014-04-29T10:13:57+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "133627e57441b50128807702bea0d395129809ad"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/133627e57441b50128807702bea0d395129809ad", "type": "zip", "shasum": "", "reference": "133627e57441b50128807702bea0d395129809ad"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.0-BETA2"}, "time": "2014-04-16T10:36:21+00:00", "require-dev": {"symfony/dependency-injection": "~2.0", "symfony/stopwatch": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4b9a8defce833386224bdbf9045128a6e51fb0b4"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4b9a8defce833386224bdbf9045128a6e51fb0b4", "type": "zip", "shasum": "", "reference": "4b9a8defce833386224bdbf9045128a6e51fb0b4"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.5.0-BETA1"}, "time": "2014-04-03T05:23:50+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7362304b252f9303e037ab47e6330f24d7ebbf50"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7362304b252f9303e037ab47e6330f24d7ebbf50", "type": "zip", "shasum": "", "reference": "7362304b252f9303e037ab47e6330f24d7ebbf50"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.10"}, "time": "2014-09-22T08:51:05+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "require-dev": {"symfony/dependency-injection": "~2.0"}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d52ce15255fef63c71ace3740b6a4c9f21e9edda"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d52ce15255fef63c71ace3740b6a4c9f21e9edda", "type": "zip", "shasum": "", "reference": "d52ce15255fef63c71ace3740b6a4c9f21e9edda"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.9"}, "time": "2014-07-28T13:13:16+00:00"}, {"version": "v2.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6008a029b6a3c0816d58be2ca8923e324cc89d93"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6008a029b6a3c0816d58be2ca8923e324cc89d93", "type": "zip", "shasum": "", "reference": "6008a029b6a3c0816d58be2ca8923e324cc89d93"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.8"}, "time": "2014-07-09T09:04:55+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9ef3a5aaa44ff4effce9f1a4000e480c08b1a611"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9ef3a5aaa44ff4effce9f1a4000e480c08b1a611", "type": "zip", "shasum": "", "reference": "9ef3a5aaa44ff4effce9f1a4000e480c08b1a611"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.7"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e539602e5455aa086c0e81e604745af7789e4d8a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e539602e5455aa086c0e81e604745af7789e4d8a", "type": "zip", "shasum": "", "reference": "e539602e5455aa086c0e81e604745af7789e4d8a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.6"}, "time": "2014-04-16T10:34:31+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "4708b8cd41984a5ba29fe7dd40716f7f761ac501"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/4708b8cd41984a5ba29fe7dd40716f7f761ac501", "type": "zip", "shasum": "", "reference": "4708b8cd41984a5ba29fe7dd40716f7f761ac501"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.3"}, "time": "2014-02-11T13:52:09+00:00"}, {"version": "v2.4.2", "version_normalized": "*******"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e3ba42f6a70554ed05749e61b829550f6ac33601"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e3ba42f6a70554ed05749e61b829550f6ac33601", "type": "zip", "shasum": "", "reference": "e3ba42f6a70554ed05749e61b829550f6ac33601"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.1"}, "time": "2013-12-28T08:12:03+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "acd1707236f6eb96fbb8d58f63d289b72ebc2f6e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/acd1707236f6eb96fbb8d58f63d289b72ebc2f6e", "type": "zip", "shasum": "", "reference": "acd1707236f6eb96fbb8d58f63d289b72ebc2f6e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.0"}, "time": "2013-12-03T14:52:22+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "04a4e9564f5a506c0ec306b0303fe729cedb5326"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/04a4e9564f5a506c0ec306b0303fe729cedb5326", "type": "zip", "shasum": "", "reference": "04a4e9564f5a506c0ec306b0303fe729cedb5326"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.0-RC1"}, "time": "2013-11-22T17:42:00+00:00"}, {"version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "d51d78b34c1d9dcc384ba48155105fe99284dd67"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d51d78b34c1d9dcc384ba48155105fe99284dd67", "type": "zip", "shasum": "", "reference": "d51d78b34c1d9dcc384ba48155105fe99284dd67"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.0-BETA2"}, "time": "2013-10-17T11:48:11+00:00"}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "9b4fe5757870682eb2251e283228a66d938265a8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9b4fe5757870682eb2251e283228a66d938265a8", "type": "zip", "shasum": "", "reference": "9b4fe5757870682eb2251e283228a66d938265a8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.4.0-BETA1"}, "time": "2013-09-19T09:47:34+00:00"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "fd6d162d97bf3e6060622e5c015af39ca72e33bc"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/fd6d162d97bf3e6060622e5c015af39ca72e33bc", "type": "zip", "shasum": "", "reference": "fd6d162d97bf3e6060622e5c015af39ca72e33bc"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/2.3"}, "time": "2016-04-04T09:22:54+00:00", "autoload": {"psr-0": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "require-dev": {"symfony/dependency-injection": "~2.0,>=2.0.5"}}, {"version": "v2.3.41", "version_normalized": "********"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b2d9d812e21d6e00983061d49ee61a82f54cace7"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b2d9d812e21d6e00983061d49ee61a82f54cace7", "type": "zip", "shasum": "", "reference": "b2d9d812e21d6e00983061d49ee61a82f54cace7"}, "time": "2016-03-04T07:12:06+00:00"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "011c727163ccc251b315d09d11558acf711fce32"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/011c727163ccc251b315d09d11558acf711fce32", "type": "zip", "shasum": "", "reference": "011c727163ccc251b315d09d11558acf711fce32"}, "time": "2016-01-25T17:00:36+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "03bb35f85a3eafa91cbce7350fa581c6b49c77f9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/03bb35f85a3eafa91cbce7350fa581c6b49c77f9", "type": "zip", "shasum": "", "reference": "03bb35f85a3eafa91cbce7350fa581c6b49c77f9"}, "time": "2016-01-12T19:46:23+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "08564581a8444035d0874efc35a6366be8c0af9e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/08564581a8444035d0874efc35a6366be8c0af9e", "type": "zip", "shasum": "", "reference": "08564581a8444035d0874efc35a6366be8c0af9e"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.35", "version_normalized": "********"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "26692d64c66d679dbb6669703ea3a029418dfb80"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/26692d64c66d679dbb6669703ea3a029418dfb80", "type": "zip", "shasum": "", "reference": "26692d64c66d679dbb6669703ea3a029418dfb80"}, "time": "2015-10-11T09:37:49+00:00", "autoload": {"psr-0": {"Symfony\\Component\\EventDispatcher\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "708b106f00ea634b2acc7d0ec89689c914e446b0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/708b106f00ea634b2acc7d0ec89689c914e446b0", "type": "zip", "shasum": "", "reference": "708b106f00ea634b2acc7d0ec89689c914e446b0"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.33"}, "time": "2015-09-17T01:42:05+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/dependency-injection": "~2.0,>=2.0.5"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "af8fba40bdab97fc666173a8c2087e02445c231c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/af8fba40bdab97fc666173a8c2087e02445c231c", "type": "zip", "shasum": "", "reference": "af8fba40bdab97fc666173a8c2087e02445c231c"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.32"}, "time": "2015-05-15T13:28:34+00:00"}, {"version": "v2.3.31", "version_normalized": "********"}, {"version": "v2.3.30", "version_normalized": "********"}, {"version": "v2.3.29", "version_normalized": "********"}, {"version": "v2.3.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "a0d920f6a2c3dcc6ad296d09a879689127cde251"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a0d920f6a2c3dcc6ad296d09a879689127cde251", "type": "zip", "shasum": "", "reference": "a0d920f6a2c3dcc6ad296d09a879689127cde251"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.28"}, "time": "2015-05-01T14:06:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "68771791930c7c1923bc64db1cfaf8692a5878a0"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/68771791930c7c1923bc64db1cfaf8692a5878a0", "type": "zip", "shasum": "", "reference": "68771791930c7c1923bc64db1cfaf8692a5878a0"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.27"}, "time": "2015-02-24T10:24:26+00:00"}, {"version": "v2.3.26", "version_normalized": "********"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "49d3432269d092b8d325b58615815fdbe93f49cb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/49d3432269d092b8d325b58615815fdbe93f49cb", "type": "zip", "shasum": "", "reference": "49d3432269d092b8d325b58615815fdbe93f49cb"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.25"}, "time": "2015-01-03T13:14:51+00:00", "require-dev": {"symfony/dependency-injection": "~2.0,>=2.0.5"}}, {"version": "v2.3.24", "version_normalized": "********"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "36a40695d94e948d7a85347db0b12ba446c400fa"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/36a40695d94e948d7a85347db0b12ba446c400fa", "type": "zip", "shasum": "", "reference": "36a40695d94e948d7a85347db0b12ba446c400fa"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.23"}, "time": "2014-11-30T13:33:44+00:00", "require-dev": {"symfony/dependency-injection": "~2.0"}}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "1087b07bc130533f674c328f40a099b1ee3ff7d2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1087b07bc130533f674c328f40a099b1ee3ff7d2", "type": "zip", "shasum": "", "reference": "1087b07bc130533f674c328f40a099b1ee3ff7d2"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.22"}, "time": "2014-10-26T07:30:58+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3e0b837811fadd73c833c7c06a92201d953df59d"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3e0b837811fadd73c833c7c06a92201d953df59d", "type": "zip", "shasum": "", "reference": "3e0b837811fadd73c833c7c06a92201d953df59d"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.21"}, "time": "2014-10-01T05:39:06+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "041739a0ada100e7f62ee3bf95aa86c41e6a1215"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/041739a0ada100e7f62ee3bf95aa86c41e6a1215", "type": "zip", "shasum": "", "reference": "041739a0ada100e7f62ee3bf95aa86c41e6a1215"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.20"}, "time": "2014-09-22T08:32:35+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2c64e46d7e22bcafcab4413ff62bc389abf87ea5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2c64e46d7e22bcafcab4413ff62bc389abf87ea5", "type": "zip", "shasum": "", "reference": "2c64e46d7e22bcafcab4413ff62bc389abf87ea5"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.19"}, "time": "2014-07-25T15:00:14+00:00"}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "5b8028ed237cf8b58329fceb3809cd47ce261033"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5b8028ed237cf8b58329fceb3809cd47ce261033", "type": "zip", "shasum": "", "reference": "5b8028ed237cf8b58329fceb3809cd47ce261033"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.18"}, "time": "2014-07-07T10:13:42+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "158e002aab15cca219aba342bc5b476ebea79506"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/158e002aab15cca219aba342bc5b476ebea79506", "type": "zip", "shasum": "", "reference": "158e002aab15cca219aba342bc5b476ebea79506"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "cb7cd38c081507d10997553c4c522956a4d2afab"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cb7cd38c081507d10997553c4c522956a4d2afab", "type": "zip", "shasum": "", "reference": "cb7cd38c081507d10997553c4c522956a4d2afab"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.16"}, "time": "2014-04-16T10:30:19+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "15645237c6ff70e74a28e8836362d82492765055"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/15645237c6ff70e74a28e8836362d82492765055", "type": "zip", "shasum": "", "reference": "15645237c6ff70e74a28e8836362d82492765055"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.12"}, "time": "2014-02-11T10:29:24+00:00"}, {"version": "v2.3.11", "version_normalized": "********"}, {"version": "v2.3.10", "version_normalized": "********"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3153c07622eff7894a154f87cfa7dc501d2dbf8e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3153c07622eff7894a154f87cfa7dc501d2dbf8e", "type": "zip", "shasum": "", "reference": "3153c07622eff7894a154f87cfa7dc501d2dbf8e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.9"}, "time": "2013-12-28T07:46:05+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "30b2f954258361a10301189ddad8522462321924"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/30b2f954258361a10301189ddad8522462321924", "type": "zip", "shasum": "", "reference": "30b2f954258361a10301189ddad8522462321924"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.8"}, "time": "2013-12-03T14:51:26+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2d8ece3c610726a73d0c95c885134efea182610e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2d8ece3c610726a73d0c95c885134efea182610e", "type": "zip", "shasum": "", "reference": "2d8ece3c610726a73d0c95c885134efea182610e"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.7"}, "time": "2013-10-13T06:32:10+00:00"}, {"version": "v2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "7fc72a7a346a1887d3968cc1ce5642a15cd182e9"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7fc72a7a346a1887d3968cc1ce5642a15cd182e9", "type": "zip", "shasum": "", "reference": "7fc72a7a346a1887d3968cc1ce5642a15cd182e9"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.6"}, "time": "2013-09-19T09:45:20+00:00"}, {"version": "v2.3.5", "version_normalized": "*******"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "41c9826457c65fa3cf746f214985b7ca9cba42f8"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/41c9826457c65fa3cf746f214985b7ca9cba42f8", "type": "zip", "shasum": "", "reference": "41c9826457c65fa3cf746f214985b7ca9cba42f8"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.4"}, "time": "2013-07-21T12:12:18+00:00"}, {"version": "v2.3.3", "version_normalized": "*******"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2d84c66c090ed676f9ac60a46b9366d4dd80a523"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2d84c66c090ed676f9ac60a46b9366d4dd80a523", "type": "zip", "shasum": "", "reference": "2d84c66c090ed676f9ac60a46b9366d4dd80a523"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.3.2"}, "time": "2013-05-13T14:36:40+00:00"}, {"version": "v2.3.1", "version_normalized": "*******"}, {"version": "v2.3.0", "version_normalized": "*******"}, {"version": "v2.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "131423f38d4df06f8652f45548b79f18102ccbc7"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/131423f38d4df06f8652f45548b79f18102ccbc7", "type": "zip", "shasum": "", "reference": "131423f38d4df06f8652f45548b79f18102ccbc7"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.2.11"}, "time": "2013-11-30T09:35:04+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "suggest": {"symfony/dependency-injection": "2.2.*", "symfony/http-kernel": "2.2.*"}}, {"version": "v2.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "15a03e923e4bd18caaef1c18a2cceca3ee9da226"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/15a03e923e4bd18caaef1c18a2cceca3ee9da226", "type": "zip", "shasum": "", "reference": "15a03e923e4bd18caaef1c18a2cceca3ee9da226"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.2.10"}, "time": "2013-09-18T07:27:26+00:00"}, {"version": "v2.2.9", "version_normalized": "*******"}, {"version": "v2.2.8", "version_normalized": "*******"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "45c43ffa186a0473c71a947981e43e025cd93c87"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/45c43ffa186a0473c71a947981e43e025cd93c87", "type": "zip", "shasum": "", "reference": "45c43ffa186a0473c71a947981e43e025cd93c87"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.2.6"}, "time": "2013-02-11T11:26:43+00:00"}, {"version": "v2.2.5", "version_normalized": "*******"}, {"version": "v2.2.4", "version_normalized": "*******"}, {"version": "v2.2.3", "version_normalized": "*******"}, {"version": "v2.2.2", "version_normalized": "*******"}, {"version": "v2.2.1", "version_normalized": "*******"}, {"version": "v2.2.0", "version_normalized": "*******"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "e1d18ff0ff6f3e45ac82f000bc221135df635527"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e1d18ff0ff6f3e45ac82f000bc221135df635527", "type": "zip", "shasum": "", "reference": "e1d18ff0ff6f3e45ac82f000bc221135df635527"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.13"}, "time": "2013-02-11T11:26:14+00:00", "autoload": {"psr-0": {"Symfony\\Component\\EventDispatcher": ""}}, "require-dev": {"symfony/dependency-injection": "2.1.*"}, "suggest": {"symfony/dependency-injection": "2.1.*", "symfony/http-kernel": "2.1.*"}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********"}, {"version": "v2.1.10", "version_normalized": "********"}, {"version": "v2.1.9", "version_normalized": "*******"}, {"version": "v2.1.8", "version_normalized": "*******"}, {"version": "v2.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "023521a01c28454729a8cbd4d037a8e8a40731dd"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/023521a01c28454729a8cbd4d037a8e8a40731dd", "type": "zip", "shasum": "", "reference": "023521a01c28454729a8cbd4d037a8e8a40731dd"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.7"}, "time": "2013-01-11T00:31:43+00:00"}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "df9bde91f8f6fa7fb40996edf30e632cfc606b4a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/df9bde91f8f6fa7fb40996edf30e632cfc606b4a", "type": "zip", "shasum": "", "reference": "df9bde91f8f6fa7fb40996edf30e632cfc606b4a"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.6"}, "time": "2012-12-06T10:00:55+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "b5b6807bcc192f3a1c4fba7ef48466ebbd3c4485"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b5b6807bcc192f3a1c4fba7ef48466ebbd3c4485", "type": "zip", "shasum": "", "reference": "b5b6807bcc192f3a1c4fba7ef48466ebbd3c4485"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.4"}, "time": "2012-11-08T09:51:48+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "fb273317c0505ba657aa3d727530d36ae671fb82"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/fb273317c0505ba657aa3d727530d36ae671fb82", "type": "zip", "shasum": "", "reference": "fb273317c0505ba657aa3d727530d36ae671fb82"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.3"}, "time": "2012-10-04T15:17:57+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "6e9726a4e82fe95fcb243e7c4021f7fb2de19b70"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/6e9726a4e82fe95fcb243e7c4021f7fb2de19b70", "type": "zip", "shasum": "", "reference": "6e9726a4e82fe95fcb243e7c4021f7fb2de19b70"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.2"}, "time": "2012-09-10T10:53:42+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "421520fd35ace52106947b2d6c2d9db49cb5a866"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/421520fd35ace52106947b2d6c2d9db49cb5a866", "type": "zip", "shasum": "", "reference": "421520fd35ace52106947b2d6c2d9db49cb5a866"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.1.0"}, "time": "2012-08-22T13:48:41+00:00", "suggest": {"symfony/dependency-injection": "v2.1.0", "symfony/http-kernel": "v2.1.0"}}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3b20aeffc3432e0bf2b43f55238f754fe384d5a5"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3b20aeffc3432e0bf2b43f55238f754fe384d5a5", "type": "zip", "shasum": "", "reference": "3b20aeffc3432e0bf2b43f55238f754fe384d5a5"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.25"}, "time": "2013-02-10T10:45:08+00:00", "require": {"php": ">=5.3.2"}, "require-dev": "__unset", "suggest": "__unset", "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********"}, {"version": "v2.0.23", "version_normalized": "********"}, {"version": "v2.0.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "bb9bd2bc2b2a7bad4baf2d0861b6be2c701a0190"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bb9bd2bc2b2a7bad4baf2d0861b6be2c701a0190", "type": "zip", "shasum": "", "reference": "bb9bd2bc2b2a7bad4baf2d0861b6be2c701a0190"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.22"}, "time": "2013-01-04T16:59:43+00:00"}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "db210286198c2a335388d410fbff893ae509bad6"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/db210286198c2a335388d410fbff893ae509bad6", "type": "zip", "shasum": "", "reference": "db210286198c2a335388d410fbff893ae509bad6"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.21"}, "time": "2012-07-09T12:43:50+00:00"}, {"version": "v2.0.20", "version_normalized": "********"}, {"version": "v2.0.19", "version_normalized": "********"}, {"version": "v2.0.18", "version_normalized": "********"}, {"version": "v2.0.17", "version_normalized": "********"}, {"version": "v2.0.16", "version_normalized": "********"}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "96f30ea9be0dfe567ba12168e65f92e061a5e3ff"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/96f30ea9be0dfe567ba12168e65f92e061a5e3ff", "type": "zip", "shasum": "", "reference": "96f30ea9be0dfe567ba12168e65f92e061a5e3ff"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.15"}, "time": "2012-05-15T16:56:32+00:00"}, {"version": "v2.0.14", "version_normalized": "********"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "69f86e478aad6b1bf981e60414c9ead0f9a114f1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/69f86e478aad6b1bf981e60414c9ead0f9a114f1", "type": "zip", "shasum": "", "reference": "69f86e478aad6b1bf981e60414c9ead0f9a114f1"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.13"}, "time": "2012-04-12T10:11:02+00:00"}, {"version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "f8e90fed28e24e38c5061e27df54f8c01a3c0a9f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f8e90fed28e24e38c5061e27df54f8c01a3c0a9f", "type": "zip", "shasum": "", "reference": "f8e90fed28e24e38c5061e27df54f8c01a3c0a9f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.12"}, "time": "2012-03-03T00:31:12+00:00"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "359cd11745d3ecbe01e5ce309451a8210f6cc03f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/359cd11745d3ecbe01e5ce309451a8210f6cc03f", "type": "zip", "shasum": "", "reference": "359cd11745d3ecbe01e5ce309451a8210f6cc03f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.10"}, "time": "2012-01-05T13:51:20+00:00"}, {"version": "v2.0.9", "version_normalized": "*******"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "3b06b4edcf0ffb14f2a03e756ae6a73bff78575f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/3b06b4edcf0ffb14f2a03e756ae6a73bff78575f", "type": "zip", "shasum": "", "reference": "3b06b4edcf0ffb14f2a03e756ae6a73bff78575f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.7"}, "time": "2011-11-17T05:58:47+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "84fd3cd87d9cf4e9f8cc9549c6543245da232f36"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/84fd3cd87d9cf4e9f8cc9549c6543245da232f36", "type": "zip", "shasum": "", "reference": "84fd3cd87d9cf4e9f8cc9549c6543245da232f36"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.6"}, "time": "2011-11-02T13:18:45+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "2b4120a1b9f26e9c73195929d7723291ef592182"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2b4120a1b9f26e9c73195929d7723291ef592182", "type": "zip", "shasum": "", "reference": "2b4120a1b9f26e9c73195929d7723291ef592182"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher.git", "type": "git", "reference": "11d844997699682f23b6fbb50c00d343c645654f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/11d844997699682f23b6fbb50c00d343c645654f", "type": "zip", "shasum": "", "reference": "11d844997699682f23b6fbb50c00d343c645654f"}, "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v2.0.4"}, "time": "2011-09-26T22:55:43+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 29 May 2025 07:49:56 GMT"}