{"minified": "composer/2.0", "packages": {"symfony/http-client": [{"name": "symfony/http-client", "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "keywords": ["http"], "homepage": "https://symfony.com", "version": "v7.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4403d87a2c16f33345dca93407a8714ee8c05a64"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4403d87a2c16f33345dca93407a8714ee8c05a64", "type": "zip", "shasum": "", "reference": "4403d87a2c16f33345dca93407a8714ee8c05a64"}, "type": "library", "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-28T07:58:39+00:00", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "conflict": {"amphp/amp": "<2.5", "amphp/socket": "<1.1", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "57e4fb86314015a695a750ace358d07a7e37b8a9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/57e4fb86314015a695a750ace358d07a7e37b8a9", "type": "zip", "shasum": "", "reference": "57e4fb86314015a695a750ace358d07a7e37b8a9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.0"}, "time": "2025-05-02T08:23:16+00:00", "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "conflict": {"amphp/amp": "<2.5", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.0-BETA1"}}, {"version": "v7.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "f604d01d5bddeba9730df6e2745a2bdadb6a0af9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/f604d01d5bddeba9730df6e2745a2bdadb6a0af9", "type": "zip", "shasum": "", "reference": "f604d01d5bddeba9730df6e2745a2bdadb6a0af9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.8"}, "time": "2025-06-28T07:58:33+00:00"}, {"version": "v7.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/78981a2ffef6437ed92d4d7e2a86a82f256c6dc6", "type": "zip", "shasum": "", "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.4"}, "time": "2025-02-13T10:27:23+00:00"}, {"version": "v7.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7ce6078c79a4a7afff931c413d2959d3bffbfb8d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7ce6078c79a4a7afff931c413d2959d3bffbfb8d", "type": "zip", "shasum": "", "reference": "7ce6078c79a4a7afff931c413d2959d3bffbfb8d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.3"}, "time": "2025-01-28T15:51:35+00:00"}, {"version": "v7.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "339ba21476eb184290361542f732ad12c97591ec"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/339ba21476eb184290361542f732ad12c97591ec", "type": "zip", "shasum": "", "reference": "339ba21476eb184290361542f732ad12c97591ec"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.2"}, "time": "2024-12-30T18:35:15+00:00"}, {"version": "v7.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ff4df2b68d1c67abb9fef146e6540ea16b58d99e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ff4df2b68d1c67abb9fef146e6540ea16b58d99e", "type": "zip", "shasum": "", "reference": "ff4df2b68d1c67abb9fef146e6540ea16b58d99e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.1"}, "time": "2024-12-07T08:50:44+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "955e43336aff03df1e8a8e17daefabb0127a313b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/955e43336aff03df1e8a8e17daefabb0127a313b", "type": "zip", "shasum": "", "reference": "955e43336aff03df1e8a8e17daefabb0127a313b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.0"}, "time": "2024-11-29T08:22:02+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.3|^3.5.1", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9243e3847e5d86c9bde2b909bc78010eeb4b0409"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9243e3847e5d86c9bde2b909bc78010eeb4b0409", "type": "zip", "shasum": "", "reference": "9243e3847e5d86c9bde2b909bc78010eeb4b0409"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.0-RC1"}, "time": "2024-11-13T13:40:36+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "^3.4.1", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v7.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "730f1bb15938598419e92432e2156fc960dcf782"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/730f1bb15938598419e92432e2156fc960dcf782", "type": "zip", "shasum": "", "reference": "730f1bb15938598419e92432e2156fc960dcf782"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.0-BETA2"}, "time": "2024-11-06T08:44:59+00:00"}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "dee07290e432b1dde37dbb4ea1d67663f3cce0bc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/dee07290e432b1dde37dbb4ea1d67663f3cce0bc", "type": "zip", "shasum": "", "reference": "dee07290e432b1dde37dbb4ea1d67663f3cce0bc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.0-BETA1"}, "time": "2024-10-22T19:26:41+00:00"}, {"version": "v7.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "71632c1f13b36cb4c23ccdd255946dc02753afef"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/71632c1f13b36cb4c23ccdd255946dc02753afef", "type": "zip", "shasum": "", "reference": "71632c1f13b36cb4c23ccdd255946dc02753afef"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.11"}, "time": "2025-01-28T15:50:57+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}}, {"version": "v7.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "e221bfd51e70f12568e2f84890e6a5ffed0c35c7"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/e221bfd51e70f12568e2f84890e6a5ffed0c35c7", "type": "zip", "shasum": "", "reference": "e221bfd51e70f12568e2f84890e6a5ffed0c35c7"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.10"}, "time": "2024-12-30T18:35:03+00:00"}, {"version": "v7.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2ec49720a38a8041673ba4c42512bfd845218c56"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2ec49720a38a8041673ba4c42512bfd845218c56", "type": "zip", "shasum": "", "reference": "2ec49720a38a8041673ba4c42512bfd845218c56"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.9"}, "time": "2024-11-27T11:52:45+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.3|^3.5.1", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v7.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c30d91a1deac0dc3ed5e604683cf2e1dfc635b8a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c30d91a1deac0dc3ed5e604683cf2e1dfc635b8a", "type": "zip", "shasum": "", "reference": "c30d91a1deac0dc3ed5e604683cf2e1dfc635b8a"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.8"}, "time": "2024-11-13T13:40:27+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "^3.4.1", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v7.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "90ab2a4992dcf5d1f19a9b8737eba36a7c305fd0"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/90ab2a4992dcf5d1f19a9b8737eba36a7c305fd0", "type": "zip", "shasum": "", "reference": "90ab2a4992dcf5d1f19a9b8737eba36a7c305fd0"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.7"}, "time": "2024-11-05T16:45:54+00:00"}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "274e2f6886b43a36f8bd5dfeb67215f7ebf9e291"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/274e2f6886b43a36f8bd5dfeb67215f7ebf9e291", "type": "zip", "shasum": "", "reference": "274e2f6886b43a36f8bd5dfeb67215f7ebf9e291"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.6"}, "time": "2024-10-22T09:40:50+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "abca35865118edf35a23f2f24978a1784c831cb4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/abca35865118edf35a23f2f24978a1784c831cb4", "type": "zip", "shasum": "", "reference": "abca35865118edf35a23f2f24978a1784c831cb4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.5"}, "time": "2024-09-20T13:35:23+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a8f8d60b30b331cf4b743b3632e5acdba3f8285c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a8f8d60b30b331cf4b743b3632e5acdba3f8285c", "type": "zip", "shasum": "", "reference": "a8f8d60b30b331cf4b743b3632e5acdba3f8285c"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.4"}, "time": "2024-08-26T06:32:37+00:00"}, {"version": "v7.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b79858aa7a051ea791b0d50269a234a0b50cb231"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b79858aa7a051ea791b0d50269a234a0b50cb231", "type": "zip", "shasum": "", "reference": "b79858aa7a051ea791b0d50269a234a0b50cb231"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.3"}, "time": "2024-07-17T06:10:24+00:00"}, {"version": "v7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "90ace27d17ccc9afc6f7ec0081e8529fb0e29425"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/90ace27d17ccc9afc6f7ec0081e8529fb0e29425", "type": "zip", "shasum": "", "reference": "90ace27d17ccc9afc6f7ec0081e8529fb0e29425"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.2"}, "time": "2024-06-28T08:00:31+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1ec24a54d1885b11e862d6ddab31bd6749720d20"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1ec24a54d1885b11e862d6ddab31bd6749720d20", "type": "zip", "shasum": "", "reference": "1ec24a54d1885b11e862d6ddab31bd6749720d20"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2266f9813ed7d8c84e04627edead7b7fd249d6e9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2266f9813ed7d8c84e04627edead7b7fd249d6e9", "type": "zip", "shasum": "", "reference": "2266f9813ed7d8c84e04627edead7b7fd249d6e9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.0"}, "time": "2024-05-13T15:35:37+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "699730653571e7d2d95ca8587104bc6701d4859f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/699730653571e7d2d95ca8587104bc6701d4859f", "type": "zip", "shasum": "", "reference": "699730653571e7d2d95ca8587104bc6701d4859f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.1.0-BETA1"}, "time": "2024-05-02T11:58:01+00:00"}, {"version": "v7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3ae495c67ba9c3b504fecd070a6c28b4143088cf"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3ae495c67ba9c3b504fecd070a6c28b4143088cf", "type": "zip", "shasum": "", "reference": "3ae495c67ba9c3b504fecd070a6c28b4143088cf"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.10"}, "time": "2024-07-17T06:06:58+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3.4.1", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}}, {"version": "v7.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "10be5723e079acf878f7c4350021539e2071fac2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/10be5723e079acf878f7c4350021539e2071fac2", "type": "zip", "shasum": "", "reference": "10be5723e079acf878f7c4350021539e2071fac2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.9"}, "time": "2024-06-28T07:59:17+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7a05958fd4ecc139e2aa3ae7455f19c2cb4e08af"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7a05958fd4ecc139e2aa3ae7455f19c2cb4e08af", "type": "zip", "shasum": "", "reference": "7a05958fd4ecc139e2aa3ae7455f19c2cb4e08af"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6ce3c4c899051b3d7326ea1a1dda3729e29ae6d7"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6ce3c4c899051b3d7326ea1a1dda3729e29ae6d7", "type": "zip", "shasum": "", "reference": "6ce3c4c899051b3d7326ea1a1dda3729e29ae6d7"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6e70473909f46fe5dd3b994a0f1b20ecb6b2f858"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6e70473909f46fe5dd3b994a0f1b20ecb6b2f858", "type": "zip", "shasum": "", "reference": "6e70473909f46fe5dd3b994a0f1b20ecb6b2f858"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.6"}, "time": "2024-04-01T20:49:44+00:00"}, {"version": "v7.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "425f462a59d8030703ee04a9e1c666575ed5db3b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/425f462a59d8030703ee04a9e1c666575ed5db3b", "type": "zip", "shasum": "", "reference": "425f462a59d8030703ee04a9e1c666575ed5db3b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.5"}, "time": "2024-03-02T12:46:12+00:00", "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}}, {"version": "v7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8384876f49a2316a63f88a9cd12436de6936bee6"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8384876f49a2316a63f88a9cd12436de6936bee6", "type": "zip", "shasum": "", "reference": "8384876f49a2316a63f88a9cd12436de6936bee6"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.4"}, "time": "2024-02-15T11:33:06+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3d2605c07cd14aec294f72f5bf8147702f7a5ada"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3d2605c07cd14aec294f72f5bf8147702f7a5ada", "type": "zip", "shasum": "", "reference": "3d2605c07cd14aec294f72f5bf8147702f7a5ada"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.3"}, "time": "2024-01-29T15:41:16+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "db714986d3b84330bb6196fdb201c9f79b3a8853"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/db714986d3b84330bb6196fdb201c9f79b3a8853", "type": "zip", "shasum": "", "reference": "db714986d3b84330bb6196fdb201c9f79b3a8853"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.2"}, "time": "2023-12-02T12:51:19+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c3e90d09b3c45a5d47170e81a712d51c352cbc68"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c3e90d09b3c45a5d47170e81a712d51c352cbc68", "type": "zip", "shasum": "", "reference": "c3e90d09b3c45a5d47170e81a712d51c352cbc68"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.0"}, "time": "2023-11-29T08:40:23+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2c09ff8be6d35fa7f3ff4710c79423f3d4d00e6e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2c09ff8be6d35fa7f3ff4710c79423f3d4d00e6e", "type": "zip", "shasum": "", "reference": "2c09ff8be6d35fa7f3ff4710c79423f3d4d00e6e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.0-RC1"}, "time": "2023-11-07T10:26:03+00:00"}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5c8eddf04e1c0d8307bb6c02e8d9adeb535b8b06"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5c8eddf04e1c0d8307bb6c02e8d9adeb535b8b06", "type": "zip", "shasum": "", "reference": "5c8eddf04e1c0d8307bb6c02e8d9adeb535b8b06"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.0-BETA2"}, "time": "2023-10-29T12:50:44+00:00"}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0fc3d3a3a77b310f98100f4eec59f4039a24bcd4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0fc3d3a3a77b310f98100f4eec59f4039a24bcd4", "type": "zip", "shasum": "", "reference": "0fc3d3a3a77b310f98100f4eec59f4039a24bcd4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v7.0.0-BETA1"}, "time": "2023-10-07T16:01:47+00:00"}, {"version": "v6.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "19f11e742b94dcfd968a54f5381bb9082a88cb57"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/19f11e742b94dcfd968a54f5381bb9082a88cb57", "type": "zip", "shasum": "", "reference": "19f11e742b94dcfd968a54f5381bb9082a88cb57"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.23"}, "time": "2025-06-27T20:02:31+00:00", "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.3"}}, {"version": "v6.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3294a433fc9d12ae58128174896b5b1822c28dad"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3294a433fc9d12ae58128174896b5b1822c28dad", "type": "zip", "shasum": "", "reference": "3294a433fc9d12ae58128174896b5b1822c28dad"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.19"}, "time": "2025-02-13T09:55:13+00:00"}, {"version": "v6.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "394b440934056b8d9d6ba250001458e9d7998b7f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/394b440934056b8d9d6ba250001458e9d7998b7f", "type": "zip", "shasum": "", "reference": "394b440934056b8d9d6ba250001458e9d7998b7f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.18"}, "time": "2025-01-28T15:49:13+00:00"}, {"version": "v6.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "88898d842eb29d7e1a903724c94e90a6ca9c0509"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/88898d842eb29d7e1a903724c94e90a6ca9c0509", "type": "zip", "shasum": "", "reference": "88898d842eb29d7e1a903724c94e90a6ca9c0509"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.17"}, "time": "2024-12-18T12:18:31+00:00"}, {"version": "v6.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "60a113666fa67e598abace38e5f46a0954d8833d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/60a113666fa67e598abace38e5f46a0954d8833d", "type": "zip", "shasum": "", "reference": "60a113666fa67e598abace38e5f46a0954d8833d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.16"}, "time": "2024-11-27T11:52:33+00:00", "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.3|^3.5.1", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v6.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "cb4073c905cd12b8496d24ac428a9228c1750670"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/cb4073c905cd12b8496d24ac428a9228c1750670", "type": "zip", "shasum": "", "reference": "cb4073c905cd12b8496d24ac428a9228c1750670"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.15"}, "time": "2024-11-13T13:40:18+00:00", "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "^3.4.1", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v6.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "05d88cbd816ad6e0202edd9a9963cb9d615b8826"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/05d88cbd816ad6e0202edd9a9963cb9d615b8826", "type": "zip", "shasum": "", "reference": "05d88cbd816ad6e0202edd9a9963cb9d615b8826"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.14"}, "time": "2024-11-05T16:39:55+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "509d0e8a798bf5e41e0b6317e9bce1140af47376"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/509d0e8a798bf5e41e0b6317e9bce1140af47376", "type": "zip", "shasum": "", "reference": "509d0e8a798bf5e41e0b6317e9bce1140af47376"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.13"}, "time": "2024-10-14T18:15:01+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "fbebfcce21084d3e91ea987ae5bdd8c71ff0fd56"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/fbebfcce21084d3e91ea987ae5bdd8c71ff0fd56", "type": "zip", "shasum": "", "reference": "fbebfcce21084d3e91ea987ae5bdd8c71ff0fd56"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.12"}, "time": "2024-09-20T08:21:33+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4c92046bb788648ff1098cc66da69aa7eac8cb65"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4c92046bb788648ff1098cc66da69aa7eac8cb65", "type": "zip", "shasum": "", "reference": "4c92046bb788648ff1098cc66da69aa7eac8cb65"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.11"}, "time": "2024-08-26T06:30:21+00:00"}, {"version": "v6.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b5e498f763e0bf5eed8dcd946e50a3b3f71d4ded"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b5e498f763e0bf5eed8dcd946e50a3b3f71d4ded", "type": "zip", "shasum": "", "reference": "b5e498f763e0bf5eed8dcd946e50a3b3f71d4ded"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.10"}, "time": "2024-07-15T09:26:24+00:00"}, {"version": "v6.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6e9db0025db565bcf8f1d46ed734b549e51e6045"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6e9db0025db565bcf8f1d46ed734b549e51e6045", "type": "zip", "shasum": "", "reference": "6e9db0025db565bcf8f1d46ed734b549e51e6045"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.9"}, "time": "2024-06-28T07:59:05+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "61faba993e620fc22d4f0ab3b6bcf8fbb0d44b05"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/61faba993e620fc22d4f0ab3b6bcf8fbb0d44b05", "type": "zip", "shasum": "", "reference": "61faba993e620fc22d4f0ab3b6bcf8fbb0d44b05"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3683d8107cf1efdd24795cc5f7482be1eded34ac"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3683d8107cf1efdd24795cc5f7482be1eded34ac", "type": "zip", "shasum": "", "reference": "3683d8107cf1efdd24795cc5f7482be1eded34ac"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6a46c0ea9b099f9a5132d560a51833ffcbd5b0d9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6a46c0ea9b099f9a5132d560a51833ffcbd5b0d9", "type": "zip", "shasum": "", "reference": "6a46c0ea9b099f9a5132d560a51833ffcbd5b0d9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.6"}, "time": "2024-04-01T20:35:50+00:00"}, {"version": "v6.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "f3c86a60a3615f466333a11fd42010d4382a82c7"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/f3c86a60a3615f466333a11fd42010d4382a82c7", "type": "zip", "shasum": "", "reference": "f3c86a60a3615f466333a11fd42010d4382a82c7"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.5"}, "time": "2024-03-02T12:45:30+00:00", "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}}, {"version": "v6.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "aa6281ddb3be1b3088f329307d05abfbbeb97649"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/aa6281ddb3be1b3088f329307d05abfbbeb97649", "type": "zip", "shasum": "", "reference": "aa6281ddb3be1b3088f329307d05abfbbeb97649"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.4"}, "time": "2024-02-14T16:28:12+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a9034bc119fab8238f76cf49c770f3135f3ead86"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a9034bc119fab8238f76cf49c770f3135f3ead86", "type": "zip", "shasum": "", "reference": "a9034bc119fab8238f76cf49c770f3135f3ead86"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.3"}, "time": "2024-01-29T15:01:07+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "fc0944665bd932cf32a7b8a1d009466afc16528f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/fc0944665bd932cf32a7b8a1d009466afc16528f", "type": "zip", "shasum": "", "reference": "fc0944665bd932cf32a7b8a1d009466afc16528f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.2"}, "time": "2023-12-02T12:49:56+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5c584530b77aa10ae216989ffc48b4bedc9c0b29"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5c584530b77aa10ae216989ffc48b4bedc9c0b29", "type": "zip", "shasum": "", "reference": "5c584530b77aa10ae216989ffc48b4bedc9c0b29"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.0"}, "time": "2023-11-28T20:55:58+00:00"}, {"version": "v6.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3904fc3e97e6f28e1db3837063e98928f5ee7264"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3904fc3e97e6f28e1db3837063e98928f5ee7264", "type": "zip", "shasum": "", "reference": "3904fc3e97e6f28e1db3837063e98928f5ee7264"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.0-RC2"}, "time": "2023-11-25T16:57:46+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2e2dff5212ab9834f331060a126e7564ed7f3722"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2e2dff5212ab9834f331060a126e7564ed7f3722", "type": "zip", "shasum": "", "reference": "2e2dff5212ab9834f331060a126e7564ed7f3722"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.0-RC1"}, "time": "2023-11-07T10:18:57+00:00"}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a4d0e09bf84c03747e3fde236f1f13153cccaaff"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a4d0e09bf84c03747e3fde236f1f13153cccaaff", "type": "zip", "shasum": "", "reference": "a4d0e09bf84c03747e3fde236f1f13153cccaaff"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.0-BETA2"}, "time": "2023-10-29T12:42:46+00:00"}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "bea130754ee8a62ae5845a4356cbc07f841544da"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/bea130754ee8a62ae5845a4356cbc07f841544da", "type": "zip", "shasum": "", "reference": "bea130754ee8a62ae5845a4356cbc07f841544da"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.0-BETA1"}, "time": "2023-10-07T07:34:26+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "510c51058dbef5db7d49c704954f32d6527d17f8"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/510c51058dbef5db7d49c704954f32d6527d17f8", "type": "zip", "shasum": "", "reference": "510c51058dbef5db7d49c704954f32d6527d17f8"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.12"}, "time": "2024-01-29T14:46:07+00:00", "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0"}}, {"version": "v6.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2f44f1d84bdcd9cca21ff430f5cd4a3fefa746fa"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2f44f1d84bdcd9cca21ff430f5cd4a3fefa746fa", "type": "zip", "shasum": "", "reference": "2f44f1d84bdcd9cca21ff430f5cd4a3fefa746fa"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.11"}, "time": "2023-12-02T12:48:42+00:00"}, {"version": "v6.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0314e2d49939a9831929d6fc81c01c6df137fd0a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0314e2d49939a9831929d6fc81c01c6df137fd0a", "type": "zip", "shasum": "", "reference": "0314e2d49939a9831929d6fc81c01c6df137fd0a"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.8"}, "time": "2023-11-06T18:31:59+00:00"}, {"version": "v6.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "cd67fcaf4524ec6ae5d9b2d9497682d7ad3ce57d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/cd67fcaf4524ec6ae5d9b2d9497682d7ad3ce57d", "type": "zip", "shasum": "", "reference": "cd67fcaf4524ec6ae5d9b2d9497682d7ad3ce57d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.7"}, "time": "2023-10-29T12:41:36+00:00"}, {"version": "v6.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ab8446f997efb9913627e9da10fa784d2182fe92"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ab8446f997efb9913627e9da10fa784d2182fe92", "type": "zip", "shasum": "", "reference": "ab8446f997efb9913627e9da10fa784d2182fe92"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.6"}, "time": "2023-10-06T10:08:56+00:00"}, {"version": "v6.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "213e564da4cbf61acc9728d97e666bcdb868c10d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/213e564da4cbf61acc9728d97e666bcdb868c10d", "type": "zip", "shasum": "", "reference": "213e564da4cbf61acc9728d97e666bcdb868c10d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.5"}, "time": "2023-09-29T15:57:12+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "15f9f4bad62bfcbe48b5dedd866f04a08fc7ff00"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/15f9f4bad62bfcbe48b5dedd866f04a08fc7ff00", "type": "zip", "shasum": "", "reference": "15f9f4bad62bfcbe48b5dedd866f04a08fc7ff00"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.2"}, "time": "2023-07-05T08:41:27+00:00"}, {"version": "v6.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1c828a06aef2f5eeba42026dfc532d4fc5406123"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1c828a06aef2f5eeba42026dfc532d4fc5406123", "type": "zip", "shasum": "", "reference": "1c828a06aef2f5eeba42026dfc532d4fc5406123"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.1"}, "time": "2023-06-24T11:51:27+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b2f892c91e4e02a939edddeb7ef452522d10a424"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b2f892c91e4e02a939edddeb7ef452522d10a424", "type": "zip", "shasum": "", "reference": "b2f892c91e4e02a939edddeb7ef452522d10a424"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.0"}, "time": "2023-05-12T08:49:48+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.0-BETA3"}}, {"version": "v6.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "497e8cb4d849bd0b7db54f8182cd982dd898bfd6"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/497e8cb4d849bd0b7db54f8182cd982dd898bfd6", "type": "zip", "shasum": "", "reference": "497e8cb4d849bd0b7db54f8182cd982dd898bfd6"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.0-BETA2"}, "time": "2023-05-05T15:55:45+00:00"}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "e435847c4c5ebbc2ba81273a6bb834cb56f73f49"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/e435847c4c5ebbc2ba81273a6bb834cb56f73f49", "type": "zip", "shasum": "", "reference": "e435847c4c5ebbc2ba81273a6bb834cb56f73f49"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.3.0-BETA1"}, "time": "2023-04-28T15:57:00+00:00", "conflict": {"symfony/http-foundation": "<6.3"}}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "297374a399ce6852d5905d92a1351df00bb9dd10"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/297374a399ce6852d5905d92a1351df00bb9dd10", "type": "zip", "shasum": "", "reference": "297374a399ce6852d5905d92a1351df00bb9dd10"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.13"}, "time": "2023-07-03T12:13:45+00:00", "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0"}, "conflict": "__unset"}, {"version": "v6.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7ef37b91629937843e8f52a2da5587ff16d75d25"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7ef37b91629937843e8f52a2da5587ff16d75d25", "type": "zip", "shasum": "", "reference": "7ef37b91629937843e8f52a2da5587ff16d75d25"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.12"}, "time": "2023-06-24T11:48:11+00:00"}, {"version": "v6.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "39f679c12648cc43bd9f0db12cc69b82041b91a1"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/39f679c12648cc43bd9f0db12cc69b82041b91a1", "type": "zip", "shasum": "", "reference": "39f679c12648cc43bd9f0db12cc69b82041b91a1"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.11"}, "time": "2023-05-12T08:48:34+00:00"}, {"version": "v6.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3f5545a91c8e79dedd1a06c4b04e1682c80c42f9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3f5545a91c8e79dedd1a06c4b04e1682c80c42f9", "type": "zip", "shasum": "", "reference": "3f5545a91c8e79dedd1a06c4b04e1682c80c42f9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.10"}, "time": "2023-04-20T13:12:48+00:00", "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0"}}, {"version": "v6.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7daf5d24c21a683164688b95bb73b7a4bd3b32fc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7daf5d24c21a683164688b95bb73b7a4bd3b32fc", "type": "zip", "shasum": "", "reference": "7daf5d24c21a683164688b95bb73b7a4bd3b32fc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.9"}, "time": "2023-04-11T16:03:19+00:00"}, {"version": "v6.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "66391ba3a8862c560e1d9134c96d9bd2a619b477"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/66391ba3a8862c560e1d9134c96d9bd2a619b477", "type": "zip", "shasum": "", "reference": "66391ba3a8862c560e1d9134c96d9bd2a619b477"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.8"}, "time": "2023-03-31T09:14:44+00:00"}, {"keywords": [], "version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0a5be6cbc570ae23b51b49d67341f378629d78e4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0a5be6cbc570ae23b51b49d67341f378629d78e4", "type": "zip", "shasum": "", "reference": "0a5be6cbc570ae23b51b49d67341f378629d78e4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.7"}, "time": "2023-02-21T10:54:55+00:00"}, {"version": "v6.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6efa9a7521ab7d031a82cf0a759484d1b02a6ad9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6efa9a7521ab7d031a82cf0a759484d1b02a6ad9", "type": "zip", "shasum": "", "reference": "6efa9a7521ab7d031a82cf0a759484d1b02a6ad9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.6"}, "time": "2023-01-30T15:46:28+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c5e5b772033eae96ae82f2190546399ad18c1373"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c5e5b772033eae96ae82f2190546399ad18c1373", "type": "zip", "shasum": "", "reference": "c5e5b772033eae96ae82f2190546399ad18c1373"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.5"}, "time": "2023-01-13T08:35:57+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7054ad466f836309aef511789b9c697bc986d8ce"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7054ad466f836309aef511789b9c697bc986d8ce", "type": "zip", "shasum": "", "reference": "7054ad466f836309aef511789b9c697bc986d8ce"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.2"}, "time": "2022-12-14T16:11:27+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "153540b6ed72eecdcb42dc847f8d8cf2e2516e8e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/153540b6ed72eecdcb42dc847f8d8cf2e2516e8e", "type": "zip", "shasum": "", "reference": "153540b6ed72eecdcb42dc847f8d8cf2e2516e8e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.0"}, "time": "2022-11-14T10:13:36+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1180fd65e14de233b06f44d7e17370b4e8e01c76"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1180fd65e14de233b06f44d7e17370b4e8e01c76", "type": "zip", "shasum": "", "reference": "1180fd65e14de233b06f44d7e17370b4e8e01c76"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.0-BETA2"}, "time": "2022-10-28T16:24:13+00:00"}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "e7125232ba7d50020f232caedf33c8acbfa2a136"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/e7125232ba7d50020f232caedf33c8acbfa2a136", "type": "zip", "shasum": "", "reference": "e7125232ba7d50020f232caedf33c8acbfa2a136"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.2.0-BETA1"}, "time": "2022-10-21T16:52:33+00:00"}, {"version": "v6.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "96b94205f960fefe351d3b30645cfe1664b7f208"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/96b94205f960fefe351d3b30645cfe1664b7f208", "type": "zip", "shasum": "", "reference": "96b94205f960fefe351d3b30645cfe1664b7f208"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.12"}, "time": "2023-01-30T15:43:30+00:00", "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "36ed2c206eab2ad25d13da0d37fa3255167c078f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/36ed2c206eab2ad25d13da0d37fa3255167c078f", "type": "zip", "shasum": "", "reference": "36ed2c206eab2ad25d13da0d37fa3255167c078f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.11"}, "time": "2023-01-13T08:35:02+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c7c6c95c83a86492d09d054d152da1ac872eba85"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c7c6c95c83a86492d09d054d152da1ac872eba85", "type": "zip", "shasum": "", "reference": "c7c6c95c83a86492d09d054d152da1ac872eba85"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.9"}, "time": "2022-12-14T16:05:20+00:00"}, {"version": "v6.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b7374c1f95714b27aeae9e4e4f573274f77a7f37"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b7374c1f95714b27aeae9e4e4f573274f77a7f37", "type": "zip", "shasum": "", "reference": "b7374c1f95714b27aeae9e4e4f573274f77a7f37"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.8"}, "time": "2022-11-14T10:13:26+00:00"}, {"version": "v6.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "f515d066728774efb34347a87580621416ca8968"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/f515d066728774efb34347a87580621416ca8968", "type": "zip", "shasum": "", "reference": "f515d066728774efb34347a87580621416ca8968"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.7"}, "time": "2022-10-28T16:23:08+00:00"}, {"version": "v6.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c8c887f4813370550147afd27d9eb8a8523e53b2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c8c887f4813370550147afd27d9eb8a8523e53b2", "type": "zip", "shasum": "", "reference": "c8c887f4813370550147afd27d9eb8a8523e53b2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.6"}, "time": "2022-10-12T05:10:31+00:00"}, {"version": "v6.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "565b0f2ce2c5882e89b3ef5e255d7e0478b9c675"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/565b0f2ce2c5882e89b3ef5e255d7e0478b9c675", "type": "zip", "shasum": "", "reference": "565b0f2ce2c5882e89b3ef5e255d7e0478b9c675"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.5"}, "time": "2022-09-09T09:34:27+00:00"}, {"version": "v6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "06dc27cbdcee26d6796c226db5266a0d58359739"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/06dc27cbdcee26d6796c226db5266a0d58359739", "type": "zip", "shasum": "", "reference": "06dc27cbdcee26d6796c226db5266a0d58359739"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.4"}, "time": "2022-08-02T16:17:38+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1ef59920a9cedf223e8564ae8ad7608cbe799b4d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1ef59920a9cedf223e8564ae8ad7608cbe799b4d", "type": "zip", "shasum": "", "reference": "1ef59920a9cedf223e8564ae8ad7608cbe799b4d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.3"}, "time": "2022-07-28T13:40:41+00:00"}, {"version": "v6.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ea1af6c8cc479147d67a3fead457dd7b06261041"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ea1af6c8cc479147d67a3fead457dd7b06261041", "type": "zip", "shasum": "", "reference": "ea1af6c8cc479147d67a3fead457dd7b06261041"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.2"}, "time": "2022-06-19T13:02:30+00:00"}, {"version": "v6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c5473d69640980367a6bdb0cdb449225dcaa991b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c5473d69640980367a6bdb0cdb449225dcaa991b", "type": "zip", "shasum": "", "reference": "c5473d69640980367a6bdb0cdb449225dcaa991b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.1"}, "time": "2022-06-09T13:06:55+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "aa0f9bae4e9f0328373f2cdf93996fb2278b0dd6"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/aa0f9bae4e9f0328373f2cdf93996fb2278b0dd6", "type": "zip", "shasum": "", "reference": "aa0f9bae4e9f0328373f2cdf93996fb2278b0dd6"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.0"}, "time": "2022-05-21T13:34:40+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "29a0e184c584045bc95b4ebdc93a9ab012dd5194"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/29a0e184c584045bc95b4ebdc93a9ab012dd5194", "type": "zip", "shasum": "", "reference": "29a0e184c584045bc95b4ebdc93a9ab012dd5194"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.0-RC1"}, "time": "2022-05-11T12:12:29+00:00"}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "da3338541435c14d6c7b77f9677bf74088984c7b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/da3338541435c14d6c7b77f9677bf74088984c7b", "type": "zip", "shasum": "", "reference": "da3338541435c14d6c7b77f9677bf74088984c7b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.0-BETA2"}, "time": "2022-04-20T06:22:07+00:00"}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4544e83e1db45b3b87255c812d63b17bb95735c4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4544e83e1db45b3b87255c812d63b17bb95735c4", "type": "zip", "shasum": "", "reference": "4544e83e1db45b3b87255c812d63b17bb95735c4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.1.0-BETA1"}, "time": "2022-04-12T16:22:53+00:00"}, {"version": "v6.0.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "541c04560da1875f62c963c3aab6ea12a7314e11"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/541c04560da1875f62c963c3aab6ea12a7314e11", "type": "zip", "shasum": "", "reference": "541c04560da1875f62c963c3aab6ea12a7314e11"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.20"}, "time": "2023-01-30T15:41:07+00:00", "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5effe441679acb403442fdd48d262ffac6fac542"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5effe441679acb403442fdd48d262ffac6fac542", "type": "zip", "shasum": "", "reference": "5effe441679acb403442fdd48d262ffac6fac542"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.19"}, "time": "2023-01-13T08:34:10+00:00"}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d104286d135d29a17ead777888087e7f0fd11771"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d104286d135d29a17ead777888087e7f0fd11771", "type": "zip", "shasum": "", "reference": "d104286d135d29a17ead777888087e7f0fd11771"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.17"}, "time": "2022-12-14T15:52:41+00:00"}, {"version": "v6.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5db1221826d5f841f443d434358d5f82c862c5a9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5db1221826d5f841f443d434358d5f82c862c5a9", "type": "zip", "shasum": "", "reference": "5db1221826d5f841f443d434358d5f82c862c5a9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.16"}, "time": "2022-11-14T10:09:52+00:00"}, {"version": "v6.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "af84893895ce7637a4b60fd3de87fe9e44286a21"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/af84893895ce7637a4b60fd3de87fe9e44286a21", "type": "zip", "shasum": "", "reference": "af84893895ce7637a4b60fd3de87fe9e44286a21"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.15"}, "time": "2022-10-28T16:22:58+00:00"}, {"version": "v6.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ec183a587e3ad47f03cf1572d4b8437e0fc3e923"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ec183a587e3ad47f03cf1572d4b8437e0fc3e923", "type": "zip", "shasum": "", "reference": "ec183a587e3ad47f03cf1572d4b8437e0fc3e923"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.14"}, "time": "2022-10-11T15:20:43+00:00"}, {"version": "v6.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2067d3c398d47292f3b413fcc4f56385c1afd0d4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2067d3c398d47292f3b413fcc4f56385c1afd0d4", "type": "zip", "shasum": "", "reference": "2067d3c398d47292f3b413fcc4f56385c1afd0d4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.13"}, "time": "2022-09-09T09:33:56+00:00"}, {"version": "v6.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "411f73ad1a797f327d100d27fa5d715b947a8272"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/411f73ad1a797f327d100d27fa5d715b947a8272", "type": "zip", "shasum": "", "reference": "411f73ad1a797f327d100d27fa5d715b947a8272"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.12"}, "time": "2022-08-02T16:01:06+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "49bef7df70f84a4f5d516eb268963779ca80320d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/49bef7df70f84a4f5d516eb268963779ca80320d", "type": "zip", "shasum": "", "reference": "49bef7df70f84a4f5d516eb268963779ca80320d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.11"}, "time": "2022-07-28T13:39:17+00:00"}, {"version": "v6.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3c6fc53a3deed2d3c1825d41ad8b3f23a6b038b5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3c6fc53a3deed2d3c1825d41ad8b3f23a6b038b5", "type": "zip", "shasum": "", "reference": "3c6fc53a3deed2d3c1825d41ad8b3f23a6b038b5"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.9"}, "time": "2022-05-21T13:33:31+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d347895193283e08b4c3ebf2f2974a1df3e1f670"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d347895193283e08b4c3ebf2f2974a1df3e1f670", "type": "zip", "shasum": "", "reference": "d347895193283e08b4c3ebf2f2974a1df3e1f670"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.8"}, "time": "2022-04-12T16:11:42+00:00"}, {"version": "v6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a7930c47248b9b57e9d0b8da100ffc1e031536dc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a7930c47248b9b57e9d0b8da100ffc1e031536dc", "type": "zip", "shasum": "", "reference": "a7930c47248b9b57e9d0b8da100ffc1e031536dc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.7"}, "time": "2022-04-01T12:27:43+00:00"}, {"version": "v6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a8f87328930932c455cffd048f965d1223d91915"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a8f87328930932c455cffd048f965d1223d91915", "type": "zip", "shasum": "", "reference": "a8f87328930932c455cffd048f965d1223d91915"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.5"}, "time": "2022-02-27T08:47:28+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "45b95017f6a20d564584bdee6a376c9a79caa316"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/45b95017f6a20d564584bdee6a376c9a79caa316", "type": "zip", "shasum": "", "reference": "45b95017f6a20d564584bdee6a376c9a79caa316"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.3"}, "time": "2022-01-22T06:58:00+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7f1cbd44590cb0acc6208c1711a52733e9a91663"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7f1cbd44590cb0acc6208c1711a52733e9a91663", "type": "zip", "shasum": "", "reference": "7f1cbd44590cb0acc6208c1711a52733e9a91663"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.2"}, "time": "2021-12-29T10:14:09+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "99e42b54cedf061d898aa796a0b3758598021607"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/99e42b54cedf061d898aa796a0b3758598021607", "type": "zip", "shasum": "", "reference": "99e42b54cedf061d898aa796a0b3758598021607"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "39f34cd5d28cd263b95a58ebad18421b6fefc4ba"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/39f34cd5d28cd263b95a58ebad18421b6fefc4ba", "type": "zip", "shasum": "", "reference": "39f34cd5d28cd263b95a58ebad18421b6fefc4ba"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.0"}, "time": "2021-11-23T19:05:29+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6727ed16b2e59233a24507f406575aafae23fb73"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6727ed16b2e59233a24507f406575aafae23fb73", "type": "zip", "shasum": "", "reference": "6727ed16b2e59233a24507f406575aafae23fb73"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.0-BETA3"}, "time": "2021-11-17T15:57:09+00:00", "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3.0", "symfony/service-contracts": "^1.0|^2.0|^3.0"}}, {"version": "v6.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2304f50331a0ee0b52e01d691b7d0a1fe9a7dc71"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2304f50331a0ee0b52e01d691b7d0a1fe9a7dc71", "type": "zip", "shasum": "", "reference": "2304f50331a0ee0b52e01d691b7d0a1fe9a7dc71"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.0-BETA2"}, "time": "2021-11-12T11:44:21+00:00"}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c6a3c1e972b1c671b26e7b8f9ea74ef815b86cd2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c6a3c1e972b1c671b26e7b8f9ea74ef815b86cd2", "type": "zip", "shasum": "", "reference": "c6a3c1e972b1c671b26e7b8f9ea74ef815b86cd2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.0-BETA1"}, "time": "2021-11-04T17:14:40+00:00"}, {"keywords": ["http"], "version": "v5.4.49", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d77d8e212cde7b5c4a64142bf431522f19487c28"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d77d8e212cde7b5c4a64142bf431522f19487c28", "type": "zip", "shasum": "", "reference": "d77d8e212cde7b5c4a64142bf431522f19487c28"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.49"}, "time": "2024-11-28T08:37:04+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}}, {"version": "v5.4.48", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "63a12783b8b367100a24e447cb3badf60ed4fc22"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/63a12783b8b367100a24e447cb3badf60ed4fc22", "type": "zip", "shasum": "", "reference": "63a12783b8b367100a24e447cb3badf60ed4fc22"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.48"}, "time": "2024-11-27T11:19:14+00:00"}, {"version": "v5.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3b643b83f87e1765d2e9b1e946bb56ee0b4b7bde"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3b643b83f87e1765d2e9b1e946bb56ee0b4b7bde", "type": "zip", "shasum": "", "reference": "3b643b83f87e1765d2e9b1e946bb56ee0b4b7bde"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.47"}, "time": "2024-11-13T12:18:12+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.3", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}}, {"version": "v5.4.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ebcaeeafc48b69f497f82b9700ddf54bfe975f71"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ebcaeeafc48b69f497f82b9700ddf54bfe975f71", "type": "zip", "shasum": "", "reference": "ebcaeeafc48b69f497f82b9700ddf54bfe975f71"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.46"}, "time": "2024-10-25T11:45:42+00:00"}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "54118c6340dc6831a00f10b296ea6e80592ec89d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/54118c6340dc6831a00f10b296ea6e80592ec89d", "type": "zip", "shasum": "", "reference": "54118c6340dc6831a00f10b296ea6e80592ec89d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00"}, {"version": "v5.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "58d3dc6bfa5fb37137e32d52ddc202ba4d1cea04"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/58d3dc6bfa5fb37137e32d52ddc202ba4d1cea04", "type": "zip", "shasum": "", "reference": "58d3dc6bfa5fb37137e32d52ddc202ba4d1cea04"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.44"}, "time": "2024-09-16T14:04:28+00:00"}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4d547e5259221bd37685f4ddc8e8947acc2cb755"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4d547e5259221bd37685f4ddc8e8947acc2cb755", "type": "zip", "shasum": "", "reference": "4d547e5259221bd37685f4ddc8e8947acc2cb755"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.43"}, "time": "2024-08-20T14:48:02+00:00"}, {"version": "v5.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ed17728617f53903ac684aa4f7a1739f121798d3"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ed17728617f53903ac684aa4f7a1739f121798d3", "type": "zip", "shasum": "", "reference": "ed17728617f53903ac684aa4f7a1739f121798d3"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.42"}, "time": "2024-07-10T08:10:21+00:00"}, {"version": "v5.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "87ca825717928d178de8a3458f163100925fb675"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/87ca825717928d178de8a3458f163100925fb675", "type": "zip", "shasum": "", "reference": "87ca825717928d178de8a3458f163100925fb675"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.41"}, "time": "2024-06-28T07:25:22+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "970a4d5a3393be3ba987b91a018e98ee332db63b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/970a4d5a3393be3ba987b91a018e98ee332db63b", "type": "zip", "shasum": "", "reference": "970a4d5a3393be3ba987b91a018e98ee332db63b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3cdc551aa98173bb8bac7e5ee49f3526abde0b04"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3cdc551aa98173bb8bac7e5ee49f3526abde0b04", "type": "zip", "shasum": "", "reference": "3cdc551aa98173bb8bac7e5ee49f3526abde0b04"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2a292194f6d4cf22d2348248d1c637750f72309d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2a292194f6d4cf22d2348248d1c637750f72309d", "type": "zip", "shasum": "", "reference": "2a292194f6d4cf22d2348248d1c637750f72309d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.38"}, "time": "2024-04-01T18:54:44+00:00"}, {"version": "v5.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "63d93fd99523b9608929a38172da3365a6c0821c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/63d93fd99523b9608929a38172da3365a6c0821c", "type": "zip", "shasum": "", "reference": "63d93fd99523b9608929a38172da3365a6c0821c"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.37"}, "time": "2024-02-28T15:18:15+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}}, {"version": "v5.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3e147c34ce44644f7bf7c2b8c8ecf76c0aac94b9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3e147c34ce44644f7bf7c2b8c8ecf76c0aac94b9", "type": "zip", "shasum": "", "reference": "3e147c34ce44644f7bf7c2b8c8ecf76c0aac94b9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.36"}, "time": "2024-02-14T15:13:37+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "53e4cc088a5f3466dc77c9f121f17e8e02ecc9c3"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/53e4cc088a5f3466dc77c9f121f17e8e02ecc9c3", "type": "zip", "shasum": "", "reference": "53e4cc088a5f3466dc77c9f121f17e8e02ecc9c3"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.35"}, "time": "2024-01-29T14:02:34+00:00"}, {"version": "v5.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8fe833b758bc5b325e9d96a913376d6d57a90fb0"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8fe833b758bc5b325e9d96a913376d6d57a90fb0", "type": "zip", "shasum": "", "reference": "8fe833b758bc5b325e9d96a913376d6d57a90fb0"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.34"}, "time": "2023-12-02T08:41:43+00:00"}, {"version": "v5.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6cdf6cdf48101454f014a9ab4e0905f0b902389d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6cdf6cdf48101454f014a9ab4e0905f0b902389d", "type": "zip", "shasum": "", "reference": "6cdf6cdf48101454f014a9ab4e0905f0b902389d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.31"}, "time": "2023-10-29T12:33:05+00:00"}, {"version": "v5.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "04784c66cbee613a827363ee1e65db65392893c1"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/04784c66cbee613a827363ee1e65db65392893c1", "type": "zip", "shasum": "", "reference": "04784c66cbee613a827363ee1e65db65392893c1"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.29"}, "time": "2023-09-14T20:49:15+00:00"}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "19d48ef7f38e5057ed1789a503cd3eccef039bce"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/19d48ef7f38e5057ed1789a503cd3eccef039bce", "type": "zip", "shasum": "", "reference": "19d48ef7f38e5057ed1789a503cd3eccef039bce"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.26"}, "time": "2023-07-03T12:14:50+00:00"}, {"version": "v5.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ccbb572627466f03a3d7aa1b23483787f5969afc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ccbb572627466f03a3d7aa1b23483787f5969afc", "type": "zip", "shasum": "", "reference": "ccbb572627466f03a3d7aa1b23483787f5969afc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.25"}, "time": "2023-06-21T14:44:30+00:00"}, {"version": "v5.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9e89ac4c9dfe29f4ed2b10a36e62720286632ad6"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9e89ac4c9dfe29f4ed2b10a36e62720286632ad6", "type": "zip", "shasum": "", "reference": "9e89ac4c9dfe29f4ed2b10a36e62720286632ad6"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.24"}, "time": "2023-05-07T13:11:28+00:00"}, {"version": "v5.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "617c98e46b54e43ca76945a908b1749bb82f4478"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/617c98e46b54e43ca76945a908b1749bb82f4478", "type": "zip", "shasum": "", "reference": "617c98e46b54e43ca76945a908b1749bb82f4478"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.23"}, "time": "2023-04-20T13:04:04+00:00", "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}}, {"version": "v5.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4cd1b7e7ee846c8b22cb47cbc435344af9b2a8bf"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4cd1b7e7ee846c8b22cb47cbc435344af9b2a8bf", "type": "zip", "shasum": "", "reference": "4cd1b7e7ee846c8b22cb47cbc435344af9b2a8bf"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.22"}, "time": "2023-03-24T15:16:26+00:00"}, {"keywords": [], "version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6b88914a7f1bf144df15904f60a19be78a67a3b2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6b88914a7f1bf144df15904f60a19be78a67a3b2", "type": "zip", "shasum": "", "reference": "6b88914a7f1bf144df15904f60a19be78a67a3b2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.21"}, "time": "2023-02-17T21:35:35+00:00"}, {"version": "v5.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b4d936b657c7952a41e89efd0ddcea51f8c90f34"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b4d936b657c7952a41e89efd0ddcea51f8c90f34", "type": "zip", "shasum": "", "reference": "b4d936b657c7952a41e89efd0ddcea51f8c90f34"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.20"}, "time": "2023-01-25T18:32:18+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0c22562d0601e19bd01c4480893f5438e6b12db5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0c22562d0601e19bd01c4480893f5438e6b12db5", "type": "zip", "shasum": "", "reference": "0c22562d0601e19bd01c4480893f5438e6b12db5"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.19"}, "time": "2023-01-12T15:47:53+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "772129f800fc0bfaa6bd40c40934d544f0957d30"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/772129f800fc0bfaa6bd40c40934d544f0957d30", "type": "zip", "shasum": "", "reference": "772129f800fc0bfaa6bd40c40934d544f0957d30"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.17"}, "time": "2022-12-13T11:07:37+00:00"}, {"version": "v5.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0f43af12a27733a060b92396b7bde84a4376da0a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0f43af12a27733a060b92396b7bde84a4376da0a", "type": "zip", "shasum": "", "reference": "0f43af12a27733a060b92396b7bde84a4376da0a"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.16"}, "time": "2022-11-09T11:27:39+00:00"}, {"version": "v5.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8f29b0f06c9ff48c8431e78eb90c8bd6f82cb12b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8f29b0f06c9ff48c8431e78eb90c8bd6f82cb12b", "type": "zip", "shasum": "", "reference": "8f29b0f06c9ff48c8431e78eb90c8bd6f82cb12b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.15"}, "time": "2022-10-25T16:22:13+00:00"}, {"version": "v5.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8a3929c814cba77db93de61c22759e0dbeaa4c87"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8a3929c814cba77db93de61c22759e0dbeaa4c87", "type": "zip", "shasum": "", "reference": "8a3929c814cba77db93de61c22759e0dbeaa4c87"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.14"}, "time": "2022-10-11T15:16:01+00:00"}, {"version": "v5.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "596fd752f00e0205d895cd6b184d135c27bb5d6a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/596fd752f00e0205d895cd6b184d135c27bb5d6a", "type": "zip", "shasum": "", "reference": "596fd752f00e0205d895cd6b184d135c27bb5d6a"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.13"}, "time": "2022-09-08T18:41:21+00:00"}, {"version": "v5.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6a057be154824487fd5e6b65ab83899e0c5ac550"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6a057be154824487fd5e6b65ab83899e0c5ac550", "type": "zip", "shasum": "", "reference": "6a057be154824487fd5e6b65ab83899e0c5ac550"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.12"}, "time": "2022-08-02T15:52:22+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5c5c37eb2a276d8d7d669dd76688aa1606ee78fb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5c5c37eb2a276d8d7d669dd76688aa1606ee78fb", "type": "zip", "shasum": "", "reference": "5c5c37eb2a276d8d7d669dd76688aa1606ee78fb"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.11"}, "time": "2022-07-28T13:33:28+00:00"}, {"version": "v5.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "dc0b15e42b762c040761c1eb9ce86a55d47cf672"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/dc0b15e42b762c040761c1eb9ce86a55d47cf672", "type": "zip", "shasum": "", "reference": "dc0b15e42b762c040761c1eb9ce86a55d47cf672"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.9"}, "time": "2022-05-21T08:57:05+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0dabec4e3898d3e00451dd47b5ef839168f9bbf5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0dabec4e3898d3e00451dd47b5ef839168f9bbf5", "type": "zip", "shasum": "", "reference": "0dabec4e3898d3e00451dd47b5ef839168f9bbf5"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.8"}, "time": "2022-04-12T16:02:29+00:00"}, {"version": "v5.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "88b6909f74fd1f2147e068411f71870a3b27ac56"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/88b6909f74fd1f2147e068411f71870a3b27ac56", "type": "zip", "shasum": "", "reference": "88b6909f74fd1f2147e068411f71870a3b27ac56"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.7"}, "time": "2022-04-01T12:27:37+00:00"}, {"version": "v5.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "fab84798694e45b4571d305125215699eb2b1f73"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/fab84798694e45b4571d305125215699eb2b1f73", "type": "zip", "shasum": "", "reference": "fab84798694e45b4571d305125215699eb2b1f73"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.5"}, "time": "2022-02-27T08:46:18+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a5a467b62dc91eb253db51a91a2c1977f611f60c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a5a467b62dc91eb253db51a91a2c1977f611f60c", "type": "zip", "shasum": "", "reference": "a5a467b62dc91eb253db51a91a2c1977f611f60c"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.3"}, "time": "2022-01-22T06:53:01+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5e344f1402584a56631c81a24ec9403e3159c790"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5e344f1402584a56631c81a24ec9403e3159c790", "type": "zip", "shasum": "", "reference": "5e344f1402584a56631c81a24ec9403e3159c790"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.2"}, "time": "2021-12-29T10:10:35+00:00"}, {"version": "v5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "78b69fc4532253f3025db7f2429d8765e506cbf2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/78b69fc4532253f3025db7f2429d8765e506cbf2", "type": "zip", "shasum": "", "reference": "78b69fc4532253f3025db7f2429d8765e506cbf2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.1"}, "time": "2021-12-01T15:04:08+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b3d99775f5372ff746035e98d6fa00182e832380"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b3d99775f5372ff746035e98d6fa00182e832380", "type": "zip", "shasum": "", "reference": "b3d99775f5372ff746035e98d6fa00182e832380"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.0"}, "time": "2021-11-23T15:26:31+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "23591dc3b72b403efd45ce5e2c5a92b2760fb6b5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/23591dc3b72b403efd45ce5e2c5a92b2760fb6b5", "type": "zip", "shasum": "", "reference": "23591dc3b72b403efd45ce5e2c5a92b2760fb6b5"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.0-BETA3"}, "time": "2021-11-17T15:49:41+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1", "symfony/http-client-contracts": "^2.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "cf4981d0601e7638d407fe0405379d3b31b465ae"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/cf4981d0601e7638d407fe0405379d3b31b465ae", "type": "zip", "shasum": "", "reference": "cf4981d0601e7638d407fe0405379d3b31b465ae"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.0-BETA2"}, "time": "2021-11-12T11:42:37+00:00"}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "29ac6909a1d4697710e53cb6b3f24388133ee6aa"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/29ac6909a1d4697710e53cb6b3f24388133ee6aa", "type": "zip", "shasum": "", "reference": "29ac6909a1d4697710e53cb6b3f24388133ee6aa"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.0-BETA1"}, "time": "2021-11-04T16:48:04+00:00"}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "692eace552581e33f05969180289c93c43911d8e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/692eace552581e33f05969180289c93c43911d8e", "type": "zip", "shasum": "", "reference": "692eace552581e33f05969180289c93c43911d8e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.14"}, "time": "2022-01-22T06:52:49+00:00", "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4.13|^5.1.5", "symfony/process": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6b47b4785bed0b1c3409e1dfab2abbb1f553f224"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6b47b4785bed0b1c3409e1dfab2abbb1f553f224", "type": "zip", "shasum": "", "reference": "6b47b4785bed0b1c3409e1dfab2abbb1f553f224"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.13"}, "time": "2021-12-29T10:09:56+00:00"}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3f55da60fc3c3d6e6a4144ee65a37d23a637107e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3f55da60fc3c3d6e6a4144ee65a37d23a637107e", "type": "zip", "shasum": "", "reference": "3f55da60fc3c3d6e6a4144ee65a37d23a637107e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.11"}, "time": "2021-11-20T16:42:42+00:00"}, {"version": "v5.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "710b69ed4bc9469900ec5ae5c3807b0509bee0dc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/710b69ed4bc9469900ec5ae5c3807b0509bee0dc", "type": "zip", "shasum": "", "reference": "710b69ed4bc9469900ec5ae5c3807b0509bee0dc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.10"}, "time": "2021-10-19T08:32:53+00:00"}, {"version": "v5.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c6370fe2c0a445aedc08f592a6a3149da1fea4c7"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c6370fe2c0a445aedc08f592a6a3149da1fea4c7", "type": "zip", "shasum": "", "reference": "c6370fe2c0a445aedc08f592a6a3149da1fea4c7"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.8"}, "time": "2021-09-07T10:45:28+00:00"}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "da8638ffecefc4e8ba2bc848d7b61a408119b333"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/da8638ffecefc4e8ba2bc848d7b61a408119b333", "type": "zip", "shasum": "", "reference": "da8638ffecefc4e8ba2bc848d7b61a408119b333"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.7"}, "time": "2021-08-28T16:24:37+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "67c177d4df8601d9a71f9d615c52171c98d22d74"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/67c177d4df8601d9a71f9d615c52171c98d22d74", "type": "zip", "shasum": "", "reference": "67c177d4df8601d9a71f9d615c52171c98d22d74"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.4"}, "time": "2021-07-23T15:55:36+00:00"}, {"version": "v5.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "fde4bdb10bf197f932ebccfcb9982881d296fc4c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/fde4bdb10bf197f932ebccfcb9982881d296fc4c", "type": "zip", "shasum": "", "reference": "fde4bdb10bf197f932ebccfcb9982881d296fc4c"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.3"}, "time": "2021-06-24T08:13:00+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/deprecation-contracts": "^2.1", "symfony/http-client-contracts": "^2.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d2464f48482223c7c6826cd8c6ed7929d1ce6093"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d2464f48482223c7c6826cd8c6ed7929d1ce6093", "type": "zip", "shasum": "", "reference": "d2464f48482223c7c6826cd8c6ed7929d1ce6093"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.2"}, "time": "2021-06-12T10:15:17+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ef85ca5fa7a4f9c57592fab49faeccdf22b13136"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ef85ca5fa7a4f9c57592fab49faeccdf22b13136", "type": "zip", "shasum": "", "reference": "ef85ca5fa7a4f9c57592fab49faeccdf22b13136"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ddaf185f8348bfcb69ed652f6bc23a2a28c76283"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ddaf185f8348bfcb69ed652f6bc23a2a28c76283", "type": "zip", "shasum": "", "reference": "ddaf185f8348bfcb69ed652f6bc23a2a28c76283"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.0-RC1"}, "time": "2021-05-15T13:12:37+00:00"}, {"version": "v5.3.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5573d1dd07f6156ba5e4ffb1233797b5390e0b57"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5573d1dd07f6156ba5e4ffb1233797b5390e0b57", "type": "zip", "shasum": "", "reference": "5573d1dd07f6156ba5e4ffb1233797b5390e0b57"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.0-BETA4"}, "time": "2021-05-10T14:44:53+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a408c485b61874e77b678ffb5c2d0fc30377c6eb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a408c485b61874e77b678ffb5c2d0fc30377c6eb", "type": "zip", "shasum": "", "reference": "a408c485b61874e77b678ffb5c2d0fc30377c6eb"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.3.0-BETA1"}, "time": "2021-04-07T16:44:23+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1895ede860a198803395a67738104211508b5ddc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1895ede860a198803395a67738104211508b5ddc", "type": "zip", "shasum": "", "reference": "1895ede860a198803395a67738104211508b5ddc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.12"}, "time": "2021-07-23T15:55:19+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^2.2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.2"}}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b7bfe8436da21fbd056890baab17623bb55cfd3a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b7bfe8436da21fbd056890baab17623bb55cfd3a", "type": "zip", "shasum": "", "reference": "b7bfe8436da21fbd056890baab17623bb55cfd3a"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.11"}, "time": "2021-06-24T08:07:28+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^2.2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a1fef5661ae36f39c1d48cb0672c4591e9452323"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a1fef5661ae36f39c1d48cb0672c4591e9452323", "type": "zip", "shasum": "", "reference": "a1fef5661ae36f39c1d48cb0672c4591e9452323"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.10"}, "time": "2021-05-26T12:52:38+00:00"}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "653dc5e2201659abfa090a7396ce202371787540"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/653dc5e2201659abfa090a7396ce202371787540", "type": "zip", "shasum": "", "reference": "653dc5e2201659abfa090a7396ce202371787540"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.9"}, "time": "2021-05-15T12:42:34+00:00"}, {"version": "v5.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a2baf9c3c5b25e04740cece0e429f0a0013002f2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a2baf9c3c5b25e04740cece0e429f0a0013002f2", "type": "zip", "shasum": "", "reference": "a2baf9c3c5b25e04740cece0e429f0a0013002f2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.8"}, "time": "2021-05-10T14:39:23+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "cdaf3df771d3ea9b05696c9e91281ffd056aff66"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/cdaf3df771d3ea9b05696c9e91281ffd056aff66", "type": "zip", "shasum": "", "reference": "cdaf3df771d3ea9b05696c9e91281ffd056aff66"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.7"}, "time": "2021-04-07T16:27:53+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3c3075467da15bc2edf38d2ac20d34719e794bd8"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3c3075467da15bc2edf38d2ac20d34719e794bd8", "type": "zip", "shasum": "", "reference": "3c3075467da15bc2edf38d2ac20d34719e794bd8"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.6"}, "time": "2021-03-28T09:42:18+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c7d1f35a31ef153a302e3f80336170e1280b983d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c7d1f35a31ef153a302e3f80336170e1280b983d", "type": "zip", "shasum": "", "reference": "c7d1f35a31ef153a302e3f80336170e1280b983d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.4"}, "time": "2021-03-01T00:40:14+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "22cb1a7844fff206cc5186409776e78865405ea5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/22cb1a7844fff206cc5186409776e78865405ea5", "type": "zip", "shasum": "", "reference": "22cb1a7844fff206cc5186409776e78865405ea5"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.3"}, "time": "2021-01-27T10:15:41+00:00", "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}}, {"version": "v5.2.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.2"}}, {"description": "Symfony HttpClient component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a77cbec69ea90dea509beef29b79748c0df33a83"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a77cbec69ea90dea509beef29b79748c0df33a83", "type": "zip", "shasum": "", "reference": "a77cbec69ea90dea509beef29b79748c0df33a83"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.1"}, "time": "2020-12-14T10:56:50+00:00", "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4.13|^5.1.5", "symfony/process": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0"}}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5b9fc5d85a6cec73832ff170ccd468d97dd082d9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5b9fc5d85a6cec73832ff170ccd468d97dd082d9", "type": "zip", "shasum": "", "reference": "5b9fc5d85a6cec73832ff170ccd468d97dd082d9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.0"}, "time": "2020-11-28T13:45:20+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b972e7cfa9ea90f67bec80c0530c14f674de0a41"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b972e7cfa9ea90f67bec80c0530c14f674de0a41", "type": "zip", "shasum": "", "reference": "b972e7cfa9ea90f67bec80c0530c14f674de0a41"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.0-RC2"}, "time": "2020-11-05T20:41:09+00:00"}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "281d6be8fae0c5f9f5dc240e367efb0baf9eda9b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/281d6be8fae0c5f9f5dc240e367efb0baf9eda9b", "type": "zip", "shasum": "", "reference": "281d6be8fae0c5f9f5dc240e367efb0baf9eda9b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:08:07+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2336b1bc248401887d2fafa56e3757d92e50c8c2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2336b1bc248401887d2fafa56e3757d92e50c8c2", "type": "zip", "shasum": "", "reference": "2336b1bc248401887d2fafa56e3757d92e50c8c2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.0-BETA2"}, "time": "2020-10-14T17:08:19+00:00", "extra": {"branch-version": "5.2"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4.13|^5.1.5", "symfony/process": "^4.4|^5.0"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5afabe48e75f179913468fc1669da674391332a3"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5afabe48e75f179913468fc1669da674391332a3", "type": "zip", "shasum": "", "reference": "5afabe48e75f179913468fc1669da674391332a3"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.0-BETA1"}, "time": "2020-10-04T07:03:11+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "82f87fa4b738977937803ab8d52948d490047564"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/82f87fa4b738977937803ab8d52948d490047564", "type": "zip", "shasum": "", "reference": "82f87fa4b738977937803ab8d52948d490047564"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "require-dev": {"amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4.13|^5.1.5", "symfony/process": "^4.4|^5.0"}, "extra": "__unset"}, {"description": "Symfony HttpClient component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "81c60c5844e1e9e4913eb624bcd216f116d4e3e7"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/81c60c5844e1e9e4913eb624bcd216f116d4e3e7", "type": "zip", "shasum": "", "reference": "81c60c5844e1e9e4913eb624bcd216f116d4e3e7"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.10"}, "time": "2020-12-14T10:10:03+00:00", "require-dev": {"amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4.13|^5.1.5", "symfony/process": "^4.4|^5.0"}}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8b236277f97be2f56f79330910ce372293fdc5b4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8b236277f97be2f56f79330910ce372293fdc5b4", "type": "zip", "shasum": "", "reference": "8b236277f97be2f56f79330910ce372293fdc5b4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.9"}, "time": "2020-11-28T13:45:11+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "97a6a1f9f5bb3a6094833107b58a72bc9a9165cc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/97a6a1f9f5bb3a6094833107b58a72bc9a9165cc", "type": "zip", "shasum": "", "reference": "97a6a1f9f5bb3a6094833107b58a72bc9a9165cc"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "df757997ee95101c0ca94c7ea2b76e16a758e0ca"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/df757997ee95101c0ca94c7ea2b76e16a758e0ca", "type": "zip", "shasum": "", "reference": "df757997ee95101c0ca94c7ea2b76e16a758e0ca"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.7"}, "time": "2020-10-02T14:24:03+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4a5f2750b54e3cfc5b6711dd78fdbac6563ee7bf"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4a5f2750b54e3cfc5b6711dd78fdbac6563ee7bf", "type": "zip", "shasum": "", "reference": "4a5f2750b54e3cfc5b6711dd78fdbac6563ee7bf"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.6"}, "time": "2020-09-27T03:44:28+00:00"}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "21c4372e9cd2305313f4d4792d7b9fa7c25ade53"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/21c4372e9cd2305313f4d4792d7b9fa7c25ade53", "type": "zip", "shasum": "", "reference": "21c4372e9cd2305313f4d4792d7b9fa7c25ade53"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.5"}, "time": "2020-09-02T08:02:12+00:00", "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^2.1.1", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "edf00ab1692aa2ad19ba87e58273c825b9f39c93"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/edf00ab1692aa2ad19ba87e58273c825b9f39c93", "type": "zip", "shasum": "", "reference": "edf00ab1692aa2ad19ba87e58273c825b9f39c93"}, "support": {"source": "https://github.com/symfony/http-client/tree/5.1"}, "time": "2020-08-30T07:26:18+00:00", "require-dev": {"amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/process": "^4.4|^5.0"}}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "050dc633a598bdadbd49449500c87e30dabe5c58"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/050dc633a598bdadbd49449500c87e30dabe5c58", "type": "zip", "shasum": "", "reference": "050dc633a598bdadbd49449500c87e30dabe5c58"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.3"}, "time": "2020-07-06T13:23:11+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "aae28b613d7a88e529df46e617f046be0236ab54"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/aae28b613d7a88e529df46e617f046be0236ab54", "type": "zip", "shasum": "", "reference": "aae28b613d7a88e529df46e617f046be0236ab54"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.2"}, "time": "2020-06-11T21:20:02+00:00"}, {"version": "v5.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/http-client/tree/5.1"}}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "63342eabdc6fc6c12e6b18506a207d16687aa33f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/63342eabdc6fc6c12e6b18506a207d16687aa33f", "type": "zip", "shasum": "", "reference": "63342eabdc6fc6c12e6b18506a207d16687aa33f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.1.0"}, "time": "2020-05-30T21:52:37+00:00"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "456d3e078d6280eb7843af63d7e42944c6510448"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/456d3e078d6280eb7843af63d7e42944c6510448", "type": "zip", "shasum": "", "reference": "456d3e078d6280eb7843af63d7e42944c6510448"}, "support": {"source": "https://github.com/symfony/http-client/tree/5.1"}, "time": "2020-05-25T17:37:45+00:00"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1243f899b77446b555694c188664eb14228499c8"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1243f899b77446b555694c188664eb14228499c8", "type": "zip", "shasum": "", "reference": "1243f899b77446b555694c188664eb14228499c8"}, "support": {"source": "https://github.com/symfony/http-client/tree/master"}, "time": "2020-05-16T09:12:54+00:00", "require": {"php": "^7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^2.1.1", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ff12d3fb19fd1b3f4961320d5648de172fab3976"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ff12d3fb19fd1b3f4961320d5648de172fab3976", "type": "zip", "shasum": "", "reference": "ff12d3fb19fd1b3f4961320d5648de172fab3976"}, "time": "2020-05-05T16:19:12+00:00", "require": {"php": "^7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9eec6ed50ea38f562ce0a1fc8a7d96a010d58509"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9eec6ed50ea38f562ce0a1fc8a7d96a010d58509", "type": "zip", "shasum": "", "reference": "9eec6ed50ea38f562ce0a1fc8a7d96a010d58509"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.11"}, "time": "2020-07-05T09:43:09+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}, "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/process": "^4.4|^5.0"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "bb216d38ea79bd797237c340870b3cd3c2397bfd"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/bb216d38ea79bd797237c340870b3cd3c2397bfd", "type": "zip", "shasum": "", "reference": "bb216d38ea79bd797237c340870b3cd3c2397bfd"}, "support": {"source": "https://github.com/symfony/http-client/tree/5.0"}, "time": "2020-06-11T21:19:34+00:00"}, {"version": "v5.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4593ee5769c827fcf655e853c93a36eb54b7d063"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4593ee5769c827fcf655e853c93a36eb54b7d063", "type": "zip", "shasum": "", "reference": "4593ee5769c827fcf655e853c93a36eb54b7d063"}, "time": "2020-05-30T21:52:15+00:00"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "93b41572fbb3b8dd11d4f6f0434bbbbacd8619ab"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/93b41572fbb3b8dd11d4f6f0434bbbbacd8619ab", "type": "zip", "shasum": "", "reference": "93b41572fbb3b8dd11d4f6f0434bbbbacd8619ab"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.8"}, "time": "2020-04-12T16:45:47+00:00", "require": {"php": "^7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "14d386ae55b699ea9a0ddb872fa5f3e35219bba8"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/14d386ae55b699ea9a0ddb872fa5f3e35219bba8", "type": "zip", "shasum": "", "reference": "14d386ae55b699ea9a0ddb872fa5f3e35219bba8"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.7"}, "time": "2020-03-27T16:56:45+00:00"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b643c720858d433d2c376407703df1f874534f07"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b643c720858d433d2c376407703df1f874534f07", "type": "zip", "shasum": "", "reference": "b643c720858d433d2c376407703df1f874534f07"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.6"}, "time": "2020-03-16T16:38:48+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "2edd40250649944775aad5d6b4cc8e164c1e9d72"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/2edd40250649944775aad5d6b4cc8e164c1e9d72", "type": "zip", "shasum": "", "reference": "2edd40250649944775aad5d6b4cc8e164c1e9d72"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.5"}, "time": "2020-02-26T22:30:10+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "4240ae267d89db5b694bdb5712e691b1e24cdc26"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/4240ae267d89db5b694bdb5712e691b1e24cdc26", "type": "zip", "shasum": "", "reference": "4240ae267d89db5b694bdb5712e691b1e24cdc26"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.4"}, "time": "2020-01-31T09:13:47+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "20802daa0c9ad4f78da7d518a924a5666cf3aa4e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/20802daa0c9ad4f78da7d518a924a5666cf3aa4e", "type": "zip", "shasum": "", "reference": "20802daa0c9ad4f78da7d518a924a5666cf3aa4e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.3"}, "time": "2020-01-21T08:40:24+00:00"}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "14e77c6591f7c4c2a6ff86c65af72d3919835adc"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/14e77c6591f7c4c2a6ff86c65af72d3919835adc", "type": "zip", "shasum": "", "reference": "14e77c6591f7c4c2a6ff86c65af72d3919835adc"}, "support": {"source": "https://github.com/symfony/http-client/tree/5.0"}, "time": "2019-12-19T16:01:11+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ee432c2648a309ee8f3c6fe454745e8cbd12deb1"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ee432c2648a309ee8f3c6fe454745e8cbd12deb1", "type": "zip", "shasum": "", "reference": "ee432c2648a309ee8f3c6fe454745e8cbd12deb1"}, "time": "2019-12-01T11:18:54+00:00"}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "e68e5680f2bfda0b17097fde6d28b61d87c757ee"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/e68e5680f2bfda0b17097fde6d28b61d87c757ee", "type": "zip", "shasum": "", "reference": "e68e5680f2bfda0b17097fde6d28b61d87c757ee"}, "support": {"source": "https://github.com/symfony/http-client/tree/v5.0.0"}, "time": "2019-11-21T07:02:40+00:00", "require": {"php": "^7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11"}, "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/service-contracts": "^1.0|^2", "symfony/var-dumper": "^4.4|^5.0"}, "conflict": {"symfony/http-kernel": "<4.4"}}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9b3482917eceaefe70af5a0f4a4189109b4cd759"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9b3482917eceaefe70af5a0f4a4189109b4cd759", "type": "zip", "shasum": "", "reference": "9b3482917eceaefe70af5a0f4a4189109b4cd759"}, "support": {"source": "https://github.com/symfony/http-client/tree/master"}, "time": "2019-11-17T10:12:24+00:00", "require": {"php": "^7.2.9", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11"}, "provide": {"php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "646f1e21bb1c2c0e165f80be750a54a3ab4c4881"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/646f1e21bb1c2c0e165f80be750a54a3ab4c4881", "type": "zip", "shasum": "", "reference": "646f1e21bb1c2c0e165f80be750a54a3ab4c4881"}, "time": "2019-11-08T09:16:28+00:00"}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1"}, {"description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "version": "v4.4.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "da88e5df8a24e7c229c3df4f19f24ac502b8532b"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/da88e5df8a24e7c229c3df4f19f24ac502b8532b", "type": "zip", "shasum": "", "reference": "da88e5df8a24e7c229c3df4f19f24ac502b8532b"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.51"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-04T08:52:02+00:00", "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^1.1.10|^2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2"}, "require-dev": {"guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "php-http/message-factory": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4.13", "symfony/process": "^4.2|^5.0"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1|2.0"}, "extra": "__unset", "conflict": "__unset"}, {"version": "v4.4.49", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0185497cd61440bdf68df7d81241b97a543e9c3f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0185497cd61440bdf68df7d81241b97a543e9c3f", "type": "zip", "shasum": "", "reference": "0185497cd61440bdf68df7d81241b97a543e9c3f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.49"}, "time": "2022-11-03T19:03:45+00:00", "require-dev": {"guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4.13", "symfony/process": "^4.2|^5.0"}}, {"version": "v4.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a3cc0a6e73a40f8ea3424a4ab528209fc4f3c272"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a3cc0a6e73a40f8ea3424a4ab528209fc4f3c272", "type": "zip", "shasum": "", "reference": "a3cc0a6e73a40f8ea3424a4ab528209fc4f3c272"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.47"}, "time": "2022-10-07T09:30:05+00:00"}, {"version": "v4.4.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "095274039721d26d23225d7b7ba441423b1b2413"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/095274039721d26d23225d7b7ba441423b1b2413", "type": "zip", "shasum": "", "reference": "095274039721d26d23225d7b7ba441423b1b2413"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.46"}, "time": "2022-09-04T17:31:03+00:00"}, {"version": "v4.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9f9dd96f2ab29eec1a56ad7593c594075e5b368d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9f9dd96f2ab29eec1a56ad7593c594075e5b368d", "type": "zip", "shasum": "", "reference": "9f9dd96f2ab29eec1a56ad7593c594075e5b368d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.45"}, "time": "2022-08-02T07:15:06+00:00"}, {"version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "900632460c2ed5fa656b13cb911ff8f702918618"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/900632460c2ed5fa656b13cb911ff8f702918618", "type": "zip", "shasum": "", "reference": "900632460c2ed5fa656b13cb911ff8f702918618"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.44"}, "time": "2022-07-27T17:16:03+00:00"}, {"version": "v4.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "0366fe9d67709477e86b45e2e51a34ccf5018d04"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/0366fe9d67709477e86b45e2e51a34ccf5018d04", "type": "zip", "shasum": "", "reference": "0366fe9d67709477e86b45e2e51a34ccf5018d04"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.42"}, "time": "2022-05-17T14:14:05+00:00"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "bad7c3296590c5a69a9ed89e8a51f13c07c34b54"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/bad7c3296590c5a69a9ed89e8a51f13c07c34b54", "type": "zip", "shasum": "", "reference": "bad7c3296590c5a69a9ed89e8a51f13c07c34b54"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.41"}, "time": "2022-04-12T15:19:55+00:00"}, {"version": "v4.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c66fc3b60900359ea10a7b22921c797446783bb3"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c66fc3b60900359ea10a7b22921c797446783bb3", "type": "zip", "shasum": "", "reference": "c66fc3b60900359ea10a7b22921c797446783bb3"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.40"}, "time": "2022-04-01T12:25:39+00:00"}, {"version": "v4.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "40342406a975385c5b21e929df46e3fc0278853d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/40342406a975385c5b21e929df46e3fc0278853d", "type": "zip", "shasum": "", "reference": "40342406a975385c5b21e929df46e3fc0278853d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.39"}, "time": "2022-02-28T13:17:32+00:00"}, {"version": "v4.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a6b1a66575c485a77f18200e5e7566c8aae25300"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a6b1a66575c485a77f18200e5e7566c8aae25300", "type": "zip", "shasum": "", "reference": "a6b1a66575c485a77f18200e5e7566c8aae25300"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.38"}, "time": "2022-02-27T08:45:34+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8129ccd6233338e1d495b7734c003053766cb262"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8129ccd6233338e1d495b7734c003053766cb262", "type": "zip", "shasum": "", "reference": "8129ccd6233338e1d495b7734c003053766cb262"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.37"}, "time": "2022-01-19T13:29:07+00:00"}, {"version": "v4.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "35e2cd1862b9ec2b46ebf050fbb13e285944b6a3"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/35e2cd1862b9ec2b46ebf050fbb13e285944b6a3", "type": "zip", "shasum": "", "reference": "35e2cd1862b9ec2b46ebf050fbb13e285944b6a3"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.36"}, "time": "2021-12-29T09:28:53+00:00"}, {"version": "v4.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6ed0c02fdc21a76966f19b9000de18e688d9ca68"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6ed0c02fdc21a76966f19b9000de18e688d9ca68", "type": "zip", "shasum": "", "reference": "6ed0c02fdc21a76966f19b9000de18e688d9ca68"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.35"}, "time": "2021-11-22T21:43:45+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b80aac7b6385ac6e65ec9d256319dc36601c3c2e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b80aac7b6385ac6e65ec9d256319dc36601c3c2e", "type": "zip", "shasum": "", "reference": "b80aac7b6385ac6e65ec9d256319dc36601c3c2e"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.34"}, "time": "2021-11-19T11:24:24+00:00"}, {"version": "v4.4.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9a5fdf129b522a06a46d13400500d326c41d8a73"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9a5fdf129b522a06a46d13400500d326c41d8a73", "type": "zip", "shasum": "", "reference": "9a5fdf129b522a06a46d13400500d326c41d8a73"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.33"}, "time": "2021-10-18T16:39:13+00:00"}, {"version": "v4.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6b900ffa399e25203f30f79f6f4a56b89eee14c2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6b900ffa399e25203f30f79f6f4a56b89eee14c2", "type": "zip", "shasum": "", "reference": "6b900ffa399e25203f30f79f6f4a56b89eee14c2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.31"}, "time": "2021-09-06T10:00:00+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ade6979785bb799e08912f3104959fb169739462"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ade6979785bb799e08912f3104959fb169739462", "type": "zip", "shasum": "", "reference": "ade6979785bb799e08912f3104959fb169739462"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.27"}, "time": "2021-07-23T15:41:52+00:00"}, {"version": "v4.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "78bd3796452b2e47d585f807dbfca945cfe34a73"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/78bd3796452b2e47d585f807dbfca945cfe34a73", "type": "zip", "shasum": "", "reference": "78bd3796452b2e47d585f807dbfca945cfe34a73"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.26"}, "time": "2021-06-23T20:58:46+00:00", "require": {"php": ">=7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.10|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "00bb90bfb0b3823f700d7251735dced581f9dd90"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/00bb90bfb0b3823f700d7251735dced581f9dd90", "type": "zip", "shasum": "", "reference": "00bb90bfb0b3823f700d7251735dced581f9dd90"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.25"}, "time": "2021-05-26T11:20:16+00:00"}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "30b901a6e645fde5fd66102e9e0c023a8bfc404a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/30b901a6e645fde5fd66102e9e0c023a8bfc404a", "type": "zip", "shasum": "", "reference": "30b901a6e645fde5fd66102e9e0c023a8bfc404a"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.24"}, "time": "2021-05-14T07:58:18+00:00"}, {"version": "v4.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7bac9c0c2d1a293a67c5e1d4b9eb7a9044579013"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7bac9c0c2d1a293a67c5e1d4b9eb7a9044579013", "type": "zip", "shasum": "", "reference": "7bac9c0c2d1a293a67c5e1d4b9eb7a9044579013"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.23"}, "time": "2021-05-10T14:26:32+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ad1e2d512ec080e78ebd65c01ab92bd78057d007"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ad1e2d512ec080e78ebd65c01ab92bd78057d007", "type": "zip", "shasum": "", "reference": "ad1e2d512ec080e78ebd65c01ab92bd78057d007"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.22"}, "time": "2021-04-12T06:52:04+00:00"}, {"version": "v4.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "911177e186b82e5b9a9f41c13af53699b6745657"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/911177e186b82e5b9a9f41c13af53699b6745657", "type": "zip", "shasum": "", "reference": "911177e186b82e5b9a9f41c13af53699b6745657"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.21"}, "time": "2021-03-25T17:52:07+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "67c5af7489b3c2eea771abd973243f5c58f5fb40"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/67c5af7489b3c2eea771abd973243f5c58f5fb40", "type": "zip", "shasum": "", "reference": "67c5af7489b3c2eea771abd973243f5c58f5fb40"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.20"}, "time": "2021-02-25T18:06:45+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d8df50fe9229576b254c6822eb5cfff36c02c967"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d8df50fe9229576b254c6822eb5cfff36c02c967", "type": "zip", "shasum": "", "reference": "d8df50fe9229576b254c6822eb5cfff36c02c967"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00", "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}}, {"description": "Symfony HttpClient component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "dc518f62677944938026746e6e58ac37ebcc6238"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/dc518f62677944938026746e6e58ac37ebcc6238", "type": "zip", "shasum": "", "reference": "dc518f62677944938026746e6e58ac37ebcc6238"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.18"}, "time": "2020-12-05T06:03:08+00:00", "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4.13", "symfony/process": "^4.2|^5.0"}}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7196e9b38e4d15947e664f6f455614d4ede6ca17"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7196e9b38e4d15947e664f6f455614d4ede6ca17", "type": "zip", "shasum": "", "reference": "7196e9b38e4d15947e664f6f455614d4ede6ca17"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.17"}, "time": "2020-11-28T13:23:02+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "3ead7e297f4cc8a84661ef1f411c029acb34bc11"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/3ead7e297f4cc8a84661ef1f411c029acb34bc11", "type": "zip", "shasum": "", "reference": "3ead7e297f4cc8a84661ef1f411c029acb34bc11"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b1cb966898aaf8df37280fde537a27b6724b3bc4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b1cb966898aaf8df37280fde537a27b6724b3bc4", "type": "zip", "shasum": "", "reference": "b1cb966898aaf8df37280fde537a27b6724b3bc4"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.15"}, "time": "2020-10-02T13:41:48+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "00a61220bb0439ce71d48d375b15be7a23f9ea98"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/00a61220bb0439ce71d48d375b15be7a23f9ea98", "type": "zip", "shasum": "", "reference": "00a61220bb0439ce71d48d375b15be7a23f9ea98"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.14"}, "time": "2020-09-24T13:25:24+00:00"}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "1d06c290f2875cc87ecf64ecd33e62f857530ce4"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/1d06c290f2875cc87ecf64ecd33e62f857530ce4", "type": "zip", "shasum": "", "reference": "1d06c290f2875cc87ecf64ecd33e62f857530ce4"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.4"}, "time": "2020-09-02T08:01:15+00:00", "require": {"php": ">=7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v4.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6a3af6c202a6a1c7fcf0ddf6de573a426de82dbb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6a3af6c202a6a1c7fcf0ddf6de573a426de82dbb", "type": "zip", "shasum": "", "reference": "6a3af6c202a6a1c7fcf0ddf6de573a426de82dbb"}, "time": "2020-08-30T07:21:38+00:00", "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/process": "^4.2|^5.0"}}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c71448cfd772db2334e30f993c9011e14677f604"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c71448cfd772db2334e30f993c9011e14677f604", "type": "zip", "shasum": "", "reference": "c71448cfd772db2334e30f993c9011e14677f604"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.11"}, "time": "2020-07-04T09:37:14+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "b141d8dd0c2dca86287e33c3dcab079a778ed479"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/b141d8dd0c2dca86287e33c3dcab079a778ed479", "type": "zip", "shasum": "", "reference": "b141d8dd0c2dca86287e33c3dcab079a778ed479"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.4"}, "time": "2020-06-10T15:33:47+00:00"}, {"version": "v4.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d850d6ee955aaa18f05e6df9a11c393c42d45fe0"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d850d6ee955aaa18f05e6df9a11c393c42d45fe0", "type": "zip", "shasum": "", "reference": "d850d6ee955aaa18f05e6df9a11c393c42d45fe0"}, "time": "2020-05-30T21:49:23+00:00"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "88d1745f4095727b8bf0574a0f414331f4ec229c"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/88d1745f4095727b8bf0574a0f414331f4ec229c", "type": "zip", "shasum": "", "reference": "88d1745f4095727b8bf0574a0f414331f4ec229c"}, "time": "2020-04-12T16:14:02+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}}, {"version": "v4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9a8f5c968dc68d58044f8e9ff39d03074489b55d"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9a8f5c968dc68d58044f8e9ff39d03074489b55d", "type": "zip", "shasum": "", "reference": "9a8f5c968dc68d58044f8e9ff39d03074489b55d"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.7"}, "time": "2020-03-27T16:54:36+00:00"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "d24b4bbd62e73dc274cd7c4258e6d7a70167cb31"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/d24b4bbd62e73dc274cd7c4258e6d7a70167cb31", "type": "zip", "shasum": "", "reference": "d24b4bbd62e73dc274cd7c4258e6d7a70167cb31"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.4"}, "time": "2020-03-16T16:13:17+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "65ccb6ca4a5a7bf7c244b8a28195fa188521b3d2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/65ccb6ca4a5a7bf7c244b8a28195fa188521b3d2", "type": "zip", "shasum": "", "reference": "65ccb6ca4a5a7bf7c244b8a28195fa188521b3d2"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.5"}, "time": "2020-02-25T13:55:11+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "250c5363e4d67f8e3c9cdf3362f134e040e69612"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/250c5363e4d67f8e3c9cdf3362f134e040e69612", "type": "zip", "shasum": "", "reference": "250c5363e4d67f8e3c9cdf3362f134e040e69612"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.4"}, "time": "2020-01-31T09:11:17+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "a7f82a1590e2f146b9bb44c6323b21f091fb9e00"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/a7f82a1590e2f146b9bb44c6323b21f091fb9e00", "type": "zip", "shasum": "", "reference": "a7f82a1590e2f146b9bb44c6323b21f091fb9e00"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.3"}, "time": "2020-01-21T07:39:36+00:00"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9ebfc77b5018a05226b38642def82746f3e2ce0f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9ebfc77b5018a05226b38642def82746f3e2ce0f", "type": "zip", "shasum": "", "reference": "9ebfc77b5018a05226b38642def82746f3e2ce0f"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.2"}, "time": "2019-12-19T15:57:49+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "6c235cf15d8b516b41204a28d555685191409729"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/6c235cf15d8b516b41204a28d555685191409729", "type": "zip", "shasum": "", "reference": "6c235cf15d8b516b41204a28d555685191409729"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.1"}, "time": "2019-11-29T16:55:58+00:00"}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "233f73babb157deac7289119aa3d0e871bac8def"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/233f73babb157deac7289119aa3d0e871bac8def", "type": "zip", "shasum": "", "reference": "233f73babb157deac7289119aa3d0e871bac8def"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.4"}, "time": "2019-11-18T12:47:27+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.8|^2", "symfony/polyfill-php73": "^1.11"}, "require-dev": {"guzzlehttp/promises": "^1.3.1", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/process": "^4.2|^5.0", "symfony/service-contracts": "^1.0|^2"}, "conflict": {"symfony/http-kernel": "<4.4"}}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "e548fdfc876ca4318cf0781670a18635de962384"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/e548fdfc876ca4318cf0781670a18635de962384", "type": "zip", "shasum": "", "reference": "e548fdfc876ca4318cf0781670a18635de962384"}, "time": "2019-11-16T18:59:35+00:00", "provide": {"php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "50c1c0901ea7315bf6230b115f8fa4f1f1a50897"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/50c1c0901ea7315bf6230b115f8fa4f1f1a50897", "type": "zip", "shasum": "", "reference": "50c1c0901ea7315bf6230b115f8fa4f1f1a50897"}, "time": "2019-11-12T17:18:47+00:00"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.0-BETA1"}}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "5d416356d61157b2a81bb0f52a1a81810167ca0e"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/5d416356d61157b2a81bb0f52a1a81810167ca0e", "type": "zip", "shasum": "", "reference": "5d416356d61157b2a81bb0f52a1a81810167ca0e"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.3"}, "time": "2020-01-30T16:47:09+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.7", "symfony/polyfill-php73": "^1.11", "symfony/service-contracts": "^1.0|^2"}, "require-dev": {"nyholm/psr7": "^1.0", "psr/http-client": "^1.0", "symfony/http-kernel": "^4.3", "symfony/process": "^4.2"}, "provide": {"psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1"}, "conflict": "__unset"}, {"version": "v4.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "aa662bb26b8fe6f93620a32779fef3ab958ff840"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/aa662bb26b8fe6f93620a32779fef3ab958ff840", "type": "zip", "shasum": "", "reference": "aa662bb26b8fe6f93620a32779fef3ab958ff840"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.3.10"}, "time": "2020-01-13T17:23:05+00:00"}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "8d25d5816b33c7c5cde0d557af31c924e68d4fb9"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/8d25d5816b33c7c5cde0d557af31c924e68d4fb9", "type": "zip", "shasum": "", "reference": "8d25d5816b33c7c5cde0d557af31c924e68d4fb9"}, "support": {"source": "https://github.com/symfony/http-client/tree/v4.3.9"}, "time": "2019-11-28T14:09:27+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ae3a9cd491f1aadb5583f34a6bda5cca34081ce8"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ae3a9cd491f1aadb5583f34a6bda5cca34081ce8", "type": "zip", "shasum": "", "reference": "ae3a9cd491f1aadb5583f34a6bda5cca34081ce8"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.3"}, "time": "2019-11-08T08:23:45+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.7", "symfony/polyfill-php73": "^1.11"}}, {"version": "v4.3.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/http-client/tree/v4.3.7"}}, {"version": "v4.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9aea44c00dee78844f18c345009ef3f0fb3e1d0f"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9aea44c00dee78844f18c345009ef3f0fb3e1d0f", "type": "zip", "shasum": "", "reference": "9aea44c00dee78844f18c345009ef3f0fb3e1d0f"}, "support": {"source": "https://github.com/symfony/http-client/tree/4.3"}, "time": "2019-10-31T07:19:20+00:00"}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "69d438274718121e1166e7f65c290f891a4c8ddb"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/69d438274718121e1166e7f65c290f891a4c8ddb", "type": "zip", "shasum": "", "reference": "69d438274718121e1166e7f65c290f891a4c8ddb"}, "time": "2019-10-07T10:52:41+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "9a4fa769269ed730196a5c52c742b30600cf1e87"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/9a4fa769269ed730196a5c52c742b30600cf1e87", "type": "zip", "shasum": "", "reference": "9a4fa769269ed730196a5c52c742b30600cf1e87"}, "time": "2019-08-20T14:27:59+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.6", "symfony/polyfill-php73": "^1.11"}}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7759fabfbdf7f57a1d29655550b49c4ff04891a5"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7759fabfbdf7f57a1d29655550b49c4ff04891a5", "type": "zip", "shasum": "", "reference": "7759fabfbdf7f57a1d29655550b49c4ff04891a5"}, "time": "2019-07-24T10:16:23+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.4", "symfony/polyfill-php73": "^1.11"}}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "e903ab079c99eab322fc5c71eb63fc467cd19a2a"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/e903ab079c99eab322fc5c71eb63fc467cd19a2a", "type": "zip", "shasum": "", "reference": "e903ab079c99eab322fc5c71eb63fc467cd19a2a"}, "time": "2019-06-26T07:29:23+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "c453b1bb8e8a8b79c9db65a1bd86822f8c6e2bb7"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/c453b1bb8e8a8b79c9db65a1bd86822f8c6e2bb7", "type": "zip", "shasum": "", "reference": "c453b1bb8e8a8b79c9db65a1bd86822f8c6e2bb7"}, "time": "2019-06-05T13:19:12+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1.3", "symfony/polyfill-php73": "^1.11"}}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "fec81f1da93f50334085b00113d2fb6f34efc801"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/fec81f1da93f50334085b00113d2fb6f34efc801", "type": "zip", "shasum": "", "reference": "fec81f1da93f50334085b00113d2fb6f34efc801"}, "time": "2019-05-28T08:25:44+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/http-client-contracts": "^1.1", "symfony/polyfill-php73": "^1.11"}}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "ec24d5fbbcf5c5a17ec5380c1421a1b44a75c4a2"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/ec24d5fbbcf5c5a17ec5380c1421a1b44a75c4a2", "type": "zip", "shasum": "", "reference": "ec24d5fbbcf5c5a17ec5380c1421a1b44a75c4a2"}, "time": "2019-05-22T04:50:01+00:00", "require": {"php": "^7.1.3", "psr/log": "^1.0", "symfony/contracts": "^1.1", "symfony/polyfill-php73": "^1.11"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/http-client.git", "type": "git", "reference": "7a11d9f0e02369711de806ca74769f25fa366c25"}, "dist": {"url": "https://api.github.com/repos/symfony/http-client/zipball/7a11d9f0e02369711de806ca74769f25fa366c25", "type": "zip", "shasum": "", "reference": "7a11d9f0e02369711de806ca74769f25fa366c25"}, "support": {"source": "https://github.com/symfony/http-client/tree/master"}, "time": "2019-04-28T21:09:39+00:00", "provide": {"psr/http-client-implementation": "1.0", "symfony/http-client-contracts-implementation": "1.1"}}]}, "security-advisories": [{"advisoryId": "PKSA-4k7v-pfvw-nqvp", "affectedVersions": ">=4.3.0,<4.4.0|>=4.4.0,<5.0.0|>=5.0.0,<5.1.0|>=5.1.0,<5.2.0|>=5.2.0,<5.3.0|>=5.3.0,<5.4.0|>=5.4.0,<5.4.47|>=6.0.0,<6.1.0|>=6.1.0,<6.2.0|>=6.2.0,<6.3.0|>=6.3.0,<6.4.0|>=6.4.0,<6.4.15|>=7.0.0,<7.1.0|>=7.1.0,<7.1.8"}], "last-modified": "Sat, 28 Jun 2025 08:29:19 GMT"}