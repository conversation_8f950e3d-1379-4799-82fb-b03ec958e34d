{"minified": "composer/2.0", "packages": {"doctrine/collections": [{"name": "doctrine/collections", "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "keywords": ["collections", "php", "array", "iterators"], "homepage": "https://www.doctrine-project.org/projects/collections.html", "version": "2.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "type": "zip", "shasum": "", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "type": "library", "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "require": {"php": "^8.1", "doctrine/deprecations": "^1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"ext-json": "*", "doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "d8af7f248c74f195f7347424600fd9e17b57af59"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/d8af7f248c74f195f7347424600fd9e17b57af59", "type": "zip", "shasum": "", "reference": "d8af7f248c74f195f7347424600fd9e17b57af59"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.2.2"}, "time": "2024-04-18T06:56:21+00:00", "require": {"php": "^8.1", "doctrine/deprecations": "^1"}, "require-dev": {"ext-json": "*", "doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.11"}}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "420480fc085bc65f3c956af13abe8e7546f94813"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/420480fc085bc65f3c956af13abe8e7546f94813", "type": "zip", "shasum": "", "reference": "420480fc085bc65f3c956af13abe8e7546f94813"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.2.1"}, "time": "2024-03-05T22:28:45+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "07e16cd7b80a2cffed99e36b541876af172f0257"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/07e16cd7b80a2cffed99e36b541876af172f0257", "type": "zip", "shasum": "", "reference": "07e16cd7b80a2cffed99e36b541876af172f0257"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.2.0"}, "time": "2024-02-25T22:55:36+00:00"}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "68b834890627e96a1daa8874e79ebd79560a5275"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/68b834890627e96a1daa8874e79ebd79560a5275", "type": "zip", "shasum": "", "reference": "68b834890627e96a1daa8874e79ebd79560a5275"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.1.5"}, "time": "2024-02-25T22:46:58+00:00"}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "72328a11443a0de79967104ad36ba7b30bded134"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/72328a11443a0de79967104ad36ba7b30bded134", "type": "zip", "shasum": "", "reference": "72328a11443a0de79967104ad36ba7b30bded134"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.1.4"}, "time": "2023-10-03T09:22:33+00:00", "require-dev": {"ext-json": "*", "doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^5.11"}}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "3023e150f90a38843856147b58190aa8b46cc155"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/3023e150f90a38843856147b58190aa8b46cc155", "type": "zip", "shasum": "", "reference": "3023e150f90a38843856147b58190aa8b46cc155"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.1.3"}, "time": "2023-07-06T15:15:36+00:00", "require-dev": {"ext-json": "*", "doctrine/coding-standard": "^10.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^5.11"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "db8cda536a034337f7dd63febecc713d4957f9ee"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/db8cda536a034337f7dd63febecc713d4957f9ee", "type": "zip", "shasum": "", "reference": "db8cda536a034337f7dd63febecc713d4957f9ee"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.1.2"}, "time": "2022-12-27T23:41:38+00:00", "require-dev": {"ext-json": "*", "doctrine/coding-standard": "^10.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.22"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "425857d49df4812ca9199691d7d7aa66097d94f3"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/425857d49df4812ca9199691d7d7aa66097d94f3", "type": "zip", "shasum": "", "reference": "425857d49df4812ca9199691d7d7aa66097d94f3"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.1.1"}, "time": "2022-12-08T22:30:56+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "6930dc3b23f2470b6bf77342586483b7fa370d0f"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/6930dc3b23f2470b6bf77342586483b7fa370d0f", "type": "zip", "shasum": "", "reference": "6930dc3b23f2470b6bf77342586483b7fa370d0f"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.1.0"}, "time": "2022-11-16T07:10:43+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "d766a450ba818dacc4b1f50a56cfaad4683cefef"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/d766a450ba818dacc4b1f50a56cfaad4683cefef", "type": "zip", "shasum": "", "reference": "d766a450ba818dacc4b1f50a56cfaad4683cefef"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.0.1"}, "time": "2022-09-30T08:03:04+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "fbc051ebfdb0d11560c86491867cb9268a4d1a8e"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/fbc051ebfdb0d11560c86491867cb9268a4d1a8e", "type": "zip", "shasum": "", "reference": "fbc051ebfdb0d11560c86491867cb9268a4d1a8e"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.0.0"}, "funding": [], "time": "2022-09-28T05:46:46+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "type": "zip", "shasum": "", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00", "require": {"php": "^7.1.3 || ^8.0", "doctrine/deprecations": "^0.5.3 || ^1"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}}, {"version": "1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "09dde3eb237756190f2de738d3c97cff10a8407b"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/09dde3eb237756190f2de738d3c97cff10a8407b", "type": "zip", "shasum": "", "reference": "09dde3eb237756190f2de738d3c97cff10a8407b"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.7.3"}, "time": "2022-09-01T19:34:23+00:00"}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "3fe77330f5591108bbf1315da7377a7e704ed8a0"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/3fe77330f5591108bbf1315da7377a7e704ed8a0", "type": "zip", "shasum": "", "reference": "3fe77330f5591108bbf1315da7377a7e704ed8a0"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.7.2"}, "time": "2022-08-27T16:08:58+00:00"}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "d93dbc5ef2e6e98a5b16ca5b07c3e15de9fd9113"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/d93dbc5ef2e6e98a5b16ca5b07c3e15de9fd9113", "type": "zip", "shasum": "", "reference": "d93dbc5ef2e6e98a5b16ca5b07c3e15de9fd9113"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.7.1"}, "time": "2022-08-23T18:47:41+00:00", "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "07d15c8a766e664ec271ae84e5dfc597aeeb03b1"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/07d15c8a766e664ec271ae84e5dfc597aeeb03b1", "type": "zip", "shasum": "", "reference": "07d15c8a766e664ec271ae84e5dfc597aeeb03b1"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.7.0"}, "time": "2022-08-18T05:44:45+00:00"}, {"version": "1.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "1269d42c03f8a1cc9b56a1b727568423268a191b"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/1269d42c03f8a1cc9b56a1b727568423268a191b", "type": "zip", "shasum": "", "reference": "1269d42c03f8a1cc9b56a1b727568423268a191b"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.9"}, "time": "2022-08-15T12:08:20+00:00", "require": {"php": "^7.1.3 || ^8.0"}}, {"version": "1.6.8", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/1958a744696c6bb3bb0d28db2611dc11610e78af", "type": "zip", "shasum": "", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.8"}, "time": "2021-08-10T18:51:53+00:00", "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^0.12", "vimeo/psalm": "^4.2.1"}}, {"version": "1.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "55f8b799269a1a472457bd1a41b4f379d4cfba4a"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/55f8b799269a1a472457bd1a41b4f379d4cfba4a", "type": "zip", "shasum": "", "reference": "55f8b799269a1a472457bd1a41b4f379d4cfba4a"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.7"}, "time": "2020-07-27T17:53:49+00:00", "require-dev": {"phpunit/phpunit": "^7.0", "doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "vimeo/psalm": "^3.8.1"}}, {"version": "1.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "5f0470363ff042d0057006ae7acabc5d7b5252d5"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/5f0470363ff042d0057006ae7acabc5d7b5252d5", "type": "zip", "shasum": "", "reference": "5f0470363ff042d0057006ae7acabc5d7b5252d5"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.6"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2020-06-22T19:14:02+00:00"}, {"version": "1.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "fc0206348e17e530d09463fef07ba8968406cd6d"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/fc0206348e17e530d09463fef07ba8968406cd6d", "type": "zip", "shasum": "", "reference": "fc0206348e17e530d09463fef07ba8968406cd6d"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.5"}, "time": "2020-05-25T19:24:35+00:00"}, {"version": "1.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7", "type": "zip", "shasum": "", "reference": "6b1e4b2b66f6d6e49983cebfe23a21b7ccc5b0d7"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.4"}, "time": "2019-11-13T13:07:11+00:00", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "require": {"php": "^7.1.3"}, "require-dev": {"phpunit/phpunit": "^7.0", "doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "vimeo/psalm": "^3.2.2"}, "funding": "__unset"}, {"version": "1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "037ccc5b1c249275f8f6bb48e35f140fd52fd577"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/037ccc5b1c249275f8f6bb48e35f140fd52fd577", "type": "zip", "shasum": "", "reference": "037ccc5b1c249275f8f6bb48e35f140fd52fd577"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6"}, "time": "2019-11-04T15:26:07+00:00"}, {"version": "v1.6.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "c5e0bc17b1620e97c968ac409acbff28b8b850be"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/c5e0bc17b1620e97c968ac409acbff28b8b850be", "type": "zip", "shasum": "", "reference": "c5e0bc17b1620e97c968ac409acbff28b8b850be"}, "time": "2019-06-09T13:48:14+00:00"}, {"version": "v1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "d2ae4ef05e25197343b6a39bae1d3c427a2f6956"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/d2ae4ef05e25197343b6a39bae1d3c427a2f6956", "type": "zip", "shasum": "", "reference": "d2ae4ef05e25197343b6a39bae1d3c427a2f6956"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/v1.6.1"}, "time": "2019-03-25T19:03:48+00:00"}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "acc35813077be497dc7f658af76729fa456444e3"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/acc35813077be497dc7f658af76729fa456444e3", "type": "zip", "shasum": "", "reference": "acc35813077be497dc7f658af76729fa456444e3"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/v1.6.0"}, "time": "2019-03-24T18:21:39+00:00"}, {"description": "Collections Abstraction library", "keywords": ["collections", "iterator", "array"], "homepage": "http://www.doctrine-project.org", "version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "a01ee38fcd999f34d9bfbcee59dbda5105449cbf"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/a01ee38fcd999f34d9bfbcee59dbda5105449cbf", "type": "zip", "shasum": "", "reference": "a01ee38fcd999f34d9bfbcee59dbda5105449cbf"}, "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/master"}, "time": "2017-07-22T10:37:32+00:00", "autoload": {"psr-0": {"Doctrine\\Common\\Collections\\": "lib/"}}, "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^5.7", "doctrine/coding-standard": "~0.1@dev"}}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "1a4fb7e902202c33cce8c55989b945612943c2ba"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/1a4fb7e902202c33cce8c55989b945612943c2ba", "type": "zip", "shasum": "", "reference": "1a4fb7e902202c33cce8c55989b945612943c2ba"}, "time": "2017-01-03T10:49:41+00:00", "require": {"php": "^5.6 || ^7.0"}}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "6c1e4eef75f310ea1b3e30945e9f06e652128b8a"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/6c1e4eef75f310ea1b3e30945e9f06e652128b8a", "type": "zip", "shasum": "", "reference": "6c1e4eef75f310ea1b3e30945e9f06e652128b8a"}, "support": {"source": "https://github.com/doctrine/collections/tree/v1.3.0"}, "time": "2015-04-14T22:21:58+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "4c926609fe198833934600b55d99ff6f59effaf5"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/4c926609fe198833934600b55d99ff6f59effaf5", "type": "zip", "shasum": "", "reference": "4c926609fe198833934600b55d99ff6f59effaf5"}, "support": {"source": "https://github.com/doctrine/collections/tree/v1.2.1"}, "time": "2015-06-16T18:50:45+00:00", "require-dev": "__unset"}, {"version": "v1.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.jwage.com/", "role": "Creator"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>", "homepage": "http://www.instaclick.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "b99c5c46c87126201899afe88ec490a25eedd6a2"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/b99c5c46c87126201899afe88ec490a25eedd6a2", "type": "zip", "shasum": "", "reference": "b99c5c46c87126201899afe88ec490a25eedd6a2"}, "support": {"source": "https://github.com/doctrine/collections/tree/master"}, "time": "2014-02-03T23:07:43+00:00"}, {"version": "v1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "560f29c39cfcfbcd210e5d549d993a39d898b04b"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/560f29c39cfcfbcd210e5d549d993a39d898b04b", "type": "zip", "shasum": "", "reference": "560f29c39cfcfbcd210e5d549d993a39d898b04b"}, "time": "2013-03-07T12:15:54+00:00", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}}, {"version": "v1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/collections.git", "type": "git", "reference": "b1e6852b5e49e94ac818feca3b1a8689daefbffc"}, "dist": {"url": "https://api.github.com/repos/doctrine/collections/zipball/b1e6852b5e49e94ac818feca3b1a8689daefbffc", "type": "zip", "shasum": "", "reference": "b1e6852b5e49e94ac818feca3b1a8689daefbffc"}, "support": {"source": "https://github.com/doctrine/collections/tree/v1.0"}, "time": "2013-01-12T16:36:50+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Sat, 22 Mar 2025 10:18:24 GMT"}