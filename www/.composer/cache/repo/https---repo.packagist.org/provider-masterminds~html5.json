{"minified": "composer/2.0", "packages": {"masterminds/html5": [{"name": "masterminds/html5", "description": "An HTML5 parser and serializer.", "keywords": ["xml", "parser", "HTML5", "html", "dom", "serializer", "querypath"], "homepage": "http://masterminds.github.io/html5-php", "version": "2.9.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "type": "zip", "shasum": "", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "type": "library", "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "funding": [], "time": "2024-03-31T07:05:07+00:00", "autoload": {"psr-4": {"Masterminds\\": "src"}}, "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}}, {"version": "2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "f47dcf3c70c584de14f21143c55d9939631bc6cf"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f47dcf3c70c584de14f21143c55d9939631bc6cf", "type": "zip", "shasum": "", "reference": "f47dcf3c70c584de14f21143c55d9939631bc6cf"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.8.1"}, "time": "2023-05-10T11:58:31+00:00", "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8"}}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "3c5d5a56d56f48a1ca08a0670f0f80c1dad368f3"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/3c5d5a56d56f48a1ca08a0670f0f80c1dad368f3", "type": "zip", "shasum": "", "reference": "3c5d5a56d56f48a1ca08a0670f0f80c1dad368f3"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.8.0"}, "time": "2023-04-26T07:27:39+00:00"}, {"version": "2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "897eb517a343a2281f11bc5556d6548db7d93947"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/897eb517a343a2281f11bc5556d6548db7d93947", "type": "zip", "shasum": "", "reference": "897eb517a343a2281f11bc5556d6548db7d93947"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.6"}, "time": "2022-08-18T16:18:26+00:00", "require": {"ext-ctype": "*", "ext-dom": "*", "ext-libxml": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7"}}, {"version": "2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "f640ac1bdddff06ea333a920c95bbad8872429ab"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f640ac1bdddff06ea333a920c95bbad8872429ab", "type": "zip", "shasum": "", "reference": "f640ac1bdddff06ea333a920c95bbad8872429ab"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.5"}, "time": "2021-07-01T14:25:37+00:00"}, {"version": "2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "9227822783c75406cfe400984b2f095cdf03d417"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/9227822783c75406cfe400984b2f095cdf03d417", "type": "zip", "shasum": "", "reference": "9227822783c75406cfe400984b2f095cdf03d417"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.4"}, "time": "2020-10-01T13:52:52+00:00", "require-dev": {"phpunit/phpunit": "^4.8.35"}}, {"version": "2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "aad73dbfefd71d46072138109ce1288d96c329cc"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/aad73dbfefd71d46072138109ce1288d96c329cc", "type": "zip", "shasum": "", "reference": "aad73dbfefd71d46072138109ce1288d96c329cc"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.3"}, "time": "2020-07-05T07:53:37+00:00", "require-dev": {"satooshi/php-coveralls": "1.0.*", "phpunit/phpunit": "^4.8.35", "sami/sami": "~2.0"}}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "6c5dea561d99641f66caed6a6d3b8827a8052205"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/6c5dea561d99641f66caed6a6d3b8827a8052205", "type": "zip", "shasum": "", "reference": "6c5dea561d99641f66caed6a6d3b8827a8052205"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/master"}, "time": "2020-07-01T09:47:09+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "a3edfe52f9e7380e498d33157e1330e85386645d"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/a3edfe52f9e7380e498d33157e1330e85386645d", "type": "zip", "shasum": "", "reference": "a3edfe52f9e7380e498d33157e1330e85386645d"}, "time": "2020-02-06T11:39:04+00:00"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "104443ad663d15981225f99532ba73c2f1d6b6f2"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/104443ad663d15981225f99532ba73c2f1d6b6f2", "type": "zip", "shasum": "", "reference": "104443ad663d15981225f99532ba73c2f1d6b6f2"}, "time": "2019-07-25T07:03:26+00:00", "funding": "__unset"}, {"version": "2.6.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "c961ca6a0a81dc6b55b6859b3f9ea7f402edf9ad"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/c961ca6a0a81dc6b55b6859b3f9ea7f402edf9ad", "type": "zip", "shasum": "", "reference": "c961ca6a0a81dc6b55b6859b3f9ea7f402edf9ad"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.6.0"}, "time": "2019-03-10T11:41:28+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "b5d892a4bd058d61f736935d32a9c248f11ccc93"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/b5d892a4bd058d61f736935d32a9c248f11ccc93", "type": "zip", "shasum": "", "reference": "b5d892a4bd058d61f736935d32a9c248f11ccc93"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/master"}, "time": "2018-12-27T22:03:43+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "cadcfaaa13153e0e8eb92c49a53e140cf1a85dea"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/cadcfaaa13153e0e8eb92c49a53e140cf1a85dea", "type": "zip", "shasum": "", "reference": "cadcfaaa13153e0e8eb92c49a53e140cf1a85dea"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.x"}, "time": "2018-11-17T20:24:36+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require-dev": {"satooshi/php-coveralls": "1.0.*", "phpunit/phpunit": "4.*", "sami/sami": "~2.0"}}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "33f8d475d28741398be26cdff7a10a63003324a3"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/33f8d475d28741398be26cdff7a10a63003324a3", "type": "zip", "shasum": "", "reference": "33f8d475d28741398be26cdff7a10a63003324a3"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.3.1"}, "time": "2018-10-22T16:58:34+00:00", "require": {"ext-libxml": "*", "php": ">=5.3.0"}}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "2c37c6c520b995b761674de3be8455a381679067"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/2c37c6c520b995b761674de3be8455a381679067", "type": "zip", "shasum": "", "reference": "2c37c6c520b995b761674de3be8455a381679067"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.x"}, "time": "2017-09-04T12:26:28+00:00"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "7866e93dcf0245de22378414e0c2c7350abc45af"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/7866e93dcf0245de22378414e0c2c7350abc45af", "type": "zip", "shasum": "", "reference": "7866e93dcf0245de22378414e0c2c7350abc45af"}, "time": "2016-09-22T11:01:11+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "170aa5cb35b29fccafbf5ea63487c013f396fdc9"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/170aa5cb35b29fccafbf5ea63487c013f396fdc9", "type": "zip", "shasum": "", "reference": "170aa5cb35b29fccafbf5ea63487c013f396fdc9"}, "time": "2016-05-10T14:11:45+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "cb1ce51f817af9bbf400c88e8dcebcbf9a097077"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/cb1ce51f817af9bbf400c88e8dcebcbf9a097077", "type": "zip", "shasum": "", "reference": "cb1ce51f817af9bbf400c88e8dcebcbf9a097077"}, "time": "2016-04-11T13:54:57+00:00"}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "8f782e0f01a6e33a319bdc8f6de9cfd6569979a4"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/8f782e0f01a6e33a319bdc8f6de9cfd6569979a4", "type": "zip", "shasum": "", "reference": "8f782e0f01a6e33a319bdc8f6de9cfd6569979a4"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/master"}, "time": "2015-06-07T08:43:18+00:00", "require-dev": {"satooshi/php-coveralls": "0.6.*", "phpunit/phpunit": "4.*", "sami/sami": "~2.0"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "28490e9e0caea10f5ca09ab68f88d1f26e80ff9d"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/28490e9e0caea10f5ca09ab68f88d1f26e80ff9d", "type": "zip", "shasum": "", "reference": "28490e9e0caea10f5ca09ab68f88d1f26e80ff9d"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.1.1"}, "time": "2015-03-23T22:56:43+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "a10f8d392e1aad0b500f7b440c8f0d3bc9189704"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/a10f8d392e1aad0b500f7b440c8f0d3bc9189704", "type": "zip", "shasum": "", "reference": "a10f8d392e1aad0b500f7b440c8f0d3bc9189704"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/master"}, "time": "2015-02-09T16:26:00+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "398ebb68c9395a67858d230d0610aa3676bebdc7"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/398ebb68c9395a67858d230d0610aa3676bebdc7", "type": "zip", "shasum": "", "reference": "398ebb68c9395a67858d230d0610aa3676bebdc7"}, "time": "2014-12-17T20:31:54+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "e1e579a69254e8e20d6270b5c13c4475f8461438"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/e1e579a69254e8e20d6270b5c13c4475f8461438", "type": "zip", "shasum": "", "reference": "e1e579a69254e8e20d6270b5c13c4475f8461438"}, "time": "2014-09-23T09:49:02+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "2cad9c173f14eb390f78100dd74ef005f67e1fa5"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/2cad9c173f14eb390f78100dd74ef005f67e1fa5", "type": "zip", "shasum": "", "reference": "2cad9c173f14eb390f78100dd74ef005f67e1fa5"}, "time": "2014-07-28T06:21:18+00:00", "require-dev": {"satooshi/php-coveralls": "0.6.*", "phpunit/phpunit": "4.*", "phpdocumentor/phpdocumentor": "2.*"}}, {"version": "1.0.7", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "d7b07f77d0ac4f77a5e0487e0682012f64f4ce23"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/d7b07f77d0ac4f77a5e0487e0682012f64f4ce23", "type": "zip", "shasum": "", "reference": "d7b07f77d0ac4f77a5e0487e0682012f64f4ce23"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/1.0"}, "time": "2015-02-06T20:28:48+00:00", "autoload": {"psr-0": {"HTML5": "src"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "ea9eafcfdd1904292cd91b92e8f35dbdf70bd9f8"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/ea9eafcfdd1904292cd91b92e8f35dbdf70bd9f8", "type": "zip", "shasum": "", "reference": "ea9eafcfdd1904292cd91b92e8f35dbdf70bd9f8"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/1.0.6"}, "time": "2015-01-21T14:15:11+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mattfarina.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "09b7bdafd2605a6e9407d6f931e6326e8813beb2"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/09b7bdafd2605a6e9407d6f931e6326e8813beb2", "type": "zip", "shasum": "", "reference": "09b7bdafd2605a6e9407d6f931e6326e8813beb2"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/1.0.5"}, "time": "2014-06-11T00:40:56+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "56c6eac664e7ff446b25db583947b155a94e1584"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/56c6eac664e7ff446b25db583947b155a94e1584", "type": "zip", "shasum": "", "reference": "56c6eac664e7ff446b25db583947b155a94e1584"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/master"}, "time": "2014-04-29T15:14:29+00:00", "require-dev": {"satooshi/php-coveralls": "0.6.*", "phpunit/phpunit": "4.*", "phpdocumentor/phpdocumentor": "2.1.*"}, "extra": "__unset"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "7443b4bae591b64ae76b8e2021763749f62749bd"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/7443b4bae591b64ae76b8e2021763749f62749bd", "type": "zip", "shasum": "", "reference": "7443b4bae591b64ae76b8e2021763749f62749bd"}, "time": "2014-02-28T19:10:00+00:00", "require-dev": {"satooshi/php-coveralls": "0.6.*", "phpunit/phpunit": "*", "phpdocumentor/phpdocumentor": "2.1.*"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "4b3da0978f5a77d4ac45daf3fb04fbe438701fe6"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/4b3da0978f5a77d4ac45daf3fb04fbe438701fe6", "type": "zip", "shasum": "", "reference": "4b3da0978f5a77d4ac45daf3fb04fbe438701fe6"}, "time": "2014-02-12T15:33:18+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "176c8593729313f6d8ab710271fe354b92f7e1b4"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/176c8593729313f6d8ab710271fe354b92f7e1b4", "type": "zip", "shasum": "", "reference": "176c8593729313f6d8ab710271fe354b92f7e1b4"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/1.0.1"}, "time": "2013-11-07T12:57:26+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "c8d2617657ab014244488594674c814933247578"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/c8d2617657ab014244488594674c814933247578", "type": "zip", "shasum": "", "reference": "c8d2617657ab014244488594674c814933247578"}, "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/master"}, "time": "2013-10-02T12:16:50+00:00", "require-dev": "__unset"}, {"homepage": "https://github.com/Masterminds/html5-php", "version": "1.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "6453ad37f5e2dde8bbf911fec6e56eed510fd3d6"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/6453ad37f5e2dde8bbf911fec6e56eed510fd3d6", "type": "zip", "shasum": "", "reference": "6453ad37f5e2dde8bbf911fec6e56eed510fd3d6"}, "time": "2013-07-01T15:22:42+00:00"}, {"version": "1.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "618ef6c2c06b503aed92ee1177e2ed9c6d3f3741"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/618ef6c2c06b503aed92ee1177e2ed9c6d3f3741", "type": "zip", "shasum": "", "reference": "618ef6c2c06b503aed92ee1177e2ed9c6d3f3741"}, "time": "2013-06-18T13:29:51+00:00"}, {"version": "1.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/Masterminds/html5-php.git", "type": "git", "reference": "b766756c27be53c82268d06a879641ebb1f696aa"}, "dist": {"url": "https://api.github.com/repos/Masterminds/html5-php/zipball/b766756c27be53c82268d06a879641ebb1f696aa", "type": "zip", "shasum": "", "reference": "b766756c27be53c82268d06a879641ebb1f696aa"}, "time": "2013-06-01T12:02:34+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 03 Apr 2024 14:00:49 GMT"}