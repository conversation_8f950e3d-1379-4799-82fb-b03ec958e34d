{"minified": "composer/2.0", "packages": {"spiral/roadrunner": [{"name": "spiral/roadrunner", "description": "RoadRunner: High-performance PHP application server and process manager written in Go and powered with plugins", "keywords": [], "homepage": "https://roadrunner.dev/", "version": "dev-master", "version_normalized": "dev-master", "license": ["MIT"], "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "bad01bdabaabb92a42fc5beb284f7d96334a1c71"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/bad01bdabaabb92a42fc5beb284f7d96334a1c71", "type": "zip", "shasum": "", "reference": "bad01bdabaabb92a42fc5beb284f7d96334a1c71"}, "type": "metapackage", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/master"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-07-05T19:38:33+00:00", "default-branch": true}, {"version": "dev-dependabot/go_modules/github.com/olekukonko/tablewriter-1.0.8", "version_normalized": "dev-dependabot/go_modules/github.com/olekukonko/tablewriter-1.0.8", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "74bc141b0581bce2e7d3fd40ccb99ce9c7739bc6"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/74bc141b0581bce2e7d3fd40ccb99ce9c7739bc6", "type": "zip", "shasum": "", "reference": "74bc141b0581bce2e7d3fd40ccb99ce9c7739bc6"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/dependabot/go_modules/github.com/olekukonko/tablewriter-1.0.8"}, "time": "2025-07-07T15:25:37+00:00", "default-branch": "__unset"}, {"version": "dev-stable", "version_normalized": "dev-stable", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "885c7087efa77380d5109901cf0a4888f611294b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/885c7087efa77380d5109901cf0a4888f611294b", "type": "zip", "shasum": "", "reference": "885c7087efa77380d5109901cf0a4888f611294b"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/master"}, "time": "2025-06-14T22:21:13+00:00"}]}, "last-modified": "Mon, 07 Jul 2025 15:25:45 GMT"}