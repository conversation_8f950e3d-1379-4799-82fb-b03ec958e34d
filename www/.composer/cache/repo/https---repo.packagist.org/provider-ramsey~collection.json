{"minified": "composer/2.0", "packages": {"ramsey/collection": [{"name": "ramsey/collection", "description": "A PHP library for representing and manipulating collections.", "keywords": ["queue", "array", "map", "hash", "set", "collection"], "homepage": "", "version": "2.1.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "type": "zip", "shasum": "", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "type": "library", "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "funding": [], "time": "2025-03-22T05:38:12+00:00", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109", "type": "zip", "shasum": "", "reference": "3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.0"}, "time": "2025-03-02T04:48:29+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5", "type": "zip", "shasum": "", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.0.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-31T21:50:55+00:00", "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "type": "zip", "shasum": "", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "time": "2022-12-27T19:12:24+00:00", "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "cccc74ee5e328031b15640b51056ee8d3bb66c0a"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/cccc74ee5e328031b15640b51056ee8d3bb66c0a", "type": "zip", "shasum": "", "reference": "cccc74ee5e328031b15640b51056ee8d3bb66c0a"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.2.2"}, "time": "2021-10-10T03:01:02+00:00", "require": {"php": "^7.3 || ^8", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/captainhook": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "ergebnis/composer-normalize": "^2.6", "fakerphp/faker": "^1.5", "hamcrest/hamcrest-php": "^2", "jangregor/phpstan-prophecy": "^0.8", "mockery/mockery": "^1.3", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^0.12.32", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5 || ^9", "psy/psysh": "^0.10.4", "slevomat/coding-standard": "^6.3", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.4"}, "extra": "__unset"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "eaca1dc1054ddd10cbd83c1461907bee6fb528fa"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/eaca1dc1054ddd10cbd83c1461907bee6fb528fa", "type": "zip", "shasum": "", "reference": "eaca1dc1054ddd10cbd83c1461907bee6fb528fa"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.2.1"}, "time": "2021-08-06T03:41:06+00:00"}, {"description": "A PHP 7.2+ library for representing and manipulating collections.", "version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "4d85fbcd9d2fb1cfd6f81b2f00e687b1c6056b3a"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/4d85fbcd9d2fb1cfd6f81b2f00e687b1c6056b3a", "type": "zip", "shasum": "", "reference": "4d85fbcd9d2fb1cfd6f81b2f00e687b1c6056b3a"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.2.0"}, "time": "2021-08-05T14:54:37+00:00"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "ab2237657ad99667a5143e32ba2683c8029563d4"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/ab2237657ad99667a5143e32ba2683c8029563d4", "type": "zip", "shasum": "", "reference": "ab2237657ad99667a5143e32ba2683c8029563d4"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.1.4"}, "time": "2021-07-30T00:58:27+00:00", "require": {"php": "^7.2 || ^8"}, "require-dev": {"captainhook/captainhook": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "ergebnis/composer-normalize": "^2.6", "fakerphp/faker": "^1.5", "hamcrest/hamcrest-php": "^2", "jangregor/phpstan-prophecy": "^0.8", "mockery/mockery": "^1.3", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^0.12.32", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5 || ^9", "psy/psysh": "^0.10.4", "slevomat/coding-standard": "^6.3", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.4"}}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "28a5c4ab2f5111db6a60b2b4ec84057e0f43b9c1"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/28a5c4ab2f5111db6a60b2b4ec84057e0f43b9c1", "type": "zip", "shasum": "", "reference": "28a5c4ab2f5111db6a60b2b4ec84057e0f43b9c1"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.1.3"}, "time": "2021-01-21T17:40:04+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "a2a85f56ac8f0f973f0e43fcbad5464355bcfe1f"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/a2a85f56ac8f0f973f0e43fcbad5464355bcfe1f", "type": "zip", "shasum": "", "reference": "a2a85f56ac8f0f973f0e43fcbad5464355bcfe1f"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.1.2"}, "time": "2021-01-21T02:12:46+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "24d93aefb2cd786b7edd9f45b554aea20b28b9b1"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/24d93aefb2cd786b7edd9f45b554aea20b28b9b1", "type": "zip", "shasum": "", "reference": "24d93aefb2cd786b7edd9f45b554aea20b28b9b1"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.1.1"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}], "time": "2020-09-10T20:58:17+00:00", "require-dev": {"captainhook/captainhook": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "ergebnis/composer-normalize": "^2.6", "fzaninotto/faker": "^1.5", "hamcrest/hamcrest-php": "^2", "jangregor/phpstan-prophecy": "^0.6", "mockery/mockery": "^1.3", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^0.12.32", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5", "psy/psysh": "^0.10.4", "slevomat/coding-standard": "^6.3", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^3.12.2"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "044184884e3c803e4cbb6451386cb71562939b18"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/044184884e3c803e4cbb6451386cb71562939b18", "type": "zip", "shasum": "", "reference": "044184884e3c803e4cbb6451386cb71562939b18"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.1.0"}, "time": "2020-08-11T00:57:21+00:00"}, {"homepage": "https://github.com/ramsey/collection", "version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "925ad8cf55ba7a3fc92e332c58fd0478ace3e1ca"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/925ad8cf55ba7a3fc92e332c58fd0478ace3e1ca", "type": "zip", "shasum": "", "reference": "925ad8cf55ba7a3fc92e332c58fd0478ace3e1ca"}, "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection"}, "time": "2020-01-05T00:22:59+00:00", "require": {"php": "^7.2"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "fzaninotto/faker": "^1.5", "jakub-onderka/php-parallel-lint": "^1", "jangregor/phpstan-prophecy": "^0.6", "mockery/mockery": "^1.3", "phpstan/extension-installer": "^1", "phpstan/phpdoc-parser": "0.4.1", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5"}, "funding": "__unset"}, {"keywords": ["array", "map", "hash", "collection"], "version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "c7a212fb072674acd77f7f62041182b545f91bc5"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/c7a212fb072674acd77f7f62041182b545f91bc5", "type": "zip", "shasum": "", "reference": "c7a212fb072674acd77f7f62041182b545f91bc5"}, "time": "2018-12-31T09:36:17+00:00", "require-dev": {"fzaninotto/faker": "^1.5", "jakub-onderka/php-parallel-lint": "^1.0", "jangregor/phpstan-prophecy": "^0.2.0", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.10.3", "phpstan/phpstan-mockery": "^0.10.2", "phpunit/phpunit": "^7.3", "squizlabs/php_codesniffer": "^3.3"}}, {"description": "A collections framework for representing and manipulating collections.", "version": "0.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://benramsey.com"}], "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "ecb0b5423d558d01c2063e31215119f602844e4b"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/ecb0b5423d558d01c2063e31215119f602844e4b", "type": "zip", "shasum": "", "reference": "ecb0b5423d558d01c2063e31215119f602844e4b"}, "time": "2016-05-23T22:24:52+00:00", "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "fzaninotto/faker": "^1.5"}}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "a675c3c15625f5f924e1c894c4779a7aeb20fd25"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/a675c3c15625f5f924e1c894c4779a7aeb20fd25", "type": "zip", "shasum": "", "reference": "a675c3c15625f5f924e1c894c4779a7aeb20fd25"}, "time": "2016-02-22T20:02:30+00:00", "require-dev": {"phpunit/phpunit": "^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "fzaninotto/faker": "^1.5"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "a26013fa9fe3660944dd789c4b78f790a8b0ff62"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/a26013fa9fe3660944dd789c4b78f790a8b0ff62", "type": "zip", "shasum": "", "reference": "a26013fa9fe3660944dd789c4b78f790a8b0ff62"}, "time": "2016-02-05T19:35:01+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/collection.git", "type": "git", "reference": "c7ce6d6a998c26a9299ca40f7737f77d20c24b2d"}, "dist": {"url": "https://api.github.com/repos/ramsey/collection/zipball/c7ce6d6a998c26a9299ca40f7737f77d20c24b2d", "type": "zip", "shasum": "", "reference": "c7ce6d6a998c26a9299ca40f7737f77d20c24b2d"}, "time": "2015-10-27T20:48:32+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 22 Mar 2025 05:39:24 GMT"}