{"minified": "composer/2.0", "packages": {"zircote/swagger-php": [{"name": "zircote/swagger-php", "description": "Generate interactive documentation for your RESTful API using PHP attributes (preferred) or PHPDoc annotations", "keywords": ["json", "rest", "api", "service discovery"], "homepage": "https://github.com/zircote/swagger-php", "version": "5.1.3", "version_normalized": "*******", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "b8ba6bd99805c0ae09a38d1b26c1c92820509bd0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/b8ba6bd99805c0ae09a38d1b26c1c92820509bd0", "type": "zip", "shasum": "", "reference": "b8ba6bd99805c0ae09a38d1b26c1c92820509bd0"}, "type": "library", "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.1.3"}, "funding": [], "time": "2025-05-20T03:35:10+00:00", "autoload": {"psr-4": {"OpenApi\\": "src"}}, "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "bin": ["bin/openapi"], "require": {"php": ">=7.4", "ext-json": "*", "nikic/php-parser": "^4.19 || ^5.0", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": "^5.0 || ^6.0 || ^7.0", "symfony/yaml": "^5.0 || ^6.0 || ^7.0"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^2.0", "friendsofphp/php-cs-fixer": "^3.62.0", "phpstan/phpstan": "^1.6 || ^2.0", "phpunit/phpunit": "^9.0", "rector/rector": "^1.0 || ^2.0", "vimeo/psalm": "^4.30 || ^5.0"}, "suggest": {"doctrine/annotations": "^2.0"}, "conflict": {"symfony/process": ">=6, <6.4.14"}}, {"version": "5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "4d7c5f63c5b5c3afe171ad6500ff761f95ce4175"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/4d7c5f63c5b5c3afe171ad6500ff761f95ce4175", "type": "zip", "shasum": "", "reference": "4d7c5f63c5b5c3afe171ad6500ff761f95ce4175"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.1.2"}, "time": "2025-05-14T08:07:38+00:00"}, {"version": "5.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7a6544c60441ddb5959b91266b3a290dc28537ba"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7a6544c60441ddb5959b91266b3a290dc28537ba", "type": "zip", "shasum": "", "reference": "7a6544c60441ddb5959b91266b3a290dc28537ba"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.1.1"}, "time": "2025-04-27T10:02:08+00:00"}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "a9b953c25f5bd11ea0542636936de04504496bd9"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/a9b953c25f5bd11ea0542636936de04504496bd9", "type": "zip", "shasum": "", "reference": "a9b953c25f5bd11ea0542636936de04504496bd9"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.1.0"}, "time": "2025-04-18T00:35:12+00:00"}, {"description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "version": "5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "18457fa71f753cfd4a2b21916baf329864fdfaa6"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/18457fa71f753cfd4a2b21916baf329864fdfaa6", "type": "zip", "shasum": "", "reference": "18457fa71f753cfd4a2b21916baf329864fdfaa6"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.7"}, "time": "2025-03-19T03:31:11+00:00"}, {"version": "5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "ea60f1439aa4fefba230a4386a403eeb1ee52f08"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/ea60f1439aa4fefba230a4386a403eeb1ee52f08", "type": "zip", "shasum": "", "reference": "ea60f1439aa4fefba230a4386a403eeb1ee52f08"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.6"}, "time": "2025-03-05T19:40:05+00:00"}, {"version": "5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2eb4005840058d8844a0bcc14403932331331068"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2eb4005840058d8844a0bcc14403932331331068", "type": "zip", "shasum": "", "reference": "2eb4005840058d8844a0bcc14403932331331068"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.5"}, "time": "2025-02-24T00:48:00+00:00"}, {"version": "5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "df70c152638cae07d83527e55d8ed185bd2b6753"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/df70c152638cae07d83527e55d8ed185bd2b6753", "type": "zip", "shasum": "", "reference": "df70c152638cae07d83527e55d8ed185bd2b6753"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.4"}, "time": "2025-02-18T22:03:30+00:00"}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7708510b17502a416214148edaa8c9958b23b6cd"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7708510b17502a416214148edaa8c9958b23b6cd", "type": "zip", "shasum": "", "reference": "7708510b17502a416214148edaa8c9958b23b6cd"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.3"}, "time": "2025-01-15T21:02:43+00:00"}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c6956de52edb270da4df2630b938c9ac3e26e42f"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c6956de52edb270da4df2630b938c9ac3e26e42f", "type": "zip", "shasum": "", "reference": "c6956de52edb270da4df2630b938c9ac3e26e42f"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.2"}, "time": "2025-01-09T19:42:31+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2d365300e9bc64b935fca99a8f0bc8fe4314deb4"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2d365300e9bc64b935fca99a8f0bc8fe4314deb4", "type": "zip", "shasum": "", "reference": "2d365300e9bc64b935fca99a8f0bc8fe4314deb4"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.1"}, "time": "2025-01-09T02:31:15+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "a6eeb5990ea7b623a0c85a20aa6e4ef83eacd8f7"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/a6eeb5990ea7b623a0c85a20aa6e4ef83eacd8f7", "type": "zip", "shasum": "", "reference": "a6eeb5990ea7b623a0c85a20aa6e4ef83eacd8f7"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/5.0.0"}, "time": "2025-01-07T21:13:11+00:00", "require": {"php": ">=7.4", "ext-json": "*", "nikic/php-parser": "^4.19", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": "^5.0 || ^6.0 || ^7.0", "symfony/yaml": "^5.0 || ^6.0 || ^7.0"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^2.0", "friendsofphp/php-cs-fixer": "^3.62.0", "phpstan/phpstan": "^1.6", "phpunit/phpunit": "^9.0", "rector/rector": "^1.0", "vimeo/psalm": "^4.30"}}, {"version": "4.11.1", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7df10e8ec47db07c031db317a25bef962b4e5de1"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7df10e8ec47db07c031db317a25bef962b4e5de1", "type": "zip", "shasum": "", "reference": "7df10e8ec47db07c031db317a25bef962b4e5de1"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.11.1"}, "time": "2024-10-15T19:20:02+00:00", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "require": {"php": ">=7.2", "ext-json": "*", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.7 || ^2.0", "friendsofphp/php-cs-fixer": "^2.17 || 3.62.0", "phpstan/phpstan": "^1.6", "phpunit/phpunit": ">=8", "vimeo/psalm": "^4.23"}, "suggest": {"doctrine/annotations": "^1.7 || ^2.0"}, "conflict": "__unset"}, {"version": "4.11.0", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "3b6f3800f4fd6544ada4dce180c6b69eaead7c7c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/3b6f3800f4fd6544ada4dce180c6b69eaead7c7c", "type": "zip", "shasum": "", "reference": "3b6f3800f4fd6544ada4dce180c6b69eaead7c7c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.11.0"}, "time": "2024-10-09T03:11:12+00:00"}, {"version": "4.10.7", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "589821f0f1e2c083443749b52a31f077c524dbe6"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/589821f0f1e2c083443749b52a31f077c524dbe6", "type": "zip", "shasum": "", "reference": "589821f0f1e2c083443749b52a31f077c524dbe6"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.7"}, "time": "2024-10-02T01:43:50+00:00"}, {"version": "4.10.6", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e462ff5269ea0ec91070edd5d51dc7215bdea3b6"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e462ff5269ea0ec91070edd5d51dc7215bdea3b6", "type": "zip", "shasum": "", "reference": "e462ff5269ea0ec91070edd5d51dc7215bdea3b6"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.6"}, "time": "2024-07-26T03:04:43+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.7 || ^2.0", "friendsofphp/php-cs-fixer": "^2.17 || ^3.47.1", "phpstan/phpstan": "^1.6", "phpunit/phpunit": ">=8", "vimeo/psalm": "^4.23"}}, {"version": "4.10.5", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "0840dad40fd6759912485f34bfccf13b49b29226"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/0840dad40fd6759912485f34bfccf13b49b29226", "type": "zip", "shasum": "", "reference": "0840dad40fd6759912485f34bfccf13b49b29226"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.5"}, "time": "2024-07-16T03:13:27+00:00"}, {"version": "4.10.4", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "88895afcfaac955ed061489a2b19a431bf58b3e2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/88895afcfaac955ed061489a2b19a431bf58b3e2", "type": "zip", "shasum": "", "reference": "88895afcfaac955ed061489a2b19a431bf58b3e2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.4"}, "time": "2024-07-10T02:06:32+00:00"}, {"version": "4.10.3", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "ad3f913d39b2a4dfb6e59ee4babb35a6b4a2b998"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/ad3f913d39b2a4dfb6e59ee4babb35a6b4a2b998", "type": "zip", "shasum": "", "reference": "ad3f913d39b2a4dfb6e59ee4babb35a6b4a2b998"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.3"}, "time": "2024-07-04T07:53:11+00:00"}, {"version": "4.10.2", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "4756339cbfc774149d9fbd1dcd39cda35366dfe3"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/4756339cbfc774149d9fbd1dcd39cda35366dfe3", "type": "zip", "shasum": "", "reference": "4756339cbfc774149d9fbd1dcd39cda35366dfe3"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.2"}, "time": "2024-07-03T20:49:02+00:00"}, {"version": "4.10.1", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "23822178b22367a60c24988bc832fcd86e74b6b4"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/23822178b22367a60c24988bc832fcd86e74b6b4", "type": "zip", "shasum": "", "reference": "23822178b22367a60c24988bc832fcd86e74b6b4"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.1"}, "time": "2024-07-02T03:56:29+00:00"}, {"version": "4.10.0", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2d983ce67b9eb7e18403ae7bc5e765f8ce7b8d56"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2d983ce67b9eb7e18403ae7bc5e765f8ce7b8d56", "type": "zip", "shasum": "", "reference": "2d983ce67b9eb7e18403ae7bc5e765f8ce7b8d56"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.0"}, "time": "2024-06-06T22:42:02+00:00"}, {"version": "4.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "b1a792e59ebec2219be8356a1b22c8e9f0278fab"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/b1a792e59ebec2219be8356a1b22c8e9f0278fab", "type": "zip", "shasum": "", "reference": "b1a792e59ebec2219be8356a1b22c8e9f0278fab"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.9.3"}, "time": "2024-06-06T00:56:21+00:00"}, {"version": "4.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "256d42cb07ba1c2206d66bc7516ee3d3e3e9f0b2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/256d42cb07ba1c2206d66bc7516ee3d3e3e9f0b2", "type": "zip", "shasum": "", "reference": "256d42cb07ba1c2206d66bc7516ee3d3e3e9f0b2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.9.2"}, "time": "2024-05-02T21:36:00+00:00"}, {"version": "4.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "520ffb7c4fb19d8b150a82d992a00aa72ab9dc3d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/520ffb7c4fb19d8b150a82d992a00aa72ab9dc3d", "type": "zip", "shasum": "", "reference": "520ffb7c4fb19d8b150a82d992a00aa72ab9dc3d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.9.1"}, "time": "2024-04-30T22:01:17+00:00"}, {"version": "4.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "b46a36d006f4db4d761995a5add1e7ab0386ed1d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/b46a36d006f4db4d761995a5add1e7ab0386ed1d", "type": "zip", "shasum": "", "reference": "b46a36d006f4db4d761995a5add1e7ab0386ed1d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.9.0"}, "time": "2024-04-18T22:32:11+00:00"}, {"version": "4.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2357fafbb084be0f9eda7b5c1a659704fed65b28"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2357fafbb084be0f9eda7b5c1a659704fed65b28", "type": "zip", "shasum": "", "reference": "2357fafbb084be0f9eda7b5c1a659704fed65b28"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.7"}, "time": "2024-03-23T06:35:46+00:00"}, {"version": "4.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "fe45a0e98a156999ddafc31c9ad333fdcadbcb2d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/fe45a0e98a156999ddafc31c9ad333fdcadbcb2d", "type": "zip", "shasum": "", "reference": "fe45a0e98a156999ddafc31c9ad333fdcadbcb2d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.6"}, "time": "2024-03-15T01:13:55+00:00"}, {"version": "4.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "038da8ad219f1e9c3f82c5d84c47da3b6f35039c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/038da8ad219f1e9c3f82c5d84c47da3b6f35039c", "type": "zip", "shasum": "", "reference": "038da8ad219f1e9c3f82c5d84c47da3b6f35039c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.5"}, "time": "2024-03-07T23:56:26+00:00"}, {"version": "4.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "bdee7f5a9216ce103ba2c953c1c43c4a3e139e4c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/bdee7f5a9216ce103ba2c953c1c43c4a3e139e4c", "type": "zip", "shasum": "", "reference": "bdee7f5a9216ce103ba2c953c1c43c4a3e139e4c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.4"}, "time": "2024-02-04T21:16:47+00:00"}, {"version": "4.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "598958d8a83cfbd44ba36388b2f9ed69e8b86ed4"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/598958d8a83cfbd44ba36388b2f9ed69e8b86ed4", "type": "zip", "shasum": "", "reference": "598958d8a83cfbd44ba36388b2f9ed69e8b86ed4"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.3"}, "time": "2024-01-07T22:33:09+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.7 || ^2.0", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpstan/phpstan": "^1.6", "phpunit/phpunit": ">=8", "vimeo/psalm": "^4.23"}}, {"version": "4.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "a70a5dc5db26183c86f589ff3d76fb11e141fc58"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/a70a5dc5db26183c86f589ff3d76fb11e141fc58", "type": "zip", "shasum": "", "reference": "a70a5dc5db26183c86f589ff3d76fb11e141fc58"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.2"}, "time": "2023-12-19T19:59:07+00:00"}, {"version": "4.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2a2ae434965f5c497713e05d1398fc9476dfdeb0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2a2ae434965f5c497713e05d1398fc9476dfdeb0", "type": "zip", "shasum": "", "reference": "2a2ae434965f5c497713e05d1398fc9476dfdeb0"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.1"}, "time": "2023-12-11T08:35:08+00:00"}, {"version": "4.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8ea52e3542133faf364bd9295d1a69406b465220"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8ea52e3542133faf364bd9295d1a69406b465220", "type": "zip", "shasum": "", "reference": "8ea52e3542133faf364bd9295d1a69406b465220"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.0"}, "time": "2023-12-04T19:43:43+00:00"}, {"version": "4.7.16", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "21366a8c43819457d458e772a4e86d0df9233b5e"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/21366a8c43819457d458e772a4e86d0df9233b5e", "type": "zip", "shasum": "", "reference": "21366a8c43819457d458e772a4e86d0df9233b5e"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.16"}, "time": "2023-11-30T21:47:39+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7 || ^2.0", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpstan/phpstan": "^1.6", "phpunit/phpunit": ">=8", "vimeo/psalm": "^4.23"}, "suggest": "__unset"}, {"version": "4.7.15", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "df8de8e484003f68cd2fa68db1e6cfb47a3a92cb"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/df8de8e484003f68cd2fa68db1e6cfb47a3a92cb", "type": "zip", "shasum": "", "reference": "df8de8e484003f68cd2fa68db1e6cfb47a3a92cb"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.15"}, "time": "2023-10-12T20:26:34+00:00"}, {"version": "4.7.14", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e53c0c7a6e250c435cc13ab50792247a8eb07da3"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e53c0c7a6e250c435cc13ab50792247a8eb07da3", "type": "zip", "shasum": "", "reference": "e53c0c7a6e250c435cc13ab50792247a8eb07da3"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.14"}, "time": "2023-09-12T00:18:13+00:00"}, {"version": "4.7.13", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e1062da5b47d3a97ee9910301f902b0f5b408340"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e1062da5b47d3a97ee9910301f902b0f5b408340", "type": "zip", "shasum": "", "reference": "e1062da5b47d3a97ee9910301f902b0f5b408340"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.13"}, "time": "2023-08-30T23:32:39+00:00"}, {"version": "4.7.12", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "6279683387b09e6f8fac6f4fb12c4ad6239bce18"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/6279683387b09e6f8fac6f4fb12c4ad6239bce18", "type": "zip", "shasum": "", "reference": "6279683387b09e6f8fac6f4fb12c4ad6239bce18"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.12"}, "time": "2023-08-30T22:56:22+00:00"}, {"version": "4.7.11", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f01574d1ac55cb0bf31a8dd80525ad58eef83afc"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f01574d1ac55cb0bf31a8dd80525ad58eef83afc", "type": "zip", "shasum": "", "reference": "f01574d1ac55cb0bf31a8dd80525ad58eef83afc"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.11"}, "time": "2023-08-03T21:23:48+00:00"}, {"version": "4.7.10", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "6d2f0fcc46bf9043877de8656a9ea95331155522"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/6d2f0fcc46bf9043877de8656a9ea95331155522", "type": "zip", "shasum": "", "reference": "6d2f0fcc46bf9043877de8656a9ea95331155522"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.10"}, "time": "2023-04-28T00:56:39+00:00"}, {"version": "4.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "3727cd85f98039f4242f1be14a01d095069ce71f"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/3727cd85f98039f4242f1be14a01d095069ce71f", "type": "zip", "shasum": "", "reference": "3727cd85f98039f4242f1be14a01d095069ce71f"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.9"}, "time": "2023-04-12T23:40:55+00:00"}, {"version": "4.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "4a1d1272c2dd3afd8d96e92f02dc3565d5cc3c18"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/4a1d1272c2dd3afd8d96e92f02dc3565d5cc3c18", "type": "zip", "shasum": "", "reference": "4a1d1272c2dd3afd8d96e92f02dc3565d5cc3c18"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.8"}, "time": "2023-03-19T21:25:02+00:00"}, {"version": "4.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "1661f99005655b77d0b9c4d93dba12ed9352e3be"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/1661f99005655b77d0b9c4d93dba12ed9352e3be", "type": "zip", "shasum": "", "reference": "1661f99005655b77d0b9c4d93dba12ed9352e3be"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.4"}, "time": "2023-03-17T19:58:25+00:00"}, {"version": "4.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "0299edc47eb4813a2c598a0348eaf205704adc92"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/0299edc47eb4813a2c598a0348eaf205704adc92", "type": "zip", "shasum": "", "reference": "0299edc47eb4813a2c598a0348eaf205704adc92"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.3"}, "time": "2023-03-08T02:27:27+00:00"}, {"version": "4.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "09f0850afaa40eac6dee3e9086c188d0388584ab"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/09f0850afaa40eac6dee3e9086c188d0388584ab", "type": "zip", "shasum": "", "reference": "09f0850afaa40eac6dee3e9086c188d0388584ab"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.2"}, "time": "2023-03-07T02:52:14+00:00"}, {"version": "4.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "de32660ba43f7dd5f47a7a38d33d5d2c1c367d65"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/de32660ba43f7dd5f47a7a38d33d5d2c1c367d65", "type": "zip", "shasum": "", "reference": "de32660ba43f7dd5f47a7a38d33d5d2c1c367d65"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.1"}, "time": "2023-02-25T22:37:20+00:00"}, {"version": "4.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f2bcbd5aa9d838d28bf5665c70f88eda6adc22cb"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f2bcbd5aa9d838d28bf5665c70f88eda6adc22cb", "type": "zip", "shasum": "", "reference": "f2bcbd5aa9d838d28bf5665c70f88eda6adc22cb"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.7.0"}, "time": "2023-02-20T23:05:35+00:00"}, {"version": "4.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "14d6c5e2bde957e03a3a5496a3b18419a0480199"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/14d6c5e2bde957e03a3a5496a3b18419a0480199", "type": "zip", "shasum": "", "reference": "14d6c5e2bde957e03a3a5496a3b18419a0480199"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.6.2"}, "time": "2023-02-14T22:54:06+00:00"}, {"version": "4.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e449670473d7ecaff75dff744e8e9be68a79a44c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e449670473d7ecaff75dff744e8e9be68a79a44c", "type": "zip", "shasum": "", "reference": "e449670473d7ecaff75dff744e8e9be68a79a44c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.6.1"}, "time": "2023-02-03T01:25:00+00:00"}, {"version": "4.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "ee8147745c92dd4404adb531751ce32676b888d4"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/ee8147745c92dd4404adb531751ce32676b888d4", "type": "zip", "shasum": "", "reference": "ee8147745c92dd4404adb531751ce32676b888d4"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.6.0"}, "time": "2023-01-13T00:51:01+00:00"}, {"version": "4.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "6fea22f00b2a0048e96f0a8f228356cbbfc1b930"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/6fea22f00b2a0048e96f0a8f228356cbbfc1b930", "type": "zip", "shasum": "", "reference": "6fea22f00b2a0048e96f0a8f228356cbbfc1b930"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.6"}, "time": "2023-01-12T19:58:53+00:00"}, {"version": "4.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "af672d33ab83ca999a04740cebd88049604ea2ae"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/af672d33ab83ca999a04740cebd88049604ea2ae", "type": "zip", "shasum": "", "reference": "af672d33ab83ca999a04740cebd88049604ea2ae"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.5"}, "time": "2023-01-05T01:20:08+00:00"}, {"version": "4.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "09356f4d68d29bdf3254811fb2602a5d5d1788ea"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/09356f4d68d29bdf3254811fb2602a5d5d1788ea", "type": "zip", "shasum": "", "reference": "09356f4d68d29bdf3254811fb2602a5d5d1788ea"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.4"}, "time": "2023-01-04T00:51:43+00:00"}, {"version": "4.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e505bce612a86fe90f8fd50917e0848afc5d2ba8"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e505bce612a86fe90f8fd50917e0848afc5d2ba8", "type": "zip", "shasum": "", "reference": "e505bce612a86fe90f8fd50917e0848afc5d2ba8"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.3"}, "time": "2022-12-21T18:26:59+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}}, {"version": "4.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8b55d616213cd0b05de84e0fabc34de74a4c6d1d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8b55d616213cd0b05de84e0fabc34de74a4c6d1d", "type": "zip", "shasum": "", "reference": "8b55d616213cd0b05de84e0fabc34de74a4c6d1d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.2"}, "time": "2022-12-19T00:41:18+00:00"}, {"version": "4.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "eb84fb4d65a327e604812fbddc6c27f69b9ed6e2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/eb84fb4d65a327e604812fbddc6c27f69b9ed6e2", "type": "zip", "shasum": "", "reference": "eb84fb4d65a327e604812fbddc6c27f69b9ed6e2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.1"}, "time": "2022-11-09T00:17:13+00:00"}, {"version": "4.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "6bd53355eb6505e1a1825f163dced55c240a3102"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/6bd53355eb6505e1a1825f163dced55c240a3102", "type": "zip", "shasum": "", "reference": "6bd53355eb6505e1a1825f163dced55c240a3102"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.5.0"}, "time": "2022-11-03T19:42:57+00:00"}, {"version": "4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "795be27b3b3bacc8353350cacc663ac5cbcd05cc"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/795be27b3b3bacc8353350cacc663ac5cbcd05cc", "type": "zip", "shasum": "", "reference": "795be27b3b3bacc8353350cacc663ac5cbcd05cc"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.10"}, "time": "2022-10-19T02:28:50+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}}, {"version": "4.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "0c1cdd31e8cfeb7116c54696aafdab9c778070fd"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/0c1cdd31e8cfeb7116c54696aafdab9c778070fd", "type": "zip", "shasum": "", "reference": "0c1cdd31e8cfeb7116c54696aafdab9c778070fd"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.9"}, "time": "2022-09-11T20:51:43+00:00"}, {"version": "4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "34c0980c4cd4f32a1a43f995463001e450d18896"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/34c0980c4cd4f32a1a43f995463001e450d18896", "type": "zip", "shasum": "", "reference": "34c0980c4cd4f32a1a43f995463001e450d18896"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.8"}, "time": "2022-08-16T23:21:13+00:00"}, {"version": "4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "97abe42376b41e072ede13b6f0582457a54d797a"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/97abe42376b41e072ede13b6f0582457a54d797a", "type": "zip", "shasum": "", "reference": "97abe42376b41e072ede13b6f0582457a54d797a"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.7"}, "time": "2022-07-02T04:55:25+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "psr/log": "^1.1 || ^2.0 || 3.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}}, {"version": "4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "41c03aa742657473335fa53e2d44adcf3b97a97d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/41c03aa742657473335fa53e2d44adcf3b97a97d", "type": "zip", "shasum": "", "reference": "41c03aa742657473335fa53e2d44adcf3b97a97d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.6"}, "time": "2022-06-29T01:40:06+00:00"}, {"version": "4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "fd8f1eb8d0165c7a668f307fbccbf3adf33f4017"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/fd8f1eb8d0165c7a668f307fbccbf3adf33f4017", "type": "zip", "shasum": "", "reference": "fd8f1eb8d0165c7a668f307fbccbf3adf33f4017"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.5"}, "time": "2022-06-02T21:05:02+00:00"}, {"version": "4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "fb967b3ef9e311626e7232fa71096763d5f3eec2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/fb967b3ef9e311626e7232fa71096763d5f3eec2", "type": "zip", "shasum": "", "reference": "fb967b3ef9e311626e7232fa71096763d5f3eec2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.4"}, "time": "2022-05-29T06:22:07+00:00"}, {"version": "4.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "05e3cb201dd4b08c5263193bd48841e3a4ca22a0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/05e3cb201dd4b08c5263193bd48841e3a4ca22a0", "type": "zip", "shasum": "", "reference": "05e3cb201dd4b08c5263193bd48841e3a4ca22a0"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.3"}, "time": "2022-05-24T21:21:19+00:00"}, {"version": "4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "1653b53eb3973a9a6dac87ef8f2676801b14469f"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/1653b53eb3973a9a6dac87ef8f2676801b14469f", "type": "zip", "shasum": "", "reference": "1653b53eb3973a9a6dac87ef8f2676801b14469f"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.2"}, "time": "2022-05-13T23:51:07+00:00"}, {"version": "4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "86ab9cb3468a9a6989b14f57d95bb4202aa593ce"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/86ab9cb3468a9a6989b14f57d95bb4202aa593ce", "type": "zip", "shasum": "", "reference": "86ab9cb3468a9a6989b14f57d95bb4202aa593ce"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.1"}, "time": "2022-05-06T09:12:54+00:00"}, {"version": "4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "3eb4e8193b74373251edc1f614f9fe5401a28c0c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/3eb4e8193b74373251edc1f614f9fe5401a28c0c", "type": "zip", "shasum": "", "reference": "3eb4e8193b74373251edc1f614f9fe5401a28c0c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.4.0"}, "time": "2022-05-05T20:23:51+00:00"}, {"version": "4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "24b23371ee962ac201fac33292034ae099c8d4a0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/24b23371ee962ac201fac33292034ae099c8d4a0", "type": "zip", "shasum": "", "reference": "24b23371ee962ac201fac33292034ae099c8d4a0"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.3.0"}, "time": "2022-04-17T01:42:30+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8"}}, {"version": "4.2.15", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2f66ec81d2bc4b82c26b250b187d5e9ea07b0538"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2f66ec81d2bc4b82c26b250b187d5e9ea07b0538", "type": "zip", "shasum": "", "reference": "2f66ec81d2bc4b82c26b250b187d5e9ea07b0538"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.15"}, "time": "2022-04-12T05:49:55+00:00"}, {"version": "4.2.14", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "975c598084a1f8e7acccaa68aed12c803cebbd6a"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/975c598084a1f8e7acccaa68aed12c803cebbd6a", "type": "zip", "shasum": "", "reference": "975c598084a1f8e7acccaa68aed12c803cebbd6a"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.14"}, "time": "2022-04-01T08:53:53+00:00"}, {"version": "4.2.13", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8888655d7dc21eda6ec71e521f71a757605f48fe"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8888655d7dc21eda6ec71e521f71a757605f48fe", "type": "zip", "shasum": "", "reference": "8888655d7dc21eda6ec71e521f71a757605f48fe"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.13"}, "time": "2022-03-23T08:54:27+00:00"}, {"version": "4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "4ad0dc2245b6b603d732630fbea251ac92c630f2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/4ad0dc2245b6b603d732630fbea251ac92c630f2", "type": "zip", "shasum": "", "reference": "4ad0dc2245b6b603d732630fbea251ac92c630f2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.12"}, "time": "2022-03-15T20:43:30+00:00"}, {"version": "4.2.11", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "1c7a04e381b07e14aae080a61e02f2fe9cdea424"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/1c7a04e381b07e14aae080a61e02f2fe9cdea424", "type": "zip", "shasum": "", "reference": "1c7a04e381b07e14aae080a61e02f2fe9cdea424"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.11"}, "time": "2022-03-06T19:16:21+00:00"}, {"version": "4.2.10", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7263e95f5fff5524a697e2a75ceb6f9902453d01"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7263e95f5fff5524a697e2a75ceb6f9902453d01", "type": "zip", "shasum": "", "reference": "7263e95f5fff5524a697e2a75ceb6f9902453d01"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.10"}, "time": "2022-03-04T01:21:38+00:00"}, {"version": "4.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "867cfdcf31fec8bf44b2d7107a2f51b4bab00601"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/867cfdcf31fec8bf44b2d7107a2f51b4bab00601", "type": "zip", "shasum": "", "reference": "867cfdcf31fec8bf44b2d7107a2f51b4bab00601"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.9"}, "time": "2022-02-20T21:16:53+00:00"}, {"version": "4.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "aa691c9ee5d08a8f796da8f1872c215900f8580f"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/aa691c9ee5d08a8f796da8f1872c215900f8580f", "type": "zip", "shasum": "", "reference": "aa691c9ee5d08a8f796da8f1872c215900f8580f"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.8"}, "time": "2022-02-12T01:37:11+00:00"}, {"version": "4.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "ef7ac9107fb2998440948b46610182f398487b6d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/ef7ac9107fb2998440948b46610182f398487b6d", "type": "zip", "shasum": "", "reference": "ef7ac9107fb2998440948b46610182f398487b6d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.7"}, "time": "2022-02-10T19:48:55+00:00"}, {"version": "4.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2d12005ff3b8882e45ece835f0bb5cdccca19929"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2d12005ff3b8882e45ece835f0bb5cdccca19929", "type": "zip", "shasum": "", "reference": "2d12005ff3b8882e45ece835f0bb5cdccca19929"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.6"}, "time": "2022-02-04T21:53:17+00:00"}, {"version": "4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e87cec1ec79926e0de6e920a93fb6142f837a750"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e87cec1ec79926e0de6e920a93fb6142f837a750", "type": "zip", "shasum": "", "reference": "e87cec1ec79926e0de6e920a93fb6142f837a750"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.5"}, "time": "2022-01-31T00:21:28+00:00"}, {"version": "4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f00ba9db74722f029412705419c67bdd98e707fb"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f00ba9db74722f029412705419c67bdd98e707fb", "type": "zip", "shasum": "", "reference": "f00ba9db74722f029412705419c67bdd98e707fb"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.4"}, "time": "2022-01-20T08:28:48+00:00"}, {"version": "4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "27422d180a2a1fef6b0b590d87858f5aa30b45f2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/27422d180a2a1fef6b0b590d87858f5aa30b45f2", "type": "zip", "shasum": "", "reference": "27422d180a2a1fef6b0b590d87858f5aa30b45f2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.3"}, "time": "2022-01-19T01:07:44+00:00"}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "910eda5bc1114d7d203af7788df462b781f98286"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/910eda5bc1114d7d203af7788df462b781f98286", "type": "zip", "shasum": "", "reference": "910eda5bc1114d7d203af7788df462b781f98286"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.2"}, "time": "2022-01-16T05:39:33+00:00"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8f53ded4c90ec7cccb18333c1d7d8a4c16f0c3c8"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8f53ded4c90ec7cccb18333c1d7d8a4c16f0c3c8", "type": "zip", "shasum": "", "reference": "8f53ded4c90ec7cccb18333c1d7d8a4c16f0c3c8"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.1"}, "time": "2022-01-10T22:04:03+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c5a53203caf9a3214fdc2d59a3a9d615c4a2cc5b"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c5a53203caf9a3214fdc2d59a3a9d615c4a2cc5b", "type": "zip", "shasum": "", "reference": "c5a53203caf9a3214fdc2d59a3a9d615c4a2cc5b"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.2.0"}, "time": "2022-01-09T20:34:48+00:00"}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "47bc50f88a8a2be7a3048112344dacd6cfba1926"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/47bc50f88a8a2be7a3048112344dacd6cfba1926", "type": "zip", "shasum": "", "reference": "47bc50f88a8a2be7a3048112344dacd6cfba1926"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.1.1"}, "time": "2022-01-01T02:07:33+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "faafa3535c11c05e17f05f21a541b96a837a55a2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/faafa3535c11c05e17f05f21a541b96a837a55a2", "type": "zip", "shasum": "", "reference": "faafa3535c11c05e17f05f21a541b96a837a55a2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.1.0"}, "time": "2021-12-23T01:28:48+00:00"}, {"version": "4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "31210d89e8c4df86e01a428bdbd2943fc8fc09b2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/31210d89e8c4df86e01a428bdbd2943fc8fc09b2", "type": "zip", "shasum": "", "reference": "31210d89e8c4df86e01a428bdbd2943fc8fc09b2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.5"}, "time": "2021-12-20T01:09:47+00:00"}, {"version": "4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c24704d90fbd296f889744d8da92c67653baef14"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c24704d90fbd296f889744d8da92c67653baef14", "type": "zip", "shasum": "", "reference": "c24704d90fbd296f889744d8da92c67653baef14"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.4"}, "time": "2021-12-10T03:33:50+00:00"}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "6bfe1c422a676c425ce9280f707015f0deebf254"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/6bfe1c422a676c425ce9280f707015f0deebf254", "type": "zip", "shasum": "", "reference": "6bfe1c422a676c425ce9280f707015f0deebf254"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.3"}, "time": "2021-12-10T00:03:49+00:00"}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "cd7cb605c9bf3dca2bf9962552b5b09ede8a573c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/cd7cb605c9bf3dca2bf9962552b5b09ede8a573c", "type": "zip", "shasum": "", "reference": "cd7cb605c9bf3dca2bf9962552b5b09ede8a573c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.2"}, "time": "2021-12-08T19:57:37+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7e9ba95c97715b24176caaa00cba0423dc1e87dd"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7e9ba95c97715b24176caaa00cba0423dc1e87dd", "type": "zip", "shasum": "", "reference": "7e9ba95c97715b24176caaa00cba0423dc1e87dd"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.1"}, "time": "2021-12-06T08:08:01+00:00", "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "dd5f1b7b84ffa86527e984b292c5223aeac5f50f"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/dd5f1b7b84ffa86527e984b292c5223aeac5f50f", "type": "zip", "shasum": "", "reference": "dd5f1b7b84ffa86527e984b292c5223aeac5f50f"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.0"}, "time": "2021-11-28T00:58:14+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "psr/log": "^1.1", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}}, {"version": "4.0.0-RC1", "version_normalized": "*******-RC1", "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.0.0-RC1"}}, {"version": "3.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e8c3bb316e385e93a0c7e8b2aa0681079244c381", "type": "zip", "shasum": "", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.7"}, "time": "2023-01-03T21:17:10+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"OpenApi\\": "src"}}, "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8.5.14"}, "extra": "__unset"}, {"version": "3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "5016342f966fca29dda84455de066c5c90d37941"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/5016342f966fca29dda84455de066c5c90d37941", "type": "zip", "shasum": "", "reference": "5016342f966fca29dda84455de066c5c90d37941"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.6"}, "time": "2022-05-21T01:52:14+00:00"}, {"version": "3.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7313ff7d1991d00e52d0e852087693d4482df631"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7313ff7d1991d00e52d0e852087693d4482df631", "type": "zip", "shasum": "", "reference": "7313ff7d1991d00e52d0e852087693d4482df631"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.5"}, "time": "2022-02-22T21:09:06+00:00"}, {"version": "3.3.4", "version_normalized": "*******", "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.4"}}, {"version": "3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "cec9943f974df43370c51be7c489fc1007f80f2b"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/cec9943f974df43370c51be7c489fc1007f80f2b", "type": "zip", "shasum": "", "reference": "cec9943f974df43370c51be7c489fc1007f80f2b"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.3"}, "time": "2021-12-23T19:45:20+00:00"}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "68c76ce2bb43fb4603315fb973d4595711dcbfd3"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/68c76ce2bb43fb4603315fb973d4595711dcbfd3", "type": "zip", "shasum": "", "reference": "68c76ce2bb43fb4603315fb973d4595711dcbfd3"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.2"}, "time": "2021-11-15T20:45:42+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "psr/log": "^1.1", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8"}}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e8594d3578fa0e871ef9b435a0c0f3631bfa8292"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e8594d3578fa0e871ef9b435a0c0f3631bfa8292", "type": "zip", "shasum": "", "reference": "e8594d3578fa0e871ef9b435a0c0f3631bfa8292"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.1"}, "time": "2021-11-13T23:19:29+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8f2c1ff650c62fa056ec74c5bf7f625e45b2bb8a"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8f2c1ff650c62fa056ec74c5bf7f625e45b2bb8a", "type": "zip", "shasum": "", "reference": "8f2c1ff650c62fa056ec74c5bf7f625e45b2bb8a"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.0"}, "time": "2021-11-08T00:45:29+00:00"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "41ed0eb1dacebe2c365623b3f9ab13d1531a03da"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/41ed0eb1dacebe2c365623b3f9ab13d1531a03da", "type": "zip", "shasum": "", "reference": "41ed0eb1dacebe2c365623b3f9ab13d1531a03da"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.2.3"}, "time": "2021-06-25T04:08:57+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "^1.7", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8"}}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f3abeb79e075694b7f4b4500bd3566d88f53a486"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f3abeb79e075694b7f4b4500bd3566d88f53a486", "type": "zip", "shasum": "", "reference": "f3abeb79e075694b7f4b4500bd3566d88f53a486"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.2.2"}, "time": "2021-06-23T21:21:24+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": ">=8"}}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "eef1dd98e6f6071573908fd7007270510051e5f9"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/eef1dd98e6f6071573908fd7007270510051e5f9", "type": "zip", "shasum": "", "reference": "eef1dd98e6f6071573908fd7007270510051e5f9"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.2.1"}, "time": "2021-05-20T21:46:24+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c5f0819764f9bd8a246732d662b95ce533c0479f"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c5f0819764f9bd8a246732d662b95ce533c0479f", "type": "zip", "shasum": "", "reference": "c5f0819764f9bd8a246732d662b95ce533c0479f"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.2.0"}, "time": "2021-05-20T00:45:34+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "9d172471e56433b5c7061006b9a766f262a3edfd"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/9d172471e56433b5c7061006b9a766f262a3edfd", "type": "zip", "shasum": "", "reference": "9d172471e56433b5c7061006b9a766f262a3edfd"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.1.0"}, "time": "2020-09-03T20:18:43+00:00", "require": {"php": ">=7.2", "ext-json": "*", "doctrine/annotations": "*", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}}, {"version": "3.0.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bfanger.nl"}], "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "7f4b9e09c9e96a4c7dd57b0a4ddd5b45f1e261e1"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/7f4b9e09c9e96a4c7dd57b0a4ddd5b45f1e261e1", "type": "zip", "shasum": "", "reference": "7f4b9e09c9e96a4c7dd57b0a4ddd5b45f1e261e1"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.0.5"}, "time": "2020-08-24T04:51:22+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "fa47d62c22c95272625624fbf8109fa46ffac43b"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/fa47d62c22c95272625624fbf8109fa46ffac43b", "type": "zip", "shasum": "", "reference": "fa47d62c22c95272625624fbf8109fa46ffac43b"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.0.4"}, "time": "2020-05-07T09:10:49+00:00", "require": {"php": ">=7.2", "doctrine/annotations": "*", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": ">=3.3", "phpunit/phpunit": ">=8"}}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c86386bd623ffad6f7e6f9269bf51d42d2797012"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c86386bd623ffad6f7e6f9269bf51d42d2797012", "type": "zip", "shasum": "", "reference": "c86386bd623ffad6f7e6f9269bf51d42d2797012"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.0.3"}, "time": "2019-11-30T13:35:44+00:00", "funding": "__unset"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f10ab7f81d89dba97653a980cc90cf4b7b73f543"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f10ab7f81d89dba97653a980cc90cf4b7b73f543", "type": "zip", "shasum": "", "reference": "f10ab7f81d89dba97653a980cc90cf4b7b73f543"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/master"}, "time": "2018-11-16T15:04:29+00:00", "require": {"php": ">=7.0", "doctrine/annotations": "*", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": ">=3.3", "phpunit/phpunit": ">=6.3"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8fc3bc059a7f71b3f100bcfd84a96b5b8fcf6fcf"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8fc3bc059a7f71b3f100bcfd84a96b5b8fcf6fcf", "type": "zip", "shasum": "", "reference": "8fc3bc059a7f71b3f100bcfd84a96b5b8fcf6fcf"}, "time": "2018-09-30T12:19:07+00:00", "require": {"php": ">=7.0", "doctrine/annotations": "*", "symfony/finder": ">=2.2", "symfony/yaml": ">=2.8"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "67f517375c0c8180bf2c09dbad6f305524cff2ab"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/67f517375c0c8180bf2c09dbad6f305524cff2ab", "type": "zip", "shasum": "", "reference": "67f517375c0c8180bf2c09dbad6f305524cff2ab"}, "time": "2018-08-16T06:06:29+00:00"}, {"description": "Swagger-PHP - Generate interactive documentation for your RESTful API using phpdoc annotations", "version": "2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "cc88b23d2dfb9fa38b989cd479477860235d0591"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/cc88b23d2dfb9fa38b989cd479477860235d0591", "type": "zip", "shasum": "", "reference": "cc88b23d2dfb9fa38b989cd479477860235d0591"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.1.13"}, "funding": [], "time": "2023-09-12T22:12:26+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Swagger\\": "src"}}, "bin": ["bin/swagger"], "require": {"php": ">=7.2", "doctrine/annotations": "^1.7", "symfony/finder": ">=3.4"}, "require-dev": {"squizlabs/php_codesniffer": ">=2.7", "phpunit/phpunit": "^8.5.21 || ^9"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f144351118e6bcc04a275f490d7e359a3dd62586"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f144351118e6bcc04a275f490d7e359a3dd62586", "type": "zip", "shasum": "", "reference": "f144351118e6bcc04a275f490d7e359a3dd62586"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.1.2"}, "time": "2022-06-18T00:00:23+00:00", "require-dev": {"squizlabs/php_codesniffer": ">=2.7", "phpunit/phpunit": "^8 || ^9"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "2ea32ca26a87284c7f7feaeb2934b07802dba479"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/2ea32ca26a87284c7f7feaeb2934b07802dba479", "type": "zip", "shasum": "", "reference": "2ea32ca26a87284c7f7feaeb2934b07802dba479"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.1.1"}, "time": "2021-12-27T09:08:49+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "5b26395e0e652ca8fc8e2327659f670422eedccd"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/5b26395e0e652ca8fc8e2327659f670422eedccd", "type": "zip", "shasum": "", "reference": "5b26395e0e652ca8fc8e2327659f670422eedccd"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.1.0"}, "time": "2020-11-29T21:40:13+00:00"}, {"version": "2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "a25c1bfe508e5f27d5f618648449593a79cbe406"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/a25c1bfe508e5f27d5f618648449593a79cbe406", "type": "zip", "shasum": "", "reference": "a25c1bfe508e5f27d5f618648449593a79cbe406"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.16"}, "time": "2020-05-10T13:42:24+00:00", "require": {"php": ">=5.6", "doctrine/annotations": "*", "symfony/finder": ">=2.2"}, "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": ">=2.7", "phpunit/phpunit": ">=4.8.35 <=5.6"}}, {"version": "2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "5fd9439cfb76713925e23f206e9db4bf35784683"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/5fd9439cfb76713925e23f206e9db4bf35784683", "type": "zip", "shasum": "", "reference": "5fd9439cfb76713925e23f206e9db4bf35784683"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.15"}, "time": "2020-01-25T14:15:57+00:00", "funding": "__unset"}, {"version": "2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f2a00f26796e5cd08fd812275ba2db3d1e807663"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f2a00f26796e5cd08fd812275ba2db3d1e807663", "type": "zip", "shasum": "", "reference": "f2a00f26796e5cd08fd812275ba2db3d1e807663"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.14"}, "time": "2019-05-17T10:10:34+00:00"}, {"version": "2.0.13", "version_normalized": "********", "license": ["Apache2"], "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8b42fdc3d8c5a5e0d1f8d344aa359822c9f085e0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8b42fdc3d8c5a5e0d1f8d344aa359822c9f085e0", "type": "zip", "shasum": "", "reference": "8b42fdc3d8c5a5e0d1f8d344aa359822c9f085e0"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.13"}, "time": "2017-12-01T09:22:05+00:00"}, {"version": "2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "5393727e0c2fe822252f82230a5975572ca32d45"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/5393727e0c2fe822252f82230a5975572ca32d45", "type": "zip", "shasum": "", "reference": "5393727e0c2fe822252f82230a5975572ca32d45"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/master"}, "time": "2017-10-27T13:08:09+00:00", "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": ">=2.7", "phpunit/phpunit": ">=4.8 <=5.6"}}, {"version": "2.0.11", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "d010ab67536784f8b578cb4ba7d15c906f3e1a45"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/d010ab67536784f8b578cb4ba7d15c906f3e1a45", "type": "zip", "shasum": "", "reference": "d010ab67536784f8b578cb4ba7d15c906f3e1a45"}, "time": "2017-08-16T08:32:59+00:00", "require": {"php": ">=5.6", "doctrine/annotations": "*", "symfony/finder": "*"}}, {"version": "2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "68e25262d95b9f70f145b488f49be0d6cc73302b"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/68e25262d95b9f70f145b488f49be0d6cc73302b", "type": "zip", "shasum": "", "reference": "68e25262d95b9f70f145b488f49be0d6cc73302b"}, "time": "2017-05-12T22:02:20+00:00"}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "b0b136e3c51c8b05d78a0ea3f1b60cd331cc3544"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/b0b136e3c51c8b05d78a0ea3f1b60cd331cc3544", "type": "zip", "shasum": "", "reference": "b0b136e3c51c8b05d78a0ea3f1b60cd331cc3544"}, "time": "2017-02-22T23:16:25+00:00"}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "39f4c30692a4925597e7d4280fc58794fb4f3730"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/39f4c30692a4925597e7d4280fc58794fb4f3730", "type": "zip", "shasum": "", "reference": "39f4c30692a4925597e7d4280fc58794fb4f3730"}, "time": "2016-12-16T12:39:03+00:00", "require": {"php": ">=5.4.0", "doctrine/annotations": "*", "symfony/finder": "*"}, "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": ">=2.7", "phpunit/phpunit": ">=4.8"}}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "299df0b51f641225ed50ba5199b21e8cfc34e4f4"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/299df0b51f641225ed50ba5199b21e8cfc34e4f4", "type": "zip", "shasum": "", "reference": "299df0b51f641225ed50ba5199b21e8cfc34e4f4"}, "time": "2016-05-27T09:35:51+00:00", "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": "2.*"}}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "0dfc289d53bad4a2bd193adc8d4bd058029ab417"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/0dfc289d53bad4a2bd193adc8d4bd058029ab417", "type": "zip", "shasum": "", "reference": "0dfc289d53bad4a2bd193adc8d4bd058029ab417"}, "time": "2016-02-13T15:39:11+00:00", "require-dev": {"zendframework/zend-form": "*", "squizlabs/php_codesniffer": "2.*"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c19af4edcc13c00e82fabeee926335b1fe1d92e9"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c19af4edcc13c00e82fabeee926335b1fe1d92e9", "type": "zip", "shasum": "", "reference": "c19af4edcc13c00e82fabeee926335b1fe1d92e9"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.5"}, "time": "2016-01-15T09:39:28+00:00", "require-dev": {"zendframework/zend-form": "*"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "be5d96e56c23cbe52c5bc5e267851323d95c57cd"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/be5d96e56c23cbe52c5bc5e267851323d95c57cd", "type": "zip", "shasum": "", "reference": "be5d96e56c23cbe52c5bc5e267851323d95c57cd"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.4"}, "time": "2015-11-13T13:50:11+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "f6624cc067d7894ec32943f5b94cf282c683f7c7"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/f6624cc067d7894ec32943f5b94cf282c683f7c7", "type": "zip", "shasum": "", "reference": "f6624cc067d7894ec32943f5b94cf282c683f7c7"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.3"}, "time": "2015-10-18T13:05:54+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "236499b635b3d91059d8450407c471be003e45f8"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/236499b635b3d91059d8450407c471be003e45f8", "type": "zip", "shasum": "", "reference": "236499b635b3d91059d8450407c471be003e45f8"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.2"}, "time": "2015-09-17T14:46:52+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "449778e5cba0cd56b9c1b3ac53e26f5e3fad6dcb"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/449778e5cba0cd56b9c1b3ac53e26f5e3fad6dcb", "type": "zip", "shasum": "", "reference": "449778e5cba0cd56b9c1b3ac53e26f5e3fad6dcb"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.1"}, "time": "2015-08-04T08:07:52+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "9f03cbb49eccf77c0bd5fd303fef3afd1c015878"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/9f03cbb49eccf77c0bd5fd303fef3afd1c015878", "type": "zip", "shasum": "", "reference": "9f03cbb49eccf77c0bd5fd303fef3afd1c015878"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/2.0.0"}, "time": "2015-07-04T17:44:27+00:00"}, {"description": "Swagger-PHP library implementing the swagger.wordnik.com specification to describe web services, operations/actions and models enabling a uniform means of producing, consuming, and visualizing RESTful web services.", "keywords": ["json", "api", "service discovery"], "version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "93b904dcdf88ab3a4d195679229943ffc766a0d1"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/93b904dcdf88ab3a4d195679229943ffc766a0d1", "type": "zip", "shasum": "", "reference": "93b904dcdf88ab3a4d195679229943ffc766a0d1"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/1.x"}, "time": "2015-04-25T08:05:39+00:00", "autoload": {"psr-0": {"Swagger": "library"}}, "require": {"php": ">=5.3.3", "doctrine/annotations": "*"}, "require-dev": {"symfony/finder": "*", "symfony/process": "*"}}, {"version": "0.9.6", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "4ba5d73eeb068dc590162ea367ef94853011bb36"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/4ba5d73eeb068dc590162ea367ef94853011bb36", "type": "zip", "shasum": "", "reference": "4ba5d73eeb068dc590162ea367ef94853011bb36"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.6"}, "time": "2014-10-11T11:14:09+00:00"}, {"version": "0.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "66a02e689b30d8f16a752151661c80fe077a99a8"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/66a02e689b30d8f16a752151661c80fe077a99a8", "type": "zip", "shasum": "", "reference": "66a02e689b30d8f16a752151661c80fe077a99a8"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.5"}, "time": "2014-09-04T08:19:01+00:00"}, {"version": "0.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "d4059d09695cc5fd6cf5bd91d6a9f5287a1cb460"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/d4059d09695cc5fd6cf5bd91d6a9f5287a1cb460", "type": "zip", "shasum": "", "reference": "d4059d09695cc5fd6cf5bd91d6a9f5287a1cb460"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.4"}, "time": "2014-05-28T13:58:09+00:00"}, {"version": "0.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8a24869779e3e3bf5569d20d2e3c04c254293ad1"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8a24869779e3e3bf5569d20d2e3c04c254293ad1", "type": "zip", "shasum": "", "reference": "8a24869779e3e3bf5569d20d2e3c04c254293ad1"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.3"}, "time": "2014-05-11T09:51:47+00:00"}, {"version": "0.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "d211e0e0995c15618b8ec6befbd5a69c36e6f29c"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/d211e0e0995c15618b8ec6befbd5a69c36e6f29c", "type": "zip", "shasum": "", "reference": "d211e0e0995c15618b8ec6befbd5a69c36e6f29c"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.2"}, "time": "2014-03-26T07:34:22+00:00"}, {"version": "0.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "1459999c87a0ab14e3909cb6f4396810522d4353"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/1459999c87a0ab14e3909cb6f4396810522d4353", "type": "zip", "shasum": "", "reference": "1459999c87a0ab14e3909cb6f4396810522d4353"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.1"}, "time": "2014-03-21T14:33:09+00:00"}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "33f143ea4b84fac996744d4bcc1b4ec81cd897f0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/33f143ea4b84fac996744d4bcc1b4ec81cd897f0", "type": "zip", "shasum": "", "reference": "33f143ea4b84fac996744d4bcc1b4ec81cd897f0"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.9.0"}, "time": "2014-01-22T19:59:44+00:00"}, {"version": "0.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "1c8a31d9653afb994d6db177d91c0e3e86ccfb53"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/1c8a31d9653afb994d6db177d91c0e3e86ccfb53", "type": "zip", "shasum": "", "reference": "1c8a31d9653afb994d6db177d91c0e3e86ccfb53"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.8.3"}, "time": "2013-12-15T13:20:51+00:00", "require": {"php": ">=5.3.3", "doctrine/common": "*"}}, {"version": "0.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "8bf5dc69d92b6b26446ff1fdb872e20356812123"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/8bf5dc69d92b6b26446ff1fdb872e20356812123", "type": "zip", "shasum": "", "reference": "8bf5dc69d92b6b26446ff1fdb872e20356812123"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.8.2"}, "time": "2013-11-30T18:19:37+00:00"}, {"version": "0.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "72d998d7d08d18d96c48fda4391e7b1057d5f0c9"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/72d998d7d08d18d96c48fda4391e7b1057d5f0c9", "type": "zip", "shasum": "", "reference": "72d998d7d08d18d96c48fda4391e7b1057d5f0c9"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.8.1"}, "time": "2013-10-30T16:23:56+00:00"}, {"version": "0.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "15774bbb99440286f23484adc8010336953f0ba6"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/15774bbb99440286f23484adc8010336953f0ba6", "type": "zip", "shasum": "", "reference": "15774bbb99440286f23484adc8010336953f0ba6"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.8.0"}, "time": "2013-10-16T12:12:54+00:00"}, {"version": "0.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c3f3d1c0eb4381719a09c9664c244b2df1b3687e"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c3f3d1c0eb4381719a09c9664c244b2df1b3687e", "type": "zip", "shasum": "", "reference": "c3f3d1c0eb4381719a09c9664c244b2df1b3687e"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.7.3"}, "time": "2013-10-08T20:10:27+00:00"}, {"version": "0.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "adabef9ea34378580c0b800b529ef5914ce10301"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/adabef9ea34378580c0b800b529ef5914ce10301", "type": "zip", "shasum": "", "reference": "adabef9ea34378580c0b800b529ef5914ce10301"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.7.2"}, "time": "2013-09-12T19:11:55+00:00"}, {"version": "0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "4336e34ee5e44a0f77c1e9324614ad70a069e35a"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/4336e34ee5e44a0f77c1e9324614ad70a069e35a", "type": "zip", "shasum": "", "reference": "4336e34ee5e44a0f77c1e9324614ad70a069e35a"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.7.1"}, "time": "2013-09-02T00:35:29+00:00"}, {"version": "0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "1df17a043e2cb569c944f932ccf4be41b4e558ae"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/1df17a043e2cb569c944f932ccf4be41b4e558ae", "type": "zip", "shasum": "", "reference": "1df17a043e2cb569c944f932ccf4be41b4e558ae"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.7"}, "time": "2013-08-18T14:54:32+00:00"}, {"version": "0.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "093c40d81b60d7de7886bcce2c4654e49382231a"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/093c40d81b60d7de7886bcce2c4654e49382231a", "type": "zip", "shasum": "", "reference": "093c40d81b60d7de7886bcce2c4654e49382231a"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/master"}, "time": "2013-06-12T12:10:15+00:00"}, {"version": "0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "21be9d005537cc88de358904709bc301da299ef0"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/21be9d005537cc88de358904709bc301da299ef0", "type": "zip", "shasum": "", "reference": "21be9d005537cc88de358904709bc301da299ef0"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.6"}, "time": "2013-05-09T08:43:46+00:00"}, {"version": "0.4.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}], "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "14aea39974409cd480fb22c3ef38b117f4ae72bc"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/14aea39974409cd480fb22c3ef38b117f4ae72bc", "type": "zip", "shasum": "", "reference": "14aea39974409cd480fb22c3ef38b117f4ae72bc"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/master"}, "time": "2012-12-17T00:00:00+00:00"}, {"version": "0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "74fbb3c71a1ac0eed0231db8ebcb256cea617860"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/74fbb3c71a1ac0eed0231db8ebcb256cea617860", "type": "zip", "shasum": "", "reference": "74fbb3c71a1ac0eed0231db8ebcb256cea617860"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.4.2"}, "extra": {"branch-alias": {"dev-master": "0.5.0-dev"}}}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "ca14aa5df6e1e87930371a576950ef40ba088f48"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/ca14aa5df6e1e87930371a576950ef40ba088f48", "type": "zip", "shasum": "", "reference": "ca14aa5df6e1e87930371a576950ef40ba088f48"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.4.1"}, "time": "2012-12-15T00:00:00+00:00", "extra": "__unset"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "0dae8549487b021111a097f61b610b99bd809bb2"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/0dae8549487b021111a097f61b610b99bd809bb2", "type": "zip", "shasum": "", "reference": "0dae8549487b021111a097f61b610b99bd809bb2"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.4.0"}, "time": "2012-04-01T00:00:00+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "c28cf19808c2ad49d2c2466641e39daa96fd5402"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/c28cf19808c2ad49d2c2466641e39daa96fd5402", "type": "zip", "shasum": "", "reference": "c28cf19808c2ad49d2c2466641e39daa96fd5402"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.3.0"}, "require": {"php": ">=5.3.3"}, "bin": "__unset", "require-dev": "__unset"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "e522fb3e1d4d6109d3e7cd86befcd7ef2a758d1d"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/e522fb3e1d4d6109d3e7cd86befcd7ef2a758d1d", "type": "zip", "shasum": "", "reference": "e522fb3e1d4d6109d3e7cd86befcd7ef2a758d1d"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.2.1"}}, {"version": "0.1.0", "version_normalized": "*******", "license": [], "source": {"url": "https://github.com/zircote/swagger-php.git", "type": "git", "reference": "b1ec5893d36d44cf55fb7cc9fc205f9e333b69b3"}, "dist": {"url": "https://api.github.com/repos/zircote/swagger-php/zipball/b1ec5893d36d44cf55fb7cc9fc205f9e333b69b3", "type": "zip", "shasum": "", "reference": "b1ec5893d36d44cf55fb7cc9fc205f9e333b69b3"}, "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/0.1.0"}, "time": "2012-04-19T02:17:25+00:00", "require": {"php": ">=5.2.4"}}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 20 May 2025 03:37:34 GMT"}