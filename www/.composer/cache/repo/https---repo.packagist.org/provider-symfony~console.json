{"minified": "composer/2.0", "packages": {"symfony/console": [{"name": "symfony/console", "description": "Eases the creation of beautiful and testable command line interfaces", "keywords": ["cli", "console", "command-line", "terminal"], "homepage": "https://symfony.com", "version": "v7.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9e27aecde8f506ba0fd1d9989620c04a87697101"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9e27aecde8f506ba0fd1d9989620c04a87697101", "type": "zip", "shasum": "", "reference": "9e27aecde8f506ba0fd1d9989620c04a87697101"}, "type": "library", "support": {"source": "https://github.com/symfony/console/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "66c1440edf6f339fd82ed6c7caa76cb006211b44"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/66c1440edf6f339fd82ed6c7caa76cb006211b44", "type": "zip", "shasum": "", "reference": "66c1440edf6f339fd82ed6c7caa76cb006211b44"}, "support": {"source": "https://github.com/symfony/console/tree/v7.3.0"}, "time": "2025-05-24T10:34:04+00:00"}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/console/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "aa8b412c06f682ba488b1d3589aa544cd1eff0c0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/aa8b412c06f682ba488b1d3589aa544cd1eff0c0", "type": "zip", "shasum": "", "reference": "aa8b412c06f682ba488b1d3589aa544cd1eff0c0"}, "support": {"source": "https://github.com/symfony/console/tree/v7.3.0-BETA2"}, "time": "2025-05-09T14:50:30+00:00"}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9bd4d772b9678ebf398763403a8676b3010a18a7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9bd4d772b9678ebf398763403a8676b3010a18a7", "type": "zip", "shasum": "", "reference": "9bd4d772b9678ebf398763403a8676b3010a18a7"}, "support": {"source": "https://github.com/symfony/console/tree/v7.3.0-BETA1"}, "time": "2025-04-07T19:11:40+00:00", "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4", "symfony/runtime": "<7.3"}}, {"version": "v7.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a08090dc8d5b6360bf9af0cb0622e8d7279d988f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a08090dc8d5b6360bf9af0cb0622e8d7279d988f", "type": "zip", "shasum": "", "reference": "a08090dc8d5b6360bf9af0cb0622e8d7279d988f"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.8"}, "time": "2025-06-27T19:53:16+00:00", "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}}, {"version": "v7.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "56d7d02c6a6a549a3cd09d5233429100c44270c0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/56d7d02c6a6a549a3cd09d5233429100c44270c0", "type": "zip", "shasum": "", "reference": "56d7d02c6a6a549a3cd09d5233429100c44270c0"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.7"}, "time": "2025-05-07T07:45:33+00:00"}, {"version": "v7.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "type": "zip", "shasum": "", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.6"}, "time": "2025-04-07T19:09:28+00:00"}, {"version": "v7.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e51498ea18570c062e7df29d05a7003585b19b88"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e51498ea18570c062e7df29d05a7003585b19b88", "type": "zip", "shasum": "", "reference": "e51498ea18570c062e7df29d05a7003585b19b88"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.5"}, "time": "2025-03-12T08:11:12+00:00"}, {"version": "v7.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fefcc18c0f5d0efe3ab3152f15857298868dc2c3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fefcc18c0f5d0efe3ab3152f15857298868dc2c3", "type": "zip", "shasum": "", "reference": "fefcc18c0f5d0efe3ab3152f15857298868dc2c3"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.1"}, "time": "2024-12-11T03:49:26+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "23c8aae6d764e2bae02d2a99f7532a7f6ed619cf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/23c8aae6d764e2bae02d2a99f7532a7f6ed619cf", "type": "zip", "shasum": "", "reference": "23c8aae6d764e2bae02d2a99f7532a7f6ed619cf"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.0"}, "time": "2024-11-06T14:24:19+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/console/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3fda7e592a83a6f487cdce3146f6b3408e6f01de"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3fda7e592a83a6f487cdce3146f6b3408e6f01de", "type": "zip", "shasum": "", "reference": "3fda7e592a83a6f487cdce3146f6b3408e6f01de"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.0-BETA2"}, "time": "2024-11-05T15:35:02+00:00"}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "de74db6d7c9f4ecabf7b4a1a20655e021b034001"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/de74db6d7c9f4ecabf7b4a1a20655e021b034001", "type": "zip", "shasum": "", "reference": "de74db6d7c9f4ecabf7b4a1a20655e021b034001"}, "support": {"source": "https://github.com/symfony/console/tree/v7.2.0-BETA1"}, "time": "2024-10-23T06:56:12+00:00"}, {"version": "v7.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bb06e2d7f8dd9dffe5eada8a5cbe0f68f1482db7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bb06e2d7f8dd9dffe5eada8a5cbe0f68f1482db7", "type": "zip", "shasum": "", "reference": "bb06e2d7f8dd9dffe5eada8a5cbe0f68f1482db7"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.10"}, "time": "2024-12-09T07:30:10+00:00"}, {"version": "v7.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ff04e5b5ba043d2badfb308197b9e6b42883fcd5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ff04e5b5ba043d2badfb308197b9e6b42883fcd5", "type": "zip", "shasum": "", "reference": "ff04e5b5ba043d2badfb308197b9e6b42883fcd5"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.8"}, "time": "2024-11-06T14:23:19+00:00"}, {"version": "v7.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3284aafcac338b6e86fd955ee4d794cbe434151a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3284aafcac338b6e86fd955ee4d794cbe434151a", "type": "zip", "shasum": "", "reference": "3284aafcac338b6e86fd955ee4d794cbe434151a"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.7"}, "time": "2024-11-05T15:34:55+00:00"}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bb5192af6edc797cbab5c8e8ecfea2fe5f421e57"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bb5192af6edc797cbab5c8e8ecfea2fe5f421e57", "type": "zip", "shasum": "", "reference": "bb5192af6edc797cbab5c8e8ecfea2fe5f421e57"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.6"}, "time": "2024-10-09T08:46:59+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0fa539d12b3ccf068a722bbbffa07ca7079af9ee"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0fa539d12b3ccf068a722bbbffa07ca7079af9ee", "type": "zip", "shasum": "", "reference": "0fa539d12b3ccf068a722bbbffa07ca7079af9ee"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.5"}, "time": "2024-09-20T08:28:38+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1eed7af6961d763e7832e874d7f9b21c3ea9c111"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1eed7af6961d763e7832e874d7f9b21c3ea9c111", "type": "zip", "shasum": "", "reference": "1eed7af6961d763e7832e874d7f9b21c3ea9c111"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.4"}, "time": "2024-08-15T22:48:53+00:00"}, {"version": "v7.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cb1dcb30ebc7005c29864ee78adb47b5fb7c3cd9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cb1dcb30ebc7005c29864ee78adb47b5fb7c3cd9", "type": "zip", "shasum": "", "reference": "cb1dcb30ebc7005c29864ee78adb47b5fb7c3cd9"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.3"}, "time": "2024-07-26T12:41:01+00:00"}, {"version": "v7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0aa29ca177f432ab68533432db0de059f39c92ae"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0aa29ca177f432ab68533432db0de059f39c92ae", "type": "zip", "shasum": "", "reference": "0aa29ca177f432ab68533432db0de059f39c92ae"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.2"}, "time": "2024-06-28T10:03:55+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9b008f2d7b21c74ef4d0c3de6077a642bc55ece3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9b008f2d7b21c74ef4d0c3de6077a642bc55ece3", "type": "zip", "shasum": "", "reference": "9b008f2d7b21c74ef4d0c3de6077a642bc55ece3"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5bcde9e0b2ea9bd9772bca17618365ea921c5707"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5bcde9e0b2ea9bd9772bca17618365ea921c5707", "type": "zip", "shasum": "", "reference": "5bcde9e0b2ea9bd9772bca17618365ea921c5707"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.0"}, "time": "2024-05-17T10:55:18+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/console/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "965f054b640b28a4cfaa77cc69b53506393e3a4a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/965f054b640b28a4cfaa77cc69b53506393e3a4a", "type": "zip", "shasum": "", "reference": "965f054b640b28a4cfaa77cc69b53506393e3a4a"}, "support": {"source": "https://github.com/symfony/console/tree/v7.1.0-BETA1"}, "time": "2024-05-02T11:32:55+00:00"}, {"version": "v7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f381ef0bc6675a29796d7055088a7193a9e6edff"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f381ef0bc6675a29796d7055088a7193a9e6edff", "type": "zip", "shasum": "", "reference": "f381ef0bc6675a29796d7055088a7193a9e6edff"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.10"}, "time": "2024-07-26T12:31:22+00:00"}, {"version": "v7.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6de397ca4006cac6d78f717afffa43fdab1343a5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6de397ca4006cac6d78f717afffa43fdab1343a5", "type": "zip", "shasum": "", "reference": "6de397ca4006cac6d78f717afffa43fdab1343a5"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.9"}, "time": "2024-06-28T09:58:46+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "725da159ff1e502183b9bf412535a63ebc75fd75"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/725da159ff1e502183b9bf412535a63ebc75fd75", "type": "zip", "shasum": "", "reference": "725da159ff1e502183b9bf412535a63ebc75fd75"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c981e0e9380ce9f146416bde3150c79197ce9986"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c981e0e9380ce9f146416bde3150c79197ce9986", "type": "zip", "shasum": "", "reference": "c981e0e9380ce9f146416bde3150c79197ce9986"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fde915cd8e7eb99b3d531d3d5c09531429c3f9e5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fde915cd8e7eb99b3d531d3d5c09531429c3f9e5", "type": "zip", "shasum": "", "reference": "fde915cd8e7eb99b3d531d3d5c09531429c3f9e5"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.6"}, "time": "2024-04-01T11:04:53+00:00"}, {"version": "v7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6b099f3306f7c9c2d2786ed736d0026b2903205f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6b099f3306f7c9c2d2786ed736d0026b2903205f", "type": "zip", "shasum": "", "reference": "6b099f3306f7c9c2d2786ed736d0026b2903205f"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.4"}, "time": "2024-02-22T20:27:20+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c5010d50f1ee4b25cfa0201d9915cf1b14071456"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c5010d50f1ee4b25cfa0201d9915cf1b14071456", "type": "zip", "shasum": "", "reference": "c5010d50f1ee4b25cfa0201d9915cf1b14071456"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f8587c4cdc5acad67af71c37db34ef03af91e59c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f8587c4cdc5acad67af71c37db34ef03af91e59c", "type": "zip", "shasum": "", "reference": "f8587c4cdc5acad67af71c37db34ef03af91e59c"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.2"}, "time": "2023-12-10T16:54:46+00:00"}, {"version": "v7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cdce5c684b2f920bb1343deecdfba356ffad83d5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cdce5c684b2f920bb1343deecdfba356ffad83d5", "type": "zip", "shasum": "", "reference": "cdce5c684b2f920bb1343deecdfba356ffad83d5"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.1"}, "time": "2023-12-01T15:10:06+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "64e06788686633deb8d9a7c75ab31bcf4b233a26"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/64e06788686633deb8d9a7c75ab31bcf4b233a26", "type": "zip", "shasum": "", "reference": "64e06788686633deb8d9a7c75ab31bcf4b233a26"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.0"}, "time": "2023-11-20T16:43:42+00:00"}, {"version": "v7.0.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/console/tree/v7.0.0-RC2"}}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d2607da72d372a090a7e34327821059441f69eee"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d2607da72d372a090a7e34327821059441f69eee", "type": "zip", "shasum": "", "reference": "d2607da72d372a090a7e34327821059441f69eee"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.0-RC1"}, "time": "2023-11-07T10:26:03+00:00"}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/console/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d7a88e0247eb39161d175745569ab346c6117dab"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d7a88e0247eb39161d175745569ab346c6117dab", "type": "zip", "shasum": "", "reference": "d7a88e0247eb39161d175745569ab346c6117dab"}, "support": {"source": "https://github.com/symfony/console/tree/v7.0.0-BETA1"}, "time": "2023-10-17T13:48:31+00:00"}, {"version": "v6.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9056771b8eca08d026cd3280deeec3cfd99c4d93"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9056771b8eca08d026cd3280deeec3cfd99c4d93", "type": "zip", "shasum": "", "reference": "9056771b8eca08d026cd3280deeec3cfd99c4d93"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.23"}, "time": "2025-06-27T19:37:22+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/config": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}}, {"version": "v6.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "type": "zip", "shasum": "", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.22"}, "time": "2025-05-07T07:05:04+00:00"}, {"version": "v6.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a3011c7b7adb58d89f6c0d822abb641d7a5f9719", "type": "zip", "shasum": "", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.21"}, "time": "2025-04-07T15:42:41+00:00"}, {"version": "v6.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2e4af9c952617cc3f9559ff706aee420a8464c36"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2e4af9c952617cc3f9559ff706aee420a8464c36", "type": "zip", "shasum": "", "reference": "2e4af9c952617cc3f9559ff706aee420a8464c36"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.20"}, "time": "2025-03-03T17:16:38+00:00"}, {"version": "v6.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "799445db3f15768ecc382ac5699e6da0520a0a04"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/799445db3f15768ecc382ac5699e6da0520a0a04", "type": "zip", "shasum": "", "reference": "799445db3f15768ecc382ac5699e6da0520a0a04"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.17"}, "time": "2024-12-07T12:07:30+00:00"}, {"version": "v6.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f1fc6f47283e27336e7cebb9e8946c8de7bff9bd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f1fc6f47283e27336e7cebb9e8946c8de7bff9bd", "type": "zip", "shasum": "", "reference": "f1fc6f47283e27336e7cebb9e8946c8de7bff9bd"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.15"}, "time": "2024-11-06T14:19:14+00:00"}, {"version": "v6.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "897c2441ed4eec8a8a2c37b943427d24dba3f26b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/897c2441ed4eec8a8a2c37b943427d24dba3f26b", "type": "zip", "shasum": "", "reference": "897c2441ed4eec8a8a2c37b943427d24dba3f26b"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.14"}, "time": "2024-11-05T15:34:40+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f793dd5a7d9ae9923e35d0503d08ba734cec1d79"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f793dd5a7d9ae9923e35d0503d08ba734cec1d79", "type": "zip", "shasum": "", "reference": "f793dd5a7d9ae9923e35d0503d08ba734cec1d79"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.13"}, "time": "2024-10-09T08:40:40+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "72d080eb9edf80e36c19be61f72c98ed8273b765"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/72d080eb9edf80e36c19be61f72c98ed8273b765", "type": "zip", "shasum": "", "reference": "72d080eb9edf80e36c19be61f72c98ed8273b765"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.12"}, "time": "2024-09-20T08:15:52+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "42686880adaacdad1835ee8fc2a9ec5b7bd63998"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/42686880adaacdad1835ee8fc2a9ec5b7bd63998", "type": "zip", "shasum": "", "reference": "42686880adaacdad1835ee8fc2a9ec5b7bd63998"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.11"}, "time": "2024-08-15T22:48:29+00:00"}, {"version": "v6.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "504974cbe43d05f83b201d6498c206f16fc0cdbc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/504974cbe43d05f83b201d6498c206f16fc0cdbc", "type": "zip", "shasum": "", "reference": "504974cbe43d05f83b201d6498c206f16fc0cdbc"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.10"}, "time": "2024-07-26T12:30:32+00:00"}, {"version": "v6.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6edb5363ec0c78ad4d48c5128ebf4d083d89d3a9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6edb5363ec0c78ad4d48c5128ebf4d083d89d3a9", "type": "zip", "shasum": "", "reference": "6edb5363ec0c78ad4d48c5128ebf4d083d89d3a9"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.9"}, "time": "2024-06-28T09:49:33+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "be5854cee0e8c7b110f00d695d11debdfa1a2a91"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/be5854cee0e8c7b110f00d695d11debdfa1a2a91", "type": "zip", "shasum": "", "reference": "be5854cee0e8c7b110f00d695d11debdfa1a2a91"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a170e64ae10d00ba89e2acbb590dc2e54da8ad8f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a170e64ae10d00ba89e2acbb590dc2e54da8ad8f", "type": "zip", "shasum": "", "reference": "a170e64ae10d00ba89e2acbb590dc2e54da8ad8f"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a2708a5da5c87d1d0d52937bdeac625df659e11f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a2708a5da5c87d1d0d52937bdeac625df659e11f", "type": "zip", "shasum": "", "reference": "a2708a5da5c87d1d0d52937bdeac625df659e11f"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.6"}, "time": "2024-03-29T19:07:53+00:00"}, {"version": "v6.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0d9e4eb5ad413075624378f474c4167ea202de78"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0d9e4eb5ad413075624378f474c4167ea202de78", "type": "zip", "shasum": "", "reference": "0d9e4eb5ad413075624378f474c4167ea202de78"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.4"}, "time": "2024-02-22T20:27:10+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2aaf83b4de5b9d43b93e4aec6f2f8b676f7c567e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2aaf83b4de5b9d43b93e4aec6f2f8b676f7c567e", "type": "zip", "shasum": "", "reference": "2aaf83b4de5b9d43b93e4aec6f2f8b676f7c567e"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0254811a143e6bc6c8deea08b589a7e68a37f625"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0254811a143e6bc6c8deea08b589a7e68a37f625", "type": "zip", "shasum": "", "reference": "0254811a143e6bc6c8deea08b589a7e68a37f625"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.2"}, "time": "2023-12-10T16:15:48+00:00"}, {"version": "v6.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a550a7c99daeedef3f9d23fb82e3531525ff11fd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a550a7c99daeedef3f9d23fb82e3531525ff11fd", "type": "zip", "shasum": "", "reference": "a550a7c99daeedef3f9d23fb82e3531525ff11fd"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.1"}, "time": "2023-11-30T10:54:28+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cd9864b47c367450e14ab32f78fdbf98c44c26b6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cd9864b47c367450e14ab32f78fdbf98c44c26b6", "type": "zip", "shasum": "", "reference": "cd9864b47c367450e14ab32f78fdbf98c44c26b6"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.0"}, "time": "2023-11-20T16:41:16+00:00"}, {"version": "v6.4.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/console/tree/v6.4.0-RC2"}}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "380ae25f02e34809fba16fa387191f1da9852778"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/380ae25f02e34809fba16fa387191f1da9852778", "type": "zip", "shasum": "", "reference": "380ae25f02e34809fba16fa387191f1da9852778"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.0-RC1"}, "time": "2023-11-02T13:23:55+00:00"}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/console/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d9c21b11e19aaa3288d714015904debfca0da95d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d9c21b11e19aaa3288d714015904debfca0da95d", "type": "zip", "shasum": "", "reference": "d9c21b11e19aaa3288d714015904debfca0da95d"}, "support": {"source": "https://github.com/symfony/console/tree/v6.4.0-BETA1"}, "time": "2023-10-17T13:22:42+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6f69929b2421cf733a5b791dde3c3a2cfa6340cd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6f69929b2421cf733a5b791dde3c3a2cfa6340cd", "type": "zip", "shasum": "", "reference": "6f69929b2421cf733a5b791dde3c3a2cfa6340cd"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.12"}, "time": "2024-01-23T16:21:43+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0"}, "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/lock": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/var-dumper": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ca73e92b0ab86d3c5347f58ec6d822cce6ded1b0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ca73e92b0ab86d3c5347f58ec6d822cce6ded1b0", "type": "zip", "shasum": "", "reference": "ca73e92b0ab86d3c5347f58ec6d822cce6ded1b0"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.11"}, "time": "2023-12-10T14:03:40+00:00"}, {"version": "v6.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0566dbd051f8648d980592c7849f5d90d2c7c60c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0566dbd051f8648d980592c7849f5d90d2c7c60c", "type": "zip", "shasum": "", "reference": "0566dbd051f8648d980592c7849f5d90d2c7c60c"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.9"}, "time": "2023-11-20T16:36:29+00:00"}, {"version": "v6.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0d14a9f6d04d4ac38a8cea1171f4554e325dae92"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0d14a9f6d04d4ac38a8cea1171f4554e325dae92", "type": "zip", "shasum": "", "reference": "0d14a9f6d04d4ac38a8cea1171f4554e325dae92"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.8"}, "time": "2023-10-31T08:09:35+00:00"}, {"version": "v6.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "eca495f2ee845130855ddf1cf18460c38966c8b6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/eca495f2ee845130855ddf1cf18460c38966c8b6", "type": "zip", "shasum": "", "reference": "eca495f2ee845130855ddf1cf18460c38966c8b6"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.4"}, "time": "2023-08-16T10:10:12+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "aa5d64ad3f63f2e48964fc81ee45cb318a723898"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/aa5d64ad3f63f2e48964fc81ee45cb318a723898", "type": "zip", "shasum": "", "reference": "aa5d64ad3f63f2e48964fc81ee45cb318a723898"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.2"}, "time": "2023-07-19T20:17:28+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8788808b07cf0bdd6e4b7fdd23d8ddb1470c83b7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8788808b07cf0bdd6e4b7fdd23d8ddb1470c83b7", "type": "zip", "shasum": "", "reference": "8788808b07cf0bdd6e4b7fdd23d8ddb1470c83b7"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.0"}, "time": "2023-05-29T12:49:39+00:00"}, {"version": "v6.3.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "83ecd3f1a6d5ee30f9d0f6d81b4788a65eb99349"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/83ecd3f1a6d5ee30f9d0f6d81b4788a65eb99349", "type": "zip", "shasum": "", "reference": "83ecd3f1a6d5ee30f9d0f6d81b4788a65eb99349"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.0-RC2"}, "time": "2023-05-26T08:18:10+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2211316d39bf8791a39e7d2a52c5893a4072c8c9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2211316d39bf8791a39e7d2a52c5893a4072c8c9", "type": "zip", "shasum": "", "reference": "2211316d39bf8791a39e7d2a52c5893a4072c8c9"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.0-RC1"}, "time": "2023-05-19T12:46:45+00:00"}, {"version": "v6.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ea11261501f3b6afa1e782648e2a870af0a13c48"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ea11261501f3b6afa1e782648e2a870af0a13c48", "type": "zip", "shasum": "", "reference": "ea11261501f3b6afa1e782648e2a870af0a13c48"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.0-BETA2"}, "time": "2023-05-05T10:58:01+00:00"}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "609351c91141cb1afcb0e0ac5fedf7f8c5d5e7c1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/609351c91141cb1afcb0e0ac5fedf7f8c5d5e7c1", "type": "zip", "shasum": "", "reference": "609351c91141cb1afcb0e0ac5fedf7f8c5d5e7c1"}, "support": {"source": "https://github.com/symfony/console/tree/v6.3.0-BETA1"}, "time": "2023-04-28T15:51:51+00:00"}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e30dfbceadb96cb67605b69b1277c41332fae185"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e30dfbceadb96cb67605b69b1277c41332fae185", "type": "zip", "shasum": "", "reference": "e30dfbceadb96cb67605b69b1277c41332fae185"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.13"}, "time": "2023-07-19T20:17:04+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.4|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}}, {"version": "v6.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dad41ae62bacb7fd96c463278ad583f81ccdffb5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dad41ae62bacb7fd96c463278ad583f81ccdffb5", "type": "zip", "shasum": "", "reference": "dad41ae62bacb7fd96c463278ad583f81ccdffb5"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.12"}, "time": "2023-05-29T12:46:33+00:00"}, {"version": "v6.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5aa03db8ef0a5457c316ec580e69562d97734c77"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5aa03db8ef0a5457c316ec580e69562d97734c77", "type": "zip", "shasum": "", "reference": "5aa03db8ef0a5457c316ec580e69562d97734c77"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.11"}, "time": "2023-05-26T08:16:21+00:00"}, {"version": "v6.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "12288d9f4500f84a4d02254d4aa968b15488476f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/12288d9f4500f84a4d02254d4aa968b15488476f", "type": "zip", "shasum": "", "reference": "12288d9f4500f84a4d02254d4aa968b15488476f"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.10"}, "time": "2023-04-28T13:37:43+00:00"}, {"version": "v6.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3582d68a64a86ec25240aaa521ec8bc2342b369b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3582d68a64a86ec25240aaa521ec8bc2342b369b", "type": "zip", "shasum": "", "reference": "3582d68a64a86ec25240aaa521ec8bc2342b369b"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.8"}, "time": "2023-03-29T21:42:15+00:00"}, {"keywords": ["cli", "console", "command line", "terminal"], "version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cbad09eb8925b6ad4fb721c7a179344dc4a19d45"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cbad09eb8925b6ad4fb721c7a179344dc4a19d45", "type": "zip", "shasum": "", "reference": "cbad09eb8925b6ad4fb721c7a179344dc4a19d45"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.7"}, "time": "2023-02-25T17:00:03+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3e294254f2191762c1d137aed4b94e966965e985"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3e294254f2191762c1d137aed4b94e966965e985", "type": "zip", "shasum": "", "reference": "3e294254f2191762c1d137aed4b94e966965e985"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.5"}, "time": "2023-01-01T08:38:09+00:00"}, {"version": "v6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0f579613e771dba2dbb8211c382342a641f5da06"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0f579613e771dba2dbb8211c382342a641f5da06", "type": "zip", "shasum": "", "reference": "0f579613e771dba2dbb8211c382342a641f5da06"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.3"}, "time": "2022-12-28T14:26:22+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5a9bd5c543f00157c55face973c149957467db31"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5a9bd5c543f00157c55face973c149957467db31", "type": "zip", "shasum": "", "reference": "5a9bd5c543f00157c55face973c149957467db31"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.2"}, "time": "2022-12-16T15:08:36+00:00"}, {"version": "v6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "58f6cef5dc5f641b7bbdbf8b32b44cc926c35f3f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/58f6cef5dc5f641b7bbdbf8b32b44cc926c35f3f", "type": "zip", "shasum": "", "reference": "58f6cef5dc5f641b7bbdbf8b32b44cc926c35f3f"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.1"}, "time": "2022-12-01T13:44:20+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "75d4749d9620a8fa21a2d2847800a84b5c4e7682"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/75d4749d9620a8fa21a2d2847800a84b5c4e7682", "type": "zip", "shasum": "", "reference": "75d4749d9620a8fa21a2d2847800a84b5c4e7682"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.0"}, "time": "2022-11-29T16:44:51+00:00"}, {"version": "v6.2.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "54f790fd94f1e83d7845ebb8e5f2aa252d74dc86"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/54f790fd94f1e83d7845ebb8e5f2aa252d74dc86", "type": "zip", "shasum": "", "reference": "54f790fd94f1e83d7845ebb8e5f2aa252d74dc86"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.0-RC2"}, "time": "2022-11-25T19:00:27+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "42af19c215b34d1f619a499ca2b2ba549af19490"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/42af19c215b34d1f619a499ca2b2ba549af19490", "type": "zip", "shasum": "", "reference": "42af19c215b34d1f619a499ca2b2ba549af19490"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.0-RC1"}, "time": "2022-11-20T18:39:20+00:00"}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1aab3877cc61fef7799e206e6f15c4c55431c97e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1aab3877cc61fef7799e206e6f15c4c55431c97e", "type": "zip", "shasum": "", "reference": "1aab3877cc61fef7799e206e6f15c4c55431c97e"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.0-BETA3"}, "time": "2022-11-19T14:35:57+00:00"}, {"version": "v6.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b0acb1e8d7130c317a5cf655443b0b38a21d446d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b0acb1e8d7130c317a5cf655443b0b38a21d446d", "type": "zip", "shasum": "", "reference": "b0acb1e8d7130c317a5cf655443b0b38a21d446d"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.0-BETA2"}, "time": "2022-10-26T21:44:56+00:00"}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "53399d9eb0c6438d2261dfc4b8e02f450e52043c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/53399d9eb0c6438d2261dfc4b8e02f450e52043c", "type": "zip", "shasum": "", "reference": "53399d9eb0c6438d2261dfc4b8e02f450e52043c"}, "support": {"source": "https://github.com/symfony/console/tree/v6.2.0-BETA1"}, "time": "2022-10-18T13:21:06+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bdf195cee00f47ee93eb6f75fb1b18892d507785"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bdf195cee00f47ee93eb6f75fb1b18892d507785", "type": "zip", "shasum": "", "reference": "bdf195cee00f47ee93eb6f75fb1b18892d507785"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.11"}, "time": "2023-01-01T08:36:55+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b800f23e66bcb96602da4941fe966e50f0fff1f3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b800f23e66bcb96602da4941fe966e50f0fff1f3", "type": "zip", "shasum": "", "reference": "b800f23e66bcb96602da4941fe966e50f0fff1f3"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.9"}, "time": "2022-12-28T14:22:24+00:00"}, {"version": "v6.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a71863ea74f444d93c768deb3e314e1f750cf20d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a71863ea74f444d93c768deb3e314e1f750cf20d", "type": "zip", "shasum": "", "reference": "a71863ea74f444d93c768deb3e314e1f750cf20d"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.8"}, "time": "2022-11-25T18:59:16+00:00"}, {"version": "v6.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a1282bd0c096e0bdb8800b104177e2ce404d8815"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a1282bd0c096e0bdb8800b104177e2ce404d8815", "type": "zip", "shasum": "", "reference": "a1282bd0c096e0bdb8800b104177e2ce404d8815"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.7"}, "time": "2022-10-26T21:42:49+00:00"}, {"version": "v6.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7fa3b9cf17363468795e539231a5c91b02b608fc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7fa3b9cf17363468795e539231a5c91b02b608fc", "type": "zip", "shasum": "", "reference": "7fa3b9cf17363468795e539231a5c91b02b608fc"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.6"}, "time": "2022-10-07T08:04:03+00:00"}, {"version": "v6.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "17524a64ebcfab68d237bbed247e9a9917747096"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/17524a64ebcfab68d237bbed247e9a9917747096", "type": "zip", "shasum": "", "reference": "17524a64ebcfab68d237bbed247e9a9917747096"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.5"}, "time": "2022-09-03T14:24:42+00:00"}, {"version": "v6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7fccea8728aa2d431a6725b02b3ce759049fc84d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7fccea8728aa2d431a6725b02b3ce759049fc84d", "type": "zip", "shasum": "", "reference": "7fccea8728aa2d431a6725b02b3ce759049fc84d"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.4"}, "time": "2022-08-26T10:32:31+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "43fcb5c5966b43c56bcfa481368d90d748936ab8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/43fcb5c5966b43c56bcfa481368d90d748936ab8", "type": "zip", "shasum": "", "reference": "43fcb5c5966b43c56bcfa481368d90d748936ab8"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.3"}, "time": "2022-07-22T14:17:57+00:00"}, {"version": "v6.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7a86c1c42fbcb69b59768504c7bca1d3767760b7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7a86c1c42fbcb69b59768504c7bca1d3767760b7", "type": "zip", "shasum": "", "reference": "7a86c1c42fbcb69b59768504c7bca1d3767760b7"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.2"}, "time": "2022-06-26T13:01:30+00:00"}, {"version": "v6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6187424023fbffcd757789aeb517c9161b1eabee"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6187424023fbffcd757789aeb517c9161b1eabee", "type": "zip", "shasum": "", "reference": "6187424023fbffcd757789aeb517c9161b1eabee"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.1"}, "time": "2022-06-08T14:02:09+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c9646197ef43b0e2ff44af61e7f0571526fd4170"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c9646197ef43b0e2ff44af61e7f0571526fd4170", "type": "zip", "shasum": "", "reference": "c9646197ef43b0e2ff44af61e7f0571526fd4170"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.0"}, "time": "2022-05-27T06:34:22+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7755bd7069072348cb7215dadf57bcdba628e747"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7755bd7069072348cb7215dadf57bcdba628e747", "type": "zip", "shasum": "", "reference": "7755bd7069072348cb7215dadf57bcdba628e747"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.0-RC1"}, "time": "2022-05-14T12:53:54+00:00"}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2a9be9b22169c36f041dfd38997518414ab1a313"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2a9be9b22169c36f041dfd38997518414ab1a313", "type": "zip", "shasum": "", "reference": "2a9be9b22169c36f041dfd38997518414ab1a313"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.0-BETA2"}, "time": "2022-04-22T08:18:23+00:00"}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "196c43b0e37be374a7d36ac3911682027fd8decd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/196c43b0e37be374a7d36ac3911682027fd8decd", "type": "zip", "shasum": "", "reference": "196c43b0e37be374a7d36ac3911682027fd8decd"}, "support": {"source": "https://github.com/symfony/console/tree/v6.1.0-BETA1"}, "time": "2022-04-12T16:22:53+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c3ebc83d031b71c39da318ca8b7a07ecc67507ed"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c3ebc83d031b71c39da318ca8b7a07ecc67507ed", "type": "zip", "shasum": "", "reference": "c3ebc83d031b71c39da318ca8b7a07ecc67507ed"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.19"}, "time": "2023-01-01T08:36:10+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.4|^6.0"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2ab307342a7233b9a260edd5ef94087aaca57d18"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2ab307342a7233b9a260edd5ef94087aaca57d18", "type": "zip", "shasum": "", "reference": "2ab307342a7233b9a260edd5ef94087aaca57d18"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.17"}, "time": "2022-12-28T14:21:34+00:00"}, {"version": "v6.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "be294423f337dda97c810733138c0caec1bb0575"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/be294423f337dda97c810733138c0caec1bb0575", "type": "zip", "shasum": "", "reference": "be294423f337dda97c810733138c0caec1bb0575"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.16"}, "time": "2022-11-25T18:58:46+00:00"}, {"version": "v6.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b0b910724a0a0326b4481e4f8a30abb2dd442efb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b0b910724a0a0326b4481e4f8a30abb2dd442efb", "type": "zip", "shasum": "", "reference": "b0b910724a0a0326b4481e4f8a30abb2dd442efb"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.15"}, "time": "2022-10-26T21:42:20+00:00"}, {"version": "v6.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1f89cab8d52c84424f798495b3f10342a7b1a070"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1f89cab8d52c84424f798495b3f10342a7b1a070", "type": "zip", "shasum": "", "reference": "1f89cab8d52c84424f798495b3f10342a7b1a070"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.14"}, "time": "2022-10-07T08:02:12+00:00"}, {"version": "v6.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8f14753b865651c2aad107ef97475740a9b0730f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8f14753b865651c2aad107ef97475740a9b0730f", "type": "zip", "shasum": "", "reference": "8f14753b865651c2aad107ef97475740a9b0730f"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.13"}, "time": "2022-09-03T14:23:25+00:00"}, {"version": "v6.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c5c2e313aa682530167c25077d6bdff36346251e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c5c2e313aa682530167c25077d6bdff36346251e", "type": "zip", "shasum": "", "reference": "c5c2e313aa682530167c25077d6bdff36346251e"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.12"}, "time": "2022-08-23T20:52:30+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "09b8e50f09bf0e5bbde9b61b19d7f53751114725"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/09b8e50f09bf0e5bbde9b61b19d7f53751114725", "type": "zip", "shasum": "", "reference": "09b8e50f09bf0e5bbde9b61b19d7f53751114725"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.11"}, "time": "2022-07-22T14:17:38+00:00"}, {"version": "v6.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d8d41b93c16f1da2f2d4b9209b7de78c4d203642"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d8d41b93c16f1da2f2d4b9209b7de78c4d203642", "type": "zip", "shasum": "", "reference": "d8d41b93c16f1da2f2d4b9209b7de78c4d203642"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.10"}, "time": "2022-06-26T13:01:22+00:00"}, {"version": "v6.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9b190bc7a19d19add1dbb3382721973836e59b50"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9b190bc7a19d19add1dbb3382721973836e59b50", "type": "zip", "shasum": "", "reference": "9b190bc7a19d19add1dbb3382721973836e59b50"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.9"}, "time": "2022-05-27T06:40:13+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0d00aa289215353aa8746a31d101f8e60826285c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0d00aa289215353aa8746a31d101f8e60826285c", "type": "zip", "shasum": "", "reference": "0d00aa289215353aa8746a31d101f8e60826285c"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.8"}, "time": "2022-04-20T15:01:42+00:00"}, {"version": "v6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "70dcf7b2ca2ea08ad6ebcc475f104a024fb5632e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/70dcf7b2ca2ea08ad6ebcc475f104a024fb5632e", "type": "zip", "shasum": "", "reference": "70dcf7b2ca2ea08ad6ebcc475f104a024fb5632e"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.7"}, "time": "2022-03-31T17:18:25+00:00"}, {"version": "v6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3bebf4108b9e07492a2a4057d207aa5a77d146b1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3bebf4108b9e07492a2a4057d207aa5a77d146b1", "type": "zip", "shasum": "", "reference": "3bebf4108b9e07492a2a4057d207aa5a77d146b1"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.5"}, "time": "2022-02-25T10:48:52+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "22e8efd019c3270c4f79376234a3f8752cd25490"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/22e8efd019c3270c4f79376234a3f8752cd25490", "type": "zip", "shasum": "", "reference": "22e8efd019c3270c4f79376234a3f8752cd25490"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.3"}, "time": "2022-01-26T17:23:29+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dd434fa8d69325e5d210f63070014d889511fcb3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dd434fa8d69325e5d210f63070014d889511fcb3", "type": "zip", "shasum": "", "reference": "dd434fa8d69325e5d210f63070014d889511fcb3"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.2"}, "time": "2021-12-27T21:05:08+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fafd9802d386bf1c267e0249ddb7ceb14dcfdad4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fafd9802d386bf1c267e0249ddb7ceb14dcfdad4", "type": "zip", "shasum": "", "reference": "fafd9802d386bf1c267e0249ddb7ceb14dcfdad4"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.1"}, "time": "2021-12-09T12:47:37+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "675a4e6b05029a02ac43d3daf5aa73f039e876ea"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/675a4e6b05029a02ac43d3daf5aa73f039e876ea", "type": "zip", "shasum": "", "reference": "675a4e6b05029a02ac43d3daf5aa73f039e876ea"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.0"}, "time": "2021-11-29T15:32:57+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c27250e77ff3f6595c600e5fd20e838473936e04"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c27250e77ff3f6595c600e5fd20e838473936e04", "type": "zip", "shasum": "", "reference": "c27250e77ff3f6595c600e5fd20e838473936e04"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.0-RC1"}, "time": "2021-11-23T19:05:29+00:00"}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b15afc0c88ded99920b45020751145c82707067d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b15afc0c88ded99920b45020751145c82707067d", "type": "zip", "shasum": "", "reference": "b15afc0c88ded99920b45020751145c82707067d"}, "support": {"source": "https://github.com/symfony/console/tree/v6.0.0-BETA1"}, "time": "2021-11-04T17:14:40+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2.0|^3.0", "symfony/string": "^5.4|^6.0"}}, {"keywords": ["cli", "console", "command-line", "terminal"], "version": "v5.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "type": "zip", "shasum": "", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.47"}, "time": "2024-11-06T11:30:55+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "require-dev": {"symfony/config": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0", "psr/log": "^1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}}, {"version": "v5.4.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fb0d4760e7147d81ab4d9e2d57d56268261b4e4e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fb0d4760e7147d81ab4d9e2d57d56268261b4e4e", "type": "zip", "shasum": "", "reference": "fb0d4760e7147d81ab4d9e2d57d56268261b4e4e"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.46"}, "time": "2024-11-05T14:17:06+00:00"}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "108d436c2af470858bdaba3257baab3a74172017"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/108d436c2af470858bdaba3257baab3a74172017", "type": "zip", "shasum": "", "reference": "108d436c2af470858bdaba3257baab3a74172017"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.45"}, "time": "2024-10-08T07:27:17+00:00"}, {"version": "v5.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5b5a0aa66e3296e303e22490f90f521551835a83"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5b5a0aa66e3296e303e22490f90f521551835a83", "type": "zip", "shasum": "", "reference": "5b5a0aa66e3296e303e22490f90f521551835a83"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.44"}, "time": "2024-09-20T07:56:40+00:00"}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e86f8554de667c16dde8aeb89a3990cfde924df9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e86f8554de667c16dde8aeb89a3990cfde924df9", "type": "zip", "shasum": "", "reference": "e86f8554de667c16dde8aeb89a3990cfde924df9"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.43"}, "time": "2024-08-13T16:31:56+00:00"}, {"version": "v5.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cef62396a0477e94fc52e87a17c6e5c32e226b7f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cef62396a0477e94fc52e87a17c6e5c32e226b7f", "type": "zip", "shasum": "", "reference": "cef62396a0477e94fc52e87a17c6e5c32e226b7f"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.42"}, "time": "2024-07-26T12:21:55+00:00"}, {"version": "v5.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6473d441a913cb997123b59ff2dbe3d1cf9e11ba"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6473d441a913cb997123b59ff2dbe3d1cf9e11ba", "type": "zip", "shasum": "", "reference": "6473d441a913cb997123b59ff2dbe3d1cf9e11ba"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.41"}, "time": "2024-06-28T07:48:55+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "aa73115c0c24220b523625bfcfa655d7d73662dd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/aa73115c0c24220b523625bfcfa655d7d73662dd", "type": "zip", "shasum": "", "reference": "aa73115c0c24220b523625bfcfa655d7d73662dd"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f3e591c48688a0cfa1a3296205926c05e84b22b1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f3e591c48688a0cfa1a3296205926c05e84b22b1", "type": "zip", "shasum": "", "reference": "f3e591c48688a0cfa1a3296205926c05e84b22b1"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "39f75d9d73d0c11952fdcecf4877b4d0f62a8f6e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/39f75d9d73d0c11952fdcecf4877b4d0f62a8f6e", "type": "zip", "shasum": "", "reference": "39f75d9d73d0c11952fdcecf4877b4d0f62a8f6e"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.36"}, "time": "2024-02-20T16:33:57+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dbdf6adcb88d5f83790e1efb57ef4074309d3931"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dbdf6adcb88d5f83790e1efb57ef4074309d3931", "type": "zip", "shasum": "", "reference": "dbdf6adcb88d5f83790e1efb57ef4074309d3931"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.35"}, "time": "2024-01-23T14:28:09+00:00"}, {"version": "v5.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4b4d8cd118484aa604ec519062113dd87abde18c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4b4d8cd118484aa604ec519062113dd87abde18c", "type": "zip", "shasum": "", "reference": "4b4d8cd118484aa604ec519062113dd87abde18c"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.34"}, "time": "2023-12-08T13:33:03+00:00"}, {"version": "v5.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c70df1ffaf23a8d340bded3cfab1b86752ad6ed7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c70df1ffaf23a8d340bded3cfab1b86752ad6ed7", "type": "zip", "shasum": "", "reference": "c70df1ffaf23a8d340bded3cfab1b86752ad6ed7"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.32"}, "time": "2023-11-18T18:23:04+00:00"}, {"version": "v5.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "11ac5f154e0e5c4c77af83ad11ead9165280b92a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/11ac5f154e0e5c4c77af83ad11ead9165280b92a", "type": "zip", "shasum": "", "reference": "11ac5f154e0e5c4c77af83ad11ead9165280b92a"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.31"}, "time": "2023-10-31T07:58:33+00:00"}, {"version": "v5.4.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f4f71842f24c2023b91237c72a365306f3c58827"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f4f71842f24c2023b91237c72a365306f3c58827", "type": "zip", "shasum": "", "reference": "f4f71842f24c2023b91237c72a365306f3c58827"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.28"}, "time": "2023-08-07T06:12:30+00:00"}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b504a3d266ad2bb632f196c0936ef2af5ff6e273"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b504a3d266ad2bb632f196c0936ef2af5ff6e273", "type": "zip", "shasum": "", "reference": "b504a3d266ad2bb632f196c0936ef2af5ff6e273"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.26"}, "time": "2023-07-19T20:11:33+00:00"}, {"version": "v5.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "560fc3ed7a43e6d30ea94a07d77f9a60b8ed0fb8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/560fc3ed7a43e6d30ea94a07d77f9a60b8ed0fb8", "type": "zip", "shasum": "", "reference": "560fc3ed7a43e6d30ea94a07d77f9a60b8ed0fb8"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.24"}, "time": "2023-05-26T05:13:16+00:00"}, {"version": "v5.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "90f21e27d0d88ce38720556dd164d4a1e4c3934c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/90f21e27d0d88ce38720556dd164d4a1e4c3934c", "type": "zip", "shasum": "", "reference": "90f21e27d0d88ce38720556dd164d4a1e4c3934c"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.23"}, "time": "2023-04-24T18:47:29+00:00"}, {"version": "v5.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3cd51fd2e6c461ca678f84d419461281bd87a0a8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3cd51fd2e6c461ca678f84d419461281bd87a0a8", "type": "zip", "shasum": "", "reference": "3cd51fd2e6c461ca678f84d419461281bd87a0a8"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.22"}, "time": "2023-03-25T09:27:28+00:00"}, {"keywords": ["cli", "console", "command line", "terminal"], "version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c77433ddc6cdc689caf48065d9ea22ca0853fbd9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c77433ddc6cdc689caf48065d9ea22ca0853fbd9", "type": "zip", "shasum": "", "reference": "c77433ddc6cdc689caf48065d9ea22ca0853fbd9"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.21"}, "time": "2023-02-25T16:59:41+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dccb8d251a9017d5994c988b034d3e18aaabf740"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dccb8d251a9017d5994c988b034d3e18aaabf740", "type": "zip", "shasum": "", "reference": "dccb8d251a9017d5994c988b034d3e18aaabf740"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.19"}, "time": "2023-01-01T08:32:19+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "58422fdcb0e715ed05b385f70d3e8b5ed4bbd45f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/58422fdcb0e715ed05b385f70d3e8b5ed4bbd45f", "type": "zip", "shasum": "", "reference": "58422fdcb0e715ed05b385f70d3e8b5ed4bbd45f"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.17"}, "time": "2022-12-28T14:15:31+00:00"}, {"version": "v5.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8e9b9c8dfb33af6057c94e1b44846bee700dc5ef"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8e9b9c8dfb33af6057c94e1b44846bee700dc5ef", "type": "zip", "shasum": "", "reference": "8e9b9c8dfb33af6057c94e1b44846bee700dc5ef"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.16"}, "time": "2022-11-25T14:09:27+00:00"}, {"version": "v5.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ea59bb0edfaf9f28d18d8791410ee0355f317669"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ea59bb0edfaf9f28d18d8791410ee0355f317669", "type": "zip", "shasum": "", "reference": "ea59bb0edfaf9f28d18d8791410ee0355f317669"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.15"}, "time": "2022-10-26T21:41:52+00:00"}, {"version": "v5.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "984ea2c0f45f42dfed01d2f3987b187467c4b16d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/984ea2c0f45f42dfed01d2f3987b187467c4b16d", "type": "zip", "shasum": "", "reference": "984ea2c0f45f42dfed01d2f3987b187467c4b16d"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.14"}, "time": "2022-10-07T08:01:20+00:00"}, {"version": "v5.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3f97f6c7b7e26848a90c0c0cfb91eeb2bb8618be"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3f97f6c7b7e26848a90c0c0cfb91eeb2bb8618be", "type": "zip", "shasum": "", "reference": "3f97f6c7b7e26848a90c0c0cfb91eeb2bb8618be"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.13"}, "time": "2022-08-26T13:50:20+00:00"}, {"version": "v5.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c072aa8f724c3af64e2c7a96b796a4863d24dba1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c072aa8f724c3af64e2c7a96b796a4863d24dba1", "type": "zip", "shasum": "", "reference": "c072aa8f724c3af64e2c7a96b796a4863d24dba1"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.12"}, "time": "2022-08-17T13:18:05+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "535846c7ee6bc4dd027ca0d93220601456734b10"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/535846c7ee6bc4dd027ca0d93220601456734b10", "type": "zip", "shasum": "", "reference": "535846c7ee6bc4dd027ca0d93220601456734b10"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.11"}, "time": "2022-07-22T10:42:43+00:00"}, {"version": "v5.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4d671ab4ddac94ee439ea73649c69d9d200b5000"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4d671ab4ddac94ee439ea73649c69d9d200b5000", "type": "zip", "shasum": "", "reference": "4d671ab4ddac94ee439ea73649c69d9d200b5000"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.10"}, "time": "2022-06-26T13:00:04+00:00"}, {"version": "v5.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "829d5d1bf60b2efeb0887b7436873becc71a45eb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/829d5d1bf60b2efeb0887b7436873becc71a45eb", "type": "zip", "shasum": "", "reference": "829d5d1bf60b2efeb0887b7436873becc71a45eb"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.9"}, "time": "2022-05-18T06:17:34+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ffe3aed36c4d60da2cf1b0a1cee6b8f2e5fa881b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ffe3aed36c4d60da2cf1b0a1cee6b8f2e5fa881b", "type": "zip", "shasum": "", "reference": "ffe3aed36c4d60da2cf1b0a1cee6b8f2e5fa881b"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.8"}, "time": "2022-04-12T16:02:29+00:00"}, {"version": "v5.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "900275254f0a1a2afff1ab0e11abd5587a10e1d6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/900275254f0a1a2afff1ab0e11abd5587a10e1d6", "type": "zip", "shasum": "", "reference": "900275254f0a1a2afff1ab0e11abd5587a10e1d6"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.7"}, "time": "2022-03-31T17:09:19+00:00"}, {"version": "v5.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d8111acc99876953f52fe16d4c50eb60940d49ad"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d8111acc99876953f52fe16d4c50eb60940d49ad", "type": "zip", "shasum": "", "reference": "d8111acc99876953f52fe16d4c50eb60940d49ad"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.5"}, "time": "2022-02-24T12:45:35+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a2a86ec353d825c75856c6fd14fac416a7bdb6b8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a2a86ec353d825c75856c6fd14fac416a7bdb6b8", "type": "zip", "shasum": "", "reference": "a2a86ec353d825c75856c6fd14fac416a7bdb6b8"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.3"}, "time": "2022-01-26T16:28:35+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a2c6b7ced2eb7799a35375fb9022519282b5405e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a2c6b7ced2eb7799a35375fb9022519282b5405e", "type": "zip", "shasum": "", "reference": "a2c6b7ced2eb7799a35375fb9022519282b5405e"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.2"}, "time": "2021-12-20T16:11:12+00:00"}, {"version": "v5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9130e1a0fc93cb0faadca4ee917171bd2ca9e5f4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9130e1a0fc93cb0faadca4ee917171bd2ca9e5f4", "type": "zip", "shasum": "", "reference": "9130e1a0fc93cb0faadca4ee917171bd2ca9e5f4"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.1"}, "time": "2021-12-09T11:22:43+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ec3661faca1d110d6c307e124b44f99ac54179e3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ec3661faca1d110d6c307e124b44f99ac54179e3", "type": "zip", "shasum": "", "reference": "ec3661faca1d110d6c307e124b44f99ac54179e3"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.0"}, "time": "2021-11-29T15:30:56+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f797eedbbede8a2e6cdb8d5f16da0d6fb9bbfd12"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f797eedbbede8a2e6cdb8d5f16da0d6fb9bbfd12", "type": "zip", "shasum": "", "reference": "f797eedbbede8a2e6cdb8d5f16da0d6fb9bbfd12"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.0-RC1"}, "time": "2021-11-23T18:53:11+00:00"}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bea7632e3b1d12decedba0a7fe7a7e0ebf7ee2f4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bea7632e3b1d12decedba0a7fe7a7e0ebf7ee2f4", "type": "zip", "shasum": "", "reference": "bea7632e3b1d12decedba0a7fe7a7e0ebf7ee2f4"}, "support": {"source": "https://github.com/symfony/console/tree/v5.4.0-BETA1"}, "time": "2021-11-04T16:48:04+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1|^6.0"}}, {"version": "v5.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2e322c76cdccb302af6b275ea2207169c8355328"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2e322c76cdccb302af6b275ea2207169c8355328", "type": "zip", "shasum": "", "reference": "2e322c76cdccb302af6b275ea2207169c8355328"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.16"}, "time": "2022-03-01T08:24:05+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0", "psr/log": "^1|^2"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "79e088706f4962d4785d9a0d3a22ad4e0c75c9f1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/79e088706f4962d4785d9a0d3a22ad4e0c75c9f1", "type": "zip", "shasum": "", "reference": "79e088706f4962d4785d9a0d3a22ad4e0c75c9f1"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.14"}, "time": "2022-01-26T16:22:30+00:00"}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "402db098660083dfd23e9289bcdd351f3935b641"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/402db098660083dfd23e9289bcdd351f3935b641", "type": "zip", "shasum": "", "reference": "402db098660083dfd23e9289bcdd351f3935b641"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.13"}, "time": "2021-12-15T10:43:37+00:00"}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3e7ab8f5905058984899b05a4648096f558bfeba"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3e7ab8f5905058984899b05a4648096f558bfeba", "type": "zip", "shasum": "", "reference": "3e7ab8f5905058984899b05a4648096f558bfeba"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.11"}, "time": "2021-11-21T19:41:05+00:00"}, {"version": "v5.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d4e409d9fbcfbf71af0e5a940abb7b0b4bad0bd3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d4e409d9fbcfbf71af0e5a940abb7b0b4bad0bd3", "type": "zip", "shasum": "", "reference": "d4e409d9fbcfbf71af0e5a940abb7b0b4bad0bd3"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.10"}, "time": "2021-10-26T09:30:15+00:00", "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8b1008344647462ae6ec57559da166c2bfa5e16a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8b1008344647462ae6ec57559da166c2bfa5e16a", "type": "zip", "shasum": "", "reference": "8b1008344647462ae6ec57559da166c2bfa5e16a"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.7"}, "time": "2021-08-25T20:02:16+00:00"}, {"version": "v5.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "51b71afd6d2dc8f5063199357b9880cea8d8bfe2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/51b71afd6d2dc8f5063199357b9880cea8d8bfe2", "type": "zip", "shasum": "", "reference": "51b71afd6d2dc8f5063199357b9880cea8d8bfe2"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.6"}, "time": "2021-07-27T19:10:22+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ebd610dacd40d75b6a12bf64b5ccd494fc7d6ab1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ebd610dacd40d75b6a12bf64b5ccd494fc7d6ab1", "type": "zip", "shasum": "", "reference": "ebd610dacd40d75b6a12bf64b5ccd494fc7d6ab1"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.4"}, "time": "2021-07-26T16:33:26+00:00"}, {"version": "v5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "649730483885ff2ca99ca0560ef0e5f6b03f2ac1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/649730483885ff2ca99ca0560ef0e5f6b03f2ac1", "type": "zip", "shasum": "", "reference": "649730483885ff2ca99ca0560ef0e5f6b03f2ac1"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.2"}, "time": "2021-06-12T09:42:48+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0", "psr/log": "~1.0"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "058553870f7809087fa80fa734704a21b9bcaeb2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/058553870f7809087fa80fa734704a21b9bcaeb2", "type": "zip", "shasum": "", "reference": "058553870f7809087fa80fa734704a21b9bcaeb2"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e84aab170bab4df0445d6c104a197a89df087ed7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e84aab170bab4df0445d6c104a197a89df087ed7", "type": "zip", "shasum": "", "reference": "e84aab170bab4df0445d6c104a197a89df087ed7"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.0-RC1"}, "time": "2021-05-13T09:45:23+00:00"}, {"version": "v5.3.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cbb69227421d7050e0525c96122c7f39c4e3c4fb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cbb69227421d7050e0525c96122c7f39c4e3c4fb", "type": "zip", "shasum": "", "reference": "cbb69227421d7050e0525c96122c7f39c4e3c4fb"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.0-BETA4"}, "time": "2021-05-11T16:08:00+00:00"}, {"version": "v5.3.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "eac8e550585cd4264656a4585f58d7c94920d584"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/eac8e550585cd4264656a4585f58d7c94920d584", "type": "zip", "shasum": "", "reference": "eac8e550585cd4264656a4585f58d7c94920d584"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.0-BETA3"}, "time": "2021-05-07T14:48:44+00:00"}, {"version": "v5.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "959bfea5f92bd9b0b278f458a69cfd3f96dd411d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/959bfea5f92bd9b0b278f458a69cfd3f96dd411d", "type": "zip", "shasum": "", "reference": "959bfea5f92bd9b0b278f458a69cfd3f96dd411d"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.0-BETA2"}, "time": "2021-05-01T00:53:07+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1d077bd682f7c0794d5f5b794b16e2b30febec6b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1d077bd682f7c0794d5f5b794b16e2b30febec6b", "type": "zip", "shasum": "", "reference": "1d077bd682f7c0794d5f5b794b16e2b30febec6b"}, "support": {"source": "https://github.com/symfony/console/tree/v5.3.0-BETA1"}, "time": "2021-04-16T17:36:28+00:00"}, {"version": "v5.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ffc2722adb0983451855c794c4bc7818d3c65fa2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ffc2722adb0983451855c794c4bc7818d3c65fa2", "type": "zip", "shasum": "", "reference": "ffc2722adb0983451855c794c4bc7818d3c65fa2"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.14"}, "time": "2021-07-29T06:18:06+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0", "psr/log": "^1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "eb07d40cdcfebc158481a028d5a1a498f5e0810c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/eb07d40cdcfebc158481a028d5a1a498f5e0810c", "type": "zip", "shasum": "", "reference": "eb07d40cdcfebc158481a028d5a1a498f5e0810c"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.12"}, "time": "2021-07-26T16:33:12+00:00"}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2f93c8c64295e029b7ff4d68b9a59c5ad8b7e24a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2f93c8c64295e029b7ff4d68b9a59c5ad8b7e24a", "type": "zip", "shasum": "", "reference": "2f93c8c64295e029b7ff4d68b9a59c5ad8b7e24a"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.11"}, "time": "2021-06-06T09:50:27+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0", "psr/log": "~1.0"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9e18ae5de0ca8c6d0a9784f5b4ae94fad5325040"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9e18ae5de0ca8c6d0a9784f5b4ae94fad5325040", "type": "zip", "shasum": "", "reference": "9e18ae5de0ca8c6d0a9784f5b4ae94fad5325040"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.10"}, "time": "2021-05-26T12:52:38+00:00"}, {"version": "v5.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "864568fdc0208b3eba3638b6000b69d2386e6768"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/864568fdc0208b3eba3638b6000b69d2386e6768", "type": "zip", "shasum": "", "reference": "864568fdc0208b3eba3638b6000b69d2386e6768"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.8"}, "time": "2021-05-11T15:45:21+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "90374b8ed059325b49a29b55b3f8bb4062c87629"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/90374b8ed059325b49a29b55b3f8bb4062c87629", "type": "zip", "shasum": "", "reference": "90374b8ed059325b49a29b55b3f8bb4062c87629"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.7"}, "time": "2021-04-19T14:07:32+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "35f039df40a3b335ebf310f244cb242b3a83ac8d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/35f039df40a3b335ebf310f244cb242b3a83ac8d", "type": "zip", "shasum": "", "reference": "35f039df40a3b335ebf310f244cb242b3a83ac8d"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.6"}, "time": "2021-03-28T09:42:18+00:00"}, {"version": "v5.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "938ebbadae1b0a9c9d1ec313f87f9708609f1b79"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/938ebbadae1b0a9c9d1ec313f87f9708609f1b79", "type": "zip", "shasum": "", "reference": "938ebbadae1b0a9c9d1ec313f87f9708609f1b79"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.5"}, "time": "2021-03-06T13:42:15+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d6d0cc30d8c0fda4e7b213c20509b0159a8f4556"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d6d0cc30d8c0fda4e7b213c20509b0159a8f4556", "type": "zip", "shasum": "", "reference": "d6d0cc30d8c0fda4e7b213c20509b0159a8f4556"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.4"}, "time": "2021-02-23T10:08:49+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "89d4b176d12a2946a1ae4e34906a025b7b6b135a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/89d4b176d12a2946a1ae4e34906a025b7b6b135a", "type": "zip", "shasum": "", "reference": "89d4b176d12a2946a1ae4e34906a025b7b6b135a"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.3"}, "time": "2021-01-28T22:06:19+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d62ec79478b55036f65e2602e282822b8eaaff0a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d62ec79478b55036f65e2602e282822b8eaaff0a", "type": "zip", "shasum": "", "reference": "d62ec79478b55036f65e2602e282822b8eaaff0a"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.2"}, "time": "2021-01-27T10:15:41+00:00"}, {"description": "Symfony Console Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "47c02526c532fb381374dab26df05e7313978976"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/47c02526c532fb381374dab26df05e7313978976", "type": "zip", "shasum": "", "reference": "47c02526c532fb381374dab26df05e7313978976"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.1"}, "time": "2020-12-18T08:03:05+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3e0564fb08d44a98bd5f1960204c958e57bd586b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3e0564fb08d44a98bd5f1960204c958e57bd586b", "type": "zip", "shasum": "", "reference": "3e0564fb08d44a98bd5f1960204c958e57bd586b"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.0"}, "time": "2020-11-28T11:24:18+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d1d8b8fd9b605630aa73b1b384e246fee54e8ffa"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d1d8b8fd9b605630aa73b1b384e246fee54e8ffa", "type": "zip", "shasum": "", "reference": "d1d8b8fd9b605630aa73b1b384e246fee54e8ffa"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.0-RC2"}, "time": "2020-11-05T20:05:54+00:00"}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/console/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b8474c7497390d0ae2807c25944af1314db9846b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b8474c7497390d0ae2807c25944af1314db9846b", "type": "zip", "shasum": "", "reference": "b8474c7497390d0ae2807c25944af1314db9846b"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.0-BETA3"}, "time": "2020-10-27T15:37:34+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5d5ff78dc06109b534866efe6918973581ca68b2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5d5ff78dc06109b534866efe6918973581ca68b2", "type": "zip", "shasum": "", "reference": "5d5ff78dc06109b534866efe6918973581ca68b2"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9df6bfc5afd969d972f005a068576e48a0fccec0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9df6bfc5afd969d972f005a068576e48a0fccec0", "type": "zip", "shasum": "", "reference": "9df6bfc5afd969d972f005a068576e48a0fccec0"}, "support": {"source": "https://github.com/symfony/console/tree/v5.2.0-BETA1"}, "time": "2020-10-03T14:00:31+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Eases the creation of beautiful and testable command line interfaces", "keywords": [], "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d9a267b621c5082e0a6c659d73633b6fd28a8a08"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d9a267b621c5082e0a6c659d73633b6fd28a8a08", "type": "zip", "shasum": "", "reference": "d9a267b621c5082e0a6c659d73633b6fd28a8a08"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "extra": "__unset"}, {"description": "Symfony Console Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "60aeac975b988cdc7b5e3ed0903aa4dfe9f9efd4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/60aeac975b988cdc7b5e3ed0903aa4dfe9f9efd4", "type": "zip", "shasum": "", "reference": "60aeac975b988cdc7b5e3ed0903aa4dfe9f9efd4"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.10"}, "time": "2020-12-18T08:02:46+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "037b57ac42cafb64b7b55273fe1786f35d623077"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/037b57ac42cafb64b7b55273fe1786f35d623077", "type": "zip", "shasum": "", "reference": "037b57ac42cafb64b7b55273fe1786f35d623077"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.9"}, "time": "2020-11-28T10:57:20+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e0b2c29c0fa6a69089209bbe8fcff4df2a313d0e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e0b2c29c0fa6a69089209bbe8fcff4df2a313d0e", "type": "zip", "shasum": "", "reference": "e0b2c29c0fa6a69089209bbe8fcff4df2a313d0e"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ae789a8a2ad189ce7e8216942cdb9b77319f5eb8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ae789a8a2ad189ce7e8216942cdb9b77319f5eb8", "type": "zip", "shasum": "", "reference": "ae789a8a2ad189ce7e8216942cdb9b77319f5eb8"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.7"}, "time": "2020-10-07T15:23:00+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "04c3a31fe8ea94b42c9e2d1acc93d19782133b00"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/04c3a31fe8ea94b42c9e2d1acc93d19782133b00", "type": "zip", "shasum": "", "reference": "04c3a31fe8ea94b42c9e2d1acc93d19782133b00"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.6"}, "time": "2020-09-18T14:27:32+00:00"}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "186f395b256065ba9b890c0a4e48a91d598fa2cf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/186f395b256065ba9b890c0a4e48a91d598fa2cf", "type": "zip", "shasum": "", "reference": "186f395b256065ba9b890c0a4e48a91d598fa2cf"}, "support": {"source": "https://github.com/symfony/console/tree/5.1"}, "time": "2020-09-02T07:07:40+00:00"}, {"version": "v5.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "51ff337ce194bdc3d8db12b20ce8cd54ac9f71e9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/51ff337ce194bdc3d8db12b20ce8cd54ac9f71e9", "type": "zip", "shasum": "", "reference": "51ff337ce194bdc3d8db12b20ce8cd54ac9f71e9"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.4"}, "time": "2020-08-17T13:51:41+00:00"}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2226c68009627934b8cfc01260b4d287eab070df"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2226c68009627934b8cfc01260b4d287eab070df", "type": "zip", "shasum": "", "reference": "2226c68009627934b8cfc01260b4d287eab070df"}, "support": {"source": "https://github.com/symfony/console/tree/5.1"}, "time": "2020-07-06T13:23:11+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "34ac555a3627e324b660e318daa07572e1140123"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/34ac555a3627e324b660e318daa07572e1140123", "type": "zip", "shasum": "", "reference": "34ac555a3627e324b660e318daa07572e1140123"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.2"}, "time": "2020-06-15T12:59:21+00:00"}, {"version": "v5.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0f0a271bc9b7d02a053d36fcdcb12662e2a8096e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0f0a271bc9b7d02a053d36fcdcb12662e2a8096e", "type": "zip", "shasum": "", "reference": "0f0a271bc9b7d02a053d36fcdcb12662e2a8096e"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.1"}, "time": "2020-06-07T19:47:44+00:00"}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "00bed125812716d09b163f0727ef33bb49bf3448"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/00bed125812716d09b163f0727ef33bb49bf3448", "type": "zip", "shasum": "", "reference": "00bed125812716d09b163f0727ef33bb49bf3448"}, "support": {"source": "https://github.com/symfony/console/tree/5.1"}, "time": "2020-05-30T20:35:19+00:00"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "43cdfacce4338d3820350c2daa419ab836d6c469"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/43cdfacce4338d3820350c2daa419ab836d6c469", "type": "zip", "shasum": "", "reference": "43cdfacce4338d3820350c2daa419ab836d6c469"}, "time": "2020-05-24T08:49:09+00:00"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9242971b162522252532d83f93b8deae1a99a85d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9242971b162522252532d83f93b8deae1a99a85d", "type": "zip", "shasum": "", "reference": "9242971b162522252532d83f93b8deae1a99a85d"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2020-05-08T12:36:29+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "47aef3fb222c96713557f87f6fc3350ef439b470"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/47aef3fb222c96713557f87f6fc3350ef439b470", "type": "zip", "shasum": "", "reference": "47aef3fb222c96713557f87f6fc3350ef439b470"}, "support": {"source": "https://github.com/symfony/console/tree/v5.1.0-BETA1"}, "time": "2020-05-05T11:56:29+00:00"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "95794074741645473221fb126d5cb4057ad25bf1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/95794074741645473221fb126d5cb4057ad25bf1", "type": "zip", "shasum": "", "reference": "95794074741645473221fb126d5cb4057ad25bf1"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.11"}, "time": "2020-07-06T13:22:03+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f91588c06ab1a03cfd3b27c3335fae93ee90325d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f91588c06ab1a03cfd3b27c3335fae93ee90325d", "type": "zip", "shasum": "", "reference": "f91588c06ab1a03cfd3b27c3335fae93ee90325d"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.9"}, "time": "2020-05-30T20:12:43+00:00"}, {"version": "v5.0.9", "version_normalized": "*******"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5fa1caadc8cdaa17bcfb25219f3b53fe294a9935"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5fa1caadc8cdaa17bcfb25219f3b53fe294a9935", "type": "zip", "shasum": "", "reference": "5fa1caadc8cdaa17bcfb25219f3b53fe294a9935"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.7"}, "time": "2020-03-30T11:42:42+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.0.7", "version_normalized": "*******"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0085aec018950e1161bdf6bcb19fcb0828308cfc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0085aec018950e1161bdf6bcb19fcb0828308cfc", "type": "zip", "shasum": "", "reference": "0085aec018950e1161bdf6bcb19fcb0828308cfc"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.6"}, "time": "2020-03-16T13:02:39+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d29e2d36941de13600c399e393a60b8cfe59ac49"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d29e2d36941de13600c399e393a60b8cfe59ac49", "type": "zip", "shasum": "", "reference": "d29e2d36941de13600c399e393a60b8cfe59ac49"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.5"}, "time": "2020-02-24T15:05:31+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "91c294166c38d8c0858a86fad76d8c14dc1144c8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/91c294166c38d8c0858a86fad76d8c14dc1144c8", "type": "zip", "shasum": "", "reference": "91c294166c38d8c0858a86fad76d8c14dc1144c8"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.4"}, "time": "2020-01-25T15:56:29+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "345ab6ecb456b5147ea3b3271d7f1f00aadfd257"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/345ab6ecb456b5147ea3b3271d7f1f00aadfd257", "type": "zip", "shasum": "", "reference": "345ab6ecb456b5147ea3b3271d7f1f00aadfd257"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.3"}, "time": "2020-01-19T11:13:19+00:00"}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fe6e3cd889ca64172d7a742a2eb058541404ef47"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fe6e3cd889ca64172d7a742a2eb058541404ef47", "type": "zip", "shasum": "", "reference": "fe6e3cd889ca64172d7a742a2eb058541404ef47"}, "support": {"source": "https://github.com/symfony/console/tree/5.0"}, "time": "2019-12-17T13:20:22+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dae5ef273d700771168ab889d9f8a19b2d206656"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dae5ef273d700771168ab889d9f8a19b2d206656", "type": "zip", "shasum": "", "reference": "dae5ef273d700771168ab889d9f8a19b2d206656"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.1"}, "time": "2019-12-01T10:51:15+00:00"}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1ece22ce0c2d54572dafcd114cc08c0031cc0ac3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1ece22ce0c2d54572dafcd114cc08c0031cc0ac3", "type": "zip", "shasum": "", "reference": "1ece22ce0c2d54572dafcd114cc08c0031cc0ac3"}, "support": {"source": "https://github.com/symfony/console/tree/5.0"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9abbf185aef3061817d3ca1a4de29eac7b653a17"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9abbf185aef3061817d3ca1a4de29eac7b653a17", "type": "zip", "shasum": "", "reference": "9abbf185aef3061817d3ca1a4de29eac7b653a17"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2019-11-13T07:42:48+00:00", "require": {"php": "^7.2.9", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "de391bddbba48442d0d49bc203f375c91d689a5b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/de391bddbba48442d0d49bc203f375c91d689a5b", "type": "zip", "shasum": "", "reference": "de391bddbba48442d0d49bc203f375c91d689a5b"}, "support": {"source": "https://github.com/symfony/console/tree/v5.0.0-BETA1"}, "time": "2019-11-08T16:32:03+00:00"}, {"description": "Eases the creation of beautiful and testable command line interfaces", "version": "v4.4.49", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "33fa45ffc81fdcc1ca368d4946da859c8cdb58d9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/33fa45ffc81fdcc1ca368d4946da859c8cdb58d9", "type": "zip", "shasum": "", "reference": "33fa45ffc81fdcc1ca368d4946da859c8cdb58d9"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-05T17:10:16+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "require-dev": {"symfony/config": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0", "psr/log": "^1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "extra": "__unset"}, {"version": "v4.4.48", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8e70c1cab07ac641b885ce80385b9824a293c623"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8e70c1cab07ac641b885ce80385b9824a293c623", "type": "zip", "shasum": "", "reference": "8e70c1cab07ac641b885ce80385b9824a293c623"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.48"}, "time": "2022-10-26T16:02:45+00:00"}, {"version": "v4.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4f40012db8d55c956406890b5720f686fee7f7b7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4f40012db8d55c956406890b5720f686fee7f7b7", "type": "zip", "shasum": "", "reference": "4f40012db8d55c956406890b5720f686fee7f7b7"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.47"}, "time": "2022-10-04T05:58:30+00:00"}, {"version": "v4.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "28b77970939500fb04180166a1f716e75a871ef8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/28b77970939500fb04180166a1f716e75a871ef8", "type": "zip", "shasum": "", "reference": "28b77970939500fb04180166a1f716e75a871ef8"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.45"}, "time": "2022-08-17T14:50:19+00:00"}, {"version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c35fafd7f12ebd6f9e29c95a370df7f1fb171a40"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c35fafd7f12ebd6f9e29c95a370df7f1fb171a40", "type": "zip", "shasum": "", "reference": "c35fafd7f12ebd6f9e29c95a370df7f1fb171a40"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"version": "v4.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8a2628d2d5639f35113dc1b833ecd91e1ed1cf46"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8a2628d2d5639f35113dc1b833ecd91e1ed1cf46", "type": "zip", "shasum": "", "reference": "8a2628d2d5639f35113dc1b833ecd91e1ed1cf46"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.43"}, "time": "2022-06-23T12:22:25+00:00"}, {"version": "v4.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cce7a9f99e22937a71a16b23afa762558808d587"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cce7a9f99e22937a71a16b23afa762558808d587", "type": "zip", "shasum": "", "reference": "cce7a9f99e22937a71a16b23afa762558808d587"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.42"}, "time": "2022-05-14T12:35:33+00:00"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0e1e62083b20ccb39c2431293de060f756af905c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0e1e62083b20ccb39c2431293de060f756af905c", "type": "zip", "shasum": "", "reference": "0e1e62083b20ccb39c2431293de060f756af905c"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.41"}, "time": "2022-04-12T15:19:55+00:00"}, {"version": "v4.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bdcc66f3140421038f495e5b50e3ca6ffa14c773"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bdcc66f3140421038f495e5b50e3ca6ffa14c773", "type": "zip", "shasum": "", "reference": "bdcc66f3140421038f495e5b50e3ca6ffa14c773"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.40"}, "time": "2022-03-26T22:12:04+00:00"}, {"version": "v4.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5a50085bf5460f0c0d60a50b58388c1249826b8a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5a50085bf5460f0c0d60a50b58388c1249826b8a", "type": "zip", "shasum": "", "reference": "5a50085bf5460f0c0d60a50b58388c1249826b8a"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.38"}, "time": "2022-01-30T21:23:57+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0259f01dbf9d77badddbbf4c2abb681f24c9cac6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0259f01dbf9d77badddbbf4c2abb681f24c9cac6", "type": "zip", "shasum": "", "reference": "0259f01dbf9d77badddbbf4c2abb681f24c9cac6"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.37"}, "time": "2022-01-26T16:15:26+00:00"}, {"version": "v4.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "621379b62bb19af213b569b60013200b11dd576f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/621379b62bb19af213b569b60013200b11dd576f", "type": "zip", "shasum": "", "reference": "621379b62bb19af213b569b60013200b11dd576f"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.36"}, "time": "2021-12-15T10:33:10+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "329b3a75cc6b16d435ba1b1a41df54a53382a3f0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/329b3a75cc6b16d435ba1b1a41df54a53382a3f0", "type": "zip", "shasum": "", "reference": "329b3a75cc6b16d435ba1b1a41df54a53382a3f0"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.34"}, "time": "2021-11-04T12:23:33+00:00"}, {"version": "v4.4.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8dbd23ef7a8884051482183ddee8d9061b5feed0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8dbd23ef7a8884051482183ddee8d9061b5feed0", "type": "zip", "shasum": "", "reference": "8dbd23ef7a8884051482183ddee8d9061b5feed0"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.33"}, "time": "2021-10-25T16:36:08+00:00"}, {"version": "v4.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a3f7189a0665ee33b50e9e228c46f50f5acbed22"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a3f7189a0665ee33b50e9e228c46f50f5acbed22", "type": "zip", "shasum": "", "reference": "a3f7189a0665ee33b50e9e228c46f50f5acbed22"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.30"}, "time": "2021-08-25T19:27:26+00:00"}, {"version": "v4.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8baf0bbcfddfde7d7225ae8e04705cfd1081cd7b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8baf0bbcfddfde7d7225ae8e04705cfd1081cd7b", "type": "zip", "shasum": "", "reference": "8baf0bbcfddfde7d7225ae8e04705cfd1081cd7b"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.29"}, "time": "2021-07-27T19:04:53+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e523c86d2c727b128ce339a72733c9688e002ed3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e523c86d2c727b128ce339a72733c9688e002ed3", "type": "zip", "shasum": "", "reference": "e523c86d2c727b128ce339a72733c9688e002ed3"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.27"}, "time": "2021-07-22T08:29:31+00:00"}, {"version": "v4.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9aa1eb46c1b12fada74dc0c529e93d1ccef22576"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9aa1eb46c1b12fada74dc0c529e93d1ccef22576", "type": "zip", "shasum": "", "reference": "9aa1eb46c1b12fada74dc0c529e93d1ccef22576"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.26"}, "time": "2021-06-06T09:12:27+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "require-dev": {"symfony/config": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0", "psr/log": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a62acecdf5b50e314a4f305cd01b5282126f3095"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a62acecdf5b50e314a4f305cd01b5282126f3095", "type": "zip", "shasum": "", "reference": "a62acecdf5b50e314a4f305cd01b5282126f3095"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.25"}, "time": "2021-05-26T11:20:16+00:00"}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1b15ca1b1bedda86f98064da9ff5d800560d4c6d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1b15ca1b1bedda86f98064da9ff5d800560d4c6d", "type": "zip", "shasum": "", "reference": "1b15ca1b1bedda86f98064da9ff5d800560d4c6d"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.24"}, "time": "2021-05-13T06:28:07+00:00"}, {"version": "v4.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1ab187ac21d41d7d34a4f529091a1f5d0bb2924f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1ab187ac21d41d7d34a4f529091a1f5d0bb2924f", "type": "zip", "shasum": "", "reference": "1ab187ac21d41d7d34a4f529091a1f5d0bb2924f"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.23"}, "time": "2021-05-10T12:53:15+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "36bbd079b69b94bcc9c9c9e1e37ca3b1e7971625"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/36bbd079b69b94bcc9c9c9e1e37ca3b1e7971625", "type": "zip", "shasum": "", "reference": "36bbd079b69b94bcc9c9c9e1e37ca3b1e7971625"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.22"}, "time": "2021-04-16T17:32:19+00:00"}, {"version": "v4.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1ba4560dbbb9fcf5ae28b61f71f49c678086cf23"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1ba4560dbbb9fcf5ae28b61f71f49c678086cf23", "type": "zip", "shasum": "", "reference": "1ba4560dbbb9fcf5ae28b61f71f49c678086cf23"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.21"}, "time": "2021-03-26T09:23:24+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c98349bda966c70d6c08b4cd8658377c94166492"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c98349bda966c70d6c08b4cd8658377c94166492", "type": "zip", "shasum": "", "reference": "c98349bda966c70d6c08b4cd8658377c94166492"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.20"}, "time": "2021-02-22T18:44:15+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "24026c44fc37099fa145707fecd43672831b837a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/24026c44fc37099fa145707fecd43672831b837a", "type": "zip", "shasum": "", "reference": "24026c44fc37099fa145707fecd43672831b837a"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00"}, {"description": "Symfony Console Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "12e071278e396cc3e1c149857337e9e192deca0b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/12e071278e396cc3e1c149857337e9e192deca0b", "type": "zip", "shasum": "", "reference": "12e071278e396cc3e1c149857337e9e192deca0b"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.18"}, "time": "2020-12-18T07:41:31+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c8e37f6928c19816437a4dd7bf16e3bd79941470"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c8e37f6928c19816437a4dd7bf16e3bd79941470", "type": "zip", "shasum": "", "reference": "c8e37f6928c19816437a4dd7bf16e3bd79941470"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.17"}, "time": "2020-11-28T10:15:42+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "20f73dd143a5815d475e0838ff867bce1eebd9d5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/20f73dd143a5815d475e0838ff867bce1eebd9d5", "type": "zip", "shasum": "", "reference": "20f73dd143a5815d475e0838ff867bce1eebd9d5"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "90933b39c7b312fc3ceaa1ddeac7eb48cb953124"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/90933b39c7b312fc3ceaa1ddeac7eb48cb953124", "type": "zip", "shasum": "", "reference": "90933b39c7b312fc3ceaa1ddeac7eb48cb953124"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.15"}, "time": "2020-09-15T07:58:55+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/v4.4.14"}}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b39fd99b9297b67fb7633b7d8083957a97e1e727"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b39fd99b9297b67fb7633b7d8083957a97e1e727", "type": "zip", "shasum": "", "reference": "b39fd99b9297b67fb7633b7d8083957a97e1e727"}, "support": {"source": "https://github.com/symfony/console/tree/4.4"}, "time": "2020-09-02T07:07:21+00:00"}, {"version": "v4.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1f601a29fd7591a0316bffbc0d7550a5953c6c1c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1f601a29fd7591a0316bffbc0d7550a5953c6c1c", "type": "zip", "shasum": "", "reference": "1f601a29fd7591a0316bffbc0d7550a5953c6c1c"}, "time": "2020-08-17T07:39:58+00:00"}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "55d07021da933dd0d633ffdab6f45d5b230c7e02"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/55d07021da933dd0d633ffdab6f45d5b230c7e02", "type": "zip", "shasum": "", "reference": "55d07021da933dd0d633ffdab6f45d5b230c7e02"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.11"}, "time": "2020-07-06T13:18:39+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "326b064d804043005526f5a0494cfb49edb59bb0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/326b064d804043005526f5a0494cfb49edb59bb0", "type": "zip", "shasum": "", "reference": "326b064d804043005526f5a0494cfb49edb59bb0"}, "support": {"source": "https://github.com/symfony/console/tree/4.4"}, "time": "2020-05-30T20:06:45+00:00"}, {"version": "v4.4.9", "version_normalized": "*******"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "10bb3ee3c97308869d53b3e3d03f6ac23ff985f7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/10bb3ee3c97308869d53b3e3d03f6ac23ff985f7", "type": "zip", "shasum": "", "reference": "10bb3ee3c97308869d53b3e3d03f6ac23ff985f7"}, "time": "2020-03-30T11:41:10+00:00", "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v4.4.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/console/tree/v4.4.7"}}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "20bc0c1068565103075359f5ce9e0639b36f92d1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/20bc0c1068565103075359f5ce9e0639b36f92d1", "type": "zip", "shasum": "", "reference": "20bc0c1068565103075359f5ce9e0639b36f92d1"}, "support": {"source": "https://github.com/symfony/console/tree/4.4"}, "time": "2020-03-16T08:56:54+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4fa15ae7be74e53f6ec8c83ed403b97e23b665e9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4fa15ae7be74e53f6ec8c83ed403b97e23b665e9", "type": "zip", "shasum": "", "reference": "4fa15ae7be74e53f6ec8c83ed403b97e23b665e9"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.5"}, "time": "2020-02-24T13:10:00+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f512001679f37e6a042b51897ed24a2f05eba656"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f512001679f37e6a042b51897ed24a2f05eba656", "type": "zip", "shasum": "", "reference": "f512001679f37e6a042b51897ed24a2f05eba656"}, "support": {"source": "https://github.com/symfony/console/tree/4.4"}, "time": "2020-01-25T12:44:29+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e9ee09d087e2c88eaf6e5fc0f5c574f64d100e4f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e9ee09d087e2c88eaf6e5fc0f5c574f64d100e4f", "type": "zip", "shasum": "", "reference": "e9ee09d087e2c88eaf6e5fc0f5c574f64d100e4f"}, "time": "2020-01-10T21:54:01+00:00"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "82437719dab1e6bdd28726af14cb345c2ec816d0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/82437719dab1e6bdd28726af14cb345c2ec816d0", "type": "zip", "shasum": "", "reference": "82437719dab1e6bdd28726af14cb345c2ec816d0"}, "time": "2019-12-17T10:32:23+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f0aea3df20d15635b3cb9730ca5eea1c65b7f201"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f0aea3df20d15635b3cb9730ca5eea1c65b7f201", "type": "zip", "shasum": "", "reference": "f0aea3df20d15635b3cb9730ca5eea1c65b7f201"}, "time": "2019-12-01T10:06:17+00:00"}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "35d9077f495c6d184d9930f7a7ecbd1ad13c7ab8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/35d9077f495c6d184d9930f7a7ecbd1ad13c7ab8", "type": "zip", "shasum": "", "reference": "35d9077f495c6d184d9930f7a7ecbd1ad13c7ab8"}, "time": "2019-11-13T07:39:40+00:00"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f78ddec3953c759e13915ffdae1b32dc78ffc974"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f78ddec3953c759e13915ffdae1b32dc78ffc974", "type": "zip", "shasum": "", "reference": "f78ddec3953c759e13915ffdae1b32dc78ffc974"}, "support": {"source": "https://github.com/symfony/console/tree/v4.4.0-BETA1"}, "time": "2019-11-10T09:37:33+00:00"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "82aeab8f852a63e83d781617841237944392cd45"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/82aeab8f852a63e83d781617841237944392cd45", "type": "zip", "shasum": "", "reference": "82aeab8f852a63e83d781617841237944392cd45"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.11"}, "time": "2020-01-25T12:32:28+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1"}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/event-dispatcher": "^4.3", "symfony/dependency-injection": "~3.4|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "symfony/var-dumper": "^4.3", "psr/log": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3", "symfony/process": "<3.3"}}, {"version": "v4.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "487f139d21506279eaf93d4469255daba3d8fb70"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/487f139d21506279eaf93d4469255daba3d8fb70", "type": "zip", "shasum": "", "reference": "487f139d21506279eaf93d4469255daba3d8fb70"}, "support": {"source": "https://github.com/symfony/console/tree/4.3"}, "time": "2020-01-10T21:48:14+00:00"}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "92e3577f4310553c83e362db25cc73f9673217de"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/92e3577f4310553c83e362db25cc73f9673217de", "type": "zip", "shasum": "", "reference": "92e3577f4310553c83e362db25cc73f9673217de"}, "time": "2019-12-01T10:04:59+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "831424efae0a1fe6642784bd52aae14ece6538e6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/831424efae0a1fe6642784bd52aae14ece6538e6", "type": "zip", "shasum": "", "reference": "831424efae0a1fe6642784bd52aae14ece6538e6"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.8"}, "time": "2019-11-13T07:29:07+00:00"}, {"version": "v4.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d2e39dbddae68560fa6be0c576da6ad4e945b90d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d2e39dbddae68560fa6be0c576da6ad4e945b90d", "type": "zip", "shasum": "", "reference": "d2e39dbddae68560fa6be0c576da6ad4e945b90d"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.7"}, "time": "2019-11-05T15:00:49+00:00"}, {"version": "v4.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "136c4bd62ea871d00843d1bc0316de4c4a84bb78"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/136c4bd62ea871d00843d1bc0316de4c4a84bb78", "type": "zip", "shasum": "", "reference": "136c4bd62ea871d00843d1bc0316de4c4a84bb78"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.6"}, "time": "2019-10-30T12:58:49+00:00"}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "929ddf360d401b958f611d44e726094ab46a7369"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/929ddf360d401b958f611d44e726094ab46a7369", "type": "zip", "shasum": "", "reference": "929ddf360d401b958f611d44e726094ab46a7369"}, "support": {"source": "https://github.com/symfony/console/tree/4.3"}, "time": "2019-10-07T12:36:49+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "de63799239b3881b8a08f8481b22348f77ed7b36"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/de63799239b3881b8a08f8481b22348f77ed7b36", "type": "zip", "shasum": "", "reference": "de63799239b3881b8a08f8481b22348f77ed7b36"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.4"}, "time": "2019-08-26T08:26:39+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8b0ae5742ce9aaa8b0075665862c1ca397d1c1d9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8b0ae5742ce9aaa8b0075665862c1ca397d1c1d9", "type": "zip", "shasum": "", "reference": "8b0ae5742ce9aaa8b0075665862c1ca397d1c1d9"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.3"}, "time": "2019-07-24T17:13:59+00:00"}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b592b26a24265a35172d8a2094d8b10f22b7cc39"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b592b26a24265a35172d8a2094d8b10f22b7cc39", "type": "zip", "shasum": "", "reference": "b592b26a24265a35172d8a2094d8b10f22b7cc39"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.2"}, "time": "2019-06-13T11:03:18+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d50bbeeb0e17e6dd4124ea391eff235e932cbf64"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d50bbeeb0e17e6dd4124ea391eff235e932cbf64", "type": "zip", "shasum": "", "reference": "d50bbeeb0e17e6dd4124ea391eff235e932cbf64"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.1"}, "time": "2019-06-05T13:25:51+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "707b619d2c3bedf0224d56f95f77dabc60102305"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/707b619d2c3bedf0224d56f95f77dabc60102305", "type": "zip", "shasum": "", "reference": "707b619d2c3bedf0224d56f95f77dabc60102305"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.0"}, "time": "2019-05-27T08:16:38+00:00"}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/console/tree/4.3"}}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "260d24eac24351644343f042e316c4fc85d473af"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/260d24eac24351644343f042e316c4fc85d473af", "type": "zip", "shasum": "", "reference": "260d24eac24351644343f042e316c4fc85d473af"}, "time": "2019-05-09T09:33:12+00:00", "require": {"php": "^7.1.3", "symfony/contracts": "^1.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5c693cbf786fa8c5e28c1b8efb6366f9d9ca67af"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5c693cbf786fa8c5e28c1b8efb6366f9d9ca67af", "type": "zip", "shasum": "", "reference": "5c693cbf786fa8c5e28c1b8efb6366f9d9ca67af"}, "support": {"source": "https://github.com/symfony/console/tree/v4.3.0-BETA1"}, "time": "2019-05-08T20:09:06+00:00"}, {"version": "v4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fc2e274aade6567a750551942094b2145ade9b6c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fc2e274aade6567a750551942094b2145ade9b6c", "type": "zip", "shasum": "", "reference": "fc2e274aade6567a750551942094b2145ade9b6c"}, "support": {"source": "https://github.com/symfony/console/tree/4.2"}, "time": "2019-07-24T17:13:20+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.0", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/event-dispatcher": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "psr/log": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}}, {"version": "v4.2.11", "version_normalized": "********"}, {"version": "v4.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "79860cda331a2edb497c72ee487ed75c484ab75e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/79860cda331a2edb497c72ee487ed75c484ab75e", "type": "zip", "shasum": "", "reference": "79860cda331a2edb497c72ee487ed75c484ab75e"}, "time": "2019-06-13T10:57:15+00:00"}, {"version": "v4.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7a293c9a4587a92e6a0e81edb0bea54071b1b99d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7a293c9a4587a92e6a0e81edb0bea54071b1b99d", "type": "zip", "shasum": "", "reference": "7a293c9a4587a92e6a0e81edb0bea54071b1b99d"}, "time": "2019-05-09T09:19:46+00:00"}, {"version": "v4.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e2840bb38bddad7a0feaf85931e38fdcffdb2f81"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e2840bb38bddad7a0feaf85931e38fdcffdb2f81", "type": "zip", "shasum": "", "reference": "e2840bb38bddad7a0feaf85931e38fdcffdb2f81"}, "time": "2019-04-08T14:23:48+00:00"}, {"version": "v4.2.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/console/tree/v4.2.7"}}, {"version": "v4.2.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/console/tree/4.2"}}, {"version": "v4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "24206aff3efe6962593297e57ef697ebb220e384"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/24206aff3efe6962593297e57ef697ebb220e384", "type": "zip", "shasum": "", "reference": "24206aff3efe6962593297e57ef697ebb220e384"}, "support": {"source": "https://github.com/symfony/console/tree/v4.2.5"}, "time": "2019-04-01T07:32:59+00:00"}, {"version": "v4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9dc2299a016497f9ee620be94524e6c0af0280a9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9dc2299a016497f9ee620be94524e6c0af0280a9", "type": "zip", "shasum": "", "reference": "9dc2299a016497f9ee620be94524e6c0af0280a9"}, "support": {"source": "https://github.com/symfony/console/tree/4.2"}, "time": "2019-02-23T15:17:42+00:00"}, {"version": "v4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1f0ad51dfde4da8a6070f06adc58b4e37cbb37a4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1f0ad51dfde4da8a6070f06adc58b4e37cbb37a4", "type": "zip", "shasum": "", "reference": "1f0ad51dfde4da8a6070f06adc58b4e37cbb37a4"}, "time": "2019-01-25T14:35:16+00:00"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b0a03c1bb0fcbe288629956cf2f1dd3f1dc97522"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b0a03c1bb0fcbe288629956cf2f1dd3f1dc97522", "type": "zip", "shasum": "", "reference": "b0a03c1bb0fcbe288629956cf2f1dd3f1dc97522"}, "time": "2019-01-04T15:13:53+00:00", "suggest": {"psr/log-implementation": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "provide": "__unset"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4dff24e5d01e713818805c1862d2e3f901ee7dd0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4dff24e5d01e713818805c1862d2e3f901ee7dd0", "type": "zip", "shasum": "", "reference": "4dff24e5d01e713818805c1862d2e3f901ee7dd0"}, "time": "2018-11-27T07:40:44+00:00"}, {"version": "v4.2.0", "version_normalized": "*******"}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "10997603fb252831e6647dfdb3170e48c1d62499"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/10997603fb252831e6647dfdb3170e48c1d62499", "type": "zip", "shasum": "", "reference": "10997603fb252831e6647dfdb3170e48c1d62499"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2018-11-20T16:22:05+00:00"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f75de4cb9e4376c09e600a30174fefad9daa2272"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f75de4cb9e4376c09e600a30174fefad9daa2272", "type": "zip", "shasum": "", "reference": "f75de4cb9e4376c09e600a30174fefad9daa2272"}, "time": "2018-11-15T12:17:10+00:00"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0a26a2eadf8d06b48074daae9ce7e208d690e151"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0a26a2eadf8d06b48074daae9ce7e208d690e151", "type": "zip", "shasum": "", "reference": "0a26a2eadf8d06b48074daae9ce7e208d690e151"}, "time": "2018-10-31T10:56:31+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9e87c798f67dc9fceeb4f3d57847b52d945d1a02"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9e87c798f67dc9fceeb4f3d57847b52d945d1a02", "type": "zip", "shasum": "", "reference": "9e87c798f67dc9fceeb4f3d57847b52d945d1a02"}, "support": {"source": "https://github.com/symfony/console/tree/v4.1.12"}, "time": "2019-01-25T14:34:37+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "provide": {"psr/log-implementation": "1.0"}}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e798b40183701b297c43bb45f81fb2fa8d177b0a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e798b40183701b297c43bb45f81fb2fa8d177b0a", "type": "zip", "shasum": "", "reference": "e798b40183701b297c43bb45f81fb2fa8d177b0a"}, "time": "2019-01-04T15:13:30+00:00", "suggest": {"psr/log-implementation": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "provide": "__unset"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c74f4d1988dfcd8760273e53551694da32b056d0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c74f4d1988dfcd8760273e53551694da32b056d0", "type": "zip", "shasum": "", "reference": "c74f4d1988dfcd8760273e53551694da32b056d0"}, "time": "2018-11-26T14:00:40+00:00"}, {"version": "v4.1.8", "version_normalized": "*******"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "432122af37d8cd52fba1b294b11976e0d20df595"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/432122af37d8cd52fba1b294b11976e0d20df595", "type": "zip", "shasum": "", "reference": "432122af37d8cd52fba1b294b11976e0d20df595"}, "time": "2018-10-31T09:30:44+00:00"}, {"version": "v4.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dc7122fe5f6113cfaba3b3de575d31112c9aa60b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dc7122fe5f6113cfaba3b3de575d31112c9aa60b", "type": "zip", "shasum": "", "reference": "dc7122fe5f6113cfaba3b3de575d31112c9aa60b"}, "time": "2018-10-03T08:15:46+00:00"}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d3dbe91fd5b8b11ecb73508c844bc6a490de15b4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d3dbe91fd5b8b11ecb73508c844bc6a490de15b4", "type": "zip", "shasum": "", "reference": "d3dbe91fd5b8b11ecb73508c844bc6a490de15b4"}, "time": "2018-09-30T03:38:13+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ca80b8ced97cf07390078b29773dc384c39eee1f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ca80b8ced97cf07390078b29773dc384c39eee1f", "type": "zip", "shasum": "", "reference": "ca80b8ced97cf07390078b29773dc384c39eee1f"}, "time": "2018-07-26T11:24:31+00:00"}, {"version": "v4.1.3", "version_normalized": "*******"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5c31f6a97c1c240707f6d786e7e59bfacdbc0219"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5c31f6a97c1c240707f6d786e7e59bfacdbc0219", "type": "zip", "shasum": "", "reference": "5c31f6a97c1c240707f6d786e7e59bfacdbc0219"}, "time": "2018-07-16T14:05:40+00:00"}, {"version": "v4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "70591cda56b4b47c55776ac78e157c4bb6c8b43f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/70591cda56b4b47c55776ac78e157c4bb6c8b43f", "type": "zip", "shasum": "", "reference": "70591cda56b4b47c55776ac78e157c4bb6c8b43f"}, "time": "2018-05-31T10:17:53+00:00"}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2d5d973bf9933d46802b01010bd25c800c87c242"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2d5d973bf9933d46802b01010bd25c800c87c242", "type": "zip", "shasum": "", "reference": "2d5d973bf9933d46802b01010bd25c800c87c242"}, "time": "2018-05-30T07:26:09+00:00"}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "694be1112b8fc9d86020207add0d8af5ccc6127a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/694be1112b8fc9d86020207add0d8af5ccc6127a", "type": "zip", "shasum": "", "reference": "694be1112b8fc9d86020207add0d8af5ccc6127a"}, "time": "2018-05-16T14:33:22+00:00"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "00f24b3c437c902becd39a74670bd7fc253e50c5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/00f24b3c437c902becd39a74670bd7fc253e50c5", "type": "zip", "shasum": "", "reference": "00f24b3c437c902becd39a74670bd7fc253e50c5"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2018-05-04T00:03:52+00:00"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3179d1350843d2caa796973173dd0609ed4c7c67"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3179d1350843d2caa796973173dd0609ed4c7c67", "type": "zip", "shasum": "", "reference": "3179d1350843d2caa796973173dd0609ed4c7c67"}, "support": {"source": "https://github.com/symfony/console/tree/4.0"}, "time": "2018-07-26T11:22:46+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9f44418299c8b543a675ca6d2d6af5dbf40efd1d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9f44418299c8b543a675ca6d2d6af5dbf40efd1d", "type": "zip", "shasum": "", "reference": "9f44418299c8b543a675ca6d2d6af5dbf40efd1d"}, "time": "2018-07-16T13:59:46+00:00"}, {"version": "v4.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1cc17d0e1cae585fb136976e1234de9d605e2e53"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1cc17d0e1cae585fb136976e1234de9d605e2e53", "type": "zip", "shasum": "", "reference": "1cc17d0e1cae585fb136976e1234de9d605e2e53"}, "time": "2018-05-31T10:16:04+00:00"}, {"version": "v4.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "058f120b8e06ebcd7b211de5ffae07b2db00fbdd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/058f120b8e06ebcd7b211de5ffae07b2db00fbdd", "type": "zip", "shasum": "", "reference": "058f120b8e06ebcd7b211de5ffae07b2db00fbdd"}, "time": "2018-05-16T09:05:32+00:00"}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3e820bc2c520a87ca209ad8fa961c97f42e0b4ae"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3e820bc2c520a87ca209ad8fa961c97f42e0b4ae", "type": "zip", "shasum": "", "reference": "3e820bc2c520a87ca209ad8fa961c97f42e0b4ae"}, "time": "2018-04-30T01:23:47+00:00"}, {"version": "v4.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "aad9a6fe47319f22748fd764f52d3a7ca6fa6b64"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/aad9a6fe47319f22748fd764f52d3a7ca6fa6b64", "type": "zip", "shasum": "", "reference": "aad9a6fe47319f22748fd764f52d3a7ca6fa6b64"}, "time": "2018-04-03T05:24:00+00:00", "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}}, {"version": "v4.0.7", "version_normalized": "*******"}, {"version": "v4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "555c8dbe0ae9e561740451eabdbed2cc554b6a51"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/555c8dbe0ae9e561740451eabdbed2cc554b6a51", "type": "zip", "shasum": "", "reference": "555c8dbe0ae9e561740451eabdbed2cc554b6a51"}, "time": "2018-02-26T15:55:47+00:00"}, {"version": "v4.0.5", "version_normalized": "*******"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "36d5b41e7d4e1ccf0370f6babe966c08ef0a1488"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/36d5b41e7d4e1ccf0370f6babe966c08ef0a1488", "type": "zip", "shasum": "", "reference": "36d5b41e7d4e1ccf0370f6babe966c08ef0a1488"}, "time": "2018-01-29T09:06:29+00:00"}, {"version": "v4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fe0e69d7162cba0885791cf7eea5f0d7bc0f897e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fe0e69d7162cba0885791cf7eea5f0d7bc0f897e", "type": "zip", "shasum": "", "reference": "fe0e69d7162cba0885791cf7eea5f0d7bc0f897e"}, "time": "2018-01-03T07:38:00+00:00"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "de8cf039eacdec59d83f7def67e3b8ff5ed46714"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/de8cf039eacdec59d83f7def67e3b8ff5ed46714", "type": "zip", "shasum": "", "reference": "de8cf039eacdec59d83f7def67e3b8ff5ed46714"}, "time": "2017-12-14T19:48:22+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2ff6acc1342a0e7c3ad757a80fd837f5b22535a1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2ff6acc1342a0e7c3ad757a80fd837f5b22535a1", "type": "zip", "shasum": "", "reference": "2ff6acc1342a0e7c3ad757a80fd837f5b22535a1"}, "time": "2017-12-04T12:31:58+00:00"}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5cd0dd461dfc72f59c8405cac32d31e82c7348e8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5cd0dd461dfc72f59c8405cac32d31e82c7348e8", "type": "zip", "shasum": "", "reference": "5cd0dd461dfc72f59c8405cac32d31e82c7348e8"}, "time": "2017-11-29T13:42:03+00:00"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "00229c3703db3aace4246f6a5e5843670e7f5d72"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/00229c3703db3aace4246f6a5e5843670e7f5d72", "type": "zip", "shasum": "", "reference": "00229c3703db3aace4246f6a5e5843670e7f5d72"}, "time": "2017-11-23T09:03:09+00:00"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "08ab3151fc662592011a196e4579009d6f296aa8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/08ab3151fc662592011a196e4579009d6f296aa8", "type": "zip", "shasum": "", "reference": "08ab3151fc662592011a196e4579009d6f296aa8"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2017-11-19T18:43:46+00:00"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "481dcfc9c0be0ed91be456ab72960ee8a07168e2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/481dcfc9c0be0ed91be456ab72960ee8a07168e2", "type": "zip", "shasum": "", "reference": "481dcfc9c0be0ed91be456ab72960ee8a07168e2"}, "time": "2017-11-12T16:54:20+00:00", "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/debug": "~3.4|~4.0"}}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "77848ab790c4e7dcce7e5e40733dc2e45cf829bf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/77848ab790c4e7dcce7e5e40733dc2e45cf829bf", "type": "zip", "shasum": "", "reference": "77848ab790c4e7dcce7e5e40733dc2e45cf829bf"}, "time": "2017-11-05T16:26:21+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7fbcbad00ff2128b94c5ed6dea38706fbd2366a9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7fbcbad00ff2128b94c5ed6dea38706fbd2366a9", "type": "zip", "shasum": "", "reference": "7fbcbad00ff2128b94c5ed6dea38706fbd2366a9"}, "time": "2017-10-24T14:41:02+00:00"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "315748809dc4424c2e7a5dcdaee1ceb6aca34f02"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/315748809dc4424c2e7a5dcdaee1ceb6aca34f02", "type": "zip", "shasum": "", "reference": "315748809dc4424c2e7a5dcdaee1ceb6aca34f02"}, "time": "2017-10-19T00:12:27+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a10b1da6fc93080c180bba7219b5ff5b7518fe81", "type": "zip", "shasum": "", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00", "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0", "symfony/debug": "~2.8|~3.0|~4.0"}, "require-dev": {"symfony/config": "~3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/v3.4.46"}}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b28996bc0a3b08914b2a8609163ec35b36b30685"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b28996bc0a3b08914b2a8609163ec35b36b30685", "type": "zip", "shasum": "", "reference": "b28996bc0a3b08914b2a8609163ec35b36b30685"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.45"}, "time": "2020-09-09T05:09:37+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "71da881ad579f0cd66aef8677e4cf6217d8ecd0c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/71da881ad579f0cd66aef8677e4cf6217d8ecd0c", "type": "zip", "shasum": "", "reference": "71da881ad579f0cd66aef8677e4cf6217d8ecd0c"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.44"}, "time": "2020-08-09T08:16:57+00:00"}, {"version": "v3.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "afc7189694d2c59546cf24ea606a236fa46a966e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/afc7189694d2c59546cf24ea606a236fa46a966e", "type": "zip", "shasum": "", "reference": "afc7189694d2c59546cf24ea606a236fa46a966e"}, "support": {"source": "https://github.com/symfony/console/tree/3.4"}, "time": "2020-07-06T08:57:31+00:00"}, {"version": "v3.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bfe29ead7e7b1cc9ce74c6a40d06ad1f96fced13"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bfe29ead7e7b1cc9ce74c6a40d06ad1f96fced13", "type": "zip", "shasum": "", "reference": "bfe29ead7e7b1cc9ce74c6a40d06ad1f96fced13"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.41"}, "time": "2020-05-30T18:58:05+00:00"}, {"version": "v3.4.41", "version_normalized": "********"}, {"version": "v3.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bf60d5e606cd595391c5f82bf6b570d9573fa120"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bf60d5e606cd595391c5f82bf6b570d9573fa120", "type": "zip", "shasum": "", "reference": "bf60d5e606cd595391c5f82bf6b570d9573fa120"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.39"}, "time": "2020-03-27T17:07:22+00:00"}, {"version": "v3.4.39", "version_normalized": "********"}, {"version": "v3.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6827023c5872bea44b29d145de693b21981cf4cd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6827023c5872bea44b29d145de693b21981cf4cd", "type": "zip", "shasum": "", "reference": "6827023c5872bea44b29d145de693b21981cf4cd"}, "support": {"source": "https://github.com/symfony/console/tree/3.4"}, "funding": [], "time": "2020-02-15T13:27:16+00:00"}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7c5bdd346f9d90a2d22d4e1fe61e02dc19b98f12"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7c5bdd346f9d90a2d22d4e1fe61e02dc19b98f12", "type": "zip", "shasum": "", "reference": "7c5bdd346f9d90a2d22d4e1fe61e02dc19b98f12"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.37"}, "time": "2020-01-10T07:52:48+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1ee23b3b659b06c622f2bd2492a229e416eb4586"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1ee23b3b659b06c622f2bd2492a229e416eb4586", "type": "zip", "shasum": "", "reference": "1ee23b3b659b06c622f2bd2492a229e416eb4586"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.36"}, "time": "2019-12-01T10:04:45+00:00"}, {"version": "v3.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "17b154f932c5874cdbda6d05796b6490eec9f9f7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/17b154f932c5874cdbda6d05796b6490eec9f9f7", "type": "zip", "shasum": "", "reference": "17b154f932c5874cdbda6d05796b6490eec9f9f7"}, "support": {"source": "https://github.com/symfony/console/tree/3.4"}, "time": "2019-11-13T07:12:39+00:00"}, {"version": "v3.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c7edffb26b29cae972ca4afccb610a38ce8d0ccf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c7edffb26b29cae972ca4afccb610a38ce8d0ccf", "type": "zip", "shasum": "", "reference": "c7edffb26b29cae972ca4afccb610a38ce8d0ccf"}, "time": "2019-10-24T15:33:53+00:00"}, {"version": "v3.4.33", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/v3.4.33"}}, {"version": "v3.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4727d7f3c99b9dea0ae70ed4f34645728aa90453"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4727d7f3c99b9dea0ae70ed4f34645728aa90453", "type": "zip", "shasum": "", "reference": "4727d7f3c99b9dea0ae70ed4f34645728aa90453"}, "support": {"source": "https://github.com/symfony/console/tree/3.4"}, "time": "2019-10-06T19:52:09+00:00"}, {"version": "v3.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4510f04e70344d70952566e4262a0b11df39cb10"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4510f04e70344d70952566e4262a0b11df39cb10", "type": "zip", "shasum": "", "reference": "4510f04e70344d70952566e4262a0b11df39cb10"}, "time": "2019-08-26T07:52:58+00:00"}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "12940f20a816c978860fa4925b3f1bbb27e9ac46"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/12940f20a816c978860fa4925b3f1bbb27e9ac46", "type": "zip", "shasum": "", "reference": "12940f20a816c978860fa4925b3f1bbb27e9ac46"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.30"}, "time": "2019-07-24T14:46:41+00:00"}, {"version": "v3.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c4d2f3529755ffc0be9fb823583b28d8744eeb3d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c4d2f3529755ffc0be9fb823583b28d8744eeb3d", "type": "zip", "shasum": "", "reference": "c4d2f3529755ffc0be9fb823583b28d8744eeb3d"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.29"}, "time": "2019-06-05T11:33:52+00:00"}, {"version": "v3.4.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8e1d1e406dd31727fa70cd5a99cda202e9d6a5c6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8e1d1e406dd31727fa70cd5a99cda202e9d6a5c6", "type": "zip", "shasum": "", "reference": "8e1d1e406dd31727fa70cd5a99cda202e9d6a5c6"}, "support": {"source": "https://github.com/symfony/console/tree/3.4"}, "time": "2019-05-09T08:42:51+00:00"}, {"version": "v3.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "15a9104356436cb26e08adab97706654799d31d8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/15a9104356436cb26e08adab97706654799d31d8", "type": "zip", "shasum": "", "reference": "15a9104356436cb26e08adab97706654799d31d8"}, "support": {"source": "https://github.com/symfony/console/tree/v3.4.26"}, "time": "2019-04-08T09:29:13+00:00"}, {"version": "v3.4.26", "version_normalized": "********"}, {"version": "v3.4.25", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/3.4"}}, {"version": "v3.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "98ae3cdc4bec48fe7ee24afc81dbb4a242186c9e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/98ae3cdc4bec48fe7ee24afc81dbb4a242186c9e", "type": "zip", "shasum": "", "reference": "98ae3cdc4bec48fe7ee24afc81dbb4a242186c9e"}, "time": "2019-03-31T11:33:18+00:00"}, {"version": "v3.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "71ce77f37af0c5ffb9590e43cc4f70e426945c5e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/71ce77f37af0c5ffb9590e43cc4f70e426945c5e", "type": "zip", "shasum": "", "reference": "71ce77f37af0c5ffb9590e43cc4f70e426945c5e"}, "time": "2019-02-23T15:06:07+00:00"}, {"version": "v3.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "069bf3f0e8f871a2169a06e43d9f3f03f355e9be"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/069bf3f0e8f871a2169a06e43d9f3f03f355e9be", "type": "zip", "shasum": "", "reference": "069bf3f0e8f871a2169a06e43d9f3f03f355e9be"}, "time": "2019-01-25T10:42:12+00:00"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a700b874d3692bc8342199adfb6d3b99f62cc61a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a700b874d3692bc8342199adfb6d3b99f62cc61a", "type": "zip", "shasum": "", "reference": "a700b874d3692bc8342199adfb6d3b99f62cc61a"}, "time": "2019-01-04T04:42:43+00:00", "suggest": {"psr/log-implementation": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "provide": "__unset"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8f80fc39bbc3b7c47ee54ba7aa2653521ace94bb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8f80fc39bbc3b7c47ee54ba7aa2653521ace94bb", "type": "zip", "shasum": "", "reference": "8f80fc39bbc3b7c47ee54ba7aa2653521ace94bb"}, "time": "2018-11-26T12:48:07+00:00"}, {"version": "v3.4.19", "version_normalized": "********"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1d228fb4602047d7b26a0554e0d3efd567da5803"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1d228fb4602047d7b26a0554e0d3efd567da5803", "type": "zip", "shasum": "", "reference": "1d228fb4602047d7b26a0554e0d3efd567da5803"}, "time": "2018-10-30T16:50:50+00:00"}, {"version": "v3.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3b2b415d4c48fbefca7dc742aa0a0171bfae4e0b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3b2b415d4c48fbefca7dc742aa0a0171bfae4e0b", "type": "zip", "shasum": "", "reference": "3b2b415d4c48fbefca7dc742aa0a0171bfae4e0b"}, "time": "2018-10-02T16:33:53+00:00"}, {"version": "v3.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1cbaac35024c9dfc9612b7e2310e82677bf85709"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1cbaac35024c9dfc9612b7e2310e82677bf85709", "type": "zip", "shasum": "", "reference": "1cbaac35024c9dfc9612b7e2310e82677bf85709"}, "time": "2018-09-30T03:37:36+00:00"}, {"version": "v3.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6b217594552b9323bcdcfc14f8a0ce126e84cd73"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6b217594552b9323bcdcfc14f8a0ce126e84cd73", "type": "zip", "shasum": "", "reference": "6b217594552b9323bcdcfc14f8a0ce126e84cd73"}, "time": "2018-07-26T11:19:56+00:00"}, {"version": "v3.4.14", "version_normalized": "********"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e54f84c50e3b12972e7750edfc5ca84b2284c44e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e54f84c50e3b12972e7750edfc5ca84b2284c44e", "type": "zip", "shasum": "", "reference": "e54f84c50e3b12972e7750edfc5ca84b2284c44e"}, "time": "2018-07-10T14:02:11+00:00"}, {"version": "v3.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1b97071a26d028c9bd4588264e101e14f6e7cd00"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1b97071a26d028c9bd4588264e101e14f6e7cd00", "type": "zip", "shasum": "", "reference": "1b97071a26d028c9bd4588264e101e14f6e7cd00"}, "time": "2018-05-23T05:02:55+00:00"}, {"version": "v3.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "36f83f642443c46f3cf751d4d2ee5d047d757a27"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/36f83f642443c46f3cf751d4d2ee5d047d757a27", "type": "zip", "shasum": "", "reference": "36f83f642443c46f3cf751d4d2ee5d047d757a27"}, "time": "2018-05-16T08:49:21+00:00"}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5b1fdfa8eb93464bcc36c34da39cedffef822cdf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5b1fdfa8eb93464bcc36c34da39cedffef822cdf", "type": "zip", "shasum": "", "reference": "5b1fdfa8eb93464bcc36c34da39cedffef822cdf"}, "time": "2018-04-30T01:22:56+00:00"}, {"version": "v3.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d4bb70fa24d540c309d88a9d6e43fb2d339b1fbf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d4bb70fa24d540c309d88a9d6e43fb2d339b1fbf", "type": "zip", "shasum": "", "reference": "d4bb70fa24d540c309d88a9d6e43fb2d339b1fbf"}, "time": "2018-04-03T05:22:50+00:00", "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}}, {"version": "v3.4.7", "version_normalized": "*******"}, {"version": "v3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "067339e9b8ec30d5f19f5950208893ff026b94f7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/067339e9b8ec30d5f19f5950208893ff026b94f7", "type": "zip", "shasum": "", "reference": "067339e9b8ec30d5f19f5950208893ff026b94f7"}, "time": "2018-02-26T15:46:28+00:00"}, {"version": "v3.4.5", "version_normalized": "*******"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "26b6f419edda16c19775211987651cb27baea7f1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/26b6f419edda16c19775211987651cb27baea7f1", "type": "zip", "shasum": "", "reference": "26b6f419edda16c19775211987651cb27baea7f1"}, "time": "2018-01-29T09:03:43+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8394c8ef121949e8f858f13bc1e34f05169e4e7d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8394c8ef121949e8f858f13bc1e34f05169e4e7d", "type": "zip", "shasum": "", "reference": "8394c8ef121949e8f858f13bc1e34f05169e4e7d"}, "time": "2018-01-03T07:37:34+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9f21adfb92a9315b73ae2ed43138988ee4913d4e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9f21adfb92a9315b73ae2ed43138988ee4913d4e", "type": "zip", "shasum": "", "reference": "9f21adfb92a9315b73ae2ed43138988ee4913d4e"}, "time": "2017-12-14T19:40:10+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2cdef78de8f54f68ff16a857e710e7302b47d4c7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2cdef78de8f54f68ff16a857e710e7302b47d4c7", "type": "zip", "shasum": "", "reference": "2cdef78de8f54f68ff16a857e710e7302b47d4c7"}, "time": "2017-12-02T18:20:11+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9468ad3fba3a5e1f0dc12a96e50e84cddb923cf0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9468ad3fba3a5e1f0dc12a96e50e84cddb923cf0", "type": "zip", "shasum": "", "reference": "9468ad3fba3a5e1f0dc12a96e50e84cddb923cf0"}, "time": "2017-11-29T13:28:14+00:00"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "72b0921e1c59928b61be8b7a7a98ae5a4f2a02e1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/72b0921e1c59928b61be8b7a7a98ae5a4f2a02e1", "type": "zip", "shasum": "", "reference": "72b0921e1c59928b61be8b7a7a98ae5a4f2a02e1"}, "time": "2017-11-19T18:41:20+00:00"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0f859c42d6b557fdac74754c9823170765ae3872"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0f859c42d6b557fdac74754c9823170765ae3872", "type": "zip", "shasum": "", "reference": "0f859c42d6b557fdac74754c9823170765ae3872"}, "time": "2017-11-12T16:53:54+00:00"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "379744aa3f7ccc56d0b767d0a27edc44b3bef8d9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/379744aa3f7ccc56d0b767d0a27edc44b3bef8d9", "type": "zip", "shasum": "", "reference": "379744aa3f7ccc56d0b767d0a27edc44b3bef8d9"}, "time": "2017-11-05T16:10:10+00:00"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8e5f4d2b26b0a158bd7c83ffc780ffcd08fd3bb3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8e5f4d2b26b0a158bd7c83ffc780ffcd08fd3bb3", "type": "zip", "shasum": "", "reference": "8e5f4d2b26b0a158bd7c83ffc780ffcd08fd3bb3"}, "time": "2017-10-24T14:40:29+00:00"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0b3c68603ca452d69d713ca5551b5a8799d938f1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0b3c68603ca452d69d713ca5551b5a8799d938f1", "type": "zip", "shasum": "", "reference": "0b3c68603ca452d69d713ca5551b5a8799d938f1"}, "time": "2017-10-15T12:36:44+00:00"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "af7ec995de93671c03cc1b4e3176c8588bc79dcc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/af7ec995de93671c03cc1b4e3176c8588bc79dcc", "type": "zip", "shasum": "", "reference": "af7ec995de93671c03cc1b4e3176c8588bc79dcc"}, "support": {"source": "https://github.com/symfony/console/tree/3.3"}, "time": "2018-01-29T09:02:23+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0", "symfony/debug": "~2.8|~3.0"}, "require-dev": {"symfony/config": "~3.3", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "psr/log": "~1.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/filesystem": "", "symfony/process": ""}, "conflict": {"symfony/dependency-injection": "<3.3"}}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "56f07f63c80baeb43ab259abc42c6cecf6461fae"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/56f07f63c80baeb43ab259abc42c6cecf6461fae", "type": "zip", "shasum": "", "reference": "56f07f63c80baeb43ab259abc42c6cecf6461fae"}, "time": "2018-01-03T07:37:11+00:00"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "55497618e68845b6f92a66d13187138ac3d7750e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/55497618e68845b6f92a66d13187138ac3d7750e", "type": "zip", "shasum": "", "reference": "55497618e68845b6f92a66d13187138ac3d7750e"}, "time": "2017-11-29T12:25:49+00:00"}, {"version": "v3.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "63cd7960a0a522c3537f6326706d7f3b8de65805"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/63cd7960a0a522c3537f6326706d7f3b8de65805", "type": "zip", "shasum": "", "reference": "63cd7960a0a522c3537f6326706d7f3b8de65805"}, "time": "2017-11-16T15:24:32+00:00"}, {"version": "v3.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "099302cc53e57cbb7414fd9f3ace40e5e2767c0b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/099302cc53e57cbb7414fd9f3ace40e5e2767c0b", "type": "zip", "shasum": "", "reference": "099302cc53e57cbb7414fd9f3ace40e5e2767c0b"}, "time": "2017-11-12T16:53:41+00:00"}, {"version": "v3.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fd684d68f83568d8293564b4971928a2c4bdfc5c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fd684d68f83568d8293564b4971928a2c4bdfc5c", "type": "zip", "shasum": "", "reference": "fd684d68f83568d8293564b4971928a2c4bdfc5c"}, "time": "2017-11-07T14:16:22+00:00"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "116bc56e45a8e5572e51eb43ab58c769a352366c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/116bc56e45a8e5572e51eb43ab58c769a352366c", "type": "zip", "shasum": "", "reference": "116bc56e45a8e5572e51eb43ab58c769a352366c"}, "time": "2017-10-02T06:42:24+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a1e1b01293a090cb9ae2ddd221a3251a4a7e4abf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a1e1b01293a090cb9ae2ddd221a3251a4a7e4abf", "type": "zip", "shasum": "", "reference": "a1e1b01293a090cb9ae2ddd221a3251a4a7e4abf"}, "time": "2017-09-06T16:40:18+00:00"}, {"version": "v3.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d6596cb5022b6a0bd940eae54a1de78646a5fda6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d6596cb5022b6a0bd940eae54a1de78646a5fda6", "type": "zip", "shasum": "", "reference": "d6596cb5022b6a0bd940eae54a1de78646a5fda6"}, "time": "2017-08-27T14:52:21+00:00"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b0878233cb5c4391347e5495089c7af11b8e6201"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b0878233cb5c4391347e5495089c7af11b8e6201", "type": "zip", "shasum": "", "reference": "b0878233cb5c4391347e5495089c7af11b8e6201"}, "time": "2017-07-29T21:27:59+00:00", "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0", "symfony/debug": "~2.8|~3.0"}, "require-dev": {"symfony/config": "~3.3", "symfony/http-kernel": "~2.8|~3.0", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "psr/log": "~1.0"}}, {"version": "v3.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a97e45d98c59510f085fa05225a1acb74dfe0546"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a97e45d98c59510f085fa05225a1acb74dfe0546", "type": "zip", "shasum": "", "reference": "a97e45d98c59510f085fa05225a1acb74dfe0546"}, "time": "2017-07-03T13:19:36+00:00"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "70d2a29b2911cbdc91a7e268046c395278238b2e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/70d2a29b2911cbdc91a7e268046c395278238b2e", "type": "zip", "shasum": "", "reference": "70d2a29b2911cbdc91a7e268046c395278238b2e"}, "time": "2017-06-02T19:24:58+00:00"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c80e63f3f5e3a331bfc25e6e9332b10422eb9b05"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c80e63f3f5e3a331bfc25e6e9332b10422eb9b05", "type": "zip", "shasum": "", "reference": "c80e63f3f5e3a331bfc25e6e9332b10422eb9b05"}, "time": "2017-05-28T14:08:56+00:00", "require-dev": {"symfony/http-kernel": "~2.8|~3.0", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "psr/log": "~1.0"}}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bdbaaa623c914a4654450452351c45857708dbcf"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bdbaaa623c914a4654450452351c45857708dbcf", "type": "zip", "shasum": "", "reference": "bdbaaa623c914a4654450452351c45857708dbcf"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2017-05-15T12:04:53+00:00"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0f0470206c5e0f768459fb07c3480683e43f5018"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0f0470206c5e0f768459fb07c3480683e43f5018", "type": "zip", "shasum": "", "reference": "0f0470206c5e0f768459fb07c3480683e43f5018"}, "time": "2017-04-29T09:46:23+00:00"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "eced439413608647aeff243038a33ea246b2b33a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/eced439413608647aeff243038a33ea246b2b33a", "type": "zip", "shasum": "", "reference": "eced439413608647aeff243038a33ea246b2b33a"}, "support": {"source": "https://github.com/symfony/console/tree/3.2"}, "time": "2017-07-29T21:27:41+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require-dev": {"symfony/event-dispatcher": "~2.8|~3.0", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "psr/log": "~1.0"}, "conflict": "__unset"}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c091707512e290806794161201c0b021205583f2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c091707512e290806794161201c0b021205583f2", "type": "zip", "shasum": "", "reference": "c091707512e290806794161201c0b021205583f2"}, "time": "2017-07-03T08:06:20+00:00"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2a23fb0e7a76e618ecd5e0392d2484a9f7e367ef"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2a23fb0e7a76e618ecd5e0392d2484a9f7e367ef", "type": "zip", "shasum": "", "reference": "2a23fb0e7a76e618ecd5e0392d2484a9f7e367ef"}, "time": "2017-05-28T14:08:39+00:00"}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a7a17e0c6c3c4d70a211f80782e4b90ddadeaa38"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a7a17e0c6c3c4d70a211f80782e4b90ddadeaa38", "type": "zip", "shasum": "", "reference": "a7a17e0c6c3c4d70a211f80782e4b90ddadeaa38"}, "time": "2017-04-26T01:39:17+00:00"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c30243cc51f726812be3551316b109a2f5deaf8d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c30243cc51f726812be3551316b109a2f5deaf8d", "type": "zip", "shasum": "", "reference": "c30243cc51f726812be3551316b109a2f5deaf8d"}, "time": "2017-04-04T14:33:42+00:00"}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "28fb243a2b5727774ca309ec2d92da240f1af0dd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/28fb243a2b5727774ca309ec2d92da240f1af0dd", "type": "zip", "shasum": "", "reference": "28fb243a2b5727774ca309ec2d92da240f1af0dd"}, "time": "2017-03-06T19:30:27+00:00"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0e5e6899f82230fcb1153bcaf0e106ffaa44b870"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0e5e6899f82230fcb1153bcaf0e106ffaa44b870", "type": "zip", "shasum": "", "reference": "0e5e6899f82230fcb1153bcaf0e106ffaa44b870"}, "time": "2017-02-16T14:07:22+00:00"}, {"version": "v3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7a8405a9fc175f87fed8a3c40856b0d866d61936"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7a8405a9fc175f87fed8a3c40856b0d866d61936", "type": "zip", "shasum": "", "reference": "7a8405a9fc175f87fed8a3c40856b0d866d61936"}, "time": "2017-02-06T12:04:21+00:00"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4f9e449e76996adf310498a8ca955c6deebe29dd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4f9e449e76996adf310498a8ca955c6deebe29dd", "type": "zip", "shasum": "", "reference": "4f9e449e76996adf310498a8ca955c6deebe29dd"}, "time": "2017-01-08T20:47:33+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d12aa9ca20f4db83ec58410978dab6afcb9d6aaa"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d12aa9ca20f4db83ec58410978dab6afcb9d6aaa", "type": "zip", "shasum": "", "reference": "d12aa9ca20f4db83ec58410978dab6afcb9d6aaa"}, "time": "2016-12-11T14:34:22+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "09d0fd33560e3573185a2ea17614e37ba38716c5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/09d0fd33560e3573185a2ea17614e37ba38716c5", "type": "zip", "shasum": "", "reference": "09d0fd33560e3573185a2ea17614e37ba38716c5"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2016-11-16T22:18:16+00:00"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "72fafa731ed6dcfdeadc60ed92922e8d6ff451b5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/72fafa731ed6dcfdeadc60ed92922e8d6ff451b5", "type": "zip", "shasum": "", "reference": "72fafa731ed6dcfdeadc60ed92922e8d6ff451b5"}, "time": "2016-10-21T21:28:04+00:00"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "047f16485d68c083bd5d9b73ff16f9cb9c1a9f52"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/047f16485d68c083bd5d9b73ff16f9cb9c1a9f52", "type": "zip", "shasum": "", "reference": "047f16485d68c083bd5d9b73ff16f9cb9c1a9f52"}, "support": {"source": "https://github.com/symfony/console/tree/3.1"}, "time": "2017-01-08T20:43:43+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "require-dev": {"symfony/event-dispatcher": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "psr/log": "~1.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}}, {"version": "v3.1.9", "version_normalized": "*******"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "221a60fb2f369a065eea1ed96b61183219fdfa6e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/221a60fb2f369a065eea1ed96b61183219fdfa6e", "type": "zip", "shasum": "", "reference": "221a60fb2f369a065eea1ed96b61183219fdfa6e"}, "time": "2016-12-08T14:58:14+00:00"}, {"version": "v3.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5be36e1f3ac7ecbe7e34fb641480ad8497b83aa6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5be36e1f3ac7ecbe7e34fb641480ad8497b83aa6", "type": "zip", "shasum": "", "reference": "5be36e1f3ac7ecbe7e34fb641480ad8497b83aa6"}, "time": "2016-11-16T22:17:09+00:00"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c99da1119ae61e15de0e4829196b9fba6f73d065"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c99da1119ae61e15de0e4829196b9fba6f73d065", "type": "zip", "shasum": "", "reference": "c99da1119ae61e15de0e4829196b9fba6f73d065"}, "time": "2016-10-06T01:44:51+00:00"}, {"version": "v3.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6cb0872fb57b38b3b09ff213c21ed693956b9eb0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6cb0872fb57b38b3b09ff213c21ed693956b9eb0", "type": "zip", "shasum": "", "reference": "6cb0872fb57b38b3b09ff213c21ed693956b9eb0"}, "time": "2016-09-28T00:11:12+00:00"}, {"version": "v3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8ea494c34f0f772c3954b5fbe00bffc5a435e563"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8ea494c34f0f772c3954b5fbe00bffc5a435e563", "type": "zip", "shasum": "", "reference": "8ea494c34f0f772c3954b5fbe00bffc5a435e563"}, "time": "2016-08-19T06:48:39+00:00", "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0"}}, {"version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f9e638e8149e9e41b570ff092f8007c477ef0ce5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f9e638e8149e9e41b570ff092f8007c477ef0ce5", "type": "zip", "shasum": "", "reference": "f9e638e8149e9e41b570ff092f8007c477ef0ce5"}, "time": "2016-07-26T08:04:17+00:00"}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "747154aa69b0f83cd02fc9aa554836dee417631a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/747154aa69b0f83cd02fc9aa554836dee417631a", "type": "zip", "shasum": "", "reference": "747154aa69b0f83cd02fc9aa554836dee417631a"}, "time": "2016-06-29T07:02:31+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "64a4d43b045f07055bb197650159769604cb2a92"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/64a4d43b045f07055bb197650159769604cb2a92", "type": "zip", "shasum": "", "reference": "64a4d43b045f07055bb197650159769604cb2a92"}, "time": "2016-06-14T11:18:07+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f62db5b8afec27073a4609b8c84b1f9936652259"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f62db5b8afec27073a4609b8c84b1f9936652259", "type": "zip", "shasum": "", "reference": "f62db5b8afec27073a4609b8c84b1f9936652259"}, "time": "2016-05-30T06:58:39+00:00"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4e3bd48cc686ca82c7345e448b4b96701fb93bcb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4e3bd48cc686ca82c7345e448b4b96701fb93bcb", "type": "zip", "shasum": "", "reference": "4e3bd48cc686ca82c7345e448b4b96701fb93bcb"}, "time": "2016-05-26T08:21:36+00:00"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5759fda2d99302c7a995db7e23d08ff48fa4ae75"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5759fda2d99302c7a995db7e23d08ff48fa4ae75", "type": "zip", "shasum": "", "reference": "5759fda2d99302c7a995db7e23d08ff48fa4ae75"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2016-05-13T18:06:41+00:00"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "926061e74229e935d3c5b4e9ba87237316c6693f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/926061e74229e935d3c5b4e9ba87237316c6693f", "type": "zip", "shasum": "", "reference": "926061e74229e935d3c5b4e9ba87237316c6693f"}, "support": {"source": "https://github.com/symfony/console/tree/3.0"}, "time": "2016-07-30T07:22:48+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a7abb7153f6d1da47f87ec50274844e246b09d9f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a7abb7153f6d1da47f87ec50274844e246b09d9f", "type": "zip", "shasum": "", "reference": "a7abb7153f6d1da47f87ec50274844e246b09d9f"}, "time": "2016-06-29T07:02:21+00:00"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "382fc9ed852edabd6133e34f8549d7a7d99db115"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/382fc9ed852edabd6133e34f8549d7a7d99db115", "type": "zip", "shasum": "", "reference": "382fc9ed852edabd6133e34f8549d7a7d99db115"}, "time": "2016-06-06T15:08:35+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "34a214710e0714b6efcf40ba3cd1e31373a97820"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/34a214710e0714b6efcf40ba3cd1e31373a97820", "type": "zip", "shasum": "", "reference": "34a214710e0714b6efcf40ba3cd1e31373a97820"}, "time": "2016-04-28T09:48:42+00:00"}, {"version": "v3.0.5", "version_normalized": "*******"}, {"version": "v3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6b1175135bc2a74c08a28d89761272de8beed8cd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6b1175135bc2a74c08a28d89761272de8beed8cd", "type": "zip", "shasum": "", "reference": "6b1175135bc2a74c08a28d89761272de8beed8cd"}, "time": "2016-03-16T17:00:50+00:00"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2ed5e2706ce92313d120b8fe50d1063bcfd12e04"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2ed5e2706ce92313d120b8fe50d1063bcfd12e04", "type": "zip", "shasum": "", "reference": "2ed5e2706ce92313d120b8fe50d1063bcfd12e04"}, "time": "2016-02-28T16:24:34+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5a02eaadaa285e2bb727eb6bbdfb8201fcd971b0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5a02eaadaa285e2bb727eb6bbdfb8201fcd971b0", "type": "zip", "shasum": "", "reference": "5a02eaadaa285e2bb727eb6bbdfb8201fcd971b0"}, "time": "2016-02-02T13:44:19+00:00"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ebcdc507829df915f4ca23067bd59ee4ef61f6c3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ebcdc507829df915f4ca23067bd59ee4ef61f6c3", "type": "zip", "shasum": "", "reference": "ebcdc507829df915f4ca23067bd59ee4ef61f6c3"}, "time": "2015-12-22T10:39:06+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "175871ca8d1ef16ff8d8cac395a1c73afa8d0e63"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/175871ca8d1ef16ff8d8cac395a1c73afa8d0e63", "type": "zip", "shasum": "", "reference": "175871ca8d1ef16ff8d8cac395a1c73afa8d0e63"}, "support": {"source": "https://github.com/symfony/console/tree/master"}, "time": "2015-11-30T12:36:17+00:00"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "56cd0ec9b4a7f08a7fd66b57e8f938b1e488d301"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/56cd0ec9b4a7f08a7fd66b57e8f938b1e488d301", "type": "zip", "shasum": "", "reference": "56cd0ec9b4a7f08a7fd66b57e8f938b1e488d301"}, "time": "2015-11-05T14:03:06+00:00"}, {"version": "v2.8.52", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cbcf4b5e233af15cd2bbd50dee1ccc9b7927dc12"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cbcf4b5e233af15cd2bbd50dee1ccc9b7927dc12", "type": "zip", "shasum": "", "reference": "cbcf4b5e233af15cd2bbd50dee1ccc9b7927dc12"}, "support": {"source": "https://github.com/symfony/console/tree/v2.8.52"}, "time": "2018-11-20T15:55:20+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0", "symfony/debug": "^2.7.2|~3.0.0"}, "require-dev": {"symfony/event-dispatcher": "~2.1|~3.0.0", "symfony/process": "~2.1|~3.0.0", "psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}}, {"version": "v2.8.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/v2.8.50"}}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "48ed63767c4add573fb3e9e127d3426db27f78e8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/48ed63767c4add573fb3e9e127d3426db27f78e8", "type": "zip", "shasum": "", "reference": "48ed63767c4add573fb3e9e127d3426db27f78e8"}, "time": "2018-10-30T14:26:34+00:00"}, {"version": "v2.8.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "aca0dcc0c75496e17e2aa0303bb9c8e6d79ed789"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/aca0dcc0c75496e17e2aa0303bb9c8e6d79ed789", "type": "zip", "shasum": "", "reference": "aca0dcc0c75496e17e2aa0303bb9c8e6d79ed789"}, "time": "2018-09-30T03:33:07+00:00"}, {"version": "v2.8.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0c1fcbb9afb5cff992c982ff99c0434f0146dcfc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0c1fcbb9afb5cff992c982ff99c0434f0146dcfc", "type": "zip", "shasum": "", "reference": "0c1fcbb9afb5cff992c982ff99c0434f0146dcfc"}, "time": "2018-07-26T11:13:39+00:00"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "42a0adc7dd656ca2e360285eb6d822df9ce0b160"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/42a0adc7dd656ca2e360285eb6d822df9ce0b160", "type": "zip", "shasum": "", "reference": "42a0adc7dd656ca2e360285eb6d822df9ce0b160"}, "time": "2018-07-09T12:58:09+00:00"}, {"version": "v2.8.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e8e59b74ad1274714dad2748349b55e3e6e630c7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e8e59b74ad1274714dad2748349b55e3e6e630c7", "type": "zip", "shasum": "", "reference": "e8e59b74ad1274714dad2748349b55e3e6e630c7"}, "time": "2018-05-15T21:17:45+00:00"}, {"version": "v2.8.41", "version_normalized": "********"}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "932d1e4f7f33ee37d3534f5f452474daa66283c2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/932d1e4f7f33ee37d3534f5f452474daa66283c2", "type": "zip", "shasum": "", "reference": "932d1e4f7f33ee37d3534f5f452474daa66283c2"}, "time": "2018-04-30T01:21:07+00:00"}, {"version": "v2.8.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7f78892d900c72a40acd1fe793c856ef0c110f26"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7f78892d900c72a40acd1fe793c856ef0c110f26", "type": "zip", "shasum": "", "reference": "7f78892d900c72a40acd1fe793c856ef0c110f26"}, "time": "2018-04-03T05:20:27+00:00", "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}}, {"version": "v2.8.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "390fa4899dbcc47bd41935d87c4572ea4305d3ce"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/390fa4899dbcc47bd41935d87c4572ea4305d3ce", "type": "zip", "shasum": "", "reference": "390fa4899dbcc47bd41935d87c4572ea4305d3ce"}, "time": "2018-03-19T21:13:58+00:00"}, {"version": "v2.8.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a6ff8b2ffa4eb43046828b303af2e3fedadacc27"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a6ff8b2ffa4eb43046828b303af2e3fedadacc27", "type": "zip", "shasum": "", "reference": "a6ff8b2ffa4eb43046828b303af2e3fedadacc27"}, "time": "2018-02-26T15:33:21+00:00"}, {"version": "v2.8.35", "version_normalized": "********"}, {"version": "v2.8.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "162ca7d0ea597599967aa63b23418e747da0896b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/162ca7d0ea597599967aa63b23418e747da0896b", "type": "zip", "shasum": "", "reference": "162ca7d0ea597599967aa63b23418e747da0896b"}, "time": "2018-01-29T08:54:45+00:00"}, {"version": "v2.8.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a4bd0f02ea156cf7b5138774a7ba0ab44d8da4fe"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a4bd0f02ea156cf7b5138774a7ba0ab44d8da4fe", "type": "zip", "shasum": "", "reference": "a4bd0f02ea156cf7b5138774a7ba0ab44d8da4fe"}, "time": "2018-01-03T07:36:31+00:00"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "46270f1ca44f08ebc134ce120fd2c2baf5fd63de"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/46270f1ca44f08ebc134ce120fd2c2baf5fd63de", "type": "zip", "shasum": "", "reference": "46270f1ca44f08ebc134ce120fd2c2baf5fd63de"}, "time": "2017-11-29T09:33:18+00:00"}, {"version": "v2.8.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7cad097cf081c0ab3d0322cc38d34ee80484d86f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7cad097cf081c0ab3d0322cc38d34ee80484d86f", "type": "zip", "shasum": "", "reference": "7cad097cf081c0ab3d0322cc38d34ee80484d86f"}, "time": "2017-11-16T15:20:19+00:00"}, {"version": "v2.8.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3eeaec6a5d9a253925139cd19eda07d882635d87"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3eeaec6a5d9a253925139cd19eda07d882635d87", "type": "zip", "shasum": "", "reference": "3eeaec6a5d9a253925139cd19eda07d882635d87"}, "time": "2017-11-12T16:36:54+00:00"}, {"version": "v2.8.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "89143ce2b463515a75b5f5e9650e6ecfb2684158"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/89143ce2b463515a75b5f5e9650e6ecfb2684158", "type": "zip", "shasum": "", "reference": "89143ce2b463515a75b5f5e9650e6ecfb2684158"}, "time": "2017-11-07T14:08:47+00:00"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f81549d2c5fdee8d711c9ab3c7e7362353ea5853"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f81549d2c5fdee8d711c9ab3c7e7362353ea5853", "type": "zip", "shasum": "", "reference": "f81549d2c5fdee8d711c9ab3c7e7362353ea5853"}, "time": "2017-10-01T21:00:16+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c0807a2ca978e64d8945d373a9221a5c35d1a253"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c0807a2ca978e64d8945d373a9221a5c35d1a253", "type": "zip", "shasum": "", "reference": "c0807a2ca978e64d8945d373a9221a5c35d1a253"}, "time": "2017-08-27T14:29:03+00:00"}, {"version": "v2.8.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "32a3c6b3398de5db8ed381f4ef92970c59c2fcdd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/32a3c6b3398de5db8ed381f4ef92970c59c2fcdd", "type": "zip", "shasum": "", "reference": "32a3c6b3398de5db8ed381f4ef92970c59c2fcdd"}, "time": "2017-07-29T21:26:04+00:00"}, {"version": "v2.8.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "46e65f8d98c9ab629bbfcc16a4ff023f61c37fb2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/46e65f8d98c9ab629bbfcc16a4ff023f61c37fb2", "type": "zip", "shasum": "", "reference": "46e65f8d98c9ab629bbfcc16a4ff023f61c37fb2"}, "time": "2017-07-03T08:04:30+00:00"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3ef6ef64abecd566d551d9e7f6393ac6e93b2462"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3ef6ef64abecd566d551d9e7f6393ac6e93b2462", "type": "zip", "shasum": "", "reference": "3ef6ef64abecd566d551d9e7f6393ac6e93b2462"}, "time": "2017-06-02T14:36:56+00:00"}, {"version": "v2.8.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "efa4d466b67c2fc9bf9419a981e683e1f99fa029"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/efa4d466b67c2fc9bf9419a981e683e1f99fa029", "type": "zip", "shasum": "", "reference": "efa4d466b67c2fc9bf9419a981e683e1f99fa029"}, "time": "2017-05-28T14:07:33+00:00"}, {"version": "v2.8.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2cfcbced8e39e2313ed4da8896fc8c59a56c0d7e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2cfcbced8e39e2313ed4da8896fc8c59a56c0d7e", "type": "zip", "shasum": "", "reference": "2cfcbced8e39e2313ed4da8896fc8c59a56c0d7e"}, "time": "2017-04-26T01:38:53+00:00"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "86407ff20855a5eaa2a7219bd815e9c40a88633e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/86407ff20855a5eaa2a7219bd815e9c40a88633e", "type": "zip", "shasum": "", "reference": "86407ff20855a5eaa2a7219bd815e9c40a88633e"}, "time": "2017-04-03T20:37:06+00:00"}, {"version": "v2.8.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "81508e6fac4476771275a3f4f53c3fee9b956bfa"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/81508e6fac4476771275a3f4f53c3fee9b956bfa", "type": "zip", "shasum": "", "reference": "81508e6fac4476771275a3f4f53c3fee9b956bfa"}, "time": "2017-03-04T11:00:12+00:00"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f3c234cd8db9f7e520a91d695db7d8bb5daeb7a4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f3c234cd8db9f7e520a91d695db7d8bb5daeb7a4", "type": "zip", "shasum": "", "reference": "f3c234cd8db9f7e520a91d695db7d8bb5daeb7a4"}, "time": "2017-02-06T12:04:06+00:00", "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0", "symfony/debug": "~2.7,>=2.7.2|~3.0.0"}}, {"version": "v2.8.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2e18b8903d9c498ba02e1dfa73f64d4894bb6912"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2e18b8903d9c498ba02e1dfa73f64d4894bb6912", "type": "zip", "shasum": "", "reference": "2e18b8903d9c498ba02e1dfa73f64d4894bb6912"}, "time": "2017-01-08T20:43:03+00:00"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d5643cd095e5e37d31e004bb2606b5dd7e96602f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d5643cd095e5e37d31e004bb2606b5dd7e96602f", "type": "zip", "shasum": "", "reference": "d5643cd095e5e37d31e004bb2606b5dd7e96602f"}, "time": "2016-12-06T11:59:35+00:00"}, {"version": "v2.8.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a871ba00e0f604dceac64c56c27f99fbeaf4854e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a871ba00e0f604dceac64c56c27f99fbeaf4854e", "type": "zip", "shasum": "", "reference": "a871ba00e0f604dceac64c56c27f99fbeaf4854e"}, "time": "2016-11-15T23:02:12+00:00"}, {"version": "v2.8.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7350016c8abcab897046f1aead2b766b84d3eff8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7350016c8abcab897046f1aead2b766b84d3eff8", "type": "zip", "shasum": "", "reference": "7350016c8abcab897046f1aead2b766b84d3eff8"}, "time": "2016-10-06T01:43:09+00:00"}, {"version": "v2.8.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d7a5a88178f94dcc29531ea4028ea614e35452d4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d7a5a88178f94dcc29531ea4028ea614e35452d4", "type": "zip", "shasum": "", "reference": "d7a5a88178f94dcc29531ea4028ea614e35452d4"}, "time": "2016-09-28T00:10:16+00:00"}, {"version": "v2.8.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3d3e4fa5f0614c8e45220e5de80332322e33bd90"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3d3e4fa5f0614c8e45220e5de80332322e33bd90", "type": "zip", "shasum": "", "reference": "3d3e4fa5f0614c8e45220e5de80332322e33bd90"}, "time": "2016-09-06T10:55:00+00:00", "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0"}}, {"version": "v2.8.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bac1eb5869c1848a173852aee61df960e3a49197"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bac1eb5869c1848a173852aee61df960e3a49197", "type": "zip", "shasum": "", "reference": "bac1eb5869c1848a173852aee61df960e3a49197"}, "time": "2016-08-19T06:48:01+00:00"}, {"version": "v2.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "36e62335caca8a6e909c5c5bac4a8128149911c9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/36e62335caca8a6e909c5c5bac4a8128149911c9", "type": "zip", "shasum": "", "reference": "36e62335caca8a6e909c5c5bac4a8128149911c9"}, "time": "2016-07-30T07:20:35+00:00"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c392a6ec72f2122748032c2ad6870420561ffcfa"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c392a6ec72f2122748032c2ad6870420561ffcfa", "type": "zip", "shasum": "", "reference": "c392a6ec72f2122748032c2ad6870420561ffcfa"}, "time": "2016-06-29T07:02:14+00:00"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5ac8bc9aa77bb2edf06af3a1bb6bc1020d23acd3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5ac8bc9aa77bb2edf06af3a1bb6bc1020d23acd3", "type": "zip", "shasum": "", "reference": "5ac8bc9aa77bb2edf06af3a1bb6bc1020d23acd3"}, "time": "2016-06-06T15:06:25+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "48221d3de4dc22d2cd57c97e8b9361821da86609"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/48221d3de4dc22d2cd57c97e8b9361821da86609", "type": "zip", "shasum": "", "reference": "48221d3de4dc22d2cd57c97e8b9361821da86609"}, "time": "2016-04-26T12:00:47+00:00"}, {"version": "v2.8.5", "version_normalized": "*******"}, {"version": "v2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9a5aef5fc0d4eff86853d44202b02be8d5a20154"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9a5aef5fc0d4eff86853d44202b02be8d5a20154", "type": "zip", "shasum": "", "reference": "9a5aef5fc0d4eff86853d44202b02be8d5a20154"}, "time": "2016-03-17T09:19:04+00:00"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "56cc5caf051189720b8de974e4746090aaa10d44"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/56cc5caf051189720b8de974e4746090aaa10d44", "type": "zip", "shasum": "", "reference": "56cc5caf051189720b8de974e4746090aaa10d44"}, "time": "2016-02-28T16:20:50+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d0239fb42f98dd02e7d342f793c5d2cdee0c478d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d0239fb42f98dd02e7d342f793c5d2cdee0c478d", "type": "zip", "shasum": "", "reference": "d0239fb42f98dd02e7d342f793c5d2cdee0c478d"}, "time": "2016-01-14T08:33:16+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2e06a5ccb19dcf9b89f1c6a677a39a8df773635a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2e06a5ccb19dcf9b89f1c6a677a39a8df773635a", "type": "zip", "shasum": "", "reference": "2e06a5ccb19dcf9b89f1c6a677a39a8df773635a"}, "time": "2015-12-22T10:25:57+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d232bfc100dfd32b18ccbcab4bcc8f28697b7e41"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d232bfc100dfd32b18ccbcab4bcc8f28697b7e41", "type": "zip", "shasum": "", "reference": "d232bfc100dfd32b18ccbcab4bcc8f28697b7e41"}, "time": "2015-11-30T12:35:10+00:00"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "74bf9202a531cc6942454eb9e2dd1e13bde34a31"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/74bf9202a531cc6942454eb9e2dd1e13bde34a31", "type": "zip", "shasum": "", "reference": "74bf9202a531cc6942454eb9e2dd1e13bde34a31"}, "time": "2015-11-04T00:47:19+00:00"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "574cb4cfaa01ba115fc2fc0c2355b2c5472a4804"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/574cb4cfaa01ba115fc2fc0c2355b2c5472a4804", "type": "zip", "shasum": "", "reference": "574cb4cfaa01ba115fc2fc0c2355b2c5472a4804"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.51"}, "time": "2018-05-13T15:44:36+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require": {"php": ">=5.3.9", "symfony/debug": "^2.7.2"}, "require-dev": {"symfony/event-dispatcher": "~2.1", "symfony/process": "~2.1", "psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/console/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "********"}, {"version": "v2.7.47", "version_normalized": "********"}, {"version": "v2.7.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "27874602b1a3089cb99c427cc24fa99a10b27a58"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/27874602b1a3089cb99c427cc24fa99a10b27a58", "type": "zip", "shasum": "", "reference": "27874602b1a3089cb99c427cc24fa99a10b27a58"}, "time": "2018-04-22T05:55:13+00:00", "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}}, {"version": "v2.7.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "505b03813476d0779f3407c33c55a70bf82362c1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/505b03813476d0779f3407c33c55a70bf82362c1", "type": "zip", "shasum": "", "reference": "505b03813476d0779f3407c33c55a70bf82362c1"}, "time": "2018-03-20T14:50:33+00:00"}, {"version": "v2.7.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "926047a257917a5520689fcf592ced19c22ade5c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/926047a257917a5520689fcf592ced19c22ade5c", "type": "zip", "shasum": "", "reference": "926047a257917a5520689fcf592ced19c22ade5c"}, "time": "2018-03-19T20:46:57+00:00"}, {"version": "v2.7.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d98c21092678cfce0021746a17f89af5515f6737"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d98c21092678cfce0021746a17f89af5515f6737", "type": "zip", "shasum": "", "reference": "d98c21092678cfce0021746a17f89af5515f6737"}, "time": "2018-02-24T16:59:02+00:00"}, {"version": "v2.7.42", "version_normalized": "********"}, {"version": "v2.7.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a39062a5a1e98a1f1e392e4253a6b3b0ebdece18"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a39062a5a1e98a1f1e392e4253a6b3b0ebdece18", "type": "zip", "shasum": "", "reference": "a39062a5a1e98a1f1e392e4253a6b3b0ebdece18"}, "time": "2018-01-26T15:25:25+00:00"}, {"version": "v2.7.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "26fa9a4d5944c0080991414d64cdd7fc75058878"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/26fa9a4d5944c0080991414d64cdd7fc75058878", "type": "zip", "shasum": "", "reference": "26fa9a4d5944c0080991414d64cdd7fc75058878"}, "time": "2018-01-03T07:23:28+00:00"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a54fb931b2fd0a9ed4041cb5fcb1b86208e52682"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a54fb931b2fd0a9ed4041cb5fcb1b86208e52682", "type": "zip", "shasum": "", "reference": "a54fb931b2fd0a9ed4041cb5fcb1b86208e52682"}, "time": "2017-11-18T13:50:49+00:00"}, {"version": "v2.7.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c9a214bc07f1d80c87681f4d7fb7aa97938f78d4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c9a214bc07f1d80c87681f4d7fb7aa97938f78d4", "type": "zip", "shasum": "", "reference": "c9a214bc07f1d80c87681f4d7fb7aa97938f78d4"}, "time": "2017-11-15T09:26:39+00:00"}, {"version": "v2.7.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f1983b12e0560ad419ccc9ca344c2f245da1a1f6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f1983b12e0560ad419ccc9ca344c2f245da1a1f6", "type": "zip", "shasum": "", "reference": "f1983b12e0560ad419ccc9ca344c2f245da1a1f6"}, "time": "2017-11-11T22:09:14+00:00"}, {"version": "v2.7.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e9f298b1b186e24ce06117049b35f3e5bf2880b5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e9f298b1b186e24ce06117049b35f3e5bf2880b5", "type": "zip", "shasum": "", "reference": "e9f298b1b186e24ce06117049b35f3e5bf2880b5"}, "time": "2017-11-07T14:04:08+00:00"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "fd09d8c347a5da4738f7068dc64690e96b6cb310"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/fd09d8c347a5da4738f7068dc64690e96b6cb310", "type": "zip", "shasum": "", "reference": "fd09d8c347a5da4738f7068dc64690e96b6cb310"}, "time": "2017-09-30T14:00:25+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a288d36702dc27144b5e1a3a25dd1c776737c939"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a288d36702dc27144b5e1a3a25dd1c776737c939", "type": "zip", "shasum": "", "reference": "a288d36702dc27144b5e1a3a25dd1c776737c939"}, "time": "2017-08-17T08:44:22+00:00"}, {"version": "v2.7.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a7844e72ac496096420e20d45970610b36fadde9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a7844e72ac496096420e20d45970610b36fadde9", "type": "zip", "shasum": "", "reference": "a7844e72ac496096420e20d45970610b36fadde9"}, "time": "2017-07-29T07:58:31+00:00"}, {"version": "v2.7.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9227d7607b1b42b55bc46610afe3d516b5e89fa2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9227d7607b1b42b55bc46610afe3d516b5e89fa2", "type": "zip", "shasum": "", "reference": "9227d7607b1b42b55bc46610afe3d516b5e89fa2"}, "time": "2017-07-01T09:26:27+00:00"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bba0fc4b140be58b80b9ea0bf125741c6ae9ce0e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bba0fc4b140be58b80b9ea0bf125741c6ae9ce0e", "type": "zip", "shasum": "", "reference": "bba0fc4b140be58b80b9ea0bf125741c6ae9ce0e"}, "time": "2017-06-02T14:34:38+00:00"}, {"version": "v2.7.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "16768b82ed9b261aebdc816d6938561c742a4971"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/16768b82ed9b261aebdc816d6938561c742a4971", "type": "zip", "shasum": "", "reference": "16768b82ed9b261aebdc816d6938561c742a4971"}, "time": "2017-05-28T13:43:15+00:00"}, {"version": "v2.7.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "64f199c318543c0cc958766604aabcc117bbbc57"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/64f199c318543c0cc958766604aabcc117bbbc57", "type": "zip", "shasum": "", "reference": "64f199c318543c0cc958766604aabcc117bbbc57"}, "time": "2017-04-25T14:03:21+00:00"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "de8394eea9399d4169af7f2b532e6f5ddcc68cb8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/de8394eea9399d4169af7f2b532e6f5ddcc68cb8", "type": "zip", "shasum": "", "reference": "de8394eea9399d4169af7f2b532e6f5ddcc68cb8"}, "time": "2017-03-31T14:33:09+00:00"}, {"version": "v2.7.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "17a39e7f9126f87fc228e03edcb5d6b72e1bcbc6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/17a39e7f9126f87fc228e03edcb5d6b72e1bcbc6", "type": "zip", "shasum": "", "reference": "17a39e7f9126f87fc228e03edcb5d6b72e1bcbc6"}, "time": "2017-02-28T18:03:12+00:00"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1ec4c92149e1305481b19b8f31d409fc77a5403c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1ec4c92149e1305481b19b8f31d409fc77a5403c", "type": "zip", "shasum": "", "reference": "1ec4c92149e1305481b19b8f31d409fc77a5403c"}, "time": "2017-02-03T21:25:39+00:00", "require": {"php": ">=5.3.9", "symfony/debug": "~2.7,>=2.7.2"}}, {"version": "v2.7.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f54ecfe99a2655bfaa47149307bd619e6536409a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f54ecfe99a2655bfaa47149307bd619e6536409a", "type": "zip", "shasum": "", "reference": "f54ecfe99a2655bfaa47149307bd619e6536409a"}, "time": "2017-01-06T13:13:12+00:00"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "904569a605f7d4afe584bdf97cb4588f59d88711"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/904569a605f7d4afe584bdf97cb4588f59d88711", "type": "zip", "shasum": "", "reference": "904569a605f7d4afe584bdf97cb4588f59d88711"}, "time": "2016-12-05T13:00:57+00:00"}, {"version": "v2.7.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "54fb85a818596bc3b33618125fa3ee99b9f45382"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/54fb85a818596bc3b33618125fa3ee99b9f45382", "type": "zip", "shasum": "", "reference": "54fb85a818596bc3b33618125fa3ee99b9f45382"}, "time": "2016-11-13T17:41:36+00:00"}, {"version": "v2.7.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "47766c4c81e6d7790a9e4f029747f9a00da5f64d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/47766c4c81e6d7790a9e4f029747f9a00da5f64d", "type": "zip", "shasum": "", "reference": "47766c4c81e6d7790a9e4f029747f9a00da5f64d"}, "time": "2016-10-05T17:26:56+00:00"}, {"version": "v2.7.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "91a8fb9f91aeb00caaeab95818063e3a96bff9e2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/91a8fb9f91aeb00caaeab95818063e3a96bff9e2", "type": "zip", "shasum": "", "reference": "91a8fb9f91aeb00caaeab95818063e3a96bff9e2"}, "time": "2016-09-27T17:33:51+00:00"}, {"version": "v2.7.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cc3d188345b84d27a931db0969b0ff36272f451c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cc3d188345b84d27a931db0969b0ff36272f451c", "type": "zip", "shasum": "", "reference": "cc3d188345b84d27a931db0969b0ff36272f451c"}, "time": "2016-09-06T07:26:07+00:00", "require": {"php": ">=5.3.9"}}, {"version": "v2.7.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "86cbcb081ade8b9bd8617775b6984c5981192840"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/86cbcb081ade8b9bd8617775b6984c5981192840", "type": "zip", "shasum": "", "reference": "86cbcb081ade8b9bd8617775b6984c5981192840"}, "time": "2016-08-19T06:41:18+00:00"}, {"version": "v2.7.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "61a73eabb2cf16681eda784b8755c587a869ec42"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/61a73eabb2cf16681eda784b8755c587a869ec42", "type": "zip", "shasum": "", "reference": "61a73eabb2cf16681eda784b8755c587a869ec42"}, "time": "2016-07-30T07:17:26+00:00"}, {"version": "v2.7.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2f9adaf91c7d70c9d028c9aace7cc051f9cd56b3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2f9adaf91c7d70c9d028c9aace7cc051f9cd56b3", "type": "zip", "shasum": "", "reference": "2f9adaf91c7d70c9d028c9aace7cc051f9cd56b3"}, "time": "2016-06-29T05:44:56+00:00"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1db9bfd331383f3ba0b3c2f54da418df68c867c4"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1db9bfd331383f3ba0b3c2f54da418df68c867c4", "type": "zip", "shasum": "", "reference": "1db9bfd331383f3ba0b3c2f54da418df68c867c4"}, "time": "2016-06-06T14:41:06+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "dfb9d26a4dd62fc9389c42196cf4a087400948b8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/dfb9d26a4dd62fc9389c42196cf4a087400948b8", "type": "zip", "shasum": "", "reference": "dfb9d26a4dd62fc9389c42196cf4a087400948b8"}, "time": "2016-04-20T06:32:07+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6100a40569b2be1b6aba1cc6a92fe6cb71024fc8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6100a40569b2be1b6aba1cc6a92fe6cb71024fc8", "type": "zip", "shasum": "", "reference": "6100a40569b2be1b6aba1cc6a92fe6cb71024fc8"}, "time": "2016-03-09T16:30:49+00:00"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ee91ec301cd88ee38ab14505025fe94bbc19a9c1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ee91ec301cd88ee38ab14505025fe94bbc19a9c1", "type": "zip", "shasum": "", "reference": "ee91ec301cd88ee38ab14505025fe94bbc19a9c1"}, "time": "2016-02-28T16:19:47+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d3fc138b6ed8f8074591821d3416d8f9c04d6ca6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d3fc138b6ed8f8074591821d3416d8f9c04d6ca6", "type": "zip", "shasum": "", "reference": "d3fc138b6ed8f8074591821d3416d8f9c04d6ca6"}, "time": "2016-01-14T08:26:43+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4e35a78f932a4c07bd349efea647ac741c1419b6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4e35a78f932a4c07bd349efea647ac741c1419b6", "type": "zip", "shasum": "", "reference": "4e35a78f932a4c07bd349efea647ac741c1419b6"}, "time": "2015-12-23T11:17:38+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "16bb1cb86df43c90931df65f529e7ebd79636750"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/16bb1cb86df43c90931df65f529e7ebd79636750", "type": "zip", "shasum": "", "reference": "16bb1cb86df43c90931df65f529e7ebd79636750"}, "time": "2015-11-18T09:54:26+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5efd632294c8320ea52492db22292ff853a43766"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5efd632294c8320ea52492db22292ff853a43766", "type": "zip", "shasum": "", "reference": "5efd632294c8320ea52492db22292ff853a43766"}, "time": "2015-10-20T14:38:46+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "06cb17c013a82f94a3d840682b49425cd00a2161"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/06cb17c013a82f94a3d840682b49425cd00a2161", "type": "zip", "shasum": "", "reference": "06cb17c013a82f94a3d840682b49425cd00a2161"}, "time": "2015-09-25T08:32:23+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/event-dispatcher": "~2.1", "symfony/process": "~2.1", "psr/log": "~1.0"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "9ff9032151186bd66ecee727d728f1319f52d1d8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/9ff9032151186bd66ecee727d728f1319f52d1d8", "type": "zip", "shasum": "", "reference": "9ff9032151186bd66ecee727d728f1319f52d1d8"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.4"}, "time": "2015-09-03T11:40:38+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d6cf02fe73634c96677e428f840704bfbcaec29e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d6cf02fe73634c96677e428f840704bfbcaec29e", "type": "zip", "shasum": "", "reference": "d6cf02fe73634c96677e428f840704bfbcaec29e"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.3"}, "time": "2015-07-28T15:18:12+00:00"}, {"version": "v2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "8cf484449130cabfd98dcb4694ca9945802a21ed"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/8cf484449130cabfd98dcb4694ca9945802a21ed", "type": "zip", "shasum": "", "reference": "8cf484449130cabfd98dcb4694ca9945802a21ed"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.2"}, "time": "2015-07-09T16:07:40+00:00"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "564398bc1f33faf92fc2ec86859983d30eb81806"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/564398bc1f33faf92fc2ec86859983d30eb81806", "type": "zip", "shasum": "", "reference": "564398bc1f33faf92fc2ec86859983d30eb81806"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.1"}, "time": "2015-06-10T15:30:22+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7f0bec04961c61c961df0cb8c2ae88dbfd83f399"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7f0bec04961c61c961df0cb8c2ae88dbfd83f399", "type": "zip", "shasum": "", "reference": "7f0bec04961c61c961df0cb8c2ae88dbfd83f399"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.0"}, "time": "2015-05-29T16:22:24+00:00"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "555ba7fc54a538e1b83988e2299db09b6b6dca64"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/555ba7fc54a538e1b83988e2299db09b6b6dca64", "type": "zip", "shasum": "", "reference": "555ba7fc54a538e1b83988e2299db09b6b6dca64"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.0-BETA2"}, "time": "2015-05-02T15:21:08+00:00"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7a8b8e2b82827622ef60127547bc3c7ce2e18982"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7a8b8e2b82827622ef60127547bc3c7ce2e18982", "type": "zip", "shasum": "", "reference": "7a8b8e2b82827622ef60127547bc3c7ce2e18982"}, "support": {"source": "https://github.com/symfony/console/tree/v2.7.0-BETA1"}, "time": "2015-04-10T15:31:32+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Console\\": ""}}, "target-dir": "Symfony/Component/Console"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "0e5e18ae09d3f5c06367759be940e9ed3f568359"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/0e5e18ae09d3f5c06367759be940e9ed3f568359", "type": "zip", "shasum": "", "reference": "0e5e18ae09d3f5c06367759be940e9ed3f568359"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.11"}, "time": "2015-07-26T09:08:40+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********"}, {"version": "v2.6.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "042107bc202845086739414339e551fbe81293d7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/042107bc202845086739414339e551fbe81293d7", "type": "zip", "shasum": "", "reference": "042107bc202845086739414339e551fbe81293d7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.10"}, "time": "2015-07-13T09:09:47+00:00"}, {"version": "v2.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b5ec0c11a204718f2b656357f5505a8e578f30dd"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b5ec0c11a204718f2b656357f5505a8e578f30dd", "type": "zip", "shasum": "", "reference": "b5ec0c11a204718f2b656357f5505a8e578f30dd"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.9"}, "time": "2015-05-29T14:42:58+00:00"}, {"version": "v2.6.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2343f6d8026306bd330e0c987e4c102483c213e7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2343f6d8026306bd330e0c987e4c102483c213e7", "type": "zip", "shasum": "", "reference": "2343f6d8026306bd330e0c987e4c102483c213e7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.8"}, "time": "2015-05-22T14:53:08+00:00"}, {"version": "v2.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ebc5679854aa24ed7d65062e9e3ab0b18a917272"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ebc5679854aa24ed7d65062e9e3ab0b18a917272", "type": "zip", "shasum": "", "reference": "ebc5679854aa24ed7d65062e9e3ab0b18a917272"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.7"}, "time": "2015-05-02T15:18:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5b91dc4ed5eb08553f57f6df04c4730a73992667"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5b91dc4ed5eb08553f57f6df04c4730a73992667", "type": "zip", "shasum": "", "reference": "5b91dc4ed5eb08553f57f6df04c4730a73992667"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.6"}, "time": "2015-03-30T15:54:10+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "53f86497ccd01677e22435cfb7262599450a90d1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/53f86497ccd01677e22435cfb7262599450a90d1", "type": "zip", "shasum": "", "reference": "53f86497ccd01677e22435cfb7262599450a90d1"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.5"}, "time": "2015-03-13T17:37:22+00:00"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e44154bfe3e41e8267d7a3794cd9da9a51cfac34"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e44154bfe3e41e8267d7a3794cd9da9a51cfac34", "type": "zip", "shasum": "", "reference": "e44154bfe3e41e8267d7a3794cd9da9a51cfac34"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.4"}, "time": "2015-01-25T04:39:26+00:00", "require-dev": {"symfony/event-dispatcher": "~2.1", "symfony/process": "~2.1", "psr/log": "~1.0"}}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6ac6491ff60c0e5a941db3ccdc75a07adbb61476"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6ac6491ff60c0e5a941db3ccdc75a07adbb61476", "type": "zip", "shasum": "", "reference": "6ac6491ff60c0e5a941db3ccdc75a07adbb61476"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.3"}, "time": "2015-01-06T17:50:02+00:00"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ef825fd9f809d275926547c9e57cbf14968793e8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ef825fd9f809d275926547c9e57cbf14968793e8", "type": "zip", "shasum": "", "reference": "ef825fd9f809d275926547c9e57cbf14968793e8"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.1"}, "time": "2014-12-02T20:19:20+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d3bac228fd7a2aac9193e241b239880b3ba39a10"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d3bac228fd7a2aac9193e241b239880b3ba39a10", "type": "zip", "shasum": "", "reference": "d3bac228fd7a2aac9193e241b239880b3ba39a10"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.0-BETA2"}, "time": "2014-11-20T13:24:23+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2fc3644d2e10e14935b2c79e57a5185281eb1eb7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2fc3644d2e10e14935b2c79e57a5185281eb1eb7", "type": "zip", "shasum": "", "reference": "2fc3644d2e10e14935b2c79e57a5185281eb1eb7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.6.0-BETA1"}, "time": "2014-11-03T03:55:50+00:00"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a43e750b4c74f3bdfca77c79c343033d35a6cd6e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a43e750b4c74f3bdfca77c79c343033d35a6cd6e", "type": "zip", "shasum": "", "reference": "a43e750b4c74f3bdfca77c79c343033d35a6cd6e"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.12"}, "time": "2015-02-08T07:07:45+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "require-dev": {"symfony/event-dispatcher": "~2.1", "psr/log": "~1.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": ""}}, {"version": "v2.5.11", "version_normalized": "********"}, {"version": "v2.5.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f41807e67f00244f013bb96a66ac02f778a31209"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f41807e67f00244f013bb96a66ac02f778a31209", "type": "zip", "shasum": "", "reference": "f41807e67f00244f013bb96a66ac02f778a31209"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.10"}, "time": "2015-01-25T04:37:39+00:00"}, {"version": "v2.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "754f4b6de7b4a1d442f9b6a728bfb7adef54592c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/754f4b6de7b4a1d442f9b6a728bfb7adef54592c", "type": "zip", "shasum": "", "reference": "754f4b6de7b4a1d442f9b6a728bfb7adef54592c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.9"}, "time": "2015-01-06T17:40:45+00:00"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "806e29280b4b9700693b32d055ce1b1a491dec39"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/806e29280b4b9700693b32d055ce1b1a491dec39", "type": "zip", "shasum": "", "reference": "806e29280b4b9700693b32d055ce1b1a491dec39"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.8"}, "time": "2014-12-02T20:15:53+00:00"}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "61b13c27c9258e97009249d4ef193c964bf346b7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/61b13c27c9258e97009249d4ef193c964bf346b7", "type": "zip", "shasum": "", "reference": "61b13c27c9258e97009249d4ef193c964bf346b7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.7"}, "time": "2014-11-20T13:22:25+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6f177fca24200a5b97aef5ce7a5c98124a0f0db0"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6f177fca24200a5b97aef5ce7a5c98124a0f0db0", "type": "zip", "shasum": "", "reference": "6f177fca24200a5b97aef5ce7a5c98124a0f0db0"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.6"}, "time": "2014-10-05T13:57:04+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ca053eaa031c93afb68a71e4eb1f4168dfd4a661"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ca053eaa031c93afb68a71e4eb1f4168dfd4a661", "type": "zip", "shasum": "", "reference": "ca053eaa031c93afb68a71e4eb1f4168dfd4a661"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.5"}, "time": "2014-09-25T09:53:56+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "748beed2a1e73179c3f5154d33fe6ae100c1aeb1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/748beed2a1e73179c3f5154d33fe6ae100c1aeb1", "type": "zip", "shasum": "", "reference": "748beed2a1e73179c3f5154d33fe6ae100c1aeb1"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.4"}, "time": "2014-08-14T16:10:54+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cd2d1e4bac2206b337326b0140ff475fe9ad5f63"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cd2d1e4bac2206b337326b0140ff475fe9ad5f63", "type": "zip", "shasum": "", "reference": "cd2d1e4bac2206b337326b0140ff475fe9ad5f63"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.3"}, "time": "2014-08-05T09:00:40+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "386fa63407805959bd2c5fe540294721ad4224c8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/386fa63407805959bd2c5fe540294721ad4224c8", "type": "zip", "shasum": "", "reference": "386fa63407805959bd2c5fe540294721ad4224c8"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.2"}, "time": "2014-07-15T14:15:12+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2e3df33dd72a9cdef7e9745d930e29ff844fe055"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2e3df33dd72a9cdef7e9745d930e29ff844fe055", "type": "zip", "shasum": "", "reference": "2e3df33dd72a9cdef7e9745d930e29ff844fe055"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.1"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ef4ca73b0b3a10cbac653d3ca482d0cdd4502b2c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ef4ca73b0b3a10cbac653d3ca482d0cdd4502b2c", "type": "zip", "shasum": "", "reference": "ef4ca73b0b3a10cbac653d3ca482d0cdd4502b2c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.0-RC1"}, "time": "2014-05-22T08:54:24+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d12686cea4e1155ed93449c0bca9db223f31bb2d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d12686cea4e1155ed93449c0bca9db223f31bb2d", "type": "zip", "shasum": "", "reference": "d12686cea4e1155ed93449c0bca9db223f31bb2d"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.0-BETA2"}, "time": "2014-04-28T13:26:08+00:00"}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "abfdf388c2f47c4d4ba721afab7e91f15ffa476f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/abfdf388c2f47c4d4ba721afab7e91f15ffa476f", "type": "zip", "shasum": "", "reference": "abfdf388c2f47c4d4ba721afab7e91f15ffa476f"}, "support": {"source": "https://github.com/symfony/console/tree/v2.5.0-BETA1"}, "time": "2014-04-02T16:54:39+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "39dd1c85ffdfc3c5fb008019dd027af05f64ec36"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/39dd1c85ffdfc3c5fb008019dd027af05f64ec36", "type": "zip", "shasum": "", "reference": "39dd1c85ffdfc3c5fb008019dd027af05f64ec36"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.10"}, "time": "2014-09-22T08:51:05+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "require-dev": {"symfony/event-dispatcher": "~2.1"}, "suggest": {"symfony/event-dispatcher": ""}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6cdbe19f8f9025a84ccc45c3870e388e424bea5c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6cdbe19f8f9025a84ccc45c3870e388e424bea5c", "type": "zip", "shasum": "", "reference": "6cdbe19f8f9025a84ccc45c3870e388e424bea5c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.9"}, "time": "2014-08-14T14:51:32+00:00"}, {"version": "v2.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "29ef7af8aa6e3c015445f34291ccab9b8019085b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/29ef7af8aa6e3c015445f34291ccab9b8019085b", "type": "zip", "shasum": "", "reference": "29ef7af8aa6e3c015445f34291ccab9b8019085b"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.8"}, "time": "2014-07-09T12:44:38+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a14ed929c880e1c9d7bbaa5f77cdc07da3cc04a6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a14ed929c880e1c9d7bbaa5f77cdc07da3cc04a6", "type": "zip", "shasum": "", "reference": "a14ed929c880e1c9d7bbaa5f77cdc07da3cc04a6"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.7"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "24f723436e62598c9dddee2a8573d6992504dc5d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/24f723436e62598c9dddee2a8573d6992504dc5d", "type": "zip", "shasum": "", "reference": "24f723436e62598c9dddee2a8573d6992504dc5d"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.6"}, "time": "2014-05-14T21:48:29+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2e452005b1e1d003d23702d227e23614679eb5ca"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2e452005b1e1d003d23702d227e23614679eb5ca", "type": "zip", "shasum": "", "reference": "2e452005b1e1d003d23702d227e23614679eb5ca"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.4"}, "time": "2014-04-27T13:34:57+00:00"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ef20f1f58d7f693ee888353962bd2db336e3bbcb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ef20f1f58d7f693ee888353962bd2db336e3bbcb", "type": "zip", "shasum": "", "reference": "ef20f1f58d7f693ee888353962bd2db336e3bbcb"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.3"}, "time": "2014-03-01T17:35:04+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "940f217cbc3c8a33e5403e7c595495c4884400fe"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/940f217cbc3c8a33e5403e7c595495c4884400fe", "type": "zip", "shasum": "", "reference": "940f217cbc3c8a33e5403e7c595495c4884400fe"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.2"}, "time": "2014-02-11T13:52:09+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4c1ed2ff514bd85ee186eebb010ccbdeeab05af7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4c1ed2ff514bd85ee186eebb010ccbdeeab05af7", "type": "zip", "shasum": "", "reference": "4c1ed2ff514bd85ee186eebb010ccbdeeab05af7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.1"}, "time": "2014-01-01T08:14:50+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3c1496ae96d24ccc6c340fcc25f71d7a1ab4c12c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3c1496ae96d24ccc6c340fcc25f71d7a1ab4c12c", "type": "zip", "shasum": "", "reference": "3c1496ae96d24ccc6c340fcc25f71d7a1ab4c12c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.0"}, "time": "2013-11-27T09:10:40+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f694dcda2204ee14a047ef60bfd9faf242adbc1a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f694dcda2204ee14a047ef60bfd9faf242adbc1a", "type": "zip", "shasum": "", "reference": "f694dcda2204ee14a047ef60bfd9faf242adbc1a"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.0-RC1"}, "time": "2013-11-22T17:42:00+00:00"}, {"version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "eca72b22138f66f4244a3eddc18c7261cfcd130c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/eca72b22138f66f4244a3eddc18c7261cfcd130c", "type": "zip", "shasum": "", "reference": "eca72b22138f66f4244a3eddc18c7261cfcd130c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.0-BETA2"}, "time": "2013-10-30T08:33:58+00:00"}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ed9b8a905b21e3d4bab8b6e0e016a076aefcc5bc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ed9b8a905b21e3d4bab8b6e0e016a076aefcc5bc", "type": "zip", "shasum": "", "reference": "ed9b8a905b21e3d4bab8b6e0e016a076aefcc5bc"}, "support": {"source": "https://github.com/symfony/console/tree/v2.4.0-BETA1"}, "time": "2013-10-02T07:34:37+00:00"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "20c12c6d6c5a087a66d4e77999451713a92a3507"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/20c12c6d6c5a087a66d4e77999451713a92a3507", "type": "zip", "shasum": "", "reference": "20c12c6d6c5a087a66d4e77999451713a92a3507"}, "support": {"source": "https://github.com/symfony/console/tree/2.3"}, "time": "2016-05-26T08:04:58+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.3.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1220da2143dce4c785c0eaffe14a92470fca202e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1220da2143dce4c785c0eaffe14a92470fca202e", "type": "zip", "shasum": "", "reference": "1220da2143dce4c785c0eaffe14a92470fca202e"}, "time": "2016-04-04T17:01:16+00:00"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cc386594e19024e53e81ee7b6d6c37c221864c4e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cc386594e19024e53e81ee7b6d6c37c221864c4e", "type": "zip", "shasum": "", "reference": "cc386594e19024e53e81ee7b6d6c37c221864c4e"}, "time": "2016-03-04T09:20:54+00:00"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "020e15e710bc52508bc3bb7ad1566d362ca86d85"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/020e15e710bc52508bc3bb7ad1566d362ca86d85", "type": "zip", "shasum": "", "reference": "020e15e710bc52508bc3bb7ad1566d362ca86d85"}, "time": "2016-02-24T14:09:34+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "60d32da452c7067840332097199572fc151913ea"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/60d32da452c7067840332097199572fc151913ea", "type": "zip", "shasum": "", "reference": "60d32da452c7067840332097199572fc151913ea"}, "time": "2016-01-14T07:51:59+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ee4f872f08ef4f859a2bbd5c1c4526ce2686d2a1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ee4f872f08ef4f859a2bbd5c1c4526ce2686d2a1", "type": "zip", "shasum": "", "reference": "ee4f872f08ef4f859a2bbd5c1c4526ce2686d2a1"}, "time": "2015-12-16T23:47:15+00:00"}, {"version": "v2.3.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ed9c6cad324afb02672fa8ebf55fe0feb1659067"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ed9c6cad324afb02672fa8ebf55fe0feb1659067", "type": "zip", "shasum": "", "reference": "ed9c6cad324afb02672fa8ebf55fe0feb1659067"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7c334a289dc3fade68725a2e06a7bdafa66cade8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7c334a289dc3fade68725a2e06a7bdafa66cade8", "type": "zip", "shasum": "", "reference": "7c334a289dc3fade68725a2e06a7bdafa66cade8"}, "time": "2015-10-12T11:59:00+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Console\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "379468cf4a5a955a02cb53ef79b5ce56b3a603d7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/379468cf4a5a955a02cb53ef79b5ce56b3a603d7", "type": "zip", "shasum": "", "reference": "379468cf4a5a955a02cb53ef79b5ce56b3a603d7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.33"}, "time": "2015-09-20T21:33:09+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/event-dispatcher": "~2.1"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "cb9006df9d50f1dd7b9dfa3e4ad8942acde9f76c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/cb9006df9d50f1dd7b9dfa3e4ad8942acde9f76c", "type": "zip", "shasum": "", "reference": "cb9006df9d50f1dd7b9dfa3e4ad8942acde9f76c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.32"}, "time": "2015-08-31T12:48:21+00:00"}, {"version": "v2.3.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c685d21f478b62315248a671f07d28973e456009"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c685d21f478b62315248a671f07d28973e456009", "type": "zip", "shasum": "", "reference": "c685d21f478b62315248a671f07d28973e456009"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.31"}, "time": "2015-07-13T09:08:56+00:00"}, {"version": "v2.3.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4ce3d2a448af346068e799a2182c3010ff2ed651"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4ce3d2a448af346068e799a2182c3010ff2ed651", "type": "zip", "shasum": "", "reference": "4ce3d2a448af346068e799a2182c3010ff2ed651"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.30"}, "time": "2015-05-24T18:51:45+00:00"}, {"version": "v2.3.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a36522dba03556e22ddb2ccf840cd8e03322ed5e"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a36522dba03556e22ddb2ccf840cd8e03322ed5e", "type": "zip", "shasum": "", "reference": "a36522dba03556e22ddb2ccf840cd8e03322ed5e"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.29"}, "time": "2015-05-20T09:08:20+00:00"}, {"version": "v2.3.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d446270de14e324e3b52ce82c83a7e33c480c808"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d446270de14e324e3b52ce82c83a7e33c480c808", "type": "zip", "shasum": "", "reference": "d446270de14e324e3b52ce82c83a7e33c480c808"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.28"}, "time": "2015-05-01T14:06:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6081c3ca0284655996fa3d9f91063b8f0b669f3b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6081c3ca0284655996fa3d9f91063b8f0b669f3b", "type": "zip", "shasum": "", "reference": "6081c3ca0284655996fa3d9f91063b8f0b669f3b"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.27"}, "time": "2015-03-27T22:05:05+00:00"}, {"version": "v2.3.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1c5a5e7f270355722ea61fbd7431d89f6b9c6423"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1c5a5e7f270355722ea61fbd7431d89f6b9c6423", "type": "zip", "shasum": "", "reference": "1c5a5e7f270355722ea61fbd7431d89f6b9c6423"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.26"}, "time": "2015-03-13T17:09:38+00:00"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "edb0f826583b10209ff0317964983b04c3bc0574"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/edb0f826583b10209ff0317964983b04c3bc0574", "type": "zip", "shasum": "", "reference": "edb0f826583b10209ff0317964983b04c3bc0574"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.25"}, "time": "2015-01-25T04:18:27+00:00", "require-dev": {"symfony/event-dispatcher": "~2.1"}}, {"version": "v2.3.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b25019d7e91d65e95d0bcd9600d44a2259a99161"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b25019d7e91d65e95d0bcd9600d44a2259a99161", "type": "zip", "shasum": "", "reference": "b25019d7e91d65e95d0bcd9600d44a2259a99161"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.24"}, "time": "2015-01-07T10:18:33+00:00"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a50c64cefa27215d5f3260d2f26b781c66b350d6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a50c64cefa27215d5f3260d2f26b781c66b350d6", "type": "zip", "shasum": "", "reference": "a50c64cefa27215d5f3260d2f26b781c66b350d6"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.23"}, "time": "2014-12-02T19:42:47+00:00"}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ffa26b9f54d1011c728ea9fada3e8f86b6174e7b"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ffa26b9f54d1011c728ea9fada3e8f86b6174e7b", "type": "zip", "shasum": "", "reference": "ffa26b9f54d1011c728ea9fada3e8f86b6174e7b"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.22"}, "time": "2014-11-17T16:27:42+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "aa12ac573c583a74c2cb26ad9be478e119f04ad1"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/aa12ac573c583a74c2cb26ad9be478e119f04ad1", "type": "zip", "shasum": "", "reference": "aa12ac573c583a74c2cb26ad9be478e119f04ad1"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.21"}, "time": "2014-10-05T13:45:10+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7c3ebdbdfd96a38122fd510931f2af6b1baee43f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7c3ebdbdfd96a38122fd510931f2af6b1baee43f", "type": "zip", "shasum": "", "reference": "7c3ebdbdfd96a38122fd510931f2af6b1baee43f"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.20"}, "time": "2014-09-22T08:32:35+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a2723f99716c2fa6411e2eb5842edbe680e4b462"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a2723f99716c2fa6411e2eb5842edbe680e4b462", "type": "zip", "shasum": "", "reference": "a2723f99716c2fa6411e2eb5842edbe680e4b462"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.19"}, "time": "2014-08-07T15:55:24+00:00"}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d9171137f97e9b14d71f7aa83ba5039b68efb8af"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d9171137f97e9b14d71f7aa83ba5039b68efb8af", "type": "zip", "shasum": "", "reference": "d9171137f97e9b14d71f7aa83ba5039b68efb8af"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.18"}, "time": "2014-07-07T10:13:42+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "6dfec56ea85b8f62868d0f22a602ecc038c2e5f5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/6dfec56ea85b8f62868d0f22a602ecc038c2e5f5", "type": "zip", "shasum": "", "reference": "6dfec56ea85b8f62868d0f22a602ecc038c2e5f5"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "c6c5a354a9945a5e9a9a6a495ca19558eb8639e9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/c6c5a354a9945a5e9a9a6a495ca19558eb8639e9", "type": "zip", "shasum": "", "reference": "c6c5a354a9945a5e9a9a6a495ca19558eb8639e9"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.16"}, "time": "2014-05-14T13:35:53+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "255ee393592c0b5fdd4e8af5d6bf78710b280577"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/255ee393592c0b5fdd4e8af5d6bf78710b280577", "type": "zip", "shasum": "", "reference": "255ee393592c0b5fdd4e8af5d6bf78710b280577"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.13"}, "time": "2014-04-26T11:09:19+00:00"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "df17996d37eb113a5675ca4cc2ac45f4fc057cb7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/df17996d37eb113a5675ca4cc2ac45f4fc057cb7", "type": "zip", "shasum": "", "reference": "df17996d37eb113a5675ca4cc2ac45f4fc057cb7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.12"}, "time": "2014-03-01T17:25:29+00:00"}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7613bbd8b02c569e417cb4fcb668a3e6570d7a7f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7613bbd8b02c569e417cb4fcb668a3e6570d7a7f", "type": "zip", "shasum": "", "reference": "7613bbd8b02c569e417cb4fcb668a3e6570d7a7f"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.11"}, "time": "2014-02-12T08:04:19+00:00"}, {"version": "v2.3.10", "version_normalized": "********"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "1a52318149de4167cd722233323507415169261f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/1a52318149de4167cd722233323507415169261f", "type": "zip", "shasum": "", "reference": "1a52318149de4167cd722233323507415169261f"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.9"}, "time": "2014-01-01T07:52:14+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "15031c5ca7583997de218c1f5b1b406f621d18c3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/15031c5ca7583997de218c1f5b1b406f621d18c3", "type": "zip", "shasum": "", "reference": "15031c5ca7583997de218c1f5b1b406f621d18c3"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.8"}, "time": "2013-12-15T18:46:51+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "00848d3e13cf512e77c7498c2b3b0192f61f4b18"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/00848d3e13cf512e77c7498c2b3b0192f61f4b18", "type": "zip", "shasum": "", "reference": "00848d3e13cf512e77c7498c2b3b0192f61f4b18"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.7"}, "time": "2013-11-13T21:27:40+00:00"}, {"version": "v2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f880062d56edefb25b36f2defa65aafe65959dc7"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f880062d56edefb25b36f2defa65aafe65959dc7", "type": "zip", "shasum": "", "reference": "f880062d56edefb25b36f2defa65aafe65959dc7"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.6"}, "time": "2013-09-25T06:04:15+00:00"}, {"version": "v2.3.5", "version_normalized": "*******"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "db78f8ff7fc9e28d25ff9a0bf6ddf9f0e35fbbe3"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/db78f8ff7fc9e28d25ff9a0bf6ddf9f0e35fbbe3", "type": "zip", "shasum": "", "reference": "db78f8ff7fc9e28d25ff9a0bf6ddf9f0e35fbbe3"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.4"}, "time": "2013-08-17T16:34:49+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "290fe87d3618ce49c2caf7c456402affbd9917ac"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/290fe87d3618ce49c2caf7c456402affbd9917ac", "type": "zip", "shasum": "", "reference": "290fe87d3618ce49c2caf7c456402affbd9917ac"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.3"}, "time": "2013-07-21T12:12:18+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "251dd20d2f8acaf2f7832300eec9a20e9951393c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/251dd20d2f8acaf2f7832300eec9a20e9951393c", "type": "zip", "shasum": "", "reference": "251dd20d2f8acaf2f7832300eec9a20e9951393c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.2"}, "time": "2013-07-11T19:36:36+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ff905395bee7f99f422f9b3a2cbf536813fcc1cb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ff905395bee7f99f422f9b3a2cbf536813fcc1cb", "type": "zip", "shasum": "", "reference": "ff905395bee7f99f422f9b3a2cbf536813fcc1cb"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.1"}, "time": "2013-06-11T07:15:14+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "36e238358617de042f7c1d93e012f06b3d7e5f40"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/36e238358617de042f7c1d93e012f06b3d7e5f40", "type": "zip", "shasum": "", "reference": "36e238358617de042f7c1d93e012f06b3d7e5f40"}, "support": {"source": "https://github.com/symfony/console/tree/v2.3.0"}, "time": "2013-05-30T05:11:26+00:00"}, {"version": "v2.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "89e82ad2d09ce753359efc942ce6309053023c47"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/89e82ad2d09ce753359efc942ce6309053023c47", "type": "zip", "shasum": "", "reference": "89e82ad2d09ce753359efc942ce6309053023c47"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.11"}, "time": "2013-11-25T08:44:14+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require-dev": "__unset", "suggest": "__unset"}, {"version": "v2.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ba3a0f7e18c6942f9f501be218360bffba6a9bdc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ba3a0f7e18c6942f9f501be218360bffba6a9bdc", "type": "zip", "shasum": "", "reference": "ba3a0f7e18c6942f9f501be218360bffba6a9bdc"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.10"}, "time": "2013-11-08T00:49:34+00:00"}, {"version": "v2.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7ec2f86df8a0164d1677368f43a4a548b80a1402"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7ec2f86df8a0164d1677368f43a4a548b80a1402", "type": "zip", "shasum": "", "reference": "7ec2f86df8a0164d1677368f43a4a548b80a1402"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.9"}, "time": "2013-09-25T05:58:50+00:00"}, {"version": "v2.2.8", "version_normalized": "*******"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "b9ed9d61ff84296c9ace5ac333c15e6f35062eeb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/b9ed9d61ff84296c9ace5ac333c15e6f35062eeb", "type": "zip", "shasum": "", "reference": "b9ed9d61ff84296c9ace5ac333c15e6f35062eeb"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.6"}, "time": "2013-08-17T16:29:09+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "f0667e2b6dbdf22978436f9ef113c287b1577632"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/f0667e2b6dbdf22978436f9ef113c287b1577632", "type": "zip", "shasum": "", "reference": "f0667e2b6dbdf22978436f9ef113c287b1577632"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.5"}, "time": "2013-07-08T14:34:53+00:00"}, {"version": "v2.2.4", "version_normalized": "*******"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "76f6b389cee92e93b056d602e8ba5dd6526cea5a"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/76f6b389cee92e93b056d602e8ba5dd6526cea5a", "type": "zip", "shasum": "", "reference": "76f6b389cee92e93b056d602e8ba5dd6526cea5a"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.3"}, "time": "2013-06-13T07:47:06+00:00"}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "602d610387a4f703aea12ec65a4d947e0ee7ce5f"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/602d610387a4f703aea12ec65a4d947e0ee7ce5f", "type": "zip", "shasum": "", "reference": "602d610387a4f703aea12ec65a4d947e0ee7ce5f"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.2"}, "time": "2013-05-27T14:47:40+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d146fc32a9cf13bc479820e515048298a34cd432"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d146fc32a9cf13bc479820e515048298a34cd432", "type": "zip", "shasum": "", "reference": "d146fc32a9cf13bc479820e515048298a34cd432"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.1"}, "time": "2013-03-19T20:48:08+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5e29412917f06523987bda63b7bf12dd54161ab6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5e29412917f06523987bda63b7bf12dd54161ab6", "type": "zip", "shasum": "", "reference": "5e29412917f06523987bda63b7bf12dd54161ab6"}, "support": {"source": "https://github.com/symfony/console/tree/v2.2.0"}, "time": "2013-03-01T06:43:14+00:00"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a5e96b2363aca8a5400fec3bbcb3201a4b0513fb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a5e96b2363aca8a5400fec3bbcb3201a4b0513fb", "type": "zip", "shasum": "", "reference": "a5e96b2363aca8a5400fec3bbcb3201a4b0513fb"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.13"}, "time": "2013-06-11T07:34:22+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Console": ""}}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4709ede4eb34e02d9349b6bf2809bd76be008404"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4709ede4eb34e02d9349b6bf2809bd76be008404", "type": "zip", "shasum": "", "reference": "4709ede4eb34e02d9349b6bf2809bd76be008404"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.11"}, "time": "2013-05-26T18:42:07+00:00"}, {"version": "v2.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "7e8f6c549bd4ac0ac358355f65729ec03ec81095"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/7e8f6c549bd4ac0ac358355f65729ec03ec81095", "type": "zip", "shasum": "", "reference": "7e8f6c549bd4ac0ac358355f65729ec03ec81095"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.10"}, "time": "2013-05-06T06:31:43+00:00"}, {"version": "v2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "256b00333ab08ce1f6264152b22e08e4138275eb"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/256b00333ab08ce1f6264152b22e08e4138275eb", "type": "zip", "shasum": "", "reference": "256b00333ab08ce1f6264152b22e08e4138275eb"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.9"}, "time": "2013-03-01T16:50:10+00:00"}, {"version": "v2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e9824f38bbce58d13b4a2538d275fa8c5ce4cda6"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e9824f38bbce58d13b4a2538d275fa8c5ce4cda6", "type": "zip", "shasum": "", "reference": "e9824f38bbce58d13b4a2538d275fa8c5ce4cda6"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.8"}, "time": "2013-01-17T15:20:05+00:00"}, {"version": "v2.1.7", "version_normalized": "*******"}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e27fb799dada52e80c4f18550eca8e45fbe55f0c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e27fb799dada52e80c4f18550eca8e45fbe55f0c", "type": "zip", "shasum": "", "reference": "e27fb799dada52e80c4f18550eca8e45fbe55f0c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.6"}, "time": "2012-12-13T17:42:00+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "528b030bd94d74b06735c9da9fd9495db0567800"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/528b030bd94d74b06735c9da9fd9495db0567800", "type": "zip", "shasum": "", "reference": "528b030bd94d74b06735c9da9fd9495db0567800"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.4"}, "time": "2012-11-09T08:52:51+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "55030275de3c40849b6afac9f913ccd92ffe3232"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/55030275de3c40849b6afac9f913ccd92ffe3232", "type": "zip", "shasum": "", "reference": "55030275de3c40849b6afac9f913ccd92ffe3232"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.3"}, "time": "2012-10-22T14:37:12+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "4d463e30faf03f793c7b7184d7482ccdf71e1a05"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/4d463e30faf03f793c7b7184d7482ccdf71e1a05", "type": "zip", "shasum": "", "reference": "4d463e30faf03f793c7b7184d7482ccdf71e1a05"}, "support": {"source": "https://github.com/symfony/console/tree/v2.1.2"}, "time": "2012-08-22T13:48:41+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******"}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "da9d86bbc5336cde92f00079c0fb2108ab7bf5e9"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/da9d86bbc5336cde92f00079c0fb2108ab7bf5e9", "type": "zip", "shasum": "", "reference": "da9d86bbc5336cde92f00079c0fb2108ab7bf5e9"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.25"}, "time": "2013-02-27T09:08:35+00:00", "require": {"php": ">=5.3.2"}, "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********"}, {"version": "v2.0.23", "version_normalized": "********"}, {"version": "v2.0.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "5aaf6a32ee5d55d6421bde9e9eb06fda00158d04"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/5aaf6a32ee5d55d6421bde9e9eb06fda00158d04", "type": "zip", "shasum": "", "reference": "5aaf6a32ee5d55d6421bde9e9eb06fda00158d04"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.22"}, "time": "2013-01-11T13:49:52+00:00"}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "29e0ef35b1cf4e030db5c3a6bff067a44fc2c8c5"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/29e0ef35b1cf4e030db5c3a6bff067a44fc2c8c5", "type": "zip", "shasum": "", "reference": "29e0ef35b1cf4e030db5c3a6bff067a44fc2c8c5"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.21"}, "time": "2012-11-05T19:52:03+00:00"}, {"version": "v2.0.20", "version_normalized": "********"}, {"version": "v2.0.19", "version_normalized": "********"}, {"version": "v2.0.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "457f9f6b09dfa0ee68d07d660818fd9e6ab22cc2"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/457f9f6b09dfa0ee68d07d660818fd9e6ab22cc2", "type": "zip", "shasum": "", "reference": "457f9f6b09dfa0ee68d07d660818fd9e6ab22cc2"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.18"}, "time": "2012-09-28T14:21:46+00:00"}, {"version": "v2.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "3d0d961fa4e721ca282f2aba05f6eb2b9d20ad14"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/3d0d961fa4e721ca282f2aba05f6eb2b9d20ad14", "type": "zip", "shasum": "", "reference": "3d0d961fa4e721ca282f2aba05f6eb2b9d20ad14"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.17"}, "time": "2012-07-20T05:25:32+00:00"}, {"version": "v2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "43df083224f21ea4acad0326f6ed045c13fea523"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/43df083224f21ea4acad0326f6ed045c13fea523", "type": "zip", "shasum": "", "reference": "43df083224f21ea4acad0326f6ed045c13fea523"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.16"}, "time": "2012-07-10T13:28:02+00:00"}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "575f32de161f1c296f7c8c5e0e68c0974b2af101"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/575f32de161f1c296f7c8c5e0e68c0974b2af101", "type": "zip", "shasum": "", "reference": "575f32de161f1c296f7c8c5e0e68c0974b2af101"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.15"}, "time": "2012-05-16T09:10:06+00:00"}, {"version": "v2.0.14", "version_normalized": "********"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "2bfa3a133f2e68180daca2f1d73b35308d648af8"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/2bfa3a133f2e68180daca2f1d73b35308d648af8", "type": "zip", "shasum": "", "reference": "2bfa3a133f2e68180daca2f1d73b35308d648af8"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.13"}, "time": "2012-04-29T19:28:50+00:00"}, {"version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "bc6b1515fcbcab4aa9c561b0ee4b507588063d39"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/bc6b1515fcbcab4aa9c561b0ee4b507588063d39", "type": "zip", "shasum": "", "reference": "bc6b1515fcbcab4aa9c561b0ee4b507588063d39"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.12"}, "time": "2012-03-12T00:11:44+00:00"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "632c38bc285c63044ccc6173470d022973c2d8ba"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/632c38bc285c63044ccc6173470d022973c2d8ba", "type": "zip", "shasum": "", "reference": "632c38bc285c63044ccc6173470d022973c2d8ba"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.10"}, "time": "2012-01-11T19:33:56+00:00"}, {"version": "v2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "44cce6b12e6003b1db96e5dfc8e67b54eb59f81d"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/44cce6b12e6003b1db96e5dfc8e67b54eb59f81d", "type": "zip", "shasum": "", "reference": "44cce6b12e6003b1db96e5dfc8e67b54eb59f81d"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.9"}, "time": "2012-01-06T03:12:25+00:00"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "e76444b1c5e78d40ea7658988a8051ef55d9684c"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/e76444b1c5e78d40ea7658988a8051ef55d9684c", "type": "zip", "shasum": "", "reference": "e76444b1c5e78d40ea7658988a8051ef55d9684c"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.7"}, "time": "2011-11-17T05:58:47+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "a1197f7dada4ac936a3df3e26dffe7f4423c28fc"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/a1197f7dada4ac936a3df3e26dffe7f4423c28fc", "type": "zip", "shasum": "", "reference": "a1197f7dada4ac936a3df3e26dffe7f4423c28fc"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.6"}, "time": "2011-11-14T13:56:06+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "d7b1718424b3eed1a58f9d96b01f1590716a9365"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/d7b1718424b3eed1a58f9d96b01f1590716a9365", "type": "zip", "shasum": "", "reference": "d7b1718424b3eed1a58f9d96b01f1590716a9365"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/console.git", "type": "git", "reference": "ce34bc50cf78ef247a1506d4894e54830fa84737"}, "dist": {"url": "https://api.github.com/repos/symfony/console/zipball/ce34bc50cf78ef247a1506d4894e54830fa84737", "type": "zip", "shasum": "", "reference": "ce34bc50cf78ef247a1506d4894e54830fa84737"}, "support": {"source": "https://github.com/symfony/console/tree/v2.0.4"}, "time": "2011-09-26T22:55:43+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [], "last-modified": "Sat, 28 Jun 2025 08:29:05 GMT"}