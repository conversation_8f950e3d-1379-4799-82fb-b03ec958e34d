{"minified": "composer/2.0", "packages": {"darkaonline/l5-swagger": [{"name": "darkaonline/l5-swagger", "description": "OpenApi or Swagger integration to Laravel", "keywords": ["documentation", "ui", "api", "laravel", "specification", "swagger", "openapi"], "homepage": "", "version": "9.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "2c26427f8c41db8e72232415e7287313e6b6a2e2"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/2c26427f8c41db8e72232415e7287313e6b6a2e2", "type": "zip", "shasum": "", "reference": "2c26427f8c41db8e72232415e7287313e6b6a2e2"}, "type": "library", "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/9.0.1"}, "funding": [{"url": "https://github.com/DarkaOnLine", "type": "github"}], "time": "2025-02-28T06:25:02+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"L5Swagger\\": "src"}}, "extra": {"laravel": {"aliases": {"L5Swagger": "L5Swagger\\L5SwaggerFacade"}, "providers": ["L5Swagger\\L5SwaggerServiceProvider"]}}, "require": {"php": "^8.2", "laravel/framework": "^12.0 || ^11.0", "zircote/swagger-php": "^5.0.0", "swagger-api/swagger-ui": ">=5.18.3", "symfony/yaml": "^5.0 || ^6.0 || ^7.0", "ext-json": "*", "doctrine/annotations": "^1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^11.0", "mockery/mockery": "1.*", "orchestra/testbench": "^10.0 || ^9.0 || ^8.0 || 7.* || ^6.15 || 5.*", "php-coveralls/php-coveralls": "^2.0", "phpstan/phpstan": "^2.1"}}, {"version": "9.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "7fffe3de03f2cf36ab38e40e864feb8f392b9458"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/7fffe3de03f2cf36ab38e40e864feb8f392b9458", "type": "zip", "shasum": "", "reference": "7fffe3de03f2cf36ab38e40e864feb8f392b9458"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/9.0.0"}, "time": "2025-02-24T14:41:30+00:00"}, {"version": "8.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "4cf2b3faae9e9cffd05e4eb6e066741bf56f0a85"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/4cf2b3faae9e9cffd05e4eb6e066741bf56f0a85", "type": "zip", "shasum": "", "reference": "4cf2b3faae9e9cffd05e4eb6e066741bf56f0a85"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.6.5"}, "time": "2025-02-06T14:54:32+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^11.0 || ^10.0 || ^9.0 || >=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2.0 || ^4.0.0", "swagger-api/swagger-ui": "^3.0 || >=4.1.3", "symfony/yaml": "^5.0 || ^6.0 || ^7.0", "ext-json": "*", "doctrine/annotations": "^1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^11.0 || ^10.0 || ^9.5", "mockery/mockery": "1.*", "orchestra/testbench": "^9.0 || ^8.0 || 7.* || ^6.15 || 5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "77b5fa0f10d6df5d105b1a9d6434cb29a73b6716"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/77b5fa0f10d6df5d105b1a9d6434cb29a73b6716", "type": "zip", "shasum": "", "reference": "77b5fa0f10d6df5d105b1a9d6434cb29a73b6716"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.6.4"}, "time": "2025-02-06T07:47:13+00:00"}, {"version": "8.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "b37695804b786c04ab4077ceb6c0f2907ccd0153"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/b37695804b786c04ab4077ceb6c0f2907ccd0153", "type": "zip", "shasum": "", "reference": "b37695804b786c04ab4077ceb6c0f2907ccd0153"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.6.3"}, "time": "2024-10-28T06:29:43+00:00"}, {"version": "8.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "9d72140330b5df4379b907d9582bf90b34ae59a2"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/9d72140330b5df4379b907d9582bf90b34ae59a2", "type": "zip", "shasum": "", "reference": "9d72140330b5df4379b907d9582bf90b34ae59a2"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.6.2"}, "time": "2024-08-29T08:02:07+00:00"}, {"version": "8.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "44bab2467f69c3ec6f17f0b207e9cfcb7074f1b3"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/44bab2467f69c3ec6f17f0b207e9cfcb7074f1b3", "type": "zip", "shasum": "", "reference": "44bab2467f69c3ec6f17f0b207e9cfcb7074f1b3"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.6.1"}, "time": "2024-08-09T05:01:05+00:00"}, {"version": "8.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "76ce7aa61ac61c0a495c98ba2bc0b886ac4b6eaa"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/76ce7aa61ac61c0a495c98ba2bc0b886ac4b6eaa", "type": "zip", "shasum": "", "reference": "76ce7aa61ac61c0a495c98ba2bc0b886ac4b6eaa"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.6.0"}, "time": "2024-03-13T14:06:52+00:00"}, {"version": "8.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "2558ac430be2f825436267e6774bd407eab95372"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/2558ac430be2f825436267e6774bd407eab95372", "type": "zip", "shasum": "", "reference": "2558ac430be2f825436267e6774bd407eab95372"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.5.2"}, "time": "2023-12-12T09:53:16+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^10.0 || ^9.0 || >=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2.0 || ^4.0.0", "swagger-api/swagger-ui": "^3.0 || >=4.1.3", "symfony/yaml": "^5.0 || ^6.0 || ^7.0", "ext-json": "*", "doctrine/annotations": "^1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^10.0 || ^9.5", "mockery/mockery": "1.*", "orchestra/testbench": "^8.0 || 7.* || ^6.15 || 5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "02348149f1833c63bf52764838d5659507857394"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/02348149f1833c63bf52764838d5659507857394", "type": "zip", "shasum": "", "reference": "02348149f1833c63bf52764838d5659507857394"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.5.1"}, "time": "2023-06-05T04:21:50+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^10.0 || ^9.0 || >=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2.0 || ^4.0.0", "swagger-api/swagger-ui": "^3.0 || >=4.1.3", "symfony/yaml": "^5.0 || ^6.0", "ext-json": "*"}}, {"version": "8.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "8e61ce632fb90eb9d78cf34918d133c46baa93b4"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/8e61ce632fb90eb9d78cf34918d133c46baa93b4", "type": "zip", "shasum": "", "reference": "8e61ce632fb90eb9d78cf34918d133c46baa93b4"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.5.0"}, "time": "2023-02-15T07:29:49+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^10.0 || ^9.0 || >=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2.0 || ^4.0.0", "swagger-api/swagger-ui": "^3.0 || ^4.0", "symfony/yaml": "^5.0 || ^6.0", "ext-json": "*"}}, {"version": "8.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "0b1c3f5aca80fc710877bf7fdbab6d28d9b54f4c"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/0b1c3f5aca80fc710877bf7fdbab6d28d9b54f4c", "type": "zip", "shasum": "", "reference": "0b1c3f5aca80fc710877bf7fdbab6d28d9b54f4c"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.4.1"}, "time": "2022-09-22T05:16:35+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^9.0 || >=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2 || ^4.0", "swagger-api/swagger-ui": "^3.0 || ^4.0", "symfony/yaml": "^5.0 || ^6.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^9.5", "mockery/mockery": "1.*", "orchestra/testbench": "7.* || ^6.15 || 5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "958e4778969ebdaa1ca263269c6d98ef73dfa45f"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/958e4778969ebdaa1ca263269c6d98ef73dfa45f", "type": "zip", "shasum": "", "reference": "958e4778969ebdaa1ca263269c6d98ef73dfa45f"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.4.0"}, "time": "2022-09-21T10:30:34+00:00"}, {"version": "8.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "64d820b006cf65d4247eef497c92501eb6b3a98d"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/64d820b006cf65d4247eef497c92501eb6b3a98d", "type": "zip", "shasum": "", "reference": "64d820b006cf65d4247eef497c92501eb6b3a98d"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.3.3"}, "time": "2022-08-05T05:21:42+00:00"}, {"version": "8.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "e65fa1bd94a4f19282864f63c92d21902ec77aa3"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/e65fa1bd94a4f19282864f63c92d21902ec77aa3", "type": "zip", "shasum": "", "reference": "e65fa1bd94a4f19282864f63c92d21902ec77aa3"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.3.2"}, "time": "2022-04-27T07:29:07+00:00"}, {"version": "8.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "95326884fa22c9840866b688a60242f5f80acd00"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/95326884fa22c9840866b688a60242f5f80acd00", "type": "zip", "shasum": "", "reference": "95326884fa22c9840866b688a60242f5f80acd00"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.3.1"}, "time": "2022-04-01T08:06:18+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^9.0 || >=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2 || ^4.0", "swagger-api/swagger-ui": "^3.0 || ^4.0", "symfony/yaml": "^5.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^9.5", "mockery/mockery": "1.*", "orchestra/testbench": "6.* || 5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "b2e7885df13b0c43b48a8a1028d77428126228da"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/b2e7885df13b0c43b48a8a1028d77428126228da", "type": "zip", "shasum": "", "reference": "b2e7885df13b0c43b48a8a1028d77428126228da"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.3.0"}, "time": "2022-02-14T07:30:01+00:00"}, {"version": "8.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "f7f4451002d41ddaef120d2dc70e00630cccb787"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/f7f4451002d41ddaef120d2dc70e00630cccb787", "type": "zip", "shasum": "", "reference": "f7f4451002d41ddaef120d2dc70e00630cccb787"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.2.0"}, "time": "2022-02-03T07:36:33+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": ">=8.40.0 || ^7.0", "zircote/swagger-php": "^3.2 || ^4.0", "swagger-api/swagger-ui": "^3.0 || ^4.0", "symfony/yaml": "^5.0", "ext-json": "*"}}, {"version": "8.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "aab46bf494ba52dcdd7d259ce178ad33d0327d04"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/aab46bf494ba52dcdd7d259ce178ad33d0327d04", "type": "zip", "shasum": "", "reference": "aab46bf494ba52dcdd7d259ce178ad33d0327d04"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.1.0"}, "time": "2022-01-07T09:08:44+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": ">=8.40.0 || ^7.0", "zircote/swagger-php": "3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^5.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "9.*", "mockery/mockery": "1.*", "orchestra/testbench": "6.* || 5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "c882f984236ffeaebe4bed76b223ce9c3ec032fb"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/c882f984236ffeaebe4bed76b223ce9c3ec032fb", "type": "zip", "shasum": "", "reference": "c882f984236ffeaebe4bed76b223ce9c3ec032fb"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.9"}, "time": "2021-10-18T06:28:30+00:00", "require-dev": {"phpunit/phpunit": "8.*", "mockery/mockery": "1.*", "orchestra/testbench": "6.* || 5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "c4f678149bc99d6f474663d674f253f0021f2c07"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/c4f678149bc99d6f474663d674f253f0021f2c07", "type": "zip", "shasum": "", "reference": "c4f678149bc99d6f474663d674f253f0021f2c07"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.8"}, "time": "2021-10-12T05:37:28+00:00"}, {"version": "8.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "5e3d9c067797ebc35304d20acd94aeb40cde2825"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/5e3d9c067797ebc35304d20acd94aeb40cde2825", "type": "zip", "shasum": "", "reference": "5e3d9c067797ebc35304d20acd94aeb40cde2825"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.7"}, "time": "2021-07-12T06:31:40+00:00"}, {"version": "8.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "4d5c4f1230cd7d4a66236ddafb8651b4f9d97d9d"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/4d5c4f1230cd7d4a66236ddafb8651b4f9d97d9d", "type": "zip", "shasum": "", "reference": "4d5c4f1230cd7d4a66236ddafb8651b4f9d97d9d"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.6"}, "time": "2021-05-12T11:18:31+00:00"}, {"version": "8.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "238e5d318b3d48a77c76b6950c51146578563853"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/238e5d318b3d48a77c76b6950c51146578563853", "type": "zip", "shasum": "", "reference": "238e5d318b3d48a77c76b6950c51146578563853"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.5"}, "time": "2021-05-07T09:57:00+00:00"}, {"version": "8.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "3477e7013daf8b6fc142c45fdcb9fe6c74d7398d"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/3477e7013daf8b6fc142c45fdcb9fe6c74d7398d", "type": "zip", "shasum": "", "reference": "3477e7013daf8b6fc142c45fdcb9fe6c74d7398d"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.4"}, "time": "2020-12-08T13:29:20+00:00", "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^8.0 || ^7.0", "zircote/swagger-php": "3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^5.0", "ext-json": "*"}}, {"version": "8.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "446d3b77097d8dd737b1539823660fafa3524cb4"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/446d3b77097d8dd737b1539823660fafa3524cb4", "type": "zip", "shasum": "", "reference": "446d3b77097d8dd737b1539823660fafa3524cb4"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.3"}, "time": "2020-12-07T19:26:04+00:00"}, {"description": "Swagger integration to Laravel 5", "keywords": ["api", "laravel", "swagger"], "version": "8.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "9b900e353503237ed983cae17277ff12fc1aaaf9"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/9b900e353503237ed983cae17277ff12fc1aaaf9", "type": "zip", "shasum": "", "reference": "9b900e353503237ed983cae17277ff12fc1aaaf9"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/8.0.2"}, "time": "2020-09-09T05:12:50+00:00", "require": {"php": "^7.2", "laravel/framework": "^8.0 || ^7.0", "zircote/swagger-php": "3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^5.0", "ext-json": "*"}}, {"version": "8.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "f720e5e69e8096f62713b408a747145cae3a80b1"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/f720e5e69e8096f62713b408a747145cae3a80b1", "type": "zip", "shasum": "", "reference": "f720e5e69e8096f62713b408a747145cae3a80b1"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2020-08-21T05:56:03+00:00", "require": {"php": "^7.2", "laravel/framework": "^7.0", "zircote/swagger-php": "3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^5.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "8.*", "mockery/mockery": "1.*", "orchestra/testbench": "5.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "6e885ad33e00c4a282d0917847322010508bd286"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/6e885ad33e00c4a282d0917847322010508bd286", "type": "zip", "shasum": "", "reference": "6e885ad33e00c4a282d0917847322010508bd286"}, "time": "2020-08-18T06:15:39+00:00"}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "64e9cfaa0065f1c9d9b85c203a1de893698c7336"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/64e9cfaa0065f1c9d9b85c203a1de893698c7336", "type": "zip", "shasum": "", "reference": "64e9cfaa0065f1c9d9b85c203a1de893698c7336"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/7.0.1"}, "time": "2020-08-18T06:29:25+00:00", "require": {"php": "^7.2", "laravel/framework": "^7.0", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^5.0"}, "suggest": {"zircote/swagger-php:~2.0": "!!! Require Swagger-PHP ~2.0 for @SWG annotations support !!!"}}, {"version": "7.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "e5a8d2b88ebd00cb1aab8f7b8380eddc7792e50a"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/e5a8d2b88ebd00cb1aab8f7b8380eddc7792e50a", "type": "zip", "shasum": "", "reference": "e5a8d2b88ebd00cb1aab8f7b8380eddc7792e50a"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "funding": [], "time": "2020-03-05T07:15:45+00:00"}, {"version": "6.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "d0745ba413f679528fe6ba304b4095a6527f6e46"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/d0745ba413f679528fe6ba304b4095a6527f6e46", "type": "zip", "shasum": "", "reference": "d0745ba413f679528fe6ba304b4095a6527f6e46"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/6.0.6"}, "funding": [{"url": "https://github.com/DarkaOnLine", "type": "github"}], "time": "2021-01-11T12:34:15+00:00", "extra": {"laravel": {"providers": ["L5Swagger\\L5SwaggerServiceProvider"]}}, "require": {"php": "^7.2 || ^8.0", "laravel/framework": "^6.0", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1 || ^5.0"}, "require-dev": {"phpunit/phpunit": "8.*", "mockery/mockery": "1.*", "orchestra/testbench": "4.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "496992e73c588b76b2ccabddd6852296c29ae626"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/496992e73c588b76b2ccabddd6852296c29ae626", "type": "zip", "shasum": "", "reference": "496992e73c588b76b2ccabddd6852296c29ae626"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/6.x"}, "time": "2020-08-18T06:29:54+00:00", "require": {"php": "^7.2", "laravel/framework": "^6.0", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1"}}, {"version": "6.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "690a2db0db6091139504f454c42fcdfb7a46f405"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/690a2db0db6091139504f454c42fcdfb7a46f405", "type": "zip", "shasum": "", "reference": "690a2db0db6091139504f454c42fcdfb7a46f405"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/6.0.4"}, "time": "2020-06-05T05:07:26+00:00"}, {"version": "6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "df9e72d705e7757a700ac58d41c62b10c6ddc48c"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/df9e72d705e7757a700ac58d41c62b10c6ddc48c", "type": "zip", "shasum": "", "reference": "df9e72d705e7757a700ac58d41c62b10c6ddc48c"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/6.0.3"}, "time": "2019-11-08T17:08:43+00:00", "funding": "__unset"}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "f89cfa4499201df477d6c7fd4e0aeb683a6767fa"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/f89cfa4499201df477d6c7fd4e0aeb683a6767fa", "type": "zip", "shasum": "", "reference": "f89cfa4499201df477d6c7fd4e0aeb683a6767fa"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/6.0.2"}, "time": "2019-10-28T13:31:16+00:00"}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "b1fda96c54015bd2aa85204918816541483f4ca1"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/b1fda96c54015bd2aa85204918816541483f4ca1", "type": "zip", "shasum": "", "reference": "b1fda96c54015bd2aa85204918816541483f4ca1"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2019-09-04T12:17:21+00:00"}, {"version": "6.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "d73313f0fee757ef58149210e7afbaeed59be3f1"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/d73313f0fee757ef58149210e7afbaeed59be3f1", "type": "zip", "shasum": "", "reference": "d73313f0fee757ef58149210e7afbaeed59be3f1"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/6.0"}, "time": "2019-09-04T04:50:41+00:00"}, {"version": "5.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "fddbeed402553c7548e3389d2c501c4fdad57dbe"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/fddbeed402553c7548e3389d2c501c4fdad57dbe", "type": "zip", "shasum": "", "reference": "fddbeed402553c7548e3389d2c501c4fdad57dbe"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.8.x"}, "funding": [{"url": "https://github.com/DarkaOnLine", "type": "github"}], "time": "2020-08-21T05:39:35+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*|5.7.*|5.8.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1"}, "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "1.*", "orchestra/testbench": "3.6.*|3.8.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "5.8.4", "version_normalized": "*******", "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.8.4"}}, {"version": "5.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "32b312a7c5f0501fc79efef812ed2dade6d97200"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/32b312a7c5f0501fc79efef812ed2dade6d97200", "type": "zip", "shasum": "", "reference": "32b312a7c5f0501fc79efef812ed2dade6d97200"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.8.x"}, "time": "2019-09-04T12:15:22+00:00", "funding": "__unset"}, {"version": "5.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "085ca35c6ac9b806e971584875442f12185236b8"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/085ca35c6ac9b806e971584875442f12185236b8", "type": "zip", "shasum": "", "reference": "085ca35c6ac9b806e971584875442f12185236b8"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.8.2"}, "time": "2019-09-03T13:24:21+00:00"}, {"version": "5.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "3feea73615016405ac46b807b1eac44a188c2e7e"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/3feea73615016405ac46b807b1eac44a188c2e7e", "type": "zip", "shasum": "", "reference": "3feea73615016405ac46b807b1eac44a188c2e7e"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2019-07-12T05:47:33+00:00"}, {"version": "5.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "dba5b0c44c122784a66e228098f3b9a5aac675ee"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/dba5b0c44c122784a66e228098f3b9a5aac675ee", "type": "zip", "shasum": "", "reference": "dba5b0c44c122784a66e228098f3b9a5aac675ee"}, "time": "2019-02-27T06:02:19+00:00"}, {"version": "5.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "e90af8e73f17d92aaa69e48208df41e40c9c9317"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/e90af8e73f17d92aaa69e48208df41e40c9c9317", "type": "zip", "shasum": "", "reference": "e90af8e73f17d92aaa69e48208df41e40c9c9317"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.7.3"}, "funding": [{"url": "https://github.com/DarkaOnLine", "type": "github"}], "time": "2020-08-21T05:45:24+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*|5.7.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1"}, "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "1.*", "orchestra/testbench": "3.6.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "5.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "eb5c20e13ab3e47ad7821af60d4a906cce60d33c"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/eb5c20e13ab3e47ad7821af60d4a906cce60d33c", "type": "zip", "shasum": "", "reference": "eb5c20e13ab3e47ad7821af60d4a906cce60d33c"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2018-09-20T11:39:39+00:00", "funding": "__unset"}, {"version": "5.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "e7a08390ade2d989ef458dfb0ca7ab009da020d2"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/e7a08390ade2d989ef458dfb0ca7ab009da020d2", "type": "zip", "shasum": "", "reference": "e7a08390ade2d989ef458dfb0ca7ab009da020d2"}, "time": "2018-08-27T05:48:16+00:00"}, {"version": "5.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "3eaba4d034c719f8b1250eb1a8dbb3f41504cb4f"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/3eaba4d034c719f8b1250eb1a8dbb3f41504cb4f", "type": "zip", "shasum": "", "reference": "3eaba4d034c719f8b1250eb1a8dbb3f41504cb4f"}, "time": "2018-08-23T07:42:58+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1"}}, {"version": "5.6.9", "version_normalized": "*******"}, {"version": "5.6.8", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "691b393085015434bd43e4768812d5937564c145"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/691b393085015434bd43e4768812d5937564c145", "type": "zip", "shasum": "", "reference": "691b393085015434bd43e4768812d5937564c145"}, "time": "2018-07-25T20:30:06+00:00", "suggest": "__unset"}, {"version": "5.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "c081760712742fa0e984992aa359ef941b8b9572"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/c081760712742fa0e984992aa359ef941b8b9572", "type": "zip", "shasum": "", "reference": "c081760712742fa0e984992aa359ef941b8b9572"}, "time": "2018-07-19T06:34:18+00:00"}, {"version": "5.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "397bff23b3edd57cf39622b16f0908eafeb35d26"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/397bff23b3edd57cf39622b16f0908eafeb35d26", "type": "zip", "shasum": "", "reference": "397bff23b3edd57cf39622b16f0908eafeb35d26"}, "time": "2018-06-11T12:53:12+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0"}, "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "1.*", "orchestra/testbench": "3.6.*", "satooshi/php-coveralls": "^2.0"}}, {"version": "5.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "89438e7155aa3e486e06e901b6b8b7bfe043f6e2"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/89438e7155aa3e486e06e901b6b8b7bfe043f6e2", "type": "zip", "shasum": "", "reference": "89438e7155aa3e486e06e901b6b8b7bfe043f6e2"}, "time": "2018-04-26T06:04:14+00:00"}, {"version": "5.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "4ad03c4ceecd3e0ad92721dc57cde07747109006"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/4ad03c4ceecd3e0ad92721dc57cde07747109006", "type": "zip", "shasum": "", "reference": "4ad03c4ceecd3e0ad92721dc57cde07747109006"}, "time": "2018-03-02T11:12:01+00:00", "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "0.9.*", "orchestra/testbench": "3.6.*", "satooshi/php-coveralls": "^1.0"}}, {"version": "5.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "ee1de132297dda330cee4406ab678423cac26733"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/ee1de132297dda330cee4406ab678423cac26733", "type": "zip", "shasum": "", "reference": "ee1de132297dda330cee4406ab678423cac26733"}, "time": "2018-02-19T10:31:04+00:00"}, {"version": "5.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "0f9e6e7e7eb900bf6cd53f2b7584c55694d1be5b"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/0f9e6e7e7eb900bf6cd53f2b7584c55694d1be5b", "type": "zip", "shasum": "", "reference": "0f9e6e7e7eb900bf6cd53f2b7584c55694d1be5b"}, "time": "2018-02-12T07:48:39+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*", "zircote/swagger-php": "~2.0", "swagger-api/swagger-ui": "^3.0"}}, {"version": "5.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "fbb13f56a2077b129e848a095aa419b82ccbc43d"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/fbb13f56a2077b129e848a095aa419b82ccbc43d", "type": "zip", "shasum": "", "reference": "fbb13f56a2077b129e848a095aa419b82ccbc43d"}, "time": "2018-02-08T11:32:52+00:00"}, {"version": "5.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "691b393085015434bd43e4768812d5937564c145"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/691b393085015434bd43e4768812d5937564c145", "type": "zip", "shasum": "", "reference": "691b393085015434bd43e4768812d5937564c145"}, "time": "2018-07-25T20:30:06+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1"}, "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "1.*", "orchestra/testbench": "3.6.*", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "ac8b7d197415344a75749016c84522fe56c24874"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/ac8b7d197415344a75749016c84522fe56c24874", "type": "zip", "shasum": "", "reference": "ac8b7d197415344a75749016c84522fe56c24874"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.5.8.x"}, "time": "2018-09-07T16:06:47+00:00", "require": {"php": "~7.0", "laravel/framework": "5.5.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0"}, "require-dev": {"phpunit/phpunit": "6.*", "mockery/mockery": "0.9.*", "orchestra/testbench": "3.5.*", "satooshi/php-coveralls": "^1.0"}}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "e7a08390ade2d989ef458dfb0ca7ab009da020d2"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/e7a08390ade2d989ef458dfb0ca7ab009da020d2", "type": "zip", "shasum": "", "reference": "e7a08390ade2d989ef458dfb0ca7ab009da020d2"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2018-08-27T05:48:16+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*|5.7.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1"}, "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "1.*", "orchestra/testbench": "3.6.*", "php-coveralls/php-coveralls": "^2.0"}, "suggest": {"zircote/swagger-php:~2.0": "!!! Require Swagger-PHP ~2.0 for @SWG annotations support !!!"}}, {"version": "*******", "version_normalized": "*******"}, {"version": "5.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "9703f14c3bfab00a164830fe466b8210abd0dd08"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/9703f14c3bfab00a164830fe466b8210abd0dd08", "type": "zip", "shasum": "", "reference": "9703f14c3bfab00a164830fe466b8210abd0dd08"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.5.x"}, "time": "2018-03-02T11:30:24+00:00", "require": {"php": "~7.0", "laravel/framework": "5.5.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0"}, "require-dev": {"phpunit/phpunit": "6.*", "mockery/mockery": "0.9.*", "orchestra/testbench": "3.5.*", "satooshi/php-coveralls": "^1.0"}, "suggest": "__unset"}, {"version": "5.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "f2ec54e539b78f79be2893675d1237d289205829"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/f2ec54e539b78f79be2893675d1237d289205829", "type": "zip", "shasum": "", "reference": "f2ec54e539b78f79be2893675d1237d289205829"}, "time": "2018-02-22T06:26:51+00:00"}, {"version": "5.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "c7005dac877c917de712def0383d778fc29cef5e"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/c7005dac877c917de712def0383d778fc29cef5e", "type": "zip", "shasum": "", "reference": "c7005dac877c917de712def0383d778fc29cef5e"}, "time": "2018-02-19T10:30:42+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*", "zircote/swagger-php": "~2.0|3.*", "swagger-api/swagger-ui": "^3.0"}, "require-dev": {"phpunit/phpunit": "7.*", "mockery/mockery": "0.9.*", "orchestra/testbench": "3.6.*", "satooshi/php-coveralls": "^1.0"}}, {"version": "5.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "6e452a6421f0083fb8180188c3e6da993a8e3e2c"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/6e452a6421f0083fb8180188c3e6da993a8e3e2c", "type": "zip", "shasum": "", "reference": "6e452a6421f0083fb8180188c3e6da993a8e3e2c"}, "time": "2018-02-12T07:49:03+00:00", "require": {"php": ">=7.1.3", "laravel/framework": "5.6.*", "zircote/swagger-php": "~2.0", "swagger-api/swagger-ui": "^3.0"}}, {"version": "5.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "dec90fdf336ba478f8014d41c4abe9a776cd14b8"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/dec90fdf336ba478f8014d41c4abe9a776cd14b8", "type": "zip", "shasum": "", "reference": "dec90fdf336ba478f8014d41c4abe9a776cd14b8"}, "time": "2018-02-08T11:06:34+00:00", "require": {"php": "^7.0", "laravel/framework": "5.5.*", "zircote/swagger-php": "~2.0", "swagger-api/swagger-ui": "^3.0"}, "require-dev": {"phpunit/phpunit": "6.*", "mockery/mockery": "0.9.*", "orchestra/testbench": "3.5.*", "satooshi/php-coveralls": "^1.0"}}, {"version": "5.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "dfc85b7b74d3e654db64dc74a722b2a9d341afe0"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/dfc85b7b74d3e654db64dc74a722b2a9d341afe0", "type": "zip", "shasum": "", "reference": "dfc85b7b74d3e654db64dc74a722b2a9d341afe0"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2018-01-11T12:38:10+00:00"}, {"version": "5.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "d77b3a4d2a09b275ffb70d8c00796bfdb8e71ee8"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/d77b3a4d2a09b275ffb70d8c00796bfdb8e71ee8", "type": "zip", "shasum": "", "reference": "d77b3a4d2a09b275ffb70d8c00796bfdb8e71ee8"}, "time": "2017-10-09T05:26:15+00:00"}, {"version": "5.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "1643446dcc527e49fc4982d7765025c7186c9f12"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/1643446dcc527e49fc4982d7765025c7186c9f12", "type": "zip", "shasum": "", "reference": "1643446dcc527e49fc4982d7765025c7186c9f12"}, "time": "2017-09-07T08:44:51+00:00"}, {"version": "5.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "8827acb21ec5c0f705d7690d073f9785a842569c"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/8827acb21ec5c0f705d7690d073f9785a842569c", "type": "zip", "shasum": "", "reference": "8827acb21ec5c0f705d7690d073f9785a842569c"}, "time": "2017-08-31T05:59:11+00:00", "extra": "__unset"}, {"version": "5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "e56e49aaddf1bcbe8d3ed3c623e0382d792cb558"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/e56e49aaddf1bcbe8d3ed3c623e0382d792cb558", "type": "zip", "shasum": "", "reference": "e56e49aaddf1bcbe8d3ed3c623e0382d792cb558"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.4.x"}, "time": "2017-08-31T05:43:35+00:00", "require": {"php": "^7.0", "laravel/framework": "5.4.*", "zircote/swagger-php": "~2.0", "swagger-api/swagger-ui": "^3.0"}, "require-dev": {"phpunit/phpunit": "6.*", "mockery/mockery": "0.9.*", "orchestra/testbench": "~3.4", "satooshi/php-coveralls": "^1.0"}}, {"version": "5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "949a4fcd9baaa0b4d936ec8d1b9190d3838b653a"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/949a4fcd9baaa0b4d936ec8d1b9190d3838b653a", "type": "zip", "shasum": "", "reference": "949a4fcd9baaa0b4d936ec8d1b9190d3838b653a"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2017-08-07T06:06:36+00:00"}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "211631349d1823eac6a7b1664e942373c6a3deaf"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/211631349d1823eac6a7b1664e942373c6a3deaf", "type": "zip", "shasum": "", "reference": "211631349d1823eac6a7b1664e942373c6a3deaf"}, "time": "2017-07-12T05:51:13+00:00"}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "c385764ff940af757e792d24579287a1b821d0a4"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/c385764ff940af757e792d24579287a1b821d0a4", "type": "zip", "shasum": "", "reference": "c385764ff940af757e792d24579287a1b821d0a4"}, "time": "2017-06-09T19:45:45+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "7f3150c72c58c813e46f031603d0f9c9bbaebaa0"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/7f3150c72c58c813e46f031603d0f9c9bbaebaa0", "type": "zip", "shasum": "", "reference": "7f3150c72c58c813e46f031603d0f9c9bbaebaa0"}, "time": "2017-06-09T18:27:00+00:00"}, {"version": "5.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "1b22cc1d8dda30fe69e34a9f9523ff5e99ed4adf"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/1b22cc1d8dda30fe69e34a9f9523ff5e99ed4adf", "type": "zip", "shasum": "", "reference": "1b22cc1d8dda30fe69e34a9f9523ff5e99ed4adf"}, "time": "2017-06-05T11:43:01+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "9ec908fedf2fa911f2a11456adb88f0aeaa317a2"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/9ec908fedf2fa911f2a11456adb88f0aeaa317a2", "type": "zip", "shasum": "", "reference": "9ec908fedf2fa911f2a11456adb88f0aeaa317a2"}, "time": "2017-02-03T06:21:26+00:00", "autoload": {"psr-4": {"L5Swagger\\": "src"}}, "require": {"php": ">=5.6.4", "laravel/framework": "5.4.*", "zircote/swagger-php": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5.0", "mockery/mockery": "0.9.*", "orchestra/testbench": "~3.4", "codeclimate/php-test-reporter": "~0.1"}}, {"version": "4.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "4bef96a428ad94d1f6350cf88958c811979d519f"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/4bef96a428ad94d1f6350cf88958c811979d519f", "type": "zip", "shasum": "", "reference": "4bef96a428ad94d1f6350cf88958c811979d519f"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/4.0"}, "time": "2017-01-30T09:35:26+00:00"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "07621aad8aa2c99b327cba10c487dce90a223de4"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/07621aad8aa2c99b327cba10c487dce90a223de4", "type": "zip", "shasum": "", "reference": "07621aad8aa2c99b327cba10c487dce90a223de4"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2017-02-07T10:55:34+00:00", "require-dev": {"phpunit/phpunit": "~5.0", "mockery/mockery": "0.9.*", "orchestra/testbench": "~3.4", "satooshi/php-coveralls": "^1.0"}}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "a1424621acfe5252b0dd9dd72be4e8cc4dd8c1f7"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/a1424621acfe5252b0dd9dd72be4e8cc4dd8c1f7", "type": "zip", "shasum": "", "reference": "a1424621acfe5252b0dd9dd72be4e8cc4dd8c1f7"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/3.x"}, "time": "2017-02-03T06:22:43+00:00", "require": {"php": ">=5.5.9", "laravel/framework": "5.1.*|5.2.*|5.3.*", "zircote/swagger-php": "~2.0"}, "require-dev": {"orchestra/testbench": "~3.0", "codeclimate/php-test-reporter": "~0.1"}}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "58d17b8356c10dd8cdfb3e2c2513181b8b4b2acf"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/58d17b8356c10dd8cdfb3e2c2513181b8b4b2acf", "type": "zip", "shasum": "", "reference": "58d17b8356c10dd8cdfb3e2c2513181b8b4b2acf"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2016-12-02T09:25:48+00:00"}, {"version": "3.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "ba92fedf85556db7de354f2a7b5a959a690e65a0"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/ba92fedf85556db7de354f2a7b5a959a690e65a0", "type": "zip", "shasum": "", "reference": "ba92fedf85556db7de354f2a7b5a959a690e65a0"}, "time": "2016-10-14T07:26:36+00:00"}, {"version": "3.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "f5e6f32b0abcfbf97d582d0edb40345c07d31cf9"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/f5e6f32b0abcfbf97d582d0edb40345c07d31cf9", "type": "zip", "shasum": "", "reference": "f5e6f32b0abcfbf97d582d0edb40345c07d31cf9"}, "time": "2016-08-11T05:02:37+00:00"}, {"version": "3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "37d84532ea9bd867ef0b777c74eaced98a4d3bd8"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/37d84532ea9bd867ef0b777c74eaced98a4d3bd8", "type": "zip", "shasum": "", "reference": "37d84532ea9bd867ef0b777c74eaced98a4d3bd8"}, "time": "2016-07-20T07:01:30+00:00", "require": {"php": ">=5.5.9", "laravel/framework": "5.1.*|5.2.*", "zircote/swagger-php": "~2.0"}}, {"version": "3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "9640c48ee0335a2d286c1c8b0e6cb0b15e768572"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/9640c48ee0335a2d286c1c8b0e6cb0b15e768572", "type": "zip", "shasum": "", "reference": "9640c48ee0335a2d286c1c8b0e6cb0b15e768572"}, "time": "2016-06-30T05:37:37+00:00"}, {"version": "3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "8d20ab00254b61164b8402f4d19dcfa730a23440"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/8d20ab00254b61164b8402f4d19dcfa730a23440", "type": "zip", "shasum": "", "reference": "8d20ab00254b61164b8402f4d19dcfa730a23440"}, "time": "2016-06-27T11:32:27+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "3df6274572eaf73fbe251637780210c3c5af3818"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/3df6274572eaf73fbe251637780210c3c5af3818", "type": "zip", "shasum": "", "reference": "3df6274572eaf73fbe251637780210c3c5af3818"}, "time": "2016-06-10T12:34:09+00:00", "require": {"php": ">=5.5.9", "laravel/framework": "5.2.*", "zircote/swagger-php": "~2.0"}}, {"version": "3.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "7f89c4a6ed475da420f6a65f4df22baf92fa32d7"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/7f89c4a6ed475da420f6a65f4df22baf92fa32d7", "type": "zip", "shasum": "", "reference": "7f89c4a6ed475da420f6a65f4df22baf92fa32d7"}, "time": "2016-04-19T08:28:11+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "64b715acbff127e70a65bae0f10290558daa4291"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/64b715acbff127e70a65bae0f10290558daa4291", "type": "zip", "shasum": "", "reference": "64b715acbff127e70a65bae0f10290558daa4291"}, "time": "2016-04-04T12:57:38+00:00"}, {"version": "3.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "84de9a6ac7a8bda6bd86ed6e96a792d998131e46"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/84de9a6ac7a8bda6bd86ed6e96a792d998131e46", "type": "zip", "shasum": "", "reference": "84de9a6ac7a8bda6bd86ed6e96a792d998131e46"}, "time": "2016-02-23T08:54:00+00:00"}, {"keywords": ["laravel", "swagger"], "version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "1a5416e142b04b80d2263a85625516b08ce315d5"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/1a5416e142b04b80d2263a85625516b08ce315d5", "type": "zip", "shasum": "", "reference": "1a5416e142b04b80d2263a85625516b08ce315d5"}, "time": "2015-09-29T09:35:41+00:00", "autoload": {"psr-4": {"Darkaonline\\L5Swagger\\": "src"}}, "require": {"php": ">=5.4.0", "zircote/swagger-php": "~2.0"}, "require-dev": "__unset"}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "a135363845241d2219cf5d5768e318a9a0fa1044"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/a135363845241d2219cf5d5768e318a9a0fa1044", "type": "zip", "shasum": "", "reference": "a135363845241d2219cf5d5768e318a9a0fa1044"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/2.0"}, "time": "2015-09-29T05:26:47+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "1a28e9d52298c4dc2b8bedaa4ea1f7d6fb8faf46"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/1a28e9d52298c4dc2b8bedaa4ea1f7d6fb8faf46", "type": "zip", "shasum": "", "reference": "1a28e9d52298c4dc2b8bedaa4ea1f7d6fb8faf46"}, "time": "2015-09-28T11:38:26+00:00"}, {"version": "2.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "525cb216f26a1aa995605f3c81bbf7c38e290954"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/525cb216f26a1aa995605f3c81bbf7c38e290954", "type": "zip", "shasum": "", "reference": "525cb216f26a1aa995605f3c81bbf7c38e290954"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2015-08-04T04:44:35+00:00", "require": {"php": ">=5.4.0", "zircote/swagger-php": "2.*@dev"}}, {"version": "v2.0", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "ec08504e83ce93179d15042799d433da7d12374e"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/ec08504e83ce93179d15042799d433da7d12374e", "type": "zip", "shasum": "", "reference": "ec08504e83ce93179d15042799d433da7d12374e"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/2.0"}, "time": "2015-05-05T06:30:46+00:00"}, {"version": "v0.2", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "96e30af65e99d425c5150a20fd204074c3d1a9d9"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/96e30af65e99d425c5150a20fd204074c3d1a9d9", "type": "zip", "shasum": "", "reference": "96e30af65e99d425c5150a20fd204074c3d1a9d9"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/1.0"}, "time": "2015-05-05T06:29:45+00:00", "require": {"php": ">=5.4.0", "zircote/swagger-php": "1.*@dev"}}, {"version": "v0.1", "version_normalized": "*******", "source": {"url": "https://github.com/DarkaOnLine/L5-Swagger.git", "type": "git", "reference": "de0bc194dcca85d42d25893f715bc97545905a14"}, "dist": {"url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/de0bc194dcca85d42d25893f715bc97545905a14", "type": "zip", "shasum": "", "reference": "de0bc194dcca85d42d25893f715bc97545905a14"}, "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/master"}, "time": "2015-03-23T12:26:30+00:00", "require": {"php": ">=5.4.0", "zircote/swagger-php": "*"}}]}, "security-advisories": [], "last-modified": "Fri, 28 Feb 2025 06:25:43 GMT"}