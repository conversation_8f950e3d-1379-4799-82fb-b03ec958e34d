{"minified": "composer/2.0", "packages": {"symfony/uid": [{"name": "symfony/uid", "description": "Provides an object-oriented API to generate and represent UIDs", "keywords": ["uuid", "UID", "ulid"], "homepage": "https://symfony.com", "version": "v7.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "a69f69f3159b852651a6bf45a9fdd149520525bb"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/a69f69f3159b852651a6bf45a9fdd149520525bb", "type": "zip", "shasum": "", "reference": "a69f69f3159b852651a6bf45a9fdd149520525bb"}, "type": "library", "support": {"source": "https://github.com/symfony/uid/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^6.4|^7.0"}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "7beeb2b885cd584cd01e126c5777206ae4c3c6a3"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/7beeb2b885cd584cd01e126c5777206ae4c3c6a3", "type": "zip", "shasum": "", "reference": "7beeb2b885cd584cd01e126c5777206ae4c3c6a3"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.3.0"}, "time": "2025-05-24T14:28:13+00:00"}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "b73f9c531bfb8ff181cb11496eaaa6b890c79ab4"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/b73f9c531bfb8ff181cb11496eaaa6b890c79ab4", "type": "zip", "shasum": "", "reference": "b73f9c531bfb8ff181cb11496eaaa6b890c79ab4"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.3.0-BETA1"}, "time": "2025-04-16T14:01:08+00:00"}, {"version": "v7.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "41aced5ddb593c9c6c81ac9828320448caa02444"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/41aced5ddb593c9c6c81ac9828320448caa02444", "type": "zip", "shasum": "", "reference": "41aced5ddb593c9c6c81ac9828320448caa02444"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.2.8"}, "time": "2025-06-27T19:53:16+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "2d294d0c48df244c71c105a169d0190bfb080426"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/2d294d0c48df244c71c105a169d0190bfb080426", "type": "zip", "shasum": "", "reference": "2d294d0c48df244c71c105a169d0190bfb080426"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.2.0"}, "time": "2024-09-25T14:21:43+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v7.2.0-BETA1"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "65befb3bb2d503bbffbd08c815aa38b472999917"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/65befb3bb2d503bbffbd08c815aa38b472999917", "type": "zip", "shasum": "", "reference": "65befb3bb2d503bbffbd08c815aa38b472999917"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "8c7bb8acb933964055215d89f9a9871df0239317"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/8c7bb8acb933964055215d89f9a9871df0239317", "type": "zip", "shasum": "", "reference": "8c7bb8acb933964055215d89f9a9871df0239317"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.1.5"}, "time": "2024-09-17T09:16:35+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "82177535395109075cdb45a70533aa3d7a521cdf"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/82177535395109075cdb45a70533aa3d7a521cdf", "type": "zip", "shasum": "", "reference": "82177535395109075cdb45a70533aa3d7a521cdf"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.1.4"}, "time": "2024-08-12T09:59:40+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "bb59febeecc81528ff672fad5dab7f06db8c8277"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/bb59febeecc81528ff672fad5dab7f06db8c8277", "type": "zip", "shasum": "", "reference": "bb59febeecc81528ff672fad5dab7f06db8c8277"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "3bbcb15f311b86f72486826ade080d8013231f96"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/3bbcb15f311b86f72486826ade080d8013231f96", "type": "zip", "shasum": "", "reference": "3bbcb15f311b86f72486826ade080d8013231f96"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.1.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v7.1.0-BETA1"}}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "82d01607b09ba79f38efe4c213dbf528ff0beeed"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/82d01607b09ba79f38efe4c213dbf528ff0beeed", "type": "zip", "shasum": "", "reference": "82d01607b09ba79f38efe4c213dbf528ff0beeed"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "4f3a5d181999e25918586c8369de09e7814e7be2"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/4f3a5d181999e25918586c8369de09e7814e7be2", "type": "zip", "shasum": "", "reference": "4f3a5d181999e25918586c8369de09e7814e7be2"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "87cedaf3fabd7b733859d4d77aa4ca598259054b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/87cedaf3fabd7b733859d4d77aa4ca598259054b", "type": "zip", "shasum": "", "reference": "87cedaf3fabd7b733859d4d77aa4ca598259054b"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "9472fe6a4a2adcc9150106ebb9fde328828d312f"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/9472fe6a4a2adcc9150106ebb9fde328828d312f", "type": "zip", "shasum": "", "reference": "9472fe6a4a2adcc9150106ebb9fde328828d312f"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.0.0"}, "time": "2023-10-31T08:22:02+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/uid/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "3949585bdc9b808214e7bb63033f2c0b1769dc05"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/3949585bdc9b808214e7bb63033f2c0b1769dc05", "type": "zip", "shasum": "", "reference": "3949585bdc9b808214e7bb63033f2c0b1769dc05"}, "support": {"source": "https://github.com/symfony/uid/tree/v7.0.0-BETA1"}, "time": "2023-08-04T11:30:50+00:00"}, {"version": "v6.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "9c8592da78d7ee6af52011eef593350d87e814c0"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/9c8592da78d7ee6af52011eef593350d87e814c0", "type": "zip", "shasum": "", "reference": "9c8592da78d7ee6af52011eef593350d87e814c0"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.23"}, "time": "2025-06-26T08:06:12+00:00", "require": {"php": ">=8.1", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "18eb207f0436a993fffbdd811b5b8fa35fa5e007"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/18eb207f0436a993fffbdd811b5b8fa35fa5e007", "type": "zip", "shasum": "", "reference": "18eb207f0436a993fffbdd811b5b8fa35fa5e007"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "2f16054e0a9b194b8ca581d4a64eee3f7d4a9d4d"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/2f16054e0a9b194b8ca581d4a64eee3f7d4a9d4d", "type": "zip", "shasum": "", "reference": "2f16054e0a9b194b8ca581d4a64eee3f7d4a9d4d"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.12"}, "time": "2024-09-20T08:32:26+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "6a0394ad707de386547223948fac1e0f2805bc0b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/6a0394ad707de386547223948fac1e0f2805bc0b", "type": "zip", "shasum": "", "reference": "6a0394ad707de386547223948fac1e0f2805bc0b"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.11"}, "time": "2024-08-12T09:55:28+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "35904eca37a84bb764c560cbfcac9f0ac2bcdbdf"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/35904eca37a84bb764c560cbfcac9f0ac2bcdbdf", "type": "zip", "shasum": "", "reference": "35904eca37a84bb764c560cbfcac9f0ac2bcdbdf"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "a66efcb71d8bc3a207d9d78e0bd67f3321510355"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/a66efcb71d8bc3a207d9d78e0bd67f3321510355", "type": "zip", "shasum": "", "reference": "a66efcb71d8bc3a207d9d78e0bd67f3321510355"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "1d31267211cc3a2fff32bcfc7c1818dac41b6fc0"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/1d31267211cc3a2fff32bcfc7c1818dac41b6fc0", "type": "zip", "shasum": "", "reference": "1d31267211cc3a2fff32bcfc7c1818dac41b6fc0"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "8092dd1b1a41372110d06374f99ee62f7f0b9a92"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/8092dd1b1a41372110d06374f99ee62f7f0b9a92", "type": "zip", "shasum": "", "reference": "8092dd1b1a41372110d06374f99ee62f7f0b9a92"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.0"}, "time": "2023-10-31T08:18:17+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/uid/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "6a8d0932c3bd620e8ecd28fc72be9262bbb3af8a"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/6a8d0932c3bd620e8ecd28fc72be9262bbb3af8a", "type": "zip", "shasum": "", "reference": "6a8d0932c3bd620e8ecd28fc72be9262bbb3af8a"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.4.0-BETA1"}, "time": "2023-08-03T08:48:52+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "49d02f0c96c79f9cbfdb0e88ea2be0654b1c2da4"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/49d02f0c96c79f9cbfdb0e88ea2be0654b1c2da4", "type": "zip", "shasum": "", "reference": "49d02f0c96c79f9cbfdb0e88ea2be0654b1c2da4"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.3.12"}, "time": "2024-01-23T14:35:58+00:00", "require-dev": {"symfony/console": "^5.4|^6.0"}}, {"version": "v6.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "819fa5ac210fb7ddda4752b91a82f50be7493dd9"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/819fa5ac210fb7ddda4752b91a82f50be7493dd9", "type": "zip", "shasum": "", "reference": "819fa5ac210fb7ddda4752b91a82f50be7493dd9"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.3.8"}, "time": "2023-10-31T08:07:48+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "01b0f20b1351d997711c56f1638f7a8c3061e384"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/01b0f20b1351d997711c56f1638f7a8c3061e384", "type": "zip", "shasum": "", "reference": "01b0f20b1351d997711c56f1638f7a8c3061e384"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.3.0"}, "time": "2023-04-08T07:25:02+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v6.3.0-BETA1"}}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "d30c72a63897cfa043e1de4d4dd2ffa9ecefcdc0"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/d30c72a63897cfa043e1de4d4dd2ffa9ecefcdc0", "type": "zip", "shasum": "", "reference": "d30c72a63897cfa043e1de4d4dd2ffa9ecefcdc0"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.2.7"}, "time": "2023-02-14T08:44:56+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "8ace895bded57d6496638c9b2d3b788e05b7395b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/8ace895bded57d6496638c9b2d3b788e05b7395b", "type": "zip", "shasum": "", "reference": "8ace895bded57d6496638c9b2d3b788e05b7395b"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.2.5"}, "time": "2023-01-01T08:38:09+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "4f9f537e57261519808a7ce1d941490736522bbc"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/4f9f537e57261519808a7ce1d941490736522bbc", "type": "zip", "shasum": "", "reference": "4f9f537e57261519808a7ce1d941490736522bbc"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.2.0"}, "time": "2022-10-09T08:55:40+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v6.2.0-BETA1"}}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "7c53913df24517eb5e0fab4caf294e84fcecc277"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/7c53913df24517eb5e0fab4caf294e84fcecc277", "type": "zip", "shasum": "", "reference": "7c53913df24517eb5e0fab4caf294e84fcecc277"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.1.11"}, "time": "2023-01-01T08:36:55+00:00"}, {"version": "v6.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "e03519f7b1ce1d3c0b74f751892bb41d549a2d98"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/e03519f7b1ce1d3c0b74f751892bb41d549a2d98", "type": "zip", "shasum": "", "reference": "e03519f7b1ce1d3c0b74f751892bb41d549a2d98"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.1.5"}, "time": "2022-09-09T09:34:27+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "ea2ccf0fdb88c83e626105b68e5bab5c132d812b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/ea2ccf0fdb88c83e626105b68e5bab5c132d812b", "type": "zip", "shasum": "", "reference": "ea2ccf0fdb88c83e626105b68e5bab5c132d812b"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.1.3"}, "time": "2022-07-20T13:46:29+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "5d1bba611fcd5d7ccc6435fd4ea8580f9ed75406"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/5d1bba611fcd5d7ccc6435fd4ea8580f9ed75406", "type": "zip", "shasum": "", "reference": "5d1bba611fcd5d7ccc6435fd4ea8580f9ed75406"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.1.0"}, "time": "2022-04-15T14:25:02+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/uid/tree/v6.1.0-BETA2"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "118ddfe0051cd63fd0e23dc0d8a447655f64cc0a"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/118ddfe0051cd63fd0e23dc0d8a447655f64cc0a", "type": "zip", "shasum": "", "reference": "118ddfe0051cd63fd0e23dc0d8a447655f64cc0a"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.1.0-BETA1"}, "time": "2022-04-01T07:15:35+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "6499e28b0ac9f2aa3151e11845bdb5cd21e6bb9d"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/6499e28b0ac9f2aa3151e11845bdb5cd21e6bb9d", "type": "zip", "shasum": "", "reference": "6499e28b0ac9f2aa3151e11845bdb5cd21e6bb9d"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.19"}, "time": "2023-01-01T08:36:10+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-uuid": "^1.15"}}, {"version": "v6.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "db426b27173f5e2d8b960dd10fa8ce19ea9ca5f3"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/db426b27173f5e2d8b960dd10fa8ce19ea9ca5f3", "type": "zip", "shasum": "", "reference": "db426b27173f5e2d8b960dd10fa8ce19ea9ca5f3"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.13"}, "time": "2022-09-09T09:33:56+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "7ae9cb6ad0728f9a425499112929575c0b2cc09f"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/7ae9cb6ad0728f9a425499112929575c0b2cc09f", "type": "zip", "shasum": "", "reference": "7ae9cb6ad0728f9a425499112929575c0b2cc09f"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.11"}, "time": "2022-07-20T13:45:53+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "bc1f80c2f512430b9b322df2b71983742dd4f8e7"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/bc1f80c2f512430b9b322df2b71983742dd4f8e7", "type": "zip", "shasum": "", "reference": "bc1f80c2f512430b9b322df2b71983742dd4f8e7"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.3"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "6750d730b5d1c002b366ec40ac811317d142d1f3"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/6750d730b5d1c002b366ec40ac811317d142d1f3", "type": "zip", "shasum": "", "reference": "6750d730b5d1c002b366ec40ac811317d142d1f3"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.2"}, "time": "2021-12-16T22:13:01+00:00"}, {"keywords": ["uuid", "UID"], "version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "6ef7c361b84f8ae666843279d08b2b8ce8006033"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/6ef7c361b84f8ae666843279d08b2b8ce8006033", "type": "zip", "shasum": "", "reference": "6ef7c361b84f8ae666843279d08b2b8ce8006033"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "b071f69b8fd52e0b0fb7d51267a1fb57e8fd32c8"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/b071f69b8fd52e0b0fb7d51267a1fb57e8fd32c8", "type": "zip", "shasum": "", "reference": "b071f69b8fd52e0b0fb7d51267a1fb57e8fd32c8"}, "support": {"source": "https://github.com/symfony/uid/tree/v6.0.0"}, "time": "2021-11-02T20:19:52+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v6.0.0-BETA1"}}, {"keywords": ["uuid", "UID", "ulid"], "version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "512de7894f93ad63a7d5e33f590a83e054f571bc"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/512de7894f93ad63a7d5e33f590a83e054f571bc", "type": "zip", "shasum": "", "reference": "512de7894f93ad63a7d5e33f590a83e054f571bc"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^4.4|^5.0|^6.0"}}, {"version": "v5.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "b36c8947ee835916eac8c341e124c3de19ff43ca"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/b36c8947ee835916eac8c341e124c3de19ff43ca", "type": "zip", "shasum": "", "reference": "b36c8947ee835916eac8c341e124c3de19ff43ca"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.44"}, "time": "2024-09-11T16:39:40+00:00"}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "886c1742a710ceee6e2306169981db6b643a6dd4"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/886c1742a710ceee6e2306169981db6b643a6dd4", "type": "zip", "shasum": "", "reference": "886c1742a710ceee6e2306169981db6b643a6dd4"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.43"}, "time": "2024-07-31T15:51:49+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "3e85f7b4d7e04313b88232cd5c27e5ff2ae93218"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/3e85f7b4d7e04313b88232cd5c27e5ff2ae93218", "type": "zip", "shasum": "", "reference": "3e85f7b4d7e04313b88232cd5c27e5ff2ae93218"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "a63a5335262c5dd49763a12e76e1b5b4ec7224b6"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/a63a5335262c5dd49763a12e76e1b5b4ec7224b6", "type": "zip", "shasum": "", "reference": "a63a5335262c5dd49763a12e76e1b5b4ec7224b6"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "c280434b875236cd2b1bed1505aecc6141db73e8"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/c280434b875236cd2b1bed1505aecc6141db73e8", "type": "zip", "shasum": "", "reference": "c280434b875236cd2b1bed1505aecc6141db73e8"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.35"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v5.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "d76766c457aacff7acea4482c207231aea93362c"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/d76766c457aacff7acea4482c207231aea93362c", "type": "zip", "shasum": "", "reference": "d76766c457aacff7acea4482c207231aea93362c"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.31"}, "time": "2023-10-31T07:58:33+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "a30744506976aafc807ccb0d4f95865c0a690d02"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/a30744506976aafc807ccb0d4f95865c0a690d02", "type": "zip", "shasum": "", "reference": "a30744506976aafc807ccb0d4f95865c0a690d02"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.21"}, "time": "2023-02-14T08:03:56+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "962c08ab8e0621e966ff1a19d398308b34f24aa2"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/962c08ab8e0621e966ff1a19d398308b34f24aa2", "type": "zip", "shasum": "", "reference": "962c08ab8e0621e966ff1a19d398308b34f24aa2"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.19"}, "time": "2023-01-01T08:32:19+00:00"}, {"version": "v5.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "7e727b75ee099d530d1e93ed0897a0d89c146d16"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/7e727b75ee099d530d1e93ed0897a0d89c146d16", "type": "zip", "shasum": "", "reference": "7e727b75ee099d530d1e93ed0897a0d89c146d16"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.13"}, "time": "2022-09-09T09:04:10+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "2120eba9abf35d30db43ee2f9f2b3723cf3a4479"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/2120eba9abf35d30db43ee2f9f2b3723cf3a4479", "type": "zip", "shasum": "", "reference": "2120eba9abf35d30db43ee2f9f2b3723cf3a4479"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.11"}, "time": "2022-07-20T13:00:38+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "2b52d90f0a913a1f770e0b09be3e9baf298b4872"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/2b52d90f0a913a1f770e0b09be3e9baf298b4872", "type": "zip", "shasum": "", "reference": "2b52d90f0a913a1f770e0b09be3e9baf298b4872"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.3"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "89b2e717aa45a57cc0dbe8bff57b9d15b919c67b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/89b2e717aa45a57cc0dbe8bff57b9d15b919c67b", "type": "zip", "shasum": "", "reference": "89b2e717aa45a57cc0dbe8bff57b9d15b919c67b"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.2"}, "time": "2021-12-16T21:52:00+00:00"}, {"keywords": ["uuid", "UID"], "version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "8a7214460dcf034920583abf0018722f463a3aac"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/8a7214460dcf034920583abf0018722f463a3aac", "type": "zip", "shasum": "", "reference": "8a7214460dcf034920583abf0018722f463a3aac"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.4.0"}, "time": "2021-11-02T13:48:32+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v5.4.0-BETA1"}}, {"keywords": ["uuid", "UID", "ulid"], "version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "8aa88ed0c5002ade841d2c91b616824b14d0ab7d"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/8aa88ed0c5002ade841d2c91b616824b14d0ab7d", "type": "zip", "shasum": "", "reference": "8aa88ed0c5002ade841d2c91b616824b14d0ab7d"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00", "require-dev": {"symfony/console": "^4.4|^5.0"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "915b11b94a6a14627234f89696370d2db3e1b43b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/915b11b94a6a14627234f89696370d2db3e1b43b", "type": "zip", "shasum": "", "reference": "915b11b94a6a14627234f89696370d2db3e1b43b"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.13"}, "time": "2021-12-16T21:47:07+00:00"}, {"keywords": ["uuid", "UID"], "version": "v5.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "183336998e6b28c37ebf04ee18e6359dfb22084d"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/183336998e6b28c37ebf04ee18e6359dfb22084d", "type": "zip", "shasum": "", "reference": "183336998e6b28c37ebf04ee18e6359dfb22084d"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.10"}, "time": "2021-10-15T16:00:52+00:00"}, {"version": "v5.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "45853bbc72f2b91c32e707afe7f896fddb3ee8e9"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/45853bbc72f2b91c32e707afe7f896fddb3ee8e9", "type": "zip", "shasum": "", "reference": "45853bbc72f2b91c32e707afe7f896fddb3ee8e9"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.3"}, "time": "2021-06-24T08:13:00+00:00"}, {"version": "v5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "7f7703a1d5e106b3619d556e8313a575a47ac3fa"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/7f7703a1d5e106b3619d556e8313a575a47ac3fa", "type": "zip", "shasum": "", "reference": "7f7703a1d5e106b3619d556e8313a575a47ac3fa"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.2"}, "time": "2021-06-17T12:52:32+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "d84fdeef94c4de3f242c78c5536eaa260203114c"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/d84fdeef94c4de3f242c78c5536eaa260203114c", "type": "zip", "shasum": "", "reference": "d84fdeef94c4de3f242c78c5536eaa260203114c"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "8311a3f6e14c21960e7955452fe52a462d58ad2b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/8311a3f6e14c21960e7955452fe52a462d58ad2b", "type": "zip", "shasum": "", "reference": "8311a3f6e14c21960e7955452fe52a462d58ad2b"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.3.0-RC1"}, "time": "2021-03-23T12:33:35+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/uid/tree/v5.3.0-BETA1"}}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "3604ffeb285e2ad213113aadc891338dfa7e9ff7"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/3604ffeb285e2ad213113aadc891338dfa7e9ff7", "type": "zip", "shasum": "", "reference": "3604ffeb285e2ad213113aadc891338dfa7e9ff7"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.11"}, "time": "2021-06-23T14:41:09+00:00", "require-dev": "__unset"}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "9e052369bf8292c0f536b457312c2d3cd8e0b850"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/9e052369bf8292c0f536b457312c2d3cd8e0b850", "type": "zip", "shasum": "", "reference": "9e052369bf8292c0f536b457312c2d3cd8e0b850"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.10"}, "time": "2021-05-26T17:40:38+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "47d4347b762f0bab9b4ec02112ddfaaa6d79481b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/47d4347b762f0bab9b4ec02112ddfaaa6d79481b", "type": "zip", "shasum": "", "reference": "47d4347b762f0bab9b4ec02112ddfaaa6d79481b"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.6"}, "time": "2021-03-21T16:15:38+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "959f69a8c0d68a37311eeabd630a0ef0979bc1d0"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/959f69a8c0d68a37311eeabd630a0ef0979bc1d0", "type": "zip", "shasum": "", "reference": "959f69a8c0d68a37311eeabd630a0ef0979bc1d0"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.4"}, "time": "2021-01-27T20:50:00+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/uid/tree/v5.2.3"}}, {"version": "v5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "cce30cee5d8e4a2d6d49ca6785bb70d29043d208"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/cce30cee5d8e4a2d6d49ca6785bb70d29043d208", "type": "zip", "shasum": "", "reference": "cce30cee5d8e4a2d6d49ca6785bb70d29043d208"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.2"}, "time": "2021-01-25T13:54:05+00:00"}, {"description": "Symfony Uid component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "7085124d58b662d3fdfb1f7d2dde6c5659656aa4"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/7085124d58b662d3fdfb1f7d2dde6c5659656aa4", "type": "zip", "shasum": "", "reference": "7085124d58b662d3fdfb1f7d2dde6c5659656aa4"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.1"}, "time": "2020-12-15T09:12:47+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "a74e0dd6b21721e7f98703e1386273d65a1fc8e3"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/a74e0dd6b21721e7f98703e1386273d65a1fc8e3", "type": "zip", "shasum": "", "reference": "a74e0dd6b21721e7f98703e1386273d65a1fc8e3"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.0"}, "time": "2020-10-24T12:08:07+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/uid/tree/v5.2.0-RC2"}}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/uid/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/uid/tree/v5.2.0-BETA3"}}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "b0bbbd0e695315a181250083e5c52e2abb5fd0e2"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/b0bbbd0e695315a181250083e5c52e2abb5fd0e2", "type": "zip", "shasum": "", "reference": "b0bbbd0e695315a181250083e5c52e2abb5fd0e2"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "0ce4a0e16ca064644a45df1d132f3a71d58a300b"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/0ce4a0e16ca064644a45df1d132f3a71d58a300b", "type": "zip", "shasum": "", "reference": "0ce4a0e16ca064644a45df1d132f3a71d58a300b"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.2.0-BETA1"}, "time": "2020-09-29T06:01:16+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Provides an object-oriented API to generate and represent UIDs", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "c679017d2c9cf5fffdc6796059c6d85bff63f016"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/c679017d2c9cf5fffdc6796059c6d85bff63f016", "type": "zip", "shasum": "", "reference": "c679017d2c9cf5fffdc6796059c6d85bff63f016"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.1.11"}, "time": "2021-01-25T11:59:00+00:00", "extra": "__unset"}, {"description": "Symfony Uid component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "f5d629b4736481aebe8886766534b6008ab0d250"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/f5d629b4736481aebe8886766534b6008ab0d250", "type": "zip", "shasum": "", "reference": "f5d629b4736481aebe8886766534b6008ab0d250"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.1.10"}, "time": "2020-12-14T23:03:24+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "d6e033ed0eca26277084f76287c3322dc1d825c2"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/d6e033ed0eca26277084f76287c3322dc1d825c2", "type": "zip", "shasum": "", "reference": "d6e033ed0eca26277084f76287c3322dc1d825c2"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.1.9"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/uid/tree/v5.1.8"}}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "a0dc1758c4fd55f27c68861f1b74d654dce59f07"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/a0dc1758c4fd55f27c68861f1b74d654dce59f07", "type": "zip", "shasum": "", "reference": "a0dc1758c4fd55f27c68861f1b74d654dce59f07"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.1.7"}, "time": "2020-09-02T16:23:27+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/uid/tree/5.1"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "069ab0502cd0be25e343f646d7f62fcbdeecff02"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/069ab0502cd0be25e343f646d7f62fcbdeecff02", "type": "zip", "shasum": "", "reference": "069ab0502cd0be25e343f646d7f62fcbdeecff02"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.1.5"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/uid/tree/v5.1.4"}}, {"version": "v5.1.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/uid/tree/5.1"}}, {"version": "v5.1.2", "version_normalized": "*******"}, {"version": "v5.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/uid/tree/v5.1.0"}}, {"version": "v5.1.0", "version_normalized": "*******"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/uid/tree/5.1"}}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/uid.git", "type": "git", "reference": "d854aaa945f6277673ef54a6b65ceb2ac6736a57"}, "dist": {"url": "https://api.github.com/repos/symfony/uid/zipball/d854aaa945f6277673ef54a6b65ceb2ac6736a57", "type": "zip", "shasum": "", "reference": "d854aaa945f6277673ef54a6b65ceb2ac6736a57"}, "support": {"source": "https://github.com/symfony/uid/tree/v5.1.0-BETA1"}, "time": "2020-03-31T18:10:27+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-uuid": "^1.15"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1"}]}, "security-advisories": [], "last-modified": "Sat, 28 Jun 2025 08:28:24 GMT"}