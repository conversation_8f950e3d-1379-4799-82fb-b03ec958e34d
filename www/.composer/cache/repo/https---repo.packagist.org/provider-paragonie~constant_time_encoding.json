{"minified": "composer/2.0", "packages": {"paragonie/constant_time_encoding": [{"name": "paragonie/constant_time_encoding", "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base64", "base32", "encoding", "rfc4648", "hex", "base16", "bin2hex", "hex2bin", "base64_encode", "base64_decode", "base32_encode", "base32_decode"], "homepage": "", "version": "v3.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "type": "zip", "shasum": "", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "type": "library", "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "funding": [], "time": "2024-05-08T12:36:18+00:00", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/52a0d99e69f56b9ec27ace92ba56897fe6993105", "type": "zip", "shasum": "", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "time": "2024-05-08T12:18:48+00:00", "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58c3f47f650c94ec05a151692652a868995d2938", "type": "zip", "shasum": "", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "time": "2022-06-14T06:56:20+00:00"}, {"version": "v2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "c1b1d82d109846ba58a4664dc5480c69ad2fc097"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/c1b1d82d109846ba58a4664dc5480c69ad2fc097", "type": "zip", "shasum": "", "reference": "c1b1d82d109846ba58a4664dc5480c69ad2fc097"}, "time": "2022-06-13T05:29:16+00:00"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "d6e1d5d0fb2458dfdd7018ec2f74be120353a3b9"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/d6e1d5d0fb2458dfdd7018ec2f74be120353a3b9", "type": "zip", "shasum": "", "reference": "d6e1d5d0fb2458dfdd7018ec2f74be120353a3b9"}, "time": "2022-06-11T00:43:46+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "3f3bf06406244a94aeffd5818ba05b41a1754ae5"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/3f3bf06406244a94aeffd5818ba05b41a1754ae5", "type": "zip", "shasum": "", "reference": "3f3bf06406244a94aeffd5818ba05b41a1754ae5"}, "time": "2022-06-10T07:38:28+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "9229e15f2e6ba772f0c55dd6986c563b937170a8"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/9229e15f2e6ba772f0c55dd6986c563b937170a8", "type": "zip", "shasum": "", "reference": "9229e15f2e6ba772f0c55dd6986c563b937170a8"}, "time": "2022-01-17T05:32:27+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "type": "zip", "shasum": "", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c"}, "time": "2020-12-06T15:14:20+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "47a1cedd2e4d52688eb8c96469c05ebc8fd28fa2"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/47a1cedd2e4d52688eb8c96469c05ebc8fd28fa2", "type": "zip", "shasum": "", "reference": "47a1cedd2e4d52688eb8c96469c05ebc8fd28fa2"}, "time": "2019-11-06T19:20:29+00:00", "require-dev": {"phpunit/phpunit": "^6|^7", "vimeo/psalm": "^1|^2|^3"}, "funding": "__unset"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "55af0dc01992b4d0da7f6372e2eac097bbbaffdb"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/55af0dc01992b4d0da7f6372e2eac097bbbaffdb", "type": "zip", "shasum": "", "reference": "55af0dc01992b4d0da7f6372e2eac097bbbaffdb"}, "time": "2019-01-03T20:26:31+00:00", "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "^6|^7", "vimeo/psalm": "^1|^2"}}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "eccf915f45f911bfb189d1d1638d940ec6ee6e33"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/eccf915f45f911bfb189d1d1638d940ec6ee6e33", "type": "zip", "shasum": "", "reference": "eccf915f45f911bfb189d1d1638d940ec6ee6e33"}, "time": "2018-03-10T19:47:49+00:00", "require-dev": {"phpunit/phpunit": "^6|^7", "vimeo/psalm": "^1"}}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "7c74c5d08761ead7b5e89d07c854bc28eb0b2186"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/7c74c5d08761ead7b5e89d07c854bc28eb0b2186", "type": "zip", "shasum": "", "reference": "7c74c5d08761ead7b5e89d07c854bc28eb0b2186"}, "time": "2018-01-23T00:54:57+00:00", "require-dev": {"phpunit/phpunit": "^6", "vimeo/psalm": "^0.3|^1"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "9e7d88e6e4015c2f06a3fa22f06e1d5faa77e6c4"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/9e7d88e6e4015c2f06a3fa22f06e1d5faa77e6c4", "type": "zip", "shasum": "", "reference": "9e7d88e6e4015c2f06a3fa22f06e1d5faa77e6c4"}, "time": "2017-09-22T14:55:37+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "867933efd663a5825a7f00a5b8738b41a3b08e7d"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/867933efd663a5825a7f00a5b8738b41a3b08e7d", "type": "zip", "shasum": "", "reference": "867933efd663a5825a7f00a5b8738b41a3b08e7d"}, "time": "2017-09-16T21:00:37+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "e085e08c939de49707dbf64315d178d90fbc708d"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/e085e08c939de49707dbf64315d178d90fbc708d", "type": "zip", "shasum": "", "reference": "e085e08c939de49707dbf64315d178d90fbc708d"}, "time": "2016-07-11T20:32:06+00:00", "require-dev": {"phpunit/phpunit": "4.*|5.*"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "802a31079090bcfb8dfc88a5704bef3a761a39ec"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/802a31079090bcfb8dfc88a5704bef3a761a39ec", "type": "zip", "shasum": "", "reference": "802a31079090bcfb8dfc88a5704bef3a761a39ec"}, "time": "2016-06-13T01:06:53+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "4ed9623466a93dbdc74edc91d46860f9e58d4c04"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/4ed9623466a93dbdc74edc91d46860f9e58d4c04", "type": "zip", "shasum": "", "reference": "4ed9623466a93dbdc74edc91d46860f9e58d4c04"}, "time": "2016-05-10T20:45:16+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "58d445b0ef9575aaf1c01ea2cfbe164caf1120c3"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58d445b0ef9575aaf1c01ea2cfbe164caf1120c3", "type": "zip", "shasum": "", "reference": "58d445b0ef9575aaf1c01ea2cfbe164caf1120c3"}, "time": "2016-04-08T17:15:28+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "317718fb438e60151f72b20404f040cb5ae1d494"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/317718fb438e60151f72b20404f040cb5ae1d494", "type": "zip", "shasum": "", "reference": "317718fb438e60151f72b20404f040cb5ae1d494"}, "funding": [], "time": "2022-01-17T05:23:46+00:00", "require": {"php": "^5.3|^7|^8"}, "require-dev": {"phpunit/phpunit": ">= 4", "paragonie/random_compat": "^1.4|^2"}}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "2132f0f293d856026d7d11bd81b9f4a23a1dc1f6"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/2132f0f293d856026d7d11bd81b9f4a23a1dc1f6", "type": "zip", "shasum": "", "reference": "2132f0f293d856026d7d11bd81b9f4a23a1dc1f6"}, "time": "2018-04-30T17:57:16+00:00", "require": {"php": "^5.3|^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "paragonie/random_compat": "^1.4|^2", "vimeo/psalm": "^0.3|^1"}, "funding": "__unset"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "75e54b9bc1d8eddc59bca721cd0f3d263c70503c"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/75e54b9bc1d8eddc59bca721cd0f3d263c70503c", "type": "zip", "shasum": "", "reference": "75e54b9bc1d8eddc59bca721cd0f3d263c70503c"}, "time": "2018-04-29T17:40:22+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "6111a38faf6fdebc14e36652d22036f379ba58d3"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/6111a38faf6fdebc14e36652d22036f379ba58d3", "type": "zip", "shasum": "", "reference": "6111a38faf6fdebc14e36652d22036f379ba58d3"}, "time": "2018-03-10T19:46:06+00:00", "require-dev": {"phpunit/phpunit": "4.*|5.*", "paragonie/random_compat": "^1|^2", "vimeo/psalm": "^1"}}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "d96e63b79a7135a65659ba5b1cb02826172bfedd"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/d96e63b79a7135a65659ba5b1cb02826172bfedd", "type": "zip", "shasum": "", "reference": "d96e63b79a7135a65659ba5b1cb02826172bfedd"}, "time": "2016-06-13T01:00:24+00:00", "require-dev": {"phpunit/phpunit": "4.*|5.*", "paragonie/random_compat": "^1.4|^2.0"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "fdb1e311153233315e0f7699711e3845d81ed00f"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/fdb1e311153233315e0f7699711e3845d81ed00f", "type": "zip", "shasum": "", "reference": "fdb1e311153233315e0f7699711e3845d81ed00f"}, "time": "2016-04-08T16:58:39+00:00"}, {"description": "Constant-time Implementations of RFC 4648 Base-64 Encoding (original by <PERSON><PERSON><PERSON><PERSON>)", "keywords": ["base64", "encoding", "rfc4648"], "version": "v0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "da36633e5529862ebdd661310c77ea520727e04f"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/da36633e5529862ebdd661310c77ea520727e04f", "type": "zip", "shasum": "", "reference": "da36633e5529862ebdd661310c77ea520727e04f"}, "time": "2016-04-08T14:23:59+00:00", "require-dev": {"phpunit/phpunit": "4.*|5.*", "paragonie/random_compat": "^1.2"}}, {"version": "v0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "d1f26a6bc0a888079d84de558daca69ae8811834"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/d1f26a6bc0a888079d84de558daca69ae8811834", "type": "zip", "shasum": "", "reference": "d1f26a6bc0a888079d84de558daca69ae8811834"}, "time": "2016-03-24T20:35:40+00:00"}, {"version": "v0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "4a7477de85518c8a25649e3ba0e44d5268e9d77f"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/4a7477de85518c8a25649e3ba0e44d5268e9d77f", "type": "zip", "shasum": "", "reference": "4a7477de85518c8a25649e3ba0e44d5268e9d77f"}, "time": "2016-03-14T22:31:35+00:00"}, {"version": "v0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "c88e9971dbbce80227bef06e9a6f4c77a8e8ac3c"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/c88e9971dbbce80227bef06e9a6f4c77a8e8ac3c", "type": "zip", "shasum": "", "reference": "c88e9971dbbce80227bef06e9a6f4c77a8e8ac3c"}, "time": "2016-03-13T23:54:45+00:00"}, {"version": "v0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "6709366e81deae25c770e52211e04e2afc88d01c"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/6709366e81deae25c770e52211e04e2afc88d01c", "type": "zip", "shasum": "", "reference": "6709366e81deae25c770e52211e04e2afc88d01c"}, "time": "2016-03-12T22:39:28+00:00", "require": {"php": "^5.5|^7"}}, {"version": "v0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "b185fef0b4b4fc6359920840cdecfc90d0ba31f5"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/b185fef0b4b4fc6359920840cdecfc90d0ba31f5", "type": "zip", "shasum": "", "reference": "b185fef0b4b4fc6359920840cdecfc90d0ba31f5"}, "time": "2016-03-12T19:12:51+00:00", "require-dev": {"phpunit/phpunit": "4.*|5.*", "paragonie/random_compat": "^1.1"}}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "d331798e6cabdce20dddaacd78c364aac4adf694"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/d331798e6cabdce20dddaacd78c364aac4adf694", "type": "zip", "shasum": "", "reference": "d331798e6cabdce20dddaacd78c364aac4adf694"}, "time": "2016-03-12T00:00:34+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/paragonie/constant_time_encoding.git", "type": "git", "reference": "8eb4800a6e5744deb15d18170b105bbdb06a80da"}, "dist": {"url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/8eb4800a6e5744deb15d18170b105bbdb06a80da", "type": "zip", "shasum": "", "reference": "8eb4800a6e5744deb15d18170b105bbdb06a80da"}, "time": "2016-02-03T01:44:37+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 08 May 2024 12:37:04 GMT"}