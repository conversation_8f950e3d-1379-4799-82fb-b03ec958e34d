{"minified": "composer/2.0", "packages": {"amphp/parallel": [{"name": "amphp/parallel", "description": "Parallel processing component for Amp.", "keywords": ["async", "asynchronous", "concurrent", "multi-threading", "multi-processing"], "homepage": "https://github.com/amphp/parallel", "version": "v2.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "5113111de02796a782f5d90767455e7391cca190"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/5113111de02796a782f5d90767455e7391cca190", "type": "zip", "shasum": "", "reference": "5113111de02796a782f5d90767455e7391cca190"}, "type": "library", "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.3.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-12-21T01:56:09+00:00", "autoload": {"files": ["src/Context/functions.php", "src/Context/Internal/functions.php", "src/Ipc/functions.php", "src/Worker/functions.php"], "psr-4": {"Amp\\Parallel\\": "src"}}, "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^1"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.18"}}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "9777db1460d1535bc2a843840684fb1205225b87"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/9777db1460d1535bc2a843840684fb1205225b87", "type": "zip", "shasum": "", "reference": "9777db1460d1535bc2a843840684fb1205225b87"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.3.0"}, "time": "2024-09-14T19:16:14+00:00"}, {"version": "v2.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "73d293f1fc4df1bebc3c4fce1432e82dd7032238"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/73d293f1fc4df1bebc3c4fce1432e82dd7032238", "type": "zip", "shasum": "", "reference": "73d293f1fc4df1bebc3c4fce1432e82dd7032238"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.9"}, "time": "2024-03-24T18:27:44+00:00"}, {"version": "v2.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "efd71b342b64c2e46d904e4eb057ed5ab20f8e2d"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/efd71b342b64c2e46d904e4eb057ed5ab20f8e2d", "type": "zip", "shasum": "", "reference": "efd71b342b64c2e46d904e4eb057ed5ab20f8e2d"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.8"}, "time": "2024-03-19T16:09:34+00:00"}, {"version": "v2.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "ffda869c33c30627b6eb5c25f096882d885681dc"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/ffda869c33c30627b6eb5c25f096882d885681dc", "type": "zip", "shasum": "", "reference": "ffda869c33c30627b6eb5c25f096882d885681dc"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.7"}, "time": "2024-03-16T16:15:46+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^1"}}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "5aeaad20297507cc754859236720501b54306eba"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/5aeaad20297507cc754859236720501b54306eba", "type": "zip", "shasum": "", "reference": "5aeaad20297507cc754859236720501b54306eba"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.6"}, "time": "2024-01-07T18:12:13+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "7b53bad38e5e7091c3c3869e21754df96435f0fe"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/7b53bad38e5e7091c3c3869e21754df96435f0fe", "type": "zip", "shasum": "", "reference": "7b53bad38e5e7091c3c3869e21754df96435f0fe"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.5"}, "time": "2023-12-27T18:53:31+00:00"}, {"version": "v2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "8b8f33a742c2ba63a8bfff664f5836c26f1996ed"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/8b8f33a742c2ba63a8bfff664f5836c26f1996ed", "type": "zip", "shasum": "", "reference": "8b8f33a742c2ba63a8bfff664f5836c26f1996ed"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.4"}, "time": "2023-11-29T04:21:30+00:00", "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.4"}}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "223c47cfa57dc8166d058c072c47fe650d6b5b1d"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/223c47cfa57dc8166d058c072c47fe650d6b5b1d", "type": "zip", "shasum": "", "reference": "223c47cfa57dc8166d058c072c47fe650d6b5b1d"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.3"}, "time": "2023-11-25T00:18:59+00:00"}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "f54bb4090670397544f74e14a1e650bf2cfd853b"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/f54bb4090670397544f74e14a1e650bf2cfd853b", "type": "zip", "shasum": "", "reference": "f54bb4090670397544f74e14a1e650bf2cfd853b"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.2"}, "time": "2023-08-30T17:43:42+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "ba11031b8664134b13c150530ae041a75e631858"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/ba11031b8664134b13c150530ae041a75e631858", "type": "zip", "shasum": "", "reference": "ba11031b8664134b13c150530ae041a75e631858"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.1"}, "time": "2023-05-22T03:33:27+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "37850ff591155c6f5c662173c3fc75682752c918"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/37850ff591155c6f5c662173c3fc75682752c918", "type": "zip", "shasum": "", "reference": "37850ff591155c6f5c662173c3fc75682752c918"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.2.0"}, "time": "2023-05-12T22:42:36+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "409a8f2a091a1eacf239695cb7f37bda4f635e83"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/409a8f2a091a1eacf239695cb7f37bda4f635e83", "type": "zip", "shasum": "", "reference": "409a8f2a091a1eacf239695cb7f37bda4f635e83"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.1.0"}, "time": "2023-03-02T22:01:04+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^1 || ^0.2.1"}}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "d66e3df393b6e5b569a9ae2b5848cadd6936e14c"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/d66e3df393b6e5b569a9ae2b5848cadd6936e14c", "type": "zip", "shasum": "", "reference": "d66e3df393b6e5b569a9ae2b5848cadd6936e14c"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.0.0"}, "time": "2023-02-17T19:28:22+00:00"}, {"version": "v2.0.0-beta.5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "f1139476d212c4b64c849a5ec373d3a161a2d555"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/f1139476d212c4b64c849a5ec373d3a161a2d555", "type": "zip", "shasum": "", "reference": "f1139476d212c4b64c849a5ec373d3a161a2d555"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.0.0-beta.5"}, "time": "2023-02-01T19:01:03+00:00", "autoload": {"files": ["src/Context/functions.php", "src/Ipc/functions.php", "src/Worker/functions.php"], "psr-4": {"Amp\\Parallel\\": "src"}}, "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^1 || ^0.2.1"}}, {"version": "v2.0.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "57e22cfd433ea78ea50b0c35534a0bef4af87d78"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/57e22cfd433ea78ea50b0c35534a0bef4af87d78", "type": "zip", "shasum": "", "reference": "57e22cfd433ea78ea50b0c35534a0bef4af87d78"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.0.0-beta.4"}, "time": "2022-11-07T22:50:59+00:00", "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^4.15"}}, {"version": "v2.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "cd8086a6c0d3cbf4596cb03d632f4562f2435cc6"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/cd8086a6c0d3cbf4596cb03d632f4562f2435cc6", "type": "zip", "shasum": "", "reference": "cd8086a6c0d3cbf4596cb03d632f4562f2435cc6"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.0.0-beta.3"}, "time": "2022-04-03T17:52:08+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^0.2.1"}}, {"version": "v2.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "8eedcb2145ecd78c1d5967a05040ec205d675610"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/8eedcb2145ecd78c1d5967a05040ec205d675610", "type": "zip", "shasum": "", "reference": "8eedcb2145ecd78c1d5967a05040ec205d675610"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.0.0-beta.2"}, "time": "2022-03-28T22:24:09+00:00"}, {"version": "v2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "d491b026e3e4d7cad42be50709f3cf4eebfc5a9d"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/d491b026e3e4d7cad42be50709f3cf4eebfc5a9d", "type": "zip", "shasum": "", "reference": "d491b026e3e4d7cad42be50709f3cf4eebfc5a9d"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.0.0-beta.1"}, "time": "2022-02-08T04:02:58+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^0.2.1"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "dev-master", "psalm/phar": "^4.15"}}, {"version": "v1.4.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "3aac213ba7858566fd83d38ccb85b91b2d652cb0"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/3aac213ba7858566fd83d38ccb85b91b2d652cb0", "type": "zip", "shasum": "", "reference": "3aac213ba7858566fd83d38ccb85b91b2d652cb0"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v1.4.3"}, "time": "2023-03-23T08:04:23+00:00", "autoload": {"files": ["lib/Context/functions.php", "lib/Sync/functions.php", "lib/Worker/functions.php"], "psr-4": {"Amp\\Parallel\\": "lib"}}, "require": {"php": ">=7.1", "amphp/amp": "^2", "amphp/byte-stream": "^1.6.1", "amphp/parser": "^1", "amphp/process": "^1", "amphp/serialization": "^1", "amphp/sync": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^8 || ^7", "amphp/phpunit-util": "^1.1", "amphp/php-cs-fixer-config": "dev-master"}}, {"version": "v1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "75853e1623efa5aa5e65e986ec9a97db573a5267"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/75853e1623efa5aa5e65e986ec9a97db573a5267", "type": "zip", "shasum": "", "reference": "75853e1623efa5aa5e65e986ec9a97db573a5267"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v1.4.2"}, "time": "2022-12-30T00:21:42+00:00"}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "fbc128383c1ffb3823866f71b88d8c4722a25ce9"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/fbc128383c1ffb3823866f71b88d8c4722a25ce9", "type": "zip", "shasum": "", "reference": "fbc128383c1ffb3823866f71b88d8c4722a25ce9"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v1.4.1"}, "time": "2021-10-25T19:16:02+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "2c1039bf7ca137eae4d954b14c09a7535d7d4e1c"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/2c1039bf7ca137eae4d954b14c09a7535d7d4e1c", "type": "zip", "shasum": "", "reference": "2c1039bf7ca137eae4d954b14c09a7535d7d4e1c"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/master"}, "funding": [], "time": "2020-04-27T15:12:37+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "346d3f50939d3533a49ef161a0b9c8cca6eee809"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/346d3f50939d3533a49ef161a0b9c8cca6eee809", "type": "zip", "shasum": "", "reference": "346d3f50939d3533a49ef161a0b9c8cca6eee809"}, "time": "2020-02-23T15:28:43+00:00", "require": {"php": ">=7.1", "amphp/amp": "^2", "amphp/byte-stream": "^1.6.1", "amphp/parser": "^1", "amphp/process": "^1", "amphp/sync": "^1.0.1"}}, {"version": "v1.2.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "53aa422eaf89a539b4df58284f7bdf5219e37f66"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/53aa422eaf89a539b4df58284f7bdf5219e37f66", "type": "zip", "shasum": "", "reference": "53aa422eaf89a539b4df58284f7bdf5219e37f66"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v1.2.0"}, "time": "2019-05-11T17:13:47+00:00", "autoload": {"files": ["lib/Worker/functions.php"], "psr-4": {"Amp\\Parallel\\": "lib"}}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.5", "amphp/parser": "^1", "amphp/process": "^1", "amphp/sync": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^6", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master"}, "funding": "__unset"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "687776dc6933af4c6009ac58f915faf45e8463ce"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/687776dc6933af4c6009ac58f915faf45e8463ce", "type": "zip", "shasum": "", "reference": "687776dc6933af4c6009ac58f915faf45e8463ce"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/master"}, "time": "2019-01-09T21:31:46+00:00", "suggest": {"ext-pthreads": "Required for thread contexts"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "ccf285bdcc38f355a1a113175291e1bfe87205ad"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/ccf285bdcc38f355a1a113175291e1bfe87205ad", "type": "zip", "shasum": "", "reference": "ccf285bdcc38f355a1a113175291e1bfe87205ad"}, "time": "2018-12-30T19:32:25+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "4c3c93e46a0108f04990352b0d084ded2ec2cc17"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/4c3c93e46a0108f04990352b0d084ded2ec2cc17", "type": "zip", "shasum": "", "reference": "4c3c93e46a0108f04990352b0d084ded2ec2cc17"}, "time": "2018-10-27T16:19:01+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "d783e13fa43b528011a1ee9f533dec1ffc9c2a45"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/d783e13fa43b528011a1ee9f533dec1ffc9c2a45", "type": "zip", "shasum": "", "reference": "d783e13fa43b528011a1ee9f533dec1ffc9c2a45"}, "time": "2018-10-27T14:59:10+00:00"}, {"version": "v0.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "732694688461936bec02c0ccf020dfee10c4f7ee"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/732694688461936bec02c0ccf020dfee10c4f7ee", "type": "zip", "shasum": "", "reference": "732694688461936bec02c0ccf020dfee10c4f7ee"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/phar-copy"}, "time": "2018-03-21T14:37:51+00:00", "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.2", "amphp/parser": "^1", "amphp/process": "^0.2 || ^0.3", "amphp/sync": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^6", "amphp/phpunit-util": "^1", "friendsofphp/php-cs-fixer": "^2.3"}}, {"version": "v0.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "ed3662172d2c06f10d40745354928d943ff6eb7c"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/ed3662172d2c06f10d40745354928d943ff6eb7c", "type": "zip", "shasum": "", "reference": "ed3662172d2c06f10d40745354928d943ff6eb7c"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/master"}, "time": "2018-03-14T23:51:13+00:00"}, {"version": "v0.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "11a3e27db536f70984ee3fc988c22a5b2aa300ac"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/11a3e27db536f70984ee3fc988c22a5b2aa300ac", "type": "zip", "shasum": "", "reference": "11a3e27db536f70984ee3fc988c22a5b2aa300ac"}, "time": "2018-01-31T17:31:36+00:00"}, {"version": "v0.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "7ef75bd5dbcf575c302210578fec0148c0340595"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/7ef75bd5dbcf575c302210578fec0148c0340595", "type": "zip", "shasum": "", "reference": "7ef75bd5dbcf575c302210578fec0148c0340595"}, "time": "2018-01-22T23:59:13+00:00"}, {"version": "v0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "0d40e6da980d56de8bbd292668becef6ed45a2a7"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/0d40e6da980d56de8bbd292668becef6ed45a2a7", "type": "zip", "shasum": "", "reference": "0d40e6da980d56de8bbd292668becef6ed45a2a7"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/travis"}, "time": "2017-12-27T18:36:28+00:00"}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "c545be1dc5be296eb57f9cc2f540d53abc04da31"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/c545be1dc5be296eb57f9cc2f540d53abc04da31", "type": "zip", "shasum": "", "reference": "c545be1dc5be296eb57f9cc2f540d53abc04da31"}, "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/master"}, "time": "2017-12-14T05:06:38+00:00"}, {"version": "v0.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "25b9a2a75c4a6ba4815775267822066abab1054a"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/25b9a2a75c4a6ba4815775267822066abab1054a", "type": "zip", "shasum": "", "reference": "25b9a2a75c4a6ba4815775267822066abab1054a"}, "time": "2017-07-18T03:59:56+00:00", "extra": {"branch-alias": {"dev-master": "0.1.x-dev"}}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.1", "amphp/parser": "^1", "amphp/process": "^0.2"}, "suggest": {"ext-pcntl": "Required for fork contexts", "ext-pthreads": "Required for thread contexts", "ext-sysvsem": "Required for fork synchronization", "ext-sysvshm": "Required for fork contexts"}}, {"version": "v0.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "c22d2838c7f8a90d5c692c7c3c480b312e162d9d"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/c22d2838c7f8a90d5c692c7c3c480b312e162d9d", "type": "zip", "shasum": "", "reference": "c22d2838c7f8a90d5c692c7c3c480b312e162d9d"}, "time": "2017-06-29T04:59:24+00:00"}, {"version": "v0.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "8ecfed57bfb258a219ddfa7ebaac453caf735bc4"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/8ecfed57bfb258a219ddfa7ebaac453caf735bc4", "type": "zip", "shasum": "", "reference": "8ecfed57bfb258a219ddfa7ebaac453caf735bc4"}, "time": "2017-06-23T14:44:17+00:00"}, {"version": "v0.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "8c726c67b6ce2504255eb36282115bce9aedf7ce"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/8c726c67b6ce2504255eb36282115bce9aedf7ce", "type": "zip", "shasum": "", "reference": "8c726c67b6ce2504255eb36282115bce9aedf7ce"}, "time": "2017-06-21T16:20:06+00:00", "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1", "amphp/parser": "^1", "amphp/process": "^0.2"}}, {"version": "v0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "29a1d1bb8c1823066ad5eab1915b7be065f9ec02"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/29a1d1bb8c1823066ad5eab1915b7be065f9ec02", "type": "zip", "shasum": "", "reference": "29a1d1bb8c1823066ad5eab1915b7be065f9ec02"}, "time": "2017-06-20T03:14:19+00:00"}, {"version": "v0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "998a255c830bbb6edf70b600d013924eb4d16764"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/998a255c830bbb6edf70b600d013924eb4d16764", "type": "zip", "shasum": "", "reference": "998a255c830bbb6edf70b600d013924eb4d16764"}, "time": "2017-06-19T16:24:39+00:00"}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "4546ef8b1dff8fb291014c046f4839e799582983"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/4546ef8b1dff8fb291014c046f4839e799582983", "type": "zip", "shasum": "", "reference": "4546ef8b1dff8fb291014c046f4839e799582983"}, "time": "2017-06-17T18:46:22+00:00"}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "39664d42d09ebcde06b20b3a25819878c25ee109"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/39664d42d09ebcde06b20b3a25819878c25ee109", "type": "zip", "shasum": "", "reference": "39664d42d09ebcde06b20b3a25819878c25ee109"}, "time": "2017-06-17T17:27:16+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel.git", "type": "git", "reference": "be0d786e140738899bcf9ebcd4679260b33c86a0"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel/zipball/be0d786e140738899bcf9ebcd4679260b33c86a0", "type": "zip", "shasum": "", "reference": "be0d786e140738899bcf9ebcd4679260b33c86a0"}, "time": "2017-06-16T05:04:10+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 21 Dec 2024 01:58:57 GMT"}