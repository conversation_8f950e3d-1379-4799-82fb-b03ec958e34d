{"minified": "composer/2.0", "packages": {"spatie/laravel-activitylog": [{"name": "spatie/laravel-activitylog", "description": "A very simple activity logger to monitor the users of your website or application", "keywords": ["log", "user", "laravel", "activity", "spatie"], "homepage": "https://github.com/spatie/activitylog", "version": "dev-main", "version_normalized": "dev-main", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gummibeer.de", "role": "Developer"}], "source": {"url": "https://github.com/spatie/laravel-activitylog.git", "type": "git", "reference": "821a6dd992cbe5959f569c1b248cc1c509f2248f"}, "dist": {"url": "https://api.github.com/repos/spatie/laravel-activitylog/zipball/821a6dd992cbe5959f569c1b248cc1c509f2248f", "type": "zip", "shasum": "", "reference": "821a6dd992cbe5959f569c1b248cc1c509f2248f"}, "type": "library", "support": {"issues": "https://github.com/spatie/laravel-activitylog/issues", "source": "https://github.com/spatie/laravel-activitylog/tree/main"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2025-06-15T07:00:16+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\Activitylog\\": "src"}}, "extra": {"laravel": {"providers": ["Spatie\\Activitylog\\ActivitylogServiceProvider"]}}, "default-branch": true, "require": {"spatie/laravel-package-tools": "^1.6.3", "php": "^8.1", "illuminate/config": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/database": "^8.69 || ^9.27 || ^10.0 || ^11.0 || ^12.0", "illuminate/support": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0"}, "require-dev": {"ext-json": "*", "orchestra/testbench": "^6.23 || ^7.0 || ^8.0 || ^9.0 || ^10.0", "pestphp/pest": "^1.20 || ^2.0 || ^3.0"}}, {"version": "v3.x-dev", "version_normalized": "3.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/spatie/laravel-activitylog.git", "type": "git", "reference": "e3b0468a858c0ac8a49391ccf18426fbc1ef19a4"}, "dist": {"url": "https://api.github.com/repos/spatie/laravel-activitylog/zipball/e3b0468a858c0ac8a49391ccf18426fbc1ef19a4", "type": "zip", "shasum": "", "reference": "e3b0468a858c0ac8a49391ccf18426fbc1ef19a4"}, "support": {"issues": "https://github.com/spatie/laravel-activitylog/issues", "source": "https://github.com/spatie/laravel-activitylog/tree/v3"}, "time": "2021-07-26T14:24:51+00:00", "require": {"php": "^7.3 || ^8.0", "illuminate/config": "^6.0 || ^7.0 || ^8.0", "illuminate/database": "^6.0 || ^7.0 || ^8.0", "illuminate/support": "^6.0 || ^7.0 || ^8.0"}, "require-dev": {"ext-json": "*", "orchestra/testbench": "^4.0 || ^5.0 || ^6.0", "phpunit/phpunit": "^9.3"}, "default-branch": "__unset"}, {"version": "v2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "source": {"url": "https://github.com/spatie/laravel-activitylog.git", "type": "git", "reference": "9d9033978584d21138af86035957f341666188e7"}, "dist": {"url": "https://api.github.com/repos/spatie/laravel-activitylog/zipball/9d9033978584d21138af86035957f341666188e7", "type": "zip", "shasum": "", "reference": "9d9033978584d21138af86035957f341666188e7"}, "support": {"issues": "https://github.com/spatie/laravel-activitylog/issues", "source": "https://github.com/spatie/laravel-activitylog/tree/v2"}, "time": "2019-06-13T09:25:15+00:00", "require": {"php": "^7.0", "illuminate/config": "~5.5.0|~5.6.0|~5.7.0", "illuminate/database": "~5.5.0|~5.6.0|~5.7.0", "illuminate/support": "~5.5.0|~5.6.0|~5.7.0", "spatie/string": "^2.1"}, "require-dev": {"phpunit/phpunit": "^6.2|^7.0", "orchestra/testbench": "~3.5.0|~3.6.0|~3.7.0"}, "funding": "__unset"}, {"version": "v1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/spatie/laravel-activitylog.git", "type": "git", "reference": "d46c4a67a3f53a72e9b723bee40dff360f434921"}, "dist": {"url": "https://api.github.com/repos/spatie/laravel-activitylog/zipball/d46c4a67a3f53a72e9b723bee40dff360f434921", "type": "zip", "shasum": "", "reference": "d46c4a67a3f53a72e9b723bee40dff360f434921"}, "support": {"issues": "https://github.com/spatie/laravel-activitylog/issues", "source": "https://github.com/spatie/laravel-activitylog/tree/v1"}, "time": "2019-06-13T09:27:48+00:00", "require": {"php": "^7.0", "illuminate/config": "~5.1.0|~5.2.0|~5.3.0|~5.4.0", "illuminate/database": "~5.1.0|~5.2.0|~5.3.0|~5.4.0", "illuminate/support": "~5.1.0|~5.2.0|~5.3.0|~5.4.0", "spatie/string": "^2.1"}, "require-dev": {"phpunit/phpunit": "^5.7", "orchestra/testbench": "~3.3.0|~3.4.0", "orchestra/database": "~3.3.0|~3.4.0"}}, {"version": "dev-dependabot/github_actions/stefanzweifel/git-auto-commit-action-6.0.1", "version_normalized": "dev-dependabot/github_actions/stefanzweifel/git-auto-commit-action-6.0.1", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gummibeer.de", "role": "Developer"}], "source": {"url": "https://github.com/spatie/laravel-activitylog.git", "type": "git", "reference": "cb8ca384f504069eb80b4db200d97893d998e9ec"}, "dist": {"url": "https://api.github.com/repos/spatie/laravel-activitylog/zipball/cb8ca384f504069eb80b4db200d97893d998e9ec", "type": "zip", "shasum": "", "reference": "cb8ca384f504069eb80b4db200d97893d998e9ec"}, "support": {"issues": "https://github.com/spatie/laravel-activitylog/issues", "source": "https://github.com/spatie/laravel-activitylog/tree/dependabot/github_actions/stefanzweifel/git-auto-commit-action-6.0.1"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2025-06-12T03:58:47+00:00", "require": {"php": "^8.1", "illuminate/config": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/database": "^8.69 || ^9.27 || ^10.0 || ^11.0 || ^12.0", "illuminate/support": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "spatie/laravel-package-tools": "^1.6.3"}, "require-dev": {"ext-json": "*", "orchestra/testbench": "^6.23 || ^7.0 || ^8.0 || ^9.0 || ^10.0", "pestphp/pest": "^1.20 || ^2.0 || ^3.0"}}, {"version": "dev-shift-57948", "version_normalized": "dev-shift-57948", "source": {"url": "https://github.com/spatie/laravel-activitylog.git", "type": "git", "reference": "95c20166b0c9a272a88cbf9287656ae62f8ca285"}, "dist": {"url": "https://api.github.com/repos/spatie/laravel-activitylog/zipball/95c20166b0c9a272a88cbf9287656ae62f8ca285", "type": "zip", "shasum": "", "reference": "95c20166b0c9a272a88cbf9287656ae62f8ca285"}, "support": {"issues": "https://github.com/spatie/laravel-activitylog/issues", "source": "https://github.com/spatie/laravel-activitylog/tree/shift-57948"}, "time": "2022-03-07T17:14:11+00:00", "require": {"php": "^8.0", "illuminate/config": "^8.0 || ^9.0", "illuminate/database": "^8.53 || ^9.0", "illuminate/support": "^8.0 || ^9.0", "spatie/laravel-package-tools": "^1.6.3"}, "require-dev": {"ext-json": "*", "orchestra/testbench": "^6.23 || ^7.0", "pestphp/pest": "^1.20"}}]}, "last-modified": "Sun, 15 Jun 2025 07:00:22 GMT"}