{"minified": "composer/2.0", "packages": {"league/glide": [{"name": "league/glide", "description": "Wonderfully easy on-demand image manipulation library with an HTTP based API.", "keywords": ["image", "ImageMagick", "gd", "imagick", "manipulation", "processing", "league", "editing"], "homepage": "http://glide.thephpleague.com", "version": "3.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://titouangalopin.com"}], "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "7ef6dad6e670261fdd20b25ddc5183d1e40c28a8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/7ef6dad6e670261fdd20b25ddc5183d1e40c28a8", "type": "zip", "shasum": "", "reference": "7ef6dad6e670261fdd20b25ddc5183d1e40c28a8"}, "type": "library", "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/3.0.1"}, "funding": [], "time": "2025-03-22T12:30:13+00:00", "autoload": {"psr-4": {"League\\Glide\\": "src/"}}, "require": {"php": "^8.1", "intervention/image": "^3.9.1", "league/flysystem": "^3.0", "psr/http-message": "^1.0|^2.0"}, "require-dev": {"mockery/mockery": "^1.6", "phpunit/phpunit": "^10.5 || ^11.0", "friendsofphp/php-cs-fixer": "^3.48", "phpstan/phpstan": "^2.0"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "3be33434538773441d0e167c759339024e7877ae"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/3be33434538773441d0e167c759339024e7877ae", "type": "zip", "shasum": "", "reference": "3be33434538773441d0e167c759339024e7877ae"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/3.0.0"}, "time": "2025-03-21T14:11:55+00:00"}, {"version": "3.0.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "e1bc3afad1082ccf04729ac7a503baab965e4467"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/e1bc3afad1082ccf04729ac7a503baab965e4467", "type": "zip", "shasum": "", "reference": "e1bc3afad1082ccf04729ac7a503baab965e4467"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/3.0.0-RC2"}, "time": "2025-01-20T17:32:00+00:00"}, {"version": "3.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "89a262ff5be5cde68329ef8347f6020e765edbf3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/89a262ff5be5cde68329ef8347f6020e765edbf3", "type": "zip", "shasum": "", "reference": "89a262ff5be5cde68329ef8347f6020e765edbf3"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/3.0.0-RC1"}, "time": "2024-11-09T13:51:13+00:00", "require": {"php": "^8.1", "intervention/image": "^3.6", "league/flysystem": "^3.0", "psr/http-message": "^1.0|^2.0"}, "require-dev": {"mockery/mockery": "^1.6", "phpunit/phpunit": "^10.5 || ^11.0", "friendsofphp/php-cs-fixer": "^3.48"}}, {"version": "3.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "26af6ff11acfad6384352de3e1ded4aa721a3f77"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/26af6ff11acfad6384352de3e1ded4aa721a3f77", "type": "zip", "shasum": "", "reference": "26af6ff11acfad6384352de3e1ded4aa721a3f77"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/3.0.0-beta2"}, "time": "2024-10-27T18:34:26+00:00"}, {"version": "3.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "874a11f93ec04307e0d48047c85a4f8afd3f80ed"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/874a11f93ec04307e0d48047c85a4f8afd3f80ed", "type": "zip", "shasum": "", "reference": "874a11f93ec04307e0d48047c85a4f8afd3f80ed"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/3.0.0-beta1"}, "time": "2024-01-30T14:27:40+00:00", "require": {"php": "^8.1", "intervention/image": "^3.3", "league/flysystem": "^3.0", "psr/http-message": "^1.0|^2.0"}, "require-dev": {"mockery/mockery": "^1.6", "phpunit/phpunit": "^10.5", "friendsofphp/php-cs-fixer": "^3.48"}}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "b8e946dd87c79a9dce3290707ab90b5b52602813"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/b8e946dd87c79a9dce3290707ab90b5b52602813", "type": "zip", "shasum": "", "reference": "b8e946dd87c79a9dce3290707ab90b5b52602813"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.3.2"}, "time": "2025-03-21T13:48:39+00:00", "require": {"php": "^7.2|^8.0", "intervention/image": "^2.7", "league/flysystem": "^2.0|^3.0", "psr/http-message": "^1.0|^2.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "phpunit/phpunit": "^8.5|^9.0", "phpunit/php-token-stream": "^3.1|^4.0"}}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "62fc5ebd579e013e7573c00d1fb7e083ed395f00"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/62fc5ebd579e013e7573c00d1fb7e083ed395f00", "type": "zip", "shasum": "", "reference": "62fc5ebd579e013e7573c00d1fb7e083ed395f00"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.3.1"}, "time": "2024-12-17T05:40:16+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "2ff92c8f1edc80b74e2d3c5efccfc7223f74d407"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/2ff92c8f1edc80b74e2d3c5efccfc7223f74d407", "type": "zip", "shasum": "", "reference": "2ff92c8f1edc80b74e2d3c5efccfc7223f74d407"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.3.0"}, "time": "2023-07-08T06:26:07+00:00"}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "d31132bf5651d5abeef345ff523cd9cf2575b971"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/d31132bf5651d5abeef345ff523cd9cf2575b971", "type": "zip", "shasum": "", "reference": "d31132bf5651d5abeef345ff523cd9cf2575b971"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.2.4"}, "time": "2023-04-18T18:42:22+00:00"}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "446b1fc9f15101db52e8ddb7bec8cb16e814b244"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/446b1fc9f15101db52e8ddb7bec8cb16e814b244", "type": "zip", "shasum": "", "reference": "446b1fc9f15101db52e8ddb7bec8cb16e814b244"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.2.3"}, "time": "2023-02-14T06:15:26+00:00", "require": {"php": "^7.2|^8.0", "intervention/image": "^2.7", "league/flysystem": "^2.0|^3.0", "psr/http-message": "^1.0"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "bff5b0fe2fd26b2fde2d6958715fde313887d79d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/bff5b0fe2fd26b2fde2d6958715fde313887d79d", "type": "zip", "shasum": "", "reference": "bff5b0fe2fd26b2fde2d6958715fde313887d79d"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.2.2"}, "time": "2022-02-21T07:40:55+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "c01576e7ab4b398ad3d52a5cda9b8c9d4a406fde"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/c01576e7ab4b398ad3d52a5cda9b8c9d4a406fde", "type": "zip", "shasum": "", "reference": "c01576e7ab4b398ad3d52a5cda9b8c9d4a406fde"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.2.1"}, "time": "2022-01-14T14:41:08+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "1995b905fe86678bf3afc08302013643dfc508b9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/1995b905fe86678bf3afc08302013643dfc508b9", "type": "zip", "shasum": "", "reference": "1995b905fe86678bf3afc08302013643dfc508b9"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.2.0"}, "time": "2022-01-11T12:41:24+00:00", "require": {"php": "^7.2|^8.0", "intervention/image": "^2.7", "league/flysystem": "^2.0", "psr/http-message": "^1.0"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "87a644add027c2725487edd2a917d503bf0149af"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/87a644add027c2725487edd2a917d503bf0149af", "type": "zip", "shasum": "", "reference": "87a644add027c2725487edd2a917d503bf0149af"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.1.1"}, "time": "2021-12-09T14:04:14+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "8f62b10d561058b44bcae66bb50a0670711b8631"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/8f62b10d561058b44bcae66bb50a0670711b8631", "type": "zip", "shasum": "", "reference": "8f62b10d561058b44bcae66bb50a0670711b8631"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.1.0"}, "time": "2021-10-26T13:14:10+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "366bc4d3563d16292fa86a70036efa90669f5c7f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/366bc4d3563d16292fa86a70036efa90669f5c7f", "type": "zip", "shasum": "", "reference": "366bc4d3563d16292fa86a70036efa90669f5c7f"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/2.0.0"}, "time": "2021-03-04T14:12:41+00:00", "require": {"php": "^7.2|^8.0", "intervention/image": "^2.4", "league/flysystem": "^2.0", "psr/http-message": "^1.0"}}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "8dba756ada0b8e525bf6f1f7d1bd83c1e99e124e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/8dba756ada0b8e525bf6f1f7d1bd83c1e99e124e", "type": "zip", "shasum": "", "reference": "8dba756ada0b8e525bf6f1f7d1bd83c1e99e124e"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.7.2"}, "time": "2023-02-14T06:26:04+00:00", "require": {"php": "^7.2|^8.0", "intervention/image": "^2.4", "league/flysystem": "^1.0", "psr/http-message": "^1.0"}}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "257e0c3612ef3dc57eb7f90cb741198151a45a5f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/257e0c3612ef3dc57eb7f90cb741198151a45a5f", "type": "zip", "shasum": "", "reference": "257e0c3612ef3dc57eb7f90cb741198151a45a5f"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.7.1"}, "time": "2022-04-27T04:03:46+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}], "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "ae5e26700573cb678919d28e425a8b87bc71c546"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/ae5e26700573cb678919d28e425a8b87bc71c546", "type": "zip", "shasum": "", "reference": "ae5e26700573cb678919d28e425a8b87bc71c546"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.7.0"}, "time": "2020-11-05T17:34:03+00:00", "require": {"intervention/image": "^2.4", "league/flysystem": "^1.0", "php": "^7.2|^8.0", "psr/http-message": "^1.0"}}, {"version": "1.6.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://titouangalopin.com"}], "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "753bc8287d781fea7b1f882a078138eefbdf7c01"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/753bc8287d781fea7b1f882a078138eefbdf7c01", "type": "zip", "shasum": "", "reference": "753bc8287d781fea7b1f882a078138eefbdf7c01"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.6.1"}, "time": "2021-05-20T16:46:07+00:00", "require": {"php": "^7.2|^8.0", "intervention/image": "^2.4", "league/flysystem": "^1.0", "psr/http-message": "^1.0"}}, {"version": "1.6.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}], "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "8759b8edfe953c8e6aceb45b3647fb7ae5349a0c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/8759b8edfe953c8e6aceb45b3647fb7ae5349a0c", "type": "zip", "shasum": "", "reference": "8759b8edfe953c8e6aceb45b3647fb7ae5349a0c"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.6.0"}, "time": "2020-07-07T12:23:45+00:00", "require": {"intervention/image": "^2.4", "league/flysystem": "^1.0", "php": "^7.2", "psr/http-message": "^1.0"}, "require-dev": {"mockery/mockery": "^1.2", "phpunit/phpunit": "^8.5", "phpunit/php-token-stream": "^3.1"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "a5477e9e822ed57b39861a17092b92553634932d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/a5477e9e822ed57b39861a17092b92553634932d", "type": "zip", "shasum": "", "reference": "a5477e9e822ed57b39861a17092b92553634932d"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.5.0"}, "time": "2019-04-03T23:46:42+00:00", "require": {"intervention/image": "^2.4", "league/flysystem": "^1.0", "php": "^5.5 | ^7.0", "psr/http-message": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/php-token-stream": "^1.4", "phpunit/phpunit": "~4.4"}, "funding": "__unset"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "72430fbdb446c754910f0bab97e1ef14baab1e80"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/72430fbdb446c754910f0bab97e1ef14baab1e80", "type": "zip", "shasum": "", "reference": "72430fbdb446c754910f0bab97e1ef14baab1e80"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/master"}, "time": "2018-12-22T17:56:55+00:00", "require": {"intervention/image": "^2.4", "league/flysystem": "^1.0", "php": "^5.4 | ^7.0", "psr/http-message": "^1.0"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "bd29f65c9666abd72e66916e0573801e435ca878"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/bd29f65c9666abd72e66916e0573801e435ca878", "type": "zip", "shasum": "", "reference": "bd29f65c9666abd72e66916e0573801e435ca878"}, "time": "2018-02-12T23:28:25+00:00", "require": {"intervention/image": "^2.1", "league/flysystem": "^1.0", "php": "^5.4 | ^7.0", "psr/http-message": "^1.0"}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "8077f529a07ded3eed6c5dcf7f688249b626ddf3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/8077f529a07ded3eed6c5dcf7f688249b626ddf3", "type": "zip", "shasum": "", "reference": "8077f529a07ded3eed6c5dcf7f688249b626ddf3"}, "time": "2017-05-09T17:41:49+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "ae6351088b148ed73ebd1270e312b07fd35dd37d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/ae6351088b148ed73ebd1270e312b07fd35dd37d", "type": "zip", "shasum": "", "reference": "ae6351088b148ed73ebd1270e312b07fd35dd37d"}, "time": "2017-01-19T01:26:54+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "93bfc82142c62bf3d18420cc6c0fc0b1a8f82274"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/93bfc82142c62bf3d18420cc6c0fc0b1a8f82274", "type": "zip", "shasum": "", "reference": "93bfc82142c62bf3d18420cc6c0fc0b1a8f82274"}, "time": "2016-11-14T16:36:03+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "f44f0efee7d80e5b351055e1ffbfa4d1368a04a6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/f44f0efee7d80e5b351055e1ffbfa4d1368a04a6", "type": "zip", "shasum": "", "reference": "f44f0efee7d80e5b351055e1ffbfa4d1368a04a6"}, "time": "2016-11-14T16:01:29+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "5d71f2fb030907fdd9552b4952d1ecdc1804702e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/5d71f2fb030907fdd9552b4952d1ecdc1804702e", "type": "zip", "shasum": "", "reference": "5d71f2fb030907fdd9552b4952d1ecdc1804702e"}, "time": "2016-08-19T20:49:36+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "4218b548401ff3a8f602e2c3f78849a381b2c1bf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/4218b548401ff3a8f602e2c3f78849a381b2c1bf", "type": "zip", "shasum": "", "reference": "4218b548401ff3a8f602e2c3f78849a381b2c1bf"}, "time": "2016-06-13T14:45:28+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "0dd3e4ec976ab7a38fc42196d03014f9fb82cc94"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/0dd3e4ec976ab7a38fc42196d03014f9fb82cc94", "type": "zip", "shasum": "", "reference": "0dd3e4ec976ab7a38fc42196d03014f9fb82cc94"}, "time": "2015-12-26T15:20:39+00:00"}, {"homepage": "https://github.com/thephpleague/glide", "version": "0.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "e45a4b536924956e1b20f5d023800557d466eda7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/e45a4b536924956e1b20f5d023800557d466eda7", "type": "zip", "shasum": "", "reference": "e45a4b536924956e1b20f5d023800557d466eda7"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/0.3.x"}, "time": "2016-01-25T13:35:12+00:00", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "require": {"php": ">=5.4", "intervention/image": "~2.1", "league/flysystem": "~1.0", "symfony/http-foundation": "~2.3|~3.0", "symfony/http-kernel": "~2.3|~3.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/php-token-stream": ">=1.3.0", "phpunit/phpunit": "~4.4"}}, {"version": "0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "57a10a4b8049f9efb7debb5cfd9718f897e205d3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/57a10a4b8049f9efb7debb5cfd9718f897e205d3", "type": "zip", "shasum": "", "reference": "57a10a4b8049f9efb7debb5cfd9718f897e205d3"}, "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/master"}, "time": "2015-05-22T23:35:09+00:00", "require": {"php": ">=5.4", "intervention/image": "~2.1", "league/flysystem": "~1.0", "symfony/http-foundation": "~2.3", "symfony/http-kernel": "~2.3"}}, {"version": "0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "615861ad6c00151f8ddd36ddfccbf36ceede8554"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/615861ad6c00151f8ddd36ddfccbf36ceede8554", "type": "zip", "shasum": "", "reference": "615861ad6c00151f8ddd36ddfccbf36ceede8554"}, "time": "2015-05-22T21:16:32+00:00"}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "627e06a1f9000185dbb8c239bf0c358edda83f4a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/627e06a1f9000185dbb8c239bf0c358edda83f4a", "type": "zip", "shasum": "", "reference": "627e06a1f9000185dbb8c239bf0c358edda83f4a"}, "time": "2015-03-19T19:53:08+00:00"}, {"version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "abf28640728038ca2d9f31c69ae3f158c73406ce"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/abf28640728038ca2d9f31c69ae3f158c73406ce", "type": "zip", "shasum": "", "reference": "abf28640728038ca2d9f31c69ae3f158c73406ce"}, "time": "2015-02-26T23:03:26+00:00", "require": {"php": ">=5.4", "intervention/image": "~2.0", "league/flysystem": "~1.0", "symfony/http-foundation": "~2.3", "symfony/http-kernel": "~2.3"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/phpunit": "~4.4"}}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "0e7519dd2254cdca17d98981b1e6bd9e9677311c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/0e7519dd2254cdca17d98981b1e6bd9e9677311c", "type": "zip", "shasum": "", "reference": "0e7519dd2254cdca17d98981b1e6bd9e9677311c"}, "time": "2015-02-02T14:15:23+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "153f64e6f6c8a5ae8485c67edd8f9bc3d23d0e16"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/153f64e6f6c8a5ae8485c67edd8f9bc3d23d0e16", "type": "zip", "shasum": "", "reference": "153f64e6f6c8a5ae8485c67edd8f9bc3d23d0e16"}, "time": "2015-01-28T12:15:31+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "c0741fb596ae252f1a8285360633a9f54dcc2908"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/c0741fb596ae252f1a8285360633a9f54dcc2908", "type": "zip", "shasum": "", "reference": "c0741fb596ae252f1a8285360633a9f54dcc2908"}, "time": "2015-01-08T01:09:55+00:00", "extra": {"branch-alias": {"dev-master": "0.2-dev"}}, "require": {"php": ">=5.4", "intervention/image": "~2.0", "league/flysystem": "~0.5", "symfony/http-foundation": "~2.3", "symfony/http-kernel": "~2.3"}}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "c3311af98dc31f06042dc843ffa8333477b099ec"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/c3311af98dc31f06042dc843ffa8333477b099ec", "type": "zip", "shasum": "", "reference": "c3311af98dc31f06042dc843ffa8333477b099ec"}, "time": "2015-01-06T16:20:35+00:00", "extra": {"branch-alias": {"dev-master": "0.1-dev"}}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/glide.git", "type": "git", "reference": "19cc415e50410272807138edb64ac8fcf0504840"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide/zipball/19cc415e50410272807138edb64ac8fcf0504840", "type": "zip", "shasum": "", "reference": "19cc415e50410272807138edb64ac8fcf0504840"}, "time": "2015-01-06T13:30:31+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Sat, 22 Mar 2025 12:31:17 GMT"}