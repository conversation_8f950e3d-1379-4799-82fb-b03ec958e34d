{"minified": "composer/2.0", "packages": {"sebastian/object-reflector": [{"name": "sebastian/object-reflector", "description": "Allows reflection of object attributes, including inherited and non-public ones", "keywords": [], "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "version": "5.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "4bfa827c969c98be1e527abd576533293c634f6a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/4bfa827c969c98be1e527abd576533293c634f6a", "type": "zip", "shasum": "", "reference": "4bfa827c969c98be1e527abd576533293c634f6a"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:17+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/6e1a43b411b2ad34146dee7524cb13a068bb35f9", "type": "zip", "shasum": "", "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/4.0.1"}, "time": "2024-07-03T05:01:32+00:00", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "bb2a6255d30853425fd38f032eb64ced9f7f132d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/bb2a6255d30853425fd38f032eb64ced9f7f132d", "type": "zip", "shasum": "", "reference": "bb2a6255d30853425fd38f032eb64ced9f7f132d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/4.0.0"}, "time": "2024-02-02T06:02:18+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "type": "zip", "shasum": "", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "time": "2023-02-03T07:06:18+00:00", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "type": "zip", "shasum": "", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "time": "2020-10-26T13:14:26+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "d9d0ab3b12acb1768bc1e0a89b23c90d2043cbe5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/d9d0ab3b12acb1768bc1e0a89b23c90d2043cbe5", "type": "zip", "shasum": "", "reference": "d9d0ab3b12acb1768bc1e0a89b23c90d2043cbe5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.3"}, "time": "2020-09-28T05:56:16+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "127a46f6b057441b201253526f81d5406d6c7840"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/127a46f6b057441b201253526f81d5406d6c7840", "type": "zip", "shasum": "", "reference": "127a46f6b057441b201253526f81d5406d6c7840"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/master"}, "time": "2020-06-26T12:12:55+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "14e04b3c25b821cc0702d4837803fe497680b062"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/14e04b3c25b821cc0702d4837803fe497680b062", "type": "zip", "shasum": "", "reference": "14e04b3c25b821cc0702d4837803fe497680b062"}, "time": "2020-06-15T13:08:02+00:00", "require": {"php": "^7.3"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "f4fd0835cabb0d4a6546d9fe291e5740037aa1e7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/f4fd0835cabb0d4a6546d9fe291e5740037aa1e7", "type": "zip", "shasum": "", "reference": "f4fd0835cabb0d4a6546d9fe291e5740037aa1e7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.0"}, "time": "2020-02-07T06:19:40+00:00", "funding": "__unset"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "1d439c229e61f244ff1f211e5c99737f90c67def"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/1d439c229e61f244ff1f211e5c99737f90c67def", "type": "zip", "shasum": "", "reference": "1d439c229e61f244ff1f211e5c99737f90c67def"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:56:04+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "type": "zip", "shasum": "", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "time": "2020-11-30T07:37:18+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "type": "zip", "shasum": "", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/master"}, "time": "2017-03-29T09:07:27+00:00", "require": {"php": "^7.0"}, "funding": "__unset"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "afd5797e7af7c9f529879ad5e8e8abe126c89dab"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/afd5797e7af7c9f529879ad5e8e8abe126c89dab", "type": "zip", "shasum": "", "reference": "afd5797e7af7c9f529879ad5e8e8abe126c89dab"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.0"}, "time": "2017-03-16T14:05:21+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "type": "git", "reference": "2201553542d60d25db9c5b2c54330df776648008"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/2201553542d60d25db9c5b2c54330df776648008", "type": "zip", "shasum": "", "reference": "2201553542d60d25db9c5b2c54330df776648008"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.0.0"}, "time": "2017-03-12T15:10:22+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:58:30 GMT"}