{"minified": "composer/2.0", "packages": {"zendframework/zend-diactoros": [{"name": "zendframework/zend-diactoros", "description": "PSR HTTP Message implementations", "keywords": ["http", "psr", "psr-7"], "homepage": "", "version": "2.2.1", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [], "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "de5847b068362a88684a55b0dbb40d85986cfa52"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/de5847b068362a88684a55b0dbb40d85986cfa52", "type": "zip", "shasum": "", "reference": "de5847b068362a88684a55b0dbb40d85986cfa52"}, "type": "library", "support": {"docs": "https://docs.zendframework.com/zend-diactoros/", "forum": "https://discourse.zendframework.com/c/questions/exprssive", "issues": "https://github.com/zendframework/zend-diactoros/issues", "rss": "https://github.com/zendframework/zend-diactoros/releases.atom", "slack": "https://zendframework-slack.herokuapp.com", "source": "https://github.com/zendframework/zend-diactoros"}, "time": "2019-11-13T19:16:13+00:00", "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/marshal_uri_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php"], "psr-4": {"Zend\\Diactoros\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "2.1.x-dev", "dev-develop": "2.2.x-dev", "dev-release-1.8": "1.8.x-dev"}}, "require": {"php": "^7.1", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^0.5.0", "php-http/psr7-integration-tests": "dev-master", "phpunit/phpunit": "^7.0.2", "zendframework/zend-coding-standard": "~1.0.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "abandoned": "laminas/laminas-diactoros"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "66eded992a75bfe75a829005a2884aae5ca86daa"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/66eded992a75bfe75a829005a2884aae5ca86daa", "type": "zip", "shasum": "", "reference": "66eded992a75bfe75a829005a2884aae5ca86daa"}, "time": "2019-11-12T17:26:16+00:00"}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "6dcf9e760a6b476f3e9d80abbc9ce9c4aa921f9c"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/6dcf9e760a6b476f3e9d80abbc9ce9c4aa921f9c", "type": "zip", "shasum": "", "reference": "6dcf9e760a6b476f3e9d80abbc9ce9c4aa921f9c"}, "time": "2019-10-10T17:38:20+00:00"}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "a16378c4db99be661afc76805242281603d20a7c"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/a16378c4db99be661afc76805242281603d20a7c", "type": "zip", "shasum": "", "reference": "a16378c4db99be661afc76805242281603d20a7c"}, "time": "2019-10-08T20:08:44+00:00"}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "279723778c40164bcf984a2df12ff2c6ec5e61c1"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/279723778c40164bcf984a2df12ff2c6ec5e61c1", "type": "zip", "shasum": "", "reference": "279723778c40164bcf984a2df12ff2c6ec5e61c1"}, "time": "2019-07-10T16:13:25+00:00", "require-dev": {"ext-dom": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^0.5.0", "php-http/psr7-integration-tests": "dev-master", "phpunit/phpunit": "^7.0.2", "zendframework/zend-coding-standard": "~1.0.0"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "37bf68b428850ee26ed7c3be6c26236dd95a95f1"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/37bf68b428850ee26ed7c3be6c26236dd95a95f1", "type": "zip", "shasum": "", "reference": "37bf68b428850ee26ed7c3be6c26236dd95a95f1"}, "time": "2019-04-29T21:11:00+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "c3c330192bc9cc51b7e9ce968ff721dc32ffa986"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/c3c330192bc9cc51b7e9ce968ff721dc32ffa986", "type": "zip", "shasum": "", "reference": "c3c330192bc9cc51b7e9ce968ff721dc32ffa986"}, "time": "2019-01-05T20:13:32+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "f8c3cb729c6d416343a855e8b4be9f02356c6d7a"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/f8c3cb729c6d416343a855e8b4be9f02356c6d7a", "type": "zip", "shasum": "", "reference": "f8c3cb729c6d416343a855e8b4be9f02356c6d7a"}, "time": "2018-12-20T16:57:16+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "54788a56247183fdddbed995a61c1a177f8175be"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/54788a56247183fdddbed995a61c1a177f8175be", "type": "zip", "shasum": "", "reference": "54788a56247183fdddbed995a61c1a177f8175be"}, "time": "2019-01-05T20:18:51+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev", "dev-develop": "2.1.x-dev", "dev-release-1.8": "1.8.x-dev"}}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "ae93f809299b224b096abf227a35d9a6cd514ed9"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/ae93f809299b224b096abf227a35d9a6cd514ed9", "type": "zip", "shasum": "", "reference": "ae93f809299b224b096abf227a35d9a6cd514ed9"}, "time": "2018-12-20T16:54:16+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "a22d63fccbf1236903c2dbcd5f927f952667d749"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/a22d63fccbf1236903c2dbcd5f927f952667d749", "type": "zip", "shasum": "", "reference": "a22d63fccbf1236903c2dbcd5f927f952667d749"}, "time": "2018-12-03T16:20:17+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "0bae78192e634774b5584f0210c1232da82cb1ff"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/0bae78192e634774b5584f0210c1232da82cb1ff", "type": "zip", "shasum": "", "reference": "0bae78192e634774b5584f0210c1232da82cb1ff"}, "time": "2018-09-27T19:49:04+00:00"}, {"homepage": "https://github.com/zendframework/zend-diactoros", "version": "1.8.7", "version_normalized": "*******", "license": ["BSD-2-<PERSON><PERSON>"], "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "a85e67b86e9b8520d07e6415fcbcb8391b44a75b"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/a85e67b86e9b8520d07e6415fcbcb8391b44a75b", "type": "zip", "shasum": "", "reference": "a85e67b86e9b8520d07e6415fcbcb8391b44a75b"}, "support": {"issues": "https://github.com/zendframework/zend-diactoros/issues", "source": "https://github.com/zendframework/zend-diactoros"}, "time": "2019-08-06T17:53:53+00:00", "extra": {"branch-alias": {"dev-release-1.8": "1.8.x-dev"}}, "require": {"php": "^5.6 || ^7.0", "psr/http-message": "^1.0"}, "require-dev": {"ext-dom": "*", "ext-libxml": "*", "php-http/psr7-integration-tests": "dev-master", "phpunit/phpunit": "^5.7.16 || ^6.0.8 || ^7.2.7", "zendframework/zend-coding-standard": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}}, {"version": "1.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "20da13beba0dde8fb648be3cc19765732790f46e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/20da13beba0dde8fb648be3cc19765732790f46e", "type": "zip", "shasum": "", "reference": "20da13beba0dde8fb648be3cc19765732790f46e"}, "time": "2018-09-05T19:29:37+00:00", "extra": {"branch-alias": {"dev-master": "1.8.x-dev", "dev-develop": "1.9.x-dev", "dev-release-2.0": "2.0.x-dev"}}}, {"version": "1.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "3e4edb822c942f37ade0d09579cfbab11e2fee87"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/3e4edb822c942f37ade0d09579cfbab11e2fee87", "type": "zip", "shasum": "", "reference": "3e4edb822c942f37ade0d09579cfbab11e2fee87"}, "time": "2018-08-10T14:16:32+00:00", "require-dev": {"ext-dom": "*", "ext-libxml": "*", "phpunit/phpunit": "^5.7.16 || ^6.0.8 || ^7.2.7", "zendframework/zend-coding-standard": "~1.0"}}, {"version": "1.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "736ffa7c2bfa4a60e8a10acb316fa2ac456c5fba"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/736ffa7c2bfa4a60e8a10acb316fa2ac456c5fba", "type": "zip", "shasum": "", "reference": "736ffa7c2bfa4a60e8a10acb316fa2ac456c5fba"}, "time": "2018-08-01T13:47:49+00:00"}, {"version": "1.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "72c13834fb3db2a962e913758b384ff2e6425d6e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/72c13834fb3db2a962e913758b384ff2e6425d6e", "type": "zip", "shasum": "", "reference": "72c13834fb3db2a962e913758b384ff2e6425d6e"}, "time": "2018-07-24T21:54:38+00:00"}, {"version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "273c18bf6aaab20be9667a3aa4e7702e1e4e7ced"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/273c18bf6aaab20be9667a3aa4e7702e1e4e7ced", "type": "zip", "shasum": "", "reference": "273c18bf6aaab20be9667a3aa4e7702e1e4e7ced"}, "time": "2018-07-19T18:38:31+00:00", "require-dev": {"ext-dom": "*", "ext-libxml": "*", "phpunit/phpunit": "^5.7.16 || ^6.0.8", "zendframework/zend-coding-standard": "~1.0"}}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "63d920d1c9ebc009d860c3666593a66298727dd6"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/63d920d1c9ebc009d860c3666593a66298727dd6", "type": "zip", "shasum": "", "reference": "63d920d1c9ebc009d860c3666593a66298727dd6"}, "time": "2018-07-09T21:17:27+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "11c9c1835e60eef6f9234377a480fcec096ebd9e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/11c9c1835e60eef6f9234377a480fcec096ebd9e", "type": "zip", "shasum": "", "reference": "11c9c1835e60eef6f9234377a480fcec096ebd9e"}, "time": "2018-06-27T18:52:43+00:00"}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "741e7a571836f038de731105f4742ca8a164e43a"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/741e7a571836f038de731105f4742ca8a164e43a", "type": "zip", "shasum": "", "reference": "741e7a571836f038de731105f4742ca8a164e43a"}, "time": "2018-05-29T16:53:08+00:00", "autoload": {"psr-4": {"Zend\\Diactoros\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.7.x-dev", "dev-develop": "1.8.x-dev", "dev-release-2.0": "2.0.x-dev"}}}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "bf26aff803a11c5cc8eb7c4878a702c403ec67f1"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/bf26aff803a11c5cc8eb7c4878a702c403ec67f1", "type": "zip", "shasum": "", "reference": "bf26aff803a11c5cc8eb7c4878a702c403ec67f1"}, "time": "2018-02-26T15:44:50+00:00", "extra": {"branch-alias": {"dev-master": "1.7.x-dev", "dev-develop": "1.8.x-dev"}}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "ed6ce7e2105c400ca10277643a8327957c0384b7"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/ed6ce7e2105c400ca10277643a8327957c0384b7", "type": "zip", "shasum": "", "reference": "ed6ce7e2105c400ca10277643a8327957c0384b7"}, "time": "2018-01-04T18:21:48+00:00"}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "c8664b92a6d5bc229e48b0923486c097e45a7877"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/c8664b92a6d5bc229e48b0923486c097e45a7877", "type": "zip", "shasum": "", "reference": "c8664b92a6d5bc229e48b0923486c097e45a7877"}, "time": "2017-10-12T15:24:51+00:00", "extra": {"branch-alias": {"dev-master": "1.6-dev", "dev-develop": "1.7-dev"}}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "2faa791b66bac33ca40031d8bce03b7dc8143490"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/2faa791b66bac33ca40031d8bce03b7dc8143490", "type": "zip", "shasum": "", "reference": "2faa791b66bac33ca40031d8bce03b7dc8143490"}, "time": "2017-09-13T14:47:08+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "1d23172f9dc1687a97c195a777b0199f14f7b26e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/1d23172f9dc1687a97c195a777b0199f14f7b26e", "type": "zip", "shasum": "", "reference": "1d23172f9dc1687a97c195a777b0199f14f7b26e"}, "time": "2017-08-22T20:38:56+00:00", "extra": {"branch-alias": {"dev-master": "1.5-dev", "dev-develop": "1.6-dev"}}}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "424a840dc3bedcdeea510b42e056c77c2d6c4bef"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/424a840dc3bedcdeea510b42e056c77c2d6c4bef", "type": "zip", "shasum": "", "reference": "424a840dc3bedcdeea510b42e056c77c2d6c4bef"}, "time": "2017-08-17T21:21:00+00:00", "extra": {"branch-alias": {"dev-master": "1.4-dev", "dev-develop": "1.5-dev"}}, "require": {"php": "^5.4 || ^7.0", "psr/http-message": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6 || ^5.5", "zendframework/zend-coding-standard": "~1.0.0", "ext-dom": "*", "ext-libxml": "*"}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "b03f285a333f51e58c95cce54109a4a9ed691436"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/b03f285a333f51e58c95cce54109a4a9ed691436", "type": "zip", "shasum": "", "reference": "b03f285a333f51e58c95cce54109a4a9ed691436"}, "time": "2017-04-06T16:18:34+00:00"}, {"version": "1.3.11", "version_normalized": "********", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "95c5af43083a4dc3fa2756e2f0f96a05b3a8ee08"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/95c5af43083a4dc3fa2756e2f0f96a05b3a8ee08", "type": "zip", "shasum": "", "reference": "95c5af43083a4dc3fa2756e2f0f96a05b3a8ee08"}, "time": "2017-04-06T16:14:54+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev", "dev-develop": "1.4-dev"}}}, {"version": "1.3.10", "version_normalized": "********", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "83e8d98b9915de76c659ce27d683c02a0f99fa90"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/83e8d98b9915de76c659ce27d683c02a0f99fa90", "type": "zip", "shasum": "", "reference": "83e8d98b9915de76c659ce27d683c02a0f99fa90"}, "time": "2017-01-23T04:53:24+00:00", "require-dev": {"phpunit/phpunit": "^4.6 || ^5.5", "zendframework/zend-coding-standard": "~1.0.0"}, "provide": {"psr/http-message-implementation": "~1.0.0"}}, {"version": "1.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "d5d74ca5548753e0e427fcffca89b134943bac89"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/d5d74ca5548753e0e427fcffca89b134943bac89", "type": "zip", "shasum": "", "reference": "d5d74ca5548753e0e427fcffca89b134943bac89"}, "time": "2017-01-17T16:18:33+00:00"}, {"version": "1.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "d9c1fd7c4b024179d49faf367da544b4eef7cfe8"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/d9c1fd7c4b024179d49faf367da544b4eef7cfe8", "type": "zip", "shasum": "", "reference": "d9c1fd7c4b024179d49faf367da544b4eef7cfe8"}, "time": "2017-01-05T21:44:28+00:00"}, {"version": "1.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "969ff423d3f201da3ff718a5831bb999bb0669b0"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/969ff423d3f201da3ff718a5831bb999bb0669b0", "type": "zip", "shasum": "", "reference": "969ff423d3f201da3ff718a5831bb999bb0669b0"}, "time": "2016-10-11T13:25:21+00:00", "require-dev": {"phpunit/phpunit": "^4.6 || ^5.5", "squizlabs/php_codesniffer": "^2.3.1"}}, {"version": "1.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "a60da179c37f2c4e44ef734d0b92824a58943f7f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/a60da179c37f2c4e44ef734d0b92824a58943f7f", "type": "zip", "shasum": "", "reference": "a60da179c37f2c4e44ef734d0b92824a58943f7f"}, "time": "2016-09-07T17:57:29+00:00"}, {"version": "1.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "b1d59735b672865dbeb930805029c24f226e3e77"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/b1d59735b672865dbeb930805029c24f226e3e77", "type": "zip", "shasum": "", "reference": "b1d59735b672865dbeb930805029c24f226e3e77"}, "time": "2016-03-17T18:02:05+00:00", "require-dev": {"phpunit/phpunit": "~4.6", "squizlabs/php_codesniffer": "^2.3.1"}}, {"version": "1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "960a8cdfe129ca5353461b300d79680ac6ff2e0f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/960a8cdfe129ca5353461b300d79680ac6ff2e0f", "type": "zip", "shasum": "", "reference": "960a8cdfe129ca5353461b300d79680ac6ff2e0f"}, "time": "2016-03-17T14:10:10+00:00"}, {"version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "4d54fde709664562eb63356f0250d527824d05de"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/4d54fde709664562eb63356f0250d527824d05de", "type": "zip", "shasum": "", "reference": "4d54fde709664562eb63356f0250d527824d05de"}, "time": "2016-01-04T21:37:32+00:00", "require": {"php": ">=5.4", "psr/http-message": "~1.0"}}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "4b0e6b9122b9df484a3b2e15e4fd88aa7d8861ad"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/4b0e6b9122b9df484a3b2e15e4fd88aa7d8861ad", "type": "zip", "shasum": "", "reference": "4b0e6b9122b9df484a3b2e15e4fd88aa7d8861ad"}, "time": "2015-12-22T22:44:06+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "9d760c8db53d2fb2cfc4a33d0bfbef4fae4ed797"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/9d760c8db53d2fb2cfc4a33d0bfbef4fae4ed797", "type": "zip", "shasum": "", "reference": "9d760c8db53d2fb2cfc4a33d0bfbef4fae4ed797"}, "time": "2015-12-16T16:29:46+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "08284cfe5b5d91f64d87911902acaca1b6cff11e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/08284cfe5b5d91f64d87911902acaca1b6cff11e", "type": "zip", "shasum": "", "reference": "08284cfe5b5d91f64d87911902acaca1b6cff11e"}, "time": "2015-12-15T17:59:11+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "3f2f9e3b75b3c8969229505d864b6b6c270b1719"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/3f2f9e3b75b3c8969229505d864b6b6c270b1719", "type": "zip", "shasum": "", "reference": "3f2f9e3b75b3c8969229505d864b6b6c270b1719"}, "time": "2015-12-15T17:38:19+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev", "dev-develop": "1.3-dev"}}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "edfda00b9831630c19c411f85f50a47bb66af457"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/edfda00b9831630c19c411f85f50a47bb66af457", "type": "zip", "shasum": "", "reference": "edfda00b9831630c19c411f85f50a47bb66af457"}, "time": "2015-11-24T19:16:22+00:00"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "3f0ce6c0ba2106e018fb514a9f09dbb91eb6bfd0"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/3f0ce6c0ba2106e018fb514a9f09dbb91eb6bfd0", "type": "zip", "shasum": "", "reference": "3f0ce6c0ba2106e018fb514a9f09dbb91eb6bfd0"}, "time": "2015-10-16T15:24:05+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev", "dev-develop": "1.1-dev"}}}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "e2f5c12916c74da384058d0dfbc7fbc0b03d1181"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/e2f5c12916c74da384058d0dfbc7fbc0b03d1181", "type": "zip", "shasum": "", "reference": "e2f5c12916c74da384058d0dfbc7fbc0b03d1181"}, "time": "2015-08-10T20:04:20+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "bac9f86a4dbf282a3029602411ae655225bc370b"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/bac9f86a4dbf282a3029602411ae655225bc370b", "type": "zip", "shasum": "", "reference": "bac9f86a4dbf282a3029602411ae655225bc370b"}, "time": "2015-07-12T17:59:47+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "eeedadd333b80bb5d93559e2285047463fe4fe33"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/eeedadd333b80bb5d93559e2285047463fe4fe33", "type": "zip", "shasum": "", "reference": "eeedadd333b80bb5d93559e2285047463fe4fe33"}, "time": "2015-06-25T18:07:28+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "df65f70fc36f24d51a90ad706a09cd9b74fc4dd8"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/df65f70fc36f24d51a90ad706a09cd9b74fc4dd8", "type": "zip", "shasum": "", "reference": "df65f70fc36f24d51a90ad706a09cd9b74fc4dd8"}, "time": "2015-06-24T20:42:54+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "53d25cd28e9f957be0fc4f0be300a2c2ed93910e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/53d25cd28e9f957be0fc4f0be300a2c2ed93910e", "type": "zip", "shasum": "", "reference": "53d25cd28e9f957be0fc4f0be300a2c2ed93910e"}, "time": "2015-06-24T14:49:18+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "86f8035337387b87881975b5d31d66f09260a891"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/86f8035337387b87881975b5d31d66f09260a891", "type": "zip", "shasum": "", "reference": "86f8035337387b87881975b5d31d66f09260a891"}, "time": "2015-06-23T15:55:00+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "1dce37e6df582f65d7db29cabb3310ddc50b4a50"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/1dce37e6df582f65d7db29cabb3310ddc50b4a50", "type": "zip", "shasum": "", "reference": "1dce37e6df582f65d7db29cabb3310ddc50b4a50"}, "time": "2015-06-04T19:49:11+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "b6d2ffb9fd08ffe9808a9471fc11862d4e06bf58"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/b6d2ffb9fd08ffe9808a9471fc11862d4e06bf58", "type": "zip", "shasum": "", "reference": "b6d2ffb9fd08ffe9808a9471fc11862d4e06bf58"}, "time": "2015-06-04T17:57:31+00:00", "require": {"php": ">=5.5", "psr/http-message": "~1.0"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "be067c409377671b8268236443d993374e1987fa"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/be067c409377671b8268236443d993374e1987fa", "type": "zip", "shasum": "", "reference": "be067c409377671b8268236443d993374e1987fa"}, "time": "2015-05-26T17:14:45+00:00", "require-dev": {"phpunit/phpunit": "~4.6", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-diactoros.git", "type": "git", "reference": "9fd285ecc32edfa015dd7cc91e18f6c1c07618a7"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/9fd285ecc32edfa015dd7cc91e18f6c1c07618a7", "type": "zip", "shasum": "", "reference": "9fd285ecc32edfa015dd7cc91e18f6c1c07618a7"}, "time": "2015-05-21T16:37:54+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-rpc2-p993-kj6m", "affectedVersions": ">=1.0.0,<1.8.4"}, {"advisoryId": "PKSA-ktyg-w1nh-2q1c", "affectedVersions": ">=1.0.0,<1.8.4"}, {"advisoryId": "PKSA-bxdp-npw4-xhsq", "affectedVersions": ">=1.0.0,<1.0.4"}], "last-modified": "Fri, 07 Jun 2024 22:10:50 GMT"}