{"minified": "composer/2.0", "packages": {"windwalker/structure": [{"name": "windwalker/structure", "description": "Windwalker Structure package", "keywords": ["framework", "structure", "windwalker"], "homepage": "https://github.com/ventoviro/windwalker-structure", "version": "3.5.23", "version_normalized": "********", "license": ["LGPL-2.0-or-later"], "authors": [], "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "59466adb846685d60463f9c1403df2832d2fcf90"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/59466adb846685d60463f9c1403df2832d2fcf90", "type": "zip", "shasum": "", "reference": "59466adb846685d60463f9c1403df2832d2fcf90"}, "type": "windwalker-package", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.21"}, "funding": [], "time": "2020-07-04T09:50:33+00:00", "autoload": {"psr-4": {"Windwalker\\Structure\\": ""}}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "require": {"php": ">=7.1.3"}, "require-dev": {"windwalker/test": "~3.0", "symfony/yaml": "^4.0||^5.0", "laktak/hjson": "^2.1", "yosymfony/toml": "^1.0", "ext-json": "*"}, "suggest": {"symfony/yaml": "Install 3.* if you require YAML support.", "laktak/hjson": "Install ~2.0 if you require HJSON support.", "yosymfony/toml": "Install ~1.0 if you require TOML support."}}, {"version": "3.5.22", "version_normalized": "********", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.22"}}, {"version": "3.5.21", "version_normalized": "3.5.21.0", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.19"}}, {"version": "3.5.20", "version_normalized": "3.5.20.0", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}}, {"version": "3.5.19", "version_normalized": "********", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.19"}}, {"version": "3.5.18", "version_normalized": "********", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "4e0ea9cf1b73ad6c6651fc4f3ed86ade90eea04d"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/4e0ea9cf1b73ad6c6651fc4f3ed86ade90eea04d", "type": "zip", "shasum": "", "reference": "4e0ea9cf1b73ad6c6651fc4f3ed86ade90eea04d"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}, "time": "2019-10-27T07:38:41+00:00", "require-dev": {"windwalker/test": "~3.0", "symfony/yaml": "3.*", "laktak/hjson": "^2.1", "yosymfony/toml": "^1.0", "ext-json": "*"}}, {"version": "3.5.17", "version_normalized": "********", "funding": "__unset"}, {"version": "3.5.16", "version_normalized": "********", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.14"}}, {"version": "3.5.15", "version_normalized": "********"}, {"version": "3.5.14", "version_normalized": "********"}, {"version": "3.5.13", "version_normalized": "********", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "efd8e765b6d57db395c23ba02948953b7d20dd86"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/efd8e765b6d57db395c23ba02948953b7d20dd86", "type": "zip", "shasum": "", "reference": "efd8e765b6d57db395c23ba02948953b7d20dd86"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.10"}, "time": "2019-07-26T17:11:14+00:00"}, {"version": "3.5.12", "version_normalized": "********", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}}, {"version": "3.5.11", "version_normalized": "********", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.10"}}, {"version": "3.5.10", "version_normalized": "********"}, {"version": "3.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "6d3fc1ae456069a717d5a235532a68d1ef51486d"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/6d3fc1ae456069a717d5a235532a68d1ef51486d", "type": "zip", "shasum": "", "reference": "6d3fc1ae456069a717d5a235532a68d1ef51486d"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}, "time": "2019-04-28T06:01:00+00:00"}, {"version": "3.5.8", "version_normalized": "*******", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.5"}}, {"version": "3.5.7", "version_normalized": "*******"}, {"version": "3.5.6", "version_normalized": "*******", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}}, {"version": "3.5.5", "version_normalized": "*******", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.5"}}, {"version": "3.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "271ef82438c20d66d1f42c8c47c9c6365fc5bc9d"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/271ef82438c20d66d1f42c8c47c9c6365fc5bc9d", "type": "zip", "shasum": "", "reference": "271ef82438c20d66d1f42c8c47c9c6365fc5bc9d"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.4"}, "time": "2019-04-27T13:12:55+00:00"}, {"version": "3.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "c54090c14f1a9d1bd2a791d64d9aaceaa9440e90"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/c54090c14f1a9d1bd2a791d64d9aaceaa9440e90", "type": "zip", "shasum": "", "reference": "c54090c14f1a9d1bd2a791d64d9aaceaa9440e90"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.5.2"}, "time": "2019-02-05T05:54:56+00:00", "require-dev": {"windwalker/test": "~3.0", "symfony/yaml": "3.*", "ext-json": "*"}, "suggest": {"symfony/yaml": "Install 3.* if you require YAML support."}}, {"version": "3.5.2", "version_normalized": "*******"}, {"version": "3.5.1", "version_normalized": "*******", "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}}, {"version": "3.5", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "f18377064474ea9be5417b71b1a1554a8f19dde0"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/f18377064474ea9be5417b71b1a1554a8f19dde0", "type": "zip", "shasum": "", "reference": "f18377064474ea9be5417b71b1a1554a8f19dde0"}, "time": "2019-01-13T08:21:45+00:00"}, {"version": "3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "29df7459b4d13fc55c6ea6d9706eb81a1a568df6"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/29df7459b4d13fc55c6ea6d9706eb81a1a568df6", "type": "zip", "shasum": "", "reference": "29df7459b4d13fc55c6ea6d9706eb81a1a568df6"}, "time": "2018-12-02T04:59:44+00:00", "require": {"php": ">=5.5.9"}}, {"version": "3.4.8", "version_normalized": "*******"}, {"version": "3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "6baed463b4bcc49dd7b4d68c2e39ba2f658b7c12"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/6baed463b4bcc49dd7b4d68c2e39ba2f658b7c12", "type": "zip", "shasum": "", "reference": "6baed463b4bcc49dd7b4d68c2e39ba2f658b7c12"}, "time": "2018-10-13T05:21:47+00:00"}, {"version": "3.4.6", "version_normalized": "*******"}, {"version": "3.4.5", "version_normalized": "*******"}, {"version": "3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "17477b7c67ab880b8046767d52b8365837094029"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/17477b7c67ab880b8046767d52b8365837094029", "type": "zip", "shasum": "", "reference": "17477b7c67ab880b8046767d52b8365837094029"}, "time": "2018-07-08T16:19:13+00:00", "require-dev": {"windwalker/test": "~3.0", "symfony/yaml": "3.*"}}, {"version": "3.4.3", "version_normalized": "*******"}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "86d14202fd9f13ae9db8fd6637b635c5eab7efe7"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/86d14202fd9f13ae9db8fd6637b635c5eab7efe7", "type": "zip", "shasum": "", "reference": "86d14202fd9f13ae9db8fd6637b635c5eab7efe7"}, "time": "2018-06-15T03:01:39+00:00"}, {"version": "3.4.1", "version_normalized": "*******"}, {"version": "3.4", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "e715fcab36148943c4347a88dd51be17912e6ff9"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/e715fcab36148943c4347a88dd51be17912e6ff9", "type": "zip", "shasum": "", "reference": "e715fcab36148943c4347a88dd51be17912e6ff9"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/3.4"}, "time": "2018-06-10T06:10:53+00:00"}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "c827c27616fb363458c831682a125fa654efcc6e"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/c827c27616fb363458c831682a125fa654efcc6e", "type": "zip", "shasum": "", "reference": "c827c27616fb363458c831682a125fa654efcc6e"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/master"}, "time": "2018-02-20T08:15:16+00:00"}, {"version": "3.3.1", "version_normalized": "*******"}, {"version": "3.3", "version_normalized": "*******"}, {"version": "3.2.8", "version_normalized": "*******", "license": ["LGPL-2.0+"], "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "02cf014c40ecf64e0936554341a44f983e82e412"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/02cf014c40ecf64e0936554341a44f983e82e412", "type": "zip", "shasum": "", "reference": "02cf014c40ecf64e0936554341a44f983e82e412"}, "time": "2017-06-25T15:37:39+00:00"}, {"version": "3.2.7", "version_normalized": "*******"}, {"version": "3.2.6", "version_normalized": "*******"}, {"version": "3.2.5", "version_normalized": "*******"}, {"version": "3.2.4", "version_normalized": "*******"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "94e55b40b6d1d0be6ff487d3af2945a1f06c7d1a"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/94e55b40b6d1d0be6ff487d3af2945a1f06c7d1a", "type": "zip", "shasum": "", "reference": "94e55b40b6d1d0be6ff487d3af2945a1f06c7d1a"}, "time": "2017-06-09T18:19:49+00:00"}, {"version": "3.2.2", "version_normalized": "*******"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "5b10624fb64b6f3f5ccc3e5c8fa924611bce4ace"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/5b10624fb64b6f3f5ccc3e5c8fa924611bce4ace", "type": "zip", "shasum": "", "reference": "5b10624fb64b6f3f5ccc3e5c8fa924611bce4ace"}, "time": "2017-06-05T18:02:02+00:00"}, {"version": "3.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "becefa2fd2b4067fa24c8dc5d988400ce4d651c3"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/becefa2fd2b4067fa24c8dc5d988400ce4d651c3", "type": "zip", "shasum": "", "reference": "becefa2fd2b4067fa24c8dc5d988400ce4d651c3"}, "time": "2017-06-05T14:11:15+00:00"}, {"version": "3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "8b1ca6ed21494a65318985f15725d9e03b37df41"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/8b1ca6ed21494a65318985f15725d9e03b37df41", "type": "zip", "shasum": "", "reference": "8b1ca6ed21494a65318985f15725d9e03b37df41"}, "time": "2017-03-21T01:25:47+00:00", "require": {"php": ">=5.3.10"}, "require-dev": {"windwalker/test": "~3.0", "symfony/yaml": "2.*"}, "suggest": {"symfony/yaml": "Install 2.* if you require YAML support."}}, {"version": "3.1.5", "version_normalized": "*******"}, {"version": "3.1.4", "version_normalized": "*******"}, {"version": "3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "7339a50d103f51db3b914f99bda5d8ef59e723a3"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/7339a50d103f51db3b914f99bda5d8ef59e723a3", "type": "zip", "shasum": "", "reference": "7339a50d103f51db3b914f99bda5d8ef59e723a3"}, "time": "2016-11-19T12:25:33+00:00"}, {"version": "3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "7951da6e0ec9a412e97d6f751605188c55785562"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/7951da6e0ec9a412e97d6f751605188c55785562", "type": "zip", "shasum": "", "reference": "7951da6e0ec9a412e97d6f751605188c55785562"}, "time": "2016-10-11T13:17:06+00:00"}, {"version": "3.1.1", "version_normalized": "*******"}, {"version": "3.1", "version_normalized": "*******"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "abfdddad7274545b55378da9c011f5995e204cd8"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/abfdddad7274545b55378da9c011f5995e204cd8", "type": "zip", "shasum": "", "reference": "abfdddad7274545b55378da9c011f5995e204cd8"}, "time": "2016-10-07T01:21:36+00:00"}, {"version": "3.0", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "eefcbfebe56af8b4474f78bbd94e52d0590e9ead"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/eefcbfebe56af8b4474f78bbd94e52d0590e9ead", "type": "zip", "shasum": "", "reference": "eefcbfebe56af8b4474f78bbd94e52d0590e9ead"}, "time": "2016-07-18T11:04:07+00:00"}, {"version": "3.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "8240badbdc9cf326993182618c7b8cdad7e4021b"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/8240badbdc9cf326993182618c7b8cdad7e4021b", "type": "zip", "shasum": "", "reference": "8240badbdc9cf326993182618c7b8cdad7e4021b"}, "time": "2016-07-06T05:57:32+00:00"}, {"version": "3.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "f10f86784d1e3a524d9f5ebcbf46e66ffdb24257"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/f10f86784d1e3a524d9f5ebcbf46e66ffdb24257", "type": "zip", "shasum": "", "reference": "f10f86784d1e3a524d9f5ebcbf46e66ffdb24257"}, "time": "2016-07-03T23:54:06+00:00"}, {"description": "Windwalker Registry package", "keywords": ["framework", "registry", "windwalker"], "homepage": "https://github.com/ventoviro/windwalker-registry", "version": "2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "12e3bfd7b8977df5c5e6b12a969b12137ec25181"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/12e3bfd7b8977df5c5e6b12a969b12137ec25181", "type": "zip", "shasum": "", "reference": "12e3bfd7b8977df5c5e6b12a969b12137ec25181"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.1.9"}, "time": "2016-03-07T10:02:08+00:00", "autoload": {"psr-4": {"Windwalker\\Registry\\": ""}}, "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require-dev": {"windwalker/test": "~2.0", "symfony/yaml": "2.*"}}, {"version": "2.1.8", "version_normalized": "*******"}, {"version": "2.1.7", "version_normalized": "*******"}, {"version": "2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "04f6d6effcc22e5820a785bf7233c85927d4d1f8"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/04f6d6effcc22e5820a785bf7233c85927d4d1f8", "type": "zip", "shasum": "", "reference": "04f6d6effcc22e5820a785bf7233c85927d4d1f8"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.1.6"}, "time": "2016-02-12T11:42:15+00:00"}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "4e586405163f85d27f7da3564d1ec39a9cb74fe5"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/4e586405163f85d27f7da3564d1ec39a9cb74fe5", "type": "zip", "shasum": "", "reference": "4e586405163f85d27f7da3564d1ec39a9cb74fe5"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.1.5"}, "time": "2015-09-07T03:21:18+00:00"}, {"version": "2.1.4", "version_normalized": "*******"}, {"version": "2.1.2", "version_normalized": "*******"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "03cf4bf43e0d485e54624b7dc6e7d2cab64a05f7"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/03cf4bf43e0d485e54624b7dc6e7d2cab64a05f7", "type": "zip", "shasum": "", "reference": "03cf4bf43e0d485e54624b7dc6e7d2cab64a05f7"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.1.1"}, "time": "2015-08-21T16:12:20+00:00"}, {"version": "2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "6d3e1b712a003bf071c895e159a74a960d911de9"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/6d3e1b712a003bf071c895e159a74a960d911de9", "type": "zip", "shasum": "", "reference": "6d3e1b712a003bf071c895e159a74a960d911de9"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.1"}, "time": "2015-08-11T07:23:16+00:00"}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "96c31cf34ee6ed39f9215d77f7773cdfd7bc7a32"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/96c31cf34ee6ed39f9215d77f7773cdfd7bc7a32", "type": "zip", "shasum": "", "reference": "96c31cf34ee6ed39f9215d77f7773cdfd7bc7a32"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.0.9"}, "time": "2015-07-27T10:12:17+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "799e2eaae73c66e939aaeb759addd64493ebe643"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/799e2eaae73c66e939aaeb759addd64493ebe643", "type": "zip", "shasum": "", "reference": "799e2eaae73c66e939aaeb759addd64493ebe643"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.0.8"}, "time": "2015-03-13T08:29:16+00:00"}, {"version": "2.0.7", "version_normalized": "*******"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "b467a576bde467d1276009e586b2903803e5c777"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/b467a576bde467d1276009e586b2903803e5c777", "type": "zip", "shasum": "", "reference": "b467a576bde467d1276009e586b2903803e5c777"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.0.6"}, "time": "2014-12-16T15:28:13+00:00"}, {"version": "2.0.5", "version_normalized": "*******"}, {"version": "2.0.4", "version_normalized": "*******"}, {"version": "2.0.3", "version_normalized": "*******"}, {"version": "2.0.2", "version_normalized": "*******"}, {"version": "2.0.1", "version_normalized": "*******"}, {"version": "2.0.0", "version_normalized": "*******"}, {"version": "2.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "89ffeb3b885d7df0fb842ac008fcb16886de4224"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/89ffeb3b885d7df0fb842ac008fcb16886de4224", "type": "zip", "shasum": "", "reference": "89ffeb3b885d7df0fb842ac008fcb16886de4224"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.0.0-beta2"}, "time": "2014-11-24T10:14:02+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "2.0.0-beta1", "version_normalized": "*******-beta1", "license": ["GPL-2.0+"], "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "2b105ff44a8cd7ab915b4e271b8437ace6e8dd7e"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/2b105ff44a8cd7ab915b4e271b8437ace6e8dd7e", "type": "zip", "shasum": "", "reference": "2b105ff44a8cd7ab915b4e271b8437ace6e8dd7e"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.0.0-beta1"}, "time": "2014-10-05T10:44:12+00:00"}, {"version": "2.0.0-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/ventoviro/windwalker-structure.git", "type": "git", "reference": "d890a94e5122f4bc7b701079c5b25d7510edcc2d"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-structure/zipball/d890a94e5122f4bc7b701079c5b25d7510edcc2d", "type": "zip", "shasum": "", "reference": "d890a94e5122f4bc7b701079c5b25d7510edcc2d"}, "support": {"issues": "https://github.com/ventoviro/windwalker-structure/issues", "source": "https://github.com/ventoviro/windwalker-structure/tree/2.0.0-alpha"}, "time": "2014-10-05T10:00:28+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 18 Apr 2024 13:54:10 GMT"}