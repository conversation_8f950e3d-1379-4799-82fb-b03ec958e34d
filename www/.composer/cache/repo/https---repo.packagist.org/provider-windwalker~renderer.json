{"minified": "composer/2.0", "packages": {"windwalker/renderer": [{"name": "windwalker/renderer", "description": "Windwalker Renderer package", "keywords": ["framework", "renderer", "edge", "blade", "windwalker", "plates", "mustach"], "homepage": "https://github.com/windwalker-io/renderer", "version": "4.1.7", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "37b8042dcecdd8fb6013acfdc227d261397e42d7"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/37b8042dcecdd8fb6013acfdc227d261397e42d7", "type": "zip", "shasum": "", "reference": "37b8042dcecdd8fb6013acfdc227d261397e42d7"}, "type": "windwalker-package", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.7"}, "funding": [], "time": "2024-08-26T18:34:06+00:00", "autoload": {"files": ["src/bootstrap.php"], "psr-4": {"Windwalker\\Renderer\\": "src/"}}, "extra": {"windwalker": {"packages": ["Windwalker\\Renderer\\RendererPackage"]}, "branch-alias": {"dev-master": "4.x-dev"}}, "require": {"php": ">=8.2.0", "windwalker/utilities": "^4.0"}, "require-dev": {"phpunit/phpunit": "^8.0||^9.0||^10.0", "windwalker/test": "^4.0", "windwalker/dom": "^4.0", "windwalker/filesystem": "^4.0", "twig/twig": "^2.0||^3.0", "illuminate/view": "^6.0||^7.0||^8.0||^9.0||^10.0||^11.0", "mustache/mustache": "^2.0", "league/plates": "^3.4"}}, {"version": "4.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.6"}}, {"version": "4.1.5", "version_normalized": "*******", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.5"}}, {"version": "4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "05a8300caece042bc734eeb2f5095ebce3c252d3"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/05a8300caece042bc734eeb2f5095ebce3c252d3", "type": "zip", "shasum": "", "reference": "05a8300caece042bc734eeb2f5095ebce3c252d3"}, "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.4"}, "time": "2023-11-10T14:37:06+00:00"}, {"version": "4.1.3", "version_normalized": "*******", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.3"}}, {"version": "4.1.2", "version_normalized": "*******", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.2"}}, {"version": "4.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.1"}}, {"version": "4.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.1.0"}}, {"version": "4.1.0-beta1", "version_normalized": "*******-beta1", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.1.0-beta1"}}, {"version": "4.0.29", "version_normalized": "********", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "67f5908df3e8076c5aca2261c6e1222103cff364"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/67f5908df3e8076c5aca2261c6e1222103cff364", "type": "zip", "shasum": "", "reference": "67f5908df3e8076c5aca2261c6e1222103cff364"}, "support": {"source": "https://github.com/windwalker-io/renderer/tree/4.0.29"}, "time": "2023-05-28T14:55:55+00:00", "require": {"php": ">=8.0.0", "windwalker/utilities": "^4.0"}, "require-dev": {"phpunit/phpunit": "^8.0||^9.0||^10.0", "windwalker/test": "^4.0", "windwalker/dom": "^4.0", "windwalker/filesystem": "^4.0", "twig/twig": "^2.0", "illuminate/view": "^6.0||^7.0||^8.0||^9.0||^10.0", "mustache/mustache": "^2.0", "league/plates": "^3.4"}}, {"version": "4.0.27", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.27"}}, {"version": "4.0.26", "version_normalized": "********", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "6cdd0d5fe2863bab8f41042d64e346f032ee40cc"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/6cdd0d5fe2863bab8f41042d64e346f032ee40cc", "type": "zip", "shasum": "", "reference": "6cdd0d5fe2863bab8f41042d64e346f032ee40cc"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.26"}, "time": "2023-01-21T18:14:15+00:00", "require-dev": {"phpunit/phpunit": "^8.0||^9.0||^10.0", "windwalker/test": "^4.0", "windwalker/dom": "^4.0", "windwalker/filesystem": "^4.0", "twig/twig": "^2.0", "illuminate/view": "^6.0||^7.0||^8.0||^9.0", "mustache/mustache": "^2.0", "league/plates": "^3.4"}}, {"version": "4.0.25", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.25"}}, {"version": "4.0.24", "version_normalized": "********", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "26da20536fb9e6b21d5aae908d6c5b8e7eef8068"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/26da20536fb9e6b21d5aae908d6c5b8e7eef8068", "type": "zip", "shasum": "", "reference": "26da20536fb9e6b21d5aae908d6c5b8e7eef8068"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.24"}, "time": "2023-01-01T15:10:37+00:00"}, {"version": "4.0.23", "version_normalized": "********", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "0050919ec43d42f31334abb2c0e259b6a6999255"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/0050919ec43d42f31334abb2c0e259b6a6999255", "type": "zip", "shasum": "", "reference": "0050919ec43d42f31334abb2c0e259b6a6999255"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.23"}, "time": "2022-10-13T16:28:59+00:00"}, {"version": "4.0.22", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.22"}}, {"version": "4.0.21", "version_normalized": "********", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "d1868772387de6b6a7b025071a642fa05d66f240"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/d1868772387de6b6a7b025071a642fa05d66f240", "type": "zip", "shasum": "", "reference": "d1868772387de6b6a7b025071a642fa05d66f240"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.21"}, "time": "2021-10-09T17:58:05+00:00"}, {"version": "4.0.20", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.20"}}, {"version": "4.0.19", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.19"}}, {"version": "4.0.18", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.18"}}, {"version": "4.0.17", "version_normalized": "********", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.17"}}, {"version": "4.0.16", "version_normalized": "4.0.16.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.16"}}, {"version": "4.0.15", "version_normalized": "4.0.15.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.15"}}, {"version": "4.0.14", "version_normalized": "4.0.14.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.14"}}, {"version": "4.0.13", "version_normalized": "4.0.13.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.13"}}, {"version": "4.0.12", "version_normalized": "4.0.12.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.12"}}, {"version": "4.0.11", "version_normalized": "4.0.11.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.11"}}, {"version": "4.0.10", "version_normalized": "4.0.10.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.10"}}, {"version": "4.0.9", "version_normalized": "4.0.9.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.9"}}, {"version": "4.0.8", "version_normalized": "4.0.8.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.8"}}, {"version": "4.0.7", "version_normalized": "4.0.7.0", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.7"}}, {"version": "4.0.6", "version_normalized": "*******", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.6"}}, {"version": "4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "3b4e12e94aa0f84fa5abb14e4bcfed6a6bcd7ef5"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/3b4e12e94aa0f84fa5abb14e4bcfed6a6bcd7ef5", "type": "zip", "shasum": "", "reference": "3b4e12e94aa0f84fa5abb14e4bcfed6a6bcd7ef5"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.5"}, "time": "2021-09-25T18:37:32+00:00"}, {"version": "4.0.4", "version_normalized": "*******", "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.4"}}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "a66f82718afc98fe3d99b843c261f4547215c0b7"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/a66f82718afc98fe3d99b843c261f4547215c0b7", "type": "zip", "shasum": "", "reference": "a66f82718afc98fe3d99b843c261f4547215c0b7"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.3"}, "time": "2021-09-18T23:20:15+00:00"}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "7885341680de47d08d0a677bd6eb6ddf5486cb98"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/7885341680de47d08d0a677bd6eb6ddf5486cb98", "type": "zip", "shasum": "", "reference": "7885341680de47d08d0a677bd6eb6ddf5486cb98"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.2"}, "time": "2021-08-22T06:43:05+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/windwalker-io/renderer.git", "type": "git", "reference": "5e6ce6484e08cf5058ecbb198d3c3c8a82478d2d"}, "dist": {"url": "https://api.github.com/repos/windwalker-io/renderer/zipball/5e6ce6484e08cf5058ecbb198d3c3c8a82478d2d", "type": "zip", "shasum": "", "reference": "5e6ce6484e08cf5058ecbb198d3c3c8a82478d2d"}, "support": {"issues": "https://github.com/windwalker-io/renderer/issues", "source": "https://github.com/windwalker-io/renderer/tree/4.0.1"}, "time": "2021-08-15T15:30:37+00:00"}, {"homepage": "https://github.com/ventoviro/windwalker-renderer", "version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "be2e2aa8cd721e7485d9c676d95bdfe4e6642651"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/be2e2aa8cd721e7485d9c676d95bdfe4e6642651", "type": "zip", "shasum": "", "reference": "be2e2aa8cd721e7485d9c676d95bdfe4e6642651"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/4.0.0"}, "time": "2021-08-13T17:05:47+00:00"}, {"version": "4.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "b6407cca930ec73f50c52d6bcbd695000461995e"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/b6407cca930ec73f50c52d6bcbd695000461995e", "type": "zip", "shasum": "", "reference": "b6407cca930ec73f50c52d6bcbd695000461995e"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/4.0.0-beta2"}, "time": "2021-08-08T10:26:40+00:00", "require-dev": {"phpunit/phpunit": "^8.0||^9.0", "windwalker/test": "^4.0", "windwalker/dom": "^4.0", "windwalker/filesystem": "^4.0", "twig/twig": "^2.0", "illuminate/view": "^6.0||^7.0||^8.0||^9.0", "mustache/mustache": "^2.0", "league/plates": "^3.4"}}, {"version": "4.0.0-beta1", "version_normalized": "*******-beta1", "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/4.0.0-beta1"}}, {"keywords": ["framework", "renderer", "windwalker"], "version": "3.5.23", "version_normalized": "********", "license": ["LGPL-2.0-or-later"], "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "b157f2832dac02209db032cb61e21b8264ee4499"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/b157f2832dac02209db032cb61e21b8264ee4499", "type": "zip", "shasum": "", "reference": "b157f2832dac02209db032cb61e21b8264ee4499"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.5.21"}, "time": "2019-10-26T15:42:26+00:00", "autoload": {"psr-4": {"Windwalker\\Renderer\\": ""}}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "require": {"php": ">=7.1.3", "windwalker/structure": "~3.0"}, "require-dev": {"windwalker/test": "~3.0", "windwalker/dom": "~3.0", "windwalker/filesystem": "~3.0", "twig/twig": "1.*", "illuminate/view": "5.2.*", "mustache/mustache": "2.*", "league/plates": "3.*"}, "suggest": {"league/plates": "Install 3.* if you require Plates template engine.", "illuminate/view": "Install 5.* if you require Laravel Blade engine.", "twig/twig": "Install 1.* if you require Twig engine.", "mustache/mustache": "Install 2.* if you require Mustache engine."}}, {"version": "3.5.22", "version_normalized": "********"}, {"version": "3.5.21", "version_normalized": "3.5.21.0", "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.5.14"}}, {"version": "3.5.20", "version_normalized": "3.5.20.0", "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/test"}}, {"version": "3.5.19", "version_normalized": "3.5.19.0"}, {"version": "3.5.18", "version_normalized": "3.5.18.0", "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.5.14"}}, {"version": "3.5.17", "version_normalized": "********", "funding": "__unset"}, {"version": "3.5.16", "version_normalized": "********"}, {"version": "3.5.15", "version_normalized": "********"}, {"version": "3.5.14", "version_normalized": "********"}, {"version": "3.5.13", "version_normalized": "********", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "a67344b348d77014c530cc3df7a3ef8344788c52"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/a67344b348d77014c530cc3df7a3ef8344788c52", "type": "zip", "shasum": "", "reference": "a67344b348d77014c530cc3df7a3ef8344788c52"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.5.13"}, "time": "2019-08-22T08:26:01+00:00"}, {"version": "3.5.12", "version_normalized": "********", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "c49ee2a473f565b5a428f86d3462f6637a40b35e"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/c49ee2a473f565b5a428f86d3462f6637a40b35e", "type": "zip", "shasum": "", "reference": "c49ee2a473f565b5a428f86d3462f6637a40b35e"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/master"}, "time": "2019-08-17T05:12:18+00:00"}, {"version": "3.5.11", "version_normalized": "********", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "b5543585e54fab4e2f4d844e910272c2b405ba8f"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/b5543585e54fab4e2f4d844e910272c2b405ba8f", "type": "zip", "shasum": "", "reference": "b5543585e54fab4e2f4d844e910272c2b405ba8f"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.5.11"}, "time": "2019-05-29T13:58:28+00:00"}, {"version": "3.5.10", "version_normalized": "********", "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/master"}}, {"version": "3.5.9", "version_normalized": "*******"}, {"version": "3.5.8", "version_normalized": "*******"}, {"version": "3.5.7", "version_normalized": "*******"}, {"version": "3.5.6", "version_normalized": "*******"}, {"version": "3.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "61ac39f563b0f203de88c7834d4e0ef5f2485525"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/61ac39f563b0f203de88c7834d4e0ef5f2485525", "type": "zip", "shasum": "", "reference": "61ac39f563b0f203de88c7834d4e0ef5f2485525"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.5.5"}, "time": "2019-04-28T06:01:00+00:00"}, {"version": "3.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "6a751d7b85551e32b2c74c627095367dcec507a2"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/6a751d7b85551e32b2c74c627095367dcec507a2", "type": "zip", "shasum": "", "reference": "6a751d7b85551e32b2c74c627095367dcec507a2"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/master"}, "time": "2019-04-27T11:15:30+00:00"}, {"version": "3.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "4aceb1abba86f4f16dc5c57186d64f8c5d6b0653"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/4aceb1abba86f4f16dc5c57186d64f8c5d6b0653", "type": "zip", "shasum": "", "reference": "4aceb1abba86f4f16dc5c57186d64f8c5d6b0653"}, "time": "2019-04-18T04:22:04+00:00"}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "4b700b55b83afd9b73647f3ed6d0c90d5f86897a"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/4b700b55b83afd9b73647f3ed6d0c90d5f86897a", "type": "zip", "shasum": "", "reference": "4b700b55b83afd9b73647f3ed6d0c90d5f86897a"}, "time": "2019-04-02T05:21:25+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "9999d99c1adbc6b82e7a158701c8689efb817687"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/9999d99c1adbc6b82e7a158701c8689efb817687", "type": "zip", "shasum": "", "reference": "9999d99c1adbc6b82e7a158701c8689efb817687"}, "time": "2019-01-13T08:39:07+00:00"}, {"version": "3.5", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "04eebe3440ff6faee49cac2830e9657c1cba010a"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/04eebe3440ff6faee49cac2830e9657c1cba010a", "type": "zip", "shasum": "", "reference": "04eebe3440ff6faee49cac2830e9657c1cba010a"}, "time": "2019-01-13T08:21:45+00:00"}, {"version": "3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "401c5b6c97dd8dd1b6a6c398516cfb417378abb2"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/401c5b6c97dd8dd1b6a6c398516cfb417378abb2", "type": "zip", "shasum": "", "reference": "401c5b6c97dd8dd1b6a6c398516cfb417378abb2"}, "time": "2018-12-02T04:59:44+00:00", "require": {"php": ">=5.5.9", "windwalker/structure": "~3.0"}}, {"version": "3.4.8", "version_normalized": "*******"}, {"version": "3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "ef1b99501323762b9cd259bd4e3bc85deee2f35f"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/ef1b99501323762b9cd259bd4e3bc85deee2f35f", "type": "zip", "shasum": "", "reference": "ef1b99501323762b9cd259bd4e3bc85deee2f35f"}, "time": "2018-07-28T15:32:31+00:00"}, {"version": "3.4.6", "version_normalized": "*******"}, {"version": "3.4.5", "version_normalized": "*******"}, {"version": "3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "b8fff5c62845887c80668c1ae5b11c3d4fa38036"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/b8fff5c62845887c80668c1ae5b11c3d4fa38036", "type": "zip", "shasum": "", "reference": "b8fff5c62845887c80668c1ae5b11c3d4fa38036"}, "time": "2018-07-08T16:19:13+00:00"}, {"version": "3.4.3", "version_normalized": "*******"}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "8809c27ccef73a9a196a226d43890eebafa9943e"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/8809c27ccef73a9a196a226d43890eebafa9943e", "type": "zip", "shasum": "", "reference": "8809c27ccef73a9a196a226d43890eebafa9943e"}, "time": "2018-06-16T17:03:11+00:00"}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "fcf0d60f2428c1686e0e62a6d05dccc8442b5114"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/fcf0d60f2428c1686e0e62a6d05dccc8442b5114", "type": "zip", "shasum": "", "reference": "fcf0d60f2428c1686e0e62a6d05dccc8442b5114"}, "time": "2018-06-15T03:01:39+00:00"}, {"version": "3.4", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "acae480fe98211999278eec49f3e95ecf3476630"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/acae480fe98211999278eec49f3e95ecf3476630", "type": "zip", "shasum": "", "reference": "acae480fe98211999278eec49f3e95ecf3476630"}, "time": "2018-02-20T08:15:16+00:00"}, {"version": "3.3.2", "version_normalized": "*******"}, {"version": "3.3.1", "version_normalized": "*******"}, {"version": "3.3", "version_normalized": "*******"}, {"version": "3.2.8", "version_normalized": "*******", "license": ["LGPL-2.0+"], "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "51afdab34c388651215f63c5fcbcd4a0fd9d47ee"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/51afdab34c388651215f63c5fcbcd4a0fd9d47ee", "type": "zip", "shasum": "", "reference": "51afdab34c388651215f63c5fcbcd4a0fd9d47ee"}, "time": "2017-06-25T15:37:39+00:00"}, {"version": "3.2.7", "version_normalized": "*******"}, {"version": "3.2.6", "version_normalized": "*******"}, {"version": "3.2.5", "version_normalized": "*******"}, {"version": "3.2.4", "version_normalized": "*******"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "f92084940b50808e1e377ab3e6a02a81732f2696"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/f92084940b50808e1e377ab3e6a02a81732f2696", "type": "zip", "shasum": "", "reference": "f92084940b50808e1e377ab3e6a02a81732f2696"}, "time": "2017-06-09T18:19:49+00:00"}, {"version": "3.2.2", "version_normalized": "*******"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "768cd3cfaff9c0c318900f4e8b480982f59e32be"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/768cd3cfaff9c0c318900f4e8b480982f59e32be", "type": "zip", "shasum": "", "reference": "768cd3cfaff9c0c318900f4e8b480982f59e32be"}, "time": "2017-06-05T18:02:02+00:00"}, {"version": "3.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "dc5c15a138d9c84d4db78e0e2a037288c57b52c1"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/dc5c15a138d9c84d4db78e0e2a037288c57b52c1", "type": "zip", "shasum": "", "reference": "dc5c15a138d9c84d4db78e0e2a037288c57b52c1"}, "time": "2017-03-21T10:09:29+00:00"}, {"version": "3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "c9a8bb9099d40db81cf2a95e8df317ea07fa3464"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/c9a8bb9099d40db81cf2a95e8df317ea07fa3464", "type": "zip", "shasum": "", "reference": "c9a8bb9099d40db81cf2a95e8df317ea07fa3464"}, "time": "2017-01-31T15:30:06+00:00", "require": {"php": ">=5.3.10", "windwalker/structure": "~3.0"}, "require-dev": {"windwalker/test": "~3.0", "windwalker/dom": "~3.0", "windwalker/filesystem": "~3.0", "twig/twig": "1.*", "illuminate/view": "4.1.*", "mustache/mustache": "2.*", "league/plates": "3.*"}, "suggest": {"league/plates": "Install 3.* if you require Plates template engine.", "illuminate/view": "Install 4.1.*(PHP 5.3) or 5.*(PHP 5.4) if you require Laravel Blade engine.", "twig/twig": "Install 1.* if you require Twig engine.", "mustache/mustache": "Install 2.* if you require Mustache engine."}}, {"version": "3.1.5", "version_normalized": "*******"}, {"version": "3.1.4", "version_normalized": "*******"}, {"version": "3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "9fb60cbf382cd48694f6452c1f7b87268392e756"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/9fb60cbf382cd48694f6452c1f7b87268392e756", "type": "zip", "shasum": "", "reference": "9fb60cbf382cd48694f6452c1f7b87268392e756"}, "time": "2016-10-11T13:17:06+00:00"}, {"version": "3.1.2", "version_normalized": "*******"}, {"version": "3.1.1", "version_normalized": "*******"}, {"version": "3.1", "version_normalized": "*******"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "74bbb426fe90677af1d57ca49437ac15b5caf777"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/74bbb426fe90677af1d57ca49437ac15b5caf777", "type": "zip", "shasum": "", "reference": "74bbb426fe90677af1d57ca49437ac15b5caf777"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/3.0"}, "time": "2016-07-18T11:05:31+00:00"}, {"version": "3.0", "version_normalized": "*******"}, {"version": "3.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "045097162414b732ccabbc0b3133341593ed7620"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/045097162414b732ccabbc0b3133341593ed7620", "type": "zip", "shasum": "", "reference": "045097162414b732ccabbc0b3133341593ed7620"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/master"}, "time": "2016-07-06T06:03:43+00:00"}, {"version": "3.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "6a2fc7765d7ed71bc459e99f06c7445f3ce455b5"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/6a2fc7765d7ed71bc459e99f06c7445f3ce455b5", "type": "zip", "shasum": "", "reference": "6a2fc7765d7ed71bc459e99f06c7445f3ce455b5"}, "time": "2016-07-04T00:04:51+00:00", "require": {"php": ">=5.3.10", "windwalker/structure": "~3.0", "windwalker/data": "~3.0"}}, {"version": "2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "de05052c559a299fd6ab48e6299efd9f0b17f439"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/de05052c559a299fd6ab48e6299efd9f0b17f439", "type": "zip", "shasum": "", "reference": "de05052c559a299fd6ab48e6299efd9f0b17f439"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/test"}, "time": "2016-02-12T15:36:22+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require": {"php": ">=5.3.10", "windwalker/registry": "~2.0", "windwalker/data": "~2.0"}, "require-dev": {"windwalker/test": "~2.0", "windwalker/dom": "~2.0", "windwalker/filesystem": "~2.0", "twig/twig": "1.*", "illuminate/view": "4.1.*", "mustache/mustache": "2.*", "league/plates": "3.*"}}, {"version": "2.1.8", "version_normalized": "*******"}, {"version": "2.1.7", "version_normalized": "*******"}, {"version": "2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "1ed4412a84022d8dd24b3e029c2f4e0921fddaa7"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/1ed4412a84022d8dd24b3e029c2f4e0921fddaa7", "type": "zip", "shasum": "", "reference": "1ed4412a84022d8dd24b3e029c2f4e0921fddaa7"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/master"}, "time": "2016-02-10T18:32:59+00:00"}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "a39c6d16a0abd61b4caa34d896bbea22255b91a0"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/a39c6d16a0abd61b4caa34d896bbea22255b91a0", "type": "zip", "shasum": "", "reference": "a39c6d16a0abd61b4caa34d896bbea22255b91a0"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/test"}, "time": "2015-09-07T03:21:18+00:00"}, {"version": "2.1.4", "version_normalized": "*******"}, {"version": "2.1.2", "version_normalized": "*******"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "44af210476d2e5ec86d671475e599bb95b16a329"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/44af210476d2e5ec86d671475e599bb95b16a329", "type": "zip", "shasum": "", "reference": "44af210476d2e5ec86d671475e599bb95b16a329"}, "support": {"issues": "https://github.com/ventoviro/windwalker-renderer/issues", "source": "https://github.com/ventoviro/windwalker-renderer/tree/master"}, "time": "2015-08-21T16:12:20+00:00"}, {"version": "2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "9acd5cd1e2907d776da2744e97beee42a81f781f"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/9acd5cd1e2907d776da2744e97beee42a81f781f", "type": "zip", "shasum": "", "reference": "9acd5cd1e2907d776da2744e97beee42a81f781f"}, "time": "2015-08-11T07:10:07+00:00", "require-dev": {"windwalker/test": "~2.0", "windwalker/filesystem": "~2.0", "twig/twig": "1.*", "illuminate/view": "4.1.*", "mustache/mustache": "2.*", "league/plates": "3.*"}}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "84c47d5c244e4223e05f27f0b42cd6182bc9ff1c"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/84c47d5c244e4223e05f27f0b42cd6182bc9ff1c", "type": "zip", "shasum": "", "reference": "84c47d5c244e4223e05f27f0b42cd6182bc9ff1c"}, "time": "2015-07-27T10:12:17+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "require-dev": {"windwalker/test": "~2.0", "windwalker/filesystem": "~2.0", "twig/twig": "*", "illuminate/view": "4.1.*", "mustache/mustache": "2.*", "league/plates": "3.*"}, "suggest": {"league/plates": "Install 3.* if you require Plates template engine.", "illuminate/view": "Install 4.1.*(PHP 5.3) or 4.2.*(PHP 5.4) if you require Laravel Blade engine.", "twig/twig": "Install 1.* if you require Twig engine.", "mustache/mustache": "Install 2.* if you require Mustache engine."}}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "6ae47270e089f04f31d26b76ad7c5890074014ea"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/6ae47270e089f04f31d26b76ad7c5890074014ea", "type": "zip", "shasum": "", "reference": "6ae47270e089f04f31d26b76ad7c5890074014ea"}, "time": "2015-02-23T04:59:38+00:00", "require-dev": {"windwalker/test": "~2.0", "windwalker/filesystem": "~2.0", "twig/twig": "*", "illuminate/view": "4.1.*", "mustache/mustache": "2.*"}, "suggest": {"illuminate/view": "Install 4.1.*(PHP 5.3) or 4.2.*(PHP 5.4) if you require Laravel Blade engine.", "twig/twig": "Install 1.* if you require Twig engine.", "mustache/mustache": "Install 2.* if you require Mustache engine."}}, {"version": "2.0.7", "version_normalized": "*******"}, {"version": "2.0.6", "version_normalized": "*******"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "4f4b4ed2f752d524afa62da327224bf6c7b81058"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/4f4b4ed2f752d524afa62da327224bf6c7b81058", "type": "zip", "shasum": "", "reference": "4f4b4ed2f752d524afa62da327224bf6c7b81058"}, "time": "2015-01-03T09:37:24+00:00"}, {"version": "2.0.4", "version_normalized": "*******"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "92c34d59a2f66050f3b4fb9840cd711fd5ccd042"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/92c34d59a2f66050f3b4fb9840cd711fd5ccd042", "type": "zip", "shasum": "", "reference": "92c34d59a2f66050f3b4fb9840cd711fd5ccd042"}, "time": "2014-12-25T08:25:05+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "48820f9a84ebb69b227cda730cc72b4daaeed1b6"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/48820f9a84ebb69b227cda730cc72b4daaeed1b6", "type": "zip", "shasum": "", "reference": "48820f9a84ebb69b227cda730cc72b4daaeed1b6"}, "time": "2014-12-16T15:28:13+00:00"}, {"version": "2.0.1", "version_normalized": "*******"}, {"version": "2.0.0", "version_normalized": "*******"}, {"version": "2.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "2ea3b51254bdbb7420461293bdee43d7473d3751"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/2ea3b51254bdbb7420461293bdee43d7473d3751", "type": "zip", "shasum": "", "reference": "2ea3b51254bdbb7420461293bdee43d7473d3751"}, "time": "2014-11-25T06:39:52+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "2.0.0-beta1", "version_normalized": "*******-beta1", "license": ["GPL-2.0+"], "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "3d3ba4bfdd4c7acfc895e614a889279e6b96c4dc"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/3d3ba4bfdd4c7acfc895e614a889279e6b96c4dc", "type": "zip", "shasum": "", "reference": "3d3ba4bfdd4c7acfc895e614a889279e6b96c4dc"}, "time": "2014-10-05T10:44:12+00:00"}, {"version": "2.0.0-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/ventoviro/windwalker-renderer.git", "type": "git", "reference": "1898aae9767470076bbe5723df016f39d56c7b19"}, "dist": {"url": "https://api.github.com/repos/ventoviro/windwalker-renderer/zipball/1898aae9767470076bbe5723df016f39d56c7b19", "type": "zip", "shasum": "", "reference": "1898aae9767470076bbe5723df016f39d56c7b19"}, "time": "2014-10-05T10:00:28+00:00", "require-dev": {"twig/twig": "*", "illuminate/view": "4.1.*", "mustache/mustache": "2.*", "windwalker/test": "~2.0", "windwalker/filesystem": "~2.0"}}]}, "security-advisories": [], "last-modified": "Sat, 15 Mar 2025 11:36:23 GMT"}