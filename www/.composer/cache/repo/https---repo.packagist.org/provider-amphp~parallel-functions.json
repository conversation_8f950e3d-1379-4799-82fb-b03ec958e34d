{"minified": "composer/2.0", "packages": {"amphp/parallel-functions": [{"name": "amphp/parallel-functions", "description": "Parallel processing made simple.", "keywords": [], "homepage": "", "version": "v2.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "7eb5df559ba8f420c491c536a17654871ba6e240"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/7eb5df559ba8f420c491c536a17654871ba6e240", "type": "zip", "shasum": "", "reference": "7eb5df559ba8f420c491c536a17654871ba6e240"}, "type": "library", "support": {"issues": "https://github.com/amphp/parallel-functions/issues", "source": "https://github.com/amphp/parallel-functions/tree/v2.0.0"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2023-11-25T01:01:40+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\ParallelFunctions\\": "src"}}, "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/parallel": "^2.2.3", "amphp/serialization": "^1", "amphp/sync": "^2.1", "laravel/serializable-closure": "^1.3"}, "require-dev": {"psalm/phar": "^5.15", "amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9.5.11"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "04e92fcacfc921a56dfe12c23b3265e62593a7cb"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/04e92fcacfc921a56dfe12c23b3265e62593a7cb", "type": "zip", "shasum": "", "reference": "04e92fcacfc921a56dfe12c23b3265e62593a7cb"}, "support": {"issues": "https://github.com/amphp/parallel-functions/issues", "source": "https://github.com/amphp/parallel-functions/tree/v1.1.0"}, "time": "2022-02-03T19:32:41+00:00", "require": {"php": ">=7.4", "amphp/amp": "^2.0.3", "amphp/parallel": "^1.4", "amphp/serialization": "^1.0", "laravel/serializable-closure": "^1.0"}, "require-dev": {"amphp/php-cs-fixer-config": "v2.x-dev", "amphp/phpunit-util": "^2.0", "phpunit/phpunit": "^9.5.11"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "af9795d51abfafc3676cbe7e17965479491abaad"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/af9795d51abfafc3676cbe7e17965479491abaad", "type": "zip", "shasum": "", "reference": "af9795d51abfafc3676cbe7e17965479491abaad"}, "support": {"issues": "https://github.com/amphp/parallel-functions/issues", "source": "https://github.com/amphp/parallel-functions/tree/master"}, "time": "2020-07-10T17:05:35+00:00", "require": {"php": ">=7", "amphp/parallel": "^1.1", "amphp/amp": "^2.0.3", "opis/closure": "^3.0.7"}, "require-dev": {"amphp/phpunit-util": "^1.0", "friendsofphp/php-cs-fixer": "^2.9", "phpunit/phpunit": "^6.5"}}, {"version": "v0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "12e6c602e067b02f78ddf5b720c17e9aa01ad4b4"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/12e6c602e067b02f78ddf5b720c17e9aa01ad4b4", "type": "zip", "shasum": "", "reference": "12e6c602e067b02f78ddf5b720c17e9aa01ad4b4"}, "time": "2018-10-28T15:29:02+00:00", "require": {"php": ">=7", "amphp/parallel": "^0.1.8 || ^0.2 || ^1", "amphp/amp": "^2.0.3", "opis/closure": "^3.0.7"}, "funding": "__unset"}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "999ba8a00adaf4d1fd3a7cb40bf7e565e507ff48"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/999ba8a00adaf4d1fd3a7cb40bf7e565e507ff48", "type": "zip", "shasum": "", "reference": "999ba8a00adaf4d1fd3a7cb40bf7e565e507ff48"}, "time": "2017-12-17T18:33:29+00:00", "require": {"php": ">=7", "amphp/parallel": "^0.1.8 || ^0.2", "amphp/amp": "^2.0.3", "opis/closure": "^3.0.7"}}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "ca55bd1d27e3de2c3bc4756984e1c7526a5c2c9e"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/ca55bd1d27e3de2c3bc4756984e1c7526a5c2c9e", "type": "zip", "shasum": "", "reference": "ca55bd1d27e3de2c3bc4756984e1c7526a5c2c9e"}, "support": {"issues": "https://github.com/amphp/parallel-functions/issues", "source": "https://github.com/amphp/parallel-functions/tree/provide-pool"}, "time": "2017-12-16T17:13:33+00:00", "require": {"php": ">=7", "amphp/parallel": "^0.1.8 || ^0.2", "amphp/amp": "^2", "opis/closure": "^3.0.7"}}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/parallel-functions.git", "type": "git", "reference": "0ac35d24a9952a64d884dc1b22a4a7cf07332b7e"}, "dist": {"url": "https://api.github.com/repos/amphp/parallel-functions/zipball/0ac35d24a9952a64d884dc1b22a4a7cf07332b7e", "type": "zip", "shasum": "", "reference": "0ac35d24a9952a64d884dc1b22a4a7cf07332b7e"}, "support": {"issues": "https://github.com/amphp/parallel-functions/issues", "source": "https://github.com/amphp/parallel-functions/tree/master"}, "time": "2017-12-14T18:09:53+00:00"}]}, "security-advisories": [], "last-modified": "Mon, 25 Mar 2024 01:46:21 GMT"}