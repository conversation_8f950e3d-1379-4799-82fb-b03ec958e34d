{"minified": "composer/2.0", "packages": {"vimeo/psalm": [{"name": "vimeo/psalm", "description": "A static analysis tool for finding errors in PHP applications", "keywords": ["php", "code", "static analysis", "inspection"], "homepage": "", "version": "7.0.0-beta9", "version_normalized": "*******-beta9", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "667088995f17e8c873cbf84a8aef9fac73880924"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/667088995f17e8c873cbf84a8aef9fac73880924", "type": "zip", "shasum": "", "reference": "667088995f17e8c873cbf84a8aef9fac73880924"}, "type": "project", "support": {"docs": "https://psalm.dev/docs", "issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm"}, "funding": [], "time": "2025-06-01T15:22:01+00:00", "autoload": {"psr-4": {"Psalm\\": "src/Psalm/"}}, "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-5.x": "5.x-dev", "dev-6.x": "6.x-dev", "dev-master": "7.x-dev"}}, "bin": ["psalm", "psalm-language-server", "psalm-plugin", "psalm-refactor", "psalm-review", "psalter"], "require": {"php": "~8.1.31 || ~8.2.27 || ~8.3.16 || ~8.4.3", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "danog/advanced-json-rpc": "^3.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^5.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "~6.3.12 || ~6.4.3 || ^7.0.3", "symfony/polyfill-php84": "^1.31.0"}, "require-dev": {"ext-curl": "*", "amphp/phpunit-util": "^3", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "danog/class-finder": "^0.4.8", "dg/bypass-finals": "^1.5", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.19", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^6.0 || ^7.0"}, "suggest": {"ext-curl": "In order to send data to shepherd", "ext-igbinary": "^2.0.5 is required, used to serialize caching data"}, "provide": {"psalm/psalm": "self.version"}}, {"version": "7.0.0-beta8", "version_normalized": "*******-beta8", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ad4b7b67c11ebe41a2ee2cc427a27e71be203d00"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ad4b7b67c11ebe41a2ee2cc427a27e71be203d00", "type": "zip", "shasum": "", "reference": "ad4b7b67c11ebe41a2ee2cc427a27e71be203d00"}, "time": "2025-05-29T19:51:10+00:00"}, {"version": "7.0.0-beta7", "version_normalized": "*******-beta7", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "478551463d9d4981e6fff8ffae168e2dcc8ec484"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/478551463d9d4981e6fff8ffae168e2dcc8ec484", "type": "zip", "shasum": "", "reference": "478551463d9d4981e6fff8ffae168e2dcc8ec484"}, "time": "2025-05-03T16:37:13+00:00"}, {"version": "7.0.0-beta6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9875722ccb4e0b5e5477532286fef84ba707ac86"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9875722ccb4e0b5e5477532286fef84ba707ac86", "type": "zip", "shasum": "", "reference": "9875722ccb4e0b5e5477532286fef84ba707ac86"}, "time": "2025-04-21T19:02:06+00:00"}, {"version": "7.0.0-beta5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3675b847eab580889bb6134dda683bbd627682fc"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3675b847eab580889bb6134dda683bbd627682fc", "type": "zip", "shasum": "", "reference": "3675b847eab580889bb6134dda683bbd627682fc"}, "time": "2025-04-04T22:04:39+00:00"}, {"version": "7.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1288d7a06a0c142cfdeabee8e597aa38a3fe4661"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1288d7a06a0c142cfdeabee8e597aa38a3fe4661", "type": "zip", "shasum": "", "reference": "1288d7a06a0c142cfdeabee8e597aa38a3fe4661"}, "time": "2025-03-31T10:16:55+00:00"}, {"version": "7.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0987ad139301e268ac2f927349b3542aad5220a4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0987ad139301e268ac2f927349b3542aad5220a4", "type": "zip", "shasum": "", "reference": "0987ad139301e268ac2f927349b3542aad5220a4"}, "time": "2025-03-27T18:58:21+00:00"}, {"version": "7.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "379308b5fa07926ab6ba361896d794e466674b43"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/379308b5fa07926ab6ba361896d794e466674b43", "type": "zip", "shasum": "", "reference": "379308b5fa07926ab6ba361896d794e466674b43"}, "time": "2025-03-26T09:54:31+00:00"}, {"version": "7.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "83dea78b8fe3a29339278e06db2830eb35af0960"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/83dea78b8fe3a29339278e06db2830eb35af0960", "type": "zip", "shasum": "", "reference": "83dea78b8fe3a29339278e06db2830eb35af0960"}, "time": "2025-03-20T10:35:54+00:00"}, {"version": "6.12.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "cf420941d061a57050b6c468ef2c778faf40aee2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/cf420941d061a57050b6c468ef2c778faf40aee2", "type": "zip", "shasum": "", "reference": "cf420941d061a57050b6c468ef2c778faf40aee2"}, "time": "2025-05-28T12:52:06+00:00"}, {"version": "6.11.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4ed53b7ccebc09ef60ec4c9e464bf8a01bfd35b0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4ed53b7ccebc09ef60ec4c9e464bf8a01bfd35b0", "type": "zip", "shasum": "", "reference": "4ed53b7ccebc09ef60ec4c9e464bf8a01bfd35b0"}, "time": "2025-05-12T11:30:26+00:00"}, {"version": "6.10.3", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "90b5b9f5e7c8e441b191d3c82c58214753d7c7c1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/90b5b9f5e7c8e441b191d3c82c58214753d7c7c1", "type": "zip", "shasum": "", "reference": "90b5b9f5e7c8e441b191d3c82c58214753d7c7c1"}, "time": "2025-05-05T18:23:39+00:00"}, {"version": "6.10.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a6c7e2358bbd62d81fca6c149f5c5823ecfcdd2a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a6c7e2358bbd62d81fca6c149f5c5823ecfcdd2a", "type": "zip", "shasum": "", "reference": "a6c7e2358bbd62d81fca6c149f5c5823ecfcdd2a"}, "time": "2025-05-03T16:37:00+00:00"}, {"version": "6.10.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f9fd6bc117e9ce1e854c2ed6777e7135aaa4966b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f9fd6bc117e9ce1e854c2ed6777e7135aaa4966b", "type": "zip", "shasum": "", "reference": "f9fd6bc117e9ce1e854c2ed6777e7135aaa4966b"}, "time": "2025-04-21T18:47:37+00:00"}, {"version": "6.10.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9c0add4eb88d4b169ac04acb7c679918cbb9c252"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9c0add4eb88d4b169ac04acb7c679918cbb9c252", "type": "zip", "shasum": "", "reference": "9c0add4eb88d4b169ac04acb7c679918cbb9c252"}, "time": "2025-03-31T10:12:50+00:00"}, {"version": "6.9.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "61488aa2849793acfb72411829d114b4aa670941"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/61488aa2849793acfb72411829d114b4aa670941", "type": "zip", "shasum": "", "reference": "61488aa2849793acfb72411829d114b4aa670941"}, "time": "2025-03-27T19:06:48+00:00"}, {"version": "6.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b9989c566097cd8a1cdc0378977938c8deb90545"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b9989c566097cd8a1cdc0378977938c8deb90545", "type": "zip", "shasum": "", "reference": "b9989c566097cd8a1cdc0378977938c8deb90545"}, "time": "2025-03-26T09:54:25+00:00"}, {"version": "6.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e052de4275fb6cdbfedfef1d883a7e90732ea609"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e052de4275fb6cdbfedfef1d883a7e90732ea609", "type": "zip", "shasum": "", "reference": "e052de4275fb6cdbfedfef1d883a7e90732ea609"}, "time": "2025-03-20T13:21:28+00:00"}, {"version": "6.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "189990791826d8aa2b72c106ea3b07656534dd61"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/189990791826d8aa2b72c106ea3b07656534dd61", "type": "zip", "shasum": "", "reference": "189990791826d8aa2b72c106ea3b07656534dd61"}, "time": "2025-03-20T10:59:02+00:00"}, {"version": "6.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1cd777f270e9da12a61ce89ad3a472d0f72b0a7d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1cd777f270e9da12a61ce89ad3a472d0f72b0a7d", "type": "zip", "shasum": "", "reference": "1cd777f270e9da12a61ce89ad3a472d0f72b0a7d"}, "time": "2025-03-20T10:35:07+00:00"}, {"version": "6.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "81c8a77c0793d450fee40265cfe68891df11d505"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/81c8a77c0793d450fee40265cfe68891df11d505", "type": "zip", "shasum": "", "reference": "81c8a77c0793d450fee40265cfe68891df11d505"}, "time": "2025-03-17T09:40:52+00:00"}, {"version": "6.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6264ad417d999db4df1ee8b4765eb98fc1907183"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6264ad417d999db4df1ee8b4765eb98fc1907183", "type": "zip", "shasum": "", "reference": "6264ad417d999db4df1ee8b4765eb98fc1907183"}, "time": "2025-03-16T20:24:18+00:00"}, {"version": "6.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e856423a5dc38d65e56b5792750c557277afe393"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e856423a5dc38d65e56b5792750c557277afe393", "type": "zip", "shasum": "", "reference": "e856423a5dc38d65e56b5792750c557277afe393"}, "time": "2025-03-10T13:29:13+00:00"}, {"version": "6.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1361cd33008feb3ae2b4a93f1860e14e538ec8c2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1361cd33008feb3ae2b4a93f1860e14e538ec8c2", "type": "zip", "shasum": "", "reference": "1361cd33008feb3ae2b4a93f1860e14e538ec8c2"}, "time": "2025-02-25T17:34:39+00:00", "require": {"php": "~8.1.31 || ~8.2.27 || ~8.3.16 || ~8.4.3", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "danog/advanced-json-rpc": "^3.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^5.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "~6.3.12 || ~6.4.3 || ^7.0.3", "symfony/polyfill-php84": "*"}}, {"version": "6.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4258dc813b28c2b03865f34a17ddf072f006b357"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4258dc813b28c2b03865f34a17ddf072f006b357", "type": "zip", "shasum": "", "reference": "4258dc813b28c2b03865f34a17ddf072f006b357"}, "time": "2025-02-25T10:34:56+00:00"}, {"version": "6.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "34ba9e1e5ea2e7396d3e2e644ee3e3a1d4336603"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/34ba9e1e5ea2e7396d3e2e644ee3e3a1d4336603", "type": "zip", "shasum": "", "reference": "34ba9e1e5ea2e7396d3e2e644ee3e3a1d4336603"}, "time": "2025-02-21T10:03:22+00:00"}, {"version": "6.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0a2d040ff0a725eb9c4a7a33fe5ffb9814864251"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0a2d040ff0a725eb9c4a7a33fe5ffb9814864251", "type": "zip", "shasum": "", "reference": "0a2d040ff0a725eb9c4a7a33fe5ffb9814864251"}, "time": "2025-02-21T09:19:07+00:00"}, {"version": "6.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7ee919229d510c5834af3112072f4b12cd7bb51a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7ee919229d510c5834af3112072f4b12cd7bb51a", "type": "zip", "shasum": "", "reference": "7ee919229d510c5834af3112072f4b12cd7bb51a"}, "time": "2025-02-20T10:00:51+00:00"}, {"version": "6.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ec70f624d634a177c5a9c1e45b47e93efa27ee77"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ec70f624d634a177c5a9c1e45b47e93efa27ee77", "type": "zip", "shasum": "", "reference": "ec70f624d634a177c5a9c1e45b47e93efa27ee77"}, "time": "2025-02-20T09:55:46+00:00"}, {"version": "6.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "720ed6b578ac24f9543c65c3d4cecea0ff348ccd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/720ed6b578ac24f9543c65c3d4cecea0ff348ccd", "type": "zip", "shasum": "", "reference": "720ed6b578ac24f9543c65c3d4cecea0ff348ccd"}, "time": "2025-02-20T08:20:00+00:00"}, {"version": "6.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f12f96cde537a424fe2a9366105b5e7146d4fc23"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f12f96cde537a424fe2a9366105b5e7146d4fc23", "type": "zip", "shasum": "", "reference": "f12f96cde537a424fe2a9366105b5e7146d4fc23"}, "time": "2025-02-19T12:09:52+00:00"}, {"version": "6.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a9e6444f6bccb72393ee7e94d32f094e87884346"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a9e6444f6bccb72393ee7e94d32f094e87884346", "type": "zip", "shasum": "", "reference": "a9e6444f6bccb72393ee7e94d32f094e87884346"}, "time": "2025-02-19T11:12:21+00:00"}, {"version": "6.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a2f190972555ea01b0cfcc1913924d6c5fc1a64e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a2f190972555ea01b0cfcc1913924d6c5fc1a64e", "type": "zip", "shasum": "", "reference": "a2f190972555ea01b0cfcc1913924d6c5fc1a64e"}, "time": "2025-02-17T10:51:14+00:00", "require": {"php": "~8.1.31 || ~8.2.27 || ~8.3.16 || ~8.4.3", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "danog/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^5.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "~6.3.12 || ~6.4.3 || ^7.0.3"}, "require-dev": {"ext-curl": "*", "amphp/phpunit-util": "^3", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "dg/bypass-finals": "^1.5", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.19", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^6.0 || ^7.0"}}, {"version": "6.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "11a32693fb9e33d4f0505de17af41c8fcac698a7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/11a32693fb9e33d4f0505de17af41c8fcac698a7", "type": "zip", "shasum": "", "reference": "11a32693fb9e33d4f0505de17af41c8fcac698a7"}, "time": "2025-02-17T10:39:14+00:00", "require": {"php": "~8.1.31 || ~8.2.27 || ~8.3.16 || ~8.4.3", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "danog/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "~6.3.12 || ~6.4.3 || ^7.0.3"}}, {"version": "6.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5c44c5bd2c02e4a61158ae14c814f12b943c86cc"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5c44c5bd2c02e4a61158ae14c814f12b943c86cc", "type": "zip", "shasum": "", "reference": "5c44c5bd2c02e4a61158ae14c814f12b943c86cc"}, "time": "2025-02-16T22:21:49+00:00"}, {"version": "6.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "dae5a05eac03b55e8f50ae00f4cd2ba5d5588d59"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/dae5a05eac03b55e8f50ae00f4cd2ba5d5588d59", "type": "zip", "shasum": "", "reference": "dae5a05eac03b55e8f50ae00f4cd2ba5d5588d59"}, "time": "2025-02-16T16:55:32+00:00"}, {"version": "6.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "07e9a53713232e924da9acb4a6e47507461cd411"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/07e9a53713232e924da9acb4a6e47507461cd411", "type": "zip", "shasum": "", "reference": "07e9a53713232e924da9acb4a6e47507461cd411"}, "time": "2025-02-14T16:15:46+00:00"}, {"version": "6.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3f17a6b24a2dbe543e21408c2b19108cf6a355ef"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3f17a6b24a2dbe543e21408c2b19108cf6a355ef", "type": "zip", "shasum": "", "reference": "3f17a6b24a2dbe543e21408c2b19108cf6a355ef"}, "time": "2025-02-10T09:55:15+00:00", "require": {"php": "~8.1.31 || ~8.2.27 || ~8.3.16 || ~8.4.3", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "~6.3.12 || ~6.4.3 || ^7.0.3"}}, {"version": "6.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "38fc8444edf0cebc9205296ee6e30e906ade783b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/38fc8444edf0cebc9205296ee6e30e906ade783b", "type": "zip", "shasum": "", "reference": "38fc8444edf0cebc9205296ee6e30e906ade783b"}, "time": "2025-02-07T20:42:25+00:00", "require": {"php": "~8.1.17 || ~8.2.4 || ~8.3.0 || ~8.4.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "^6.0 || ^7.0"}}, {"version": "6.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "09a200c15910905ddc49e5edd37b73f9c78f7580"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/09a200c15910905ddc49e5edd37b73f9c78f7580", "type": "zip", "shasum": "", "reference": "09a200c15910905ddc49e5edd37b73f9c78f7580"}, "time": "2025-02-07T09:31:21+00:00"}, {"version": "6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "04f312ac6ea48ba1c3e5db4d815bf6d74641c0ee"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/04f312ac6ea48ba1c3e5db4d815bf6d74641c0ee", "type": "zip", "shasum": "", "reference": "04f312ac6ea48ba1c3e5db4d815bf6d74641c0ee"}, "time": "2025-02-05T12:31:01+00:00", "require": {"php": "~8.1.17 || ~8.2.4 || ~8.3.0 || ~8.4.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "^6.0 || ^7.0"}}, {"version": "6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "222dda8483516044c2ed7a4c3f197d7c9d6c3ddb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/222dda8483516044c2ed7a4c3f197d7c9d6c3ddb", "type": "zip", "shasum": "", "reference": "222dda8483516044c2ed7a4c3f197d7c9d6c3ddb"}, "time": "2025-02-04T16:20:17+00:00"}, {"version": "6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "814dfde37b43a1fe6d9b0996e08b19661af53bc5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/814dfde37b43a1fe6d9b0996e08b19661af53bc5", "type": "zip", "shasum": "", "reference": "814dfde37b43a1fe6d9b0996e08b19661af53bc5"}, "time": "2025-02-01T16:30:21+00:00", "bin": ["psalm", "psalm-language-server", "psalm-plugin", "psalm-refactor", "psalter"]}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "827971f8bc7a28bb4f842f34bf8901521de1cea3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/827971f8bc7a28bb4f842f34bf8901521de1cea3", "type": "zip", "shasum": "", "reference": "827971f8bc7a28bb4f842f34bf8901521de1cea3"}, "time": "2025-01-30T19:33:41+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>"}], "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b8e96bb617bf59382113b1b56cef751f648a7dc9"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b8e96bb617bf59382113b1b56cef751f648a7dc9", "type": "zip", "shasum": "", "reference": "b8e96bb617bf59382113b1b56cef751f648a7dc9"}, "time": "2025-01-26T12:03:19+00:00", "require": {"php": "~8.1.17 || ~8.2.4 || ~8.3.0 || ~8.4.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^3", "amphp/byte-stream": "^2", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^5.0.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"ext-curl": "*", "amphp/phpunit-util": "^3", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "dg/bypass-finals": "^1.5", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.19", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0 || ^7.0"}}, {"version": "5.26.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d747f6500b38ac4f7dfc5edbcae6e4b637d7add0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d747f6500b38ac4f7dfc5edbcae6e4b637d7add0", "type": "zip", "shasum": "", "reference": "d747f6500b38ac4f7dfc5edbcae6e4b637d7add0"}, "time": "2024-09-08T18:53:08+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-master": "5.x-dev"}}, "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.17", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"ext-curl": "*", "amphp/phpunit-util": "^2.0", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0 || ^7.0"}, "conflict": {"nikic/php-parser": "4.17.0"}}, {"version": "5.26.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4787eaf414e16c661902b94dfe5d882223e5b513"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4787eaf414e16c661902b94dfe5d882223e5b513", "type": "zip", "shasum": "", "reference": "4787eaf414e16c661902b94dfe5d882223e5b513"}, "time": "2024-09-08T00:00:18+00:00"}, {"version": "5.25.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "01a8eb06b9e9cc6cfb6a320bf9fb14331919d505"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/01a8eb06b9e9cc6cfb6a320bf9fb14331919d505", "type": "zip", "shasum": "", "reference": "01a8eb06b9e9cc6cfb6a320bf9fb14331919d505"}, "time": "2024-06-16T15:08:35+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.16", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0"}}, {"version": "5.24.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "462c80e31c34e58cc4f750c656be3927e80e550e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/462c80e31c34e58cc4f750c656be3927e80e550e", "type": "zip", "shasum": "", "reference": "462c80e31c34e58cc4f750c656be3927e80e550e"}, "time": "2024-05-01T19:32:08+00:00"}, {"version": "5.23.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8471a896ccea3526b26d082f4461eeea467f10a4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8471a896ccea3526b26d082f4461eeea467f10a4", "type": "zip", "shasum": "", "reference": "8471a896ccea3526b26d082f4461eeea467f10a4"}, "time": "2024-03-11T20:33:46+00:00"}, {"version": "5.23.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "005e3184fb6de4350a873b9b8c4dc3cede9db762"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/005e3184fb6de4350a873b9b8c4dc3cede9db762", "type": "zip", "shasum": "", "reference": "005e3184fb6de4350a873b9b8c4dc3cede9db762"}, "time": "2024-03-09T19:39:11+00:00"}, {"version": "5.22.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d768d914152dbbf3486c36398802f74e80cfde48"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d768d914152dbbf3486c36398802f74e80cfde48", "type": "zip", "shasum": "", "reference": "d768d914152dbbf3486c36398802f74e80cfde48"}, "time": "2024-02-22T23:39:07+00:00"}, {"version": "5.22.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e9dad66e11274315dac27e08349c628c7d6a1a43"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e9dad66e11274315dac27e08349c628c7d6a1a43", "type": "zip", "shasum": "", "reference": "e9dad66e11274315dac27e08349c628c7d6a1a43"}, "time": "2024-02-15T22:52:31+00:00"}, {"version": "5.22.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "fe2c67ec89f358940f90db05efd2d663388b45a6"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/fe2c67ec89f358940f90db05efd2d663388b45a6", "type": "zip", "shasum": "", "reference": "fe2c67ec89f358940f90db05efd2d663388b45a6"}, "time": "2024-02-13T14:22:51+00:00"}, {"version": "5.21.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8c473e2437be8b6a8fd8f630f0f11a16b114c494"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8c473e2437be8b6a8fd8f630f0f11a16b114c494", "type": "zip", "shasum": "", "reference": "8c473e2437be8b6a8fd8f630f0f11a16b114c494"}, "time": "2024-02-01T01:04:32+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.16", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0"}}, {"version": "5.21.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "04ba9358e3f7d14a9dc3edd4e814a9d51d8c637f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/04ba9358e3f7d14a9dc3edd4e814a9d51d8c637f", "type": "zip", "shasum": "", "reference": "04ba9358e3f7d14a9dc3edd4e814a9d51d8c637f"}, "time": "2024-01-30T22:52:27+00:00"}, {"version": "5.20.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3f284e96c9d9be6fe6b15c79416e1d1903dcfef4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3f284e96c9d9be6fe6b15c79416e1d1903dcfef4", "type": "zip", "shasum": "", "reference": "3f284e96c9d9be6fe6b15c79416e1d1903dcfef4"}, "time": "2024-01-18T12:15:06+00:00"}, {"version": "5.19.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b9583493b08eb36259c0f6b746a787c4c9b3ac45"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b9583493b08eb36259c0f6b746a787c4c9b3ac45", "type": "zip", "shasum": "", "reference": "b9583493b08eb36259c0f6b746a787c4c9b3ac45"}, "time": "2024-01-14T22:41:50+00:00"}, {"version": "5.19.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "06b71be009a6bd6d81b9811855d6629b9fe90e1b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/06b71be009a6bd6d81b9811855d6629b9fe90e1b", "type": "zip", "shasum": "", "reference": "06b71be009a6bd6d81b9811855d6629b9fe90e1b"}, "time": "2024-01-09T21:02:43+00:00"}, {"version": "5.18.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b113f3ed0259fd6e212d87c3df80eec95a6abf19"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b113f3ed0259fd6e212d87c3df80eec95a6abf19", "type": "zip", "shasum": "", "reference": "b113f3ed0259fd6e212d87c3df80eec95a6abf19"}, "time": "2023-12-16T09:37:35+00:00"}, {"version": "5.17.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c620f6e80d0abfca532b00bda366062aaedf6e5d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c620f6e80d0abfca532b00bda366062aaedf6e5d", "type": "zip", "shasum": "", "reference": "c620f6e80d0abfca532b00bda366062aaedf6e5d"}, "time": "2023-12-03T20:21:41+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.16", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0"}}, {"version": "5.16.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2897ba636551a8cb61601cc26f6ccfbba6c36591"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2897ba636551a8cb61601cc26f6ccfbba6c36591", "type": "zip", "shasum": "", "reference": "2897ba636551a8cb61601cc26f6ccfbba6c36591"}, "time": "2023-11-22T20:38:47+00:00"}, {"version": "5.15.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5c774aca4746caf3d239d9c8cadb9f882ca29352"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5c774aca4746caf3d239d9c8cadb9f882ca29352", "type": "zip", "shasum": "", "reference": "5c774aca4746caf3d239d9c8cadb9f882ca29352"}, "type": "library", "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.15.0"}, "time": "2023-08-20T23:07:30+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.16", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0"}, "require-dev": {"ext-curl": "*", "amphp/phpunit-util": "^2.0", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0"}}, {"version": "5.14.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b9d355e0829c397b9b3b47d0c0ed042a8a70284d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b9d355e0829c397b9b3b47d0c0ed042a8a70284d", "type": "zip", "shasum": "", "reference": "b9d355e0829c397b9b3b47d0c0ed042a8a70284d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.14.1"}, "time": "2023-08-01T05:16:55+00:00", "conflict": "__unset"}, {"version": "5.14.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b2942cefed8443002bd3f245c4cd0a54193716d8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b2942cefed8443002bd3f245c4cd0a54193716d8", "type": "zip", "shasum": "", "reference": "b2942cefed8443002bd3f245c4cd0a54193716d8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.14.0"}, "time": "2023-07-30T20:18:56+00:00"}, {"version": "5.13.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "086b94371304750d1c673315321a55d15fc59015"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/086b94371304750d1c673315321a55d15fc59015", "type": "zip", "shasum": "", "reference": "086b94371304750d1c673315321a55d15fc59015"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.13.1"}, "time": "2023-06-27T16:39:49+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer-runtime-api": "^2", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.14", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0"}}, {"version": "5.13.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a0a9c27630bcf8301ee78cb06741d2907d8c9fef"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a0a9c27630bcf8301ee78cb06741d2907d8c9fef", "type": "zip", "shasum": "", "reference": "a0a9c27630bcf8301ee78cb06741d2907d8c9fef"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.13.0"}, "time": "2023-06-24T17:05:12+00:00"}, {"version": "5.12.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f90118cdeacd0088e7215e64c0c99ceca819e176"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f90118cdeacd0088e7215e64c0c99ceca819e176", "type": "zip", "shasum": "", "reference": "f90118cdeacd0088e7215e64c0c99ceca819e176"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.12.0"}, "time": "2023-05-22T21:19:03+00:00"}, {"version": "5.11.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c9b192ab8400fdaf04b2b13d110575adc879aa90"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c9b192ab8400fdaf04b2b13d110575adc879aa90", "type": "zip", "shasum": "", "reference": "c9b192ab8400fdaf04b2b13d110575adc879aa90"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.11.0"}, "time": "2023-05-04T21:35:44+00:00"}, {"version": "5.10.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a5effd2d2dddd1a7ea7a0f6a051ce63ff979e356"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a5effd2d2dddd1a7ea7a0f6a051ce63ff979e356", "type": "zip", "shasum": "", "reference": "a5effd2d2dddd1a7ea7a0f6a051ce63ff979e356"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.10.0"}, "time": "2023-05-02T17:20:34+00:00"}, {"version": "5.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8b9ad1eb9e8b7d3101f949291da2b9f7767cd163"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8b9ad1eb9e8b7d3101f949291da2b9f7767cd163", "type": "zip", "shasum": "", "reference": "8b9ad1eb9e8b7d3101f949291da2b9f7767cd163"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.9.0"}, "time": "2023-03-29T21:38:21+00:00"}, {"version": "5.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9cf4f60a333f779ad3bc704a555920e81d4fdcda"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9cf4f60a333f779ad3bc704a555920e81d4fdcda", "type": "zip", "shasum": "", "reference": "9cf4f60a333f779ad3bc704a555920e81d4fdcda"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.8.0"}, "time": "2023-03-09T04:14:35+00:00", "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0"}}, {"version": "5.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e028ba46ba0d7f9a78bc3201c251e137383e145f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e028ba46ba0d7f9a78bc3201c251e137383e145f", "type": "zip", "shasum": "", "reference": "e028ba46ba0d7f9a78bc3201c251e137383e145f"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.7"}, "time": "2023-02-25T01:05:07+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.10.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0"}}, {"version": "5.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ae4ec68e00e4880e3f00b1edd2da891236d749ab"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ae4ec68e00e4880e3f00b1edd2da891236d749ab", "type": "zip", "shasum": "", "reference": "ae4ec68e00e4880e3f00b1edd2da891236d749ab"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.6"}, "time": "2023-02-24T19:45:16+00:00"}, {"version": "5.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5390c212bab06ee230c8720c2e9c54b823db00c8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5390c212bab06ee230c8720c2e9c54b823db00c8", "type": "zip", "shasum": "", "reference": "5390c212bab06ee230c8720c2e9c54b823db00c8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.5"}, "time": "2023-02-21T16:02:51+00:00"}, {"version": "5.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c46eccda769925073b8f65d66c4a3a7dc5d440b1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c46eccda769925073b8f65d66c4a3a7dc5d440b1", "type": "zip", "shasum": "", "reference": "c46eccda769925073b8f65d66c4a3a7dc5d440b1"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.4"}, "time": "2023-02-21T06:57:53+00:00"}, {"version": "5.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "471a8c6a1d4f0dcf91fdb34a3109adb204012fb3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/471a8c6a1d4f0dcf91fdb34a3109adb204012fb3", "type": "zip", "shasum": "", "reference": "471a8c6a1d4f0dcf91fdb34a3109adb204012fb3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.3"}, "time": "2023-02-20T22:34:36+00:00"}, {"version": "5.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9e0dd5ab59498aa3f531bd359bc93dac671c5938"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9e0dd5ab59498aa3f531bd359bc93dac671c5938", "type": "zip", "shasum": "", "reference": "9e0dd5ab59498aa3f531bd359bc93dac671c5938"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.2"}, "time": "2023-02-20T22:10:09+00:00"}, {"version": "5.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8e0fd880141f236847ab49a06f94f788d41a4292"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8e0fd880141f236847ab49a06f94f788d41a4292", "type": "zip", "shasum": "", "reference": "8e0fd880141f236847ab49a06f94f788d41a4292"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.1"}, "time": "2023-02-20T00:48:41+00:00"}, {"version": "5.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d934875532c7eb344d6b2914c3525f116a6b0cf2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d934875532c7eb344d6b2914c3525f116a6b0cf2", "type": "zip", "shasum": "", "reference": "d934875532c7eb344d6b2914c3525f116a6b0cf2"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.7.0"}, "time": "2023-02-19T10:11:10+00:00"}, {"keywords": ["php", "code", "inspection"], "version": "5.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e784128902dfe01d489c4123d69918a9f3c1eac5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e784128902dfe01d489c4123d69918a9f3c1eac5", "type": "zip", "shasum": "", "reference": "e784128902dfe01d489c4123d69918a9f3c1eac5"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.6.0"}, "time": "2023-01-23T20:32:47+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.10.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "sebastian/diff": "^4.0 || ^5.0", "spatie/array-to-xml": "^2.17.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.0", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0"}}, {"version": "5.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b63061a27f2683ec0f3509012bb22daab3b65b61"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b63061a27f2683ec0f3509012bb22daab3b65b61", "type": "zip", "shasum": "", "reference": "b63061a27f2683ec0f3509012bb22daab3b65b61"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.5.0"}, "time": "2023-01-23T01:50:35+00:00"}, {"version": "5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "62db5d4f6a7ae0a20f7cc5a4952d730272fc0863"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/62db5d4f6a7ae0a20f7cc5a4952d730272fc0863", "type": "zip", "shasum": "", "reference": "62db5d4f6a7ae0a20f7cc5a4952d730272fc0863"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.4.0"}, "time": "2022-12-19T21:31:12+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.10.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "sebastian/diff": "^4.0", "spatie/array-to-xml": "^2.17.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/polyfill-php80": "^1.25"}}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b6faa3e96b8eb50ec71384c53799b8a107236bb6"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b6faa3e96b8eb50ec71384c53799b8a107236bb6", "type": "zip", "shasum": "", "reference": "b6faa3e96b8eb50ec71384c53799b8a107236bb6"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.3.0"}, "time": "2022-12-18T18:15:37+00:00"}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "fb685a16df3050d4c18d8a4100fe83abe6458cba"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/fb685a16df3050d4c18d8a4100fe83abe6458cba", "type": "zip", "shasum": "", "reference": "fb685a16df3050d4c18d8a4100fe83abe6458cba"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.2.0"}, "time": "2022-12-12T08:18:56+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.10.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^4.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/polyfill-php80": "^1.25"}}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4defa177c89397c5e14737a80fe4896584130674"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4defa177c89397c5e14737a80fe4896584130674", "type": "zip", "shasum": "", "reference": "4defa177c89397c5e14737a80fe4896584130674"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.1.0"}, "time": "2022-12-02T01:23:35+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.10.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^4.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/polyfill-php80": "^1.25"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4e177bf0c9f03c17d2fbfd83b7cc9c47605274d8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4e177bf0c9f03c17d2fbfd83b7cc9c47605274d8", "type": "zip", "shasum": "", "reference": "4e177bf0c9f03c17d2fbfd83b7cc9c47605274d8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.0.0"}, "time": "2022-11-30T06:06:01+00:00"}, {"version": "5.0.0-rc1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8f39de9001c995eb203cee3399307570f322076a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8f39de9001c995eb203cee3399307570f322076a", "type": "zip", "shasum": "", "reference": "8f39de9001c995eb203cee3399307570f322076a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.0.0-rc1"}, "time": "2022-11-23T17:57:53+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/polyfill-php80": "^1.25"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^4.0||^6.0", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpspec/prophecy": "^1.15", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^7.0", "phpstan/phpdoc-parser": "1.6.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "5.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f960d71b7e8dccb0815bce28a22a33ce5410e361"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f960d71b7e8dccb0815bce28a22a33ce5410e361", "type": "zip", "shasum": "", "reference": "f960d71b7e8dccb0815bce28a22a33ce5410e361"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.0.0-beta1"}, "time": "2022-04-27T16:25:14+00:00", "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/polyfill-php80": "^1.25", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^4.0||^6.0", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpspec/prophecy": "^1.15", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.3 || ^5.0 || ^6.0"}}, {"version": "5.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "cfce264bbaf1eedeacc752813ff383d8323765ce"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/cfce264bbaf1eedeacc752813ff383d8323765ce", "type": "zip", "shasum": "", "reference": "cfce264bbaf1eedeacc752813ff383d8323765ce"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/5.0.0-alpha1"}, "time": "2022-02-02T21:27:05+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-master": "4.x-dev"}}, "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.10.2", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.30.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d0bc6e25d89f649e4f36a534f330f8bb4643dd69"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d0bc6e25d89f649e4f36a534f330f8bb4643dd69", "type": "zip", "shasum": "", "reference": "d0bc6e25d89f649e4f36a534f330f8bb4643dd69"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.30.0"}, "time": "2022-11-06T20:37:08+00:00", "autoload": {"files": ["src/functions.php", "src/spl_object_id.php"], "psr-4": {"Psalm\\": "src/Psalm/"}}, "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.25", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "phpstan/phpdoc-parser": "1.2.* || 1.6.4", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.29.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7ec5ffbd5f68ae03782d7fd33fff0c45a69f95b3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7ec5ffbd5f68ae03782d7fd33fff0c45a69f95b3", "type": "zip", "shasum": "", "reference": "7ec5ffbd5f68ae03782d7fd33fff0c45a69f95b3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.29.0"}, "time": "2022-10-11T17:09:17+00:00"}, {"version": "4.28.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "52e96bea381e6cb07a672aefec791a5817694a26"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/52e96bea381e6cb07a672aefec791a5817694a26", "type": "zip", "shasum": "", "reference": "52e96bea381e6cb07a672aefec791a5817694a26"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.28.0"}, "time": "2022-10-07T16:13:24+00:00"}, {"version": "4.27.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "faf106e717c37b8c81721845dba9de3d8deed8ff"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/faf106e717c37b8c81721845dba9de3d8deed8ff", "type": "zip", "shasum": "", "reference": "faf106e717c37b8c81721845dba9de3d8deed8ff"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.27.0"}, "time": "2022-08-31T13:47:09+00:00", "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.26.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6998fabb2bf528b65777bf9941920888d23c03ac"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6998fabb2bf528b65777bf9941920888d23c03ac", "type": "zip", "shasum": "", "reference": "6998fabb2bf528b65777bf9941920888d23c03ac"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.26.0"}, "time": "2022-07-31T13:10:26+00:00"}, {"version": "v4.25.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d7cd84c4ebca74ba3419b9601f81d177bcbe2aac"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d7cd84c4ebca74ba3419b9601f81d177bcbe2aac", "type": "zip", "shasum": "", "reference": "d7cd84c4ebca74ba3419b9601f81d177bcbe2aac"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/v4.25.0"}, "time": "2022-07-25T17:04:37+00:00"}, {"version": "4.24.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "06dd975cb55d36af80f242561738f16c5f58264f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/06dd975cb55d36af80f242561738f16c5f58264f", "type": "zip", "shasum": "", "reference": "06dd975cb55d36af80f242561738f16c5f58264f"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.24.0"}, "time": "2022-06-26T11:47:54+00:00"}, {"version": "4.23.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f1fe6ff483bf325c803df9f510d09a03fd796f88"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f1fe6ff483bf325c803df9f510d09a03fd796f88", "type": "zip", "shasum": "", "reference": "f1fe6ff483bf325c803df9f510d09a03fd796f88"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.23.0"}, "time": "2022-04-28T17:35:49+00:00"}, {"version": "4.22.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "fc2c6ab4d5fa5d644d8617089f012f3bb84b8703"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/fc2c6ab4d5fa5d644d8617089f012f3bb84b8703", "type": "zip", "shasum": "", "reference": "fc2c6ab4d5fa5d644d8617089f012f3bb84b8703"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.22.0"}, "time": "2022-02-24T20:34:05+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "webmozart/path-util": "^2.3"}}, {"version": "4.21.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d8bec4c7aaee111a532daec32fb09de5687053d1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d8bec4c7aaee111a532daec32fb09de5687053d1", "type": "zip", "shasum": "", "reference": "d8bec4c7aaee111a532daec32fb09de5687053d1"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.21.0"}, "time": "2022-02-18T04:34:15+00:00"}, {"version": "4.20.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f82a70e7edfc6cf2705e9374c8a0b6a974a779ed"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f82a70e7edfc6cf2705e9374c8a0b6a974a779ed", "type": "zip", "shasum": "", "reference": "f82a70e7edfc6cf2705e9374c8a0b6a974a779ed"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.20.0"}, "time": "2022-02-03T17:03:47+00:00"}, {"version": "4.19.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a2ad69ae4f5ab1f7d225a8dc4e2ec2d9415ed599"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a2ad69ae4f5ab1f7d225a8dc4e2ec2d9415ed599", "type": "zip", "shasum": "", "reference": "a2ad69ae4f5ab1f7d225a8dc4e2ec2d9415ed599"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.19.0"}, "time": "2022-01-27T19:00:37+00:00"}, {"version": "4.18.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "dda05fa913f4dc6eb3386f2f7ce5a45d37a71bcb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/dda05fa913f4dc6eb3386f2f7ce5a45d37a71bcb", "type": "zip", "shasum": "", "reference": "dda05fa913f4dc6eb3386f2f7ce5a45d37a71bcb"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.18.1"}, "time": "2022-01-08T21:21:26+00:00"}, {"version": "4.18", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "760baddcea5e0d2ba5bfb882a3243265ad1430d3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/760baddcea5e0d2ba5bfb882a3243265ad1430d3", "type": "zip", "shasum": "", "reference": "760baddcea5e0d2ba5bfb882a3243265ad1430d3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.18"}, "time": "2022-01-06T20:49:15+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "composer-runtime-api": "^2.0.0", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16.1", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.17.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6f4707aa41c9174353a6434bba3fc8840f981d9c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6f4707aa41c9174353a6434bba3fc8840f981d9c", "type": "zip", "shasum": "", "reference": "6f4707aa41c9174353a6434bba3fc8840f981d9c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.17.0"}, "time": "2022-01-01T18:39:47+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0 || ^6.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.16.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "aa7e400908833b10c0333861f86cd48c510b60eb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/aa7e400908833b10c0333861f86cd48c510b60eb", "type": "zip", "shasum": "", "reference": "aa7e400908833b10c0333861f86cd48c510b60eb"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.16.1"}, "time": "2021-12-26T08:17:05+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0 || ^6.0", "webmozart/path-util": "^2.3"}}, {"version": "4.16.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "aea1cba982087e81c692b949c57fea8d46d9b994"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/aea1cba982087e81c692b949c57fea8d46d9b994", "type": "zip", "shasum": "", "reference": "aea1cba982087e81c692b949c57fea8d46d9b994"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.16.0"}, "time": "2021-12-26T02:10:54+00:00"}, {"version": "v4.15.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a1b5e489e6fcebe40cb804793d964e99fc347820"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a1b5e489e6fcebe40cb804793d964e99fc347820", "type": "zip", "shasum": "", "reference": "a1b5e489e6fcebe40cb804793d964e99fc347820"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/v4.15.0"}, "time": "2021-12-07T11:25:29+00:00"}, {"version": "v4.14.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "14dcbc908ab2625cd7a74258ee6c740cbecc6140"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/14dcbc908ab2625cd7a74258ee6c740cbecc6140", "type": "zip", "shasum": "", "reference": "14dcbc908ab2625cd7a74258ee6c740cbecc6140"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/v4.14.0"}, "time": "2021-12-04T17:49:24+00:00"}, {"version": "4.13.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5cf660f63b548ccd4a56f62d916ee4d6028e01a3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5cf660f63b548ccd4a56f62d916ee4d6028e01a3", "type": "zip", "shasum": "", "reference": "5cf660f63b548ccd4a56f62d916ee4d6028e01a3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.13.1"}, "time": "2021-11-23T23:52:49+00:00"}, {"version": "4.13.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "cd489407a0219b93cadd04d5aff9845a942f7e5d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/cd489407a0219b93cadd04d5aff9845a942f7e5d", "type": "zip", "shasum": "", "reference": "cd489407a0219b93cadd04d5aff9845a942f7e5d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.13.0"}, "time": "2021-11-19T20:24:40+00:00"}, {"version": "4.12.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e42bc4a23f67acba28a23bb09c348e2ff38a1d87"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e42bc4a23f67acba28a23bb09c348e2ff38a1d87", "type": "zip", "shasum": "", "reference": "e42bc4a23f67acba28a23bb09c348e2ff38a1d87"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.12.0"}, "time": "2021-11-06T10:31:17+00:00", "suggest": {"ext-igbinary": "^2.0.5"}}, {"version": "4.11.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6fba5eb554f9507b72932f9c75533d8af593688d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6fba5eb554f9507b72932f9c75533d8af593688d", "type": "zip", "shasum": "", "reference": "6fba5eb554f9507b72932f9c75533d8af593688d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.11.2"}, "time": "2021-10-26T17:28:17+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.13", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.11.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e33492398bd4e5e2ab60e331d445979bd83feecd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e33492398bd4e5e2ab60e331d445979bd83feecd", "type": "zip", "shasum": "", "reference": "e33492398bd4e5e2ab60e331d445979bd83feecd"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.11.1"}, "time": "2021-10-24T12:29:22+00:00"}, {"version": "4.11.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "99e510d6ab1da2d61d4b5eb6314baa797fe0e76d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/99e510d6ab1da2d61d4b5eb6314baa797fe0e76d", "type": "zip", "shasum": "", "reference": "99e510d6ab1da2d61d4b5eb6314baa797fe0e76d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.11.0"}, "time": "2021-10-23T21:08:14+00:00"}, {"version": "4.10.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "916b098b008f6de4543892b1e0651c1c3b92cbfa"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/916b098b008f6de4543892b1e0651c1c3b92cbfa", "type": "zip", "shasum": "", "reference": "916b098b008f6de4543892b1e0651c1c3b92cbfa"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.10.0"}, "time": "2021-09-04T21:00:09+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.12", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}}, {"version": "4.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4c262932602b9bbab5020863d1eb22d49de0dbf4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4c262932602b9bbab5020863d1eb22d49de0dbf4", "type": "zip", "shasum": "", "reference": "4c262932602b9bbab5020863d1eb22d49de0dbf4"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.9.3"}, "time": "2021-08-14T16:19:38+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.12", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}}, {"version": "4.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "00c062267d6e3229d91a1939992987e2d46f2393"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/00c062267d6e3229d91a1939992987e2d46f2393", "type": "zip", "shasum": "", "reference": "00c062267d6e3229d91a1939992987e2d46f2393"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.9.2"}, "time": "2021-08-01T01:15:26+00:00", "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.16", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3 || ^5.0", "weirdan/phpunit-appveyor-reporter": "^1.0.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7e137f5b95f7394607f5d0631172ff4c2da46781"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7e137f5b95f7394607f5d0631172ff4c2da46781", "type": "zip", "shasum": "", "reference": "7e137f5b95f7394607f5d0631172ff4c2da46781"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.9.1"}, "time": "2021-07-31T13:19:20+00:00"}, {"version": "4.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c62adf965241f65aa4f899a050a94b70bbce19fd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c62adf965241f65aa4f899a050a94b70bbce19fd", "type": "zip", "shasum": "", "reference": "c62adf965241f65aa4f899a050a94b70bbce19fd"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.9.0"}, "time": "2021-07-30T21:23:45+00:00"}, {"version": "4.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f73f2299dbc59a3e6c4d66cff4605176e728ee69"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f73f2299dbc59a3e6c4d66cff4605176e728ee69", "type": "zip", "shasum": "", "reference": "f73f2299dbc59a3e6c4d66cff4605176e728ee69"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.8.1"}, "time": "2021-06-20T23:03:20+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.10.5", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}}, {"version": "4.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0a57c865499049527b3e1c47f14abea761d94b83"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0a57c865499049527b3e1c47f14abea761d94b83", "type": "zip", "shasum": "", "reference": "0a57c865499049527b3e1c47f14abea761d94b83"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.8.0"}, "time": "2021-06-20T18:12:20+00:00"}, {"version": "4.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "38c452ae584467e939d55377aaf83b5a26f19dd1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/38c452ae584467e939d55377aaf83b5a26f19dd1", "type": "zip", "shasum": "", "reference": "38c452ae584467e939d55377aaf83b5a26f19dd1"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.7.3"}, "time": "2021-05-24T04:09:51+00:00", "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3", "weirdan/phpunit-appveyor-reporter": "^1.0.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "83a0325c0a95c0ab531d6b90c877068b464377b5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/83a0325c0a95c0ab531d6b90c877068b464377b5", "type": "zip", "shasum": "", "reference": "83a0325c0a95c0ab531d6b90c877068b464377b5"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.7.2"}, "time": "2021-05-01T20:56:25+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1 || ^2.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.10.1", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "slevomat/coding-standard": "^6.3.11", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3", "weirdan/phpunit-appveyor-reporter": "^1.0.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "cd53e047a58f71f646dd6bf45476076ab07b5d44"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/cd53e047a58f71f646dd6bf45476076ab07b5d44", "type": "zip", "shasum": "", "reference": "cd53e047a58f71f646dd6bf45476076ab07b5d44"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.7.1"}, "time": "2021-04-25T21:26:25+00:00"}, {"version": "4.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d4377c0baf3ffbf0b1ec6998e8d1be2a40971005"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d4377c0baf3ffbf0b1ec6998e8d1be2a40971005", "type": "zip", "shasum": "", "reference": "d4377c0baf3ffbf0b1ec6998e8d1be2a40971005"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.7.0"}, "time": "2021-03-29T03:54:38+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.10.1", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}}, {"version": "4.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "97fe86c4e158b5a57c5150aa5055c38b5a809aab"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/97fe86c4e158b5a57c5150aa5055c38b5a809aab", "type": "zip", "shasum": "", "reference": "97fe86c4e158b5a57c5150aa5055c38b5a809aab"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.6.4"}, "time": "2021-03-16T23:28:18+00:00", "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "slevomat/coding-standard": "^6.3.11", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f1a840727dd756899eee2f1f9ea443e265a4763f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f1a840727dd756899eee2f1f9ea443e265a4763f", "type": "zip", "shasum": "", "reference": "f1a840727dd756899eee2f1f9ea443e265a4763f"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.6.3"}, "time": "2021-03-14T00:28:24+00:00"}, {"version": "4.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bca09d74adc704c4eaee36a3c3e9d379e290fc3b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bca09d74adc704c4eaee36a3c3e9d379e290fc3b", "type": "zip", "shasum": "", "reference": "bca09d74adc704c4eaee36a3c3e9d379e290fc3b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.6.2"}, "time": "2021-02-26T02:24:18+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.5", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.10.1", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "slevomat/coding-standard": "^6.3.11", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e93e532e4eaad6d68c4d7b606853800eaceccc72"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e93e532e4eaad6d68c4d7b606853800eaceccc72", "type": "zip", "shasum": "", "reference": "e93e532e4eaad6d68c4d7b606853800eaceccc72"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.6.1"}, "time": "2021-02-17T21:54:11+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.10.1", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}}, {"version": "4.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "77feecb3707bd50378c21ae4d3fe9e320e6cea65"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/77feecb3707bd50378c21ae4d3fe9e320e6cea65", "type": "zip", "shasum": "", "reference": "77feecb3707bd50378c21ae4d3fe9e320e6cea65"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.6.0"}, "time": "2021-02-15T20:32:35+00:00", "require": {"php": "^7.1|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "^4.10.1", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/path-util": "^2.3"}}, {"version": "4.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "15a9eece1305fd26eb1336a78e5a2ee7a4b36214"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/15a9eece1305fd26eb1336a78e5a2ee7a4b36214", "type": "zip", "shasum": "", "reference": "15a9eece1305fd26eb1336a78e5a2ee7a4b36214"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.5.2"}, "time": "2021-02-13T21:45:13+00:00"}, {"version": "4.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "257a1ca672a79dedc852be1285a7b97154646418"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/257a1ca672a79dedc852be1285a7b97154646418", "type": "zip", "shasum": "", "reference": "257a1ca672a79dedc852be1285a7b97154646418"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.5.1"}, "time": "2021-02-11T19:12:25+00:00", "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0||^6.0", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0||dev-master", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "slevomat/coding-standard": "^6.3.11", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3", "weirdan/prophecy-shim": "^1.0 || ^2.0"}}, {"version": "4.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a45b5e2ae01d43175ff02684f592c83cfbd48533"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a45b5e2ae01d43175ff02684f592c83cfbd48533", "type": "zip", "shasum": "", "reference": "a45b5e2ae01d43175ff02684f592c83cfbd48533"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.5.0"}, "time": "2021-02-10T17:20:36+00:00"}, {"version": "4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9fd7a7d885b3a216cff8dec9d8c21a132f275224"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9fd7a7d885b3a216cff8dec9d8c21a132f275224", "type": "zip", "shasum": "", "reference": "9fd7a7d885b3a216cff8dec9d8c21a132f275224"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.4.1"}, "time": "2021-01-14T21:44:29+00:00"}, {"version": "4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ef4afd72bca50a0aff61599d3e433c9ee64287ac"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ef4afd72bca50a0aff61599d3e433c9ee64287ac", "type": "zip", "shasum": "", "reference": "ef4afd72bca50a0aff61599d3e433c9ee64287ac"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.4.0"}, "time": "2021-01-13T23:10:59+00:00"}, {"version": "4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "57b53ff26237074fdf5cbcb034f7da5172be4524"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/57b53ff26237074fdf5cbcb034f7da5172be4524", "type": "zip", "shasum": "", "reference": "57b53ff26237074fdf5cbcb034f7da5172be4524"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.3.2"}, "time": "2020-12-29T17:37:09+00:00"}, {"version": "4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2feba22a005a18bf31d4c7b9bdb9252c73897476"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2feba22a005a18bf31d4c7b9bdb9252c73897476", "type": "zip", "shasum": "", "reference": "2feba22a005a18bf31d4c7b9bdb9252c73897476"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.3.1"}, "time": "2020-12-03T16:44:10+00:00", "require-dev": {"php": "^7.3|^8", "ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "weirdan/prophecy-shim": "^1.0 || ^2.0", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6f916553a8de3c5c2483162b6cc01b21b24e4d1d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6f916553a8de3c5c2483162b6cc01b21b24e4d1d", "type": "zip", "shasum": "", "reference": "6f916553a8de3c5c2483162b6cc01b21b24e4d1d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.3.0"}, "time": "2020-12-02T22:13:45+00:00"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ea9cb72143b77e7520c52fa37290bd8d8bc88fd9"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ea9cb72143b77e7520c52fa37290bd8d8bc88fd9", "type": "zip", "shasum": "", "reference": "ea9cb72143b77e7520c52fa37290bd8d8bc88fd9"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.2.1"}, "time": "2020-11-20T14:56:53+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ccf6e2805f4c38655110f425c10ca79511f0bb0e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ccf6e2805f4c38655110f425c10ca79511f0bb0e", "type": "zip", "shasum": "", "reference": "ccf6e2805f4c38655110f425c10ca79511f0bb0e"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.2.0"}, "time": "2020-11-20T00:40:40+00:00"}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "16bfbd9224698bd738c665f33039fade2a1a3977"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/16bfbd9224698bd738c665f33039fade2a1a3977", "type": "zip", "shasum": "", "reference": "16bfbd9224698bd738c665f33039fade2a1a3977"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.1.1"}, "time": "2020-11-02T05:54:12+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8929bde5354595dc61947b7e27cfe59db45e3bb0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8929bde5354595dc61947b7e27cfe59db45e3bb0", "type": "zip", "shasum": "", "reference": "8929bde5354595dc61947b7e27cfe59db45e3bb0"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.1.0"}, "time": "2020-10-29T23:55:10+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b1e2e30026936ef8d5bf6a354d1c3959b6231f44"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b1e2e30026936ef8d5bf6a354d1c3959b6231f44", "type": "zip", "shasum": "", "reference": "b1e2e30026936ef8d5bf6a354d1c3959b6231f44"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.0.1"}, "time": "2020-10-20T13:40:17+00:00", "require": {"php": "^7.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "^4.10.1", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.13", "weirdan/prophecy-shim": "^1.0 || ^2.0", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7afd817a3b0c102d6b2453a1557b061d93c3ce19"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7afd817a3b0c102d6b2453a1557b061d93c3ce19", "type": "zip", "shasum": "", "reference": "7afd817a3b0c102d6b2453a1557b061d93c3ce19"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.0.0"}, "time": "2020-10-19T19:10:35+00:00"}, {"version": "4.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7c5feb2968592a179c998483c276067cbdc73fd7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7c5feb2968592a179c998483c276067cbdc73fd7", "type": "zip", "shasum": "", "reference": "7c5feb2968592a179c998483c276067cbdc73fd7"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.0.0-beta3"}, "time": "2020-10-19T13:16:18+00:00"}, {"version": "4.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e918b5dff12a3b176dd6bdafaba511f04265e317"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e918b5dff12a3b176dd6bdafaba511f04265e317", "type": "zip", "shasum": "", "reference": "e918b5dff12a3b176dd6bdafaba511f04265e317"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/4.0.0-beta1"}, "time": "2020-10-19T05:26:23+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-master": "4.0-dev"}}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpdocumentor/reflection-docblock": "^5", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^9.0", "psalm/plugin-phpunit": "^0.12", "weirdan/prophecy-shim": "^1.0 || ^2.0", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.18.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "19aa905f7c3c7350569999a93c40ae91ae4e1626"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/19aa905f7c3c7350569999a93c40ae91ae4e1626", "type": "zip", "shasum": "", "reference": "19aa905f7c3c7350569999a93c40ae91ae4e1626"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.18.2"}, "time": "2020-10-20T13:48:22+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "4.3.* || 4.4.* || 4.5.* || 4.6.* || ^4.8", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.5 || ^9.0", "psalm/plugin-phpunit": "^0.11", "weirdan/prophecy-shim": "^1.0 || ^2.0", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.18.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a463c894c4323e84121b5978867923efc6f8554a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a463c894c4323e84121b5978867923efc6f8554a", "type": "zip", "shasum": "", "reference": "a463c894c4323e84121b5978867923efc6f8554a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.18.0"}, "time": "2020-10-19T19:02:41+00:00"}, {"version": "3.17.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9e526d9cb569fe4631e6a737bbb7948d05596e3f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9e526d9cb569fe4631e6a737bbb7948d05596e3f", "type": "zip", "shasum": "", "reference": "9e526d9cb569fe4631e6a737bbb7948d05596e3f"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.17.2"}, "time": "2020-10-15T00:23:17+00:00", "require": {"ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "php": "^7.1.3|^8", "nikic/php-parser": "4.3.* || 4.4.* || 4.5.* || 4.6.* || ^4.8"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "weirdan/prophecy-shim": "^1.0 || ^2.0", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5", "phpunit/phpunit": "^7.5.16 || ^8.5 || ^9.0", "psalm/plugin-phpunit": "^0.11"}}, {"version": "3.17.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8f211792d813e4dc89f04ed372785ce93b902fd1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8f211792d813e4dc89f04ed372785ce93b902fd1", "type": "zip", "shasum": "", "reference": "8f211792d813e4dc89f04ed372785ce93b902fd1"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.17.1"}, "time": "2020-10-13T00:25:46+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-mbstring": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "4.3.* || 4.4.* || 4.5.* || 4.6.* || ^4.8", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.5 || ^9.0", "psalm/plugin-phpunit": "^0.11", "weirdan/prophecy-shim": "^1.0 || ^2.0", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.17.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a6c73a9a0da841151cfcada5e7e25f142860eb17"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a6c73a9a0da841151cfcada5e7e25f142860eb17", "type": "zip", "shasum": "", "reference": "a6c73a9a0da841151cfcada5e7e25f142860eb17"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.17.0"}, "time": "2020-10-12T23:16:13+00:00"}, {"version": "3.16", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d03e5ef057d6adc656c0ff7e166c50b73b4f48f3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d03e5ef057d6adc656c0ff7e166c50b73b4f48f3", "type": "zip", "shasum": "", "reference": "d03e5ef057d6adc656c0ff7e166c50b73b4f48f3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.16"}, "time": "2020-09-15T13:39:50+00:00"}, {"version": "3.15", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "de6e7f324f44dde540ebe7ebd4eb481b97c86f30"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/de6e7f324f44dde540ebe7ebd4eb481b97c86f30", "type": "zip", "shasum": "", "reference": "de6e7f324f44dde540ebe7ebd4eb481b97c86f30"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.x"}, "time": "2020-09-01T22:09:30+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/package-versions-deprecated": "^1.8.0", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "4.3.* || 4.4.* || 4.5.* || 4.6.* || ^4.8", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}}, {"version": "3.14.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3538fe1955d47f6ee926c0769d71af6db08aa488"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3538fe1955d47f6ee926c0769d71af6db08aa488", "type": "zip", "shasum": "", "reference": "3538fe1955d47f6ee926c0769d71af6db08aa488"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.14.2"}, "time": "2020-08-22T14:01:26+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "4.3.* || 4.4.* || 4.5.* || 4.6.* || ^4.8", "composer/package-versions-deprecated": "^1.8.0", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.5 || ^9.0", "psalm/plugin-phpunit": "^0.10", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.14.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9822043ca46d6682b76097bfa97d7c450eef9e90"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9822043ca46d6682b76097bfa97d7c450eef9e90", "type": "zip", "shasum": "", "reference": "9822043ca46d6682b76097bfa97d7c450eef9e90"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.14.1"}, "time": "2020-08-17T19:48:48+00:00"}, {"version": "3.14.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f48a362fcd5665f3938adc6ea1b5f50d2c925262"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f48a362fcd5665f3938adc6ea1b5f50d2c925262", "type": "zip", "shasum": "", "reference": "f48a362fcd5665f3938adc6ea1b5f50d2c925262"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.14.0"}, "time": "2020-08-17T15:58:30+00:00"}, {"version": "3.13.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "afd8874a9e4562eac42a02de90e42e430c3a1db1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/afd8874a9e4562eac42a02de90e42e430c3a1db1", "type": "zip", "shasum": "", "reference": "afd8874a9e4562eac42a02de90e42e430c3a1db1"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.13.1"}, "time": "2020-07-30T19:42:34+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0", "nikic/php-parser": "4.3.* || 4.4.* || 4.5.* || 4.6.*", "composer/package-versions-deprecated": "^1.8.0", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.5 || ^9.0", "psalm/plugin-phpunit": "^0.10", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.13.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5635070ec6ddf3d9369a798ed19c5f7d90229908"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5635070ec6ddf3d9369a798ed19c5f7d90229908", "type": "zip", "shasum": "", "reference": "5635070ec6ddf3d9369a798ed19c5f7d90229908"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2020-07-30T16:11:28+00:00"}, {"version": "3.12.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7c7ebd068f8acaba211d4a2c707c4ba90874fa26"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7c7ebd068f8acaba211d4a2c707c4ba90874fa26", "type": "zip", "shasum": "", "reference": "7c7ebd068f8acaba211d4a2c707c4ba90874fa26"}, "time": "2020-07-03T16:59:07+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0", "nikic/php-parser": "^4.3", "ocramius/package-versions": "^1.2", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "amphp/amp": "^2.4.2", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "php-coveralls/php-coveralls": "^2.2", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.5 || ^9.0", "psalm/plugin-phpunit": "^0.10", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.12.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9b860214d58c48b5cbe99bdb17914d0eb723c9cd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9b860214d58c48b5cbe99bdb17914d0eb723c9cd", "type": "zip", "shasum": "", "reference": "9b860214d58c48b5cbe99bdb17914d0eb723c9cd"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.12.1"}, "time": "2020-06-23T00:24:34+00:00"}, {"version": "3.12.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d46283075d76ed244f7825b378eeb1cee246af73"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d46283075d76ed244f7825b378eeb1cee246af73", "type": "zip", "shasum": "", "reference": "d46283075d76ed244f7825b378eeb1cee246af73"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.12.0"}, "time": "2020-06-22T15:39:46+00:00"}, {"version": "3.11.7", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "dddc15969452fb7e832722cb6cf047781ac91e79"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/dddc15969452fb7e832722cb6cf047781ac91e79", "type": "zip", "shasum": "", "reference": "dddc15969452fb7e832722cb6cf047781ac91e79"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2020-06-22T06:10:03+00:00"}, {"version": "3.11.6", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7fc1f50f54bd6b174b1c43a37c1b0b151915d55c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7fc1f50f54bd6b174b1c43a37c1b0b151915d55c", "type": "zip", "shasum": "", "reference": "7fc1f50f54bd6b174b1c43a37c1b0b151915d55c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.11.6"}, "time": "2020-06-17T20:40:35+00:00"}, {"version": "3.11.5", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3c60609c218d4d4b3b257728b8089094e5c6c6c2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3c60609c218d4d4b3b257728b8089094e5c6c6c2", "type": "zip", "shasum": "", "reference": "3c60609c218d4d4b3b257728b8089094e5c6c6c2"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2020-05-27T15:12:09+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4", "composer/xdebug-handler": "^1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0 || ^2.0", "nikic/php-parser": "^4.3", "ocramius/package-versions": "^1.2", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}}, {"version": "3.11.4", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "58e1d8e68e5098bf4fbfdfb420c38d563f882549"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/58e1d8e68e5098bf4fbfdfb420c38d563f882549", "type": "zip", "shasum": "", "reference": "58e1d8e68e5098bf4fbfdfb420c38d563f882549"}, "time": "2020-05-11T13:39:25+00:00", "autoload": {"files": ["src/functions.php", "src/spl_object_id.php"], "psr-4": {"Psalm\\": "src/Psalm", "Psalm\\Plugin\\": "src/Psalm/Plugin"}}}, {"version": "3.11.3", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "813302206a1b9db70ab954218ec2964373b8e001"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/813302206a1b9db70ab954218ec2964373b8e001", "type": "zip", "shasum": "", "reference": "813302206a1b9db70ab954218ec2964373b8e001"}, "time": "2020-05-11T13:08:53+00:00"}, {"version": "3.11.2", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d470903722cfcbc1cd04744c5491d3e6d13ec3d9"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d470903722cfcbc1cd04744c5491d3e6d13ec3d9", "type": "zip", "shasum": "", "reference": "d470903722cfcbc1cd04744c5491d3e6d13ec3d9"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.11.2"}, "time": "2020-04-13T12:47:11+00:00"}, {"version": "3.11.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8533b866ab1a46e5a69a4692ff57f10529dbfbeb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8533b866ab1a46e5a69a4692ff57f10529dbfbeb", "type": "zip", "shasum": "", "reference": "8533b866ab1a46e5a69a4692ff57f10529dbfbeb"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.11.1"}, "time": "2020-04-13T02:19:49+00:00"}, {"version": "3.11.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5bc9b095d1a6f1a2e0890353fc2c6cd7de62371d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5bc9b095d1a6f1a2e0890353fc2c6cd7de62371d", "type": "zip", "shasum": "", "reference": "5bc9b095d1a6f1a2e0890353fc2c6cd7de62371d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2020-04-12T18:43:12+00:00"}, {"version": "3.10.1", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "eeed5ecccc10131397f0eb7ee6da810c0be3a7fc"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/eeed5ecccc10131397f0eb7ee6da810c0be3a7fc", "type": "zip", "shasum": "", "reference": "eeed5ecccc10131397f0eb7ee6da810c0be3a7fc"}, "time": "2020-03-23T11:40:30+00:00", "require": {"php": "^7.1.3|^8", "ext-simplexml": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/semver": "^1.4", "composer/xdebug-handler": "^1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0", "nikic/php-parser": "^4.3", "ocramius/package-versions": "^1.2", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0 || ^4.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.2", "brianium/paratest": "^4.0.0", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.5", "psalm/plugin-phpunit": "^0.9", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.10.0", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6058725256c24841c3d5dad4474696ded73a9968"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6058725256c24841c3d5dad4474696ded73a9968", "type": "zip", "shasum": "", "reference": "6058725256c24841c3d5dad4474696ded73a9968"}, "time": "2020-03-22T14:44:48+00:00"}, {"version": "3.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0cfe565d0afbcd31eadcc281b9017b5692911661"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0cfe565d0afbcd31eadcc281b9017b5692911661", "type": "zip", "shasum": "", "reference": "0cfe565d0afbcd31eadcc281b9017b5692911661"}, "time": "2020-03-09T22:59:56+00:00"}, {"version": "3.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "352bd3f5c5789db04e4010856c2f4e01ed354f4e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/352bd3f5c5789db04e4010856c2f4e01ed354f4e", "type": "zip", "shasum": "", "reference": "352bd3f5c5789db04e4010856c2f4e01ed354f4e"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.9.4"}, "time": "2020-03-06T20:23:11+00:00"}, {"version": "3.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2e4154d76e24d1b4e59e6cc2bebef7790cb9e550"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2e4154d76e24d1b4e59e6cc2bebef7790cb9e550", "type": "zip", "shasum": "", "reference": "2e4154d76e24d1b4e59e6cc2bebef7790cb9e550"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.9.3"}, "time": "2020-02-19T01:30:37+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "ext-curl": "*", "phpmyadmin/sql-parser": "5.1.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.0", "psalm/plugin-phpunit": "^0.9", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "87d8947ff36d8b71f54b7e46b93384cf010681a0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/87d8947ff36d8b71f54b7e46b93384cf010681a0", "type": "zip", "shasum": "", "reference": "87d8947ff36d8b71f54b7e46b93384cf010681a0"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2020-02-18T20:08:32+00:00"}, {"version": "3.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "201d54f23293bac70c9b8b75985114449b3b56eb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/201d54f23293bac70c9b8b75985114449b3b56eb", "type": "zip", "shasum": "", "reference": "201d54f23293bac70c9b8b75985114449b3b56eb"}, "time": "2020-02-18T14:40:21+00:00"}, {"version": "3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9866b292a40bc0258b1483428ea2f19baa7aed51"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9866b292a40bc0258b1483428ea2f19baa7aed51", "type": "zip", "shasum": "", "reference": "9866b292a40bc0258b1483428ea2f19baa7aed51"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.9.0"}, "time": "2020-02-18T04:19:47+00:00"}, {"version": "3.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e6ec5fa22a7b9e61670a24d07b3119aff80dcd89"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e6ec5fa22a7b9e61670a24d07b3119aff80dcd89", "type": "zip", "shasum": "", "reference": "e6ec5fa22a7b9e61670a24d07b3119aff80dcd89"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.8.5"}, "time": "2020-02-07T17:15:50+00:00", "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "ext-curl": "*", "phpmyadmin/sql-parser": "^5.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5.16 || ^8.0", "psalm/plugin-phpunit": "^0.6", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}, "funding": "__unset"}, {"version": "3.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c2972dd759aa98561b75a5d68acfc4b634152464"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c2972dd759aa98561b75a5d68acfc4b634152464", "type": "zip", "shasum": "", "reference": "c2972dd759aa98561b75a5d68acfc4b634152464"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.8.4"}, "time": "2020-02-07T15:56:31+00:00"}, {"version": "3.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "389af1bfc739bfdff3f9e3dc7bd6499aee51a831"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/389af1bfc739bfdff3f9e3dc7bd6499aee51a831", "type": "zip", "shasum": "", "reference": "389af1bfc739bfdff3f9e3dc7bd6499aee51a831"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.8.3"}, "time": "2020-01-15T03:46:19+00:00", "require": {"php": "^7.1.3", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "composer/xdebug-handler": "^1.1", "felixfbecker/advanced-json-rpc": "^3.0.3", "felixfbecker/language-server-protocol": "^1.4", "netresearch/jsonmapper": "^1.0", "nikic/php-parser": "^4.3", "ocramius/package-versions": "^1.2", "openlss/lib-array2xml": "^1.0", "sebastian/diff": "^3.0", "symfony/console": "^3.4.17 || ^4.1.6 || ^5.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "ext-curl": "*", "phpmyadmin/sql-parser": "^5.0", "phpspec/prophecy": ">=1.9.0", "phpunit/phpunit": "^7.5 || ^8.0", "psalm/plugin-phpunit": "^0.6", "slevomat/coding-standard": "^5.0", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.3"}}, {"version": "3.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "90d6b73fd8062432030ef39b7b6694b3902daa31"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/90d6b73fd8062432030ef39b7b6694b3902daa31", "type": "zip", "shasum": "", "reference": "90d6b73fd8062432030ef39b7b6694b3902daa31"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.8.2"}, "time": "2020-01-07T13:50:31+00:00", "bin": ["psalm", "psalter", "psalm-language-server", "psalm-plugin", "psalm-refactor"], "require": {"php": "^7.1.3", "ext-dom": "*", "ext-simplexml": "*", "ext-json": "*", "ext-libxml": "*", "ext-tokenizer": "*", "nikic/php-parser": "^4.3", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.4", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0||^5.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "phpspec/prophecy": ">=1.9.0", "squizlabs/php_codesniffer": "^3.5", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.6", "phpmyadmin/sql-parser": "^5.0", "symfony/process": "^4.3", "slevomat/coding-standard": "^5.0", "friendsofphp/php-cs-fixer": "^2.15", "ext-curl": "*"}}, {"version": "3.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8e54e3aa060fc490d86d0e2abbf62750516d40fd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8e54e3aa060fc490d86d0e2abbf62750516d40fd", "type": "zip", "shasum": "", "reference": "8e54e3aa060fc490d86d0e2abbf62750516d40fd"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-12-29T16:11:07+00:00", "require": {"php": "^7.1.3", "ext-dom": "*", "nikic/php-parser": "^4.3", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.4", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0||^5.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bdb2f3c2be7bd56b180535dca33d72b74ffe1af7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bdb2f3c2be7bd56b180535dca33d72b74ffe1af7", "type": "zip", "shasum": "", "reference": "bdb2f3c2be7bd56b180535dca33d72b74ffe1af7"}, "time": "2019-12-27T22:10:16+00:00", "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.5", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.6", "phpmyadmin/sql-parser": "^5.0", "symfony/process": "^4.3", "slevomat/coding-standard": "^5.0", "friendsofphp/php-cs-fixer": "^2.15", "ext-curl": "*"}}, {"version": "3.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d9cae720c1af31db9ba27c2bc1fcf9b0dd092fb0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d9cae720c1af31db9ba27c2bc1fcf9b0dd092fb0", "type": "zip", "shasum": "", "reference": "d9cae720c1af31db9ba27c2bc1fcf9b0dd092fb0"}, "time": "2019-12-03T13:33:31+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Psalm\\": "src/Psalm", "Psalm\\Plugin\\": "src/Psalm/Plugin"}}, "require": {"php": "^7.1.3", "nikic/php-parser": "^4.3", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.4", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0||^5.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8e6aa8a07d148dcedf58cccf32ce4b602b422035"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8e6aa8a07d148dcedf58cccf32ce4b602b422035", "type": "zip", "shasum": "", "reference": "8e6aa8a07d148dcedf58cccf32ce4b602b422035"}, "time": "2019-12-02T05:11:45+00:00"}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a154191922d14dcdde8ef348520a88d735d82304"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a154191922d14dcdde8ef348520a88d735d82304", "type": "zip", "shasum": "", "reference": "a154191922d14dcdde8ef348520a88d735d82304"}, "time": "2019-11-27T14:18:47+00:00", "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "3.4.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.6", "phpmyadmin/sql-parser": "^5.0", "symfony/process": "^4.3", "slevomat/coding-standard": "^5.0", "friendsofphp/php-cs-fixer": "^2.15", "ext-curl": "*"}}, {"version": "3.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5e17a9af104de39321c47f73b253cece9df1cf2c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5e17a9af104de39321c47f73b253cece9df1cf2c", "type": "zip", "shasum": "", "reference": "5e17a9af104de39321c47f73b253cece9df1cf2c"}, "time": "2019-11-14T19:44:29+00:00", "require": {"php": "^7.1.3", "nikic/php-parser": "4.2.*", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.4", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b1aae0d1a5a11908e0e5f9eb6795ab7bb3f43577"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b1aae0d1a5a11908e0e5f9eb6795ab7bb3f43577", "type": "zip", "shasum": "", "reference": "b1aae0d1a5a11908e0e5f9eb6795ab7bb3f43577"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.6.5"}, "time": "2019-11-12T05:52:10+00:00"}, {"version": "3.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3e98c800ec72e06eed7546956823ff395fc30c75"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3e98c800ec72e06eed7546956823ff395fc30c75", "type": "zip", "shasum": "", "reference": "3e98c800ec72e06eed7546956823ff395fc30c75"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.6.4"}, "time": "2019-11-06T18:13:02+00:00", "require": {"php": "^7.1.3", "nikic/php-parser": "^4.2", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.4", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d8ae33957d5806263b8192538e35a8c5f52668c9"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d8ae33957d5806263b8192538e35a8c5f52668c9", "type": "zip", "shasum": "", "reference": "d8ae33957d5806263b8192538e35a8c5f52668c9"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.6.3"}, "time": "2019-11-04T20:14:07+00:00"}, {"version": "3.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "05ace258176795e87ef07b4262b5ceba3fd27de3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/05ace258176795e87ef07b4262b5ceba3fd27de3", "type": "zip", "shasum": "", "reference": "05ace258176795e87ef07b4262b5ceba3fd27de3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-10-20T20:19:01+00:00"}, {"version": "3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7857b07f91bed5afae98574243fed7ec4088d623"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7857b07f91bed5afae98574243fed7ec4088d623", "type": "zip", "shasum": "", "reference": "7857b07f91bed5afae98574243fed7ec4088d623"}, "time": "2019-10-11T12:24:35+00:00"}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "67b32f463ef95ae7b28b2f73886488a32055e6a5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/67b32f463ef95ae7b28b2f73886488a32055e6a5", "type": "zip", "shasum": "", "reference": "67b32f463ef95ae7b28b2f73886488a32055e6a5"}, "time": "2019-10-10T14:57:43+00:00"}, {"version": "3.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c3e781c4a06cbb17dc32068eb5d6de075f6babdc"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c3e781c4a06cbb17dc32068eb5d6de075f6babdc", "type": "zip", "shasum": "", "reference": "c3e781c4a06cbb17dc32068eb5d6de075f6babdc"}, "time": "2019-09-27T13:22:06+00:00"}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "648dda67ed7aca1d4ea243258f0ee7f2844ac60a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/648dda67ed7aca1d4ea243258f0ee7f2844ac60a", "type": "zip", "shasum": "", "reference": "648dda67ed7aca1d4ea243258f0ee7f2844ac60a"}, "time": "2019-09-25T17:12:29+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "deb36e8b273d4b25601f1991482a69d412e3169f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/deb36e8b273d4b25601f1991482a69d412e3169f", "type": "zip", "shasum": "", "reference": "deb36e8b273d4b25601f1991482a69d412e3169f"}, "time": "2019-09-10T01:28:56+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.2", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.4", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "3.4.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.6", "phpmyadmin/sql-parser": "^5.0", "symfony/process": "^4.3", "slevomat/coding-standard": "^5.0", "friendsofphp/php-cs-fixer": "^2.15"}}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f8b8f35c031e2fad6754a961a9fad2708649d8da"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f8b8f35c031e2fad6754a961a9fad2708649d8da", "type": "zip", "shasum": "", "reference": "f8b8f35c031e2fad6754a961a9fad2708649d8da"}, "time": "2019-09-08T19:25:12+00:00"}, {"version": "3.4.12", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "86e5e50c1bb492045e70f2ebe1da3cad06e4e9b2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/86e5e50c1bb492045e70f2ebe1da3cad06e4e9b2", "type": "zip", "shasum": "", "reference": "86e5e50c1bb492045e70f2ebe1da3cad06e4e9b2"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.12"}, "time": "2019-08-20T18:26:32+00:00"}, {"version": "3.4.11", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "85c9b6bb442039c773120059ae35a31f8ef9d488"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/85c9b6bb442039c773120059ae35a31f8ef9d488", "type": "zip", "shasum": "", "reference": "85c9b6bb442039c773120059ae35a31f8ef9d488"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.11"}, "time": "2019-08-09T15:40:46+00:00"}, {"version": "3.4.10", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c283f0877d543e7ab738d231ba6a3cdce5e1039a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c283f0877d543e7ab738d231ba6a3cdce5e1039a", "type": "zip", "shasum": "", "reference": "c283f0877d543e7ab738d231ba6a3cdce5e1039a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-07-22T22:04:52+00:00"}, {"version": "3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "aa0efcf026e353ce2a4b66c4d3d81caebbf0966a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/aa0efcf026e353ce2a4b66c4d3d81caebbf0966a", "type": "zip", "shasum": "", "reference": "aa0efcf026e353ce2a4b66c4d3d81caebbf0966a"}, "time": "2019-07-09T11:39:03+00:00"}, {"version": "3.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "173a40e76939a20b935b23148b4e248dc708adcd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/173a40e76939a20b935b23148b4e248dc708adcd", "type": "zip", "shasum": "", "reference": "173a40e76939a20b935b23148b4e248dc708adcd"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.8"}, "time": "2019-07-08T01:20:12+00:00"}, {"version": "3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "caf7737bbe5a5e9cbdfcc1ada83ea576ca07c9ff"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/caf7737bbe5a5e9cbdfcc1ada83ea576ca07c9ff", "type": "zip", "shasum": "", "reference": "caf7737bbe5a5e9cbdfcc1ada83ea576ca07c9ff"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.7"}, "time": "2019-06-28T15:25:29+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.2", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.3", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "3.4.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.6", "phpmyadmin/sql-parser": "^5.0", "symfony/process": "^4.3", "slevomat/coding-standard": "^5.0"}}, {"version": "3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6b32565a9bf199049d070a5197c77f266170844d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6b32565a9bf199049d070a5197c77f266170844d", "type": "zip", "shasum": "", "reference": "6b32565a9bf199049d070a5197c77f266170844d"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-06-27T17:36:39+00:00"}, {"version": "3.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a0866da88e605adaa648e9f1ad37f4990650f55c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a0866da88e605adaa648e9f1ad37f4990650f55c", "type": "zip", "shasum": "", "reference": "a0866da88e605adaa648e9f1ad37f4990650f55c"}, "time": "2019-06-16T16:45:02+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.2", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "3.4.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.6", "phpmyadmin/sql-parser": "^5.0"}}, {"version": "3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c21e9917fa05ba4d97da464725471f53ab4fe0d8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c21e9917fa05ba4d97da464725471f53ab4fe0d8", "type": "zip", "shasum": "", "reference": "c21e9917fa05ba4d97da464725471f53ab4fe0d8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.4"}, "time": "2019-06-10T18:41:21+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^1.0", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e1255db32a20bbf96c66fd29569f99112596c509"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e1255db32a20bbf96c66fd29569f99112596c509", "type": "zip", "shasum": "", "reference": "e1255db32a20bbf96c66fd29569f99112596c509"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-06-10T04:02:36+00:00", "bin": ["psalm", "psalter", "psalm-language-server", "psalm-plugin"]}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f327d75116a15e88431668c36b9613f1e3dd62a4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f327d75116a15e88431668c36b9613f1e3dd62a4", "type": "zip", "shasum": "", "reference": "f327d75116a15e88431668c36b9613f1e3dd62a4"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.2"}, "time": "2019-06-05T12:50:24+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "99a2d866611c6478490bfba1377a30d527e6b874"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/99a2d866611c6478490bfba1377a30d527e6b874", "type": "zip", "shasum": "", "reference": "99a2d866611c6478490bfba1377a30d527e6b874"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.4.1"}, "time": "2019-06-04T05:58:50+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||4.0.*||4.1.*||4.2.*", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "99e6cd819f829babf50c5852d6f62d40b1fec9d9"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/99e6cd819f829babf50c5852d6f62d40b1fec9d9", "type": "zip", "shasum": "", "reference": "99e6cd819f829babf50c5852d6f62d40b1fec9d9"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-06-03T16:30:20+00:00", "require": {"php": "^7.1", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "ocramius/package-versions": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "sebastian/diff": "^3.0"}}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "201030928de5001941a59eddce77b9d326bc1880"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/201030928de5001941a59eddce77b9d326bc1880", "type": "zip", "shasum": "", "reference": "201030928de5001941a59eddce77b9d326bc1880"}, "time": "2019-05-28T19:46:56+00:00", "autoload": {"psr-4": {"Psalm\\": "src/Psalm", "Psalm\\Plugin\\": "src/Psalm/Plugin"}}}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "602bae3bb3677c8f35d6489a528fcc5f3e901b06"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/602bae3bb3677c8f35d6489a528fcc5f3e901b06", "type": "zip", "shasum": "", "reference": "602bae3bb3677c8f35d6489a528fcc5f3e901b06"}, "time": "2019-05-28T17:16:09+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a1eb191f57cd6ac1963ce7818d010f981564bb74"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a1eb191f57cd6ac1963ce7818d010f981564bb74", "type": "zip", "shasum": "", "reference": "a1eb191f57cd6ac1963ce7818d010f981564bb74"}, "time": "2019-05-21T03:14:41+00:00"}, {"version": "3.2.12", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "fe0f352132f798512ced19faf75cbfc84e4aabe7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/fe0f352132f798512ced19faf75cbfc84e4aabe7", "type": "zip", "shasum": "", "reference": "fe0f352132f798512ced19faf75cbfc84e4aabe7"}, "time": "2019-05-13T15:00:17+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.3||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "phpmyadmin/sql-parser": "^4.0"}, "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "3.4.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.5.5"}}, {"version": "3.2.11", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "111abf82daaf90f785b3a67ef847eb509b224d9b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/111abf82daaf90f785b3a67ef847eb509b224d9b", "type": "zip", "shasum": "", "reference": "111abf82daaf90f785b3a67ef847eb509b224d9b"}, "time": "2019-05-08T18:26:52+00:00"}, {"version": "3.2.10", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b9bece4cbcb3f342c8412618f73ca02db98064e8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b9bece4cbcb3f342c8412618f73ca02db98064e8", "type": "zip", "shasum": "", "reference": "b9bece4cbcb3f342c8412618f73ca02db98064e8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.2.10"}, "time": "2019-04-29T16:19:51+00:00"}, {"version": "3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "473c8cb83209de1b66a1487400c0ea47a2ee65cc"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/473c8cb83209de1b66a1487400c0ea47a2ee65cc", "type": "zip", "shasum": "", "reference": "473c8cb83209de1b66a1487400c0ea47a2ee65cc"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-04-22T17:18:19+00:00", "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "3.4.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.5.1"}}, {"version": "3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "29b70442b11e3e66113935a2ee22e165a70c74a4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/29b70442b11e3e66113935a2ee22e165a70c74a4", "type": "zip", "shasum": "", "reference": "29b70442b11e3e66113935a2ee22e165a70c74a4"}, "time": "2019-04-20T21:54:17+00:00"}, {"version": "3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6e010d9db95275725e5ad6409c387eb173091f72"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6e010d9db95275725e5ad6409c387eb173091f72", "type": "zip", "shasum": "", "reference": "6e010d9db95275725e5ad6409c387eb173091f72"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.2.7"}, "time": "2019-04-12T15:32:35+00:00"}, {"version": "3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7f8b47c5cf0b95ef23ddadbda3e62ea153c470cb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7f8b47c5cf0b95ef23ddadbda3e62ea153c470cb", "type": "zip", "shasum": "", "reference": "7f8b47c5cf0b95ef23ddadbda3e62ea153c470cb"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-04-09T22:09:57+00:00"}, {"version": "3.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d8411dc510f948dea463953d87a71d65e12fade8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d8411dc510f948dea463953d87a71d65e12fade8", "type": "zip", "shasum": "", "reference": "d8411dc510f948dea463953d87a71d65e12fade8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.2.5"}, "time": "2019-04-01T20:34:44+00:00"}, {"version": "3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3a11e1ff5b9539303ffdecafdc3d24c33fa81073"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3a11e1ff5b9539303ffdecafdc3d24c33fa81073", "type": "zip", "shasum": "", "reference": "3a11e1ff5b9539303ffdecafdc3d24c33fa81073"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.2.4"}, "time": "2019-03-25T20:32:36+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.0||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5", "phpmyadmin/sql-parser": "^4.0"}}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "691d71cdb416bc971270d96ad588abe37c4e6085"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/691d71cdb416bc971270d96ad588abe37c4e6085", "type": "zip", "shasum": "", "reference": "691d71cdb416bc971270d96ad588abe37c4e6085"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/3.2.3"}, "time": "2019-03-25T15:49:05+00:00"}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f0ddc6f3bc35ba7ecb99232903907a1d5ec1c8e8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f0ddc6f3bc35ba7ecb99232903907a1d5ec1c8e8", "type": "zip", "shasum": "", "reference": "f0ddc6f3bc35ba7ecb99232903907a1d5ec1c8e8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2019-03-17T22:14:30+00:00", "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "^3.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "^0.5.1"}}, {"version": "3.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3704e75049703e861cda4585b66d49e3eb6810bd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3704e75049703e861cda4585b66d49e3eb6810bd", "type": "zip", "shasum": "", "reference": "3704e75049703e861cda4585b66d49e3eb6810bd"}, "time": "2019-03-14T14:23:26+00:00"}, {"version": "3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6efa978680b891ef4e4ca5718323073d809cea01"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6efa978680b891ef4e4ca5718323073d809cea01", "type": "zip", "shasum": "", "reference": "6efa978680b891ef4e4ca5718323073d809cea01"}, "time": "2019-03-12T12:55:38+00:00"}, {"version": "3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e22f216a5bf1f3041c1331f60ae86fa2570d8c27"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e22f216a5bf1f3041c1331f60ae86fa2570d8c27", "type": "zip", "shasum": "", "reference": "e22f216a5bf1f3041c1331f60ae86fa2570d8c27"}, "time": "2019-03-11T15:39:33+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "aeab6e292bc6102ff994ca4204ebcdd059b92c1c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/aeab6e292bc6102ff994ca4204ebcdd059b92c1c", "type": "zip", "shasum": "", "reference": "aeab6e292bc6102ff994ca4204ebcdd059b92c1c"}, "time": "2019-03-04T05:17:45+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.0||^4.0", "amphp/amp": "^2.1", "amphp/byte-stream": "^1.5"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "564da6193726d0b2d70e2824d81411bc118efd43"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/564da6193726d0b2d70e2824d81411bc118efd43", "type": "zip", "shasum": "", "reference": "564da6193726d0b2d70e2824d81411bc118efd43"}, "time": "2019-03-01T14:24:52+00:00", "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "^3.0", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "3.0.17", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f49be31aeb7c2e063ae732521020247e769bf83d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f49be31aeb7c2e063ae732521020247e769bf83d", "type": "zip", "shasum": "", "reference": "f49be31aeb7c2e063ae732521020247e769bf83d"}, "time": "2019-02-18T21:52:09+00:00"}, {"version": "3.0.16", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0f45d14fead69db4e7af55037a0f304080aef01f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0f45d14fead69db4e7af55037a0f304080aef01f", "type": "zip", "shasum": "", "reference": "0f45d14fead69db4e7af55037a0f304080aef01f"}, "time": "2019-02-15T16:07:08+00:00"}, {"version": "3.0.15", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c310a2dd86660944b3f15fb8486ab73d45066618"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c310a2dd86660944b3f15fb8486ab73d45066618", "type": "zip", "shasum": "", "reference": "c310a2dd86660944b3f15fb8486ab73d45066618"}, "time": "2019-02-11T23:39:19+00:00"}, {"version": "3.0.14", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6d5c99545874de2cf167b88a993f5fda6783cef2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6d5c99545874de2cf167b88a993f5fda6783cef2", "type": "zip", "shasum": "", "reference": "6d5c99545874de2cf167b88a993f5fda6783cef2"}, "time": "2019-02-05T13:02:06+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "sabre/event": "^5.0.1", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.0||^4.0"}}, {"version": "3.0.13", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0bb5143efd2350bcbb01f06f5374a4c6cf5a7a4b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0bb5143efd2350bcbb01f06f5374a4c6cf5a7a4b", "type": "zip", "shasum": "", "reference": "0bb5143efd2350bcbb01f06f5374a4c6cf5a7a4b"}, "time": "2019-01-29T15:34:31+00:00"}, {"version": "3.0.12", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "279912cb5566c4b5394af9ac4f8cbd8c9a2b9450"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/279912cb5566c4b5394af9ac4f8cbd8c9a2b9450", "type": "zip", "shasum": "", "reference": "279912cb5566c4b5394af9ac4f8cbd8c9a2b9450"}, "time": "2019-01-20T17:50:47+00:00"}, {"version": "3.0.11", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3ca5ec9281ab2ab3b3caf83bfdd7ed085d3c2ecb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3ca5ec9281ab2ab3b3caf83bfdd7ed085d3c2ecb", "type": "zip", "shasum": "", "reference": "3ca5ec9281ab2ab3b3caf83bfdd7ed085d3c2ecb"}, "time": "2019-01-13T19:40:21+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0.2 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "sabre/event": "^5.0.1", "sabre/uri": "^2.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3", "symfony/console": "^3.0||^4.0"}}, {"version": "3.0.10", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d3324b42fa996bcf4b5a532086c0777153679e04"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d3324b42fa996bcf4b5a532086c0777153679e04", "type": "zip", "shasum": "", "reference": "d3324b42fa996bcf4b5a532086c0777153679e04"}, "time": "2019-01-09T13:49:02+00:00"}, {"version": "3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "195cb289edf071fd14195ecf3951eabf634b577a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/195cb289edf071fd14195ecf3951eabf634b577a", "type": "zip", "shasum": "", "reference": "195cb289edf071fd14195ecf3951eabf634b577a"}, "time": "2019-01-07T13:38:56+00:00"}, {"version": "3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c39446330e88c1b7f38c7e17568c4a82982d166c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c39446330e88c1b7f38c7e17568c4a82982d166c", "type": "zip", "shasum": "", "reference": "c39446330e88c1b7f38c7e17568c4a82982d166c"}, "time": "2019-01-02T19:24:45+00:00"}, {"version": "3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "10a36def968a4e642b1afec74e3f0be602c79f58"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/10a36def968a4e642b1afec74e3f0be602c79f58", "type": "zip", "shasum": "", "reference": "10a36def968a4e642b1afec74e3f0be602c79f58"}, "time": "2018-12-21T18:53:22+00:00"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2e30d7b4a0a3fbc0707b3e39196b8c7a01b5b447"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2e30d7b4a0a3fbc0707b3e39196b8c7a01b5b447", "type": "zip", "shasum": "", "reference": "2e30d7b4a0a3fbc0707b3e39196b8c7a01b5b447"}, "time": "2018-12-20T21:03:21+00:00"}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d9a42cf33d3f2c0bc2d645bf5a3acd4f0b4f5984"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d9a42cf33d3f2c0bc2d645bf5a3acd4f0b4f5984", "type": "zip", "shasum": "", "reference": "d9a42cf33d3f2c0bc2d645bf5a3acd4f0b4f5984"}, "time": "2018-12-14T17:35:02+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6c4377699d1e3f87148a1cec436a206ac4c3e050"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6c4377699d1e3f87148a1cec436a206ac4c3e050", "type": "zip", "shasum": "", "reference": "6c4377699d1e3f87148a1cec436a206ac4c3e050"}, "time": "2018-12-14T16:20:31+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f9753e1517efd2f7282bf9c7c141fd7224e242ec"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f9753e1517efd2f7282bf9c7c141fd7224e242ec", "type": "zip", "shasum": "", "reference": "f9753e1517efd2f7282bf9c7c141fd7224e242ec"}, "time": "2018-12-11T04:20:46+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bcc062cfc5a7561a9a2a763eb693be6b2b29e28a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bcc062cfc5a7561a9a2a763eb693be6b2b29e28a", "type": "zip", "shasum": "", "reference": "bcc062cfc5a7561a9a2a763eb693be6b2b29e28a"}, "time": "2018-11-27T05:46:19+00:00", "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0", "bamarni/composer-bin-plugin": "^1.2", "psalm/plugin-phpunit": "dev-master"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b982090e10b7c860127ee580bd08775049438187"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b982090e10b7c860127ee580bd08775049438187", "type": "zip", "shasum": "", "reference": "b982090e10b7c860127ee580bd08775049438187"}, "time": "2018-11-20T03:57:59+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-master": "3.x-dev"}}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0f90309819b604f2cc73e62c79cd3286846227ff"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0f90309819b604f2cc73e62c79cd3286846227ff", "type": "zip", "shasum": "", "reference": "0f90309819b604f2cc73e62c79cd3286846227ff"}, "time": "2018-11-18T22:18:05+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-master": "2.x-dev"}}}, {"version": "2.0.17", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ead7d62d489d58edb4fa03b21e4f0bc87a476211"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ead7d62d489d58edb4fa03b21e4f0bc87a476211", "type": "zip", "shasum": "", "reference": "ead7d62d489d58edb4fa03b21e4f0bc87a476211"}, "time": "2018-11-09T19:18:46+00:00", "autoload": {"psr-4": {"Psalm\\": "src/Psalm"}}, "bin": ["psalm", "psalter", "psalm-language-server"], "require": {"php": "^7.0", "nikic/php-parser": "^4.0.3 || ^4.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1", "felixfbecker/language-server-protocol": "^1.2", "felixfbecker/advanced-json-rpc": "^3.0.3", "netresearch/jsonmapper": "^1.0", "sabre/event": "^5.0.1", "sabre/uri": "^2.0", "webmozart/glob": "^4.1", "webmozart/path-util": "^2.3"}, "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f7a37d05b93f85a8310c908fddf603b4ca82f512"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f7a37d05b93f85a8310c908fddf603b4ca82f512", "type": "zip", "shasum": "", "reference": "f7a37d05b93f85a8310c908fddf603b4ca82f512"}, "time": "2018-11-02T17:09:04+00:00"}, {"version": "2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8b525a066b107b9936f338ec7c2c5618cf9a1764"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8b525a066b107b9936f338ec7c2c5618cf9a1764", "type": "zip", "shasum": "", "reference": "8b525a066b107b9936f338ec7c2c5618cf9a1764"}, "time": "2018-10-19T18:04:22+00:00"}, {"version": "2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "792144def40d678b7fa7c8a52da6cdbd34dd415a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/792144def40d678b7fa7c8a52da6cdbd34dd415a", "type": "zip", "shasum": "", "reference": "792144def40d678b7fa7c8a52da6cdbd34dd415a"}, "time": "2018-10-10T18:22:06+00:00", "bin": ["psalm", "psalter"], "require": {"php": "^7.0", "nikic/php-parser": "^4.0", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1"}}, {"version": "2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "afebefba19ca8c604f5c2fbc5ede44f4c12e853b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/afebefba19ca8c604f5c2fbc5ede44f4c12e853b", "type": "zip", "shasum": "", "reference": "afebefba19ca8c604f5c2fbc5ede44f4c12e853b"}, "time": "2018-10-07T04:42:25+00:00"}, {"version": "2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "db3ddaa42cbc81ffb6f9f8290d01c4db6dbbc3e6"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/db3ddaa42cbc81ffb6f9f8290d01c4db6dbbc3e6", "type": "zip", "shasum": "", "reference": "db3ddaa42cbc81ffb6f9f8290d01c4db6dbbc3e6"}, "time": "2018-09-21T15:35:51+00:00"}, {"version": "2.0.11", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3e69a333c9683596ad4a4325e0039374516295a0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3e69a333c9683596ad4a4325e0039374516295a0", "type": "zip", "shasum": "", "reference": "3e69a333c9683596ad4a4325e0039374516295a0"}, "time": "2018-08-30T00:54:48+00:00"}, {"version": "2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "366f625c83000a701ce0a69b8a05b387c7bf4bce"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/366f625c83000a701ce0a69b8a05b387c7bf4bce", "type": "zip", "shasum": "", "reference": "366f625c83000a701ce0a69b8a05b387c7bf4bce"}, "time": "2018-08-14T15:51:17+00:00"}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d3fc9a51e0519379a27c05318e5f6fd41d46f797"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d3fc9a51e0519379a27c05318e5f6fd41d46f797", "type": "zip", "shasum": "", "reference": "d3fc9a51e0519379a27c05318e5f6fd41d46f797"}, "time": "2018-07-22T23:15:40+00:00"}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ecf4ed65e3d878ec0f7b584b172ef294d5a60433"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ecf4ed65e3d878ec0f7b584b172ef294d5a60433", "type": "zip", "shasum": "", "reference": "ecf4ed65e3d878ec0f7b584b172ef294d5a60433"}, "time": "2018-07-10T15:39:38+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.0"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "friendsofphp/php-cs-fixer": "^2.3|^2.4|^2.5|^2.6|^2.7|^2.8|^2.9", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "beeab32f60dcff5959f0578d2893ecb6466fd460"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/beeab32f60dcff5959f0578d2893ecb6466fd460", "type": "zip", "shasum": "", "reference": "beeab32f60dcff5959f0578d2893ecb6466fd460"}, "time": "2018-06-30T20:21:29+00:00", "extra": "__unset", "provide": "__unset"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "23e90edc503b2c7c4c19faef32a4d2161a7ea8c0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/23e90edc503b2c7c4c19faef32a4d2161a7ea8c0", "type": "zip", "shasum": "", "reference": "23e90edc503b2c7c4c19faef32a4d2161a7ea8c0"}, "time": "2018-06-25T04:03:09+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "26fa003fe2c16409a87b127a1eedcaf149437988"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/26fa003fe2c16409a87b127a1eedcaf149437988", "type": "zip", "shasum": "", "reference": "26fa003fe2c16409a87b127a1eedcaf149437988"}, "time": "2018-06-10T19:41:04+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ea4dc9f2b80ae71cebb7613a2005149a38e84731"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ea4dc9f2b80ae71cebb7613a2005149a38e84731", "type": "zip", "shasum": "", "reference": "ea4dc9f2b80ae71cebb7613a2005149a38e84731"}, "time": "2018-06-06T03:42:02+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "53bfafa02a1305b895a3a9b8319dc39391b30c6e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/53bfafa02a1305b895a3a9b8319dc39391b30c6e", "type": "zip", "shasum": "", "reference": "53bfafa02a1305b895a3a9b8319dc39391b30c6e"}, "time": "2018-06-04T04:15:28+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2d6eab85fa8b5185801ff6e4362523da658a2431"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2d6eab85fa8b5185801ff6e4362523da658a2431", "type": "zip", "shasum": "", "reference": "2d6eab85fa8b5185801ff6e4362523da658a2431"}, "time": "2018-05-24T18:33:41+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5c0f4a999c4764c4e172489b2c0193c623b17a5c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5c0f4a999c4764c4e172489b2c0193c623b17a5c", "type": "zip", "shasum": "", "reference": "5c0f4a999c4764c4e172489b2c0193c623b17a5c"}, "time": "2018-05-11T22:35:02+00:00", "require-dev": {"phpunit/phpunit": "^5.7.4", "friendsofphp/php-cs-fixer": "^2.3|^2.4|^2.5|^2.6|^2.7|^2.8|^2.9", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d15cf3b7f50249caf933144c8926c8e69aff3d34"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d15cf3b7f50249caf933144c8926c8e69aff3d34", "type": "zip", "shasum": "", "reference": "d15cf3b7f50249caf933144c8926c8e69aff3d34"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/1.x"}, "time": "2018-08-14T16:06:16+00:00", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-master": "2.x-dev"}}, "require": {"php": "^5.6||^7.0", "nikic/php-parser": "^3.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0", "bamarni/composer-bin-plugin": "^1.2"}, "provide": {"psalm/psalm": "self.version"}}, {"version": "1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5ca35a0635234e8bce82a6bcf14512191e70bf33"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5ca35a0635234e8bce82a6bcf14512191e70bf33", "type": "zip", "shasum": "", "reference": "5ca35a0635234e8bce82a6bcf14512191e70bf33"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2018-07-22T23:29:04+00:00", "require": {"php": "^7.0", "nikic/php-parser": "^4.0", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.1"}, "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0beeb875f9a99a2ec50e9774dca8ab32c48772ba"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0beeb875f9a99a2ec50e9774dca8ab32c48772ba", "type": "zip", "shasum": "", "reference": "0beeb875f9a99a2ec50e9774dca8ab32c48772ba"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/1.x"}, "time": "2018-07-10T15:41:43+00:00", "require": {"php": "^5.6||^7.0", "nikic/php-parser": "^3.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.0"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "friendsofphp/php-cs-fixer": "^2.3|^2.4|^2.5|^2.6|^2.7|^2.8|^2.9", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0", "bamarni/composer-bin-plugin": "^1.2"}}, {"version": "1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "eb8288b53a51528317eabe5fd75148ec3ef86973"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/eb8288b53a51528317eabe5fd75148ec3ef86973", "type": "zip", "shasum": "", "reference": "eb8288b53a51528317eabe5fd75148ec3ef86973"}, "time": "2018-07-01T21:46:49+00:00", "extra": "__unset", "provide": "__unset"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "caf5c90c1189c6281f94eb2c9e129f5e11420102"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/caf5c90c1189c6281f94eb2c9e129f5e11420102", "type": "zip", "shasum": "", "reference": "caf5c90c1189c6281f94eb2c9e129f5e11420102"}, "time": "2018-05-11T22:36:06+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.1.2", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.4", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.0"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "friendsofphp/php-cs-fixer": "^2.3|^2.4|^2.5|^2.6|^2.7|^2.8|^2.9", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f9cb79d6fe212a1d2098a049d5e7d45b21c52d2d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f9cb79d6fe212a1d2098a049d5e7d45b21c52d2d", "type": "zip", "shasum": "", "reference": "f9cb79d6fe212a1d2098a049d5e7d45b21c52d2d"}, "time": "2018-04-30T05:57:44+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e26b46dc6ae7eb6d9170770df35150438e639a09"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e26b46dc6ae7eb6d9170770df35150438e639a09", "type": "zip", "shasum": "", "reference": "e26b46dc6ae7eb6d9170770df35150438e639a09"}, "time": "2018-04-26T23:15:16+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7109364313827125f04095127887413caf0cf700"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7109364313827125f04095127887413caf0cf700", "type": "zip", "shasum": "", "reference": "7109364313827125f04095127887413caf0cf700"}, "time": "2018-04-17T19:42:42+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e58274acaa82c7508fb8b91ca28fa53191c48477"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e58274acaa82c7508fb8b91ca28fa53191c48477", "type": "zip", "shasum": "", "reference": "e58274acaa82c7508fb8b91ca28fa53191c48477"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/master"}, "time": "2018-04-11T19:11:14+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1395299e281fd462d5afd3e4b07b9ddd6f7b04e3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1395299e281fd462d5afd3e4b07b9ddd6f7b04e3", "type": "zip", "shasum": "", "reference": "1395299e281fd462d5afd3e4b07b9ddd6f7b04e3"}, "time": "2018-04-03T02:40:29+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b6499c33ed9c88baf1048f4328931decce98488e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b6499c33ed9c88baf1048f4328931decce98488e", "type": "zip", "shasum": "", "reference": "b6499c33ed9c88baf1048f4328931decce98488e"}, "time": "2018-03-22T23:09:51+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.1.2", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.3", "php-cs-fixer/diff": "^1.2", "composer/xdebug-handler": "^1.0"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "cb15f484069b6039d074fda4edc27503941c57b4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/cb15f484069b6039d074fda4edc27503941c57b4", "type": "zip", "shasum": "", "reference": "cb15f484069b6039d074fda4edc27503941c57b4"}, "time": "2018-03-21T16:42:22+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d6e0708e888fae2ec9211703c5c3d9ee301f349a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d6e0708e888fae2ec9211703c5c3d9ee301f349a", "type": "zip", "shasum": "", "reference": "d6e0708e888fae2ec9211703c5c3d9ee301f349a"}, "time": "2018-03-15T21:32:03+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "47842395502a7746831836ac656bae9381f303bb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/47842395502a7746831836ac656bae9381f303bb", "type": "zip", "shasum": "", "reference": "47842395502a7746831836ac656bae9381f303bb"}, "time": "2018-03-08T22:28:47+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "276c1a785d149c2411d021e760d34b901daa613c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/276c1a785d149c2411d021e760d34b901daa613c", "type": "zip", "shasum": "", "reference": "276c1a785d149c2411d021e760d34b901daa613c"}, "time": "2018-03-02T16:32:34+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.1.2", "composer/composer": "^1.3|^1.4|^1.5", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.3", "php-cs-fixer/diff": "^1.2"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "051fe0e9def4220134705dd86e91dafd6602c5de"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/051fe0e9def4220134705dd86e91dafd6602c5de", "type": "zip", "shasum": "", "reference": "051fe0e9def4220134705dd86e91dafd6602c5de"}, "time": "2018-02-22T18:46:26+00:00"}, {"version": "0.3.93", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a8ab9728898f529914c83d968f8b3d14af862e65"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a8ab9728898f529914c83d968f8b3d14af862e65", "type": "zip", "shasum": "", "reference": "a8ab9728898f529914c83d968f8b3d14af862e65"}, "time": "2018-02-08T20:30:24+00:00", "suggest": "__unset"}, {"version": "0.3.92", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7d6dc7252a89b3dcc8e1784991193717ee8a86ef"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7d6dc7252a89b3dcc8e1784991193717ee8a86ef", "type": "zip", "shasum": "", "reference": "7d6dc7252a89b3dcc8e1784991193717ee8a86ef"}, "time": "2018-02-04T14:22:24+00:00"}, {"version": "0.3.91", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e5bc0e61299570c3dc91a0ea93e28017245c2b3b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e5bc0e61299570c3dc91a0ea93e28017245c2b3b", "type": "zip", "shasum": "", "reference": "e5bc0e61299570c3dc91a0ea93e28017245c2b3b"}, "time": "2018-02-03T00:08:10+00:00"}, {"version": "0.3.90", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "861d907845f78c1948e700871090b7f781dfc29f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/861d907845f78c1948e700871090b7f781dfc29f", "type": "zip", "shasum": "", "reference": "861d907845f78c1948e700871090b7f781dfc29f"}, "time": "2018-02-02T16:26:55+00:00"}, {"version": "0.3.89", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "952479fbc056b6b4ee9388890da0073c1093c798"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/952479fbc056b6b4ee9388890da0073c1093c798", "type": "zip", "shasum": "", "reference": "952479fbc056b6b4ee9388890da0073c1093c798"}, "time": "2018-01-30T16:31:46+00:00"}, {"version": "0.3.88", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3482ee3b396bf6374a7b2ad509b8bcc25b2bdee2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3482ee3b396bf6374a7b2ad509b8bcc25b2bdee2", "type": "zip", "shasum": "", "reference": "3482ee3b396bf6374a7b2ad509b8bcc25b2bdee2"}, "time": "2018-01-29T01:03:47+00:00"}, {"version": "0.3.87", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2d8468cb4986701fd86d76f112574dc994874548"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2d8468cb4986701fd86d76f112574dc994874548", "type": "zip", "shasum": "", "reference": "2d8468cb4986701fd86d76f112574dc994874548"}, "time": "2018-01-26T19:08:45+00:00"}, {"version": "0.3.86", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "24cbe6a52af9da97880b310d6b65f484c7daf96a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/24cbe6a52af9da97880b310d6b65f484c7daf96a", "type": "zip", "shasum": "", "reference": "24cbe6a52af9da97880b310d6b65f484c7daf96a"}, "time": "2018-01-24T18:52:34+00:00"}, {"version": "0.3.85", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bfb31bb66989f419a326a3a179b4430fefa8582f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bfb31bb66989f419a326a3a179b4430fefa8582f", "type": "zip", "shasum": "", "reference": "bfb31bb66989f419a326a3a179b4430fefa8582f"}, "time": "2018-01-22T07:08:06+00:00"}, {"version": "0.3.84", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d8a78a70861e4431de3d48242d143d168cb0b8d5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d8a78a70861e4431de3d48242d143d168cb0b8d5", "type": "zip", "shasum": "", "reference": "d8a78a70861e4431de3d48242d143d168cb0b8d5"}, "time": "2018-01-22T03:14:29+00:00"}, {"version": "0.3.83", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "799aef628ed2b3e85f89de3caca0a578e5275768"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/799aef628ed2b3e85f89de3caca0a578e5275768", "type": "zip", "shasum": "", "reference": "799aef628ed2b3e85f89de3caca0a578e5275768"}, "time": "2018-01-20T16:48:16+00:00"}, {"version": "0.3.82", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a253ca68bcbb910eb70ca7554fa7e0783593b195"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a253ca68bcbb910eb70ca7554fa7e0783593b195", "type": "zip", "shasum": "", "reference": "a253ca68bcbb910eb70ca7554fa7e0783593b195"}, "time": "2018-01-17T21:07:46+00:00"}, {"version": "0.3.81", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "49981f63c649c24b4c09354129ad47fd6749e567"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/49981f63c649c24b4c09354129ad47fd6749e567", "type": "zip", "shasum": "", "reference": "49981f63c649c24b4c09354129ad47fd6749e567"}, "time": "2018-01-11T04:29:46+00:00"}, {"version": "0.3.80", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e679c9fb843b38c48b9ecbc489e49058bbfd9e69"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e679c9fb843b38c48b9ecbc489e49058bbfd9e69", "type": "zip", "shasum": "", "reference": "e679c9fb843b38c48b9ecbc489e49058bbfd9e69"}, "time": "2018-01-10T16:01:32+00:00"}, {"version": "0.3.79", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "916ae930e2f832e7121e258e793f0b61d79141d7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/916ae930e2f832e7121e258e793f0b61d79141d7", "type": "zip", "shasum": "", "reference": "916ae930e2f832e7121e258e793f0b61d79141d7"}, "time": "2018-01-09T15:49:29+00:00"}, {"version": "0.3.78", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3f3c1380eec9d7aa6cd1ce600fadab5e005673c0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3f3c1380eec9d7aa6cd1ce600fadab5e005673c0", "type": "zip", "shasum": "", "reference": "3f3c1380eec9d7aa6cd1ce600fadab5e005673c0"}, "time": "2018-01-09T14:22:23+00:00"}, {"version": "0.3.77", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "03ecfa2f2ff6a0c816cf2d710dd6e2dc8b4ab24a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/03ecfa2f2ff6a0c816cf2d710dd6e2dc8b4ab24a", "type": "zip", "shasum": "", "reference": "03ecfa2f2ff6a0c816cf2d710dd6e2dc8b4ab24a"}, "time": "2018-01-08T19:11:57+00:00"}, {"version": "0.3.76", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "65bd144cec588ec8980d27abe7d9d68a1d5f1547"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/65bd144cec588ec8980d27abe7d9d68a1d5f1547", "type": "zip", "shasum": "", "reference": "65bd144cec588ec8980d27abe7d9d68a1d5f1547"}, "time": "2018-01-08T16:05:29+00:00"}, {"version": "0.3.75", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c28b804633ed271ce56d50eba32c398c19c8b7f5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c28b804633ed271ce56d50eba32c398c19c8b7f5", "type": "zip", "shasum": "", "reference": "c28b804633ed271ce56d50eba32c398c19c8b7f5"}, "time": "2018-01-08T06:38:25+00:00"}, {"version": "0.3.73", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "66fa08148805d4935218b9593885dd3e0af1394d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/66fa08148805d4935218b9593885dd3e0af1394d", "type": "zip", "shasum": "", "reference": "66fa08148805d4935218b9593885dd3e0af1394d"}, "time": "2018-01-07T23:06:31+00:00"}, {"version": "0.3.72", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8da23605bb1f2ad6a6dd62562d85fffc8f6c834e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8da23605bb1f2ad6a6dd62562d85fffc8f6c834e", "type": "zip", "shasum": "", "reference": "8da23605bb1f2ad6a6dd62562d85fffc8f6c834e"}, "time": "2018-01-02T14:46:50+00:00", "bin": ["psalm"], "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.1.2", "composer/composer": "^1.3|^1.4|^1.5", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions-56": "1.2.3"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "friendsofphp/php-cs-fixer": "^2.3", "squizlabs/php_codesniffer": "^3.0", "php-coveralls/php-coveralls": "^2.0"}}, {"version": "0.3.71", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "035f528581bcbcad19a2e141016234eb66b2cd14"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/035f528581bcbcad19a2e141016234eb66b2cd14", "type": "zip", "shasum": "", "reference": "035f528581bcbcad19a2e141016234eb66b2cd14"}, "time": "2017-12-19T05:06:05+00:00", "bin": ["bin/psalm"], "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.1.2", "composer/composer": "^1.3|^1.4|^1.5", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions": "^1.2.1"}}, {"version": "0.3.70", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "72da8f655ad215e1cedd85f8f50e12bf53843ea5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/72da8f655ad215e1cedd85f8f50e12bf53843ea5", "type": "zip", "shasum": "", "reference": "72da8f655ad215e1cedd85f8f50e12bf53843ea5"}, "time": "2017-12-18T16:05:53+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.1.2", "composer/composer": "^1.3", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions": "^1.2.1"}}, {"version": "0.3.69", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "136d48f77c48a6c2a4b6d9e15795fde224af096f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/136d48f77c48a6c2a4b6d9e15795fde224af096f", "type": "zip", "shasum": "", "reference": "136d48f77c48a6c2a4b6d9e15795fde224af096f"}, "time": "2017-12-14T19:22:27+00:00"}, {"version": "0.3.68", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5f22203bd1afc4baea509ab5f8005c7fe6e1029c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5f22203bd1afc4baea509ab5f8005c7fe6e1029c", "type": "zip", "shasum": "", "reference": "5f22203bd1afc4baea509ab5f8005c7fe6e1029c"}, "time": "2017-12-12T07:10:17+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "muglug/package-versions": "^1.2.1"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "friendsofphp/php-cs-fixer": "^2.3", "squizlabs/php_codesniffer": "^3.0"}}, {"version": "0.3.67", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "46feaaf1c60f6823caa71e7da1ab0754e559fe6d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/46feaaf1c60f6823caa71e7da1ab0754e559fe6d", "type": "zip", "shasum": "", "reference": "46feaaf1c60f6823caa71e7da1ab0754e559fe6d"}, "time": "2017-12-11T01:21:21+00:00"}, {"version": "0.3.66", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d10898b853b6e3f264c9477cab07a9a13de01f58"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d10898b853b6e3f264c9477cab07a9a13de01f58", "type": "zip", "shasum": "", "reference": "d10898b853b6e3f264c9477cab07a9a13de01f58"}, "time": "2017-12-05T21:54:24+00:00"}, {"version": "0.3.65", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "de09bd846c59be5aac50c96b71fbabffb20b8848"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/de09bd846c59be5aac50c96b71fbabffb20b8848", "type": "zip", "shasum": "", "reference": "de09bd846c59be5aac50c96b71fbabffb20b8848"}, "time": "2017-12-04T13:50:59+00:00"}, {"version": "0.3.64", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3b2a1d4a3ea6d7f4d72f4ab8cb7361682d3454ce"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3b2a1d4a3ea6d7f4d72f4ab8cb7361682d3454ce", "type": "zip", "shasum": "", "reference": "3b2a1d4a3ea6d7f4d72f4ab8cb7361682d3454ce"}, "time": "2017-11-25T17:31:01+00:00"}, {"version": "0.3.63", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4b573bde1f620714d1d8065e48fe45e2190563ca"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4b573bde1f620714d1d8065e48fe45e2190563ca", "type": "zip", "shasum": "", "reference": "4b573bde1f620714d1d8065e48fe45e2190563ca"}, "time": "2017-11-06T17:04:38+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3", "openlss/lib-array2xml": "^0.0.10||^0.5.1"}}, {"version": "0.3.62", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "aeb9882281e9d0453b8adbfdeed46721acc9a296"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/aeb9882281e9d0453b8adbfdeed46721acc9a296", "type": "zip", "shasum": "", "reference": "aeb9882281e9d0453b8adbfdeed46721acc9a296"}, "time": "2017-10-29T21:44:13+00:00"}, {"version": "0.3.61", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d06e844748fe13a708d3d48b0f781ddcc0d5ba51"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d06e844748fe13a708d3d48b0f781ddcc0d5ba51", "type": "zip", "shasum": "", "reference": "d06e844748fe13a708d3d48b0f781ddcc0d5ba51"}, "time": "2017-10-26T20:05:57+00:00"}, {"version": "0.3.60", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "122b354c4c3b02054738ecc3e9f831fa263e5aa7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/122b354c4c3b02054738ecc3e9f831fa263e5aa7", "type": "zip", "shasum": "", "reference": "122b354c4c3b02054738ecc3e9f831fa263e5aa7"}, "time": "2017-10-24T04:01:43+00:00"}, {"version": "0.3.59", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3c6a8a0b24c255969a066b8f71db9e067d54bbac"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3c6a8a0b24c255969a066b8f71db9e067d54bbac", "type": "zip", "shasum": "", "reference": "3c6a8a0b24c255969a066b8f71db9e067d54bbac"}, "time": "2017-10-19T23:19:29+00:00"}, {"version": "0.3.58", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "295792d55010ec9fa1b2a70d9771f6cc2b98126c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/295792d55010ec9fa1b2a70d9771f6cc2b98126c", "type": "zip", "shasum": "", "reference": "295792d55010ec9fa1b2a70d9771f6cc2b98126c"}, "time": "2017-10-15T16:38:47+00:00"}, {"version": "0.3.57", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7ebffbcd2d5caf90558f4a7e60147974779f6f3f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7ebffbcd2d5caf90558f4a7e60147974779f6f3f", "type": "zip", "shasum": "", "reference": "7ebffbcd2d5caf90558f4a7e60147974779f6f3f"}, "time": "2017-10-07T19:06:47+00:00"}, {"version": "0.3.56", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "eb79cb717247074a67091cb6dcb7806fdfe546bd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/eb79cb717247074a67091cb6dcb7806fdfe546bd", "type": "zip", "shasum": "", "reference": "eb79cb717247074a67091cb6dcb7806fdfe546bd"}, "time": "2017-09-18T20:14:01+00:00"}, {"version": "0.3.55", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "439a1ddc97f7d29793b70e1c088f14c0ed995ab8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/439a1ddc97f7d29793b70e1c088f14c0ed995ab8", "type": "zip", "shasum": "", "reference": "439a1ddc97f7d29793b70e1c088f14c0ed995ab8"}, "time": "2017-09-11T15:58:11+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3", "openlss/lib-array2xml": "^0.5.1"}}, {"version": "0.3.54", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "622217b708f7757c1fff160f02febffda5956d9c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/622217b708f7757c1fff160f02febffda5956d9c", "type": "zip", "shasum": "", "reference": "622217b708f7757c1fff160f02febffda5956d9c"}, "time": "2017-08-22T16:38:38+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3"}}, {"version": "0.3.53", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d6e2e786db086877dd1b4308b39ad227097b4456"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d6e2e786db086877dd1b4308b39ad227097b4456", "type": "zip", "shasum": "", "reference": "d6e2e786db086877dd1b4308b39ad227097b4456"}, "time": "2017-08-11T23:09:15+00:00"}, {"version": "0.3.52", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8f9fdcb63748b68cd36b53fbd08961f4cc6a90e4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8f9fdcb63748b68cd36b53fbd08961f4cc6a90e4", "type": "zip", "shasum": "", "reference": "8f9fdcb63748b68cd36b53fbd08961f4cc6a90e4"}, "time": "2017-08-08T15:03:45+00:00"}, {"version": "0.3.51", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c8ec0dc65096cd4051e1b3298ea048bae776b090"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c8ec0dc65096cd4051e1b3298ea048bae776b090", "type": "zip", "shasum": "", "reference": "c8ec0dc65096cd4051e1b3298ea048bae776b090"}, "time": "2017-07-27T23:14:16+00:00"}, {"version": "0.3.50", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "84f1993a2ebf719b83119def665ee3d35dd2b440"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/84f1993a2ebf719b83119def665ee3d35dd2b440", "type": "zip", "shasum": "", "reference": "84f1993a2ebf719b83119def665ee3d35dd2b440"}, "time": "2017-07-27T20:59:41+00:00"}, {"version": "0.3.49", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2c1a0b197e6c3a2a99316fc5feaa3006b777e828"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2c1a0b197e6c3a2a99316fc5feaa3006b777e828", "type": "zip", "shasum": "", "reference": "2c1a0b197e6c3a2a99316fc5feaa3006b777e828"}, "time": "2017-07-27T03:47:29+00:00"}, {"version": "0.3.48", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "22daecb8ad62eddfe33bee031451a1dbaf926163"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/22daecb8ad62eddfe33bee031451a1dbaf926163", "type": "zip", "shasum": "", "reference": "22daecb8ad62eddfe33bee031451a1dbaf926163"}, "time": "2017-07-27T01:30:01+00:00"}, {"version": "0.3.47", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "581cf5f7eb93cc1f2cf81f03bc20a6ed2662c01a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/581cf5f7eb93cc1f2cf81f03bc20a6ed2662c01a", "type": "zip", "shasum": "", "reference": "581cf5f7eb93cc1f2cf81f03bc20a6ed2662c01a"}, "time": "2017-07-09T19:54:43+00:00"}, {"version": "0.3.46", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "967b51d5fc802ce503b9561f9b0893e05ce81a2c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/967b51d5fc802ce503b9561f9b0893e05ce81a2c", "type": "zip", "shasum": "", "reference": "967b51d5fc802ce503b9561f9b0893e05ce81a2c"}, "time": "2017-06-21T20:36:45+00:00"}, {"version": "0.3.45", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6b2b80d0bbdfcff2b7217e81caf1c6ae4725b580"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6b2b80d0bbdfcff2b7217e81caf1c6ae4725b580", "type": "zip", "shasum": "", "reference": "6b2b80d0bbdfcff2b7217e81caf1c6ae4725b580"}, "time": "2017-06-13T04:09:15+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3", "friendsofphp/php-cs-fixer": "^2.3", "squizlabs/php_codesniffer": "^3.0"}, "require-dev": {"phpunit/phpunit": "^5.7.4"}}, {"version": "0.3.44", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "77a44051c5e49f7a47fc82a16987288876ec518b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/77a44051c5e49f7a47fc82a16987288876ec518b", "type": "zip", "shasum": "", "reference": "77a44051c5e49f7a47fc82a16987288876ec518b"}, "time": "2017-05-22T18:50:03+00:00", "require": {"php": "^5.6 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3"}, "require-dev": {"phpunit/phpunit": "^5.7.4", "squizlabs/php_codesniffer": "^2.7"}}, {"version": "0.3.43", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "052624fa94cf8fd6adbde521f1518cc07efc4304"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/052624fa94cf8fd6adbde521f1518cc07efc4304", "type": "zip", "shasum": "", "reference": "052624fa94cf8fd6adbde521f1518cc07efc4304"}, "time": "2017-05-12T23:15:08+00:00"}, {"version": "0.3.41", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "53b81ac2e171b7b94c5bdb7af62bc19216695e4d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/53b81ac2e171b7b94c5bdb7af62bc19216695e4d", "type": "zip", "shasum": "", "reference": "53b81ac2e171b7b94c5bdb7af62bc19216695e4d"}, "time": "2017-05-10T18:05:26+00:00"}, {"version": "0.3.40", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bd14de30e7126ab4c5c234a4e9ae13efa3b05c19"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bd14de30e7126ab4c5c234a4e9ae13efa3b05c19", "type": "zip", "shasum": "", "reference": "bd14de30e7126ab4c5c234a4e9ae13efa3b05c19"}, "time": "2017-05-02T16:23:53+00:00"}, {"version": "0.3.39", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9ea80b5ef83d10cee9c07770b296b0205ae0a152"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9ea80b5ef83d10cee9c07770b296b0205ae0a152", "type": "zip", "shasum": "", "reference": "9ea80b5ef83d10cee9c07770b296b0205ae0a152"}, "time": "2017-04-22T23:41:16+00:00", "require": {"php": "^5.5 || ^7.0", "nikic/php-parser": "^3.0.4", "composer/composer": "^1.3"}}, {"version": "0.3.38", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bd6a623bfd9cfde18d1b0bfd47dd2c0447785423"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bd6a623bfd9cfde18d1b0bfd47dd2c0447785423", "type": "zip", "shasum": "", "reference": "bd6a623bfd9cfde18d1b0bfd47dd2c0447785423"}, "time": "2017-04-21T20:33:18+00:00"}, {"version": "0.3.37", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7bcf7f13b1557f6721652b1ba7eac04ff80e08d1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7bcf7f13b1557f6721652b1ba7eac04ff80e08d1", "type": "zip", "shasum": "", "reference": "7bcf7f13b1557f6721652b1ba7eac04ff80e08d1"}, "time": "2017-04-11T21:43:46+00:00"}, {"version": "0.3.36", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b20ddfec94f0be73a9da7f2c7c5855fae86b2cd2"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b20ddfec94f0be73a9da7f2c7c5855fae86b2cd2", "type": "zip", "shasum": "", "reference": "b20ddfec94f0be73a9da7f2c7c5855fae86b2cd2"}, "time": "2017-04-07T13:02:09+00:00"}, {"version": "0.3.35", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d65221fbe46282aced203d10bab26104d4a6665c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d65221fbe46282aced203d10bab26104d4a6665c", "type": "zip", "shasum": "", "reference": "d65221fbe46282aced203d10bab26104d4a6665c"}, "time": "2017-04-05T18:37:22+00:00"}, {"version": "0.3.34", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0ad8e2b6fc139d5a5879f59bcebd7e232b1adcae"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0ad8e2b6fc139d5a5879f59bcebd7e232b1adcae", "type": "zip", "shasum": "", "reference": "0ad8e2b6fc139d5a5879f59bcebd7e232b1adcae"}, "time": "2017-04-03T17:51:45+00:00"}, {"version": "0.3.33", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5b34163d8d1808cf7bc31f8705eb94f36b80ea66"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5b34163d8d1808cf7bc31f8705eb94f36b80ea66", "type": "zip", "shasum": "", "reference": "5b34163d8d1808cf7bc31f8705eb94f36b80ea66"}, "time": "2017-03-31T15:21:34+00:00"}, {"version": "0.3.32", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "676c01ed2ee09de11d3204d50932d1ba707e87f6"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/676c01ed2ee09de11d3204d50932d1ba707e87f6", "type": "zip", "shasum": "", "reference": "676c01ed2ee09de11d3204d50932d1ba707e87f6"}, "time": "2017-03-20T06:23:54+00:00"}, {"version": "0.3.31", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "7fad81edacbfbd66d504fcdf46d1ccf3a72d3bab"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/7fad81edacbfbd66d504fcdf46d1ccf3a72d3bab", "type": "zip", "shasum": "", "reference": "7fad81edacbfbd66d504fcdf46d1ccf3a72d3bab"}, "time": "2017-03-15T15:38:23+00:00"}, {"version": "0.3.30", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a7da88bad316b6bf9d8a70a6e7472f6453e15ddd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a7da88bad316b6bf9d8a70a6e7472f6453e15ddd", "type": "zip", "shasum": "", "reference": "a7da88bad316b6bf9d8a70a6e7472f6453e15ddd"}, "time": "2017-03-11T17:32:27+00:00"}, {"version": "0.3.29", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "39b9afcdac3d44d06dfad77435395527ddf1f58d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/39b9afcdac3d44d06dfad77435395527ddf1f58d", "type": "zip", "shasum": "", "reference": "39b9afcdac3d44d06dfad77435395527ddf1f58d"}, "time": "2017-03-11T17:05:23+00:00"}, {"version": "0.3.28", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ce253ad5c366772b9caef7d51a3c92248b34836a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ce253ad5c366772b9caef7d51a3c92248b34836a", "type": "zip", "shasum": "", "reference": "ce253ad5c366772b9caef7d51a3c92248b34836a"}, "time": "2017-03-09T23:10:50+00:00"}, {"version": "0.3.27", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4619ef139092d02b6e1c9f06a998ed0f0c20aab8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4619ef139092d02b6e1c9f06a998ed0f0c20aab8", "type": "zip", "shasum": "", "reference": "4619ef139092d02b6e1c9f06a998ed0f0c20aab8"}, "time": "2017-03-03T20:54:08+00:00"}, {"version": "0.3.26", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f9f38f283e94a9f080a409ec12ba3c0760fa622a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f9f38f283e94a9f080a409ec12ba3c0760fa622a", "type": "zip", "shasum": "", "reference": "f9f38f283e94a9f080a409ec12ba3c0760fa622a"}, "time": "2017-03-02T17:19:18+00:00"}, {"version": "0.3.25", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ac1a45f129b14b93fc3f586a977f87982216c271"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ac1a45f129b14b93fc3f586a977f87982216c271", "type": "zip", "shasum": "", "reference": "ac1a45f129b14b93fc3f586a977f87982216c271"}, "time": "2017-02-27T15:53:32+00:00"}, {"version": "0.3.24", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c85b6b376b285ac5becf956bd384bfd1929028c8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c85b6b376b285ac5becf956bd384bfd1929028c8", "type": "zip", "shasum": "", "reference": "c85b6b376b285ac5becf956bd384bfd1929028c8"}, "time": "2017-02-21T22:48:12+00:00"}, {"version": "0.3.23", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "721fcbd790a0b469a0c70dd1de00ef7660a11b25"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/721fcbd790a0b469a0c70dd1de00ef7660a11b25", "type": "zip", "shasum": "", "reference": "721fcbd790a0b469a0c70dd1de00ef7660a11b25"}, "time": "2017-02-13T05:53:30+00:00", "require": {"php": "^5.5 || ^7.0", "nikic/php-parser": "^3.0.4"}}, {"version": "0.3.22", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "63c88e294946cc722adcf794acace87ecfc104e3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/63c88e294946cc722adcf794acace87ecfc104e3", "type": "zip", "shasum": "", "reference": "63c88e294946cc722adcf794acace87ecfc104e3"}, "time": "2017-02-13T05:18:49+00:00"}, {"version": "0.3.21", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1001e03f8ac784c9abea65bfd6b46522d2d95fac"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1001e03f8ac784c9abea65bfd6b46522d2d95fac", "type": "zip", "shasum": "", "reference": "1001e03f8ac784c9abea65bfd6b46522d2d95fac"}, "time": "2017-02-13T00:51:48+00:00"}, {"version": "0.3.20", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "dbddedb20bbf45f7968534dde5b4ca02b974f8d9"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/dbddedb20bbf45f7968534dde5b4ca02b974f8d9", "type": "zip", "shasum": "", "reference": "dbddedb20bbf45f7968534dde5b4ca02b974f8d9"}, "time": "2017-02-11T04:35:34+00:00"}, {"version": "0.3.19", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8cd83a581ae63cc0fb7eb243338d86324c6ea2ea"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8cd83a581ae63cc0fb7eb243338d86324c6ea2ea", "type": "zip", "shasum": "", "reference": "8cd83a581ae63cc0fb7eb243338d86324c6ea2ea"}, "time": "2017-02-08T16:27:06+00:00", "require": {"php": "^5.5 || ^7.0", "nikic/php-parser": "^3.0.3"}}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "09ca483e7d879fa8d767752a10ff0e4171666be1"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/09ca483e7d879fa8d767752a10ff0e4171666be1", "type": "zip", "shasum": "", "reference": "09ca483e7d879fa8d767752a10ff0e4171666be1"}, "time": "2017-02-08T07:33:29+00:00"}, {"version": "0.3.18", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "89a5f109e8b6274a41803330077fda5f233d1f4e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/89a5f109e8b6274a41803330077fda5f233d1f4e", "type": "zip", "shasum": "", "reference": "89a5f109e8b6274a41803330077fda5f233d1f4e"}, "time": "2017-02-08T05:42:53+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1051ba9fc18e8b51c6a6579ab943f75009967d1d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1051ba9fc18e8b51c6a6579ab943f75009967d1d", "type": "zip", "shasum": "", "reference": "1051ba9fc18e8b51c6a6579ab943f75009967d1d"}, "time": "2017-02-01T23:27:24+00:00", "require": {"php": "^5.5 || ^7.0", "nikic/php-parser": "^3.0.2"}}, {"version": "0.3.17", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e925e8b4db0397d092b6f1d324d08d1010954c11"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e925e8b4db0397d092b6f1d324d08d1010954c11", "type": "zip", "shasum": "", "reference": "e925e8b4db0397d092b6f1d324d08d1010954c11"}, "time": "2017-02-01T18:51:26+00:00"}, {"version": "0.3.16", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "fe803129cfd4bda8a00031835972b6aa16a058a0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/fe803129cfd4bda8a00031835972b6aa16a058a0", "type": "zip", "shasum": "", "reference": "fe803129cfd4bda8a00031835972b6aa16a058a0"}, "time": "2017-01-29T05:24:55+00:00"}, {"version": "0.3.15", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d0207b60988c31f1ade469ae7e1b4b4b3309cbdb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d0207b60988c31f1ade469ae7e1b4b4b3309cbdb", "type": "zip", "shasum": "", "reference": "d0207b60988c31f1ade469ae7e1b4b4b3309cbdb"}, "time": "2017-01-20T00:02:00+00:00"}, {"version": "0.3.14", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a14b03c057b312cf097cfbe464a27284c8f735c0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a14b03c057b312cf097cfbe464a27284c8f735c0", "type": "zip", "shasum": "", "reference": "a14b03c057b312cf097cfbe464a27284c8f735c0"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.14"}, "funding": [], "time": "2017-01-19T06:32:35+00:00", "require": {"php": ">=5.5", "nikic/php-parser": ">=3.0.2"}, "require-dev": {"phpunit/phpunit": ">=5.7.4", "squizlabs/php_codesniffer": "^2.7"}}, {"version": "0.3.13", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bc668e2c7a31bee54b13731081b84bf9417625fb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bc668e2c7a31bee54b13731081b84bf9417625fb", "type": "zip", "shasum": "", "reference": "bc668e2c7a31bee54b13731081b84bf9417625fb"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.13"}, "time": "2017-01-17T06:14:43+00:00"}, {"version": "0.3.12", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8836f0502794a2f08724a2b10dfb0d262086cfef"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8836f0502794a2f08724a2b10dfb0d262086cfef", "type": "zip", "shasum": "", "reference": "8836f0502794a2f08724a2b10dfb0d262086cfef"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.12"}, "time": "2017-01-15T17:34:23+00:00"}, {"version": "0.3.11", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "37e8b4adfef636e028fbdab5e1f48010a5274342"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/37e8b4adfef636e028fbdab5e1f48010a5274342", "type": "zip", "shasum": "", "reference": "37e8b4adfef636e028fbdab5e1f48010a5274342"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.11"}, "time": "2017-01-13T19:14:24+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "21816723c7a4408e8ac19a80edfee1b25464b659"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/21816723c7a4408e8ac19a80edfee1b25464b659", "type": "zip", "shasum": "", "reference": "21816723c7a4408e8ac19a80edfee1b25464b659"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/********"}, "time": "2016-12-30T06:51:29+00:00"}, {"version": "0.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "2709198392c7a73ae64c74a77ff8437ed674990c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/2709198392c7a73ae64c74a77ff8437ed674990c", "type": "zip", "shasum": "", "reference": "2709198392c7a73ae64c74a77ff8437ed674990c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.9"}, "time": "2016-12-30T03:48:59+00:00"}, {"version": "0.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "324b7b4801d0a97bd8eb12cb7eb534653d3a98c7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/324b7b4801d0a97bd8eb12cb7eb534653d3a98c7", "type": "zip", "shasum": "", "reference": "324b7b4801d0a97bd8eb12cb7eb534653d3a98c7"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.8"}, "time": "2016-12-27T01:06:05+00:00"}, {"version": "0.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b6eea4e812ad93eb60787dd7b680ade8bc46452b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b6eea4e812ad93eb60787dd7b680ade8bc46452b", "type": "zip", "shasum": "", "reference": "b6eea4e812ad93eb60787dd7b680ade8bc46452b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.7"}, "time": "2016-12-25T11:32:21+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ea74388c7db6aa0dc286ab826127528f9583039c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ea74388c7db6aa0dc286ab826127528f9583039c", "type": "zip", "shasum": "", "reference": "ea74388c7db6aa0dc286ab826127528f9583039c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-23T21:50:31+00:00", "require-dev": {"phpunit/phpunit": ">=5.7.4"}}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c4b1633d786364aa5664b4c9cdc7c79435458478"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c4b1633d786364aa5664b4c9cdc7c79435458478", "type": "zip", "shasum": "", "reference": "c4b1633d786364aa5664b4c9cdc7c79435458478"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-14T17:58:56+00:00", "require-dev": {"phpunit/phpunit": ">=4.1"}}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8d693d7db2dbb94e7e651875f871590826861a27"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8d693d7db2dbb94e7e651875f871590826861a27", "type": "zip", "shasum": "", "reference": "8d693d7db2dbb94e7e651875f871590826861a27"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-12T19:29:58+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4c21a5af7c8dc122ba7311342e77e881a3eb5073"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4c21a5af7c8dc122ba7311342e77e881a3eb5073", "type": "zip", "shasum": "", "reference": "4c21a5af7c8dc122ba7311342e77e881a3eb5073"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-12T04:41:11+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0b06b3b09bcb303c5ad4946ae20dbef9b2100416"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0b06b3b09bcb303c5ad4946ae20dbef9b2100416", "type": "zip", "shasum": "", "reference": "0b06b3b09bcb303c5ad4946ae20dbef9b2100416"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-10T18:07:12+00:00", "require": {"php": ">=5.5", "nikic/php-parser": ">=3.0.1"}}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "eff98bfe004edd33e33e5070f608c499bb74963c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/eff98bfe004edd33e33e5070f608c499bb74963c", "type": "zip", "shasum": "", "reference": "eff98bfe004edd33e33e5070f608c499bb74963c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-09T23:53:26+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1e8f986d8cc8efd2d2839558c73fa6ff03d47939"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1e8f986d8cc8efd2d2839558c73fa6ff03d47939", "type": "zip", "shasum": "", "reference": "1e8f986d8cc8efd2d2839558c73fa6ff03d47939"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-11T18:59:36+00:00", "require": {"php": ">=5.5", "nikic/php-parser": ">=3.0.2"}}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "117645ac73764726d75262246c3f591e8e896224"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/117645ac73764726d75262246c3f591e8e896224", "type": "zip", "shasum": "", "reference": "117645ac73764726d75262246c3f591e8e896224"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.3"}, "time": "2016-12-08T20:57:18+00:00", "require": {"php": ">=5.5", "nikic/php-parser": ">=3.0.1"}}, {"version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a5195b2571c11bec445fcc83578341a918422bc3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a5195b2571c11bec445fcc83578341a918422bc3", "type": "zip", "shasum": "", "reference": "a5195b2571c11bec445fcc83578341a918422bc3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.2"}, "time": "2016-12-08T03:38:57+00:00"}, {"version": "0.3.2-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "48ba91a05be45f350136057fb7974ba6d0dba431"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/48ba91a05be45f350136057fb7974ba6d0dba431", "type": "zip", "shasum": "", "reference": "48ba91a05be45f350136057fb7974ba6d0dba431"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.2-alpha"}, "time": "2016-12-04T05:08:53+00:00", "require": {"php": ">=5.4", "nikic/php-parser": ">=3.0.1"}}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "55a060b53ac1d7861dfe7b99f02d1861d435ed3b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/55a060b53ac1d7861dfe7b99f02d1861d435ed3b", "type": "zip", "shasum": "", "reference": "55a060b53ac1d7861dfe7b99f02d1861d435ed3b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.1"}, "time": "2016-12-07T19:13:39+00:00", "require": {"php": ">=5.5", "nikic/php-parser": ">=3.0.1"}}, {"version": "0.3.1-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d7c6e84a0d9885200e7dce31c041c60047e5aafc"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d7c6e84a0d9885200e7dce31c041c60047e5aafc", "type": "zip", "shasum": "", "reference": "d7c6e84a0d9885200e7dce31c041c60047e5aafc"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.1-alpha"}, "time": "2016-12-04T04:03:51+00:00", "require": {"php": ">=5.4", "nikic/php-parser": ">=3.0.1"}}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ff409a7f012fb4c8516006c3176d97196060d56b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ff409a7f012fb4c8516006c3176d97196060d56b", "type": "zip", "shasum": "", "reference": "ff409a7f012fb4c8516006c3176d97196060d56b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.0"}, "time": "2016-12-05T04:19:14+00:00", "require": {"php": ">=5.5", "nikic/php-parser": ">=3.0.1"}}, {"version": "0.3.0-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3159d4e7aadda2806c24f8273d472508e1b55176"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3159d4e7aadda2806c24f8273d472508e1b55176", "type": "zip", "shasum": "", "reference": "3159d4e7aadda2806c24f8273d472508e1b55176"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.3.0-alpha"}, "time": "2016-12-04T01:04:42+00:00", "require": {"php": ">=5.4", "nikic/php-parser": ">=3.0.1"}}, {"version": "0.2.66", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8928fe5e2098ed7e6b056dc5f773db90feafd224"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8928fe5e2098ed7e6b056dc5f773db90feafd224", "type": "zip", "shasum": "", "reference": "8928fe5e2098ed7e6b056dc5f773db90feafd224"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.x"}, "time": "2017-12-12T06:52:18+00:00", "require": {"php": "^5.4 || ^7.0", "nikic/php-parser": "^2.1.1", "composer/composer": "^1.3", "symfony/console": "^2.7", "symfony/yaml": "^2.8"}, "require-dev": {"phpunit/phpunit": "^4.8.30"}, "funding": "__unset"}, {"version": "0.2.65", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "20812e6fd03b8a7363c741500c7ef5de377e2dc5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/20812e6fd03b8a7363c741500c7ef5de377e2dc5", "type": "zip", "shasum": "", "reference": "20812e6fd03b8a7363c741500c7ef5de377e2dc5"}, "time": "2017-10-24T14:15:05+00:00"}, {"version": "0.2.64", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "74c3282f1ec32431f4bc7a169165cf3e77a89adb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/74c3282f1ec32431f4bc7a169165cf3e77a89adb", "type": "zip", "shasum": "", "reference": "74c3282f1ec32431f4bc7a169165cf3e77a89adb"}, "time": "2017-10-07T19:10:28+00:00"}, {"version": "0.2.63", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c1671983642151b7208d231ec77ab2982902f8a4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c1671983642151b7208d231ec77ab2982902f8a4", "type": "zip", "shasum": "", "reference": "c1671983642151b7208d231ec77ab2982902f8a4"}, "time": "2017-09-19T01:52:27+00:00"}, {"version": "0.2.62", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e875d51686ff685c68262de343fd341b1fa8aa98"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e875d51686ff685c68262de343fd341b1fa8aa98", "type": "zip", "shasum": "", "reference": "e875d51686ff685c68262de343fd341b1fa8aa98"}, "time": "2017-09-11T19:35:07+00:00"}, {"version": "0.2.60", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "dadeb59af6ca055a6840edb9fa86961f298b7465"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/dadeb59af6ca055a6840edb9fa86961f298b7465", "type": "zip", "shasum": "", "reference": "dadeb59af6ca055a6840edb9fa86961f298b7465"}, "time": "2017-08-18T21:23:29+00:00"}, {"version": "0.2.59", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f85782f8704135ba1f689a29553ee0c11a618045"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f85782f8704135ba1f689a29553ee0c11a618045", "type": "zip", "shasum": "", "reference": "f85782f8704135ba1f689a29553ee0c11a618045"}, "time": "2017-08-11T23:09:24+00:00"}, {"version": "0.2.58", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4ef4e5933cfe44bab8cdfd2ee7c4c906efba1fc5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4ef4e5933cfe44bab8cdfd2ee7c4c906efba1fc5", "type": "zip", "shasum": "", "reference": "4ef4e5933cfe44bab8cdfd2ee7c4c906efba1fc5"}, "time": "2017-08-08T15:03:56+00:00"}, {"version": "0.2.57", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "756866754da59e5642f3bf90a43e9f509a69f75c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/756866754da59e5642f3bf90a43e9f509a69f75c", "type": "zip", "shasum": "", "reference": "756866754da59e5642f3bf90a43e9f509a69f75c"}, "time": "2017-07-27T23:14:39+00:00"}, {"version": "0.2.56", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c566c43e924ba35066b6264e7a4804611af43a6f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c566c43e924ba35066b6264e7a4804611af43a6f", "type": "zip", "shasum": "", "reference": "c566c43e924ba35066b6264e7a4804611af43a6f"}, "time": "2017-07-27T21:00:26+00:00"}, {"version": "0.2.55", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5afc751648ef7a60addce822992914828ac23967"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5afc751648ef7a60addce822992914828ac23967", "type": "zip", "shasum": "", "reference": "5afc751648ef7a60addce822992914828ac23967"}, "time": "2017-07-27T20:16:16+00:00"}, {"version": "0.2.54", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b66d9e595401a0293ad531a6b895e628ad7948ab"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b66d9e595401a0293ad531a6b895e628ad7948ab", "type": "zip", "shasum": "", "reference": "b66d9e595401a0293ad531a6b895e628ad7948ab"}, "time": "2017-07-27T03:47:41+00:00"}, {"version": "0.2.53", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b2f32ed26daea1944a69cc3d3539105f150ae232"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b2f32ed26daea1944a69cc3d3539105f150ae232", "type": "zip", "shasum": "", "reference": "b2f32ed26daea1944a69cc3d3539105f150ae232"}, "time": "2017-07-27T01:30:28+00:00"}, {"version": "0.2.52", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bf893aa6b9afa150fa51469c50fc193a6d2897e4"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bf893aa6b9afa150fa51469c50fc193a6d2897e4", "type": "zip", "shasum": "", "reference": "bf893aa6b9afa150fa51469c50fc193a6d2897e4"}, "time": "2017-07-09T19:55:01+00:00"}, {"version": "0.2.51", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9a1fc7e3b86434c5a7b6fbb807e82d7d9381c87c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9a1fc7e3b86434c5a7b6fbb807e82d7d9381c87c", "type": "zip", "shasum": "", "reference": "9a1fc7e3b86434c5a7b6fbb807e82d7d9381c87c"}, "time": "2017-06-21T20:41:23+00:00"}, {"version": "0.2.50", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "da119b9dd9e94b405c3d02de3b9d3faddb5ddc60"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/da119b9dd9e94b405c3d02de3b9d3faddb5ddc60", "type": "zip", "shasum": "", "reference": "da119b9dd9e94b405c3d02de3b9d3faddb5ddc60"}, "time": "2017-06-13T04:52:16+00:00"}, {"version": "0.2.49", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1761f954e70498c080ebd85de0ea6a30117424e5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1761f954e70498c080ebd85de0ea6a30117424e5", "type": "zip", "shasum": "", "reference": "1761f954e70498c080ebd85de0ea6a30117424e5"}, "time": "2017-06-13T04:10:26+00:00"}, {"version": "0.2.48", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0f1c073e58aa94c6b7add6821d0b03dcfee7cf87"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0f1c073e58aa94c6b7add6821d0b03dcfee7cf87", "type": "zip", "shasum": "", "reference": "0f1c073e58aa94c6b7add6821d0b03dcfee7cf87"}, "time": "2017-05-22T18:50:18+00:00", "require-dev": {"php": "^5.6 || ^7.0", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^2.7"}}, {"version": "0.2.47", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "77a351aa2c2d32cb1f767282f5e02a4a9d6f64d0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/77a351aa2c2d32cb1f767282f5e02a4a9d6f64d0", "type": "zip", "shasum": "", "reference": "77a351aa2c2d32cb1f767282f5e02a4a9d6f64d0"}, "time": "2017-05-15T20:22:54+00:00"}, {"version": "0.2.46", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "bf637eb313db7a56c00ab185d66d0cf38c8191a8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/bf637eb313db7a56c00ab185d66d0cf38c8191a8", "type": "zip", "shasum": "", "reference": "bf637eb313db7a56c00ab185d66d0cf38c8191a8"}, "time": "2017-05-10T18:06:42+00:00"}, {"version": "0.2.45", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "81ff601ede284fc871ef17eb4064ab46396b8b22"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/81ff601ede284fc871ef17eb4064ab46396b8b22", "type": "zip", "shasum": "", "reference": "81ff601ede284fc871ef17eb4064ab46396b8b22"}, "time": "2017-05-02T17:11:02+00:00"}, {"version": "0.2.44", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "16edc00d392812f5893139fc7ff564efdd513864"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/16edc00d392812f5893139fc7ff564efdd513864", "type": "zip", "shasum": "", "reference": "16edc00d392812f5893139fc7ff564efdd513864"}, "time": "2017-04-21T20:33:54+00:00", "require-dev": {"phpunit/phpunit": "^4.8.30", "squizlabs/php_codesniffer": "^2.7"}}, {"version": "0.2.43", "version_normalized": "********"}, {"version": "0.2.42", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "304672a58d2200798c6e60dcfef40e78de4098a5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/304672a58d2200798c6e60dcfef40e78de4098a5", "type": "zip", "shasum": "", "reference": "304672a58d2200798c6e60dcfef40e78de4098a5"}, "time": "2017-04-11T21:45:38+00:00"}, {"version": "0.2.41", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ffe984c4224f0f9d551a87d43d330415c3d73911"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ffe984c4224f0f9d551a87d43d330415c3d73911", "type": "zip", "shasum": "", "reference": "ffe984c4224f0f9d551a87d43d330415c3d73911"}, "time": "2017-04-07T13:02:44+00:00"}, {"version": "0.2.40", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d54074057237ed2e8c9cd4cebca618f2dcf51118"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d54074057237ed2e8c9cd4cebca618f2dcf51118", "type": "zip", "shasum": "", "reference": "d54074057237ed2e8c9cd4cebca618f2dcf51118"}, "time": "2017-04-05T19:26:21+00:00"}, {"version": "0.2.39", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ac61a1ad376d7f38973d577dcdf7f2ba0543be5d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ac61a1ad376d7f38973d577dcdf7f2ba0543be5d", "type": "zip", "shasum": "", "reference": "ac61a1ad376d7f38973d577dcdf7f2ba0543be5d"}, "time": "2017-04-05T15:34:09+00:00"}, {"version": "0.2.38", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "fc723ff68e05139d10075d98a38a54a5cac63964"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/fc723ff68e05139d10075d98a38a54a5cac63964", "type": "zip", "shasum": "", "reference": "fc723ff68e05139d10075d98a38a54a5cac63964"}, "time": "2017-03-31T15:23:15+00:00"}, {"version": "0.2.37", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3e4c68ed511e91ab93cebc53913b4801d6f137fd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3e4c68ed511e91ab93cebc53913b4801d6f137fd", "type": "zip", "shasum": "", "reference": "3e4c68ed511e91ab93cebc53913b4801d6f137fd"}, "time": "2017-03-20T06:24:35+00:00"}, {"version": "0.2.36", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "40de101d1e7876363f26f39b8354a30a1a7b2dbd"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/40de101d1e7876363f26f39b8354a30a1a7b2dbd", "type": "zip", "shasum": "", "reference": "40de101d1e7876363f26f39b8354a30a1a7b2dbd"}, "time": "2017-03-15T15:38:42+00:00"}, {"version": "0.2.35", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a996e1d1df397d9ece56e276008bd68452932394"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a996e1d1df397d9ece56e276008bd68452932394", "type": "zip", "shasum": "", "reference": "a996e1d1df397d9ece56e276008bd68452932394"}, "time": "2017-03-11T17:32:38+00:00"}, {"version": "0.2.34", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "dea88156dbbca6a40aaa32ca386ce1284f9c71b3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/dea88156dbbca6a40aaa32ca386ce1284f9c71b3", "type": "zip", "shasum": "", "reference": "dea88156dbbca6a40aaa32ca386ce1284f9c71b3"}, "time": "2017-03-11T17:09:53+00:00"}, {"version": "0.2.33", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f4c475c03197235667a23fd273054900a493bef5"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f4c475c03197235667a23fd273054900a493bef5", "type": "zip", "shasum": "", "reference": "f4c475c03197235667a23fd273054900a493bef5"}, "time": "2017-03-09T23:11:45+00:00"}, {"version": "0.2.32", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "3cc5af3095b6c1ef3114279d6c83cb011dd3230c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/3cc5af3095b6c1ef3114279d6c83cb011dd3230c", "type": "zip", "shasum": "", "reference": "3cc5af3095b6c1ef3114279d6c83cb011dd3230c"}, "time": "2017-03-03T20:55:14+00:00"}, {"version": "0.2.31", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "055fb94e525d9fe6df02e2abb2d60e97f442225d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/055fb94e525d9fe6df02e2abb2d60e97f442225d", "type": "zip", "shasum": "", "reference": "055fb94e525d9fe6df02e2abb2d60e97f442225d"}, "time": "2017-03-03T15:58:43+00:00"}, {"version": "0.2.30", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e90b7d4f72a28a358e106d3ea5fe25f802b5a689"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e90b7d4f72a28a358e106d3ea5fe25f802b5a689", "type": "zip", "shasum": "", "reference": "e90b7d4f72a28a358e106d3ea5fe25f802b5a689"}, "time": "2017-03-02T18:18:34+00:00"}, {"version": "0.2.29", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ffa6b80ef6a8a570468a49b48146c552b07b4038"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ffa6b80ef6a8a570468a49b48146c552b07b4038", "type": "zip", "shasum": "", "reference": "ffa6b80ef6a8a570468a49b48146c552b07b4038"}, "time": "2017-02-27T16:08:30+00:00"}, {"version": "0.2.28", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "62e1c1132afc655a796d7b480d9a0d44abd781c0"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/62e1c1132afc655a796d7b480d9a0d44abd781c0", "type": "zip", "shasum": "", "reference": "62e1c1132afc655a796d7b480d9a0d44abd781c0"}, "time": "2017-02-27T15:53:44+00:00"}, {"version": "0.2.27", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b47a638d1e186e6220d9ce8d1c140c8016b3e6db"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b47a638d1e186e6220d9ce8d1c140c8016b3e6db", "type": "zip", "shasum": "", "reference": "b47a638d1e186e6220d9ce8d1c140c8016b3e6db"}, "time": "2017-02-22T22:27:39+00:00"}, {"version": "0.2.26", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8d913ba193fe2623077bc7c59ff28b2220e72239"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8d913ba193fe2623077bc7c59ff28b2220e72239", "type": "zip", "shasum": "", "reference": "8d913ba193fe2623077bc7c59ff28b2220e72239"}, "time": "2017-02-21T23:06:23+00:00"}, {"version": "0.2.25", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "6f8396b5942510cd337a8272e77c5d6c6a7e9969"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/6f8396b5942510cd337a8272e77c5d6c6a7e9969", "type": "zip", "shasum": "", "reference": "6f8396b5942510cd337a8272e77c5d6c6a7e9969"}, "time": "2017-02-13T05:53:51+00:00", "require": {"php": "^5.4 || ^7.0", "nikic/php-parser": "^2.1.1"}}, {"version": "0.2.24", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8220478c5fbdfb517f6ba744e7efa7a28fcf106b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8220478c5fbdfb517f6ba744e7efa7a28fcf106b", "type": "zip", "shasum": "", "reference": "8220478c5fbdfb517f6ba744e7efa7a28fcf106b"}, "time": "2017-02-13T05:19:32+00:00"}, {"version": "0.2.23", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b05783f68854536a2067ba905537648ad0d92c61"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b05783f68854536a2067ba905537648ad0d92c61", "type": "zip", "shasum": "", "reference": "b05783f68854536a2067ba905537648ad0d92c61"}, "time": "2017-02-13T00:52:05+00:00"}, {"version": "0.2.22", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "944d4419fe926e1bd8e97b87d9562162c8cd5fda"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/944d4419fe926e1bd8e97b87d9562162c8cd5fda", "type": "zip", "shasum": "", "reference": "944d4419fe926e1bd8e97b87d9562162c8cd5fda"}, "time": "2017-02-11T04:36:36+00:00"}, {"version": "0.2.21", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "213254459d3304c7a090e445b6e099156aef79cb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/213254459d3304c7a090e445b6e099156aef79cb", "type": "zip", "shasum": "", "reference": "213254459d3304c7a090e445b6e099156aef79cb"}, "time": "2017-02-08T16:27:39+00:00"}, {"version": "0.2.20", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a719aa7d8922b4373bffc6125499b49248e7f6b7"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a719aa7d8922b4373bffc6125499b49248e7f6b7", "type": "zip", "shasum": "", "reference": "a719aa7d8922b4373bffc6125499b49248e7f6b7"}, "time": "2017-02-08T07:33:59+00:00"}, {"version": "0.2.19", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9632f6ab01b3ef5bbc858b1adcbe5d2cc2404d78"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9632f6ab01b3ef5bbc858b1adcbe5d2cc2404d78", "type": "zip", "shasum": "", "reference": "9632f6ab01b3ef5bbc858b1adcbe5d2cc2404d78"}, "time": "2017-02-02T17:39:49+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d9e04b3e1809d43a511735872093197ab3b2490b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d9e04b3e1809d43a511735872093197ab3b2490b", "type": "zip", "shasum": "", "reference": "d9e04b3e1809d43a511735872093197ab3b2490b"}, "time": "2017-02-01T23:35:21+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d8b7b1025cd942d39cc125553241fd669e6ccd98"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d8b7b1025cd942d39cc125553241fd669e6ccd98", "type": "zip", "shasum": "", "reference": "d8b7b1025cd942d39cc125553241fd669e6ccd98"}, "time": "2017-02-01T19:56:24+00:00"}, {"version": "0.2.18", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e10063a0f8894544c103d3f767319592696f3839"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e10063a0f8894544c103d3f767319592696f3839", "type": "zip", "shasum": "", "reference": "e10063a0f8894544c103d3f767319592696f3839"}, "time": "2017-02-01T19:11:18+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f0ddfd43ee8111154b4416366689590f38ee643f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f0ddfd43ee8111154b4416366689590f38ee643f", "type": "zip", "shasum": "", "reference": "f0ddfd43ee8111154b4416366689590f38ee643f"}, "time": "2017-01-29T05:36:23+00:00"}, {"version": "0.2.17", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "f1bc28a8dcf4aed87a1756e8303be32b6910c23d"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/f1bc28a8dcf4aed87a1756e8303be32b6910c23d", "type": "zip", "shasum": "", "reference": "f1bc28a8dcf4aed87a1756e8303be32b6910c23d"}, "time": "2017-01-29T05:25:15+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "e0641dfddf0279ca8c054846e85f1a78d9833019"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/e0641dfddf0279ca8c054846e85f1a78d9833019", "type": "zip", "shasum": "", "reference": "e0641dfddf0279ca8c054846e85f1a78d9833019"}, "time": "2017-01-19T23:59:14+00:00", "require": {"php": ">=5.4", "nikic/php-parser": "2.1.1"}, "require-dev": {"phpunit/phpunit": "4.8.30", "phpdocumentor/reflection-docblock": "2.0.4", "symfony/yaml": "2.8.14", "squizlabs/php_codesniffer": "^2.7"}}, {"version": "0.2.16", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "ccf196acb2701a338ccd6950a23effc5cdbef992"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/ccf196acb2701a338ccd6950a23effc5cdbef992", "type": "zip", "shasum": "", "reference": "ccf196acb2701a338ccd6950a23effc5cdbef992"}, "time": "2017-01-19T22:59:30+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9801233e427b00584ed3923f76d3f2f528caee70"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9801233e427b00584ed3923f76d3f2f528caee70", "type": "zip", "shasum": "", "reference": "9801233e427b00584ed3923f76d3f2f528caee70"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/********"}, "funding": [], "time": "2017-01-19T06:34:35+00:00"}, {"version": "0.2.15", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c30f59f1723419ad4ac1dd423b3c07424fa8cc1a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c30f59f1723419ad4ac1dd423b3c07424fa8cc1a", "type": "zip", "shasum": "", "reference": "c30f59f1723419ad4ac1dd423b3c07424fa8cc1a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.15"}, "time": "2017-01-19T04:53:36+00:00"}, {"version": "0.2.14", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "640f66d88be3976d8511a6fa9d5fb551fb6d2b3a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/640f66d88be3976d8511a6fa9d5fb551fb6d2b3a", "type": "zip", "shasum": "", "reference": "640f66d88be3976d8511a6fa9d5fb551fb6d2b3a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.14"}, "time": "2017-01-15T17:16:58+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1e5140ebaf359cc6fd2f8d33ee87f5090b7c8ef3"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1e5140ebaf359cc6fd2f8d33ee87f5090b7c8ef3", "type": "zip", "shasum": "", "reference": "1e5140ebaf359cc6fd2f8d33ee87f5090b7c8ef3"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/********"}, "time": "2016-12-30T06:28:07+00:00"}, {"version": "0.2.13", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "b66d684d76bdb2245aae38dd226e69d7e0d818ca"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/b66d684d76bdb2245aae38dd226e69d7e0d818ca", "type": "zip", "shasum": "", "reference": "b66d684d76bdb2245aae38dd226e69d7e0d818ca"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.13"}, "time": "2016-12-30T03:59:05+00:00"}, {"version": "0.2.12", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "38e05852e8287edb8449857d1e654a0f85e716db"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/38e05852e8287edb8449857d1e654a0f85e716db", "type": "zip", "shasum": "", "reference": "38e05852e8287edb8449857d1e654a0f85e716db"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.12"}, "time": "2016-12-26T20:19:18+00:00"}, {"version": "0.2.11", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "04bd282499847a8290f99ce941b6c84c8598cb5f"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/04bd282499847a8290f99ce941b6c84c8598cb5f", "type": "zip", "shasum": "", "reference": "04bd282499847a8290f99ce941b6c84c8598cb5f"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.11"}, "time": "2016-12-26T10:52:25+00:00"}, {"version": "********", "version_normalized": "********", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "8400654b1f9e7558742b168d7b41afad8e38544c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/8400654b1f9e7558742b168d7b41afad8e38544c", "type": "zip", "shasum": "", "reference": "8400654b1f9e7558742b168d7b41afad8e38544c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/********"}, "time": "2016-12-23T19:45:20+00:00", "require-dev": {"phpunit/phpunit": "4.8.30", "phpdocumentor/reflection-docblock": "2.0.4", "symfony/yaml": "2.8.14"}}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "4490b952a7a27828d5078274823a3fd84b162d5a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/4490b952a7a27828d5078274823a3fd84b162d5a", "type": "zip", "shasum": "", "reference": "4490b952a7a27828d5078274823a3fd84b162d5a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-14T18:03:31+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "746b9aa4d7725a401006d624490233287de4ab19"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/746b9aa4d7725a401006d624490233287de4ab19", "type": "zip", "shasum": "", "reference": "746b9aa4d7725a401006d624490233287de4ab19"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-12T19:30:16+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "5db32e8f6085208fdf94308731bbe28f0050935b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/5db32e8f6085208fdf94308731bbe28f0050935b", "type": "zip", "shasum": "", "reference": "5db32e8f6085208fdf94308731bbe28f0050935b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-12T04:50:08+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "c64e5518a7db092b40aacc8574ec259dcfc72b1e"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/c64e5518a7db092b40aacc8574ec259dcfc72b1e", "type": "zip", "shasum": "", "reference": "c64e5518a7db092b40aacc8574ec259dcfc72b1e"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-11T19:21:19+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "881745dace36a22fcbcd048e50e2896ea65bf61b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/881745dace36a22fcbcd048e50e2896ea65bf61b", "type": "zip", "shasum": "", "reference": "881745dace36a22fcbcd048e50e2896ea65bf61b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-10T18:07:34+00:00"}, {"version": "*******", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "32a367024ed806b3322d1812ca9479fdef13dc56"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/32a367024ed806b3322d1812ca9479fdef13dc56", "type": "zip", "shasum": "", "reference": "32a367024ed806b3322d1812ca9479fdef13dc56"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/*******"}, "time": "2016-12-09T23:52:36+00:00"}, {"version": "0.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "aa40c9f834edc0178052af9db94a460be2d3d130"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/aa40c9f834edc0178052af9db94a460be2d3d130", "type": "zip", "shasum": "", "reference": "aa40c9f834edc0178052af9db94a460be2d3d130"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.7"}, "time": "2016-12-09T23:06:07+00:00"}, {"version": "0.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "364a0dc43ae0e28ac917fe745a42ec2b25082689"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/364a0dc43ae0e28ac917fe745a42ec2b25082689", "type": "zip", "shasum": "", "reference": "364a0dc43ae0e28ac917fe745a42ec2b25082689"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.6"}, "time": "2016-12-07T23:07:40+00:00"}, {"version": "0.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "76ca4839316bf78e46d0ed66523a3387ac773d53"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/76ca4839316bf78e46d0ed66523a3387ac773d53", "type": "zip", "shasum": "", "reference": "76ca4839316bf78e46d0ed66523a3387ac773d53"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.5"}, "time": "2016-12-07T19:20:11+00:00"}, {"version": "0.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "21c0d8f7e2011f083ae0e60341ad7e0a883bea5c"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/21c0d8f7e2011f083ae0e60341ad7e0a883bea5c", "type": "zip", "shasum": "", "reference": "21c0d8f7e2011f083ae0e60341ad7e0a883bea5c"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.4"}, "time": "2016-12-06T18:49:57+00:00", "require": {"php": ">=5.4", "nikic/php-parser": ">=2.1.0"}, "require-dev": {"phpunit/phpunit": ">=4.1"}}, {"version": "0.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "9a2edd6a2b71583ea9cd7256bee68287b52246f8"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/9a2edd6a2b71583ea9cd7256bee68287b52246f8", "type": "zip", "shasum": "", "reference": "9a2edd6a2b71583ea9cd7256bee68287b52246f8"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.3"}, "time": "2016-12-05T01:34:55+00:00"}, {"version": "0.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d390a57fa0b23b10221aa948c4d9ffa56b40b130"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d390a57fa0b23b10221aa948c4d9ffa56b40b130", "type": "zip", "shasum": "", "reference": "d390a57fa0b23b10221aa948c4d9ffa56b40b130"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.2"}, "time": "2016-12-04T15:59:24+00:00"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "d951e1db22ac39d11e6853385b85a9db1c728fbb"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/d951e1db22ac39d11e6853385b85a9db1c728fbb", "type": "zip", "shasum": "", "reference": "d951e1db22ac39d11e6853385b85a9db1c728fbb"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.1"}, "time": "2016-12-04T04:07:37+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "a1acbfec071f939eb9af92a939b8b9a027197f4a"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/a1acbfec071f939eb9af92a939b8b9a027197f4a", "type": "zip", "shasum": "", "reference": "a1acbfec071f939eb9af92a939b8b9a027197f4a"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.2.0"}, "time": "2016-12-04T00:11:30+00:00"}, {"version": "0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "1d603b11d0ace491a0c78c6e6bb891c9143aea6b"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/1d603b11d0ace491a0c78c6e6bb891c9143aea6b", "type": "zip", "shasum": "", "reference": "1d603b11d0ace491a0c78c6e6bb891c9143aea6b"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.1.4"}, "time": "2016-11-30T20:03:52+00:00"}, {"version": "0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "134bc950fb6c38f5e34c3510735f9fce405f25ec"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/134bc950fb6c38f5e34c3510735f9fce405f25ec", "type": "zip", "shasum": "", "reference": "134bc950fb6c38f5e34c3510735f9fce405f25ec"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.1.3"}, "time": "2016-11-22T16:11:47+00:00"}, {"version": "0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "0cf3787b7f689becf557c569ede0d70616794262"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/0cf3787b7f689becf557c569ede0d70616794262", "type": "zip", "shasum": "", "reference": "0cf3787b7f689becf557c569ede0d70616794262"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.1.2"}, "time": "2016-11-22T03:27:33+00:00"}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "632ce0eafe5b958b2407ce66873aec79a1bfdd62"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/632ce0eafe5b958b2407ce66873aec79a1bfdd62", "type": "zip", "shasum": "", "reference": "632ce0eafe5b958b2407ce66873aec79a1bfdd62"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.1.1"}, "time": "2016-11-22T00:10:25+00:00", "require": {"php": ">=5.4", "nikic/php-parser": "2.1.0"}, "require-dev": {"phpunit/phpunit": "^5.6"}, "funding": "__unset"}, {"version": "0.1", "version_normalized": "*******", "source": {"url": "https://github.com/vimeo/psalm.git", "type": "git", "reference": "09a1a92a8e0d41cfb3790e17cd65330e601f66ee"}, "dist": {"url": "https://api.github.com/repos/vimeo/psalm/zipball/09a1a92a8e0d41cfb3790e17cd65330e601f66ee", "type": "zip", "shasum": "", "reference": "09a1a92a8e0d41cfb3790e17cd65330e601f66ee"}, "support": {"issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm/tree/0.1"}, "time": "2016-11-21T22:08:17+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 01 Jun 2025 15:29:34 GMT"}