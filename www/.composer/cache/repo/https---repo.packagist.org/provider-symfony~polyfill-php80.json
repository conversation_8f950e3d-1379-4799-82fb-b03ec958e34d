{"minified": "composer/2.0", "packages": {"symfony/polyfill-php80": [{"name": "symfony/polyfill-php80", "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "keywords": ["compatibility", "portable", "polyfill", "shim"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "type": "zip", "shasum": "", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}}, {"version": "v1.31.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "type": "zip", "shasum": "", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "type": "zip", "shasum": "", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "type": "zip", "shasum": "", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "type": "zip", "shasum": "", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.28.0"}, "time": "2023-01-26T09:26:14+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "type": "zip", "shasum": "", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/cfa0ae98841b9e461207c13ab093d76b0fa7bace", "type": "zip", "shasum": "", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.26.0"}, "time": "2022-05-10T07:21:04+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/4407588e0d3f1f52efb65fbe92babe41f37fe50c", "type": "zip", "shasum": "", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.25.0"}, "time": "2022-03-04T08:16:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "57b712b08eddb97c762a8caa32c84e037892d2e9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/57b712b08eddb97c762a8caa32c84e037892d2e9", "type": "zip", "shasum": "", "reference": "57b712b08eddb97c762a8caa32c84e037892d2e9"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.24.0"}, "time": "2021-09-13T13:58:33+00:00"}, {"version": "v1.23.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/1100343ed1a92e3a38f9ae122fc0eb21602547be", "type": "zip", "shasum": "", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.23.1"}, "time": "2021-07-28T13:41:28+00:00"}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "eca0bf41ed421bed1b57c4958bab16aa86b757d0"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/eca0bf41ed421bed1b57c4958bab16aa86b757d0", "type": "zip", "shasum": "", "reference": "eca0bf41ed421bed1b57c4958bab16aa86b757d0"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.23.0"}, "time": "2021-02-19T12:13:01+00:00"}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/dc3063ba22c2a1fd2f45ed856374d79114998f91", "type": "zip", "shasum": "", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.22.1"}, "time": "2021-01-07T16:49:33+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.22.0"}}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "e70aa8b064c5b72d3df2abd5ab1e90464ad009de"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/e70aa8b064c5b72d3df2abd5ab1e90464ad009de", "type": "zip", "shasum": "", "reference": "e70aa8b064c5b72d3df2abd5ab1e90464ad009de"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "f54ef00f4678f348f133097fa8c3701d197ff44d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/f54ef00f4678f348f133097fa8c3701d197ff44d", "type": "zip", "shasum": "", "reference": "f54ef00f4678f348f133097fa8c3701d197ff44d"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.19.0"}, "time": "2020-10-23T09:01:57+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=7.0.8"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "d87d5766cbf48d72388a9f6b85f280c8ad51f981"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/d87d5766cbf48d72388a9f6b85f280c8ad51f981", "type": "zip", "shasum": "", "reference": "d87d5766cbf48d72388a9f6b85f280c8ad51f981"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/master"}, "time": "2020-07-14T12:35:20+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********"}, {"version": "v1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "4a5b6bba3259902e386eb80dd1956181ee90b5b2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/4a5b6bba3259902e386eb80dd1956181ee90b5b2", "type": "zip", "shasum": "", "reference": "4a5b6bba3259902e386eb80dd1956181ee90b5b2"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.17.1"}, "time": "2020-06-06T08:46:27+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "5e30b2799bc1ad68f7feb62b60a73743589438dd"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/5e30b2799bc1ad68f7feb62b60a73743589438dd", "type": "zip", "shasum": "", "reference": "5e30b2799bc1ad68f7feb62b60a73743589438dd"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.17.0"}, "time": "2020-05-12T16:47:27+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "c4bdcb0490a6c0e12c51e9d771fda5867e908fd2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/c4bdcb0490a6c0e12c51e9d771fda5867e908fd2", "type": "zip", "shasum": "", "reference": "c4bdcb0490a6c0e12c51e9d771fda5867e908fd2"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.16.0"}, "time": "2020-05-08T16:50:20+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "8854dc880784d2ae32908b75824754339b5c0555"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/8854dc880784d2ae32908b75824754339b5c0555", "type": "zip", "shasum": "", "reference": "8854dc880784d2ae32908b75824754339b5c0555"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/master"}, "time": "2020-03-03T16:59:03+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php80.git", "type": "git", "reference": "b0bb13886534f7353089258600fb241699609375"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/b0bb13886534f7353089258600fb241699609375", "type": "zip", "shasum": "", "reference": "b0bb13886534f7353089258600fb241699609375"}, "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.14.0"}, "time": "2020-02-13T08:45:58+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 09:41:38 GMT"}