{"minified": "composer/2.0", "packages": {"psr/simple-cache": [{"name": "psr/simple-cache", "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "simple-cache", "psr-16"], "homepage": "", "version": "3.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "type": "zip", "shasum": "", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "type": "library", "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "funding": [], "time": "2021-10-29T13:26:27+00:00", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require": {"php": ">=8.0.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/8707bf3cea6f710bf6ef05491234e3ab06f6432a", "type": "zip", "shasum": "", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a"}, "support": {"source": "https://github.com/php-fig/simple-cache/tree/2.0.0"}, "time": "2021-10-29T13:22:09+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}, {"version": "1.0.1", "version_normalized": "*******", "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "type": "zip", "shasum": "", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=5.3.0"}, "funding": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "753fa598e8f3b9966c886fe13f370baa45ef0e24"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/753fa598e8f3b9966c886fe13f370baa45ef0e24", "type": "zip", "shasum": "", "reference": "753fa598e8f3b9966c886fe13f370baa45ef0e24"}, "support": {"source": "https://github.com/php-fig/simple-cache/tree/1.0.0"}, "time": "2017-01-02T13:31:39+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "7dcf8811a19f589909a041bbca3cefe57f6b37f5"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/7dcf8811a19f589909a041bbca3cefe57f6b37f5", "type": "zip", "shasum": "", "reference": "7dcf8811a19f589909a041bbca3cefe57f6b37f5"}, "support": {"source": "https://github.com/php-fig/simple-cache/tree/0.3.0"}, "time": "2016-12-09T08:51:20+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "13f43ba7f6d5ce37c6c115c26a5f653c2d9c1e18"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/13f43ba7f6d5ce37c6c115c26a5f653c2d9c1e18", "type": "zip", "shasum": "", "reference": "13f43ba7f6d5ce37c6c115c26a5f653c2d9c1e18"}, "support": {"source": "https://github.com/php-fig/simple-cache/tree/0.2.0"}, "time": "2016-11-14T21:07:45+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/simple-cache.git", "type": "git", "reference": "6b67c817eb0e6a933e12dc29ef90ccbb53d8dc79"}, "dist": {"url": "https://api.github.com/repos/php-fig/simple-cache/zipball/6b67c817eb0e6a933e12dc29ef90ccbb53d8dc79", "type": "zip", "shasum": "", "reference": "6b67c817eb0e6a933e12dc29ef90ccbb53d8dc79"}, "support": {"source": "https://github.com/php-fig/simple-cache/tree/0.1.0"}, "time": "2016-09-01T17:36:54+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 04 Apr 2024 15:48:10 GMT"}