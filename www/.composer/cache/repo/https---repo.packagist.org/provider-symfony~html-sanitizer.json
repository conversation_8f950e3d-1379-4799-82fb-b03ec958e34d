{"minified": "composer/2.0", "packages": {"symfony/html-sanitizer": [{"name": "symfony/html-sanitizer", "description": "Provides an object-oriented API to sanitize untrusted HTML input for safe insertion into a document's DOM.", "keywords": ["html", "sanitizer", "Purifier"], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "cf21254e982b12276329940ca4af5e623ee06c58"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/cf21254e982b12276329940ca4af5e623ee06c58", "type": "zip", "shasum": "", "reference": "cf21254e982b12276329940ca4af5e623ee06c58"}, "type": "library", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-31T08:49:55+00:00", "autoload": {"psr-4": {"Symfony\\Component\\HtmlSanitizer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "ext-dom": "*", "league/uri": "^6.5|^7.0", "masterminds/html5": "^2.7.2"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.3.0-BETA1"}}, {"version": "v7.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "1bd0c8fd5938d9af3f081a7c43d360ddefd494ca"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/1bd0c8fd5938d9af3f081a7c43d360ddefd494ca", "type": "zip", "shasum": "", "reference": "1bd0c8fd5938d9af3f081a7c43d360ddefd494ca"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.2.6"}, "time": "2025-03-31T08:29:03+00:00"}, {"version": "v7.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "91443febe34cfa5e8e00425f892e6316db95bc23"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/91443febe34cfa5e8e00425f892e6316db95bc23", "type": "zip", "shasum": "", "reference": "91443febe34cfa5e8e00425f892e6316db95bc23"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.2.3"}, "time": "2025-01-27T11:08:17+00:00"}, {"version": "v7.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "f6bc679b024e30f27e33815930a5b8b304c79813"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/f6bc679b024e30f27e33815930a5b8b304c79813", "type": "zip", "shasum": "", "reference": "f6bc679b024e30f27e33815930a5b8b304c79813"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.2.2"}, "time": "2024-12-30T18:35:15+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "1d23de45af5e8508441ff5f82bb493e83cdcbba4"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/1d23de45af5e8508441ff5f82bb493e83cdcbba4", "type": "zip", "shasum": "", "reference": "1d23de45af5e8508441ff5f82bb493e83cdcbba4"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.2.0"}, "time": "2024-09-25T14:21:43+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.2.0-BETA1"}}, {"version": "v7.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "5d928d0dc7823d703282bded46ba80008ce40d81"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/5d928d0dc7823d703282bded46ba80008ce40d81", "type": "zip", "shasum": "", "reference": "5d928d0dc7823d703282bded46ba80008ce40d81"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.11"}, "time": "2025-01-27T10:57:12+00:00"}, {"version": "v7.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "f207dc5c29ae93611586fe8f22df25d94d5d0ca4"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/f207dc5c29ae93611586fe8f22df25d94d5d0ca4", "type": "zip", "shasum": "", "reference": "f207dc5c29ae93611586fe8f22df25d94d5d0ca4"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.10"}, "time": "2024-12-30T18:35:03+00:00"}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "a25620fc6407e14331f3c0c5668eb4f35c392d4a"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/a25620fc6407e14331f3c0c5668eb4f35c392d4a", "type": "zip", "shasum": "", "reference": "a25620fc6407e14331f3c0c5668eb4f35c392d4a"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "89bf376c056926bd7fe8a81c0f486a060e20fdbc"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/89bf376c056926bd7fe8a81c0f486a060e20fdbc", "type": "zip", "shasum": "", "reference": "89bf376c056926bd7fe8a81c0f486a060e20fdbc"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.5"}, "time": "2024-09-20T13:35:23+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "737cbaa8082b696d0574afd91b9f471eca67fc65"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/737cbaa8082b696d0574afd91b9f471eca67fc65", "type": "zip", "shasum": "", "reference": "737cbaa8082b696d0574afd91b9f471eca67fc65"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.1"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "a9162f2ca36a200b666f7cd1b1b2f28d1b7dc55d"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/a9162f2ca36a200b666f7cd1b1b2f28d1b7dc55d", "type": "zip", "shasum": "", "reference": "a9162f2ca36a200b666f7cd1b1b2f28d1b7dc55d"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.0"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.1.0-BETA1"}}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "737cbaa8082b696d0574afd91b9f471eca67fc65"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/737cbaa8082b696d0574afd91b9f471eca67fc65", "type": "zip", "shasum": "", "reference": "737cbaa8082b696d0574afd91b9f471eca67fc65"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "a9162f2ca36a200b666f7cd1b1b2f28d1b7dc55d"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/a9162f2ca36a200b666f7cd1b1b2f28d1b7dc55d", "type": "zip", "shasum": "", "reference": "a9162f2ca36a200b666f7cd1b1b2f28d1b7dc55d"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "a8543ad56bc5250378ca44bb3988516fcb073c5d"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/a8543ad56bc5250378ca44bb3988516fcb073c5d", "type": "zip", "shasum": "", "reference": "a8543ad56bc5250378ca44bb3988516fcb073c5d"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.4"}, "time": "2024-02-15T11:33:06+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "58f143feaaf56e75e2131dea5c288490ec27926e"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/58f143feaaf56e75e2131dea5c288490ec27926e", "type": "zip", "shasum": "", "reference": "58f143feaaf56e75e2131dea5c288490ec27926e"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "388787213379c85f07ce270afd78c5b7cff3b0f6"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/388787213379c85f07ce270afd78c5b7cff3b0f6", "type": "zip", "shasum": "", "reference": "388787213379c85f07ce270afd78c5b7cff3b0f6"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.0"}, "time": "2023-10-28T23:12:22+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.0-BETA2"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "54891da1de881e3339e2137e271b0ff75e994b71"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/54891da1de881e3339e2137e271b0ff75e994b71", "type": "zip", "shasum": "", "reference": "54891da1de881e3339e2137e271b0ff75e994b71"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v7.0.0-BETA1"}, "time": "2023-10-19T15:45:57+00:00"}, {"version": "v6.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "f66d6585c6ece946239317c339f8b2860dfdf2db"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/f66d6585c6ece946239317c339f8b2860dfdf2db", "type": "zip", "shasum": "", "reference": "f66d6585c6ece946239317c339f8b2860dfdf2db"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.21"}, "time": "2025-03-31T07:29:45+00:00", "require": {"php": ">=8.1", "ext-dom": "*", "league/uri": "^6.5|^7.0", "masterminds/html5": "^2.7.2"}}, {"version": "v6.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "28e9fb12a6784c64b1b5e6fc92853bb7a3c4bf05"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/28e9fb12a6784c64b1b5e6fc92853bb7a3c4bf05", "type": "zip", "shasum": "", "reference": "28e9fb12a6784c64b1b5e6fc92853bb7a3c4bf05"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.18"}, "time": "2025-01-17T13:18:31+00:00"}, {"version": "v6.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "7f1692593d6e66c497b6588b86999728b50f24e5"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/7f1692593d6e66c497b6588b86999728b50f24e5", "type": "zip", "shasum": "", "reference": "7f1692593d6e66c497b6588b86999728b50f24e5"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.17"}, "time": "2024-12-29T20:34:08+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "881f62f73376bcd97cdfebec4069338a16e2b76c"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/881f62f73376bcd97cdfebec4069338a16e2b76c", "type": "zip", "shasum": "", "reference": "881f62f73376bcd97cdfebec4069338a16e2b76c"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "b58efe8ed0d8f5bf84913380a2f9da0c242f4200"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/b58efe8ed0d8f5bf84913380a2f9da0c242f4200", "type": "zip", "shasum": "", "reference": "b58efe8ed0d8f5bf84913380a2f9da0c242f4200"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.12"}, "time": "2024-09-20T08:21:33+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "9de29a710320ee802374e479169c5a82f9ee7854"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/9de29a710320ee802374e479169c5a82f9ee7854", "type": "zip", "shasum": "", "reference": "9de29a710320ee802374e479169c5a82f9ee7854"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.8"}, "time": "2024-05-31T14:51:39+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "0ff5d77e3160c15db3dbc3515a4f0143e3e4a218"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/0ff5d77e3160c15db3dbc3515a4f0143e3e4a218", "type": "zip", "shasum": "", "reference": "0ff5d77e3160c15db3dbc3515a4f0143e3e4a218"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "83e1dc8b49345e078cfa21bd4c563dfa99c5ed63"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/83e1dc8b49345e078cfa21bd4c563dfa99c5ed63", "type": "zip", "shasum": "", "reference": "83e1dc8b49345e078cfa21bd4c563dfa99c5ed63"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.4"}, "time": "2024-02-13T16:25:19+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "116335ab09e10b05405f01d8afd31ccc3832b08b"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/116335ab09e10b05405f01d8afd31ccc3832b08b", "type": "zip", "shasum": "", "reference": "116335ab09e10b05405f01d8afd31ccc3832b08b"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "9cc71f272eb62504872c80845074f236e8e43536"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/9cc71f272eb62504872c80845074f236e8e43536", "type": "zip", "shasum": "", "reference": "9cc71f272eb62504872c80845074f236e8e43536"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.0"}, "time": "2023-10-28T23:12:08+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.0-BETA2"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "992083095bb5ae67b85a35ac13c1f7a24125197b"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/992083095bb5ae67b85a35ac13c1f7a24125197b", "type": "zip", "shasum": "", "reference": "992083095bb5ae67b85a35ac13c1f7a24125197b"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.4.0-BETA1"}, "time": "2023-10-19T13:30:34+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "77d508d1d91f9894a9d7b2384bf832a70197752a"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/77d508d1d91f9894a9d7b2384bf832a70197752a", "type": "zip", "shasum": "", "reference": "77d508d1d91f9894a9d7b2384bf832a70197752a"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.3.12"}, "time": "2024-01-23T14:35:58+00:00"}, {"version": "v6.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "45e5a24b63d394fa6472c595df448aecfd1e1ea5"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/45e5a24b63d394fa6472c595df448aecfd1e1ea5", "type": "zip", "shasum": "", "reference": "45e5a24b63d394fa6472c595df448aecfd1e1ea5"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.3.7"}, "time": "2023-10-27T13:27:27+00:00"}, {"version": "v6.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "947492c7351d6b01a7b38e515c98fb1107dc357d"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/947492c7351d6b01a7b38e515c98fb1107dc357d", "type": "zip", "shasum": "", "reference": "947492c7351d6b01a7b38e515c98fb1107dc357d"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.3.4"}, "time": "2023-08-23T13:34:34+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "eae9b0a9ad7a2ed1963f819547d59ff99ad9e0fd"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/eae9b0a9ad7a2ed1963f819547d59ff99ad9e0fd", "type": "zip", "shasum": "", "reference": "eae9b0a9ad7a2ed1963f819547d59ff99ad9e0fd"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.3.0"}, "time": "2023-02-14T09:04:20+00:00", "require": {"php": ">=8.1", "ext-dom": "*", "league/uri": "^6.5", "masterminds/html5": "^2.7.2"}}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.3.0-BETA1"}}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "211e36bbb20a5e5db2b940399ab1d2b421a08068"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/211e36bbb20a5e5db2b940399ab1d2b421a08068", "type": "zip", "shasum": "", "reference": "211e36bbb20a5e5db2b940399ab1d2b421a08068"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.7"}, "time": "2023-02-14T08:44:56+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "9f7eb169f929b6f75fee7218a128856a63694cc6"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/9f7eb169f929b6f75fee7218a128856a63694cc6", "type": "zip", "shasum": "", "reference": "9f7eb169f929b6f75fee7218a128856a63694cc6"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.5"}, "time": "2023-01-01T08:37:24+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "f978fcf5f2d66c66bf9922f8589b9be9a2b9526a"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/f978fcf5f2d66c66bf9922f8589b9be9a2b9526a", "type": "zip", "shasum": "", "reference": "f978fcf5f2d66c66bf9922f8589b9be9a2b9526a"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.2"}, "time": "2022-12-14T10:28:02+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "28d19124099e860ef52da06ae251e37738b750fe"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/28d19124099e860ef52da06ae251e37738b750fe", "type": "zip", "shasum": "", "reference": "28d19124099e860ef52da06ae251e37738b750fe"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.0"}, "time": "2022-05-06T15:18:34+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.0-BETA1"}}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "9f7eb169f929b6f75fee7218a128856a63694cc6"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/9f7eb169f929b6f75fee7218a128856a63694cc6", "type": "zip", "shasum": "", "reference": "9f7eb169f929b6f75fee7218a128856a63694cc6"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.1.11"}, "time": "2023-01-01T08:37:24+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "f978fcf5f2d66c66bf9922f8589b9be9a2b9526a"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/f978fcf5f2d66c66bf9922f8589b9be9a2b9526a", "type": "zip", "shasum": "", "reference": "f978fcf5f2d66c66bf9922f8589b9be9a2b9526a"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.2.2"}, "time": "2022-12-14T10:28:02+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "28d19124099e860ef52da06ae251e37738b750fe"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/28d19124099e860ef52da06ae251e37738b750fe", "type": "zip", "shasum": "", "reference": "28d19124099e860ef52da06ae251e37738b750fe"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.1.0"}, "time": "2022-05-06T15:18:34+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "867cfda8d636cc1cab0eda674300a0d47c96eb69"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/867cfda8d636cc1cab0eda674300a0d47c96eb69", "type": "zip", "shasum": "", "reference": "867cfda8d636cc1cab0eda674300a0d47c96eb69"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.1.0-BETA2"}, "time": "2022-04-16T13:37:21+00:00", "require": {"php": ">=8.1", "ext-dom": "*", "league/uri": "^6.5", "masterminds/html5": "^2.4"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/html-sanitizer.git", "type": "git", "reference": "886c35d0c2d5dab5a161a5933b2df787ac0bbb55"}, "dist": {"url": "https://api.github.com/repos/symfony/html-sanitizer/zipball/886c35d0c2d5dab5a161a5933b2df787ac0bbb55", "type": "zip", "shasum": "", "reference": "886c35d0c2d5dab5a161a5933b2df787ac0bbb55"}, "support": {"source": "https://github.com/symfony/html-sanitizer/tree/v6.1.0-BETA1"}, "time": "2022-02-25T11:15:52+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 29 May 2025 07:49:13 GMT"}