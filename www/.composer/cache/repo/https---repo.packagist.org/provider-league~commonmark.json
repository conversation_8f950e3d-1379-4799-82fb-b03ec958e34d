{"minified": "composer/2.0", "packages": {"league/commonmark": [{"name": "league/commonmark", "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "keywords": ["parser", "markdown", "github", "md", "gfm", "commonmark", "flavored", "github-flavored"], "homepage": "https://commonmark.thephpleague.com", "version": "2.7.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "6fbb36d44824ed4091adbcf4c7d4a3923cdb3405"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/6fbb36d44824ed4091adbcf4c7d4a3923cdb3405", "type": "zip", "shasum": "", "reference": "6fbb36d44824ed4091adbcf4c7d4a3923cdb3405"}, "type": "library", "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2025-05-05T12:20:28+00:00", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "extra": {"branch-alias": {"dev-main": "2.8-dev"}}, "require": {"php": "^7.4 || ^8.0", "ext-mbstring": "*", "league/config": "^1.1.1", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.31.1", "commonmark/commonmark.js": "0.31.1", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 | ^7.0", "symfony/process": "^5.4 | ^6.0 | ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 | ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}}, {"version": "2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "06c3b0bf2540338094575612f4a1778d0d2d5e94"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/06c3b0bf2540338094575612f4a1778d0d2d5e94", "type": "zip", "shasum": "", "reference": "06c3b0bf2540338094575612f4a1778d0d2d5e94"}, "time": "2025-04-18T21:09:27+00:00", "extra": {"branch-alias": {"dev-main": "2.7-dev"}}}, {"version": "2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d990688c91cedfb69753ffc2512727ec646df2ad"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d990688c91cedfb69753ffc2512727ec646df2ad", "type": "zip", "shasum": "", "reference": "d990688c91cedfb69753ffc2512727ec646df2ad"}, "time": "2024-12-29T14:10:59+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d150f911e0079e90ae3c106734c93137c184f932"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d150f911e0079e90ae3c106734c93137c184f932", "type": "zip", "shasum": "", "reference": "d150f911e0079e90ae3c106734c93137c184f932"}, "time": "2024-12-07T15:34:16+00:00"}, {"version": "2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "b650144166dfa7703e62a22e493b853b58d874b0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/b650144166dfa7703e62a22e493b853b58d874b0", "type": "zip", "shasum": "", "reference": "b650144166dfa7703e62a22e493b853b58d874b0"}, "time": "2024-08-16T11:46:16+00:00", "extra": {"branch-alias": {"dev-main": "2.6-dev"}}, "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.31.1", "commonmark/commonmark.js": "0.31.1", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 || ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 || ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "df09d5b6a4188f8f3c3ab2e43a109076a5eeb767"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/df09d5b6a4188f8f3c3ab2e43a109076a5eeb767", "type": "zip", "shasum": "", "reference": "df09d5b6a4188f8f3c3ab2e43a109076a5eeb767"}, "time": "2024-08-14T10:56:57+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.31.0", "commonmark/commonmark.js": "0.31.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 || ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 || ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "ac815920de0eff6de947eac0a6a94e5ed0fb147c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/ac815920de0eff6de947eac0a6a94e5ed0fb147c", "type": "zip", "shasum": "", "reference": "ac815920de0eff6de947eac0a6a94e5ed0fb147c"}, "time": "2024-07-24T12:52:09+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "0026475f5c9a104410ae824cb5a4d63fa3bdb1df"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/0026475f5c9a104410ae824cb5a4d63fa3bdb1df", "type": "zip", "shasum": "", "reference": "0026475f5c9a104410ae824cb5a4d63fa3bdb1df"}, "time": "2024-07-22T18:18:14+00:00"}, {"version": "2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "50bd4dce0d53e1b3b51a153fc2e44a7dc3f7ee5b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/50bd4dce0d53e1b3b51a153fc2e44a7dc3f7ee5b", "type": "zip", "shasum": "", "reference": "50bd4dce0d53e1b3b51a153fc2e44a7dc3f7ee5b"}, "time": "2024-07-22T17:11:18+00:00", "extra": {"branch-alias": {"dev-main": "2.5-dev"}}, "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.3", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 || ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 || ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}}, {"version": "2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "b7a7af3a23a818dcc5836e62e93e4b9ce4704481"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/b7a7af3a23a818dcc5836e62e93e4b9ce4704481", "type": "zip", "shasum": "", "reference": "b7a7af3a23a818dcc5836e62e93e4b9ce4704481"}, "time": "2024-07-22T12:31:21+00:00"}, {"version": "2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "91c24291965bd6d7c46c46a12ba7492f83b1cadf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/91c24291965bd6d7c46c46a12ba7492f83b1cadf", "type": "zip", "shasum": "", "reference": "91c24291965bd6d7c46c46a12ba7492f83b1cadf"}, "time": "2024-02-02T11:59:32+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "3669d6d5f7a47a93c08ddff335e6d945481a1dd5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/3669d6d5f7a47a93c08ddff335e6d945481a1dd5", "type": "zip", "shasum": "", "reference": "3669d6d5f7a47a93c08ddff335e6d945481a1dd5"}, "time": "2023-08-30T16:55:00+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d44a24690f16b8c1808bf13b1bd54ae4c63ea048"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d44a24690f16b8c1808bf13b1bd54ae4c63ea048", "type": "zip", "shasum": "", "reference": "d44a24690f16b8c1808bf13b1bd54ae4c63ea048"}, "time": "2023-03-24T15:16:10+00:00"}, {"version": "2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c1e114f74e518daca2729ea8c4bf1167038fa4b5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c1e114f74e518daca2729ea8c4bf1167038fa4b5", "type": "zip", "shasum": "", "reference": "c1e114f74e518daca2729ea8c4bf1167038fa4b5"}, "time": "2023-02-15T14:07:24+00:00", "extra": {"branch-alias": {"dev-main": "2.4-dev"}}}, {"version": "2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c493585c130544c4e91d2e0e131e6d35cb0cbc47"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c493585c130544c4e91d2e0e131e6d35cb0cbc47", "type": "zip", "shasum": "", "reference": "c493585c130544c4e91d2e0e131e6d35cb0cbc47"}, "time": "2022-12-10T16:02:17+00:00"}, {"version": "2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "a36bd2be4f5387c0f3a8792a0d76b7d68865abbf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/a36bd2be4f5387c0f3a8792a0d76b7d68865abbf", "type": "zip", "shasum": "", "reference": "a36bd2be4f5387c0f3a8792a0d76b7d68865abbf"}, "time": "2022-11-03T17:29:46+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0"}}, {"version": "2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "857afc47ce113454bd629037213378ba3219dd40"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/857afc47ce113454bd629037213378ba3219dd40", "type": "zip", "shasum": "", "reference": "857afc47ce113454bd629037213378ba3219dd40"}, "time": "2022-10-30T16:45:38+00:00"}, {"version": "2.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "84d74485fdb7074f4f9dd6f02ab957b1de513257"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/84d74485fdb7074f4f9dd6f02ab957b1de513257", "type": "zip", "shasum": "", "reference": "84d74485fdb7074f4f9dd6f02ab957b1de513257"}, "time": "2022-07-29T10:59:45+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0"}}, {"version": "2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "155ec1c95626b16fda0889cf15904d24890a60d5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/155ec1c95626b16fda0889cf15904d24890a60d5", "type": "zip", "shasum": "", "reference": "155ec1c95626b16fda0889cf15904d24890a60d5"}, "time": "2022-07-17T16:25:47+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "0da1dca5781dd3cfddbe328224d9a7a62571addc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/0da1dca5781dd3cfddbe328224d9a7a62571addc", "type": "zip", "shasum": "", "reference": "0da1dca5781dd3cfddbe328224d9a7a62571addc"}, "time": "2022-06-07T21:28:26+00:00"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "6eddb90a9e4a1a8c5773226068fcfb48cb36812a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/6eddb90a9e4a1a8c5773226068fcfb48cb36812a", "type": "zip", "shasum": "", "reference": "6eddb90a9e4a1a8c5773226068fcfb48cb36812a"}, "time": "2022-06-03T14:07:39+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "cb36fee279f7fca01d5d9399ddd1b37e48e2eca1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/cb36fee279f7fca01d5d9399ddd1b37e48e2eca1", "type": "zip", "shasum": "", "reference": "cb36fee279f7fca01d5d9399ddd1b37e48e2eca1"}, "time": "2022-05-14T15:37:39+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "32a49eb2b38fe5e5c417ab748a45d0beaab97955"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/32a49eb2b38fe5e5c417ab748a45d0beaab97955", "type": "zip", "shasum": "", "reference": "32a49eb2b38fe5e5c417ab748a45d0beaab97955"}, "time": "2022-04-07T22:37:05+00:00"}, {"version": "2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "3a466decc0bd43d9f63ed7c008043a4e9a9cb1b7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/3a466decc0bd43d9f63ed7c008043a4e9a9cb1b7", "type": "zip", "shasum": "", "reference": "3a466decc0bd43d9f63ed7c008043a4e9a9cb1b7"}, "time": "2022-06-03T14:05:26+00:00", "extra": {"branch-alias": {"dev-main": "2.3-dev"}}, "require": {"php": "^7.4 || ^8.0", "ext-mbstring": "*", "league/config": "^1.1.1", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "9981c77d62ebe1d3038e3cb3d79cd83bc50cb465"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/9981c77d62ebe1d3038e3cb3d79cd83bc50cb465", "type": "zip", "shasum": "", "reference": "9981c77d62ebe1d3038e3cb3d79cd83bc50cb465"}, "time": "2022-05-14T15:32:34+00:00"}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "47b015bc4e50fd4438c1ffef6139a1fb65d2ab71"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/47b015bc4e50fd4438c1ffef6139a1fb65d2ab71", "type": "zip", "shasum": "", "reference": "47b015bc4e50fd4438c1ffef6139a1fb65d2ab71"}, "time": "2022-02-26T21:24:45+00:00"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "13d7751377732637814f0cda0e3f6d3243f9f769"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/13d7751377732637814f0cda0e3f6d3243f9f769", "type": "zip", "shasum": "", "reference": "13d7751377732637814f0cda0e3f6d3243f9f769"}, "time": "2022-02-13T15:00:57+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "f8afb78f087777b040e0ab8a6b6ca93f6fc3f18a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/f8afb78f087777b040e0ab8a6b6ca93f6fc3f18a", "type": "zip", "shasum": "", "reference": "f8afb78f087777b040e0ab8a6b6ca93f6fc3f18a"}, "time": "2022-01-25T14:37:33+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c5aadcc15548629787d02b86a7afef03b46271b5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c5aadcc15548629787d02b86a7afef03b46271b5", "type": "zip", "shasum": "", "reference": "c5aadcc15548629787d02b86a7afef03b46271b5"}, "time": "2022-01-22T14:06:22+00:00", "require": {"php": "^7.4 || ^8.0", "ext-mbstring": "*", "league/config": "^1.1.1", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^v2.1 || ^3.0", "symfony/polyfill-php80": "^1.15"}}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "7cdfc405d1c0ac925965f4f04e75a2652120e9c0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/7cdfc405d1c0ac925965f4f04e75a2652120e9c0", "type": "zip", "shasum": "", "reference": "7cdfc405d1c0ac925965f4f04e75a2652120e9c0"}, "time": "2022-02-26T21:20:51+00:00", "extra": {"branch-alias": {"dev-main": "2.2-dev"}}, "require": {"php": "^7.4 || ^8.0", "ext-mbstring": "*", "league/config": "^1.1.1", "psr/event-dispatcher": "^1.0", "symfony/polyfill-php80": "^1.15"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "34712ca82dc1bc44e0bc2594b9ad472b66195ab8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/34712ca82dc1bc44e0bc2594b9ad472b66195ab8", "type": "zip", "shasum": "", "reference": "34712ca82dc1bc44e0bc2594b9ad472b66195ab8"}, "time": "2022-02-13T14:56:04+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "17d2b9cb5161a2ea1a8dd30e6991d668e503fb9d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/17d2b9cb5161a2ea1a8dd30e6991d668e503fb9d", "type": "zip", "shasum": "", "reference": "17d2b9cb5161a2ea1a8dd30e6991d668e503fb9d"}, "time": "2022-01-02T18:25:06+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "819276bc54e83c160617d3ac0a436c239e479928"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/819276bc54e83c160617d3ac0a436c239e479928", "type": "zip", "shasum": "", "reference": "819276bc54e83c160617d3ac0a436c239e479928"}, "time": "2021-12-05T18:25:20+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "39031adf944087c1d5a4fd9875c65e668f986e96"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/39031adf944087c1d5a4fd9875c65e668f986e96", "type": "zip", "shasum": "", "reference": "39031adf944087c1d5a4fd9875c65e668f986e96"}, "time": "2022-02-26T21:09:14+00:00", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "phpstan/phpstan": "^0.12.88", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "36949f88678df34526871f32055bce429be23b13"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/36949f88678df34526871f32055bce429be23b13", "type": "zip", "shasum": "", "reference": "36949f88678df34526871f32055bce429be23b13"}, "time": "2022-02-13T14:21:46+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "2df87709f44b0dd733df86aef0830dce9b1f0f13"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2df87709f44b0dd733df86aef0830dce9b1f0f13", "type": "zip", "shasum": "", "reference": "2df87709f44b0dd733df86aef0830dce9b1f0f13"}, "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2021-08-14T14:06:04+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "0d57f20aa03129ee7ef5f690e634884315d4238c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/0d57f20aa03129ee7ef5f690e634884315d4238c", "type": "zip", "shasum": "", "reference": "0d57f20aa03129ee7ef5f690e634884315d4238c"}, "time": "2021-07-31T19:15:22+00:00", "require": {"php": "^7.4 || ^8.0", "ext-mbstring": "*", "league/config": "^1.1", "psr/event-dispatcher": "^1.0", "symfony/polyfill-php80": "^1.15"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "167142baf9a6b946f99ad9325b06028606f8238e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/167142baf9a6b946f99ad9325b06028606f8238e", "type": "zip", "shasum": "", "reference": "167142baf9a6b946f99ad9325b06028606f8238e"}, "time": "2021-07-24T20:12:58+00:00"}, {"version": "2.0.0-rc2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "61f4efe57db6b8c02a1470dd0894fbacf23ef19b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/61f4efe57db6b8c02a1470dd0894fbacf23ef19b", "type": "zip", "shasum": "", "reference": "61f4efe57db6b8c02a1470dd0894fbacf23ef19b"}, "time": "2021-07-17T17:20:25+00:00"}, {"version": "2.0.0-rc1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "4b7060e5b7aad24c34e72581cbb9728997394765"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/4b7060e5b7aad24c34e72581cbb9728997394765", "type": "zip", "shasum": "", "reference": "4b7060e5b7aad24c34e72581cbb9728997394765"}, "time": "2021-07-10T19:56:08+00:00"}, {"version": "2.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d8b7646c9b8c7e21c6753a77e1cb6c893e0bbcdc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d8b7646c9b8c7e21c6753a77e1cb6c893e0bbcdc", "type": "zip", "shasum": "", "reference": "d8b7646c9b8c7e21c6753a77e1cb6c893e0bbcdc"}, "time": "2021-07-03T22:34:08+00:00"}, {"version": "2.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "68ef5589dec9fb78d35353a018890eeb1b34d915"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/68ef5589dec9fb78d35353a018890eeb1b34d915", "type": "zip", "shasum": "", "reference": "68ef5589dec9fb78d35353a018890eeb1b34d915"}, "time": "2021-06-27T16:40:05+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "phpstan/phpstan": "^0.12.88", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"version": "2.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "8cde9a26daaaf6bfb0f5b1fdf7c0d7127e5b18be"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/8cde9a26daaaf6bfb0f5b1fdf7c0d7127e5b18be", "type": "zip", "shasum": "", "reference": "8cde9a26daaaf6bfb0f5b1fdf7c0d7127e5b18be"}, "time": "2021-06-20T19:31:00+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "^1.0", "commonmark/commonmark.js": "0.29.2", "composer/package-versions-deprecated": "^1.8", "erusev/parsedown": "^1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "phpstan/phpstan": "^0.12.88", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and Github-Flavored Markdown (GFM)", "version": "1.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "2b8185c13bc9578367a5bf901881d1c1b5bbd09b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2b8185c13bc9578367a5bf901881d1c1b5bbd09b", "type": "zip", "shasum": "", "reference": "2b8185c13bc9578367a5bf901881d1c1b5bbd09b"}, "support": {"docs": "https://commonmark.thephpleague.com/", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2022-01-13T17:18:13+00:00", "bin": ["bin/commonmark"], "require": {"php": "^7.1 || ^8.0", "ext-mbstring": "*"}, "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.2", "erusev/parsedown": "~1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12.90", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "extra": "__unset", "suggest": "__unset"}, {"version": "1.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c4228d11e30d7493c6836d20872f9582d8ba6dcf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c4228d11e30d7493c6836d20872f9582d8ba6dcf", "type": "zip", "shasum": "", "reference": "c4228d11e30d7493c6836d20872f9582d8ba6dcf"}, "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2021-07-17T17:13:23+00:00"}, {"version": "1.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "44ffd8d3c4a9133e4bd0548622b09c55af39db5f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/44ffd8d3c4a9133e4bd0548622b09c55af39db5f", "type": "zip", "shasum": "", "reference": "44ffd8d3c4a9133e4bd0548622b09c55af39db5f"}, "time": "2021-06-26T11:57:13+00:00"}, {"version": "1.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c3c8b7217c52572fb42aaf84211abccf75a151b2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c3c8b7217c52572fb42aaf84211abccf75a151b2", "type": "zip", "shasum": "", "reference": "c3c8b7217c52572fb42aaf84211abccf75a151b2"}, "time": "2021-06-19T20:08:14+00:00"}, {"version": "1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "9b8cd7e3f234f15a25204e1e875571af7cc3dc8b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/9b8cd7e3f234f15a25204e1e875571af7cc3dc8b", "type": "zip", "shasum": "", "reference": "9b8cd7e3f234f15a25204e1e875571af7cc3dc8b"}, "time": "2021-06-19T15:03:35+00:00"}, {"version": "1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "7d70d2f19c84bcc16275ea47edabee24747352eb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/7d70d2f19c84bcc16275ea47edabee24747352eb", "type": "zip", "shasum": "", "reference": "7d70d2f19c84bcc16275ea47edabee24747352eb"}, "time": "2021-05-12T11:39:41+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.2", "erusev/parsedown": "~1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "2651c497f005de305c7ba3f232cbd87b8c00ee8c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2651c497f005de305c7ba3f232cbd87b8c00ee8c", "type": "zip", "shasum": "", "reference": "2651c497f005de305c7ba3f232cbd87b8c00ee8c"}, "time": "2021-05-08T16:08:00+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "19a9673b833cc37770439097b381d86cd125bfe8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/19a9673b833cc37770439097b381d86cd125bfe8", "type": "zip", "shasum": "", "reference": "19a9673b833cc37770439097b381d86cd125bfe8"}, "time": "2021-05-01T19:00:49+00:00"}, {"version": "1.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "08fa59b8e4e34ea8a773d55139ae9ac0e0aecbaf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/08fa59b8e4e34ea8a773d55139ae9ac0e0aecbaf", "type": "zip", "shasum": "", "reference": "08fa59b8e4e34ea8a773d55139ae9ac0e0aecbaf"}, "time": "2021-03-28T18:51:39+00:00"}, {"version": "1.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/11df9b36fd4f1d2b727a73bf14931d81373b9a54", "type": "zip", "shasum": "", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54"}, "time": "2020-10-31T13:49:32+00:00"}, {"version": "1.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "a56e91e0fa1f6d0049153a9c34f63488f6b7ce61"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/a56e91e0fa1f6d0049153a9c34f63488f6b7ce61", "type": "zip", "shasum": "", "reference": "a56e91e0fa1f6d0049153a9c34f63488f6b7ce61"}, "time": "2020-10-17T21:33:03+00:00"}, {"version": "1.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "45832dfed6007b984c0d40addfac48d403dc6432"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/45832dfed6007b984c0d40addfac48d403dc6432", "type": "zip", "shasum": "", "reference": "45832dfed6007b984c0d40addfac48d403dc6432"}, "time": "2020-09-13T14:44:46+00:00"}, {"version": "1.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "21819c989e69bab07e933866ad30c7e3f32984ba"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/21819c989e69bab07e933866ad30c7e3f32984ba", "type": "zip", "shasum": "", "reference": "21819c989e69bab07e933866ad30c7e3f32984ba"}, "time": "2020-08-18T01:19:12+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.1", "erusev/parsedown": "~1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "2574454b97e4103dc4e36917bd783b25624aefcd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2574454b97e4103dc4e36917bd783b25624aefcd", "type": "zip", "shasum": "", "reference": "2574454b97e4103dc4e36917bd783b25624aefcd"}, "time": "2020-07-19T22:47:30+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "9bc3b1d6148b3d9dfcced033f672d77cb8e535b1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/9bc3b1d6148b3d9dfcced033f672d77cb8e535b1", "type": "zip", "shasum": "", "reference": "9bc3b1d6148b3d9dfcced033f672d77cb8e535b1"}, "time": "2020-07-19T22:14:22+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "6d74caf6abeed5fd85d6ec20da23d7269cd0b46f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/6d74caf6abeed5fd85d6ec20da23d7269cd0b46f", "type": "zip", "shasum": "", "reference": "6d74caf6abeed5fd85d6ec20da23d7269cd0b46f"}, "time": "2020-06-27T12:50:08+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.1", "erusev/parsedown": "~1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "fc33ca12575e98e57cdce7d5f38b2ca5335714b3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/fc33ca12575e98e57cdce7d5f38b2ca5335714b3", "type": "zip", "shasum": "", "reference": "fc33ca12575e98e57cdce7d5f38b2ca5335714b3"}, "time": "2020-06-21T20:50:13+00:00"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "412639f7cfbc0b31ad2455b2fe965095f66ae505"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/412639f7cfbc0b31ad2455b2fe965095f66ae505", "type": "zip", "shasum": "", "reference": "412639f7cfbc0b31ad2455b2fe965095f66ae505"}, "time": "2020-05-04T22:15:21+00:00", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "require": {"php": "^7.1", "ext-mbstring": "*"}}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "9e780d972185e4f737a03bade0fd34a9e67bbf31"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/9e780d972185e4f737a03bade0fd34a9e67bbf31", "type": "zip", "shasum": "", "reference": "9e780d972185e4f737a03bade0fd34a9e67bbf31"}, "time": "2020-04-24T13:39:56+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c995966d35424bae20f76f8b31248099487a3f57"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c995966d35424bae20f76f8b31248099487a3f57", "type": "zip", "shasum": "", "reference": "c995966d35424bae20f76f8b31248099487a3f57"}, "time": "2020-04-20T13:36:51+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "517cbe1c6faf90afeb38a0e917c73acc6d3051ce"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/517cbe1c6faf90afeb38a0e917c73acc6d3051ce", "type": "zip", "shasum": "", "reference": "517cbe1c6faf90afeb38a0e917c73acc6d3051ce"}, "time": "2020-04-18T20:46:13+00:00"}, {"version": "1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "dd3261eb9a322e009fa5d96d19b9ae19708ce04b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/dd3261eb9a322e009fa5d96d19b9ae19708ce04b", "type": "zip", "shasum": "", "reference": "dd3261eb9a322e009fa5d96d19b9ae19708ce04b"}, "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league%2fcommonmark", "type": "tidelift"}], "time": "2020-04-13T20:52:18+00:00", "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.1", "erusev/parsedown": "~1.0", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan-shim": "^0.11.5", "phpunit/phpunit": "^7.5", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}}, {"version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "5a67afc2572ec6d430526cdc9c637ef124812389"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/5a67afc2572ec6d430526cdc9c637ef124812389", "type": "zip", "shasum": "", "reference": "5a67afc2572ec6d430526cdc9c637ef124812389"}, "funding": [{"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league%2fcommonmark", "type": "tidelift"}], "time": "2020-04-05T16:01:48+00:00"}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "75542a366ccbe1896ed79fcf3e8e68206d6c4257"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/75542a366ccbe1896ed79fcf3e8e68206d6c4257", "type": "zip", "shasum": "", "reference": "75542a366ccbe1896ed79fcf3e8e68206d6c4257"}, "time": "2020-03-25T19:55:28+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "8015f806173c6ee54de25a87c2d69736696e88db"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/8015f806173c6ee54de25a87c2d69736696e88db", "type": "zip", "shasum": "", "reference": "8015f806173c6ee54de25a87c2d69736696e88db"}, "time": "2020-02-28T18:53:50+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "4f30be7a2cbf3bfa5788abab71384713e48f451f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/4f30be7a2cbf3bfa5788abab71384713e48f451f", "type": "zip", "shasum": "", "reference": "4f30be7a2cbf3bfa5788abab71384713e48f451f"}, "time": "2020-02-08T23:42:03+00:00", "funding": "__unset"}, {"description": "PHP Markdown parser based on the CommonMark spec", "keywords": ["parser", "markdown", "commonmark"], "version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "34cf4ddb3892c715ae785c880e6691d839cff88d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/34cf4ddb3892c715ae785c880e6691d839cff88d", "type": "zip", "shasum": "", "reference": "34cf4ddb3892c715ae785c880e6691d839cff88d"}, "time": "2020-01-16T01:18:13+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.1", "erusev/parsedown": "~1.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan-shim": "^0.11.5", "phpunit/phpunit": "^7.5", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "suggest": {"league/commonmark-extras": "Library of useful extensions including smart punctuation"}, "replace": {"colinodell/commonmark-php": "*"}, "conflict": "__unset"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "74e08793c41c72c8ed7a22df803f2ffcaf77efb7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/74e08793c41c72c8ed7a22df803f2ffcaf77efb7", "type": "zip", "shasum": "", "reference": "74e08793c41c72c8ed7a22df803f2ffcaf77efb7"}, "time": "2020-01-15T03:32:39+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "2533c389fd2a7573d4f7be279b1c33cf941c8dfc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2533c389fd2a7573d4f7be279b1c33cf941c8dfc", "type": "zip", "shasum": "", "reference": "2533c389fd2a7573d4f7be279b1c33cf941c8dfc"}, "time": "2020-01-09T22:41:09+00:00"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "b9ffbab283593d94ed486f73ea2a2e8ac022e24b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/b9ffbab283593d94ed486f73ea2a2e8ac022e24b", "type": "zip", "shasum": "", "reference": "b9ffbab283593d94ed486f73ea2a2e8ac022e24b"}, "time": "2020-01-16T01:09:53+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "require-dev": {"ext-json": "*", "cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.0", "erusev/parsedown": "~1.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan-shim": "^0.11.5", "phpunit/phpunit": "^7.5", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "772e03fa9c6477ef5ef2d154fefd8a2a8d8ed03c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/772e03fa9c6477ef5ef2d154fefd8a2a8d8ed03c", "type": "zip", "shasum": "", "reference": "772e03fa9c6477ef5ef2d154fefd8a2a8d8ed03c"}, "time": "2019-12-10T02:55:03+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d74654d85954e3b9451d67faaebacd210fc70252"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d74654d85954e3b9451d67faaebacd210fc70252", "type": "zip", "shasum": "", "reference": "d74654d85954e3b9451d67faaebacd210fc70252"}, "time": "2019-11-11T22:23:29+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d927c05e9a391688b1e59a606c97465a90531789"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d927c05e9a391688b1e59a606c97465a90531789", "type": "zip", "shasum": "", "reference": "d927c05e9a391688b1e59a606c97465a90531789"}, "time": "2019-10-31T13:30:15+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "7a40f2b0931602c504c2a9692d9f1e33635fd5ef"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/7a40f2b0931602c504c2a9692d9f1e33635fd5ef", "type": "zip", "shasum": "", "reference": "7a40f2b0931602c504c2a9692d9f1e33635fd5ef"}, "time": "2019-06-29T11:19:01+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require-dev": {"cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.0", "erusev/parsedown": "~1.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan-shim": "^0.11.5", "phpunit/phpunit": "^7.5", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}}, {"version": "1.0.0-rc1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "a6721350ecae7594962e2cf99ff68b450cf20194"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/a6721350ecae7594962e2cf99ff68b450cf20194", "type": "zip", "shasum": "", "reference": "a6721350ecae7594962e2cf99ff68b450cf20194"}, "time": "2019-06-20T01:47:30+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "1.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "a080b1e8b38ebb2776fa5192391b2f3f024424f2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/a080b1e8b38ebb2776fa5192391b2f3f024424f2", "type": "zip", "shasum": "", "reference": "a080b1e8b38ebb2776fa5192391b2f3f024424f2"}, "time": "2019-06-05T21:46:17+00:00"}, {"version": "1.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "322dd4bbd56c5e5444088dd64bdca762245079c1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/322dd4bbd56c5e5444088dd64bdca762245079c1", "type": "zip", "shasum": "", "reference": "322dd4bbd56c5e5444088dd64bdca762245079c1"}, "time": "2019-05-28T00:52:33+00:00"}, {"version": "1.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "e96544a3ae4daf7d85ddc2544fe7b47cf3b48c99"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/e96544a3ae4daf7d85ddc2544fe7b47cf3b48c99", "type": "zip", "shasum": "", "reference": "e96544a3ae4daf7d85ddc2544fe7b47cf3b48c99"}, "time": "2019-05-27T17:48:57+00:00"}, {"version": "1.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "211be90ed241f45e688c2dcc3487d019802b0715"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/211be90ed241f45e688c2dcc3487d019802b0715", "type": "zip", "shasum": "", "reference": "211be90ed241f45e688c2dcc3487d019802b0715"}, "time": "2019-05-26T21:53:09+00:00"}, {"version": "0.19.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c6ecea928b432ae417fa9942840bbfd8c332448c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c6ecea928b432ae417fa9942840bbfd8c332448c", "type": "zip", "shasum": "", "reference": "c6ecea928b432ae417fa9942840bbfd8c332448c"}, "time": "2019-06-18T18:29:15+00:00", "extra": {"branch-alias": {"dev-master": "0.20-dev"}}}, {"version": "0.19.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "8401e1c1e409a08fcf558aadb6951c33749d701e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/8401e1c1e409a08fcf558aadb6951c33749d701e", "type": "zip", "shasum": "", "reference": "8401e1c1e409a08fcf558aadb6951c33749d701e"}, "time": "2019-05-19T13:39:36+00:00"}, {"version": "0.19.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d42b2d4a5d0a8eb2a4514f828ecb6f5db13a7c6f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d42b2d4a5d0a8eb2a4514f828ecb6f5db13a7c6f", "type": "zip", "shasum": "", "reference": "d42b2d4a5d0a8eb2a4514f828ecb6f5db13a7c6f"}, "time": "2019-04-11T04:37:01+00:00"}, {"version": "0.19.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "deb4f7ed434ba9b7a147efd3726be4895eb9b157"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/deb4f7ed434ba9b7a147efd3726be4895eb9b157", "type": "zip", "shasum": "", "reference": "deb4f7ed434ba9b7a147efd3726be4895eb9b157"}, "time": "2019-04-11T02:28:33+00:00"}, {"homepage": "https://github.com/thephpleague/commonmark", "version": "0.18.5", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "f94e18d68260f43a7d846279cad88405854b1306"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/f94e18d68260f43a7d846279cad88405854b1306", "type": "zip", "shasum": "", "reference": "f94e18d68260f43a7d846279cad88405854b1306"}, "time": "2019-03-28T13:52:31+00:00", "autoload": {"psr-4": {"League\\CommonMark\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "0.19-dev"}}, "require": {"php": ">=5.6.5", "ext-mbstring": "*"}, "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "commonmark/commonmark.js": "0.28", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.2", "phpunit/phpunit": "^5.7.27|^6.5.14", "symfony/finder": "^3.0|^4.0", "scrutinizer/ocular": "^1.1"}}, {"version": "0.18.4", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "93fa5e85c2e5e7f59ee2537449abc677096fb1c8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/93fa5e85c2e5e7f59ee2537449abc677096fb1c8", "type": "zip", "shasum": "", "reference": "93fa5e85c2e5e7f59ee2537449abc677096fb1c8"}, "time": "2019-03-24T02:45:32+00:00"}, {"version": "0.18.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "b1ec41ce15c3bd6f7cbe86a645b3efc78d927446"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/b1ec41ce15c3bd6f7cbe86a645b3efc78d927446", "type": "zip", "shasum": "", "reference": "b1ec41ce15c3bd6f7cbe86a645b3efc78d927446"}, "time": "2019-03-21T22:47:25+00:00"}, {"version": "0.18.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "ad51c7cafb90e0bbd9f34b71d18d05994547e352"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/ad51c7cafb90e0bbd9f34b71d18d05994547e352", "type": "zip", "shasum": "", "reference": "ad51c7cafb90e0bbd9f34b71d18d05994547e352"}, "time": "2019-03-17T01:41:59+00:00", "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "commonmark/commonmark.js": "0.28", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.2", "phpunit/phpunit": "^5.7|^6.5", "symfony/finder": "^3.0|^4.0", "scrutinizer/ocular": "^1.1"}}, {"version": "0.18.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "e5029f74ba39e043ce4b3ca6c05dc719d8aafd94"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/e5029f74ba39e043ce4b3ca6c05dc719d8aafd94", "type": "zip", "shasum": "", "reference": "e5029f74ba39e043ce4b3ca6c05dc719d8aafd94"}, "time": "2018-12-30T01:55:29+00:00"}, {"description": "Markdown parser for PHP based on the CommonMark spec", "version": "0.18.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "006af077d4b1b7eb1d9760964f9f984ba188632c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/006af077d4b1b7eb1d9760964f9f984ba188632c", "type": "zip", "shasum": "", "reference": "006af077d4b1b7eb1d9760964f9f984ba188632c"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/release-0.18.0"}, "time": "2018-09-18T13:13:55+00:00"}, {"version": "0.17.5", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "82d7ab62d7f68391cb9d323f3ccce50be24a5369"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/82d7ab62d7f68391cb9d323f3ccce50be24a5369", "type": "zip", "shasum": "", "reference": "82d7ab62d7f68391cb9d323f3ccce50be24a5369"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/release-0.17.5"}, "time": "2018-03-29T14:35:19+00:00", "extra": {"branch-alias": {"dev-master": "0.18-dev"}}}, {"version": "0.17.4", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "b02bfcbaca5a83666494eef5caa9d338c279a0ce"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/b02bfcbaca5a83666494eef5caa9d338c279a0ce", "type": "zip", "shasum": "", "reference": "b02bfcbaca5a83666494eef5caa9d338c279a0ce"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/master"}, "time": "2018-03-29T03:10:21+00:00"}, {"version": "0.17.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "fae7d27d6667365b0f497418014916d9918827ab"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/fae7d27d6667365b0f497418014916d9918827ab", "type": "zip", "shasum": "", "reference": "fae7d27d6667365b0f497418014916d9918827ab"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/release-0.17.3"}, "time": "2018-03-26T20:05:02+00:00"}, {"version": "0.17.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "ea1f655153cd618876634d86000b9b60c15bacd2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/ea1f655153cd618876634d86000b9b60c15bacd2", "type": "zip", "shasum": "", "reference": "ea1f655153cd618876634d86000b9b60c15bacd2"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/master"}, "time": "2018-03-26T01:39:03+00:00"}, {"version": "0.17.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "38b2fca6b8493abd9314fcf6b5b042cfba6a0f11"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/38b2fca6b8493abd9314fcf6b5b042cfba6a0f11", "type": "zip", "shasum": "", "reference": "38b2fca6b8493abd9314fcf6b5b042cfba6a0f11"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/release-0.17.1"}, "time": "2018-03-18T13:01:30+00:00"}, {"version": "0.17.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "3b4c2224524776a584de663c7a04bc8eb2e1544d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/3b4c2224524776a584de663c7a04bc8eb2e1544d", "type": "zip", "shasum": "", "reference": "3b4c2224524776a584de663c7a04bc8eb2e1544d"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/master"}, "time": "2017-12-30T22:08:48+00:00", "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "commonmark/commonmark.js": "0.28", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~5.7|~6.5", "symfony/finder": "~3.0|~4.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.16.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c0e41be0f80c51ad3170c9c713f74a0b8dec59ce"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c0e41be0f80c51ad3170c9c713f74a0b8dec59ce", "type": "zip", "shasum": "", "reference": "c0e41be0f80c51ad3170c9c713f74a0b8dec59ce"}, "time": "2017-10-31T00:49:55+00:00", "extra": {"branch-alias": {"dev-master": "0.17-dev"}}, "require": {"php": ">=5.4.8", "ext-mbstring": "*"}, "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "jgm/commonmark": "0.28", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "^4.8.35|~5.7", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.15.7", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "36d82f166e441dfa28643f8d01dd8bdd3a579adf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/36d82f166e441dfa28643f8d01dd8bdd3a579adf", "type": "zip", "shasum": "", "reference": "36d82f166e441dfa28643f8d01dd8bdd3a579adf"}, "time": "2017-10-26T15:41:07+00:00", "extra": {"branch-alias": {"dev-master": "0.16-dev"}}, "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "jgm/commonmark": "0.28", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.15.6", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "91742543c25fecedc84a4883d2919213e04a73b7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/91742543c25fecedc84a4883d2919213e04a73b7", "type": "zip", "shasum": "", "reference": "91742543c25fecedc84a4883d2919213e04a73b7"}, "time": "2017-08-08T11:47:33+00:00"}, {"version": "0.15.5", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "1162ff74f8ab66593d62c58142310101b415cd2a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/1162ff74f8ab66593d62c58142310101b415cd2a", "type": "zip", "shasum": "", "reference": "1162ff74f8ab66593d62c58142310101b415cd2a"}, "time": "2017-08-05T19:40:59+00:00"}, {"version": "0.15.4", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c4c8e6bf99e62d9568875d9fc3ef473fe3e18e0c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c4c8e6bf99e62d9568875d9fc3ef473fe3e18e0c", "type": "zip", "shasum": "", "reference": "c4c8e6bf99e62d9568875d9fc3ef473fe3e18e0c"}, "time": "2017-05-09T12:47:53+00:00", "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "jgm/commonmark": "0.27", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.15.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c8b43ee5821362216f8e9ac684f0f59de164edcc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c8b43ee5821362216f8e9ac684f0f59de164edcc", "type": "zip", "shasum": "", "reference": "c8b43ee5821362216f8e9ac684f0f59de164edcc"}, "time": "2016-12-19T00:11:43+00:00"}, {"version": "0.15.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c3b08b911e7344e45b87529eabc8b559d48093d4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c3b08b911e7344e45b87529eabc8b559d48093d4", "type": "zip", "shasum": "", "reference": "c3b08b911e7344e45b87529eabc8b559d48093d4"}, "time": "2016-11-22T17:30:29+00:00"}, {"version": "0.15.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "20bdba6777e6c63f861711501eec8887e65412fc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/20bdba6777e6c63f861711501eec8887e65412fc", "type": "zip", "shasum": "", "reference": "20bdba6777e6c63f861711501eec8887e65412fc"}, "time": "2016-11-08T15:28:32+00:00", "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "jgm/commonmark": "0.26", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.15.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "19fb96643beba24e681c371dc133e25409742664"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/19fb96643beba24e681c371dc133e25409742664", "type": "zip", "shasum": "", "reference": "19fb96643beba24e681c371dc133e25409742664"}, "time": "2016-09-14T15:44:35+00:00"}, {"version": "0.14.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "b73c0b7288bd0e6f9f56bd0b20d0657214b91838"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/b73c0b7288bd0e6f9f56bd0b20d0657214b91838", "type": "zip", "shasum": "", "reference": "b73c0b7288bd0e6f9f56bd0b20d0657214b91838"}, "time": "2016-07-02T18:48:39+00:00", "extra": {"branch-alias": {"dev-master": "0.15-dev"}}, "require-dev": {"cebe/markdown": "~1.0", "erusev/parsedown": "~1.0", "jgm/commonmark": "0.25", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.13.4", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "83f8210427fb01f671e272bb8d44b4ed3a94d459"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/83f8210427fb01f671e272bb8d44b4ed3a94d459", "type": "zip", "shasum": "", "reference": "83f8210427fb01f671e272bb8d44b4ed3a94d459"}, "time": "2016-06-14T14:49:29+00:00", "extra": {"branch-alias": {"dev-master": "0.14-dev"}}}, {"version": "0.13.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "35816f39eb2498484fbb7b1495633a976ee1a8de"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/35816f39eb2498484fbb7b1495633a976ee1a8de", "type": "zip", "shasum": "", "reference": "35816f39eb2498484fbb7b1495633a976ee1a8de"}, "time": "2016-05-21T18:41:30+00:00"}, {"version": "0.13.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "35ac362082ca983a8123df2ee2cdfcf456ab6295"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/35ac362082ca983a8123df2ee2cdfcf456ab6295", "type": "zip", "shasum": "", "reference": "35ac362082ca983a8123df2ee2cdfcf456ab6295"}, "time": "2016-03-27T19:10:13+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.25", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.13.1", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.colinodell.com", "role": "Lead Developer"}], "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "2c10de455649e3a544d45016d9df457248bcd37f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2c10de455649e3a544d45016d9df457248bcd37f", "type": "zip", "shasum": "", "reference": "2c10de455649e3a544d45016d9df457248bcd37f"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.13.1"}, "time": "2016-03-09T15:20:24+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.24", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.2.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3|~3.0", "scrutinizer/ocular": "~1.1"}}, {"version": "0.13.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "a4e93bc4fd1a8ff8f534040c4a07371ea5f4b484"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/a4e93bc4fd1a8ff8f534040c4a07371ea5f4b484", "type": "zip", "shasum": "", "reference": "a4e93bc4fd1a8ff8f534040c4a07371ea5f4b484"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/master"}, "time": "2016-01-14T04:29:54+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.24", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.1.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3", "scrutinizer/ocular": "^1.1"}}, {"version": "0.12.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "3eb64850ee688623db494398a5284a7a4cdf7b47"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/3eb64850ee688623db494398a5284a7a4cdf7b47", "type": "zip", "shasum": "", "reference": "3eb64850ee688623db494398a5284a7a4cdf7b47"}, "time": "2015-11-04T14:24:41+00:00", "extra": {"branch-alias": {"dev-master": "0.13-dev"}}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.22", "jgm/smartpunct": "0.22", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.1.0", "phpunit/phpunit": "~4.3|~5.0", "symfony/finder": "~2.3", "scrutinizer/ocular": "^1.1"}, "suggest": "__unset"}, {"version": "0.11.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d22d6a6a4b049faccc2f8e491cce6076eeb165c7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d22d6a6a4b049faccc2f8e491cce6076eeb165c7", "type": "zip", "shasum": "", "reference": "d22d6a6a4b049faccc2f8e491cce6076eeb165c7"}, "time": "2015-09-25T12:40:32+00:00", "extra": {"branch-alias": {"dev-master": "0.12-dev"}}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.22", "jgm/smartpunct": "0.22", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "~1.1.0", "phpunit/phpunit": "~4.3", "phpunit/phpunit-mock-objects": "2.3.0", "symfony/finder": "~2.3"}}, {"version": "0.11.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "467d06b442cc2da542cc0e34a81236142623a66b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/467d06b442cc2da542cc0e34a81236142623a66b", "type": "zip", "shasum": "", "reference": "467d06b442cc2da542cc0e34a81236142623a66b"}, "time": "2015-09-23T14:07:24+00:00"}, {"version": "0.11.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "38551450ca8a065f5caa5f5fc64a777b3630bef2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/38551450ca8a065f5caa5f5fc64a777b3630bef2", "type": "zip", "shasum": "", "reference": "38551450ca8a065f5caa5f5fc64a777b3630bef2"}, "time": "2015-09-22T12:05:57+00:00"}, {"version": "0.11.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "1af805de706b10705dea159a7ba927c4def2c0a1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/1af805de706b10705dea159a7ba927c4def2c0a1", "type": "zip", "shasum": "", "reference": "1af805de706b10705dea159a7ba927c4def2c0a1"}, "time": "2015-09-19T15:04:43+00:00", "bin": "__unset"}, {"version": "0.10.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "984455f72d4f07669c1f5c545768f60c4e871585"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/984455f72d4f07669c1f5c545768f60c4e871585", "type": "zip", "shasum": "", "reference": "984455f72d4f07669c1f5c545768f60c4e871585"}, "time": "2015-07-25T14:37:05+00:00", "extra": {"branch-alias": {"dev-master": "0.11-dev"}}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.21", "jgm/smartpunct": "0.21", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3", "phpunit/phpunit-mock-objects": "2.3.0", "symfony/finder": "~2.3"}}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "8b681a57233f00578195573d7d251c2f143c1a5d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/8b681a57233f00578195573d7d251c2f143c1a5d", "type": "zip", "shasum": "", "reference": "8b681a57233f00578195573d7d251c2f143c1a5d"}, "time": "2015-06-19T00:39:51+00:00", "extra": {"branch-alias": {"dev-master": "0.9-dev"}}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.20", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3", "phpunit/phpunit-mock-objects": "2.3.0", "symfony/finder": "~2.3"}}, {"version": "0.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "91696c88df298f75fdd2075e4bb19c6dbd7338ca"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/91696c88df298f75fdd2075e4bb19c6dbd7338ca", "type": "zip", "shasum": "", "reference": "91696c88df298f75fdd2075e4bb19c6dbd7338ca"}, "time": "2015-04-29T18:01:46+00:00", "extra": {"branch-alias": {"dev-master": "0.8-dev"}}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.19", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3", "phpunit/phpunit-mock-objects": "2.3.0", "symfony/finder": "~2.3"}}, {"version": "0.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "7fecb7bdef265e45c80c53e1000e2056a9463401"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/7fecb7bdef265e45c80c53e1000e2056a9463401", "type": "zip", "shasum": "", "reference": "7fecb7bdef265e45c80c53e1000e2056a9463401"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.7.2"}, "time": "2015-03-08T17:48:53+00:00", "require": {"php": ">=5.3.3", "ext-mbstring": "*"}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.18", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3"}}, {"version": "0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "7b428e962fb83a13715e057c65c88873d3165df0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/7b428e962fb83a13715e057c65c88873d3165df0", "type": "zip", "shasum": "", "reference": "7b428e962fb83a13715e057c65c88873d3165df0"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/master"}, "time": "2015-03-01T21:29:05+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.17", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3"}}, {"version": "0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "5f5137889b2aec36f8a1009ebe8673dac45f004e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/5f5137889b2aec36f8a1009ebe8673dac45f004e", "type": "zip", "shasum": "", "reference": "5f5137889b2aec36f8a1009ebe8673dac45f004e"}, "time": "2015-02-16T23:59:27+00:00", "extra": {"branch-alias": {"dev-master": "0.7-dev"}}}, {"version": "0.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "ad1b89403026158529620e0f6096be0eae96817c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/ad1b89403026158529620e0f6096be0eae96817c", "type": "zip", "shasum": "", "reference": "ad1b89403026158529620e0f6096be0eae96817c"}, "time": "2015-01-25T16:08:20+00:00", "extra": {"branch-alias": {"dev-master": "0.6-dev"}}}, {"version": "0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "c352207aab15706a1ca9c25e5bd30c847899a0c9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c352207aab15706a1ca9c25e5bd30c847899a0c9", "type": "zip", "shasum": "", "reference": "c352207aab15706a1ca9c25e5bd30c847899a0c9"}, "time": "2015-01-09T19:27:30+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.15", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3"}}, {"version": "0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "356dbd781b898163994c8e094b902d74dcdafcca"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/356dbd781b898163994c8e094b902d74dcdafcca", "type": "zip", "shasum": "", "reference": "356dbd781b898163994c8e094b902d74dcdafcca"}, "time": "2014-12-27T15:58:34+00:00", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.13", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3"}, "suggest": {"ext-mbstring": "Enables faster performance when normalizing link references"}}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "132babb2eb0ed51ecf2541eb3c585004cc6d4f1d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/132babb2eb0ed51ecf2541eb3c585004cc6d4f1d", "type": "zip", "shasum": "", "reference": "132babb2eb0ed51ecf2541eb3c585004cc6d4f1d"}, "time": "2014-12-24T22:50:50+00:00", "require": {"php": ">=5.3.3", "ext-mbstring": "*"}, "suggest": "__unset"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "f841f3fc1cd4b38a08dc51032c7d8db9fd0fd3e6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/f841f3fc1cd4b38a08dc51032c7d8db9fd0fd3e6", "type": "zip", "shasum": "", "reference": "f841f3fc1cd4b38a08dc51032c7d8db9fd0fd3e6"}, "time": "2014-12-15T18:53:02+00:00", "autoload": {"psr-4": {"League\\CommonMark\\": "src/", "League\\CommonMark\\Tests\\": "tests/"}}, "require": {"php": ">=5.3.3", "ext-mbstring": "*", "symfony/options-resolver": "~2.5.6"}, "extra": "__unset"}, {"homepage": "https://github.com/colinodell/commonmark-php", "version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "8256de6ff7b73a0633ef5bfa3f72e8964ffce1a5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/8256de6ff7b73a0633ef5bfa3f72e8964ffce1a5", "type": "zip", "shasum": "", "reference": "8256de6ff7b73a0633ef5bfa3f72e8964ffce1a5"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.3.0"}, "time": "2014-11-29T01:57:18+00:00", "autoload": {"psr-4": {"ColinODell\\CommonMark\\": "src/", "ColinODell\\CommonMark\\Tests\\": "tests/"}}, "require": {"php": ">=5.3.0", "symfony/options-resolver": "~2.5.6"}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.12", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3"}, "replace": "__unset"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "9f02985aab63a8d1bfa33376191706608ee7c9fd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/9f02985aab63a8d1bfa33376191706608ee7c9fd", "type": "zip", "shasum": "", "reference": "9f02985aab63a8d1bfa33376191706608ee7c9fd"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.2.1"}, "time": "2014-11-10T01:30:28+00:00", "require": {"php": ">=5.3.0"}, "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "0.10", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.3"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d143807ca5efa982229952bb7b0ae18d61a53aa6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d143807ca5efa982229952bb7b0ae18d61a53aa6", "type": "zip", "shasum": "", "reference": "d143807ca5efa982229952bb7b0ae18d61a53aa6"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.2.0"}, "time": "2014-11-10T00:28:43+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/commonmark": "dev-master", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.2.5"}}, {"version": "0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "d1b296681790a3ec8e9ccf0bf7b0014784309cd5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d1b296681790a3ec8e9ccf0bf7b0014784309cd5", "type": "zip", "shasum": "", "reference": "d1b296681790a3ec8e9ccf0bf7b0014784309cd5"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.1.2"}, "time": "2014-09-28T12:54:49+00:00", "require-dev": {"erusev/parsedown": "~1.0", "jgm/stmd": "dev-master#2cf0750", "michelf/php-markdown": "~1.4", "phpunit/phpunit": "~4.2.5"}}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "e04632ab466a88857d0071da013c1e8626bf2609"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/e04632ab466a88857d0071da013c1e8626bf2609", "type": "zip", "shasum": "", "reference": "e04632ab466a88857d0071da013c1e8626bf2609"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.1.1"}, "time": "2014-09-08T18:02:44+00:00", "require-dev": {"jgm/stmd": "dev-master#2cf0750", "phpunit/phpunit": "~3.7.0"}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/commonmark.git", "type": "git", "reference": "ccad94ba70f3eba5b803fe1652e11f8e1584788b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/commonmark/zipball/ccad94ba70f3eba5b803fe1652e11f8e1584788b", "type": "zip", "shasum": "", "reference": "ccad94ba70f3eba5b803fe1652e11f8e1584788b"}, "support": {"issues": "https://github.com/thephpleague/commonmark/issues", "source": "https://github.com/thephpleague/commonmark/tree/0.1.0"}, "time": "2014-09-08T13:43:41+00:00", "require-dev": {"jgm/stmd": "dev-master#d095c3d", "phpunit/phpunit": "~3.7.0"}}]}, "security-advisories": [{"advisoryId": "PKSA-rqc2-tcc6-nc79", "affectedVersions": "<2.7.0"}, {"advisoryId": "PKSA-fndg-qryc-dyc9", "affectedVersions": "<2.6.0"}, {"advisoryId": "PKSA-g6p7-7rfc-zbp2", "affectedVersions": ">=0.15.6,<0.18.1"}, {"advisoryId": "PKSA-nyyp-2pk1-frkz", "affectedVersions": "<0.18.3"}], "last-modified": "Mon, 05 May 2025 20:46:48 GMT"}