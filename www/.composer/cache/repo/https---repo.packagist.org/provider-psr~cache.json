{"minified": "composer/2.0", "packages": {"psr/cache": [{"name": "psr/cache", "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "homepage": "", "version": "3.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/cache.git", "type": "git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "type": "zip", "shasum": "", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "type": "library", "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "funding": [], "time": "2021-02-03T23:26:27+00:00", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=8.0.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/cache.git", "type": "git", "reference": "213f9dbc5b9bfbc4f8db86d2838dc968752ce13b"}, "dist": {"url": "https://api.github.com/repos/php-fig/cache/zipball/213f9dbc5b9bfbc4f8db86d2838dc968752ce13b", "type": "zip", "shasum": "", "reference": "213f9dbc5b9bfbc4f8db86d2838dc968752ce13b"}, "support": {"source": "https://github.com/php-fig/cache/tree/2.0.0"}, "time": "2021-02-03T23:23:37+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/cache.git", "type": "git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "type": "zip", "shasum": "", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00", "require": {"php": ">=5.3.0"}, "funding": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/cache.git", "type": "git", "reference": "9e66031f41fbbdda45ee11e93c45d480ccba3eb3"}, "dist": {"url": "https://api.github.com/repos/php-fig/cache/zipball/9e66031f41fbbdda45ee11e93c45d480ccba3eb3", "type": "zip", "shasum": "", "reference": "9e66031f41fbbdda45ee11e93c45d480ccba3eb3"}, "support": {"issues": "https://github.com/php-fig/cache/issues", "source": "https://github.com/php-fig/cache/tree/master"}, "time": "2015-12-11T02:52:07+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 24 Mar 2024 10:38:36 GMT"}