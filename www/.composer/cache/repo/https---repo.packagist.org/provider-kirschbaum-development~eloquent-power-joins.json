{"minified": "composer/2.0", "packages": {"kirschbaum-development/eloquent-power-joins": [{"name": "<PERSON><PERSON><PERSON><PERSON>-development/eloquent-power-joins", "description": "The Laravel magic applied to joins.", "keywords": ["mysql", "laravel", "join", "eloquent"], "homepage": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins", "version": "4.2.6", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "72cff1e838bb3f826dc09a5566219ad7fa56237f"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/72cff1e838bb3f826dc09a5566219ad7fa56237f", "type": "zip", "shasum": "", "reference": "72cff1e838bb3f826dc09a5566219ad7fa56237f"}, "type": "library", "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.6"}, "funding": [], "time": "2025-07-10T16:55:34+00:00", "autoload": {"psr-4": {"Kirschbaum\\PowerJoins\\": "src"}}, "extra": {"laravel": {"providers": ["Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider"]}}, "require": {"php": "^8.2", "illuminate/support": "^11.42|^12.0", "illuminate/database": "^11.42|^12.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "dev-master", "laravel/legacy-factories": "^1.0@dev", "orchestra/testbench": "^9.0|^10.0", "phpunit/phpunit": "^10.0|^11.0"}}, {"version": "4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "96e1a061ab97f36afb8ccfcd6c02b07f702d36e6"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/96e1a061ab97f36afb8ccfcd6c02b07f702d36e6", "type": "zip", "shasum": "", "reference": "96e1a061ab97f36afb8ccfcd6c02b07f702d36e6"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.5"}, "time": "2025-07-10T15:26:42+00:00"}, {"version": "4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "4a8012cef7abed8ac3633a180c69138a228b6c4c"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/4a8012cef7abed8ac3633a180c69138a228b6c4c", "type": "zip", "shasum": "", "reference": "4a8012cef7abed8ac3633a180c69138a228b6c4c"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.4"}, "time": "2025-05-19T14:14:41+00:00"}, {"version": "4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "d04e06b12e5e7864c303b8a8c6045bfcd4e2c641"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/d04e06b12e5e7864c303b8a8c6045bfcd4e2c641", "type": "zip", "shasum": "", "reference": "d04e06b12e5e7864c303b8a8c6045bfcd4e2c641"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.3"}, "time": "2025-04-01T14:41:56+00:00"}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "a307fab78c291526fba754e6ac8a86f7bd58d45d"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/a307fab78c291526fba754e6ac8a86f7bd58d45d", "type": "zip", "shasum": "", "reference": "a307fab78c291526fba754e6ac8a86f7bd58d45d"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.2"}, "time": "2025-03-08T01:26:00+00:00"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "84a24784d9abde8bafb1998a0841a6bc10fa6f8e"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/84a24784d9abde8bafb1998a0841a6bc10fa6f8e", "type": "zip", "shasum": "", "reference": "84a24784d9abde8bafb1998a0841a6bc10fa6f8e"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.1"}, "time": "2025-03-05T10:11:33+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "aafbe349e0ad4862273880652d0141bb753c4fc7"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/aafbe349e0ad4862273880652d0141bb753c4fc7", "type": "zip", "shasum": "", "reference": "aafbe349e0ad4862273880652d0141bb753c4fc7"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.2.0"}, "time": "2025-02-13T23:30:29+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "d6c5cb1b90c0bd033a8f9159a78fd6a23e9ac5c2"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/d6c5cb1b90c0bd033a8f9159a78fd6a23e9ac5c2", "type": "zip", "shasum": "", "reference": "d6c5cb1b90c0bd033a8f9159a78fd6a23e9ac5c2"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.1.0"}, "time": "2025-02-12T11:14:14+00:00", "require": {"php": "^8.1", "illuminate/support": "^10.0|^11.0|^12.0", "illuminate/database": "^10.0|^11.0|^12.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "dev-master", "laravel/legacy-factories": "^1.0@dev", "orchestra/testbench": "^8.0|^9.0", "phpunit/phpunit": "^10.0"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "3c1af9b86b02f1e39219849c1d2fee7cf77e8638"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/3c1af9b86b02f1e39219849c1d2fee7cf77e8638", "type": "zip", "shasum": "", "reference": "3c1af9b86b02f1e39219849c1d2fee7cf77e8638"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.0.1"}, "time": "2024-11-26T13:22:08+00:00", "require": {"php": "^8.1", "illuminate/support": "^10.0|^11.0", "illuminate/database": "^10.0|^11.0"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "c6c42a52c5a097cc11761e72782b2d0215692caf"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/c6c42a52c5a097cc11761e72782b2d0215692caf", "type": "zip", "shasum": "", "reference": "c6c42a52c5a097cc11761e72782b2d0215692caf"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/4.0.0"}, "time": "2024-10-06T12:28:14+00:00"}, {"version": "3.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "397ef08f15ceff48111fd7f57d9f1fd41bf1a453"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/397ef08f15ceff48111fd7f57d9f1fd41bf1a453", "type": "zip", "shasum": "", "reference": "397ef08f15ceff48111fd7f57d9f1fd41bf1a453"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.8"}, "time": "2024-09-10T10:28:05+00:00", "require": {"php": "^8.0", "illuminate/support": "^8.0|^9.0|^10.0|^11.0", "illuminate/database": "^8.0|^9.0|^10.0|^11.0"}, "require-dev": {"laravel/legacy-factories": "^1.0@dev", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0|^9.0", "phpunit/phpunit": "^8.0|^9.0|^10.0"}}, {"version": "3.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "3f57b398117d97bae4dfd5c37ea0f8f48f296c97"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/3f57b398117d97bae4dfd5c37ea0f8f48f296c97", "type": "zip", "shasum": "", "reference": "3f57b398117d97bae4dfd5c37ea0f8f48f296c97"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.7"}, "time": "2024-06-26T13:09:29+00:00"}, {"version": "3.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "6de51d9ec43af34e77bd1d9908173de1416a0aed"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/6de51d9ec43af34e77bd1d9908173de1416a0aed", "type": "zip", "shasum": "", "reference": "6de51d9ec43af34e77bd1d9908173de1416a0aed"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.6"}, "time": "2024-04-09T00:35:30+00:00"}, {"version": "3.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "ed5af4788f0dad3ce35bb883cecac4293496139a"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/ed5af4788f0dad3ce35bb883cecac4293496139a", "type": "zip", "shasum": "", "reference": "ed5af4788f0dad3ce35bb883cecac4293496139a"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.5"}, "time": "2024-03-27T11:14:14+00:00"}, {"version": "3.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "fc8bb379f062cd120ffab20a6913ad824c3366d8"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/fc8bb379f062cd120ffab20a6913ad824c3366d8", "type": "zip", "shasum": "", "reference": "fc8bb379f062cd120ffab20a6913ad824c3366d8"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.4"}, "time": "2024-03-26T19:12:33+00:00"}, {"version": "3.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "4b948b1682ef565aa367e3a651ae69fe37b0f400"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/4b948b1682ef565aa367e3a651ae69fe37b0f400", "type": "zip", "shasum": "", "reference": "4b948b1682ef565aa367e3a651ae69fe37b0f400"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.3"}, "time": "2024-03-26T10:38:51+00:00"}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "2fae3aca9eefd4591603a7e53406ab9f56b69fad"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/2fae3aca9eefd4591603a7e53406ab9f56b69fad", "type": "zip", "shasum": "", "reference": "2fae3aca9eefd4591603a7e53406ab9f56b69fad"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.2"}, "time": "2024-03-20T10:23:27+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "571f09c4b23c349cb1a5db8bb88af291ed30389c"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/571f09c4b23c349cb1a5db8bb88af291ed30389c", "type": "zip", "shasum": "", "reference": "571f09c4b23c349cb1a5db8bb88af291ed30389c"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.1"}, "time": "2024-03-19T22:45:47+00:00"}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "13feb3692ab6c0475b2c05de131d5f5822fb250a"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/13feb3692ab6c0475b2c05de131d5f5822fb250a", "type": "zip", "shasum": "", "reference": "13feb3692ab6c0475b2c05de131d5f5822fb250a"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.5.0"}, "time": "2024-02-13T15:40:14+00:00"}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "9238fcb53d777265ee9d8d139810e2cadecde079"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/9238fcb53d777265ee9d8d139810e2cadecde079", "type": "zip", "shasum": "", "reference": "9238fcb53d777265ee9d8d139810e2cadecde079"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.4.0"}, "time": "2023-12-07T10:44:41+00:00", "require": {"php": "^8.0", "illuminate/support": "^8.0|^9.0|^10.0", "illuminate/database": "^8.0|^9.0|^10.0"}, "require-dev": {"laravel/legacy-factories": "^1.0@dev", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "3.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "4718c937275e8a2428976cffd341a598ab420d2a"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/4718c937275e8a2428976cffd341a598ab420d2a", "type": "zip", "shasum": "", "reference": "4718c937275e8a2428976cffd341a598ab420d2a"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.3.4"}, "time": "2023-12-07T10:38:50+00:00"}, {"version": "3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "e55fa70dfa4a5e897443454825b9d4ce6081786a"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/e55fa70dfa4a5e897443454825b9d4ce6081786a", "type": "zip", "shasum": "", "reference": "e55fa70dfa4a5e897443454825b9d4ce6081786a"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.3.3"}, "time": "2023-10-31T18:01:18+00:00"}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "260f8d24306ec1731bd270104f0bc3655c4dbd32"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/260f8d24306ec1731bd270104f0bc3655c4dbd32", "type": "zip", "shasum": "", "reference": "260f8d24306ec1731bd270104f0bc3655c4dbd32"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.3.2"}, "time": "2023-10-29T00:53:00+00:00"}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "52a1282c0938fd5396a7bf970e6a0a8c56c8f560"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/52a1282c0938fd5396a7bf970e6a0a8c56c8f560", "type": "zip", "shasum": "", "reference": "52a1282c0938fd5396a7bf970e6a0a8c56c8f560"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.3.1"}, "time": "2023-10-28T13:48:46+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "9dd336b17cca5c82315365c5323895297fc35037"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/9dd336b17cca5c82315365c5323895297fc35037", "type": "zip", "shasum": "", "reference": "9dd336b17cca5c82315365c5323895297fc35037"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.3.0"}, "time": "2023-10-22T13:00:50+00:00"}, {"version": "3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "fadc20d436b0693a34c4b611d07954818e46eb4d"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/fadc20d436b0693a34c4b611d07954818e46eb4d", "type": "zip", "shasum": "", "reference": "fadc20d436b0693a34c4b611d07954818e46eb4d"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.2.4"}, "time": "2023-09-12T17:02:05+00:00"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "e9d936e0071d47796735b40ed6cce5dfd0a4305d"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/e9d936e0071d47796735b40ed6cce5dfd0a4305d", "type": "zip", "shasum": "", "reference": "e9d936e0071d47796735b40ed6cce5dfd0a4305d"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.2.3"}, "time": "2023-09-04T10:21:44+00:00"}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "23650f7707e25190511ab15b22a95e3a32ad277b"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/23650f7707e25190511ab15b22a95e3a32ad277b", "type": "zip", "shasum": "", "reference": "23650f7707e25190511ab15b22a95e3a32ad277b"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.2.2"}, "time": "2023-08-28T10:27:02+00:00"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "e92329b3d89c0e56e0ae32ba76e332f1848769a0"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/e92329b3d89c0e56e0ae32ba76e332f1848769a0", "type": "zip", "shasum": "", "reference": "e92329b3d89c0e56e0ae32ba76e332f1848769a0"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.2.1"}, "time": "2023-07-30T02:14:27+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "ef388b6b6ae1502fac172b8beb6f13ffe541569b"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/ef388b6b6ae1502fac172b8beb6f13ffe541569b", "type": "zip", "shasum": "", "reference": "ef388b6b6ae1502fac172b8beb6f13ffe541569b"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.2.0"}, "time": "2023-06-25T11:42:03+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "24af544ca28dd3300e9ee74e4b041da01818c4b1"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/24af544ca28dd3300e9ee74e4b041da01818c4b1", "type": "zip", "shasum": "", "reference": "24af544ca28dd3300e9ee74e4b041da01818c4b1"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.1.1"}, "time": "2023-06-20T13:50:15+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "e20600bcf0bcd978888baf0bfcf5edd4ebbe8842"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/e20600bcf0bcd978888baf0bfcf5edd4ebbe8842", "type": "zip", "shasum": "", "reference": "e20600bcf0bcd978888baf0bfcf5edd4ebbe8842"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.1.0"}, "time": "2023-06-15T10:43:02+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "85ff7684a25049aee6a1c9e26a751ce5be60d6d9"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/85ff7684a25049aee6a1c9e26a751ce5be60d6d9", "type": "zip", "shasum": "", "reference": "85ff7684a25049aee6a1c9e26a751ce5be60d6d9"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.0.3"}, "time": "2023-06-04T12:19:28+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "b9aa4c2364e2776e5aacca49160d4b519ae82768"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/b9aa4c2364e2776e5aacca49160d4b519ae82768", "type": "zip", "shasum": "", "reference": "b9aa4c2364e2776e5aacca49160d4b519ae82768"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.0.2"}, "time": "2023-03-16T14:41:20+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "6b312354b99695d864edad2fc4aeeb0cdaf30128"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/6b312354b99695d864edad2fc4aeeb0cdaf30128", "type": "zip", "shasum": "", "reference": "6b312354b99695d864edad2fc4aeeb0cdaf30128"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.0.1"}, "time": "2023-03-07T15:57:57+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "92ce06ef916c5426fa6c8d3c6692a17b1112e3ff"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/92ce06ef916c5426fa6c8d3c6692a17b1112e3ff", "type": "zip", "shasum": "", "reference": "92ce06ef916c5426fa6c8d3c6692a17b1112e3ff"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/3.0.0"}, "time": "2023-03-05T12:21:13+00:00"}, {"version": "2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "14eb8de38e30ce8f7323beb45800673467567f56"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/14eb8de38e30ce8f7323beb45800673467567f56", "type": "zip", "shasum": "", "reference": "14eb8de38e30ce8f7323beb45800673467567f56"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.7.3"}, "time": "2023-03-05T11:19:12+00:00", "require": {"php": "^7.1|^8.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/database": "^6.0|^7.0|^8.0|^9.0|^10.0"}}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "9db3de5c4be225d9fd19676c4219ec39c4d0e003"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/9db3de5c4be225d9fd19676c4219ec39c4d0e003", "type": "zip", "shasum": "", "reference": "9db3de5c4be225d9fd19676c4219ec39c4d0e003"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.7.2"}, "time": "2023-02-08T10:57:50+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "54c290c932e9f6bc41d4a8bc330de67456fb306f"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/54c290c932e9f6bc41d4a8bc330de67456fb306f", "type": "zip", "shasum": "", "reference": "54c290c932e9f6bc41d4a8bc330de67456fb306f"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.7.1"}, "time": "2023-02-05T11:14:16+00:00"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "be1b04e754db238e0e07a18ae595cd7594c68a1d"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/be1b04e754db238e0e07a18ae595cd7594c68a1d", "type": "zip", "shasum": "", "reference": "be1b04e754db238e0e07a18ae595cd7594c68a1d"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.7.0"}, "time": "2023-01-23T11:00:47+00:00"}, {"version": "2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "e69c88349f89639a8d83f66741a2b31cf6baba4d"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/e69c88349f89639a8d83f66741a2b31cf6baba4d", "type": "zip", "shasum": "", "reference": "e69c88349f89639a8d83f66741a2b31cf6baba4d"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.6.5"}, "time": "2022-12-09T10:31:12+00:00", "require": {"php": "^7.1|^8.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0", "illuminate/database": "^6.0|^7.0|^8.0|^9.0"}, "require-dev": {"laravel/legacy-factories": "^1.0@dev", "orchestra/testbench": "4.*|5.*|6.*|7.*", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "98e0ecc6844014a0169115ebea9985a89bbc61f2"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/98e0ecc6844014a0169115ebea9985a89bbc61f2", "type": "zip", "shasum": "", "reference": "98e0ecc6844014a0169115ebea9985a89bbc61f2"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.6.4"}, "time": "2022-04-06T22:58:03+00:00"}, {"version": "2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "704a3835ff4eb2cb8c66c8d69cbfd0a604a83a1f"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/704a3835ff4eb2cb8c66c8d69cbfd0a604a83a1f", "type": "zip", "shasum": "", "reference": "704a3835ff4eb2cb8c66c8d69cbfd0a604a83a1f"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.6.3"}, "time": "2022-03-05T19:14:37+00:00"}, {"version": "2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "11a252145f953ec106c56160b3771b03f0afdccb"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/11a252145f953ec106c56160b3771b03f0afdccb", "type": "zip", "shasum": "", "reference": "11a252145f953ec106c56160b3771b03f0afdccb"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.6.2"}, "time": "2022-03-05T18:39:07+00:00"}, {"version": "2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "d2718e1606d517e851cb7ab7646c6c5859c9d5e6"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/d2718e1606d517e851cb7ab7646c6c5859c9d5e6", "type": "zip", "shasum": "", "reference": "d2718e1606d517e851cb7ab7646c6c5859c9d5e6"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.6.1"}, "time": "2022-02-15T01:13:13+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "d6c7db85c750a4ce864e8d13d5fe74a295b0f710"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/d6c7db85c750a4ce864e8d13d5fe74a295b0f710", "type": "zip", "shasum": "", "reference": "d6c7db85c750a4ce864e8d13d5fe74a295b0f710"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.6.0"}, "time": "2022-02-09T13:36:48+00:00"}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "dd03ea7a07795150853a4d72f302caaf24eaf672"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/dd03ea7a07795150853a4d72f302caaf24eaf672", "type": "zip", "shasum": "", "reference": "dd03ea7a07795150853a4d72f302caaf24eaf672"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.5.2"}, "time": "2022-02-06T13:12:41+00:00", "require": {"php": "^7.1|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "illuminate/database": "^6.0|^7.0|^8.0"}, "require-dev": {"laravel/legacy-factories": "^1.0@dev", "orchestra/testbench": "4.*|5.*|6.*", "phpunit/phpunit": "^8.0"}}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "91544558a8e3a824bf45c24c12a1c1efb036c933"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/91544558a8e3a824bf45c24c12a1c1efb036c933", "type": "zip", "shasum": "", "reference": "91544558a8e3a824bf45c24c12a1c1efb036c933"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.5.1"}, "time": "2021-11-18T17:09:00+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "042a17abd5348303ac53ca72f689909b963b3e03"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/042a17abd5348303ac53ca72f689909b963b3e03", "type": "zip", "shasum": "", "reference": "042a17abd5348303ac53ca72f689909b963b3e03"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.5.0"}, "time": "2021-10-24T23:00:53+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "4c5c1c41f70c8bf20acafa69dc725171d8e003bd"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/4c5c1c41f70c8bf20acafa69dc725171d8e003bd", "type": "zip", "shasum": "", "reference": "4c5c1c41f70c8bf20acafa69dc725171d8e003bd"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.4.1"}, "time": "2021-08-30T10:17:57+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "4837a42be7c335141bbeb1f9aec5d96a6986ea13"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/4837a42be7c335141bbeb1f9aec5d96a6986ea13", "type": "zip", "shasum": "", "reference": "4837a42be7c335141bbeb1f9aec5d96a6986ea13"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.4.0"}, "time": "2021-04-18T22:40:29+00:00"}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "fcc545c8e68d885229dd2f71f2bb760994e9d1cf"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/fcc545c8e68d885229dd2f71f2bb760994e9d1cf", "type": "zip", "shasum": "", "reference": "fcc545c8e68d885229dd2f71f2bb760994e9d1cf"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.3.3"}, "time": "2021-04-07T12:21:19+00:00"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "d98172319faf0a4cb672c205d277d0a4c4c37333"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/d98172319faf0a4cb672c205d277d0a4c4c37333", "type": "zip", "shasum": "", "reference": "d98172319faf0a4cb672c205d277d0a4c4c37333"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.3.2"}, "time": "2020-12-29T19:20:35+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "9f622c5b26ce4bc50c07c2140551442c8e360b26"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/9f622c5b26ce4bc50c07c2140551442c8e360b26", "type": "zip", "shasum": "", "reference": "9f622c5b26ce4bc50c07c2140551442c8e360b26"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.3.1"}, "time": "2020-12-22T16:02:33+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "f3d4da21a8e03238876eedaf30a5214dbdad8475"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/f3d4da21a8e03238876eedaf30a5214dbdad8475", "type": "zip", "shasum": "", "reference": "f3d4da21a8e03238876eedaf30a5214dbdad8475"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.3.0"}, "time": "2020-11-28T15:03:49+00:00", "require": {"php": "^7.1|^8.1", "illuminate/support": "^6.0|^7.0|^8.0", "illuminate/database": "^6.0|^7.0|^8.0"}}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "1d8a117c2e438cb50a2f0d43d8ae1bea0676db93"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/1d8a117c2e438cb50a2f0d43d8ae1bea0676db93", "type": "zip", "shasum": "", "reference": "1d8a117c2e438cb50a2f0d43d8ae1bea0676db93"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.2.3"}, "time": "2020-10-30T18:45:49+00:00", "require": {"php": "^7.1", "illuminate/support": "^6.0|^7.0|^8.0", "illuminate/database": "^6.0|^7.0|^8.0"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "9b5cb738776a5ec0b109d078238e98e3a8ca2fd6"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/9b5cb738776a5ec0b109d078238e98e3a8ca2fd6", "type": "zip", "shasum": "", "reference": "9b5cb738776a5ec0b109d078238e98e3a8ca2fd6"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.2.2"}, "time": "2020-10-24T01:04:43+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "067f6db403f9ec613e2d02988e55f1b65b445734"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/067f6db403f9ec613e2d02988e55f1b65b445734", "type": "zip", "shasum": "", "reference": "067f6db403f9ec613e2d02988e55f1b65b445734"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.2.1"}, "time": "2020-10-14T19:26:12+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "657f22370f4ca9fdbd2a1269aac4d7eb9599cc16"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/657f22370f4ca9fdbd2a1269aac4d7eb9599cc16", "type": "zip", "shasum": "", "reference": "657f22370f4ca9fdbd2a1269aac4d7eb9599cc16"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.2.0"}, "time": "2020-10-09T00:37:50+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "af4a50de55911ed580837ef06351298cb537f481"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/af4a50de55911ed580837ef06351298cb537f481", "type": "zip", "shasum": "", "reference": "af4a50de55911ed580837ef06351298cb537f481"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/2.1.0"}, "time": "2020-09-10T23:49:32+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "8e593a2f1b45cd9e6fe4872ddf271b3247fb453f"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/8e593a2f1b45cd9e6fe4872ddf271b3247fb453f", "type": "zip", "shasum": "", "reference": "8e593a2f1b45cd9e6fe4872ddf271b3247fb453f"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/master"}, "time": "2020-09-08T12:00:27+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "44d24141cc9c225503dec3dfa5aeac6e6caad3bd"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/44d24141cc9c225503dec3dfa5aeac6e6caad3bd", "type": "zip", "shasum": "", "reference": "44d24141cc9c225503dec3dfa5aeac6e6caad3bd"}, "time": "2020-09-02T16:30:55+00:00", "require": {"php": "^7.1", "illuminate/support": "^6.0|^7.0|^8.0-dev", "illuminate/database": "^6.0|^7.0|^8.0-dev"}, "require-dev": {"laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "4.*|5.*|6.*", "phpunit/phpunit": "^8.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "39e448b14682c9184e74c30648a4ef15ed554588"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/39e448b14682c9184e74c30648a4ef15ed554588", "type": "zip", "shasum": "", "reference": "39e448b14682c9184e74c30648a4ef15ed554588"}, "time": "2020-08-30T14:37:21+00:00", "extra": {"laravel": {"providers": ["KirschbaumDevelopment\\EloquentJoins\\PowerJoinsServiceProvider"]}}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "7d8346a650aea050cf174cac8aee7e7ccffb6fa1"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/7d8346a650aea050cf174cac8aee7e7ccffb6fa1", "type": "zip", "shasum": "", "reference": "7d8346a650aea050cf174cac8aee7e7ccffb6fa1"}, "time": "2020-05-14T22:13:03+00:00", "autoload": {"psr-4": {"KirschbaumDevelopment\\EloquentJoins\\": "src"}}, "extra": {"laravel": {"providers": ["KirschbaumDevelopment\\EloquentJoins\\EloquentJoinsServiceProvider"]}}, "require": {"php": "^7.1", "illuminate/support": "^6.0|^7.0", "illuminate/database": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "4.*|5.*", "phpunit/phpunit": "^8.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "e6c7011be14d83dfad1bace18226a8d9b1cc5542"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/e6c7011be14d83dfad1bace18226a8d9b1cc5542", "type": "zip", "shasum": "", "reference": "e6c7011be14d83dfad1bace18226a8d9b1cc5542"}, "time": "2020-04-13T13:24:09+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "872f305a03be124c2e03820a2ce5d8f0f1e391d0"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/872f305a03be124c2e03820a2ce5d8f0f1e391d0", "type": "zip", "shasum": "", "reference": "872f305a03be124c2e03820a2ce5d8f0f1e391d0"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/1.0.0"}, "time": "2020-03-30T10:20:26+00:00"}, {"version": "0.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "5e200fa29eae2c2600e056ae58744049f334f3ba"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/5e200fa29eae2c2600e056ae58744049f334f3ba", "type": "zip", "shasum": "", "reference": "5e200fa29eae2c2600e056ae58744049f334f3ba"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/master"}, "time": "2020-03-28T17:55:03+00:00"}, {"version": "0.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "b535fa94e0c30e7100143a7bab3d344e4ccb84ec"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/b535fa94e0c30e7100143a7bab3d344e4ccb84ec", "type": "zip", "shasum": "", "reference": "b535fa94e0c30e7100143a7bab3d344e4ccb84ec"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/0.0.3"}, "time": "2020-03-28T02:14:23+00:00"}, {"version": "0.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "39e0744983ba31c1621ea6ada1722120abb12174"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/39e0744983ba31c1621ea6ada1722120abb12174", "type": "zip", "shasum": "", "reference": "39e0744983ba31c1621ea6ada1722120abb12174"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/0.0.2"}, "time": "2020-03-26T15:58:40+00:00"}, {"version": "0.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins.git", "type": "git", "reference": "4a41d092c30e54f49ddfa98f3ba4fc417fec10af"}, "dist": {"url": "https://api.github.com/repos/kirs<PERSON><PERSON>-development/eloquent-power-joins/zipball/4a41d092c30e54f49ddfa98f3ba4fc417fec10af", "type": "zip", "shasum": "", "reference": "4a41d092c30e54f49ddfa98f3ba4fc417fec10af"}, "support": {"issues": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/issues", "source": "https://github.com/k<PERSON><PERSON><PERSON>-development/eloquent-power-joins/tree/master"}, "time": "2020-03-25T01:16:07+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 10 Jul 2025 16:55:56 GMT"}