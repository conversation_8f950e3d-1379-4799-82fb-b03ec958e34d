{"minified": "composer/2.0", "packages": {"guzzlehttp/guzzle": [{"name": "guzzlehttp/guzzle", "description": "Guzzle is a PHP HTTP client library", "keywords": ["framework", "curl", "http", "rest", "http client", "client", "web service", "psr-7", "psr-18"], "homepage": "", "version": "7.9.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "type": "zip", "shasum": "", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "type": "library", "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.8.2", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "provide": {"psr/http-client-implementation": "1.0"}}, {"version": "7.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "type": "zip", "shasum": "", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "time": "2024-07-24T11:22:20+00:00"}, {"version": "7.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "a629e5b69db96eb4939c1b34114130077dd4c6fc"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/a629e5b69db96eb4939c1b34114130077dd4c6fc", "type": "zip", "shasum": "", "reference": "a629e5b69db96eb4939c1b34114130077dd4c6fc"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.1"}, "time": "2024-07-19T16:19:57+00:00"}, {"version": "7.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "84ac2b2afc44e40d3e8e658a45d68d6d20437612"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/84ac2b2afc44e40d3e8e658a45d68d6d20437612", "type": "zip", "shasum": "", "reference": "84ac2b2afc44e40d3e8e658a45d68d6d20437612"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.0"}, "time": "2024-07-18T11:52:56+00:00"}, {"version": "7.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f4152d9eb85c445fe1f992001d1748e8bec070d2"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4152d9eb85c445fe1f992001d1748e8bec070d2", "type": "zip", "shasum": "", "reference": "f4152d9eb85c445fe1f992001d1748e8bec070d2"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.2"}, "time": "2024-07-18T11:12:18+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^1.9.1 || ^2.6.3", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}}, {"version": "7.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "type": "zip", "shasum": "", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "time": "2023-12-03T20:35:24+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.8.2", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}}, {"version": "7.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "1110f66a6530a40fe7aea0378fe608ee2b2248f9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/1110f66a6530a40fe7aea0378fe608ee2b2248f9", "type": "zip", "shasum": "", "reference": "1110f66a6530a40fe7aea0378fe608ee2b2248f9"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.0"}, "time": "2023-08-27T10:20:53+00:00", "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.8.1", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}}, {"version": "7.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "085b026db54d4b5012f727c80c9958e8b8cbc454"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/085b026db54d4b5012f727c80c9958e8b8cbc454", "type": "zip", "shasum": "", "reference": "085b026db54d4b5012f727c80c9958e8b8cbc454"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.7.1"}, "time": "2023-08-27T10:02:06+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}}, {"version": "7.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "fb7566caccf22d74d1ab270de3551f72a58399f5"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/fb7566caccf22d74d1ab270de3551f72a58399f5", "type": "zip", "shasum": "", "reference": "fb7566caccf22d74d1ab270de3551f72a58399f5"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.7.0"}, "time": "2023-05-21T14:04:53+00:00"}, {"version": "7.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "8444a2bacf1960bc6a2b62ed86b8e72e11eebe51"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/8444a2bacf1960bc6a2b62ed86b8e72e11eebe51", "type": "zip", "shasum": "", "reference": "8444a2bacf1960bc6a2b62ed86b8e72e11eebe51"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.6.1"}, "time": "2023-05-15T20:43:01+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.8.1", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}}, {"version": "7.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "733dd89533dd371a0987172727df15f500dab0ef"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/733dd89533dd371a0987172727df15f500dab0ef", "type": "zip", "shasum": "", "reference": "733dd89533dd371a0987172727df15f500dab0ef"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.6.0"}, "time": "2023-05-14T11:23:39+00:00"}, {"version": "7.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "584d1f06b5caa07b0587f5054d551ed65460ce5d"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/584d1f06b5caa07b0587f5054d551ed65460ce5d", "type": "zip", "shasum": "", "reference": "584d1f06b5caa07b0587f5054d551ed65460ce5d"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.3"}, "time": "2023-05-15T20:42:18+00:00"}, {"version": "7.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "4019c94f08689e8b67b44800407d52133291dd4c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/4019c94f08689e8b67b44800407d52133291dd4c", "type": "zip", "shasum": "", "reference": "4019c94f08689e8b67b44800407d52133291dd4c"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.2"}, "time": "2023-05-14T09:20:02+00:00"}, {"version": "7.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b964ca597e86b752cd994f27293e9fa6b6a95ed9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b964ca597e86b752cd994f27293e9fa6b6a95ed9", "type": "zip", "shasum": "", "reference": "b964ca597e86b752cd994f27293e9fa6b6a95ed9"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.1"}, "time": "2023-04-17T16:30:08+00:00", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "7.5-dev"}}}, {"version": "7.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b50a2a1251152e43f6a37f0fa053e730a67d25ba"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b50a2a1251152e43f6a37f0fa053e730a67d25ba", "type": "zip", "shasum": "", "reference": "b50a2a1251152e43f6a37f0fa053e730a67d25ba"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.0"}, "time": "2022-08-28T15:39:27+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}}, {"version": "7.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "type": "zip", "shasum": "", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.5"}, "time": "2022-06-20T22:16:13+00:00", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}}, {"version": "7.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "e3ff079b22820c2029d4c2a87796b6a0b8716ad8"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/e3ff079b22820c2029d4c2a87796b6a0b8716ad8", "type": "zip", "shasum": "", "reference": "e3ff079b22820c2029d4c2a87796b6a0b8716ad8"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.4"}, "time": "2022-06-09T21:39:15+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}}, {"version": "7.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "74a8602c6faec9ef74b7a9391ac82c5e65b1cdab"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/74a8602c6faec9ef74b7a9391ac82c5e65b1cdab", "type": "zip", "shasum": "", "reference": "74a8602c6faec9ef74b7a9391ac82c5e65b1cdab"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.3"}, "time": "2022-05-25T13:24:33+00:00"}, {"version": "7.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "ac1ec1cd9b5624694c3a40be801d94137afb12b4"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/ac1ec1cd9b5624694c3a40be801d94137afb12b4", "type": "zip", "shasum": "", "reference": "ac1ec1cd9b5624694c3a40be801d94137afb12b4"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.2"}, "time": "2022-03-20T14:16:28+00:00"}, {"version": "7.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "ee0a041b1760e6a53d2a39c8c34115adc2af2c79"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/ee0a041b1760e6a53d2a39c8c34115adc2af2c79", "type": "zip", "shasum": "", "reference": "ee0a041b1760e6a53d2a39c8c34115adc2af2c79"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.1"}, "time": "2021-12-06T18:43:05+00:00"}, {"version": "7.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "868b3571a039f0ebc11ac8f344f4080babe2cb94"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/868b3571a039f0ebc11ac8f344f4080babe2cb94", "type": "zip", "shasum": "", "reference": "868b3571a039f0ebc11ac8f344f4080babe2cb94"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.0"}, "time": "2021-10-18T09:52:00+00:00", "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2"}}, {"homepage": "http://guzzlephp.org/", "version": "7.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "7008573787b430c1c1f650e3722d9bba59967628"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/7008573787b430c1c1f650e3722d9bba59967628", "type": "zip", "shasum": "", "reference": "7008573787b430c1c1f650e3722d9bba59967628"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.3.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://github.com/ale<PERSON><PERSON><PERSON><PERSON>ov", "type": "github"}, {"url": "https://github.com/gmponos", "type": "github"}], "time": "2021-03-23T11:33:13+00:00", "extra": {"branch-alias": {"dev-master": "7.3-dev"}}, "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.4", "guzzlehttp/psr7": "^1.7 || ^2.0", "psr/http-client": "^1.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1"}}, {"version": "7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0aa74dfb41ae110835923ef10a9d803a22d50e79"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0aa74dfb41ae110835923ef10a9d803a22d50e79", "type": "zip", "shasum": "", "reference": "0aa74dfb41ae110835923ef10a9d803a22d50e79"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.2.0"}, "time": "2020-10-10T11:47:56+00:00", "extra": {"branch-alias": {"dev-master": "7.1-dev"}}, "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.4", "guzzlehttp/psr7": "^1.7", "psr/http-client": "^1.0"}, "require-dev": {"ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1"}}, {"version": "7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "7427d6f99df41cc01f33cd59832f721c150ffdf3"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/7427d6f99df41cc01f33cd59832f721c150ffdf3", "type": "zip", "shasum": "", "reference": "7427d6f99df41cc01f33cd59832f721c150ffdf3"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.1.1"}, "time": "2020-09-30T08:51:17+00:00", "require": {"php": "^7.2.5", "ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "psr/http-client": "^1.0"}, "require-dev": {"ext-curl": "*", "php-http/client-integration-tests": "dev-phpunit8", "phpunit/phpunit": "^8.5.5", "psr/log": "^1.1"}}, {"version": "7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "7edeaa528fbb57123028bd5a76b9ce9540194e26"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/7edeaa528fbb57123028bd5a76b9ce9540194e26", "type": "zip", "shasum": "", "reference": "7edeaa528fbb57123028bd5a76b9ce9540194e26"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.1.0"}, "time": "2020-09-22T09:10:04+00:00"}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "2d9d3c186a6637a43193e66b097c50e4451eaab2"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/2d9d3c186a6637a43193e66b097c50e4451eaab2", "type": "zip", "shasum": "", "reference": "2d9d3c186a6637a43193e66b097c50e4451eaab2"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.0"}, "funding": [], "time": "2020-06-27T10:33:25+00:00", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "require-dev": {"ext-curl": "*", "ergebnis/composer-normalize": "^2.0", "php-http/client-integration-tests": "dev-phpunit8", "phpunit/phpunit": "^8.5.5", "psr/log": "^1.1"}}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "414c24961042f6616fb43e23fa69a785f9fc053e"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/414c24961042f6616fb43e23fa69a785f9fc053e", "type": "zip", "shasum": "", "reference": "414c24961042f6616fb43e23fa69a785f9fc053e"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2020-06-27T08:47:54+00:00"}, {"version": "7.0.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "818faffe2dc3e3880130948a9ce02aeb8f893fec"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/818faffe2dc3e3880130948a9ce02aeb8f893fec", "type": "zip", "shasum": "", "reference": "818faffe2dc3e3880130948a9ce02aeb8f893fec"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.0.0-rc.1"}, "time": "2020-06-15T17:25:07+00:00"}, {"keywords": ["framework", "curl", "http", "rest", "http client", "client", "web service"], "version": "7.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5aee9140d3ba07f916c0058de0f7bc9b156f8714"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5aee9140d3ba07f916c0058de0f7bc9b156f8714", "type": "zip", "shasum": "", "reference": "5aee9140d3ba07f916c0058de0f7bc9b156f8714"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2020-05-25T19:38:48+00:00", "require": {"php": "^7.2.5", "ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "psr/http-client": "^1.0", "symfony/polyfill-intl-idn": "1.17.0"}, "require-dev": {"ext-curl": "*", "ergebnis/composer-normalize": "^2.0", "php-http/client-integration-tests": "dev-phpunit8", "phpunit/phpunit": "^8.5", "psr/log": "^1.1"}, "suggest": {"ext-curl": "Required for CURL handler support", "psr/log": "Required for using the Log middleware"}}, {"version": "7.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "7a30f3bc91b3ab57860efbe8272649cc23dbbcc2"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/7a30f3bc91b3ab57860efbe8272649cc23dbbcc2", "type": "zip", "shasum": "", "reference": "7a30f3bc91b3ab57860efbe8272649cc23dbbcc2"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.0.0-beta.1"}, "time": "2019-12-30T14:24:25+00:00", "require": {"php": "^7.2.5", "ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "psr/http-client": "^1.0"}, "suggest": {"ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "funding": "__unset", "provide": "__unset"}, {"version": "6.5.8", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "type": "zip", "shasum": "", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "require": {"php": ">=5.5", "ext-json": "*", "symfony/polyfill-intl-idn": "^1.17", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}}, {"version": "6.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "724562fa861e21a4071c652c8a159934e4f05592"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/724562fa861e21a4071c652c8a159934e4f05592", "type": "zip", "shasum": "", "reference": "724562fa861e21a4071c652c8a159934e4f05592"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.7"}, "time": "2022-06-09T21:36:50+00:00", "require": {"php": ">=5.5", "ext-json": "*", "symfony/polyfill-intl-idn": "^1.17.0", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1"}}, {"version": "6.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f092dd734083473658de3ee4bef093ed77d2689c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f092dd734083473658de3ee4bef093ed77d2689c", "type": "zip", "shasum": "", "reference": "f092dd734083473658de3ee4bef093ed77d2689c"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.6"}, "time": "2022-05-25T13:19:12+00:00"}, {"version": "6.5.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "type": "zip", "shasum": "", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5"}, "funding": [], "time": "2020-06-16T21:01:06+00:00"}, {"version": "6.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/a4a1b6930528a8f7ee03518e6442ec7a44155d9d", "type": "zip", "shasum": "", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d"}, "time": "2020-05-25T19:35:05+00:00", "require": {"php": ">=5.5", "ext-json": "*", "symfony/polyfill-intl-idn": "1.17.0", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1"}}, {"version": "6.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "aab4ebd862aa7d04f01a4b51849d657db56d882e"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/aab4ebd862aa7d04f01a4b51849d657db56d882e", "type": "zip", "shasum": "", "reference": "aab4ebd862aa7d04f01a4b51849d657db56d882e"}, "time": "2020-04-18T10:38:46+00:00", "require": {"php": ">=5.5", "ext-json": "*", "symfony/polyfill-intl-idn": "^1.11", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1"}}, {"version": "6.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "43ece0e75098b7ecd8d13918293029e555a50f82"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/43ece0e75098b7ecd8d13918293029e555a50f82", "type": "zip", "shasum": "", "reference": "43ece0e75098b7ecd8d13918293029e555a50f82"}, "time": "2019-12-23T11:57:10+00:00", "require": {"php": ">=5.5", "ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1"}, "suggest": {"psr/log": "Required for using the Log middleware", "ext-intl": "Required for Internationalized Domain Name (IDN) support"}, "funding": "__unset"}, {"version": "6.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0274c05370a7bc9bb3a33838858253418bd7d14b"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0274c05370a7bc9bb3a33838858253418bd7d14b", "type": "zip", "shasum": "", "reference": "0274c05370a7bc9bb3a33838858253418bd7d14b"}, "time": "2019-12-21T08:51:15+00:00"}, {"version": "6.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5", "type": "zip", "shasum": "", "reference": "dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2019-12-07T18:20:45+00:00"}, {"version": "6.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0895c932405407fd3a7368b6910c09a24d26db11"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0895c932405407fd3a7368b6910c09a24d26db11", "type": "zip", "shasum": "", "reference": "0895c932405407fd3a7368b6910c09a24d26db11"}, "time": "2019-10-23T15:58:00+00:00", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "suggest": {"psr/log": "Required for using the Log middleware"}}, {"version": "6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "4c1647b1e3788187d80f325b3c49612bd06ace37"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/4c1647b1e3788187d80f325b3c49612bd06ace37", "type": "zip", "shasum": "", "reference": "4c1647b1e3788187d80f325b3c49612bd06ace37"}, "time": "2019-10-23T15:29:22+00:00"}, {"version": "6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "type": "zip", "shasum": "", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "time": "2018-04-22T15:46:56+00:00", "require": {"php": ">=5.5", "guzzlehttp/psr7": "^1.4", "guzzlehttp/promises": "^1.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}}, {"version": "6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "68d0ea14d5a3f42a20e87632a5f84931e2709c90"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/68d0ea14d5a3f42a20e87632a5f84931e2709c90", "type": "zip", "shasum": "", "reference": "68d0ea14d5a3f42a20e87632a5f84931e2709c90"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.3.2"}, "time": "2018-03-26T16:33:04+00:00", "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4", "psr/log": "^1.0"}}, {"version": "6.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "49e42e6539f775e80c74086a710a882e1d5a8111"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/49e42e6539f775e80c74086a710a882e1d5a8111", "type": "zip", "shasum": "", "reference": "49e42e6539f775e80c74086a710a882e1d5a8111"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2018-03-26T12:06:26+00:00"}, {"version": "6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4db5a78a5ea468d4831de7f0bf9d9415e348699", "type": "zip", "shasum": "", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699"}, "time": "2017-06-22T18:50:49+00:00", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0 || ^5.0", "psr/log": "^1.0"}}, {"version": "6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "8d6c6cc55186db87b7dc5009827429ba4e9dc006"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/8d6c6cc55186db87b7dc5009827429ba4e9dc006", "type": "zip", "shasum": "", "reference": "8d6c6cc55186db87b7dc5009827429ba4e9dc006"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.2.3"}, "time": "2017-02-28T22:50:30+00:00", "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0", "psr/log": "^1.0"}, "suggest": "__unset"}, {"version": "6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "ebf29dee597f02f09f4d5bbecc68230ea9b08f60"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/ebf29dee597f02f09f4d5bbecc68230ea9b08f60", "type": "zip", "shasum": "", "reference": "ebf29dee597f02f09f4d5bbecc68230ea9b08f60"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2016-10-08T15:01:37+00:00", "require": {"php": ">=5.5", "guzzlehttp/psr7": "^1.3.1", "guzzlehttp/promises": "^1.0"}}, {"version": "6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "3f808fba627f2c5b69e2501217bf31af349c1427"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/3f808fba627f2c5b69e2501217bf31af349c1427", "type": "zip", "shasum": "", "reference": "3f808fba627f2c5b69e2501217bf31af349c1427"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/release-6.2.1"}, "time": "2016-07-15T17:22:37+00:00"}, {"version": "6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d094e337976dff9d8e2424e8485872194e768662"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d094e337976dff9d8e2424e8485872194e768662", "type": "zip", "shasum": "", "reference": "d094e337976dff9d8e2424e8485872194e768662"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2016-03-21T20:02:09+00:00", "require": {"php": ">=5.5.0", "guzzlehttp/psr7": "~1.1", "guzzlehttp/promises": "~1.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0", "psr/log": "~1.0"}}, {"version": "6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "c6851d6e48f63b69357cbfa55bca116448140e0c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/c6851d6e48f63b69357cbfa55bca116448140e0c", "type": "zip", "shasum": "", "reference": "c6851d6e48f63b69357cbfa55bca116448140e0c"}, "time": "2015-11-23T00:47:50+00:00", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "66fd14b4d0b8f2389eaf37c5458608c7cb793a81"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/66fd14b4d0b8f2389eaf37c5458608c7cb793a81", "type": "zip", "shasum": "", "reference": "66fd14b4d0b8f2389eaf37c5458608c7cb793a81"}, "time": "2015-09-08T17:36:26+00:00"}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "a8dfeff00eb84616a17fea7a4d72af35e750410f"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/a8dfeff00eb84616a17fea7a4d72af35e750410f", "type": "zip", "shasum": "", "reference": "a8dfeff00eb84616a17fea7a4d72af35e750410f"}, "time": "2015-07-04T20:09:24+00:00", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f992b7b487a816c957d317442bed4966409873e0"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f992b7b487a816c957d317442bed4966409873e0", "type": "zip", "shasum": "", "reference": "f992b7b487a816c957d317442bed4966409873e0"}, "time": "2015-05-27T16:57:51+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "require": {"php": ">=5.5.0", "guzzlehttp/psr7": "^1.0.0", "guzzlehttp/promises": "^1.0.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0", "psr/log": "^1.0"}}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "df897ae757ad329d2affc38ffb5bbce782b2b510"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/df897ae757ad329d2affc38ffb5bbce782b2b510", "type": "zip", "shasum": "", "reference": "df897ae757ad329d2affc38ffb5bbce782b2b510"}, "time": "2015-05-26T18:22:06+00:00"}, {"description": "Guzzle is a PHP HTTP client library and framework for building RESTful web service clients", "version": "5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b87eda7a7162f95574032da17e9323c9899cb6b2"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b87eda7a7162f95574032da17e9323c9899cb6b2", "type": "zip", "shasum": "", "reference": "b87eda7a7162f95574032da17e9323c9899cb6b2"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/5.3"}, "time": "2019-10-30T09:32:00+00:00", "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}}, "require": {"php": ">=5.4.0", "guzzlehttp/ringphp": "^1.1", "react/promise": "^2.2"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0"}, "extra": "__unset"}, {"version": "5.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "93bbdb30d59be6cd9839495306c65f2907370eb9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/93bbdb30d59be6cd9839495306c65f2907370eb9", "type": "zip", "shasum": "", "reference": "93bbdb30d59be6cd9839495306c65f2907370eb9"}, "time": "2018-07-31T13:33:10+00:00"}, {"version": "5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f9acb4761844317e626a32259205bec1f1bc60d2"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f9acb4761844317e626a32259205bec1f1bc60d2", "type": "zip", "shasum": "", "reference": "f9acb4761844317e626a32259205bec1f1bc60d2"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/5.3.2"}, "time": "2018-01-15T07:18:01+00:00"}, {"version": "5.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "70f1fa53b71c4647bf2762c09068a95f77e12fb8"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/70f1fa53b71c4647bf2762c09068a95f77e12fb8", "type": "zip", "shasum": "", "reference": "70f1fa53b71c4647bf2762c09068a95f77e12fb8"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/5.3"}, "time": "2016-07-15T19:28:39+00:00", "require": {"php": ">=5.4.0", "guzzlehttp/ringphp": "^1.1"}}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f3c8c22471cb55475105c14769644a49c3262b93"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f3c8c22471cb55475105c14769644a49c3262b93", "type": "zip", "shasum": "", "reference": "f3c8c22471cb55475105c14769644a49c3262b93"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2015-05-20T03:47:55+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require-dev": {"ext-curl": "*", "psr/log": "^1.0", "phpunit/phpunit": "^4.0"}}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "475b29ccd411f2fa8a408e64576418728c032cfa"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/475b29ccd411f2fa8a408e64576418728c032cfa", "type": "zip", "shasum": "", "reference": "475b29ccd411f2fa8a408e64576418728c032cfa"}, "time": "2015-01-28T01:03:29+00:00", "require": {"php": ">=5.4.0", "guzzlehttp/ringphp": "~1.0"}, "require-dev": {"ext-curl": "*", "psr/log": "~1.0", "phpunit/phpunit": "~4.0"}}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f1085bb4e023766a66b7b051914ec73bdf7202b5"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f1085bb4e023766a66b7b051914ec73bdf7202b5", "type": "zip", "shasum": "", "reference": "f1085bb4e023766a66b7b051914ec73bdf7202b5"}, "time": "2014-12-19T20:27:15+00:00"}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "6c72627de1d66832e4270e36e56acdb0d1d8f282"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/6c72627de1d66832e4270e36e56acdb0d1d8f282", "type": "zip", "shasum": "", "reference": "6c72627de1d66832e4270e36e56acdb0d1d8f282"}, "time": "2014-11-04T07:09:15+00:00"}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "90b5b961dba11f5a1612eeb6fbd59c227112f763"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/90b5b961dba11f5a1612eeb6fbd59c227112f763", "type": "zip", "shasum": "", "reference": "90b5b961dba11f5a1612eeb6fbd59c227112f763"}, "time": "2014-10-31T03:26:57+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "9e806208d9b418a58ec86c49078aa94385e64bbd"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/9e806208d9b418a58ec86c49078aa94385e64bbd", "type": "zip", "shasum": "", "reference": "9e806208d9b418a58ec86c49078aa94385e64bbd"}, "time": "2014-10-16T18:00:41+00:00", "extra": {"branch-alias": {"dev-ring": "5.0-dev"}}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "28b51e11237f25cdb0efaea8e45af26007831aa9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/28b51e11237f25cdb0efaea8e45af26007831aa9", "type": "zip", "shasum": "", "reference": "28b51e11237f25cdb0efaea8e45af26007831aa9"}, "time": "2014-10-13T03:05:51+00:00"}, {"version": "4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "13a8e5acff26b0a87d353042170b48976da004a1"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/13a8e5acff26b0a87d353042170b48976da004a1", "type": "zip", "shasum": "", "reference": "13a8e5acff26b0a87d353042170b48976da004a1"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/4.x"}, "time": "2016-07-15T17:44:18+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "require": {"php": ">=5.4.0", "ext-json": "*", "guzzlehttp/streams": "~2.1"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}}, {"version": "4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "66fd916e9f9130bc22c51450476823391cb2f67c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/66fd916e9f9130bc22c51450476823391cb2f67c", "type": "zip", "shasum": "", "reference": "66fd916e9f9130bc22c51450476823391cb2f67c"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "time": "2014-10-05T19:29:14+00:00"}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "9c4fbbf6457768f5036fbd88f1229f3fca812a5d"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/9c4fbbf6457768f5036fbd88f1229f3fca812a5d", "type": "zip", "shasum": "", "reference": "9c4fbbf6457768f5036fbd88f1229f3fca812a5d"}, "time": "2014-09-08T22:11:58+00:00"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "47ca212e985d962b3cbd67675eec54bb72d66062"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/47ca212e985d962b3cbd67675eec54bb72d66062", "type": "zip", "shasum": "", "reference": "47ca212e985d962b3cbd67675eec54bb72d66062"}, "time": "2014-08-20T05:30:12+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "cc79aa76054e3e5e45dfd87f7b0f61f54b3a759d"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/cc79aa76054e3e5e45dfd87f7b0f61f54b3a759d", "type": "zip", "shasum": "", "reference": "cc79aa76054e3e5e45dfd87f7b0f61f54b3a759d"}, "time": "2014-08-18T00:48:35+00:00"}, {"version": "4.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "e196b8f44f9492a11261ea8f7b9724613a198daf"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/e196b8f44f9492a11261ea8f7b9724613a198daf", "type": "zip", "shasum": "", "reference": "e196b8f44f9492a11261ea8f7b9724613a198daf"}, "time": "2014-08-14T20:29:51+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require": {"php": ">=5.4.0", "ext-json": "*", "guzzlehttp/streams": "~1.4"}}, {"version": "4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "448f2c2076cf0fb756230611491c4f7ecb735a29"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/448f2c2076cf0fb756230611491c4f7ecb735a29", "type": "zip", "shasum": "", "reference": "448f2c2076cf0fb756230611491c4f7ecb735a29"}, "time": "2014-08-08T01:30:43+00:00"}, {"version": "4.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "e49ad58038b46a0d7739627a72f6d998cd92d3d6"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/e49ad58038b46a0d7739627a72f6d998cd92d3d6", "type": "zip", "shasum": "", "reference": "e49ad58038b46a0d7739627a72f6d998cd92d3d6"}, "time": "2014-08-03T20:38:45+00:00", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}}, {"version": "4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "75e58ada121dffe1038331b808f8d3109499edc4"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/75e58ada121dffe1038331b808f8d3109499edc4", "type": "zip", "shasum": "", "reference": "75e58ada121dffe1038331b808f8d3109499edc4"}, "time": "2014-08-02T22:52:37+00:00"}, {"version": "4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5ae164dae6e65784286a698800190bc094407f7c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5ae164dae6e65784286a698800190bc094407f7c", "type": "zip", "shasum": "", "reference": "5ae164dae6e65784286a698800190bc094407f7c"}, "time": "2014-07-23T00:52:39+00:00"}, {"version": "4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "012b2aecbda4e38f119c19580898685851015fa7"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/012b2aecbda4e38f119c19580898685851015fa7", "type": "zip", "shasum": "", "reference": "012b2aecbda4e38f119c19580898685851015fa7"}, "time": "2014-07-16T03:01:02+00:00", "require": {"php": ">=5.4.0", "ext-json": "*", "guzzlehttp/streams": "~1.3"}}, {"version": "4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "095cf4db559bebc78d967890eb93280d8ceb5a08"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/095cf4db559bebc78d967890eb93280d8ceb5a08", "type": "zip", "shasum": "", "reference": "095cf4db559bebc78d967890eb93280d8ceb5a08"}, "time": "2014-06-18T18:22:41+00:00", "require": {"php": ">=5.4.0", "ext-json": "*", "guzzlehttp/streams": "~1.0"}}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "577a69ff7d0a24e9576a2885c3a7afbaadd51ec1"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/577a69ff7d0a24e9576a2885c3a7afbaadd51ec1", "type": "zip", "shasum": "", "reference": "577a69ff7d0a24e9576a2885c3a7afbaadd51ec1"}, "time": "2014-06-08T20:00:20+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "85a0ba7de064493c928a8bcdc5eef01e0bde9953"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/85a0ba7de064493c928a8bcdc5eef01e0bde9953", "type": "zip", "shasum": "", "reference": "85a0ba7de064493c928a8bcdc5eef01e0bde9953"}, "time": "2014-05-28T05:13:19+00:00", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "40db53833aaea528347994acd4578d7b9b2211ee"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/40db53833aaea528347994acd4578d7b9b2211ee", "type": "zip", "shasum": "", "reference": "40db53833aaea528347994acd4578d7b9b2211ee"}, "time": "2014-04-16T17:33:22+00:00", "require": {"php": ">=5.4.0", "guzzlehttp/streams": "~1.0"}, "suggest": "__unset"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "c1cf6c8630b71fa7c9b8f7e769758bb1d9147dae"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/c1cf6c8630b71fa7c9b8f7e769758bb1d9147dae", "type": "zip", "shasum": "", "reference": "c1cf6c8630b71fa7c9b8f7e769758bb1d9147dae"}, "time": "2014-04-04T20:43:50+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "4063f08ca434efac12bf7a3db0d370b1c451345b"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/4063f08ca434efac12bf7a3db0d370b1c451345b", "type": "zip", "shasum": "", "reference": "4063f08ca434efac12bf7a3db0d370b1c451345b"}, "time": "2014-03-29T23:11:36+00:00", "require": {"php": ">=5.4.0", "guzzlehttp/streams": "1.*"}, "require-dev": {"ext-curl": "*", "psr/log": "~1", "phpunit/phpunit": "4.*"}}, {"version": "4.0.0-rc.2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "089d89b960fdc834713920dee6501d489bdbb63c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/089d89b960fdc834713920dee6501d489bdbb63c", "type": "zip", "shasum": "", "reference": "089d89b960fdc834713920dee6501d489bdbb63c"}, "time": "2014-03-25T18:55:31+00:00"}, {"version": "4.0.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "390ebb70a287ee70c6a66cc514515ca0f3a73983"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/390ebb70a287ee70c6a66cc514515ca0f3a73983", "type": "zip", "shasum": "", "reference": "390ebb70a287ee70c6a66cc514515ca0f3a73983"}, "time": "2014-03-15T23:49:17+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=5.4.0", "guzzlehttp/streams": "1.0.0-rc.1"}}, {"version": "v3.8.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}], "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "4de0618a01b34aa1c8c33a3f13f396dcd3882eba"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/4de0618a01b34aa1c8c33a3f13f396dcd3882eba", "type": "zip", "shasum": "", "reference": "4de0618a01b34aa1c8c33a3f13f396dcd3882eba"}, "time": "2014-01-28T22:29:15+00:00", "autoload": {"psr-0": {"Guzzle": "src/", "Guzzle\\Tests": "tests/"}}, "extra": {"branch-alias": {"dev-master": "3.8-dev"}}, "require": {"php": ">=5.3.3", "ext-curl": "*", "symfony/event-dispatcher": ">=2.1"}, "require-dev": {"doctrine/cache": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "psr/log": "1.0.*", "zendframework/zend-cache": "<2.3", "zendframework/zend-log": "<2.3", "phpunit/phpunit": "3.7.*"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}}, {"version": "v3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b4a3ce8c05e777fa18b802956d5d0e38ad338a69"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b4a3ce8c05e777fa18b802956d5d0e38ad338a69", "type": "zip", "shasum": "", "reference": "b4a3ce8c05e777fa18b802956d5d0e38ad338a69"}, "time": "2013-12-05T23:39:20+00:00", "require-dev": {"doctrine/cache": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "psr/log": "1.0.*", "zendframework/zend-cache": "2.0.*", "zendframework/zend-log": "2.0.*", "phpunit/phpunit": "3.7.*"}}, {"version": "v3.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b170b028c6bb5799640e46c8803015b0f9a45ed9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b170b028c6bb5799640e46c8803015b0f9a45ed9", "type": "zip", "shasum": "", "reference": "b170b028c6bb5799640e46c8803015b0f9a45ed9"}, "time": "2013-10-02T20:47:00+00:00", "extra": {"branch-alias": {"dev-master": "3.7-dev"}}}, {"version": "v3.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0f16aad385528b5cf790392cb4a4d16cf600e944"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0f16aad385528b5cf790392cb4a4d16cf600e944", "type": "zip", "shasum": "", "reference": "0f16aad385528b5cf790392cb4a4d16cf600e944"}, "time": "2013-09-08T21:09:18+00:00", "require": {"php": ">=5.3.2", "ext-curl": "*", "symfony/event-dispatcher": ">=2.1"}}, {"version": "v3.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "eaef90d27bb1d682e1f6ab2d77606dc0159049e6"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/eaef90d27bb1d682e1f6ab2d77606dc0159049e6", "type": "zip", "shasum": "", "reference": "eaef90d27bb1d682e1f6ab2d77606dc0159049e6"}, "time": "2013-08-02T18:31:05+00:00"}, {"version": "v3.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "aa68be2fa33896c0a0a7638d5b8e3f9e1a41bfab"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/aa68be2fa33896c0a0a7638d5b8e3f9e1a41bfab", "type": "zip", "shasum": "", "reference": "aa68be2fa33896c0a0a7638d5b8e3f9e1a41bfab"}, "time": "2013-07-05T20:17:54+00:00"}, {"version": "v3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0f2aad252b9c9120743dd475b383b1b6fb54c2f3"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0f2aad252b9c9120743dd475b383b1b6fb54c2f3", "type": "zip", "shasum": "", "reference": "0f2aad252b9c9120743dd475b383b1b6fb54c2f3"}, "time": "2013-06-11T00:24:07+00:00"}, {"version": "v3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b550d534c9b668c767b6a532bd686d0942505f7a"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b550d534c9b668c767b6a532bd686d0942505f7a", "type": "zip", "shasum": "", "reference": "b550d534c9b668c767b6a532bd686d0942505f7a"}, "time": "2013-05-30T07:01:25+00:00", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "e97207d07d0385eac269a976628b7a5f9eeb4e07"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/e97207d07d0385eac269a976628b7a5f9eeb4e07", "type": "zip", "shasum": "", "reference": "e97207d07d0385eac269a976628b7a5f9eeb4e07"}, "time": "2013-05-13T20:17:47+00:00", "extra": {"branch-alias": {"dev-master": "3.5-dev"}}}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5896a1475d245993b693434bc77a33e92c7feaf5"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5896a1475d245993b693434bc77a33e92c7feaf5", "type": "zip", "shasum": "", "reference": "5896a1475d245993b693434bc77a33e92c7feaf5"}, "time": "2013-04-30T20:31:38+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "require-dev": {"doctrine/cache": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "psr/log": "1.0.*", "zendframework/zend-cache": "2.0.*", "zendframework/zend-log": "2.0.*", "zend/zend-log1": "1.12", "zend/zend-cache1": "1.12", "phpunit/phpunit": "3.7.*"}}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "bee77d3bfdf7fa73b465a77beadde9b919f79782"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/bee77d3bfdf7fa73b465a77beadde9b919f79782", "type": "zip", "shasum": "", "reference": "bee77d3bfdf7fa73b465a77beadde9b919f79782"}, "time": "2013-04-29T23:55:30+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "938e2075f4870857f24dc3b206235dd3e0f969ae"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/938e2075f4870857f24dc3b206235dd3e0f969ae", "type": "zip", "shasum": "", "reference": "938e2075f4870857f24dc3b206235dd3e0f969ae"}, "time": "2013-04-16T20:56:26+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "76f16a154659afae37a411a868e6aafc19133466"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/76f16a154659afae37a411a868e6aafc19133466", "type": "zip", "shasum": "", "reference": "76f16a154659afae37a411a868e6aafc19133466"}, "time": "2013-04-12T05:58:15+00:00"}, {"version": "v3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0657f36c7780fb0f95cf117da3aab15cb580fb66"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0657f36c7780fb0f95cf117da3aab15cb580fb66", "type": "zip", "shasum": "", "reference": "0657f36c7780fb0f95cf117da3aab15cb580fb66"}, "time": "2013-03-10T23:05:38+00:00", "require-dev": {"doctrine/cache": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "zendframework/zend-cache": "2.0.*", "zendframework/zend-log": "2.0.*", "zend/zend-log1": "1.12", "zend/zend-cache1": "1.12", "phpunit/phpunit": "3.7.*"}}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d8f28e9f48d2566e28c80ce9be67cb9c3e1449a6"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d8f28e9f48d2566e28c80ce9be67cb9c3e1449a6", "type": "zip", "shasum": "", "reference": "d8f28e9f48d2566e28c80ce9be67cb9c3e1449a6"}, "time": "2013-03-04T00:41:45+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "3a3fa1c5af3ac29b5dcb216b708ee3c48b52325e"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/3a3fa1c5af3ac29b5dcb216b708ee3c48b52325e", "type": "zip", "shasum": "", "reference": "3a3fa1c5af3ac29b5dcb216b708ee3c48b52325e"}, "time": "2013-02-15T01:33:10+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "require-dev": {"doctrine/common": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "zendframework/zend-cache": "2.0.*", "zendframework/zend-log": "2.0.*", "zend/zend-log1": "1.12", "zend/zend-cache1": "1.12", "phpunit/phpunit": "3.7.*"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "7901ea7d27373d0cc85eac6f6694e4c2ced90a26"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/7901ea7d27373d0cc85eac6f6694e4c2ced90a26", "type": "zip", "shasum": "", "reference": "7901ea7d27373d0cc85eac6f6694e4c2ced90a26"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.1.2"}, "time": "2013-01-28T00:07:40+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d295578db8e580ce9f7d0734535406d95123f3d6"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d295578db8e580ce9f7d0734535406d95123f3d6", "type": "zip", "shasum": "", "reference": "d295578db8e580ce9f7d0734535406d95123f3d6"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.1.1"}, "time": "2013-01-21T05:46:09+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5baae799e136d399246cbf14055deed2a55d687f"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5baae799e136d399246cbf14055deed2a55d687f", "type": "zip", "shasum": "", "reference": "5baae799e136d399246cbf14055deed2a55d687f"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.1.0"}, "time": "2013-01-14T05:09:07+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f31f35d1669382936861533bd0217fcf830dc9a9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f31f35d1669382936861533bd0217fcf830dc9a9", "type": "zip", "shasum": "", "reference": "f31f35d1669382936861533bd0217fcf830dc9a9"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.7"}, "time": "2012-12-19T23:06:35+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "09c2a09584c455a3e049210ee7dc92f7b65f6210"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/09c2a09584c455a3e049210ee7dc92f7b65f6210", "type": "zip", "shasum": "", "reference": "09c2a09584c455a3e049210ee7dc92f7b65f6210"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.6"}, "time": "2012-12-10T05:25:04+00:00"}, {"version": "v3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "184407e254ec806d632416a264a7d6bf5b417f4b"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/184407e254ec806d632416a264a7d6bf5b417f4b", "type": "zip", "shasum": "", "reference": "184407e254ec806d632416a264a7d6bf5b417f4b"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.5"}, "time": "2012-11-19T00:15:33+00:00", "require": {"php": ">=5.3.2", "ext-curl": "*", "symfony/event-dispatcher": "2.1.*"}, "extra": "__unset"}, {"version": "v3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d54dce1c53219c1bf17c0095e1cac04a28e3a2a6"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d54dce1c53219c1bf17c0095e1cac04a28e3a2a6", "type": "zip", "shasum": "", "reference": "d54dce1c53219c1bf17c0095e1cac04a28e3a2a6"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.4"}, "time": "2012-11-12T00:00:24+00:00"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "20190c51e7eaec7d061afa59fb9605c02dc8bb24"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/20190c51e7eaec7d061afa59fb9605c02dc8bb24", "type": "zip", "shasum": "", "reference": "20190c51e7eaec7d061afa59fb9605c02dc8bb24"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.3"}, "time": "2012-11-04T20:31:03+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "2fdcd68fce2f3fd7cebb0d61739a41483ad557e2"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/2fdcd68fce2f3fd7cebb0d61739a41483ad557e2", "type": "zip", "shasum": "", "reference": "2fdcd68fce2f3fd7cebb0d61739a41483ad557e2"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.2"}, "time": "2012-10-25T04:55:19+00:00", "require-dev": {"doctrine/common": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "zendframework/zend-cache": "2.0.*", "zendframework/zend-log": "2.0.*", "zend/zend-log1": "1.12", "zend/zend-cache1": "1.12"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "993e8eb4905026bd79821d1faafca1798f7a78f3"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/993e8eb4905026bd79821d1faafca1798f7a78f3", "type": "zip", "shasum": "", "reference": "993e8eb4905026bd79821d1faafca1798f7a78f3"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v3.0.1"}, "time": "2012-10-22T20:20:55+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d0fc839abd2e411df908c6fd0ed1379db691b1b0"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d0fc839abd2e411df908c6fd0ed1379db691b1b0", "type": "zip", "shasum": "", "reference": "d0fc839abd2e411df908c6fd0ed1379db691b1b0"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/TAG_B"}, "time": "2012-10-16T04:57:15+00:00"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "6738770a1d9e299cec6c2079220628ee8c739483"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/6738770a1d9e299cec6c2079220628ee8c739483", "type": "zip", "shasum": "", "reference": "6738770a1d9e299cec6c2079220628ee8c739483"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.8"}, "time": "2012-10-16T00:42:47+00:00", "require-dev": {"doctrine/common": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "zendframework/zend-cache": "2.0.0beta4", "zendframework/zend-log": "2.0.0beta4", "zendframework/zend-loader": "2.0.0beta4", "zendframework/zend-stdlib": "2.0.0beta4", "zendframework/zend-eventmanager": "2.0.0beta4", "zendframework/zend-servicemanager": "2.0.0beta4", "zend/zend-log1": "1.12", "zend/zend-cache1": "1.12"}, "replace": {"guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/parser": "self.version"}}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "0ed797ec53299532192f221b65d68ce1d2a7f782"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/0ed797ec53299532192f221b65d68ce1d2a7f782", "type": "zip", "shasum": "", "reference": "0ed797ec53299532192f221b65d68ce1d2a7f782"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.7"}, "time": "2012-09-30T20:48:10+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "93e2405749b4c553de61e06f1a608eca485ff0c9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/93e2405749b4c553de61e06f1a608eca485ff0c9", "type": "zip", "shasum": "", "reference": "93e2405749b4c553de61e06f1a608eca485ff0c9"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.6"}, "time": "2012-09-05T23:30:30+00:00"}, {"version": "v2.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "cc5a019e42abeb6e4d8d9ac9759063fc37ead281"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/cc5a019e42abeb6e4d8d9ac9759063fc37ead281", "type": "zip", "shasum": "", "reference": "cc5a019e42abeb6e4d8d9ac9759063fc37ead281"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.5"}, "time": "2012-08-29T20:41:32+00:00"}, {"version": "v2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d85ebd3e6cb2fd9543184b9cbb370706fa346944"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d85ebd3e6cb2fd9543184b9cbb370706fa346944", "type": "zip", "shasum": "", "reference": "d85ebd3e6cb2fd9543184b9cbb370706fa346944"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.4"}, "time": "2012-08-15T17:48:56+00:00"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "2a646f21356bccb935414572d3bb9df308f34439"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/2a646f21356bccb935414572d3bb9df308f34439", "type": "zip", "shasum": "", "reference": "2a646f21356bccb935414572d3bb9df308f34439"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.3"}, "time": "2012-07-30T20:27:22+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "fab426ba74b3c753c03fee1f8b460babfd120d2c"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/fab426ba74b3c753c03fee1f8b460babfd120d2c", "type": "zip", "shasum": "", "reference": "fab426ba74b3c753c03fee1f8b460babfd120d2c"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.2"}, "time": "2012-07-24T18:26:42+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "9f57a330550ff07a555a8f122ff3265e3f035843"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/9f57a330550ff07a555a8f122ff3265e3f035843", "type": "zip", "shasum": "", "reference": "9f57a330550ff07a555a8f122ff3265e3f035843"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.1"}, "time": "2012-07-16T21:08:54+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "86bb222fe73df7963bad987781705e69f17ac8c1"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/86bb222fe73df7963bad987781705e69f17ac8c1", "type": "zip", "shasum": "", "reference": "86bb222fe73df7963bad987781705e69f17ac8c1"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.8.0"}, "time": "2012-07-16T00:21:47+00:00", "require": {"php": ">=5.3.2", "ext-curl": "*", "symfony/event-dispatcher": "2.1.0-beta2"}}, {"version": "v2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "992e67009d12f070d7423742f1682c58608521b9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/992e67009d12f070d7423742f1682c58608521b9", "type": "zip", "shasum": "", "reference": "992e67009d12f070d7423742f1682c58608521b9"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.7.2"}, "time": "2012-07-02T21:04:37+00:00", "require": {"php": ">=5.3.2", "ext-curl": "*", "symfony/event-dispatcher": "2.*"}, "require-dev": {"doctrine/common": "*", "symfony/class-loader": "*", "monolog/monolog": "1.*", "zendframework/zend-cache": "2.0.*", "zendframework/zend-log": "2.0.*", "zendframework/zend-loader": "2.0.*", "zendframework/zend-stdlib": "2.0.*", "zendframework/zend-eventmanager": "2.0.*", "zendframework/zend-servicemanager": "2.0.*", "zend/zend-log1": "1.11", "zend/zend-cache1": "1.11"}}, {"keywords": ["framework", "curl", "http", "rest", "client", "web service"], "homepage": "http://www.guzzlephp.org/", "version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "95bd5309ad9e42dc6bdfbe868a1f91a574e31921"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/95bd5309ad9e42dc6bdfbe868a1f91a574e31921", "type": "zip", "shasum": "", "reference": "95bd5309ad9e42dc6bdfbe868a1f91a574e31921"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.7.1"}, "time": "2012-06-27T01:58:44+00:00", "replace": "__unset"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "cb02ff78185fe8eb8a0fd7bbd727d029bdb44e8f"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/cb02ff78185fe8eb8a0fd7bbd727d029bdb44e8f", "type": "zip", "shasum": "", "reference": "cb02ff78185fe8eb8a0fd7bbd727d029bdb44e8f"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.7.0"}, "time": "2012-06-25T21:33:36+00:00"}, {"keywords": ["framework", "curl", "http", "rest", "web service"], "version": "v2.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "72b6c53af2bfaf575e78cefcfa32cb438face1df"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/72b6c53af2bfaf575e78cefcfa32cb438face1df", "type": "zip", "shasum": "", "reference": "72b6c53af2bfaf575e78cefcfa32cb438face1df"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.6"}, "time": "2012-06-10T07:49:48+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "10759045ad0710e46dd83e88f89c83021ab8a950"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/10759045ad0710e46dd83e88f89c83021ab8a950", "type": "zip", "shasum": "", "reference": "10759045ad0710e46dd83e88f89c83021ab8a950"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.5"}, "time": "2012-06-04T02:37:12+00:00", "require": {"php": ">=5.3.2", "ext-curl": "*", "symfony/event-dispatcher": "*"}, "require-dev": {"doctrine/common": "*", "symfony/class-loader": "*", "monolog/monolog": "*", "zend/zend-cache": "2.0.0beta3", "zend/zend-log": "2.0.0beta3", "zend/zend-loader": "2.0.0beta3", "zend/zend-stdlib": "2.0.0beta3", "zend/zend-eventmanager": "2.0.0beta3", "zend/zend-log1": "1.11", "zend/zend-cache1": "1.11"}}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "acd36b13c2616e54c9ca9457d6ba7bd622b522f7"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/acd36b13c2616e54c9ca9457d6ba7bd622b522f7", "type": "zip", "shasum": "", "reference": "acd36b13c2616e54c9ca9457d6ba7bd622b522f7"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.4"}, "time": "2012-05-31T06:42:52+00:00"}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "bf668df105cc8f600dca666931fe924c4c8202b1"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/bf668df105cc8f600dca666931fe924c4c8202b1", "type": "zip", "shasum": "", "reference": "bf668df105cc8f600dca666931fe924c4c8202b1"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.3"}, "time": "2012-05-23T07:23:26+00:00"}, {"version": "v2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "468b3b71a8669901ec9032feec4ccd9577999aa6"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/468b3b71a8669901ec9032feec4ccd9577999aa6", "type": "zip", "shasum": "", "reference": "468b3b71a8669901ec9032feec4ccd9577999aa6"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.2"}, "time": "2012-05-19T22:47:10+00:00"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "cd8f008050c89daaa7ab48dfcfad3f9fd1d16eb4"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/cd8f008050c89daaa7ab48dfcfad3f9fd1d16eb4", "type": "zip", "shasum": "", "reference": "cd8f008050c89daaa7ab48dfcfad3f9fd1d16eb4"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.1"}, "time": "2012-05-19T08:23:07+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "e77e1e19784a799f0aab0914dd4f4b813dd7546d"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/e77e1e19784a799f0aab0914dd4f4b813dd7546d", "type": "zip", "shasum": "", "reference": "e77e1e19784a799f0aab0914dd4f4b813dd7546d"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.6.0"}, "time": "2012-05-15T15:32:20+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f7e6aa551ac84981f7910c49936175468c2392cc"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f7e6aa551ac84981f7910c49936175468c2392cc", "type": "zip", "shasum": "", "reference": "f7e6aa551ac84981f7910c49936175468c2392cc"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.5.0"}, "time": "2012-05-08T06:55:53+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f5ee3fb4531007dca69894ba2f8a650082ec5021"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f5ee3fb4531007dca69894ba2f8a650082ec5021", "type": "zip", "shasum": "", "reference": "f5ee3fb4531007dca69894ba2f8a650082ec5021"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.4.1"}, "time": "2012-04-24T00:54:02+00:00", "require": {"php": ">=5.3.2", "ext-curl": "*", "symfony/event-dispatcher": "*", "symfony/validator": "*"}}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "cc7c7f42ac610179cfa621062cf5aa7db4e9f198"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/cc7c7f42ac610179cfa621062cf5aa7db4e9f198", "type": "zip", "shasum": "", "reference": "cc7c7f42ac610179cfa621062cf5aa7db4e9f198"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.4.0"}, "time": "2012-04-22T21:44:37+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f57169a2d6c8f001ddce442a1265706a7f1e79c8"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f57169a2d6c8f001ddce442a1265706a7f1e79c8", "type": "zip", "shasum": "", "reference": "f57169a2d6c8f001ddce442a1265706a7f1e79c8"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.3.2"}, "time": "2012-04-16T22:13:53+00:00"}, {"keywords": ["framework", "http", "rest", "web service"], "version": "v2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5ad66ff4d1cc80c072c5ef5c431c8bee1900c025"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5ad66ff4d1cc80c072c5ef5c431c8bee1900c025", "type": "zip", "shasum": "", "reference": "5ad66ff4d1cc80c072c5ef5c431c8bee1900c025"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.2.4"}, "time": "2012-04-02T22:27:37+00:00", "suggest": {"symfony/class-loader": "*", "monolog/monolog": "*", "zend/zend-cache": "*", "zend/zend-log": "*"}, "require-dev": "__unset"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5fd1ae9a82aeeca791a300e27eb98548986a4228"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5fd1ae9a82aeeca791a300e27eb98548986a4228", "type": "zip", "shasum": "", "reference": "5fd1ae9a82aeeca791a300e27eb98548986a4228"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.2.3"}, "time": "2012-03-20T16:27:40+00:00", "suggest": {"symfony/class-loader": "*", "doctrine/common": "*", "monolog/monolog": "*", "zend/zend-cache": "*", "zend/zend-log": "*"}}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b992c6f38c1dfdc3e19e780e8e16f4e00820f48d"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b992c6f38c1dfdc3e19e780e8e16f4e00820f48d", "type": "zip", "shasum": "", "reference": "b992c6f38c1dfdc3e19e780e8e16f4e00820f48d"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.2.2"}, "time": "2012-02-17T05:13:49+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "6edccc2db838dd9185315e3f9e4bf225b4ac86b9"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/6edccc2db838dd9185315e3f9e4bf225b4ac86b9", "type": "zip", "shasum": "", "reference": "6edccc2db838dd9185315e3f9e4bf225b4ac86b9"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.2.1"}, "time": "2012-02-14T02:28:33+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "b9c04909c7cd8e4cd7b3f6970621d4d6bc00936f"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/b9c04909c7cd8e4cd7b3f6970621d4d6bc00936f", "type": "zip", "shasum": "", "reference": "b9c04909c7cd8e4cd7b3f6970621d4d6bc00936f"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.2.0"}, "time": "2012-02-08T05:23:39+00:00"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "5bcdd556188da59ad8a4befcca291f440c978021"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/5bcdd556188da59ad8a4befcca291f440c978021", "type": "zip", "shasum": "", "reference": "5bcdd556188da59ad8a4befcca291f440c978021"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.1.4"}, "time": "2012-01-29T03:12:24+00:00"}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "9a9ae3b6bee7470c692612f07182a31074d3fc37"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/9a9ae3b6bee7470c692612f07182a31074d3fc37", "type": "zip", "shasum": "", "reference": "9a9ae3b6bee7470c692612f07182a31074d3fc37"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.1.3"}, "time": "2012-01-25T17:26:25+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "f619d8fa0e27fe4e8920e1013413f62bc446a304"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/f619d8fa0e27fe4e8920e1013413f62bc446a304", "type": "zip", "shasum": "", "reference": "f619d8fa0e27fe4e8920e1013413f62bc446a304"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.1.2"}, "time": "2012-01-25T17:02:08+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "dc22f479d052f8bebc232e21d0bebe69a87ae3fa"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/dc22f479d052f8bebc232e21d0bebe69a87ae3fa", "type": "zip", "shasum": "", "reference": "dc22f479d052f8bebc232e21d0bebe69a87ae3fa"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.1.1"}, "time": "2012-01-24T20:56:04+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "53a2bf791c623735c12cc48e559d45e65e84ab75"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/53a2bf791c623735c12cc48e559d45e65e84ab75", "type": "zip", "shasum": "", "reference": "53a2bf791c623735c12cc48e559d45e65e84ab75"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.1.0"}, "time": "2012-01-24T01:41:45+00:00"}, {"version": "v2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "109bad53d3c6d4f006e4dbabb63579b6cb9bc6da"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/109bad53d3c6d4f006e4dbabb63579b6cb9bc6da", "type": "zip", "shasum": "", "reference": "109bad53d3c6d4f006e4dbabb63579b6cb9bc6da"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.0.5"}, "time": "2012-01-22T23:48:25+00:00"}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "2986444944ae769e967c6b7c3666fdd6e4d35346"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/2986444944ae769e967c6b7c3666fdd6e4d35346", "type": "zip", "shasum": "", "reference": "2986444944ae769e967c6b7c3666fdd6e4d35346"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.0.4"}, "time": "2012-01-22T01:08:15+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "829d8ae1780c277f20fd712b0a32f9e6042b3ecc"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/829d8ae1780c277f20fd712b0a32f9e6042b3ecc", "type": "zip", "shasum": "", "reference": "829d8ae1780c277f20fd712b0a32f9e6042b3ecc"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.0.3"}, "time": "2012-01-20T06:28:15+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "ac64abc2c05b921efc4623379c1674a282475ae5"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/ac64abc2c05b921efc4623379c1674a282475ae5", "type": "zip", "shasum": "", "reference": "ac64abc2c05b921efc4623379c1674a282475ae5"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.0.2"}, "time": "2012-01-18T05:37:36+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "40027b0fff3a8ba620a6472df97393cd4d91824e"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/40027b0fff3a8ba620a6472df97393cd4d91824e", "type": "zip", "shasum": "", "reference": "40027b0fff3a8ba620a6472df97393cd4d91824e"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.0.1"}, "time": "2012-01-16T22:54:21+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "d7e332bb647545adb6c6bdac73882623afa1829b"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/d7e332bb647545adb6c6bdac73882623afa1829b", "type": "zip", "shasum": "", "reference": "d7e332bb647545adb6c6bdac73882623afa1829b"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v2.0.0"}, "time": "2012-01-15T16:36:28+00:00", "autoload": {"psr-0": {"Guzzle": "src/"}}}, {"keywords": ["framework", "http", "rest"], "version": "v1.0.4", "version_normalized": "*******", "authors": [{"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}, {"name": "<PERSON>", "email": "micha<PERSON>@guzzlephp.org"}], "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "13ea2e723eaba72d99eefb24ec36f95ff7ef0068"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/13ea2e723eaba72d99eefb24ec36f95ff7ef0068", "type": "zip", "shasum": "", "reference": "13ea2e723eaba72d99eefb24ec36f95ff7ef0068"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v1.0.4"}, "time": "2011-12-31T21:30:12+00:00", "require": {"php": ">=5.3.2"}, "suggest": "__unset"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/guzzle.git", "type": "git", "reference": "45e5f83e58ab6a55c52c930e307dc6be2edd679e"}, "dist": {"url": "https://api.github.com/repos/guzzle/guzzle/zipball/45e5f83e58ab6a55c52c930e307dc6be2edd679e", "type": "zip", "shasum": "", "reference": "45e5f83e58ab6a55c52c930e307dc6be2edd679e"}, "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/v1.0.3"}, "time": "2011-11-14T01:56:54+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-yfw5-9gnj-n2c7", "affectedVersions": ">=7,<7.4.5|>=4,<6.5.8"}, {"advisoryId": "PKSA-k1b4-kshy-xgbh", "affectedVersions": ">=7,<7.4.5|>=4,<6.5.8"}, {"advisoryId": "PKSA-2z36-j4q9-rsfy", "affectedVersions": ">=7,<7.4.4|>=4,<6.5.7"}, {"advisoryId": "PKSA-fvw5-9t6n-nwvr", "affectedVersions": ">=7,<7.4.4|>=4,<6.5.7"}, {"advisoryId": "PKSA-6d8m-6kgw-18zr", "affectedVersions": ">=7,<7.4.3|>=4,<6.5.6"}, {"advisoryId": "PKSA-stmn-hvzq-wph6", "affectedVersions": ">=6,<6.2.1|>=4.0.0-rc2,<4.2.4|>=5,<5.3.1"}], "last-modified": "Thu, 27 Mar 2025 18:51:19 GMT"}