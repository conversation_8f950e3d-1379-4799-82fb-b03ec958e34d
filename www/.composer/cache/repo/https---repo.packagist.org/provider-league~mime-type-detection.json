{"minified": "composer/2.0", "packages": {"league/mime-type-detection": [{"name": "league/mime-type-detection", "description": "Mime-type detection for Flysystem", "keywords": [], "homepage": "", "version": "1.16.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/2d6702ff215bf922936ccc1ad31007edc76451b9", "type": "zip", "shasum": "", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "type": "library", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-09-21T08:32:55+00:00", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "require": {"php": "^7.4 || ^8.0", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0", "phpstan/phpstan": "^0.12.68", "friendsofphp/php-cs-fixer": "^3.2"}}, {"version": "1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "type": "zip", "shasum": "", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "time": "2024-01-28T23:22:08+00:00"}, {"version": "1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "b6a5854368533df0295c5761a0253656a2e52d9e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/b6a5854368533df0295c5761a0253656a2e52d9e", "type": "zip", "shasum": "", "reference": "b6a5854368533df0295c5761a0253656a2e52d9e"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.14.0"}, "time": "2023-10-17T14:13:20+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "a6dfb1194a2946fcdc1f38219445234f65b35c96"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/a6dfb1194a2946fcdc1f38219445234f65b35c96", "type": "zip", "shasum": "", "reference": "a6dfb1194a2946fcdc1f38219445234f65b35c96"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.13.0"}, "time": "2023-08-05T12:09:49+00:00"}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "c7f2872fb273bf493811473dafc88d60ae829f48"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/c7f2872fb273bf493811473dafc88d60ae829f48", "type": "zip", "shasum": "", "reference": "c7f2872fb273bf493811473dafc88d60ae829f48"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.12.0"}, "time": "2023-08-03T07:14:11+00:00", "require": {"php": "^7.2 || ^8.0", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^8.5.8 || ^9.3", "phpstan/phpstan": "^0.12.68", "friendsofphp/php-cs-fixer": "^3.2"}}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "type": "zip", "shasum": "", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.11.0"}, "time": "2022-04-17T13:12:02+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "3e4a35d756eedc67096f30240a68a3149120dae7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/3e4a35d756eedc67096f30240a68a3149120dae7", "type": "zip", "shasum": "", "reference": "3e4a35d756eedc67096f30240a68a3149120dae7"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.10.0"}, "time": "2022-04-11T12:49:04+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "aa70e813a6ad3d1558fc927863d47309b4c23e69"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/aa70e813a6ad3d1558fc927863d47309b4c23e69", "type": "zip", "shasum": "", "reference": "aa70e813a6ad3d1558fc927863d47309b4c23e69"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.9.0"}, "time": "2021-11-21T11:48:40+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "b38b25d7b372e9fddb00335400467b223349fd7e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/b38b25d7b372e9fddb00335400467b223349fd7e", "type": "zip", "shasum": "", "reference": "b38b25d7b372e9fddb00335400467b223349fd7e"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.8.0"}, "time": "2021-09-25T08:23:19+00:00", "require-dev": {"phpunit/phpunit": "^8.5.8 || ^9.3", "phpstan/phpstan": "^0.12.68", "friendsofphp/php-cs-fixer": "^2.18"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "type": "zip", "shasum": "", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.7.0"}, "time": "2021-01-18T20:58:21+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "abab7860d86533ae520523c7f6b5e74ca071116b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/abab7860d86533ae520523c7f6b5e74ca071116b", "type": "zip", "shasum": "", "reference": "abab7860d86533ae520523c7f6b5e74ca071116b"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.6.0"}, "time": "2021-01-18T20:19:53+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "353f66d7555d8a90781f6f5e7091932f9a4250aa"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/353f66d7555d8a90781f6f5e7091932f9a4250aa", "type": "zip", "shasum": "", "reference": "353f66d7555d8a90781f6f5e7091932f9a4250aa"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.5.1"}, "time": "2020-10-18T11:50:25+00:00", "require-dev": {"phpunit/phpunit": "^8.5.8", "phpstan/phpstan": "^0.12.36"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "ea2fbfc988bade315acd5967e6d02274086d0f28"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ea2fbfc988bade315acd5967e6d02274086d0f28", "type": "zip", "shasum": "", "reference": "ea2fbfc988bade315acd5967e6d02274086d0f28"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.5.0"}, "time": "2020-09-21T18:10:53+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "fda190b62b962d96a069fcc414d781db66d65b69"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/fda190b62b962d96a069fcc414d781db66d65b69", "type": "zip", "shasum": "", "reference": "fda190b62b962d96a069fcc414d781db66d65b69"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/main"}, "time": "2020-08-09T10:34:01+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "9fc15a73d1a2ac31a3e18784398ec0899dccd368"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/9fc15a73d1a2ac31a3e18784398ec0899dccd368", "type": "zip", "shasum": "", "reference": "9fc15a73d1a2ac31a3e18784398ec0899dccd368"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.3.0"}, "time": "2020-08-08T13:57:06+00:00", "require": {"php": "^7.2", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.11"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "de24424904510eeb518ce61375dca185721cf801"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/de24424904510eeb518ce61375dca185721cf801", "type": "zip", "shasum": "", "reference": "de24424904510eeb518ce61375dca185721cf801"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.2.0"}, "time": "2020-07-26T07:57:40+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "521ff65678b30c9efab86462eba3bf9e0676f25b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/521ff65678b30c9efab86462eba3bf9e0676f25b", "type": "zip", "shasum": "", "reference": "521ff65678b30c9efab86462eba3bf9e0676f25b"}, "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/master"}, "time": "2020-04-04T15:43:58+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/mime-type-detection.git", "type": "git", "reference": "bd4d425ab8eabcd8595571b33665ae3cd2aaeeff"}, "dist": {"url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/bd4d425ab8eabcd8595571b33665ae3cd2aaeeff", "type": "zip", "shasum": "", "reference": "bd4d425ab8eabcd8595571b33665ae3cd2aaeeff"}, "time": "2020-02-28T16:48:40+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 21 Sep 2024 09:06:43 GMT"}