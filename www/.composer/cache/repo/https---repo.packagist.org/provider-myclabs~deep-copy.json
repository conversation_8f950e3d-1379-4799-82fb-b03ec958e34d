{"minified": "composer/2.0", "packages": {"myclabs/deep-copy": [{"name": "myclabs/deep-copy", "description": "Create deep copies (clones) of your objects", "keywords": ["object", "clone", "object graph", "duplicate", "copy"], "homepage": "", "version": "1.13.3", "version_normalized": "********", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/faed855a7b5f4d4637717c2b3863e277116beb36", "type": "zip", "shasum": "", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36"}, "type": "library", "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-07-05T12:25:42+00:00", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}}, {"version": "1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "d25e62e636b0a9b01e3bdebb7823b474876dd829"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/d25e62e636b0a9b01e3bdebb7823b474876dd829", "type": "zip", "shasum": "", "reference": "d25e62e636b0a9b01e3bdebb7823b474876dd829"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.2"}, "time": "2025-07-04T14:07:32+00:00"}, {"version": "1.13.1", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "type": "zip", "shasum": "", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "time": "2025-04-29T12:36:36+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "024473a478be9df5fdaca2c793f2232fe788e414"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/024473a478be9df5fdaca2c793f2232fe788e414", "type": "zip", "shasum": "", "reference": "024473a478be9df5fdaca2c793f2232fe788e414"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.0"}, "time": "2025-02-12T12:17:51+00:00"}, {"version": "1.12.1", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/123267b2c49fbf30d78a7b2d333f6be754b94845", "type": "zip", "shasum": "", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.1"}, "time": "2024-11-08T17:47:46+00:00"}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "type": "zip", "shasum": "", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.0"}, "time": "2024-06-12T14:39:25+00:00"}, {"version": "1.11.1", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "type": "zip", "shasum": "", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "time": "2023-03-08T13:26:56+00:00", "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "type": "zip", "shasum": "", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "time": "2022-03-03T13:19:32+00:00"}, {"version": "1.10.3", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "c6a951b75d684fd43fbbd69617488e1e2e8924ba"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/c6a951b75d684fd43fbbd69617488e1e2e8924ba", "type": "zip", "shasum": "", "reference": "c6a951b75d684fd43fbbd69617488e1e2e8924ba"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.10.3"}, "time": "2022-03-02T14:16:47+00:00"}, {"version": "1.10.2", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "type": "zip", "shasum": "", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.10.2"}, "time": "2020-11-13T09:40:50+00:00", "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "conflict": "__unset"}, {"version": "1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "type": "zip", "shasum": "", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.x"}, "time": "2020-06-29T13:22:24+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "5796d127b0c4ff505b77455148ea9d5269d99758"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/5796d127b0c4ff505b77455148ea9d5269d99758", "type": "zip", "shasum": "", "reference": "5796d127b0c4ff505b77455148ea9d5269d99758"}, "time": "2020-06-28T07:02:41+00:00"}, {"version": "1.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "b2c28789e80a97badd14145fda39b545d83ca3ef"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/b2c28789e80a97badd14145fda39b545d83ca3ef", "type": "zip", "shasum": "", "reference": "b2c28789e80a97badd14145fda39b545d83ca3ef"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.9.5"}, "time": "2020-01-17T21:11:47+00:00", "require": {"php": "^7.1"}, "funding": "__unset"}, {"version": "1.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "579bb7356d91f9456ccd505f24ca8b667966a0a7"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/579bb7356d91f9456ccd505f24ca8b667966a0a7", "type": "zip", "shasum": "", "reference": "579bb7356d91f9456ccd505f24ca8b667966a0a7"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.9.4"}, "time": "2019-12-15T19:12:40+00:00"}, {"version": "1.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "007c053ae6f31bba39dfa19a7726f56e9763bbea"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/007c053ae6f31bba39dfa19a7726f56e9763bbea", "type": "zip", "shasum": "", "reference": "007c053ae6f31bba39dfa19a7726f56e9763bbea"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.9.3"}, "time": "2019-08-09T12:45:53+00:00"}, {"version": "1.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "8d0a041a565fc1e927e75f1d1bea5f34edf6e7d1"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/8d0a041a565fc1e927e75f1d1bea5f34edf6e7d1", "type": "zip", "shasum": "", "reference": "8d0a041a565fc1e927e75f1d1bea5f34edf6e7d1"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/2.x"}, "time": "2019-08-09T12:38:39+00:00", "require": {"php": "^7.2"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpunit/phpunit": "^7.1"}}, {"version": "1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "type": "zip", "shasum": "", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.9.1"}, "time": "2019-04-07T13:18:21+00:00", "require": {"php": "^7.1"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "78af75148f9fdd34ea727c8b529a9b4a8f7b740c"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/78af75148f9fdd34ea727c8b529a9b4a8f7b740c", "type": "zip", "shasum": "", "reference": "78af75148f9fdd34ea727c8b529a9b4a8f7b740c"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.9.0"}, "time": "2018-10-30T00:14:44+00:00", "require": {"php": "^7.2"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpunit/phpunit": "^7.1"}}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "3e01bdad3e18354c3dce54466b7fbe33a9f9f7f8"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3e01bdad3e18354c3dce54466b7fbe33a9f9f7f8", "type": "zip", "shasum": "", "reference": "3e01bdad3e18354c3dce54466b7fbe33a9f9f7f8"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.x"}, "time": "2018-06-11T23:09:50+00:00", "require": {"php": "^7.1"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "478465659fd987669df0bd8a9bf22a8710e5f1b6"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/478465659fd987669df0bd8a9bf22a8710e5f1b6", "type": "zip", "shasum": "", "reference": "478465659fd987669df0bd8a9bf22a8710e5f1b6"}, "time": "2018-05-29T17:25:09+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "type": "zip", "shasum": "", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e"}, "time": "2017-10-19T19:58:43+00:00", "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^4.1"}, "replace": "__unset"}, {"homepage": "https://github.com/myclabs/DeepCopy", "version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "8e6e04167378abf1ddb4d3522d8755c5fd90d102"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/8e6e04167378abf1ddb4d3522d8755c5fd90d102", "type": "zip", "shasum": "", "reference": "8e6e04167378abf1ddb4d3522d8755c5fd90d102"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/master"}, "time": "2017-04-12T18:52:22+00:00", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "require": {"php": ">=5.4.0"}, "require-dev": {"doctrine/collections": "1.*", "phpunit/phpunit": "~4.1"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "5a5a9fc8025a08d8919be87d6884d5a92520cefe"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/5a5a9fc8025a08d8919be87d6884d5a92520cefe", "type": "zip", "shasum": "", "reference": "5a5a9fc8025a08d8919be87d6884d5a92520cefe"}, "time": "2017-01-26T22:05:40+00:00"}, {"version": "1.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "399c1f9781e222f6eb6cc238796f5200d1b7f108"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/399c1f9781e222f6eb6cc238796f5200d1b7f108", "type": "zip", "shasum": "", "reference": "399c1f9781e222f6eb6cc238796f5200d1b7f108"}, "time": "2016-10-31T17:19:45+00:00"}, {"version": "1.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "ea74994a3dc7f8d2f65a06009348f2d63c81e61f"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/ea74994a3dc7f8d2f65a06009348f2d63c81e61f", "type": "zip", "shasum": "", "reference": "ea74994a3dc7f8d2f65a06009348f2d63c81e61f"}, "time": "2016-09-16T13:37:59+00:00"}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "94e5ca3e90aa5b34663780393e10914f7438f991"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/94e5ca3e90aa5b34663780393e10914f7438f991", "type": "zip", "shasum": "", "reference": "94e5ca3e90aa5b34663780393e10914f7438f991"}, "time": "2016-09-07T15:34:10+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "da8529775f14f4fdae33f916eb0cf65f6afbddbc"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/da8529775f14f4fdae33f916eb0cf65f6afbddbc", "type": "zip", "shasum": "", "reference": "da8529775f14f4fdae33f916eb0cf65f6afbddbc"}, "time": "2016-09-06T16:07:05+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "a8773992b362b58498eed24bf85005f363c34771"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/a8773992b362b58498eed24bf85005f363c34771", "type": "zip", "shasum": "", "reference": "a8773992b362b58498eed24bf85005f363c34771"}, "time": "2015-11-20T12:04:31+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "e3abefcd7f106677fd352cd7c187d6c969aa9ddc"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/e3abefcd7f106677fd352cd7c187d6c969aa9ddc", "type": "zip", "shasum": "", "reference": "e3abefcd7f106677fd352cd7c187d6c969aa9ddc"}, "time": "2015-11-07T22:20:37+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "d8093b631a31628342d0703764935f8bac2c56b1"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/d8093b631a31628342d0703764935f8bac2c56b1", "type": "zip", "shasum": "", "reference": "d8093b631a31628342d0703764935f8bac2c56b1"}, "time": "2015-10-05T15:07:09+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "95d662954e06000cdf63ec9c9f0a6c598d9c5eb9"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/95d662954e06000cdf63ec9c9f0a6c598d9c5eb9", "type": "zip", "shasum": "", "reference": "95d662954e06000cdf63ec9c9f0a6c598d9c5eb9"}, "time": "2015-07-19T19:57:13+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "96fbdc07635989c35c5a1912379f4c4b2ab15fd5"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/96fbdc07635989c35c5a1912379f4c4b2ab15fd5", "type": "zip", "shasum": "", "reference": "96fbdc07635989c35c5a1912379f4c4b2ab15fd5"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.3.0"}, "time": "2015-03-21T22:40:23+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "d93c485e71bcd22df0a994e9e3e03a3ef3a3e3f3"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/d93c485e71bcd22df0a994e9e3e03a3ef3a3e3f3", "type": "zip", "shasum": "", "reference": "d93c485e71bcd22df0a994e9e3e03a3ef3a3e3f3"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.2.1"}, "time": "2014-11-20T05:11:17+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "f0cb3bd7eddef40adcc1a1aaeaf569ceb3164532"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/f0cb3bd7eddef40adcc1a1aaeaf569ceb3164532", "type": "zip", "shasum": "", "reference": "f0cb3bd7eddef40adcc1a1aaeaf569ceb3164532"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.2.0"}, "time": "2014-08-29T00:01:20+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "804c17d20d938a1d3c425307189e65f49b53231e"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/804c17d20d938a1d3c425307189e65f49b53231e", "type": "zip", "shasum": "", "reference": "804c17d20d938a1d3c425307189e65f49b53231e"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.1.0"}, "time": "2014-03-21T12:53:08+00:00", "autoload": {"psr-0": {"DeepCopy": "src/", "DeepCopyTest": "tests/"}}, "require-dev": {"doctrine/collections": "1.*"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "1ee53258c4076953dc512e78db00adbd7e5b21cd"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1ee53258c4076953dc512e78db00adbd7e5b21cd", "type": "zip", "shasum": "", "reference": "1ee53258c4076953dc512e78db00adbd7e5b21cd"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.0.1"}, "time": "2014-02-14T08:59:38+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/DeepCopy.git", "type": "git", "reference": "2c8df9f6a975b8cb2d619a9722ded6f23a104294"}, "dist": {"url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/2c8df9f6a975b8cb2d619a9722ded6f23a104294", "type": "zip", "shasum": "", "reference": "2c8df9f6a975b8cb2d619a9722ded6f23a104294"}, "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.0.0"}, "time": "2013-10-01T10:30:23+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 05 Jul 2025 12:26:04 GMT"}