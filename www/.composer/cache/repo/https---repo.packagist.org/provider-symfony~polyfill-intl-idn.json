{"minified": "composer/2.0", "packages": {"symfony/polyfill-intl-idn": [{"name": "symfony/polyfill-intl-idn", "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "keywords": ["intl", "compatibility", "portable", "idn", "polyfill", "shim"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "type": "zip", "shasum": "", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}}, {"version": "v1.31.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "type": "zip", "shasum": "", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "type": "zip", "shasum": "", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a287ed7475f85bf6f61890146edbc932c0fff919", "type": "zip", "shasum": "", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "ecaafce9f77234a6a449d29e49267ba10499116d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/ecaafce9f77234a6a449d29e49267ba10499116d", "type": "zip", "shasum": "", "reference": "ecaafce9f77234a6a449d29e49267ba10499116d"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.28.0"}, "time": "2023-01-26T09:30:37+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/639084e360537a19f9ee352433b84ce831f3d2da", "type": "zip", "shasum": "", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "type": "zip", "shasum": "", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "749045c69efb97c70d25d7463abba812e91f3a44"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/749045c69efb97c70d25d7463abba812e91f3a44", "type": "zip", "shasum": "", "reference": "749045c69efb97c70d25d7463abba812e91f3a44"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.25.0"}, "time": "2021-09-14T14:02:44+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.24.0"}}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "65bd267525e82759e7d8c4e8ceea44f398838e65"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/65bd267525e82759e7d8c4e8ceea44f398838e65", "type": "zip", "shasum": "", "reference": "65bd267525e82759e7d8c4e8ceea44f398838e65"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.23.0"}, "time": "2021-05-27T09:27:20+00:00"}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/2d63434d922daf7da8dd863e7907e67ee3031483", "type": "zip", "shasum": "", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.22.1"}, "time": "2021-01-22T09:19:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "0eb8293dbbcd6ef6bf81404c9ce7d95bcdf34f44"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/0eb8293dbbcd6ef6bf81404c9ce7d95bcdf34f44", "type": "zip", "shasum": "", "reference": "0eb8293dbbcd6ef6bf81404c9ce7d95bcdf34f44"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.22.0"}, "time": "2021-01-07T16:49:33+00:00"}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "3b75acd829741c768bc8b1f84eb33265e7cc5117"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/3b75acd829741c768bc8b1f84eb33265e7cc5117", "type": "zip", "shasum": "", "reference": "3b75acd829741c768bc8b1f84eb33265e7cc5117"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "4ad5115c0f5d5172a9fe8147675ec6de266d8826"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/4ad5115c0f5d5172a9fe8147675ec6de266d8826", "type": "zip", "shasum": "", "reference": "4ad5115c0f5d5172a9fe8147675ec6de266d8826"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.19.0"}, "time": "2020-10-21T09:57:48+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=5.3.3", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php70": "^1.10", "symfony/polyfill-php72": "^1.10"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "5dcab1bc7146cf8c1beaa4502a3d9be344334251"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/5dcab1bc7146cf8c1beaa4502a3d9be344334251", "type": "zip", "shasum": "", "reference": "5dcab1bc7146cf8c1beaa4502a3d9be344334251"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.18.1"}, "time": "2020-08-04T06:02:08+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "bc6549d068d0160e0f10f7a5a23c7d1406b95ebe"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/bc6549d068d0160e0f10f7a5a23c7d1406b95ebe", "type": "zip", "shasum": "", "reference": "bc6549d068d0160e0f10f7a5a23c7d1406b95ebe"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/master"}, "time": "2020-07-14T12:35:20+00:00"}, {"version": "v1.17.1", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "a57f8161502549a742a63c09f0a604997bf47027"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a57f8161502549a742a63c09f0a604997bf47027", "type": "zip", "shasum": "", "reference": "a57f8161502549a742a63c09f0a604997bf47027"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.17.1"}, "time": "2020-06-06T08:46:27+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.10"}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/3bff59ea7047e925be6b7f2059d60af31bb46d6a", "type": "zip", "shasum": "", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/master"}, "time": "2020-05-12T16:47:27+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "ab0af41deab94ec8dceb3d1fb408bdd038eba4dc"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/ab0af41deab94ec8dceb3d1fb408bdd038eba4dc", "type": "zip", "shasum": "", "reference": "ab0af41deab94ec8dceb3d1fb408bdd038eba4dc"}, "time": "2020-05-08T16:50:20+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "47bd6aa45beb1cd7c6a16b7d1810133b728bdfcf"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/47bd6aa45beb1cd7c6a16b7d1810133b728bdfcf", "type": "zip", "shasum": "", "reference": "47bd6aa45beb1cd7c6a16b7d1810133b728bdfcf"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.15.0"}, "time": "2020-03-09T19:04:49+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "6842f1a39cf7d580655688069a03dd7cd83d244a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/6842f1a39cf7d580655688069a03dd7cd83d244a", "type": "zip", "shasum": "", "reference": "6842f1a39cf7d580655688069a03dd7cd83d244a"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/master"}, "time": "2020-01-17T12:01:36+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}, {"version": "v1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "6f9c239e61e1b0c9229a28ff89a812dc449c3d46"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/6f9c239e61e1b0c9229a28ff89a812dc449c3d46", "type": "zip", "shasum": "", "reference": "6f9c239e61e1b0c9229a28ff89a812dc449c3d46"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.13.0"}, "time": "2019-11-27T13:56:44+00:00", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.9"}}, {"version": "v1.13.1", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/master"}}, {"version": "v1.13.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.13.0"}}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "6af626ae6fa37d396dc90a399c0ff08e5cfc45b2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/6af626ae6fa37d396dc90a399c0ff08e5cfc45b2", "type": "zip", "shasum": "", "reference": "6af626ae6fa37d396dc90a399c0ff08e5cfc45b2"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.12.0"}, "time": "2019-08-06T08:03:45+00:00", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}}, {"version": "v1.11.0", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "c766e95bec706cdd89903b1eda8afab7d7a6b7af"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c766e95bec706cdd89903b1eda8afab7d7a6b7af", "type": "zip", "shasum": "", "reference": "c766e95bec706cdd89903b1eda8afab7d7a6b7af"}, "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/master"}, "time": "2019-03-04T13:44:35+00:00", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-idn.git", "type": "git", "reference": "89de1d44f2c059b266f22c9cc9124ddc4cd0987a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/89de1d44f2c059b266f22c9cc9124ddc4cd0987a", "type": "zip", "shasum": "", "reference": "89de1d44f2c059b266f22c9cc9124ddc4cd0987a"}, "time": "2018-09-30T16:36:12+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 12:53:54 GMT"}