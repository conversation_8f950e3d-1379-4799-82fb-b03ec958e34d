{"minified": "composer/2.0", "packages": {"composer/xdebug-handler": [{"name": "composer/xdebug-handler", "description": "Restarts a process without Xdebug.", "keywords": ["performance", "Xdebug"], "homepage": "", "version": "3.0.5", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "type": "zip", "shasum": "", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "type": "library", "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "require": {"php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3", "composer/pcre": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "4f988f8fdf580d53bdb2d1278fe93d1ed5462255"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/4f988f8fdf580d53bdb2d1278fe93d1ed5462255", "type": "zip", "shasum": "", "reference": "4f988f8fdf580d53bdb2d1278fe93d1ed5462255"}, "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.4"}, "time": "2024-03-26T18:29:49+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "type": "zip", "shasum": "", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "time": "2022-02-25T21:32:43+00:00", "require-dev": {"symfony/phpunit-bridge": "^6.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "b11b961ef6b11e380e81a20b27c8cad54eeefe61"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/b11b961ef6b11e380e81a20b27c8cad54eeefe61", "type": "zip", "shasum": "", "reference": "b11b961ef6b11e380e81a20b27c8cad54eeefe61"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.2"}, "time": "2022-02-24T20:53:32+00:00", "require": {"php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3", "composer/pcre": "^1"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "12f1b79476638a5615ed00ea6adbb269cec96fd8"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/12f1b79476638a5615ed00ea6adbb269cec96fd8", "type": "zip", "shasum": "", "reference": "12f1b79476638a5615ed00ea6adbb269cec96fd8"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.1"}, "time": "2022-01-04T18:29:42+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "ec6aa897c8b6cab05ebaeddade62bb6e4a69ef38"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ec6aa897c8b6cab05ebaeddade62bb6e4a69ef38", "type": "zip", "shasum": "", "reference": "ec6aa897c8b6cab05ebaeddade62bb6e4a69ef38"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.0"}, "time": "2021-12-23T21:03:16+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/9e36aeed4616366d2b690bdce11f71e9178c579a", "type": "zip", "shasum": "", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.5"}, "time": "2022-02-24T20:20:32+00:00", "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1 || ^2 || ^3", "composer/pcre": "^1"}, "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "0c1a3925ec58a4ec98e992b9c7d171e9e184be0a"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/0c1a3925ec58a4ec98e992b9c7d171e9e184be0a", "type": "zip", "shasum": "", "reference": "0c1a3925ec58a4ec98e992b9c7d171e9e184be0a"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.4"}, "time": "2022-01-04T17:06:45+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "6555461e76962fd0379c444c46fd558a0fcfb65e"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6555461e76962fd0379c444c46fd558a0fcfb65e", "type": "zip", "shasum": "", "reference": "6555461e76962fd0379c444c46fd558a0fcfb65e"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.3"}, "time": "2021-12-08T13:07:32+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "84674dd3a7575ba617f5a76d7e9e29a7d3891339"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/84674dd3a7575ba617f5a76d7e9e29a7d3891339", "type": "zip", "shasum": "", "reference": "84674dd3a7575ba617f5a76d7e9e29a7d3891339"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.2"}, "time": "2021-07-31T17:03:58+00:00", "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^0.12.55"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "964adcdd3a28bf9ed5d9ac6450064e0d71ed7496"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/964adcdd3a28bf9ed5d9ac6450064e0d71ed7496", "type": "zip", "shasum": "", "reference": "964adcdd3a28bf9ed5d9ac6450064e0d71ed7496"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.1"}, "time": "2021-05-05T19:37:51+00:00", "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "31d57697eb1971712a08031cfaff5a846d10bdf5"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/31d57697eb1971712a08031cfaff5a846d10bdf5", "type": "zip", "shasum": "", "reference": "31d57697eb1971712a08031cfaff5a846d10bdf5"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.0"}, "time": "2021-04-09T19:40:06+00:00"}, {"version": "1.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "f27e06cd9675801df441b3656569b328e04aa37c"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/f27e06cd9675801df441b3656569b328e04aa37c", "type": "zip", "shasum": "", "reference": "f27e06cd9675801df441b3656569b328e04aa37c"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.6"}, "time": "2021-03-25T17:01:18+00:00"}, {"version": "1.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "f28d44c286812c714741478d968104c5e604a1d4"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/f28d44c286812c714741478d968104c5e604a1d4", "type": "zip", "shasum": "", "reference": "f28d44c286812c714741478d968104c5e604a1d4"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.5"}, "time": "2020-11-13T08:04:11+00:00", "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "6e076a124f7ee146f2487554a94b6a19a74887ba"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6e076a124f7ee146f2487554a94b6a19a74887ba", "type": "zip", "shasum": "", "reference": "6e076a124f7ee146f2487554a94b6a19a74887ba"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.4"}, "time": "2020-10-24T12:39:10+00:00"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "ebd27a9866ae8254e873866f795491f02418c5a5"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ebd27a9866ae8254e873866f795491f02418c5a5", "type": "zip", "shasum": "", "reference": "ebd27a9866ae8254e873866f795491f02418c5a5"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.3"}, "time": "2020-08-19T10:27:58+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51", "type": "zip", "shasum": "", "reference": "fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.2"}, "time": "2020-06-04T11:16:35+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "1ab9842d69e64fb3a01be6b656501032d1b78cb7"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/1ab9842d69e64fb3a01be6b656501032d1b78cb7", "type": "zip", "shasum": "", "reference": "1ab9842d69e64fb3a01be6b656501032d1b78cb7"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/master"}, "funding": [{"url": "https://packagist.com", "type": "custom"}], "time": "2020-03-01T12:26:26+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "cbe23383749496fe0f373345208b79568e4bc248"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/cbe23383749496fe0f373345208b79568e4bc248", "type": "zip", "shasum": "", "reference": "cbe23383749496fe0f373345208b79568e4bc248"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.0"}, "time": "2019-11-06T16:40:04+00:00", "funding": "__unset"}, {"description": "Restarts a process without xdebug.", "version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "46867cbf8ca9fb8d60c506895449eb799db1184f"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/46867cbf8ca9fb8d60c506895449eb799db1184f", "type": "zip", "shasum": "", "reference": "46867cbf8ca9fb8d60c506895449eb799db1184f"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/master"}, "time": "2019-05-27T17:52:04+00:00", "require": {"php": "^5.3.2 || ^7.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5"}}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "d17708133b6c276d6e42ef887a877866b909d892"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/d17708133b6c276d6e42ef887a877866b909d892", "type": "zip", "shasum": "", "reference": "d17708133b6c276d6e42ef887a877866b909d892"}, "time": "2019-01-28T20:25:53+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "dc523135366eb68f22268d069ea7749486458562"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/dc523135366eb68f22268d069ea7749486458562", "type": "zip", "shasum": "", "reference": "dc523135366eb68f22268d069ea7749486458562"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.3.1"}, "time": "2018-11-29T10:59:02+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "b8e9745fb9b06ea6664d8872c4505fb16df4611c"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/b8e9745fb9b06ea6664d8872c4505fb16df4611c", "type": "zip", "shasum": "", "reference": "b8e9745fb9b06ea6664d8872c4505fb16df4611c"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/master"}, "time": "2018-08-31T19:07:57+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "e37cbd80da64afe314c72de8d2d2fec0e40d9373"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/e37cbd80da64afe314c72de8d2d2fec0e40d9373", "type": "zip", "shasum": "", "reference": "e37cbd80da64afe314c72de8d2d2fec0e40d9373"}, "time": "2018-08-23T12:00:19+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "e1809da56ce1bd1b547a752936817341ac244d8e"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/e1809da56ce1bd1b547a752936817341ac244d8e", "type": "zip", "shasum": "", "reference": "e1809da56ce1bd1b547a752936817341ac244d8e"}, "time": "2018-08-16T10:54:23+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "c919dc6c62e221fc6406f861ea13433c0aa24f08"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/c919dc6c62e221fc6406f861ea13433c0aa24f08", "type": "zip", "shasum": "", "reference": "c919dc6c62e221fc6406f861ea13433c0aa24f08"}, "time": "2018-04-11T15:42:36+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/xdebug-handler.git", "type": "git", "reference": "1852e549ad0d6f2910f89c27fec833dda1b25b4a"}, "dist": {"url": "https://api.github.com/repos/composer/xdebug-handler/zipball/1852e549ad0d6f2910f89c27fec833dda1b25b4a", "type": "zip", "shasum": "", "reference": "1852e549ad0d6f2910f89c27fec833dda1b25b4a"}, "time": "2018-03-08T13:09:50+00:00"}]}, "security-advisories": [], "last-modified": "Mon, 06 May 2024 17:01:43 GMT"}