{"minified": "composer/2.0", "packages": {"symfony/finder": [{"name": "symfony/finder", "description": "Finds files and directories via an intuitive fluent interface", "keywords": [], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d", "type": "zip", "shasum": "", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "type": "library", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:26+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0-BETA1"}}, {"version": "v7.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "type": "zip", "shasum": "", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "time": "2024-12-30T19:00:17+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6de263e5868b9a137602dd1e33e4d48bfae99c49"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6de263e5868b9a137602dd1e33e4d48bfae99c49", "type": "zip", "shasum": "", "reference": "6de263e5868b9a137602dd1e33e4d48bfae99c49"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.2.0"}, "time": "2024-10-23T06:56:12+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.0-BETA1"}}, {"version": "v7.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b8b526e051ac0b33feabbec7893adcab96b23bf3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b8b526e051ac0b33feabbec7893adcab96b23bf3", "type": "zip", "shasum": "", "reference": "b8b526e051ac0b33feabbec7893adcab96b23bf3"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.1.10"}, "time": "2024-12-30T18:59:46+00:00"}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2cb89664897be33f78c65d3d2845954c8d7a43b8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2cb89664897be33f78c65d3d2845954c8d7a43b8", "type": "zip", "shasum": "", "reference": "2cb89664897be33f78c65d3d2845954c8d7a43b8"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.1.6"}, "time": "2024-10-01T08:31:23+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d95bbf319f7d052082fb7af147e0f835a695e823"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d95bbf319f7d052082fb7af147e0f835a695e823", "type": "zip", "shasum": "", "reference": "d95bbf319f7d052082fb7af147e0f835a695e823"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.1.4"}, "time": "2024-08-13T14:28:19+00:00"}, {"version": "v7.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "717c6329886f32dc65e27461f80f2a465412fdca"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/717c6329886f32dc65e27461f80f2a465412fdca", "type": "zip", "shasum": "", "reference": "717c6329886f32dc65e27461f80f2a465412fdca"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.1.3"}, "time": "2024-07-24T07:08:44+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fbb0ba67688b780efbc886c1a0a0948dcf7205d6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fbb0ba67688b780efbc886c1a0a0948dcf7205d6", "type": "zip", "shasum": "", "reference": "fbb0ba67688b780efbc886c1a0a0948dcf7205d6"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fb6c2d65c3dbf7ad83201a4168d4510c8dddaac7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fb6c2d65c3dbf7ad83201a4168d4510c8dddaac7", "type": "zip", "shasum": "", "reference": "fb6c2d65c3dbf7ad83201a4168d4510c8dddaac7"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.1.0"}, "time": "2024-04-28T18:29:00+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v7.1.0-BETA1"}}, {"version": "v7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "25b267662f297a8479bf6cf88fdc92e4b16cf24c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/25b267662f297a8479bf6cf88fdc92e4b16cf24c", "type": "zip", "shasum": "", "reference": "25b267662f297a8479bf6cf88fdc92e4b16cf24c"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.0.10"}, "time": "2024-07-24T07:06:56+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f7f198c76b0b27d6b9d1684dd03d6e6050e35bfb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f7f198c76b0b27d6b9d1684dd03d6e6050e35bfb", "type": "zip", "shasum": "", "reference": "f7f198c76b0b27d6b9d1684dd03d6e6050e35bfb"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4d58f0f4fe95a30d7b538d71197135483560b97c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4d58f0f4fe95a30d7b538d71197135483560b97c", "type": "zip", "shasum": "", "reference": "4d58f0f4fe95a30d7b538d71197135483560b97c"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.0.7"}, "time": "2024-04-28T11:44:19+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6e5688d69f7cfc4ed4a511e96007e06c2d34ce56"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6e5688d69f7cfc4ed4a511e96007e06c2d34ce56", "type": "zip", "shasum": "", "reference": "6e5688d69f7cfc4ed4a511e96007e06c2d34ce56"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.0.0"}, "time": "2023-10-31T17:59:56+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/finder/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "eb22b4ca7f23326df8c552cf87e9c464741ebffc"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/eb22b4ca7f23326df8c552cf87e9c464741ebffc", "type": "zip", "shasum": "", "reference": "eb22b4ca7f23326df8c552cf87e9c464741ebffc"}, "support": {"source": "https://github.com/symfony/finder/tree/v7.0.0-BETA1"}, "time": "2023-10-12T15:41:36+00:00"}, {"version": "v6.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "type": "zip", "shasum": "", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "time": "2024-12-29T13:51:37+00:00", "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "daea9eca0b08d0ed1dc9ab702a46128fd1be4958"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/daea9eca0b08d0ed1dc9ab702a46128fd1be4958", "type": "zip", "shasum": "", "reference": "daea9eca0b08d0ed1dc9ab702a46128fd1be4958"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.13"}, "time": "2024-10-01T08:30:56+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d7eb6daf8cd7e9ac4976e9576b32042ef7253453"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d7eb6daf8cd7e9ac4976e9576b32042ef7253453", "type": "zip", "shasum": "", "reference": "d7eb6daf8cd7e9ac4976e9576b32042ef7253453"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.11"}, "time": "2024-08-13T14:27:37+00:00"}, {"version": "v6.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "af29198d87112bebdd397bd7735fbd115997824c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/af29198d87112bebdd397bd7735fbd115997824c", "type": "zip", "shasum": "", "reference": "af29198d87112bebdd397bd7735fbd115997824c"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.10"}, "time": "2024-07-24T07:06:38+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3ef977a43883215d560a2cecb82ec8e62131471c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3ef977a43883215d560a2cecb82ec8e62131471c", "type": "zip", "shasum": "", "reference": "3ef977a43883215d560a2cecb82ec8e62131471c"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "511c48990be17358c23bf45c5d71ab85d40fb764"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/511c48990be17358c23bf45c5d71ab85d40fb764", "type": "zip", "shasum": "", "reference": "511c48990be17358c23bf45c5d71ab85d40fb764"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.7"}, "time": "2024-04-23T10:36:43+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "11d736e97f116ac375a81f96e662911a34cd50ce"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/11d736e97f116ac375a81f96e662911a34cd50ce", "type": "zip", "shasum": "", "reference": "11d736e97f116ac375a81f96e662911a34cd50ce"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.0"}, "time": "2023-10-31T17:30:12+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c0b454c593bc9dd3e23592eb899890b6e61292f0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c0b454c593bc9dd3e23592eb899890b6e61292f0", "type": "zip", "shasum": "", "reference": "c0b454c593bc9dd3e23592eb899890b6e61292f0"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.4.0-BETA1"}, "time": "2023-10-11T14:01:18+00:00"}, {"version": "v6.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a1b31d88c0e998168ca7792f222cbecee47428c4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a1b31d88c0e998168ca7792f222cbecee47428c4", "type": "zip", "shasum": "", "reference": "a1b31d88c0e998168ca7792f222cbecee47428c4"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.3.5"}, "time": "2023-09-26T12:56:25+00:00", "require-dev": {"symfony/filesystem": "^6.0"}}, {"version": "v6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9915db259f67d21eefee768c1abcf1cc61b1fc9e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9915db259f67d21eefee768c1abcf1cc61b1fc9e", "type": "zip", "shasum": "", "reference": "9915db259f67d21eefee768c1abcf1cc61b1fc9e"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.3.3"}, "time": "2023-07-31T08:31:44+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "78ce4c29757d657d2b41a91c328923b9a0d6b43d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/78ce4c29757d657d2b41a91c328923b9a0d6b43d", "type": "zip", "shasum": "", "reference": "78ce4c29757d657d2b41a91c328923b9a0d6b43d"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.3.2"}, "time": "2023-07-13T14:29:38+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d9b01ba073c44cef617c7907ce2419f8d00d75e2"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d9b01ba073c44cef617c7907ce2419f8d00d75e2", "type": "zip", "shasum": "", "reference": "d9b01ba073c44cef617c7907ce2419f8d00d75e2"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.3.0"}, "time": "2023-04-02T01:25:41+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v6.3.0-BETA1"}}, {"version": "v6.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8ccb900489183bd5ec3d04f92e28ee0c0af543dd"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8ccb900489183bd5ec3d04f92e28ee0c0af543dd", "type": "zip", "shasum": "", "reference": "8ccb900489183bd5ec3d04f92e28ee0c0af543dd"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.2.14"}, "time": "2023-07-31T10:27:17+00:00"}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a44e6768844c75bb47d01a90078c22cb13ac45e0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a44e6768844c75bb47d01a90078c22cb13ac45e0", "type": "zip", "shasum": "", "reference": "a44e6768844c75bb47d01a90078c22cb13ac45e0"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.2.13"}, "time": "2023-07-13T14:28:09+00:00"}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "20808dc6631aecafbe67c186af5dcb370be3a0eb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/20808dc6631aecafbe67c186af5dcb370be3a0eb", "type": "zip", "shasum": "", "reference": "20808dc6631aecafbe67c186af5dcb370be3a0eb"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.2.7"}, "time": "2023-02-16T09:57:23+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c90dc446976a612e3312a97a6ec0069ab0c2099c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c90dc446976a612e3312a97a6ec0069ab0c2099c", "type": "zip", "shasum": "", "reference": "c90dc446976a612e3312a97a6ec0069ab0c2099c"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.2.5"}, "time": "2023-01-20T17:45:48+00:00"}, {"version": "v6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "81eefbddfde282ee33b437ba5e13d7753211ae8e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/81eefbddfde282ee33b437ba5e13d7753211ae8e", "type": "zip", "shasum": "", "reference": "81eefbddfde282ee33b437ba5e13d7753211ae8e"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.2.3"}, "time": "2022-12-22T17:55:15+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "eb2355f69519e4ef33f1835bca4c935f5d42e570"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/eb2355f69519e4ef33f1835bca4c935f5d42e570", "type": "zip", "shasum": "", "reference": "eb2355f69519e4ef33f1835bca4c935f5d42e570"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.2.0"}, "time": "2022-10-09T08:55:40+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v6.2.0-BETA1"}}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1efd83623550a69f410f330f4eef027a188a29a7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1efd83623550a69f410f330f4eef027a188a29a7", "type": "zip", "shasum": "", "reference": "1efd83623550a69f410f330f4eef027a188a29a7"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.1.11"}, "time": "2023-01-20T17:44:30+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e854440dd5d9020cf098e59742a68149b3dfdf1e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e854440dd5d9020cf098e59742a68149b3dfdf1e", "type": "zip", "shasum": "", "reference": "e854440dd5d9020cf098e59742a68149b3dfdf1e"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.1.9"}, "time": "2022-12-22T17:54:54+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "39696bff2c2970b3779a5cac7bf9f0b88fc2b709"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/39696bff2c2970b3779a5cac7bf9f0b88fc2b709", "type": "zip", "shasum": "", "reference": "39696bff2c2970b3779a5cac7bf9f0b88fc2b709"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.1.3"}, "time": "2022-07-29T07:42:06+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "45b8beb69d6eb3b05a65689ebfd4222326773f8f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/45b8beb69d6eb3b05a65689ebfd4222326773f8f", "type": "zip", "shasum": "", "reference": "45b8beb69d6eb3b05a65689ebfd4222326773f8f"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.1.0"}, "time": "2022-04-15T08:08:08+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v6.1.0-BETA1"}}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5cc9cac6586fc0c28cd173780ca696e419fefa11"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5cc9cac6586fc0c28cd173780ca696e419fefa11", "type": "zip", "shasum": "", "reference": "5cc9cac6586fc0c28cd173780ca696e419fefa11"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.19"}, "time": "2023-01-20T17:44:14+00:00", "require": {"php": ">=8.0.2"}, "require-dev": "__unset"}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d467d625fc88f7cebf96f495e588a7196a669db1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d467d625fc88f7cebf96f495e588a7196a669db1", "type": "zip", "shasum": "", "reference": "d467d625fc88f7cebf96f495e588a7196a669db1"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.17"}, "time": "2022-12-22T17:53:58+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "09cb683ba5720385ea6966e5e06be2a34f2568b1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/09cb683ba5720385ea6966e5e06be2a34f2568b1", "type": "zip", "shasum": "", "reference": "09cb683ba5720385ea6966e5e06be2a34f2568b1"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.11"}, "time": "2022-07-29T07:39:48+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "af7edab28d17caecd1f40a9219fc646ae751c21f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/af7edab28d17caecd1f40a9219fc646ae751c21f", "type": "zip", "shasum": "", "reference": "af7edab28d17caecd1f40a9219fc646ae751c21f"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.8"}, "time": "2022-04-15T08:07:58+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8661b74dbabc23223f38c9b99d3f8ade71170430"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8661b74dbabc23223f38c9b99d3f8ade71170430", "type": "zip", "shasum": "", "reference": "8661b74dbabc23223f38c9b99d3f8ade71170430"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.3"}, "time": "2022-01-26T17:23:29+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "03d2833e677d48317cac852f9c0287fb048c3c5c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/03d2833e677d48317cac852f9c0287fb048c3c5c", "type": "zip", "shasum": "", "reference": "03d2833e677d48317cac852f9c0287fb048c3c5c"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.2"}, "time": "2021-12-20T16:21:45+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "07debda41a4d32d33e59e6ab302af1701e15f173"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/07debda41a4d32d33e59e6ab302af1701e15f173", "type": "zip", "shasum": "", "reference": "07debda41a4d32d33e59e6ab302af1701e15f173"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.0"}, "time": "2021-11-28T15:34:37+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3ec4621949b9ad48d936c5d5f44e9204294d50a3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3ec4621949b9ad48d936c5d5f44e9204294d50a3", "type": "zip", "shasum": "", "reference": "3ec4621949b9ad48d936c5d5f44e9204294d50a3"}, "support": {"source": "https://github.com/symfony/finder/tree/v6.0.0-RC1"}, "time": "2021-11-04T17:14:40+00:00"}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v6.0.0-BETA1"}}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "63741784cd7b9967975eec610b256eed3ede022b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/63741784cd7b9967975eec610b256eed3ede022b", "type": "zip", "shasum": "", "reference": "63741784cd7b9967975eec610b256eed3ede022b"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.45"}, "time": "2024-09-28T13:32:08+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ae25a9145a900764158d439653d5630191155ca0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ae25a9145a900764158d439653d5630191155ca0", "type": "zip", "shasum": "", "reference": "ae25a9145a900764158d439653d5630191155ca0"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.43"}, "time": "2024-08-13T14:03:51+00:00"}, {"version": "v5.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0724c51fa067b198e36506d2864e09a52180998a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0724c51fa067b198e36506d2864e09a52180998a", "type": "zip", "shasum": "", "reference": "0724c51fa067b198e36506d2864e09a52180998a"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.42"}, "time": "2024-07-22T08:53:29+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f51cff4687547641c7d8180d74932ab40b2205ce"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f51cff4687547641c7d8180d74932ab40b2205ce", "type": "zip", "shasum": "", "reference": "f51cff4687547641c7d8180d74932ab40b2205ce"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f6a96e4fcd468a25fede16ee665f50ced856bd0a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f6a96e4fcd468a25fede16ee665f50ced856bd0a", "type": "zip", "shasum": "", "reference": "f6a96e4fcd468a25fede16ee665f50ced856bd0a"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "abe6d6f77d9465fed3cd2d029b29d03b56b56435"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/abe6d6f77d9465fed3cd2d029b29d03b56b56435", "type": "zip", "shasum": "", "reference": "abe6d6f77d9465fed3cd2d029b29d03b56b56435"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.35"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v5.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ff4bce3c33451e7ec778070e45bd23f74214cd5d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ff4bce3c33451e7ec778070e45bd23f74214cd5d", "type": "zip", "shasum": "", "reference": "ff4bce3c33451e7ec778070e45bd23f74214cd5d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.27"}, "time": "2023-07-31T08:02:31+00:00"}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "01ef6b2391a59bb4c5d3c59e2ef9b99c7d335d17"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/01ef6b2391a59bb4c5d3c59e2ef9b99c7d335d17", "type": "zip", "shasum": "", "reference": "01ef6b2391a59bb4c5d3c59e2ef9b99c7d335d17"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.26"}, "time": "2023-07-09T13:55:15+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "078e9a5e1871fcfe6a5ce421b539344c21afef19"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/078e9a5e1871fcfe6a5ce421b539344c21afef19", "type": "zip", "shasum": "", "reference": "078e9a5e1871fcfe6a5ce421b539344c21afef19"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.21"}, "time": "2023-02-16T09:33:00+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6071aebf810ad13fe8200c224f36103abb37cf1f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6071aebf810ad13fe8200c224f36103abb37cf1f", "type": "zip", "shasum": "", "reference": "6071aebf810ad13fe8200c224f36103abb37cf1f"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.19"}, "time": "2023-01-14T19:14:44+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "40c08632019838dfb3350f18cf5563b8080055fc"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/40c08632019838dfb3350f18cf5563b8080055fc", "type": "zip", "shasum": "", "reference": "40c08632019838dfb3350f18cf5563b8080055fc"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.17"}, "time": "2022-12-22T10:31:03+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7872a66f57caffa2916a584db1aa7f12adc76f8c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7872a66f57caffa2916a584db1aa7f12adc76f8c", "type": "zip", "shasum": "", "reference": "7872a66f57caffa2916a584db1aa7f12adc76f8c"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.11"}, "time": "2022-07-29T07:37:50+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9b630f3427f3ebe7cd346c277a1408b00249dad9"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9b630f3427f3ebe7cd346c277a1408b00249dad9", "type": "zip", "shasum": "", "reference": "9b630f3427f3ebe7cd346c277a1408b00249dad9"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.8"}, "time": "2022-04-15T08:07:45+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "231313534dded84c7ecaa79d14bc5da4ccb69b7d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/231313534dded84c7ecaa79d14bc5da4ccb69b7d", "type": "zip", "shasum": "", "reference": "231313534dded84c7ecaa79d14bc5da4ccb69b7d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.3"}, "time": "2022-01-26T16:34:36+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e77046c252be48c48a40816187ed527703c8f76c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e77046c252be48c48a40816187ed527703c8f76c", "type": "zip", "shasum": "", "reference": "e77046c252be48c48a40816187ed527703c8f76c"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.2"}, "time": "2021-12-15T11:06:13+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d2f29dac98e96a98be467627bd49c2efb1bc2590"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d2f29dac98e96a98be467627bd49c2efb1bc2590", "type": "zip", "shasum": "", "reference": "d2f29dac98e96a98be467627bd49c2efb1bc2590"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.0"}, "time": "2021-11-28T15:25:38+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "76673e9f82bb6c2fb3b6e9b1673832edb55eee1a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/76673e9f82bb6c2fb3b6e9b1673832edb55eee1a", "type": "zip", "shasum": "", "reference": "76673e9f82bb6c2fb3b6e9b1673832edb55eee1a"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.4.0-RC1"}, "time": "2021-11-03T13:16:59+00:00"}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.0-BETA1"}}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5ab1855ebe36c381ccde572f110f3280f88babf5"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5ab1855ebe36c381ccde572f110f3280f88babf5", "type": "zip", "shasum": "", "reference": "5ab1855ebe36c381ccde572f110f3280f88babf5"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "35f531cc9bec5c5967bc2bd639e115158c7d190a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/35f531cc9bec5c5967bc2bd639e115158c7d190a", "type": "zip", "shasum": "", "reference": "35f531cc9bec5c5967bc2bd639e115158c7d190a"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.13"}, "time": "2021-12-15T10:43:37+00:00"}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a10000ada1e600d109a6c7632e9ac42e8bf2fb93"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a10000ada1e600d109a6c7632e9ac42e8bf2fb93", "type": "zip", "shasum": "", "reference": "a10000ada1e600d109a6c7632e9ac42e8bf2fb93"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.7"}, "time": "2021-08-04T21:20:46+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "17f50e06018baec41551a71a15731287dbaab186"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/17f50e06018baec41551a71a15731287dbaab186", "type": "zip", "shasum": "", "reference": "17f50e06018baec41551a71a15731287dbaab186"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.4"}, "time": "2021-07-23T15:54:19+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0ae3f047bed4edff6fd35b26a9a6bfdc92c953c6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0ae3f047bed4edff6fd35b26a9a6bfdc92c953c6", "type": "zip", "shasum": "", "reference": "0ae3f047bed4edff6fd35b26a9a6bfdc92c953c6"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.0"}, "time": "2021-05-26T12:52:38+00:00", "require": {"php": ">=7.2.5"}}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ccccb9d48ca42757dd12f2ca4bf857a4e217d90d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ccccb9d48ca42757dd12f2ca4bf857a4e217d90d", "type": "zip", "shasum": "", "reference": "ccccb9d48ca42757dd12f2ca4bf857a4e217d90d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.0-RC1"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.3.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "eccb8be70d7a6a2230d05f6ecede40f3fdd9e252"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/eccb8be70d7a6a2230d05f6ecede40f3fdd9e252", "type": "zip", "shasum": "", "reference": "eccb8be70d7a6a2230d05f6ecede40f3fdd9e252"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.0-BETA4"}, "time": "2021-05-10T14:39:23+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0d639a0943822626290d169965804f79400e6a04"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0d639a0943822626290d169965804f79400e6a04", "type": "zip", "shasum": "", "reference": "0d639a0943822626290d169965804f79400e6a04"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.0-BETA1"}, "time": "2021-02-15T18:55:04+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "17f50e06018baec41551a71a15731287dbaab186"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/17f50e06018baec41551a71a15731287dbaab186", "type": "zip", "shasum": "", "reference": "17f50e06018baec41551a71a15731287dbaab186"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.12"}, "time": "2021-07-23T15:54:19+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0ae3f047bed4edff6fd35b26a9a6bfdc92c953c6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0ae3f047bed4edff6fd35b26a9a6bfdc92c953c6", "type": "zip", "shasum": "", "reference": "0ae3f047bed4edff6fd35b26a9a6bfdc92c953c6"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.3.0"}, "time": "2021-05-26T12:52:38+00:00", "require": {"php": ">=7.2.5"}}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ccccb9d48ca42757dd12f2ca4bf857a4e217d90d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ccccb9d48ca42757dd12f2ca4bf857a4e217d90d", "type": "zip", "shasum": "", "reference": "ccccb9d48ca42757dd12f2ca4bf857a4e217d90d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.9"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "eccb8be70d7a6a2230d05f6ecede40f3fdd9e252"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/eccb8be70d7a6a2230d05f6ecede40f3fdd9e252", "type": "zip", "shasum": "", "reference": "eccb8be70d7a6a2230d05f6ecede40f3fdd9e252"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.8"}, "time": "2021-05-10T14:39:23+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0d639a0943822626290d169965804f79400e6a04"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0d639a0943822626290d169965804f79400e6a04", "type": "zip", "shasum": "", "reference": "0d639a0943822626290d169965804f79400e6a04"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.4"}, "time": "2021-02-15T18:55:04+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4adc8d172d602008c204c2e16956f99257248e03"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4adc8d172d602008c204c2e16956f99257248e03", "type": "zip", "shasum": "", "reference": "4adc8d172d602008c204c2e16956f99257248e03"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.3"}, "time": "2021-01-28T22:06:19+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "196f45723b5e618bf0e23b97e96d11652696ea9e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/196f45723b5e618bf0e23b97e96d11652696ea9e", "type": "zip", "shasum": "", "reference": "196f45723b5e618bf0e23b97e96d11652696ea9e"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.2"}, "time": "2021-01-27T10:01:46+00:00"}, {"description": "Symfony Finder Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0b9231a5922fd7287ba5b411893c0ecd2733e5ba"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0b9231a5922fd7287ba5b411893c0ecd2733e5ba", "type": "zip", "shasum": "", "reference": "0b9231a5922fd7287ba5b411893c0ecd2733e5ba"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.1"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fd8305521692f27eae3263895d1ef1571c71a78d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fd8305521692f27eae3263895d1ef1571c71a78d", "type": "zip", "shasum": "", "reference": "fd8305521692f27eae3263895d1ef1571c71a78d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.0"}, "time": "2020-11-18T09:42:36+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/finder/tree/v5.2.0-RC2"}}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "19de51f4fd1cb88ba4d8d9a8c39d79cca7f11577"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/19de51f4fd1cb88ba4d8d9a8c39d79cca7f11577", "type": "zip", "shasum": "", "reference": "19de51f4fd1cb88ba4d8d9a8c39d79cca7f11577"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.0-RC1"}, "time": "2020-11-02T07:55:38+00:00"}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e70eb5a69c2ff61ea135a13d2266e8914a67b3a0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e70eb5a69c2ff61ea135a13d2266e8914a67b3a0", "type": "zip", "shasum": "", "reference": "e70eb5a69c2ff61ea135a13d2266e8914a67b3a0"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "28f544a11024209fae130737ebee1de3affd2d11"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/28f544a11024209fae130737ebee1de3affd2d11", "type": "zip", "shasum": "", "reference": "28f544a11024209fae130737ebee1de3affd2d11"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b5f7c0d069d8517c994f1dc0e9c100257d7effd9"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b5f7c0d069d8517c994f1dc0e9c100257d7effd9", "type": "zip", "shasum": "", "reference": "b5f7c0d069d8517c994f1dc0e9c100257d7effd9"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2020-09-02T16:27:44+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Finds files and directories via an intuitive fluent interface", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "196f45723b5e618bf0e23b97e96d11652696ea9e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/196f45723b5e618bf0e23b97e96d11652696ea9e", "type": "zip", "shasum": "", "reference": "196f45723b5e618bf0e23b97e96d11652696ea9e"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "extra": "__unset"}, {"description": "Symfony Finder Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0b9231a5922fd7287ba5b411893c0ecd2733e5ba"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0b9231a5922fd7287ba5b411893c0ecd2733e5ba", "type": "zip", "shasum": "", "reference": "0b9231a5922fd7287ba5b411893c0ecd2733e5ba"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.1.10"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fd8305521692f27eae3263895d1ef1571c71a78d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fd8305521692f27eae3263895d1ef1571c71a78d", "type": "zip", "shasum": "", "reference": "fd8305521692f27eae3263895d1ef1571c71a78d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.2.0-RC2"}, "time": "2020-11-18T09:42:36+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e70eb5a69c2ff61ea135a13d2266e8914a67b3a0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e70eb5a69c2ff61ea135a13d2266e8914a67b3a0", "type": "zip", "shasum": "", "reference": "e70eb5a69c2ff61ea135a13d2266e8914a67b3a0"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2c3ba7ad6884e6c4451ce2340e2dc23f6fa3e0d8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2c3ba7ad6884e6c4451ce2340e2dc23f6fa3e0d8", "type": "zip", "shasum": "", "reference": "2c3ba7ad6884e6c4451ce2340e2dc23f6fa3e0d8"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.1.7"}, "time": "2020-09-02T16:23:27+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/5.1"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2b765f0cf6612b3636e738c0689b29aa63088d5d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2b765f0cf6612b3636e738c0689b29aa63088d5d", "type": "zip", "shasum": "", "reference": "2b765f0cf6612b3636e738c0689b29aa63088d5d"}, "time": "2020-08-17T10:01:29+00:00"}, {"version": "v5.1.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v5.1.4"}}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4298870062bfc667cb78d2b379be4bf5dec5f187"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4298870062bfc667cb78d2b379be4bf5dec5f187", "type": "zip", "shasum": "", "reference": "4298870062bfc667cb78d2b379be4bf5dec5f187"}, "support": {"source": "https://github.com/symfony/finder/tree/5.1"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.2", "version_normalized": "*******"}, {"version": "v5.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v5.1.0"}}, {"version": "v5.1.0", "version_normalized": "*******"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/finder/tree/5.1"}}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "53c70235d130f5df22541fb674e899e2004aa5ed"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/53c70235d130f5df22541fb674e899e2004aa5ed", "type": "zip", "shasum": "", "reference": "53c70235d130f5df22541fb674e899e2004aa5ed"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2020-03-30T11:43:41+00:00", "require": {"php": "^7.2.5"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "127bccabf3c854625af9c0162779cf06bc1dd352"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/127bccabf3c854625af9c0162779cf06bc1dd352", "type": "zip", "shasum": "", "reference": "127bccabf3c854625af9c0162779cf06bc1dd352"}, "support": {"source": "https://github.com/symfony/finder/tree/5.0"}, "time": "2020-05-20T17:38:26+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5"}}, {"version": "v5.0.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/v5.0.9"}}, {"version": "v5.0.9", "version_normalized": "*******"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "600a52c29afc0d1caa74acbec8d3095ca7e9910d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/600a52c29afc0d1caa74acbec8d3095ca7e9910d", "type": "zip", "shasum": "", "reference": "600a52c29afc0d1caa74acbec8d3095ca7e9910d"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.0.8"}, "time": "2020-03-27T16:56:45+00:00", "require": {"php": "^7.2.5"}}, {"version": "v5.0.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/5.0"}}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6251f201187ca9d66f6b099d3de65d279e971138"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6251f201187ca9d66f6b099d3de65d279e971138", "type": "zip", "shasum": "", "reference": "6251f201187ca9d66f6b099d3de65d279e971138"}, "funding": [], "time": "2020-02-14T07:43:07+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v5.0.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}]}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4176e7cb846fe08f32518b7e0ed8462e2db8d9bb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4176e7cb846fe08f32518b7e0ed8462e2db8d9bb", "type": "zip", "shasum": "", "reference": "4176e7cb846fe08f32518b7e0ed8462e2db8d9bb"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.0.4"}, "time": "2020-01-04T14:08:26+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/5.0"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "17874dd8ab9a19422028ad56172fb294287a701b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/17874dd8ab9a19422028ad56172fb294287a701b", "type": "zip", "shasum": "", "reference": "17874dd8ab9a19422028ad56172fb294287a701b"}, "support": {"source": "https://github.com/symfony/finder/tree/v5.0.2"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/5.0"}}, {"version": "v5.0.0", "version_normalized": "*******"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "28ffdc19adf3fac36a508d02b036d58c4d210e0f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/28ffdc19adf3fac36a508d02b036d58c4d210e0f", "type": "zip", "shasum": "", "reference": "28ffdc19adf3fac36a508d02b036d58c4d210e0f"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2019-11-16T15:24:47+00:00", "require": {"php": "^7.2.9"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "bd866ffc62322350897dac99d1cf96b1b5879700"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/bd866ffc62322350897dac99d1cf96b1b5879700", "type": "zip", "shasum": "", "reference": "bd866ffc62322350897dac99d1cf96b1b5879700"}, "time": "2019-10-30T13:09:48+00:00"}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1"}, {"description": "Finds files and directories via an intuitive fluent interface", "version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/66bd787edb5e42ff59d3523f623895af05043e4f", "type": "zip", "shasum": "", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:35:46+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "extra": "__unset"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "40790bdf293b462798882ef6da72bb49a4a6633a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/40790bdf293b462798882ef6da72bb49a4a6633a", "type": "zip", "shasum": "", "reference": "40790bdf293b462798882ef6da72bb49a4a6633a"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.41"}, "time": "2022-04-14T15:36:10+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b17d76d7ed179f017aad646e858c90a2771af15d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b17d76d7ed179f017aad646e858c90a2771af15d", "type": "zip", "shasum": "", "reference": "b17d76d7ed179f017aad646e858c90a2771af15d"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1fef05633cd61b629e963e5d8200fb6b67ecf42c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1fef05633cd61b629e963e5d8200fb6b67ecf42c", "type": "zip", "shasum": "", "reference": "1fef05633cd61b629e963e5d8200fb6b67ecf42c"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.36"}, "time": "2021-12-15T10:33:10+00:00"}, {"version": "v4.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "70362f1e112280d75b30087c7598b837c1b468b6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/70362f1e112280d75b30087c7598b837c1b468b6", "type": "zip", "shasum": "", "reference": "70362f1e112280d75b30087c7598b837c1b468b6"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.30"}, "time": "2021-08-04T20:31:23+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "42414d7ac96fc2880a783b872185789dea0d4262"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/42414d7ac96fc2880a783b872185789dea0d4262", "type": "zip", "shasum": "", "reference": "42414d7ac96fc2880a783b872185789dea0d4262"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.27"}, "time": "2021-07-23T15:41:52+00:00"}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ed33314396d968a8936c95f5bd1b88bd3b3e94a3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ed33314396d968a8936c95f5bd1b88bd3b3e94a3", "type": "zip", "shasum": "", "reference": "ed33314396d968a8936c95f5bd1b88bd3b3e94a3"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.25"}, "time": "2021-05-26T11:20:16+00:00", "require": {"php": ">=7.1.3"}}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a96bc19ed87c88eec78e1a4c803bdc1446952983"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a96bc19ed87c88eec78e1a4c803bdc1446952983", "type": "zip", "shasum": "", "reference": "a96bc19ed87c88eec78e1a4c803bdc1446952983"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.24"}, "time": "2021-05-16T12:27:45+00:00"}, {"version": "v4.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "67b77716f517e3f864759232e1201e7aa2ab0e82"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/67b77716f517e3f864759232e1201e7aa2ab0e82", "type": "zip", "shasum": "", "reference": "67b77716f517e3f864759232e1201e7aa2ab0e82"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.23"}, "time": "2021-05-09T09:13:09+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2543795ab1570df588b9bbd31e1a2bd7037b94f6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2543795ab1570df588b9bbd31e1a2bd7037b94f6", "type": "zip", "shasum": "", "reference": "2543795ab1570df588b9bbd31e1a2bd7037b94f6"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.20"}, "time": "2021-02-12T10:48:09+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "25d79cfccfc12e84e7a63a248c3f0720fdd92db6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/25d79cfccfc12e84e7a63a248c3f0720fdd92db6", "type": "zip", "shasum": "", "reference": "25d79cfccfc12e84e7a63a248c3f0720fdd92db6"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00"}, {"description": "Symfony Finder Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ebd0965f2dc2d4e0f11487c16fbb041e50b5c09b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ebd0965f2dc2d4e0f11487c16fbb041e50b5c09b", "type": "zip", "shasum": "", "reference": "ebd0965f2dc2d4e0f11487c16fbb041e50b5c09b"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.18"}, "time": "2020-12-08T16:59:59+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9f1d1d883b79a91ef320c0c6e803494e042ef36e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9f1d1d883b79a91ef320c0c6e803494e042ef36e", "type": "zip", "shasum": "", "reference": "9f1d1d883b79a91ef320c0c6e803494e042ef36e"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.17"}, "time": "2020-11-17T19:45:34+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "26f63b8d4e92f2eecd90f6791a563ebb001abe31"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/26f63b8d4e92f2eecd90f6791a563ebb001abe31", "type": "zip", "shasum": "", "reference": "26f63b8d4e92f2eecd90f6791a563ebb001abe31"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "60d08560f9aa72997c44077c40d47aa28a963230"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/60d08560f9aa72997c44077c40d47aa28a963230", "type": "zip", "shasum": "", "reference": "60d08560f9aa72997c44077c40d47aa28a963230"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.15"}, "time": "2020-10-02T07:34:48+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5ef0f6c609c1a36f723880dfe78301199bc96868"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5ef0f6c609c1a36f723880dfe78301199bc96868", "type": "zip", "shasum": "", "reference": "5ef0f6c609c1a36f723880dfe78301199bc96868"}, "support": {"source": "https://github.com/symfony/finder/tree/4.4"}, "time": "2020-09-02T16:08:58+00:00"}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2a78590b2c7e3de5c429628457c47541c58db9c7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2a78590b2c7e3de5c429628457c47541c58db9c7", "type": "zip", "shasum": "", "reference": "2a78590b2c7e3de5c429628457c47541c58db9c7"}, "time": "2020-08-17T09:56:45+00:00"}, {"version": "v4.4.12", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.12"}}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2727aa35fddfada1dd37599948528e9b152eb742"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2727aa35fddfada1dd37599948528e9b152eb742", "type": "zip", "shasum": "", "reference": "2727aa35fddfada1dd37599948528e9b152eb742"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.11"}, "time": "2020-07-05T09:39:30+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5729f943f9854c5781984ed4907bbb817735776b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5729f943f9854c5781984ed4907bbb817735776b", "type": "zip", "shasum": "", "reference": "5729f943f9854c5781984ed4907bbb817735776b"}, "support": {"source": "https://github.com/symfony/finder/tree/4.4"}, "time": "2020-03-27T16:54:36+00:00", "require": {"php": "^7.1.3"}}, {"version": "v4.4.9", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.8"}}, {"version": "v4.4.8", "version_normalized": "*******"}, {"version": "v4.4.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/4.4"}}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ea69c129aed9fdeca781d4b77eb20b62cf5d5357"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ea69c129aed9fdeca781d4b77eb20b62cf5d5357", "type": "zip", "shasum": "", "reference": "ea69c129aed9fdeca781d4b77eb20b62cf5d5357"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.5"}, "time": "2020-02-14T07:42:58+00:00"}, {"version": "v4.4.5", "version_normalized": "*******"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3a50be43515590faf812fbd7708200aabc327ec3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3a50be43515590faf812fbd7708200aabc327ec3", "type": "zip", "shasum": "", "reference": "3a50be43515590faf812fbd7708200aabc327ec3"}, "support": {"source": "https://github.com/symfony/finder/tree/4.4"}, "time": "2020-01-04T13:00:46+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.3"}}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ce8743441da64c41e2a667b8eb66070444ed911e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ce8743441da64c41e2a667b8eb66070444ed911e", "type": "zip", "shasum": "", "reference": "ce8743441da64c41e2a667b8eb66070444ed911e"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.0"}, "time": "2019-11-17T21:56:56+00:00"}, {"version": "v4.4.1", "version_normalized": "*******"}, {"version": "v4.4.0", "version_normalized": "*******"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5268a27b54b2e57e7b3dad1174d4e9b490443b16"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5268a27b54b2e57e7b3dad1174d4e9b490443b16", "type": "zip", "shasum": "", "reference": "5268a27b54b2e57e7b3dad1174d4e9b490443b16"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.4.0-RC1"}, "time": "2019-11-16T15:22:42+00:00"}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d2be8f79d8abebeafd87cfb9ec656ecb27dfd873"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d2be8f79d8abebeafd87cfb9ec656ecb27dfd873", "type": "zip", "shasum": "", "reference": "d2be8f79d8abebeafd87cfb9ec656ecb27dfd873"}, "support": {"source": "https://github.com/symfony/finder/tree/4.4"}, "time": "2019-10-30T12:55:29+00:00"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8174c13b932c71f10cdd8dfcd8f5e494f1e7003d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8174c13b932c71f10cdd8dfcd8f5e494f1e7003d", "type": "zip", "shasum": "", "reference": "8174c13b932c71f10cdd8dfcd8f5e494f1e7003d"}, "support": {"source": "https://github.com/symfony/finder/tree/4.3"}, "time": "2020-01-04T12:24:57+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}}, {"version": "v4.3.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/v4.3.10"}}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3d72a13a7edcffecc73151821eb75c57e9214e00"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3d72a13a7edcffecc73151821eb75c57e9214e00", "type": "zip", "shasum": "", "reference": "3d72a13a7edcffecc73151821eb75c57e9214e00"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.3.9"}, "time": "2019-11-17T21:56:13+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "72a068f77e317ae77c0a0495236ad292cfb5ce6f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/72a068f77e317ae77c0a0495236ad292cfb5ce6f", "type": "zip", "shasum": "", "reference": "72a068f77e317ae77c0a0495236ad292cfb5ce6f"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.3.6"}, "time": "2019-10-30T12:53:54+00:00"}, {"version": "v4.3.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/4.3"}}, {"version": "v4.3.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v4.3.6"}}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5e575faa95548d0586f6bedaeabec259714e44d1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5e575faa95548d0586f6bedaeabec259714e44d1", "type": "zip", "shasum": "", "reference": "5e575faa95548d0586f6bedaeabec259714e44d1"}, "support": {"source": "https://github.com/symfony/finder/tree/4.3"}, "time": "2019-09-16T11:29:48+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "86c1c929f0a4b24812e1eb109262fc3372c8e9f2"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/86c1c929f0a4b24812e1eb109262fc3372c8e9f2", "type": "zip", "shasum": "", "reference": "86c1c929f0a4b24812e1eb109262fc3372c8e9f2"}, "time": "2019-08-14T12:26:46+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9638d41e3729459860bb96f6247ccb61faaa45f2"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9638d41e3729459860bb96f6247ccb61faaa45f2", "type": "zip", "shasum": "", "reference": "9638d41e3729459860bb96f6247ccb61faaa45f2"}, "time": "2019-06-28T13:16:30+00:00"}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "33c21f7d5d3dc8a140c282854a7e13aeb5d0f91a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/33c21f7d5d3dc8a140c282854a7e13aeb5d0f91a", "type": "zip", "shasum": "", "reference": "33c21f7d5d3dc8a140c282854a7e13aeb5d0f91a"}, "time": "2019-06-13T11:03:18+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b3d4f4c0e4eadfdd8b296af9ca637cfbf51d8176"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b3d4f4c0e4eadfdd8b296af9ca637cfbf51d8176", "type": "zip", "shasum": "", "reference": "b3d4f4c0e4eadfdd8b296af9ca637cfbf51d8176"}, "time": "2019-05-26T20:47:49+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/v4.3.0"}}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/finder/tree/4.3"}}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5e2136f5d2caf276f7b4ba77a6be786988c1783c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5e2136f5d2caf276f7b4ba77a6be786988c1783c", "type": "zip", "shasum": "", "reference": "5e2136f5d2caf276f7b4ba77a6be786988c1783c"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2019-04-06T14:04:46+00:00"}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "cecff7164790b0cd72be2ed20e9591d7140715e0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/cecff7164790b0cd72be2ed20e9591d7140715e0", "type": "zip", "shasum": "", "reference": "cecff7164790b0cd72be2ed20e9591d7140715e0"}, "support": {"source": "https://github.com/symfony/finder/tree/4.2"}, "time": "2019-06-28T12:55:49+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}}, {"version": "v4.2.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/v4.2.11"}}, {"version": "v4.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "765109d3c9e20ae0f229bb7603ca5ed89f2cae84"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/765109d3c9e20ae0f229bb7603ca5ed89f2cae84", "type": "zip", "shasum": "", "reference": "765109d3c9e20ae0f229bb7603ca5ed89f2cae84"}, "support": {"source": "https://github.com/symfony/finder/tree/4.2"}, "time": "2019-06-13T10:57:15+00:00"}, {"version": "v4.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e0ff582c4b038567a7c6630f136488b1d793e6a9"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e0ff582c4b038567a7c6630f136488b1d793e6a9", "type": "zip", "shasum": "", "reference": "e0ff582c4b038567a7c6630f136488b1d793e6a9"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.2.9"}, "time": "2019-05-26T20:47:34+00:00"}, {"version": "v4.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e45135658bd6c14b61850bf131c4f09a55133f69"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e45135658bd6c14b61850bf131c4f09a55133f69", "type": "zip", "shasum": "", "reference": "e45135658bd6c14b61850bf131c4f09a55133f69"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.2.8"}, "time": "2019-04-06T13:51:08+00:00"}, {"version": "v4.2.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/4.2"}}, {"version": "v4.2.6", "version_normalized": "*******"}, {"version": "v4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "267b7002c1b70ea80db0833c3afe05f0fbde580a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/267b7002c1b70ea80db0833c3afe05f0fbde580a", "type": "zip", "shasum": "", "reference": "267b7002c1b70ea80db0833c3afe05f0fbde580a"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.2.5"}, "time": "2019-02-23T15:42:05+00:00"}, {"version": "v4.2.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/finder/tree/4.2"}}, {"version": "v4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ef71816cbb264988bb57fe6a73f610888b9aa70c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ef71816cbb264988bb57fe6a73f610888b9aa70c", "type": "zip", "shasum": "", "reference": "ef71816cbb264988bb57fe6a73f610888b9aa70c"}, "time": "2019-01-16T20:35:37+00:00"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9094d69e8c6ee3fe186a0ec5a4f1401e506071ce"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9094d69e8c6ee3fe186a0ec5a4f1401e506071ce", "type": "zip", "shasum": "", "reference": "9094d69e8c6ee3fe186a0ec5a4f1401e506071ce"}, "time": "2019-01-03T09:07:35+00:00"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e53d477d7b5c4982d0e1bfd2298dbee63d01441d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e53d477d7b5c4982d0e1bfd2298dbee63d01441d", "type": "zip", "shasum": "", "reference": "e53d477d7b5c4982d0e1bfd2298dbee63d01441d"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2018-11-11T19:52:12+00:00"}, {"version": "v4.2.0", "version_normalized": "*******"}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "dbfc8530bf5f3fbeeba9f94bf8fc797364c6b61c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/dbfc8530bf5f3fbeeba9f94bf8fc797364c6b61c", "type": "zip", "shasum": "", "reference": "dbfc8530bf5f3fbeeba9f94bf8fc797364c6b61c"}, "time": "2018-10-10T13:30:17+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "33bae4a56d3c95ac13bc586c1aa57b2baeaa5088"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/33bae4a56d3c95ac13bc586c1aa57b2baeaa5088", "type": "zip", "shasum": "", "reference": "33bae4a56d3c95ac13bc586c1aa57b2baeaa5088"}, "support": {"source": "https://github.com/symfony/finder/tree/v4.1.12"}, "time": "2019-01-16T18:21:11+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "04d34762160cfbff5768851162a887f2ed92d0cf"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/04d34762160cfbff5768851162a887f2ed92d0cf", "type": "zip", "shasum": "", "reference": "04d34762160cfbff5768851162a887f2ed92d0cf"}, "time": "2019-01-03T09:05:57+00:00"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "68fbdcafe915db67adb13fddaec4532e684f6689"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/68fbdcafe915db67adb13fddaec4532e684f6689", "type": "zip", "shasum": "", "reference": "68fbdcafe915db67adb13fddaec4532e684f6689"}, "time": "2018-11-11T19:51:29+00:00"}, {"version": "v4.1.8", "version_normalized": "*******"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1f17195b44543017a9c9b2d437c670627e96ad06"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1f17195b44543017a9c9b2d437c670627e96ad06", "type": "zip", "shasum": "", "reference": "1f17195b44543017a9c9b2d437c670627e96ad06"}, "time": "2018-10-03T08:47:56+00:00"}, {"version": "v4.1.6", "version_normalized": "*******"}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f0b042d445c155501793e7b8007457f9f5bb1c8c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f0b042d445c155501793e7b8007457f9f5bb1c8c", "type": "zip", "shasum": "", "reference": "f0b042d445c155501793e7b8007457f9f5bb1c8c"}, "time": "2018-09-21T12:49:42+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e162f1df3102d0b7472805a5a9d5db9fcf0a8068"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e162f1df3102d0b7472805a5a9d5db9fcf0a8068", "type": "zip", "shasum": "", "reference": "e162f1df3102d0b7472805a5a9d5db9fcf0a8068"}, "time": "2018-07-26T11:24:31+00:00"}, {"version": "v4.1.3", "version_normalized": "*******"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "84714b8417d19e4ba02ea78a41a975b3efaafddb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/84714b8417d19e4ba02ea78a41a975b3efaafddb", "type": "zip", "shasum": "", "reference": "84714b8417d19e4ba02ea78a41a975b3efaafddb"}, "time": "2018-06-19T21:38:16+00:00"}, {"version": "v4.1.1", "version_normalized": "*******"}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "087e2ee0d74464a4c6baac4e90417db7477dc238"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/087e2ee0d74464a4c6baac4e90417db7477dc238", "type": "zip", "shasum": "", "reference": "087e2ee0d74464a4c6baac4e90417db7477dc238"}, "time": "2018-05-16T14:33:22+00:00"}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d78fff5c545a7ecb13f17efa8212ba13ef312fd8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d78fff5c545a7ecb13f17efa8212ba13ef312fd8", "type": "zip", "shasum": "", "reference": "d78fff5c545a7ecb13f17efa8212ba13ef312fd8"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2018-04-04T05:11:41+00:00"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "21a0befb824378c088b9f4714a94fbf8492207b4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/21a0befb824378c088b9f4714a94fbf8492207b4", "type": "zip", "shasum": "", "reference": "21a0befb824378c088b9f4714a94fbf8492207b4"}, "support": {"source": "https://github.com/symfony/finder/tree/4.0"}, "time": "2018-07-26T11:22:46+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "28127911892cee60e4681d05e9d0f7fdb0335944"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/28127911892cee60e4681d05e9d0f7fdb0335944", "type": "zip", "shasum": "", "reference": "28127911892cee60e4681d05e9d0f7fdb0335944"}, "time": "2018-06-19T20:54:48+00:00"}, {"version": "v4.0.12", "version_normalized": "********"}, {"version": "v4.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8c633f5a815903a1fe6e3fc135f207267a8a79af"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8c633f5a815903a1fe6e3fc135f207267a8a79af", "type": "zip", "shasum": "", "reference": "8c633f5a815903a1fe6e3fc135f207267a8a79af"}, "time": "2018-05-16T09:05:32+00:00"}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ca27c02b7a3fef4828c998c2ff9ba7aae1641c49"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ca27c02b7a3fef4828c998c2ff9ba7aae1641c49", "type": "zip", "shasum": "", "reference": "ca27c02b7a3fef4828c998c2ff9ba7aae1641c49"}, "time": "2018-04-04T05:10:37+00:00"}, {"version": "v4.0.8", "version_normalized": "*******"}, {"version": "v4.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c72995d9f5999b3fcdd8660c0c9690243252e1e1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c72995d9f5999b3fcdd8660c0c9690243252e1e1", "type": "zip", "shasum": "", "reference": "c72995d9f5999b3fcdd8660c0c9690243252e1e1"}, "time": "2018-04-02T09:52:41+00:00"}, {"version": "v4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "44a796d2ecc2a16a5fc8f2956a34ee617934d55f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/44a796d2ecc2a16a5fc8f2956a34ee617934d55f", "type": "zip", "shasum": "", "reference": "44a796d2ecc2a16a5fc8f2956a34ee617934d55f"}, "time": "2018-03-05T18:28:26+00:00"}, {"version": "v4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "552e244df10237f845a94fd64b194f848805e34b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/552e244df10237f845a94fd64b194f848805e34b", "type": "zip", "shasum": "", "reference": "552e244df10237f845a94fd64b194f848805e34b"}, "time": "2018-02-11T17:17:44+00:00"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8b08180f2b7ccb41062366b9ad91fbc4f1af8601"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8b08180f2b7ccb41062366b9ad91fbc4f1af8601", "type": "zip", "shasum": "", "reference": "8b08180f2b7ccb41062366b9ad91fbc4f1af8601"}, "time": "2018-01-03T07:38:00+00:00"}, {"version": "v4.0.3", "version_normalized": "*******"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c9cdda4dc4a3182d8d6daeebce4a25fef078ea4c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c9cdda4dc4a3182d8d6daeebce4a25fef078ea4c", "type": "zip", "shasum": "", "reference": "c9cdda4dc4a3182d8d6daeebce4a25fef078ea4c"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2017-11-07T14:45:01+00:00"}, {"version": "v4.0.1", "version_normalized": "*******"}, {"version": "v4.0.0", "version_normalized": "*******"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2d9d7374d27cfce41cb9f79b18a7cb2f569291f8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2d9d7374d27cfce41cb9f79b18a7cb2f569291f8", "type": "zip", "shasum": "", "reference": "2d9d7374d27cfce41cb9f79b18a7cb2f569291f8"}, "time": "2017-11-05T16:26:21+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a0f890f68eb35d951da7c9966059a0195e9c6a7e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a0f890f68eb35d951da7c9966059a0195e9c6a7e", "type": "zip", "shasum": "", "reference": "a0f890f68eb35d951da7c9966059a0195e9c6a7e"}, "time": "2017-10-24T14:16:56+00:00"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6f58c06f2cca44b04d8c9b2fd1bf82c019594e9b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6f58c06f2cca44b04d8c9b2fd1bf82c019594e9b", "type": "zip", "shasum": "", "reference": "6f58c06f2cca44b04d8c9b2fd1bf82c019594e9b"}, "time": "2017-10-02T06:59:24+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "type": "zip", "shasum": "", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-11-16T17:02:08+00:00", "require": {"php": "^5.5.9|>=7.0.8"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4e1da3c110c52d868f8a9153b7de3ebc381fba78"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4e1da3c110c52d868f8a9153b7de3ebc381fba78", "type": "zip", "shasum": "", "reference": "4e1da3c110c52d868f8a9153b7de3ebc381fba78"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.46"}, "time": "2020-10-24T10:57:07+00:00"}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "52140652ed31cee3dabd0c481b5577201fa769b4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/52140652ed31cee3dabd0c481b5577201fa769b4", "type": "zip", "shasum": "", "reference": "52140652ed31cee3dabd0c481b5577201fa769b4"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.45"}, "time": "2020-09-02T16:06:40+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5ec813ccafa8164ef21757e8c725d3a57da59200"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5ec813ccafa8164ef21757e8c725d3a57da59200", "type": "zip", "shasum": "", "reference": "5ec813ccafa8164ef21757e8c725d3a57da59200"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.44"}, "time": "2020-02-14T07:34:21+00:00"}, {"version": "v3.4.43", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/v3.4.39"}}, {"version": "v3.4.42", "version_normalized": "********"}, {"version": "v3.4.41", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/3.4"}, "funding": []}, {"version": "v3.4.40", "version_normalized": "********"}, {"version": "v3.4.39", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/v3.4.39"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}]}, {"version": "v3.4.38", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/3.4"}, "funding": []}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a90a9d3b9f458a5cdeabfa4090b20c000ca3962f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a90a9d3b9f458a5cdeabfa4090b20c000ca3962f", "type": "zip", "shasum": "", "reference": "a90a9d3b9f458a5cdeabfa4090b20c000ca3962f"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.37"}, "time": "2020-01-01T11:03:25+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "290ae21279b37bfd287cdcce640d51204e84afdf"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/290ae21279b37bfd287cdcce640d51204e84afdf", "type": "zip", "shasum": "", "reference": "290ae21279b37bfd287cdcce640d51204e84afdf"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.36"}, "time": "2019-11-17T21:55:15+00:00"}, {"version": "v3.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3e915e5ce305f8bc8017597f71f1f4095092ddf8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3e915e5ce305f8bc8017597f71f1f4095092ddf8", "type": "zip", "shasum": "", "reference": "3e915e5ce305f8bc8017597f71f1f4095092ddf8"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.33"}, "time": "2019-10-30T12:43:22+00:00"}, {"version": "v3.4.34", "version_normalized": "********"}, {"version": "v3.4.33", "version_normalized": "********"}, {"version": "v3.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2b6a666d6ff7fb65d10b97d817c8e7930944afb9"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2b6a666d6ff7fb65d10b97d817c8e7930944afb9", "type": "zip", "shasum": "", "reference": "2b6a666d6ff7fb65d10b97d817c8e7930944afb9"}, "support": {"source": "https://github.com/symfony/finder/tree/3.4"}, "time": "2019-09-01T21:32:23+00:00"}, {"version": "v3.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1fcad80b440abcd1451767349906b6f9d3961d37"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1fcad80b440abcd1451767349906b6f9d3961d37", "type": "zip", "shasum": "", "reference": "1fcad80b440abcd1451767349906b6f9d3961d37"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.31"}, "time": "2019-08-14T09:39:58+00:00"}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1e762fdf73ace6ceb42ba5a6ca280be86082364a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1e762fdf73ace6ceb42ba5a6ca280be86082364a", "type": "zip", "shasum": "", "reference": "1e762fdf73ace6ceb42ba5a6ca280be86082364a"}, "support": {"source": "https://github.com/symfony/finder/tree/3.4"}, "time": "2019-06-28T08:02:59+00:00"}, {"version": "v3.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5f80266a729e30bbcc37f8bf0e62c3d5a38c8208"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5f80266a729e30bbcc37f8bf0e62c3d5a38c8208", "type": "zip", "shasum": "", "reference": "5f80266a729e30bbcc37f8bf0e62c3d5a38c8208"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.29"}, "time": "2019-05-30T15:47:52+00:00"}, {"version": "v3.4.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fa5d962a71f2169dfe1cbae217fa5a2799859f6c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fa5d962a71f2169dfe1cbae217fa5a2799859f6c", "type": "zip", "shasum": "", "reference": "fa5d962a71f2169dfe1cbae217fa5a2799859f6c"}, "support": {"source": "https://github.com/symfony/finder/tree/3.4"}, "time": "2019-05-24T12:25:55+00:00"}, {"version": "v3.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "61af5ce0b34b942d414fe8f1b11950d0e9a90e98"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/61af5ce0b34b942d414fe8f1b11950d0e9a90e98", "type": "zip", "shasum": "", "reference": "61af5ce0b34b942d414fe8f1b11950d0e9a90e98"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.25"}, "time": "2019-04-02T19:54:57+00:00"}, {"version": "v3.4.26", "version_normalized": "********"}, {"version": "v3.4.25", "version_normalized": "********"}, {"version": "v3.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fcdde4aa38f48190ce70d782c166f23930084f9b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fcdde4aa38f48190ce70d782c166f23930084f9b", "type": "zip", "shasum": "", "reference": "fcdde4aa38f48190ce70d782c166f23930084f9b"}, "support": {"source": "https://github.com/symfony/finder/tree/v3.4.24"}, "time": "2019-02-22T14:44:53+00:00"}, {"version": "v3.4.23", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/3.4"}}, {"version": "v3.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7c0c627220308928e958a87c293108e5891cde1d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7c0c627220308928e958a87c293108e5891cde1d", "type": "zip", "shasum": "", "reference": "7c0c627220308928e958a87c293108e5891cde1d"}, "time": "2019-01-16T13:43:35+00:00"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3f2a2ab6315dd7682d4c16dcae1e7b95c8b8555e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3f2a2ab6315dd7682d4c16dcae1e7b95c8b8555e", "type": "zip", "shasum": "", "reference": "3f2a2ab6315dd7682d4c16dcae1e7b95c8b8555e"}, "time": "2019-01-01T13:45:19+00:00"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6cf2be5cbd0e87aa35c01f80ae0bf40b6798e442"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6cf2be5cbd0e87aa35c01f80ae0bf40b6798e442", "type": "zip", "shasum": "", "reference": "6cf2be5cbd0e87aa35c01f80ae0bf40b6798e442"}, "time": "2018-11-11T19:48:54+00:00"}, {"version": "v3.4.19", "version_normalized": "********"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "54ba444dddc5bd5708a34bd095ea67c6eb54644d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/54ba444dddc5bd5708a34bd095ea67c6eb54644d", "type": "zip", "shasum": "", "reference": "54ba444dddc5bd5708a34bd095ea67c6eb54644d"}, "time": "2018-10-03T08:46:40+00:00"}, {"version": "v3.4.17", "version_normalized": "********"}, {"version": "v3.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e8db87d755e14271e920e31ba834a4ae99483232"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e8db87d755e14271e920e31ba834a4ae99483232", "type": "zip", "shasum": "", "reference": "e8db87d755e14271e920e31ba834a4ae99483232"}, "time": "2018-09-21T12:47:54+00:00"}, {"version": "v3.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8a84fcb207451df0013b2c74cbbf1b62d47b999a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8a84fcb207451df0013b2c74cbbf1b62d47b999a", "type": "zip", "shasum": "", "reference": "8a84fcb207451df0013b2c74cbbf1b62d47b999a"}, "time": "2018-07-26T11:19:56+00:00"}, {"version": "v3.4.14", "version_normalized": "********"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3a8c3de91d2b2c68cd2d665cf9d00f7ef9eaa394"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3a8c3de91d2b2c68cd2d665cf9d00f7ef9eaa394", "type": "zip", "shasum": "", "reference": "3a8c3de91d2b2c68cd2d665cf9d00f7ef9eaa394"}, "time": "2018-06-19T20:52:10+00:00"}, {"version": "v3.4.12", "version_normalized": "********"}, {"version": "v3.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "472a92f3df8b247b49ae364275fb32943b9656c6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/472a92f3df8b247b49ae364275fb32943b9656c6", "type": "zip", "shasum": "", "reference": "472a92f3df8b247b49ae364275fb32943b9656c6"}, "time": "2018-05-16T08:49:21+00:00"}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "bd14efe8b1fabc4de82bf50dce62f05f9a102433"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/bd14efe8b1fabc4de82bf50dce62f05f9a102433", "type": "zip", "shasum": "", "reference": "bd14efe8b1fabc4de82bf50dce62f05f9a102433"}, "time": "2018-04-04T05:07:11+00:00"}, {"version": "v3.4.8", "version_normalized": "*******"}, {"version": "v3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7a2e1299cc0c4162996f18e347b6356729a55317"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7a2e1299cc0c4162996f18e347b6356729a55317", "type": "zip", "shasum": "", "reference": "7a2e1299cc0c4162996f18e347b6356729a55317"}, "time": "2018-03-28T18:23:39+00:00"}, {"version": "v3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a479817ce0a9e4adfd7d39c6407c95d97c254625"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a479817ce0a9e4adfd7d39c6407c95d97c254625", "type": "zip", "shasum": "", "reference": "a479817ce0a9e4adfd7d39c6407c95d97c254625"}, "time": "2018-03-05T18:28:11+00:00"}, {"version": "v3.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6a615613745cef820d807443f32076bb9f5d0a38"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6a615613745cef820d807443f32076bb9f5d0a38", "type": "zip", "shasum": "", "reference": "6a615613745cef820d807443f32076bb9f5d0a38"}, "time": "2018-02-11T17:15:12+00:00"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "613e26310776f49a1773b6737c6bd554b8bc8c6f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/613e26310776f49a1773b6737c6bd554b8bc8c6f", "type": "zip", "shasum": "", "reference": "613e26310776f49a1773b6737c6bd554b8bc8c6f"}, "time": "2018-01-03T07:37:34+00:00"}, {"version": "v3.4.3", "version_normalized": "*******"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "dac8d7db537bac7ad8143eb11360a8c2231f251a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/dac8d7db537bac7ad8143eb11360a8c2231f251a", "type": "zip", "shasum": "", "reference": "dac8d7db537bac7ad8143eb11360a8c2231f251a"}, "time": "2017-11-05T16:10:10+00:00"}, {"version": "v3.4.1", "version_normalized": "*******"}, {"version": "v3.4.0", "version_normalized": "*******"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "29422c4e871dd668ecac0e8075cf64dccdabff46"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/29422c4e871dd668ecac0e8075cf64dccdabff46", "type": "zip", "shasum": "", "reference": "29422c4e871dd668ecac0e8075cf64dccdabff46"}, "time": "2017-10-24T14:12:06+00:00"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "6db4a8ddcd86146761130989eb348451de03de74"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/6db4a8ddcd86146761130989eb348451de03de74", "type": "zip", "shasum": "", "reference": "6db4a8ddcd86146761130989eb348451de03de74"}, "time": "2017-10-02T06:49:52+00:00"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "03da5d9bac613d2095373df9ca8697b9fa571789"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/03da5d9bac613d2095373df9ca8697b9fa571789", "type": "zip", "shasum": "", "reference": "03da5d9bac613d2095373df9ca8697b9fa571789"}, "support": {"source": "https://github.com/symfony/finder/tree/3.3"}, "time": "2018-01-03T07:37:11+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "138af5ec075d4b1d1bd19de08c38a34bb2d7d880"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/138af5ec075d4b1d1bd19de08c38a34bb2d7d880", "type": "zip", "shasum": "", "reference": "138af5ec075d4b1d1bd19de08c38a34bb2d7d880"}, "time": "2017-11-05T15:47:03+00:00"}, {"version": "v3.3.13", "version_normalized": "********"}, {"version": "v3.3.12", "version_normalized": "********"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "773e19a491d97926f236942484cb541560ce862d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/773e19a491d97926f236942484cb541560ce862d", "type": "zip", "shasum": "", "reference": "773e19a491d97926f236942484cb541560ce862d"}, "time": "2017-10-02T06:42:24+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b2260dbc80f3c4198f903215f91a1ac7fe9fe09e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b2260dbc80f3c4198f903215f91a1ac7fe9fe09e", "type": "zip", "shasum": "", "reference": "b2260dbc80f3c4198f903215f91a1ac7fe9fe09e"}, "time": "2017-07-29T21:54:42+00:00"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "baea7f66d30854ad32988c11a09d7ffd485810c4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/baea7f66d30854ad32988c11a09d7ffd485810c4", "type": "zip", "shasum": "", "reference": "baea7f66d30854ad32988c11a09d7ffd485810c4"}, "time": "2017-06-01T21:01:25+00:00", "require": {"php": ">=5.5.9"}}, {"version": "v3.3.5", "version_normalized": "*******"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "30cb2a2c09627823a7243638dd456de4e2748fed"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/30cb2a2c09627823a7243638dd456de4e2748fed", "type": "zip", "shasum": "", "reference": "30cb2a2c09627823a7243638dd456de4e2748fed"}, "time": "2017-05-25T23:10:31+00:00"}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ce309911aabd89ef885673ddb924076c1c410356"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ce309911aabd89ef885673ddb924076c1c410356", "type": "zip", "shasum": "", "reference": "ce309911aabd89ef885673ddb924076c1c410356"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2017-04-12T14:14:56+00:00"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a263455d7907ff59be81a71926a46a377f5b34a4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a263455d7907ff59be81a71926a46a377f5b34a4", "type": "zip", "shasum": "", "reference": "a263455d7907ff59be81a71926a46a377f5b34a4"}, "support": {"source": "https://github.com/symfony/finder/tree/3.2"}, "time": "2017-06-01T21:00:24+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "81ea578fa04c2caca62dc823596ab5670de50a0c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/81ea578fa04c2caca62dc823596ab5670de50a0c", "type": "zip", "shasum": "", "reference": "81ea578fa04c2caca62dc823596ab5670de50a0c"}, "time": "2017-05-25T22:59:05+00:00"}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9cf076f8f492f4b1ffac40aae9c2d287b4ca6930"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9cf076f8f492f4b1ffac40aae9c2d287b4ca6930", "type": "zip", "shasum": "", "reference": "9cf076f8f492f4b1ffac40aae9c2d287b4ca6930"}, "time": "2017-04-12T14:13:17+00:00"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b20900ce5ea164cd9314af52725b0bb5a758217a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b20900ce5ea164cd9314af52725b0bb5a758217a", "type": "zip", "shasum": "", "reference": "b20900ce5ea164cd9314af52725b0bb5a758217a"}, "time": "2017-03-20T09:32:19+00:00"}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "92d7476d2df60cd851a3e13e078664b1deb8ce10"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/92d7476d2df60cd851a3e13e078664b1deb8ce10", "type": "zip", "shasum": "", "reference": "92d7476d2df60cd851a3e13e078664b1deb8ce10"}, "time": "2017-02-21T09:12:04+00:00"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8c71141cae8e2957946b403cc71a67213c0380d6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8c71141cae8e2957946b403cc71a67213c0380d6", "type": "zip", "shasum": "", "reference": "8c71141cae8e2957946b403cc71a67213c0380d6"}, "time": "2017-01-02T20:32:22+00:00"}, {"version": "v3.2.3", "version_normalized": "*******"}, {"version": "v3.2.2", "version_normalized": "*******"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a69cb5d455b4885ca376dc5bb3e1155cc8c08c4b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a69cb5d455b4885ca376dc5bb3e1155cc8c08c4b", "type": "zip", "shasum": "", "reference": "a69cb5d455b4885ca376dc5bb3e1155cc8c08c4b"}, "time": "2016-12-13T09:39:43+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4263e35a1e342a0f195c9349c0dee38148f8a14f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4263e35a1e342a0f195c9349c0dee38148f8a14f", "type": "zip", "shasum": "", "reference": "4263e35a1e342a0f195c9349c0dee38148f8a14f"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2016-11-03T08:11:03+00:00"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "df6823821a90eec42c3040b4b06ba8b2af89ae30"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/df6823821a90eec42c3040b4b06ba8b2af89ae30", "type": "zip", "shasum": "", "reference": "df6823821a90eec42c3040b4b06ba8b2af89ae30"}, "time": "2016-09-28T00:11:20+00:00"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "59687a255d1562f2c17b012418273862083d85f7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/59687a255d1562f2c17b012418273862083d85f7", "type": "zip", "shasum": "", "reference": "59687a255d1562f2c17b012418273862083d85f7"}, "support": {"source": "https://github.com/symfony/finder/tree/3.1"}, "time": "2017-01-02T20:31:54+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}, {"version": "v3.1.9", "version_normalized": "*******"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "74dcd370c8d057882575e535616fde935e411b19"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/74dcd370c8d057882575e535616fde935e411b19", "type": "zip", "shasum": "", "reference": "74dcd370c8d057882575e535616fde935e411b19"}, "time": "2016-12-13T09:38:21+00:00"}, {"version": "v3.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9925935bf7144f9e4d2b976905881b4face036fb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9925935bf7144f9e4d2b976905881b4face036fb", "type": "zip", "shasum": "", "reference": "9925935bf7144f9e4d2b976905881b4face036fb"}, "time": "2016-11-03T08:04:31+00:00"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "205b5ffbb518a98ba2ae60a52656c4a31ab00c6f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/205b5ffbb518a98ba2ae60a52656c4a31ab00c6f", "type": "zip", "shasum": "", "reference": "205b5ffbb518a98ba2ae60a52656c4a31ab00c6f"}, "time": "2016-09-28T00:11:12+00:00"}, {"version": "v3.1.5", "version_normalized": "*******"}, {"version": "v3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e568ef1784f447a0e54dcb6f6de30b9747b0f577"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e568ef1784f447a0e54dcb6f6de30b9747b0f577", "type": "zip", "shasum": "", "reference": "e568ef1784f447a0e54dcb6f6de30b9747b0f577"}, "time": "2016-08-26T12:04:02+00:00"}, {"version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8201978de88a9fa0923e18601bb17f1df9c721e7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8201978de88a9fa0923e18601bb17f1df9c721e7", "type": "zip", "shasum": "", "reference": "8201978de88a9fa0923e18601bb17f1df9c721e7"}, "time": "2016-06-29T05:41:56+00:00"}, {"version": "v3.1.2", "version_normalized": "*******"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "40d17ed287bf51a2f884c4619ce8ff2a1c5cd219"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/40d17ed287bf51a2f884c4619ce8ff2a1c5cd219", "type": "zip", "shasum": "", "reference": "40d17ed287bf51a2f884c4619ce8ff2a1c5cd219"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2016-05-13T18:06:41+00:00"}, {"version": "v3.1.0", "version_normalized": "*******"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3eb4e64c6145ef8b92adefb618a74ebdde9e3fe9"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3eb4e64c6145ef8b92adefb618a74ebdde9e3fe9", "type": "zip", "shasum": "", "reference": "3eb4e64c6145ef8b92adefb618a74ebdde9e3fe9"}, "support": {"source": "https://github.com/symfony/finder/tree/3.0"}, "time": "2016-06-29T05:40:00+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "39e5f3d533d07b5416b9d7aad53a27f939d4f811"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/39e5f3d533d07b5416b9d7aad53a27f939d4f811", "type": "zip", "shasum": "", "reference": "39e5f3d533d07b5416b9d7aad53a27f939d4f811"}, "time": "2016-05-13T18:03:36+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c54e407b35bc098916704e9fd090da21da4c4f52"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c54e407b35bc098916704e9fd090da21da4c4f52", "type": "zip", "shasum": "", "reference": "c54e407b35bc098916704e9fd090da21da4c4f52"}, "time": "2016-03-10T11:13:05+00:00"}, {"version": "v3.0.5", "version_normalized": "*******"}, {"version": "v3.0.4", "version_normalized": "*******"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "623bda0abd9aa29e529c8e9c08b3b84171914723"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/623bda0abd9aa29e529c8e9c08b3b84171914723", "type": "zip", "shasum": "", "reference": "623bda0abd9aa29e529c8e9c08b3b84171914723"}, "time": "2016-01-27T05:14:46+00:00"}, {"version": "v3.0.2", "version_normalized": "*******"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8617895eb798b6bdb338321ce19453dc113e5675"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8617895eb798b6bdb338321ce19453dc113e5675", "type": "zip", "shasum": "", "reference": "8617895eb798b6bdb338321ce19453dc113e5675"}, "time": "2015-12-05T11:13:14+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3577eb98dba90721d1a0a3edfc6956ab8b1aecee"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3577eb98dba90721d1a0a3edfc6956ab8b1aecee", "type": "zip", "shasum": "", "reference": "3577eb98dba90721d1a0a3edfc6956ab8b1aecee"}, "support": {"source": "https://github.com/symfony/finder/tree/master"}, "time": "2015-10-30T23:35:59+00:00"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.8.52", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1444eac52273e345d9b95129bf914639305a9ba4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1444eac52273e345d9b95129bf914639305a9ba4", "type": "zip", "shasum": "", "reference": "1444eac52273e345d9b95129bf914639305a9ba4"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.8.50"}, "time": "2018-11-11T11:18:13+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9"}}, {"version": "v2.8.50", "version_normalized": "********"}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5ebb438d1aabe9dba93099dd06e0500f97817a6e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5ebb438d1aabe9dba93099dd06e0500f97817a6e", "type": "zip", "shasum": "", "reference": "5ebb438d1aabe9dba93099dd06e0500f97817a6e"}, "time": "2018-09-21T12:46:38+00:00"}, {"version": "v2.8.46", "version_normalized": "********"}, {"version": "v2.8.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f0de0b51913eb2caab7dfed6413b87e14fca780e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f0de0b51913eb2caab7dfed6413b87e14fca780e", "type": "zip", "shasum": "", "reference": "f0de0b51913eb2caab7dfed6413b87e14fca780e"}, "time": "2018-07-26T11:13:39+00:00"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "995cd7c28a0778cece02e2133b4d813dc509dfc3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/995cd7c28a0778cece02e2133b4d813dc509dfc3", "type": "zip", "shasum": "", "reference": "995cd7c28a0778cece02e2133b4d813dc509dfc3"}, "time": "2018-06-19T11:07:17+00:00"}, {"version": "v2.8.42", "version_normalized": "********"}, {"version": "v2.8.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "79764d21163db295f0daf8bd9d9b91f97e65db6a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/79764d21163db295f0daf8bd9d9b91f97e65db6a", "type": "zip", "shasum": "", "reference": "79764d21163db295f0daf8bd9d9b91f97e65db6a"}, "time": "2018-05-15T21:17:45+00:00"}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "423746fc18ccf31f9abec43e4f078bb6e024b2d5"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/423746fc18ccf31f9abec43e4f078bb6e024b2d5", "type": "zip", "shasum": "", "reference": "423746fc18ccf31f9abec43e4f078bb6e024b2d5"}, "time": "2018-04-04T13:38:31+00:00"}, {"version": "v2.8.38", "version_normalized": "********"}, {"version": "v2.8.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3cf7b50fa4cd6c2c4aa504b85196b12e386659d4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3cf7b50fa4cd6c2c4aa504b85196b12e386659d4", "type": "zip", "shasum": "", "reference": "3cf7b50fa4cd6c2c4aa504b85196b12e386659d4"}, "time": "2018-03-28T18:22:50+00:00"}, {"version": "v2.8.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c5c751ccd50230b4517393344080a0eaf968bf2d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c5c751ccd50230b4517393344080a0eaf968bf2d", "type": "zip", "shasum": "", "reference": "c5c751ccd50230b4517393344080a0eaf968bf2d"}, "time": "2018-03-05T18:27:59+00:00"}, {"version": "v2.8.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7099de3ebc97d3af98c464076d12dfca3ade0252"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7099de3ebc97d3af98c464076d12dfca3ade0252", "type": "zip", "shasum": "", "reference": "7099de3ebc97d3af98c464076d12dfca3ade0252"}, "time": "2018-02-11T16:53:59+00:00"}, {"version": "v2.8.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9786ccb6a1f94a89ae18fc6a1b68de1f070823ed"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9786ccb6a1f94a89ae18fc6a1b68de1f070823ed", "type": "zip", "shasum": "", "reference": "9786ccb6a1f94a89ae18fc6a1b68de1f070823ed"}, "time": "2018-01-29T08:54:45+00:00"}, {"version": "v2.8.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "cb2ce50366dd3168f7b06135ffee0a5e35713ce8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/cb2ce50366dd3168f7b06135ffee0a5e35713ce8", "type": "zip", "shasum": "", "reference": "cb2ce50366dd3168f7b06135ffee0a5e35713ce8"}, "time": "2018-01-03T07:36:31+00:00"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "efeceae6a05a9b2fcb3391333f1d4a828ff44ab8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/efeceae6a05a9b2fcb3391333f1d4a828ff44ab8", "type": "zip", "shasum": "", "reference": "efeceae6a05a9b2fcb3391333f1d4a828ff44ab8"}, "time": "2017-11-05T15:25:56+00:00"}, {"version": "v2.8.31", "version_normalized": "********"}, {"version": "v2.8.30", "version_normalized": "********"}, {"version": "v2.8.29", "version_normalized": "********"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a945724b201f74d543e356f6059c930bb8d10c92"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a945724b201f74d543e356f6059c930bb8d10c92", "type": "zip", "shasum": "", "reference": "a945724b201f74d543e356f6059c930bb8d10c92"}, "time": "2017-10-01T21:00:16+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4f4e84811004e065a3bb5ceeb1d9aa592630f9ad"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4f4e84811004e065a3bb5ceeb1d9aa592630f9ad", "type": "zip", "shasum": "", "reference": "4f4e84811004e065a3bb5ceeb1d9aa592630f9ad"}, "time": "2017-06-01T20:52:29+00:00"}, {"version": "v2.8.26", "version_normalized": "********"}, {"version": "v2.8.25", "version_normalized": "********"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********"}, {"version": "v2.8.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b058a6f0cb6ee9b6b727aae03d5a62474a308528"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b058a6f0cb6ee9b6b727aae03d5a62474a308528", "type": "zip", "shasum": "", "reference": "b058a6f0cb6ee9b6b727aae03d5a62474a308528"}, "time": "2017-05-25T22:57:22+00:00"}, {"version": "v2.8.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "16d55394b31547e4a8494551b85c9b9915545347"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/16d55394b31547e4a8494551b85c9b9915545347", "type": "zip", "shasum": "", "reference": "16d55394b31547e4a8494551b85c9b9915545347"}, "time": "2017-04-12T14:07:15+00:00"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7131327eb95d86d72039fd1216226c28f36fd02a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7131327eb95d86d72039fd1216226c28f36fd02a", "type": "zip", "shasum": "", "reference": "7131327eb95d86d72039fd1216226c28f36fd02a"}, "time": "2017-03-20T08:46:40+00:00"}, {"version": "v2.8.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5fc4b5cab38b9d28be318fcffd8066988e7d9451"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5fc4b5cab38b9d28be318fcffd8066988e7d9451", "type": "zip", "shasum": "", "reference": "5fc4b5cab38b9d28be318fcffd8066988e7d9451"}, "time": "2017-02-21T08:33:48+00:00"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "355fccac526522dc5fca8ecf0e62749a149f3b8b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/355fccac526522dc5fca8ecf0e62749a149f3b8b", "type": "zip", "shasum": "", "reference": "355fccac526522dc5fca8ecf0e62749a149f3b8b"}, "time": "2017-01-02T20:30:24+00:00"}, {"version": "v2.8.16", "version_normalized": "********"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c0f10576335743b881ac1ed39d18c0fa66048775"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c0f10576335743b881ac1ed39d18c0fa66048775", "type": "zip", "shasum": "", "reference": "c0f10576335743b881ac1ed39d18c0fa66048775"}, "time": "2016-12-13T09:38:12+00:00"}, {"version": "v2.8.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0023b024363dfc0cd21262e556f25a291fe8d7fd"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0023b024363dfc0cd21262e556f25a291fe8d7fd", "type": "zip", "shasum": "", "reference": "0023b024363dfc0cd21262e556f25a291fe8d7fd"}, "time": "2016-11-03T07:52:58+00:00"}, {"version": "v2.8.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "bc24c8f5674c6f6841f2856b70e5d60784be5691"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/bc24c8f5674c6f6841f2856b70e5d60784be5691", "type": "zip", "shasum": "", "reference": "bc24c8f5674c6f6841f2856b70e5d60784be5691"}, "time": "2016-09-28T00:10:16+00:00"}, {"version": "v2.8.12", "version_normalized": "********"}, {"version": "v2.8.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "bec5533e6ed650547d6ec8de4b541dc9929066f7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/bec5533e6ed650547d6ec8de4b541dc9929066f7", "type": "zip", "shasum": "", "reference": "bec5533e6ed650547d6ec8de4b541dc9929066f7"}, "time": "2016-08-26T11:57:43+00:00"}, {"version": "v2.8.10", "version_normalized": "********"}, {"version": "v2.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "60804d88691e4a73bbbb3035eb1d9f075c5c2c10"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/60804d88691e4a73bbbb3035eb1d9f075c5c2c10", "type": "zip", "shasum": "", "reference": "60804d88691e4a73bbbb3035eb1d9f075c5c2c10"}, "time": "2016-07-26T08:02:44+00:00"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "bf0506ef4e7778fd3f0f1f141ab5e8c1ef35dd7d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/bf0506ef4e7778fd3f0f1f141ab5e8c1ef35dd7d", "type": "zip", "shasum": "", "reference": "bf0506ef4e7778fd3f0f1f141ab5e8c1ef35dd7d"}, "time": "2016-06-29T05:29:29+00:00"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3ec095fab1800222732ca522a95dce8fa124007b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3ec095fab1800222732ca522a95dce8fa124007b", "type": "zip", "shasum": "", "reference": "3ec095fab1800222732ca522a95dce8fa124007b"}, "time": "2016-06-06T11:11:27+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ca24cf2cd4e3826f571e0067e535758e73807aa1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ca24cf2cd4e3826f571e0067e535758e73807aa1", "type": "zip", "shasum": "", "reference": "ca24cf2cd4e3826f571e0067e535758e73807aa1"}, "time": "2016-03-10T10:53:53+00:00"}, {"version": "v2.8.5", "version_normalized": "*******"}, {"version": "v2.8.4", "version_normalized": "*******"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "877bb4b16ea573cc8c024e9590888fcf7eb7e0f7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/877bb4b16ea573cc8c024e9590888fcf7eb7e0f7", "type": "zip", "shasum": "", "reference": "877bb4b16ea573cc8c024e9590888fcf7eb7e0f7"}, "time": "2016-02-22T16:12:45+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c90fabdd97e431ee19b6383999cf35334dff27da"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c90fabdd97e431ee19b6383999cf35334dff27da", "type": "zip", "shasum": "", "reference": "c90fabdd97e431ee19b6383999cf35334dff27da"}, "time": "2016-01-14T08:26:52+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "dd41ae57f4f737be271d944a0cc5f5f21203a7c6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/dd41ae57f4f737be271d944a0cc5f5f21203a7c6", "type": "zip", "shasum": "", "reference": "dd41ae57f4f737be271d944a0cc5f5f21203a7c6"}, "time": "2015-12-05T11:09:21+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ead9b07af4ba77b6507bee697396a5c79e633f08"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ead9b07af4ba77b6507bee697396a5c79e633f08", "type": "zip", "shasum": "", "reference": "ead9b07af4ba77b6507bee697396a5c79e633f08"}, "time": "2015-10-30T20:15:42+00:00"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "34226a3aa279f1e356ad56181b91acfdc9a2525c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/34226a3aa279f1e356ad56181b91acfdc9a2525c", "type": "zip", "shasum": "", "reference": "34226a3aa279f1e356ad56181b91acfdc9a2525c"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.7.51"}, "time": "2018-05-14T06:36:14+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/finder/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "********"}, {"version": "v2.7.47", "version_normalized": "********"}, {"version": "v2.7.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "79fe1ed67eda99c2d4282d8c52f6d9220bf00740"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/79fe1ed67eda99c2d4282d8c52f6d9220bf00740", "type": "zip", "shasum": "", "reference": "79fe1ed67eda99c2d4282d8c52f6d9220bf00740"}, "time": "2018-04-04T06:34:32+00:00"}, {"version": "v2.7.45", "version_normalized": "********"}, {"version": "v2.7.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1afa5070425c0dec88d512e437c88a73897e36e6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1afa5070425c0dec88d512e437c88a73897e36e6", "type": "zip", "shasum": "", "reference": "1afa5070425c0dec88d512e437c88a73897e36e6"}, "time": "2018-03-20T14:14:56+00:00"}, {"version": "v2.7.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "1f813a57d23235d052b16367e2fdcafa27d8e5d4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/1f813a57d23235d052b16367e2fdcafa27d8e5d4", "type": "zip", "shasum": "", "reference": "1f813a57d23235d052b16367e2fdcafa27d8e5d4"}, "time": "2018-03-04T15:13:29+00:00"}, {"version": "v2.7.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "850138b3e3e5c1b4a82d4a682b8cd84ab49834c8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/850138b3e3e5c1b4a82d4a682b8cd84ab49834c8", "type": "zip", "shasum": "", "reference": "850138b3e3e5c1b4a82d4a682b8cd84ab49834c8"}, "time": "2018-02-11T10:02:34+00:00"}, {"version": "v2.7.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "75f766f4a1c60407ec371cd2a7ef0a09d0e8e745"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/75f766f4a1c60407ec371cd2a7ef0a09d0e8e745", "type": "zip", "shasum": "", "reference": "75f766f4a1c60407ec371cd2a7ef0a09d0e8e745"}, "time": "2018-01-24T17:56:32+00:00"}, {"version": "v2.7.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b9a4e37aeb5a367df8f6a898ead1de39cf59356c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b9a4e37aeb5a367df8f6a898ead1de39cf59356c", "type": "zip", "shasum": "", "reference": "b9a4e37aeb5a367df8f6a898ead1de39cf59356c"}, "time": "2018-01-03T07:23:28+00:00"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d90987b0ee6b1fd03ee8237cc1aefe7b10c8e194"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d90987b0ee6b1fd03ee8237cc1aefe7b10c8e194", "type": "zip", "shasum": "", "reference": "d90987b0ee6b1fd03ee8237cc1aefe7b10c8e194"}, "time": "2017-10-29T09:49:53+00:00"}, {"version": "v2.7.38", "version_normalized": "********"}, {"version": "v2.7.37", "version_normalized": "********"}, {"version": "v2.7.36", "version_normalized": "********"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "3ab69a269d40bf94f8d04594bc693bc1c32d345d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/3ab69a269d40bf94f8d04594bc693bc1c32d345d", "type": "zip", "shasum": "", "reference": "3ab69a269d40bf94f8d04594bc693bc1c32d345d"}, "time": "2017-09-30T14:00:25+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "936529dea4ebedcfe577700b6079a75eb918d21f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/936529dea4ebedcfe577700b6079a75eb918d21f", "type": "zip", "shasum": "", "reference": "936529dea4ebedcfe577700b6079a75eb918d21f"}, "time": "2017-06-01T20:44:56+00:00"}, {"version": "v2.7.33", "version_normalized": "********"}, {"version": "v2.7.32", "version_normalized": "********"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********"}, {"version": "v2.7.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f65720cd31a43cbd996addf59e46488fd47f10b8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f65720cd31a43cbd996addf59e46488fd47f10b8", "type": "zip", "shasum": "", "reference": "f65720cd31a43cbd996addf59e46488fd47f10b8"}, "time": "2017-05-22T11:36:46+00:00"}, {"version": "v2.7.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9ec5f638fa4c6f02e5231d04747963186f6840a6"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9ec5f638fa4c6f02e5231d04747963186f6840a6", "type": "zip", "shasum": "", "reference": "9ec5f638fa4c6f02e5231d04747963186f6840a6"}, "time": "2017-04-12T07:39:27+00:00"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f2589d96fa5fbcf0ff55d5791d433987247c4fbb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f2589d96fa5fbcf0ff55d5791d433987247c4fbb", "type": "zip", "shasum": "", "reference": "f2589d96fa5fbcf0ff55d5791d433987247c4fbb"}, "time": "2017-03-14T11:32:10+00:00"}, {"version": "v2.7.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d8633c69cd53165ca71786f9e0a591b06ce87403"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d8633c69cd53165ca71786f9e0a591b06ce87403", "type": "zip", "shasum": "", "reference": "d8633c69cd53165ca71786f9e0a591b06ce87403"}, "time": "2017-02-21T08:32:25+00:00"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "927a4a6c37fc59e1331f4300cc138f4a752eeb12"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/927a4a6c37fc59e1331f4300cc138f4a752eeb12", "type": "zip", "shasum": "", "reference": "927a4a6c37fc59e1331f4300cc138f4a752eeb12"}, "time": "2017-01-02T20:30:00+00:00"}, {"version": "v2.7.23", "version_normalized": "********"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0c68dd978d58b5795c1b3349902cd29a4f8c5ce0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0c68dd978d58b5795c1b3349902cd29a4f8c5ce0", "type": "zip", "shasum": "", "reference": "0c68dd978d58b5795c1b3349902cd29a4f8c5ce0"}, "time": "2016-12-03T05:31:03+00:00"}, {"version": "v2.7.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0e89934a55c762ed7aad55f707e946b82b7410e3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0e89934a55c762ed7aad55f707e946b82b7410e3", "type": "zip", "shasum": "", "reference": "0e89934a55c762ed7aad55f707e946b82b7410e3"}, "time": "2016-11-03T07:44:55+00:00"}, {"version": "v2.7.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "be6a0cebb24ee147b21ce35952ed1b999cca3291"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/be6a0cebb24ee147b21ce35952ed1b999cca3291", "type": "zip", "shasum": "", "reference": "be6a0cebb24ee147b21ce35952ed1b999cca3291"}, "time": "2016-09-16T16:53:37+00:00"}, {"version": "v2.7.19", "version_normalized": "********"}, {"version": "v2.7.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2716347131481656cc613d98ec12228bae81c8c3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2716347131481656cc613d98ec12228bae81c8c3", "type": "zip", "shasum": "", "reference": "2716347131481656cc613d98ec12228bae81c8c3"}, "time": "2016-08-23T19:36:25+00:00"}, {"version": "v2.7.17", "version_normalized": "********"}, {"version": "v2.7.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "864a73cc1b5f7a597ce8f74ae31851ffc83d7353"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/864a73cc1b5f7a597ce8f74ae31851ffc83d7353", "type": "zip", "shasum": "", "reference": "864a73cc1b5f7a597ce8f74ae31851ffc83d7353"}, "time": "2016-07-26T04:40:54+00:00"}, {"version": "v2.7.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7f8893d016a4deddf653a9c327fb426aae4e05b3"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7f8893d016a4deddf653a9c327fb426aae4e05b3", "type": "zip", "shasum": "", "reference": "7f8893d016a4deddf653a9c327fb426aae4e05b3"}, "time": "2016-06-28T06:24:06+00:00"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5795f68a72b7c7a11cc4e6dc4214663473b44857"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5795f68a72b7c7a11cc4e6dc4214663473b44857", "type": "zip", "shasum": "", "reference": "5795f68a72b7c7a11cc4e6dc4214663473b44857"}, "time": "2016-06-06T11:03:51+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0bacc7c9fc1905cfce20212633013a5b300dce52"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0bacc7c9fc1905cfce20212633013a5b300dce52", "type": "zip", "shasum": "", "reference": "0bacc7c9fc1905cfce20212633013a5b300dce52"}, "time": "2016-03-10T10:49:29+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "addcb70b33affbca4f3979b0d000259b63dd6711"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/addcb70b33affbca4f3979b0d000259b63dd6711", "type": "zip", "shasum": "", "reference": "addcb70b33affbca4f3979b0d000259b63dd6711"}, "time": "2016-02-22T16:12:29+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d20ac81c81a67ab898b0c0afa435f3e9a7d460cf"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d20ac81c81a67ab898b0c0afa435f3e9a7d460cf", "type": "zip", "shasum": "", "reference": "d20ac81c81a67ab898b0c0afa435f3e9a7d460cf"}, "time": "2016-01-14T08:26:43+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "937edcbac3f2dd3187c56cf90368867d55dee991"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/937edcbac3f2dd3187c56cf90368867d55dee991", "type": "zip", "shasum": "", "reference": "937edcbac3f2dd3187c56cf90368867d55dee991"}, "time": "2015-12-05T11:06:38+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a06a0c0ff7db3736a50d530c908cca547bf13da9"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a06a0c0ff7db3736a50d530c908cca547bf13da9", "type": "zip", "shasum": "", "reference": "a06a0c0ff7db3736a50d530c908cca547bf13da9"}, "time": "2015-10-30T20:10:21+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2ffb4e9598db3c48eb6d0ae73b04bbf09280c59d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2ffb4e9598db3c48eb6d0ae73b04bbf09280c59d", "type": "zip", "shasum": "", "reference": "2ffb4e9598db3c48eb6d0ae73b04bbf09280c59d"}, "time": "2015-10-11T09:39:48+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8262ab605973afbb3ef74b945daabf086f58366f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8262ab605973afbb3ef74b945daabf086f58366f", "type": "zip", "shasum": "", "reference": "8262ab605973afbb3ef74b945daabf086f58366f"}, "time": "2015-09-19T19:59:23+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "fff4b0c362640a0ab7355e2647b3d461608e9065"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/fff4b0c362640a0ab7355e2647b3d461608e9065", "type": "zip", "shasum": "", "reference": "fff4b0c362640a0ab7355e2647b3d461608e9065"}, "support": {"source": "https://github.com/symfony/Finder/tree/2.7"}, "time": "2015-08-26T17:56:37+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "ae0f363277485094edc04c9f3cbe595b183b78e4"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/ae0f363277485094edc04c9f3cbe595b183b78e4", "type": "zip", "shasum": "", "reference": "ae0f363277485094edc04c9f3cbe595b183b78e4"}, "time": "2015-07-09T16:07:40+00:00"}, {"version": "v2.7.2", "version_normalized": "*******"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "c13a40d638aeede1e8400f8c956c7f9246c05f75"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/c13a40d638aeede1e8400f8c956c7f9246c05f75", "type": "zip", "shasum": "", "reference": "c13a40d638aeede1e8400f8c956c7f9246c05f75"}, "time": "2015-06-04T20:11:48+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "ccb8ed8339cf24824f2ef35dacec30d92ff44368"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/ccb8ed8339cf24824f2ef35dacec30d92ff44368", "type": "zip", "shasum": "", "reference": "ccb8ed8339cf24824f2ef35dacec30d92ff44368"}, "time": "2015-05-15T14:02:48+00:00"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "818480c101d7263e6e8fb36b67836d6fbf2c6dec"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/818480c101d7263e6e8fb36b67836d6fbf2c6dec", "type": "zip", "shasum": "", "reference": "818480c101d7263e6e8fb36b67836d6fbf2c6dec"}, "time": "2015-05-02T15:21:08+00:00"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "3e0b09269a91264201517df15ca753e9b9329a9b"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/3e0b09269a91264201517df15ca753e9b9329a9b", "type": "zip", "shasum": "", "reference": "3e0b09269a91264201517df15ca753e9b9329a9b"}, "time": "2015-04-10T07:23:38+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Finder\\": ""}}, "target-dir": "Symfony/Component/Finder"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "203a10f928ae30176deeba33512999233181dd28"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/203a10f928ae30176deeba33512999233181dd28", "type": "zip", "shasum": "", "reference": "203a10f928ae30176deeba33512999233181dd28"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.6.11"}, "time": "2015-07-09T16:02:48+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "203a10f928ae30176deeba33512999233181dd28"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/203a10f928ae30176deeba33512999233181dd28", "type": "zip", "shasum": "", "reference": "203a10f928ae30176deeba33512999233181dd28"}, "support": {"source": "https://github.com/symfony/Finder/tree/2.6"}}, {"version": "v2.6.10", "version_normalized": "********"}, {"version": "v2.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "ffedd3e0ff8155188155e9322fe21b9ee012ac14"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/ffedd3e0ff8155188155e9322fe21b9ee012ac14", "type": "zip", "shasum": "", "reference": "ffedd3e0ff8155188155e9322fe21b9ee012ac14"}, "time": "2015-05-15T13:32:45+00:00"}, {"version": "v2.6.8", "version_normalized": "*******"}, {"version": "v2.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "704c64c8b12c8882640d5c0330a8414b1e06dc99"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/704c64c8b12c8882640d5c0330a8414b1e06dc99", "type": "zip", "shasum": "", "reference": "704c64c8b12c8882640d5c0330a8414b1e06dc99"}, "time": "2015-05-02T15:18:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "5dbe2e73a580618f5b4880fda93406eed25de251"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/5dbe2e73a580618f5b4880fda93406eed25de251", "type": "zip", "shasum": "", "reference": "5dbe2e73a580618f5b4880fda93406eed25de251"}, "time": "2015-03-30T15:54:10+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "bebc7479c566fa4f14b9bcef9e32e719eabec74e"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/bebc7479c566fa4f14b9bcef9e32e719eabec74e", "type": "zip", "shasum": "", "reference": "bebc7479c566fa4f14b9bcef9e32e719eabec74e"}, "time": "2015-03-12T10:28:44+00:00"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "16513333bca64186c01609961a2bb1b95b5e1355"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/16513333bca64186c01609961a2bb1b95b5e1355", "type": "zip", "shasum": "", "reference": "16513333bca64186c01609961a2bb1b95b5e1355"}, "time": "2015-01-03T08:01:59+00:00", "require-dev": "__unset"}, {"version": "v2.6.3", "version_normalized": "*******"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "0d3ef7f6ec55a7af5eca7914eaa0dacc04ccc721"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/0d3ef7f6ec55a7af5eca7914eaa0dacc04ccc721", "type": "zip", "shasum": "", "reference": "0d3ef7f6ec55a7af5eca7914eaa0dacc04ccc721"}, "time": "2014-12-02T20:19:20+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "d574347c652a14cfee0349f744c7880e1d9029fd"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/d574347c652a14cfee0349f744c7880e1d9029fd", "type": "zip", "shasum": "", "reference": "d574347c652a14cfee0349f744c7880e1d9029fd"}, "time": "2014-11-28T10:00:40+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "d556a66cbe5b2b81e7abc75b4066b057fbc7af94"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/d556a66cbe5b2b81e7abc75b4066b057fbc7af94", "type": "zip", "shasum": "", "reference": "d556a66cbe5b2b81e7abc75b4066b057fbc7af94"}, "support": {"source": "https://github.com/symfony/Finder/tree/master"}, "time": "2014-10-26T07:46:28+00:00"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "e527ebf47ff912a45e148b7d0b107b80ec0b3cc2"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/e527ebf47ff912a45e148b7d0b107b80ec0b3cc2", "type": "zip", "shasum": "", "reference": "e527ebf47ff912a45e148b7d0b107b80ec0b3cc2"}, "support": {"source": "https://github.com/symfony/Finder/tree/v2.5.11"}, "time": "2015-01-03T08:01:13+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}}, {"version": "v2.5.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/Finder/tree/2.5"}}, {"version": "v2.5.10", "version_normalized": "********"}, {"version": "v2.5.9", "version_normalized": "*******"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "e5487b9a23adc4be228ac550100d89c50f1ee8d5"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/e5487b9a23adc4be228ac550100d89c50f1ee8d5", "type": "zip", "shasum": "", "reference": "e5487b9a23adc4be228ac550100d89c50f1ee8d5"}, "time": "2014-12-02T20:15:53+00:00"}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "743aabbf4958663ef626e10ae3a6c7b17a0fa3bd"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/743aabbf4958663ef626e10ae3a6c7b17a0fa3bd", "type": "zip", "shasum": "", "reference": "743aabbf4958663ef626e10ae3a6c7b17a0fa3bd"}, "time": "2014-10-26T07:41:27+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "cf66df4e783e6aade319b273c9bcf9e42aa9b10f"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/cf66df4e783e6aade319b273c9bcf9e42aa9b10f", "type": "zip", "shasum": "", "reference": "cf66df4e783e6aade319b273c9bcf9e42aa9b10f"}, "time": "2014-10-01T05:50:18+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "d5033742b9a6206ef6d06e813870bca18e9205df"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/d5033742b9a6206ef6d06e813870bca18e9205df", "type": "zip", "shasum": "", "reference": "d5033742b9a6206ef6d06e813870bca18e9205df"}, "time": "2014-09-27T08:35:39+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "f40854d1a19c339c7f969f8f6d6d6e9153311c4c"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/f40854d1a19c339c7f969f8f6d6d6e9153311c4c", "type": "zip", "shasum": "", "reference": "f40854d1a19c339c7f969f8f6d6d6e9153311c4c"}, "time": "2014-09-03T09:00:14+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "090fe4eaff414d8f2171c7a4748ea868d530775f"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/090fe4eaff414d8f2171c7a4748ea868d530775f", "type": "zip", "shasum": "", "reference": "090fe4eaff414d8f2171c7a4748ea868d530775f"}, "time": "2014-07-28T13:20:46+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "576d8f69feec477067e91b6bd0367c113e76a1a0"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/576d8f69feec477067e91b6bd0367c113e76a1a0", "type": "zip", "shasum": "", "reference": "576d8f69feec477067e91b6bd0367c113e76a1a0"}, "time": "2014-07-15T14:15:12+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "614f0f46f81873d952684e355af7acceb85409b9"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/614f0f46f81873d952684e355af7acceb85409b9", "type": "zip", "shasum": "", "reference": "614f0f46f81873d952684e355af7acceb85409b9"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "307aad2c541bbdf43183043645e186ef2cd6b973"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/307aad2c541bbdf43183043645e186ef2cd6b973", "type": "zip", "shasum": "", "reference": "307aad2c541bbdf43183043645e186ef2cd6b973"}, "support": {"source": "https://github.com/symfony/Finder/tree/master"}, "time": "2014-05-22T13:47:45+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "51e3af2081d8497f6552cb3338e8a40541646773"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/51e3af2081d8497f6552cb3338e8a40541646773", "type": "zip", "shasum": "", "reference": "51e3af2081d8497f6552cb3338e8a40541646773"}, "time": "2014-04-28T05:15:04+00:00"}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "d45faef922df8438cce99b5ee226bf06c32d145e"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/d45faef922df8438cce99b5ee226bf06c32d145e", "type": "zip", "shasum": "", "reference": "d45faef922df8438cce99b5ee226bf06c32d145e"}, "time": "2014-03-03T12:53:01+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "cd03920154bc6669f64fbabb803d24cc8629964c"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/cd03920154bc6669f64fbabb803d24cc8629964c", "type": "zip", "shasum": "", "reference": "cd03920154bc6669f64fbabb803d24cc8629964c"}, "support": {"source": "https://github.com/symfony/Finder/tree/2.4"}, "time": "2014-09-27T08:35:25+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "9f4151230ad2aa56b667279d10f7a9d4617cc567"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/9f4151230ad2aa56b667279d10f7a9d4617cc567", "type": "zip", "shasum": "", "reference": "9f4151230ad2aa56b667279d10f7a9d4617cc567"}, "time": "2014-09-03T08:42:07+00:00"}, {"version": "v2.4.8", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "b495517531feb9f27c35991d0ed606ffb210faea"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/b495517531feb9f27c35991d0ed606ffb210faea", "type": "zip", "shasum": "", "reference": "b495517531feb9f27c35991d0ed606ffb210faea"}, "time": "2014-07-15T14:07:10+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "3b38b869ae35d50210a8f8733c164f3bc7e091b5"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/3b38b869ae35d50210a8f8733c164f3bc7e091b5", "type": "zip", "shasum": "", "reference": "3b38b869ae35d50210a8f8733c164f3bc7e091b5"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "46295af1b1e24304b35d5f20f1bf1ec0c2c4934a"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/46295af1b1e24304b35d5f20f1bf1ec0c2c4934a", "type": "zip", "shasum": "", "reference": "46295af1b1e24304b35d5f20f1bf1ec0c2c4934a"}, "time": "2014-05-22T13:46:01+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "25e1e7d5e7376f8a92ae3b1d714d956edf33a730"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/25e1e7d5e7376f8a92ae3b1d714d956edf33a730", "type": "zip", "shasum": "", "reference": "25e1e7d5e7376f8a92ae3b1d714d956edf33a730"}, "time": "2014-04-27T13:34:57+00:00"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "ab301c7d21f536c34ab6c2c79dba8d465104c518"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/ab301c7d21f536c34ab6c2c79dba8d465104c518", "type": "zip", "shasum": "", "reference": "ab301c7d21f536c34ab6c2c79dba8d465104c518"}, "time": "2014-03-01T17:35:04+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "b6735d1fc16da13c4c7dddfe78366a4a098cf011"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/b6735d1fc16da13c4c7dddfe78366a4a098cf011", "type": "zip", "shasum": "", "reference": "b6735d1fc16da13c4c7dddfe78366a4a098cf011"}, "time": "2014-01-07T13:28:54+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "6904345cf2b3bbab1f6d6e4ce1724cb99df9f00a"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/6904345cf2b3bbab1f6d6e4ce1724cb99df9f00a", "type": "zip", "shasum": "", "reference": "6904345cf2b3bbab1f6d6e4ce1724cb99df9f00a"}, "time": "2014-01-01T08:14:50+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "72356bf0646b11af1bae666c28a639bd8ede459f"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/72356bf0646b11af1bae666c28a639bd8ede459f", "type": "zip", "shasum": "", "reference": "72356bf0646b11af1bae666c28a639bd8ede459f"}, "time": "2013-11-26T16:40:27+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "fb8ee0aa408bd243d414a861344b9be6954839a3"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/fb8ee0aa408bd243d414a861344b9be6954839a3", "type": "zip", "shasum": "", "reference": "fb8ee0aa408bd243d414a861344b9be6954839a3"}, "support": {"source": "https://github.com/symfony/Finder/tree/master"}, "time": "2013-11-22T17:42:00+00:00"}, {"version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Finder.git", "type": "git", "reference": "e2ce3164ab58b4d54612e630571f158035ee8603"}, "dist": {"url": "https://api.github.com/repos/symfony/Finder/zipball/e2ce3164ab58b4d54612e630571f158035ee8603", "type": "zip", "shasum": "", "reference": "e2ce3164ab58b4d54612e630571f158035ee8603"}, "time": "2013-09-19T09:47:34+00:00"}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "dce4b58434fc1cbd66e3006e539bb53074dfea82"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/dce4b58434fc1cbd66e3006e539bb53074dfea82", "type": "zip", "shasum": "", "reference": "dce4b58434fc1cbd66e3006e539bb53074dfea82"}, "support": {"source": "https://github.com/symfony/finder/tree/2.3"}, "time": "2016-05-13T14:58:35+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.3.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "24cbc57da78ef7d05250c657b18b1ddcb1298bdf"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/24cbc57da78ef7d05250c657b18b1ddcb1298bdf", "type": "zip", "shasum": "", "reference": "24cbc57da78ef7d05250c657b18b1ddcb1298bdf"}, "time": "2016-03-09T12:50:31+00:00"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "302d819de11f9b77571f6c9d4c865edf4a762123"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/302d819de11f9b77571f6c9d4c865edf4a762123", "type": "zip", "shasum": "", "reference": "302d819de11f9b77571f6c9d4c865edf4a762123"}, "time": "2016-02-21T11:34:19+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "ebc6186d49e5d25399d76451a022738dc8a9476b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/ebc6186d49e5d25399d76451a022738dc8a9476b", "type": "zip", "shasum": "", "reference": "ebc6186d49e5d25399d76451a022738dc8a9476b"}, "time": "2016-01-13T17:07:01+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7fc286d733c3efbd71ed02e984554f2a25c3ea44"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7fc286d733c3efbd71ed02e984554f2a25c3ea44", "type": "zip", "shasum": "", "reference": "7fc286d733c3efbd71ed02e984554f2a25c3ea44"}, "time": "2015-12-01T11:58:24+00:00"}, {"version": "v2.3.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5baf7b74657db10c4ad8ca0934be74151268b741"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5baf7b74657db10c4ad8ca0934be74151268b741", "type": "zip", "shasum": "", "reference": "5baf7b74657db10c4ad8ca0934be74151268b741"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9f7414499657d108024d0e444d22e6f6e59c661a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9f7414499657d108024d0e444d22e6f6e59c661a", "type": "zip", "shasum": "", "reference": "9f7414499657d108024d0e444d22e6f6e59c661a"}, "time": "2015-10-11T09:37:49+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Finder\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "cedd2c7537b1c4cbb0a0714bd1a4aed3cde80b19"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/cedd2c7537b1c4cbb0a0714bd1a4aed3cde80b19", "type": "zip", "shasum": "", "reference": "cedd2c7537b1c4cbb0a0714bd1a4aed3cde80b19"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.33"}, "time": "2015-09-18T09:35:50+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "87a0f52f6ec3061499a71e225145a031afa22511"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/87a0f52f6ec3061499a71e225145a031afa22511", "type": "zip", "shasum": "", "reference": "87a0f52f6ec3061499a71e225145a031afa22511"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.32"}, "time": "2015-08-26T16:41:06+00:00"}, {"version": "v2.3.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a2d5caa65c513e4acc5a849d91b24d5d2ed2a584"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a2d5caa65c513e4acc5a849d91b24d5d2ed2a584", "type": "zip", "shasum": "", "reference": "a2d5caa65c513e4acc5a849d91b24d5d2ed2a584"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.31"}, "time": "2015-07-08T05:54:32+00:00"}, {"version": "v2.3.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8a798a71280c379b7ec45be091fbdd66eaf4a28d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8a798a71280c379b7ec45be091fbdd66eaf4a28d", "type": "zip", "shasum": "", "reference": "8a798a71280c379b7ec45be091fbdd66eaf4a28d"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.30"}, "time": "2015-05-15T13:28:34+00:00"}, {"version": "v2.3.29", "version_normalized": "********"}, {"version": "v2.3.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "7e8087452b82c5dc92cc119d841655fbf3f8f1e2"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/7e8087452b82c5dc92cc119d841655fbf3f8f1e2", "type": "zip", "shasum": "", "reference": "7e8087452b82c5dc92cc119d841655fbf3f8f1e2"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.28"}, "time": "2015-05-01T14:06:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "271c8f13ab5442868fbbc6737d33704a64b29351"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/271c8f13ab5442868fbbc6737d33704a64b29351", "type": "zip", "shasum": "", "reference": "271c8f13ab5442868fbbc6737d33704a64b29351"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.27"}, "time": "2015-03-27T22:05:05+00:00"}, {"version": "v2.3.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "97f661a53e4f5f5739f84531ff273ba2121a3831"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/97f661a53e4f5f5739f84531ff273ba2121a3831", "type": "zip", "shasum": "", "reference": "97f661a53e4f5f5739f84531ff273ba2121a3831"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.26"}, "time": "2015-03-07T19:12:23+00:00"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "37b7288b705ec885673d9255d565992f4e93d971"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/37b7288b705ec885673d9255d565992f4e93d971", "type": "zip", "shasum": "", "reference": "37b7288b705ec885673d9255d565992f4e93d971"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.25"}, "time": "2015-01-02T08:58:20+00:00", "require-dev": "__unset"}, {"version": "v2.3.24", "version_normalized": "********"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d533aea3400dc463c4d0ba9c3ecf40bd80d49dbd"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d533aea3400dc463c4d0ba9c3ecf40bd80d49dbd", "type": "zip", "shasum": "", "reference": "d533aea3400dc463c4d0ba9c3ecf40bd80d49dbd"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.23"}, "time": "2014-12-02T19:42:47+00:00"}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "414c1ab217ea8584dbf111cd6839ade524caaffb"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/414c1ab217ea8584dbf111cd6839ade524caaffb", "type": "zip", "shasum": "", "reference": "414c1ab217ea8584dbf111cd6839ade524caaffb"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.22"}, "time": "2014-10-26T07:30:58+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fc25dab213d14468c39f12d47e5b79a72b898d4d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fc25dab213d14468c39f12d47e5b79a72b898d4d", "type": "zip", "shasum": "", "reference": "fc25dab213d14468c39f12d47e5b79a72b898d4d"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.21"}, "time": "2014-10-01T05:39:06+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d396f14fd65cdf91dddca01f089088173c2d6ff7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d396f14fd65cdf91dddca01f089088173c2d6ff7", "type": "zip", "shasum": "", "reference": "d396f14fd65cdf91dddca01f089088173c2d6ff7"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.20"}, "time": "2014-09-26T04:59:53+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "f7a5a5a3b1fc0ec9a1a5b4429223a8726e0f4b55"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/f7a5a5a3b1fc0ec9a1a5b4429223a8726e0f4b55", "type": "zip", "shasum": "", "reference": "f7a5a5a3b1fc0ec9a1a5b4429223a8726e0f4b55"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.19"}, "time": "2014-08-31T04:03:32+00:00"}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4ad9d2ca4ca54f9e454a259b30a0277ced57ce92"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4ad9d2ca4ca54f9e454a259b30a0277ced57ce92", "type": "zip", "shasum": "", "reference": "4ad9d2ca4ca54f9e454a259b30a0277ced57ce92"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.18"}, "time": "2014-07-10T08:55:37+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c5502ec022f0bc28c9f0bfa8b299b21a0d1ce415"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c5502ec022f0bc28c9f0bfa8b299b21a0d1ce415", "type": "zip", "shasum": "", "reference": "c5502ec022f0bc28c9f0bfa8b299b21a0d1ce415"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "32949721cc76afd75e4ac60d14ac3cf55b10a768"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/32949721cc76afd75e4ac60d14ac3cf55b10a768", "type": "zip", "shasum": "", "reference": "32949721cc76afd75e4ac60d14ac3cf55b10a768"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.16"}, "time": "2014-05-22T13:42:36+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "10b6133782cb970ec5e4ea54cc61ac3ef76380b4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/10b6133782cb970ec5e4ea54cc61ac3ef76380b4", "type": "zip", "shasum": "", "reference": "10b6133782cb970ec5e4ea54cc61ac3ef76380b4"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.13"}, "time": "2014-04-23T21:00:27+00:00"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "09ca26eac83a9510860ae751286276d727d48e3c"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/09ca26eac83a9510860ae751286276d727d48e3c", "type": "zip", "shasum": "", "reference": "09ca26eac83a9510860ae751286276d727d48e3c"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.12"}, "time": "2014-03-01T17:25:29+00:00"}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "df41b68d0795949c6edcf3fb331c8147b7cc4201"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/df41b68d0795949c6edcf3fb331c8147b7cc4201", "type": "zip", "shasum": "", "reference": "df41b68d0795949c6edcf3fb331c8147b7cc4201"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.11"}, "time": "2014-02-20T15:15:12+00:00"}, {"version": "v2.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c39a28f2e6bcea817dd696993d528b67917f324e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c39a28f2e6bcea817dd696993d528b67917f324e", "type": "zip", "shasum": "", "reference": "c39a28f2e6bcea817dd696993d528b67917f324e"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.10"}, "time": "2014-01-07T13:19:25+00:00"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "143131ec3f123dcd505a84391a9ba60545881991"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/143131ec3f123dcd505a84391a9ba60545881991", "type": "zip", "shasum": "", "reference": "143131ec3f123dcd505a84391a9ba60545881991"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.9"}, "time": "2014-01-01T07:52:14+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fc0fbd5a4eec06cad631311726e7d939e770d82d"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fc0fbd5a4eec06cad631311726e7d939e770d82d", "type": "zip", "shasum": "", "reference": "fc0fbd5a4eec06cad631311726e7d939e770d82d"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.8"}, "time": "2013-11-25T14:49:41+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "a175521f680b178e63c5d0ab87c6b046c0990c3f"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/a175521f680b178e63c5d0ab87c6b046c0990c3f", "type": "zip", "shasum": "", "reference": "a175521f680b178e63c5d0ab87c6b046c0990c3f"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.7"}, "time": "2013-09-19T09:45:20+00:00"}, {"version": "v2.3.6", "version_normalized": "*******"}, {"version": "v2.3.5", "version_normalized": "*******"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "4a0fee5b86f5bbd9dfdc11ec124eba2915737ce1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/4a0fee5b86f5bbd9dfdc11ec124eba2915737ce1", "type": "zip", "shasum": "", "reference": "4a0fee5b86f5bbd9dfdc11ec124eba2915737ce1"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.4"}, "time": "2013-08-13T20:18:00+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "b251476db261edb54bb1d7c6b8be6ae49ff9a371"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/b251476db261edb54bb1d7c6b8be6ae49ff9a371", "type": "zip", "shasum": "", "reference": "b251476db261edb54bb1d7c6b8be6ae49ff9a371"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.3"}, "time": "2013-07-21T12:12:18+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "71ced0f8fa27461da26c245ddf6b0e49a6a6c2c4"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/71ced0f8fa27461da26c245ddf6b0e49a6a6c2c4", "type": "zip", "shasum": "", "reference": "71ced0f8fa27461da26c245ddf6b0e49a6a6c2c4"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.2"}, "time": "2013-07-01T12:17:23+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "cbe6c533f9ca17bbb5844252147fd8386cc234c0"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/cbe6c533f9ca17bbb5844252147fd8386cc234c0", "type": "zip", "shasum": "", "reference": "cbe6c533f9ca17bbb5844252147fd8386cc234c0"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.3.1"}, "time": "2013-06-02T12:05:51+00:00"}, {"version": "v2.3.0", "version_normalized": "*******"}, {"version": "v2.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0fbb7672173a7caab9d266dd26de94b6bc044918"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0fbb7672173a7caab9d266dd26de94b6bc044918", "type": "zip", "shasum": "", "reference": "0fbb7672173a7caab9d266dd26de94b6bc044918"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.11"}, "time": "2013-11-25T10:21:43+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c5bf144230f8f7384d60c05618a7282f5295337e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c5bf144230f8f7384d60c05618a7282f5295337e", "type": "zip", "shasum": "", "reference": "c5bf144230f8f7384d60c05618a7282f5295337e"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.10"}, "time": "2013-09-18T07:27:26+00:00"}, {"version": "v2.2.9", "version_normalized": "*******"}, {"version": "v2.2.8", "version_normalized": "*******"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "5ac3486e27d79116dfe67ae17521f19a033abc58"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/5ac3486e27d79116dfe67ae17521f19a033abc58", "type": "zip", "shasum": "", "reference": "5ac3486e27d79116dfe67ae17521f19a033abc58"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.6"}, "time": "2013-08-13T19:56:24+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e2e53adf642ea31e088860967e29b3f71700c39a"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e2e53adf642ea31e088860967e29b3f71700c39a", "type": "zip", "shasum": "", "reference": "e2e53adf642ea31e088860967e29b3f71700c39a"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.5"}, "time": "2013-07-01T12:15:46+00:00"}, {"version": "v2.2.4", "version_normalized": "*******"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "95460adb3a71b0cf64236b608624412e3f554158"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/95460adb3a71b0cf64236b608624412e3f554158", "type": "zip", "shasum": "", "reference": "95460adb3a71b0cf64236b608624412e3f554158"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.3"}, "time": "2013-06-10T18:35:46+00:00"}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "9b21ea89de1b02e3cf1db5941bff57627793d6dd"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/9b21ea89de1b02e3cf1db5941bff57627793d6dd", "type": "zip", "shasum": "", "reference": "9b21ea89de1b02e3cf1db5941bff57627793d6dd"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.2"}, "time": "2013-05-27T20:26:32+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "18f6ec5aca2415559879cfe75ee09af61d45b7b1"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/18f6ec5aca2415559879cfe75ee09af61d45b7b1", "type": "zip", "shasum": "", "reference": "18f6ec5aca2415559879cfe75ee09af61d45b7b1"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.1"}, "time": "2013-04-01T07:51:50+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "2feb8d33876f64801bd1b14bdce829b314e2c418"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/2feb8d33876f64801bd1b14bdce829b314e2c418", "type": "zip", "shasum": "", "reference": "2feb8d33876f64801bd1b14bdce829b314e2c418"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.2.0"}, "time": "2013-02-28T14:06:36+00:00"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "0dbc61194b58bc513e003789853ddfe0aea8cf33"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/0dbc61194b58bc513e003789853ddfe0aea8cf33", "type": "zip", "shasum": "", "reference": "0dbc61194b58bc513e003789853ddfe0aea8cf33"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.13"}, "time": "2013-05-25T15:47:15+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Finder": ""}}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********"}, {"version": "v2.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "69858f868c0caabe3d859c448f98d249541c0489"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/69858f868c0caabe3d859c448f98d249541c0489", "type": "zip", "shasum": "", "reference": "69858f868c0caabe3d859c448f98d249541c0489"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.10"}, "time": "2013-03-06T19:26:55+00:00"}, {"version": "v2.1.9", "version_normalized": "*******"}, {"version": "v2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "c66d4fc4d39d34de3b9baac6a2ecd2461a282494"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/c66d4fc4d39d34de3b9baac6a2ecd2461a282494", "type": "zip", "shasum": "", "reference": "c66d4fc4d39d34de3b9baac6a2ecd2461a282494"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.8"}, "time": "2013-01-09T08:51:07+00:00"}, {"version": "v2.1.7", "version_normalized": "*******"}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d9cc0a82455be6a49ffbfe2fb014da78d0b8c354"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d9cc0a82455be6a49ffbfe2fb014da78d0b8c354", "type": "zip", "shasum": "", "reference": "d9cc0a82455be6a49ffbfe2fb014da78d0b8c354"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.6"}, "time": "2012-12-10T12:46:54+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "24e814560125249010dd08717f4d17079c061e98"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/24e814560125249010dd08717f4d17079c061e98", "type": "zip", "shasum": "", "reference": "24e814560125249010dd08717f4d17079c061e98"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.4"}, "time": "2012-11-08T09:51:48+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e5dac3c0c277c1b5266dcb467c49b29dc7fb6a9b"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e5dac3c0c277c1b5266dcb467c49b29dc7fb6a9b", "type": "zip", "shasum": "", "reference": "e5dac3c0c277c1b5266dcb467c49b29dc7fb6a9b"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.3"}, "time": "2012-10-20T07:10:30+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "e2e9f746294f60aed3bb5bc20da24e23b92f0479"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/e2e9f746294f60aed3bb5bc20da24e23b92f0479", "type": "zip", "shasum": "", "reference": "e2e9f746294f60aed3bb5bc20da24e23b92f0479"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.1.2"}, "time": "2012-08-22T13:48:41+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******"}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d0419c569e7ffbc35370db8b7c37d66d42af3c68"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d0419c569e7ffbc35370db8b7c37d66d42af3c68", "type": "zip", "shasum": "", "reference": "d0419c569e7ffbc35370db8b7c37d66d42af3c68"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.25"}, "time": "2013-01-04T16:59:43+00:00", "require": {"php": ">=5.3.2"}, "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********"}, {"version": "v2.0.23", "version_normalized": "********"}, {"version": "v2.0.22", "version_normalized": "********"}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "303e6cb6ec335256bd05f5caf8e6703a4f840690"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/303e6cb6ec335256bd05f5caf8e6703a4f840690", "type": "zip", "shasum": "", "reference": "303e6cb6ec335256bd05f5caf8e6703a4f840690"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.21"}, "time": "2012-07-09T12:43:50+00:00"}, {"version": "v2.0.20", "version_normalized": "********"}, {"version": "v2.0.19", "version_normalized": "********"}, {"version": "v2.0.18", "version_normalized": "********"}, {"version": "v2.0.17", "version_normalized": "********"}, {"version": "v2.0.16", "version_normalized": "********"}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "d5663232e0d21083f6d7457bf6de3a6af720001e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/d5663232e0d21083f6d7457bf6de3a6af720001e", "type": "zip", "shasum": "", "reference": "d5663232e0d21083f6d7457bf6de3a6af720001e"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.15"}, "time": "2012-05-18T17:37:58+00:00"}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "fa0c5b0ebadbc8fe23faa9ebdbc687e7b25db208"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/fa0c5b0ebadbc8fe23faa9ebdbc687e7b25db208", "type": "zip", "shasum": "", "reference": "fa0c5b0ebadbc8fe23faa9ebdbc687e7b25db208"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.14"}, "time": "2012-05-15T16:56:32+00:00"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "42e22b1a62010e2a78088c3929d9d0034751bfb8"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/42e22b1a62010e2a78088c3929d9d0034751bfb8", "type": "zip", "shasum": "", "reference": "42e22b1a62010e2a78088c3929d9d0034751bfb8"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.13"}, "time": "2012-04-08T17:39:04+00:00"}, {"version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "51bd7df92b39d9abb464a09e96bee964d3661cae"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/51bd7df92b39d9abb464a09e96bee964d3661cae", "type": "zip", "shasum": "", "reference": "51bd7df92b39d9abb464a09e96bee964d3661cae"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.12"}, "time": "2012-02-24T08:04:00+00:00"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "8560867631b561c2c9249fce66e654ee29c8b503"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/8560867631b561c2c9249fce66e654ee29c8b503", "type": "zip", "shasum": "", "reference": "8560867631b561c2c9249fce66e654ee29c8b503"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.10"}, "time": "2012-01-05T13:51:20+00:00"}, {"version": "v2.0.9", "version_normalized": "*******"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "785273df63b27481a705841a37463d677b78cd5e"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/785273df63b27481a705841a37463d677b78cd5e", "type": "zip", "shasum": "", "reference": "785273df63b27481a705841a37463d677b78cd5e"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.7"}, "time": "2011-11-17T05:58:47+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "bdef5dc407b7c258337f3a1d83ae6ade48404c65"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/bdef5dc407b7c258337f3a1d83ae6ade48404c65", "type": "zip", "shasum": "", "reference": "bdef5dc407b7c258337f3a1d83ae6ade48404c65"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.6"}, "time": "2011-11-05T13:34:29+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "68c9ccbc39c602a419632d8cd603f82d8a1f98e7"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/68c9ccbc39c602a419632d8cd603f82d8a1f98e7", "type": "zip", "shasum": "", "reference": "68c9ccbc39c602a419632d8cd603f82d8a1f98e7"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/finder.git", "type": "git", "reference": "eac81a6c7143739d14d4e8c22f18e75797983656"}, "dist": {"url": "https://api.github.com/repos/symfony/finder/zipball/eac81a6c7143739d14d4e8c22f18e75797983656", "type": "zip", "shasum": "", "reference": "eac81a6c7143739d14d4e8c22f18e75797983656"}, "support": {"source": "https://github.com/symfony/finder/tree/v2.0.4"}, "time": "2011-09-26T22:55:43+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 29 May 2025 07:49:06 GMT"}