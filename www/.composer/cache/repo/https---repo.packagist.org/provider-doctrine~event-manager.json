{"minified": "composer/2.0", "packages": {"doctrine/event-manager": [{"name": "doctrine/event-manager", "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "keywords": ["event", "events", "event dispatcher", "event manager", "event system"], "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "version": "2.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "type": "zip", "shasum": "", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "type": "library", "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "conflict": {"doctrine/common": "<2.9"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "750671534e0241a7c50ea5b43f67e23eb5c96f32"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/750671534e0241a7c50ea5b43f67e23eb5c96f32", "type": "zip", "shasum": "", "reference": "750671534e0241a7c50ea5b43f67e23eb5c96f32"}, "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.0"}, "time": "2022-10-12T20:59:15+00:00", "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.28"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "type": "zip", "shasum": "", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "time": "2022-10-12T20:51:15+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/deprecations": "^0.5.3 || ^1"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "eb2ecf80e3093e8f3c2769ac838e27d8ede8e683"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/eb2ecf80e3093e8f3c2769ac838e27d8ede8e683", "type": "zip", "shasum": "", "reference": "eb2ecf80e3093e8f3c2769ac838e27d8ede8e683"}, "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.2"}, "time": "2022-07-27T22:18:11+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "~1.4.10 || ^1.5.4", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "type": "zip", "shasum": "", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "time": "2020-05-29T18:28:51+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "conflict": {"doctrine/common": "<2.9@dev"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "629572819973f13486371cb611386eb17851e85c"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/629572819973f13486371cb611386eb17851e85c", "type": "zip", "shasum": "", "reference": "629572819973f13486371cb611386eb17851e85c"}, "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.0"}, "time": "2019-11-10T09:48:07+00:00", "require": {"php": "^7.1"}, "funding": "__unset"}, {"description": "Doctrine Event Manager component", "keywords": ["eventmanager", "event", "eventdispatcher"], "version": "v1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/event-manager.git", "type": "git", "reference": "a520bc093a0170feeb6b14e9d83f3a14452e64b3"}, "dist": {"url": "https://api.github.com/repos/doctrine/event-manager/zipball/a520bc093a0170feeb6b14e9d83f3a14452e64b3", "type": "zip", "shasum": "", "reference": "a520bc093a0170feeb6b14e9d83f3a14452e64b3"}, "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/master"}, "time": "2018-06-11T11:59:03+00:00", "require-dev": {"phpunit/phpunit": "^7.0", "doctrine/coding-standard": "^4.0"}}]}, "security-advisories": [], "last-modified": "Wed, 22 May 2024 20:51:05 GMT"}