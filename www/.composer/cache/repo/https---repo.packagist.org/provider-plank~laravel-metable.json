{"minified": "composer/2.0", "packages": {"plank/laravel-metable": [{"name": "plank/laravel-metable", "description": "A package for attaching arbitrary data to Eloquent models", "keywords": [], "homepage": "", "version": "6.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "7b48822ab89a4a4883dfd5621447de4037f99026"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/7b48822ab89a4a4883dfd5621447de4037f99026", "type": "zip", "shasum": "", "reference": "7b48822ab89a4a4883dfd5621447de4037f99026"}, "type": "library", "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.2.0"}, "funding": [], "time": "2025-02-20T02:31:25+00:00", "autoload": {"psr-4": {"Plank\\Metable\\": "src/"}}, "extra": {"laravel": {"providers": ["Plank\\Metable\\MetableServiceProvider"]}}, "require": {"php": ">=8.1", "ext-json": "*", "illuminate/support": "^10.48.25|^11.34.0|^12.0", "illuminate/database": "^10.48.25|^11.34.0|^12.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"laravel/legacy-factories": "^1.4.0", "orchestra/testbench": "^9.0|^10.0", "phpunit/phpunit": "^10.5|^11.5.3", "guzzlehttp/guzzle": "^7.9", "guzzlehttp/promises": "^1.5|^2.0", "mockery/mockery": "^1.6.0", "nesbot/carbon": "^2.72.0|^3.8.4", "php-coveralls/php-coveralls": "^2.7.0"}}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "9c91fff0bcb2318f20837dc96f8afbfe1c37776e"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/9c91fff0bcb2318f20837dc96f8afbfe1c37776e", "type": "zip", "shasum": "", "reference": "9c91fff0bcb2318f20837dc96f8afbfe1c37776e"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.1.0"}, "time": "2024-12-05T02:34:26+00:00", "require": {"php": ">=8.1", "ext-json": "*", "illuminate/support": "^10.48.25|^11.34.0", "illuminate/database": "^10.48.25|^11.34.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"laravel/legacy-factories": "^1.4.0", "orchestra/testbench": "^9.0", "phpunit/phpunit": "^10.5", "guzzlehttp/guzzle": "^7.9", "guzzlehttp/promises": "^1.5", "mockery/mockery": "^1.6.0", "nesbot/carbon": "^2.72.0", "php-coveralls/php-coveralls": "^2.7.0"}}, {"version": "6.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "de76793a55b8bb19eeb181b05170b887192fbf0f"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/de76793a55b8bb19eeb181b05170b887192fbf0f", "type": "zip", "shasum": "", "reference": "de76793a55b8bb19eeb181b05170b887192fbf0f"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.0.4"}, "time": "2024-05-16T02:18:34+00:00", "require": {"php": ">=8.1", "ext-json": "*", "illuminate/support": "^10.10|^11.0", "illuminate/database": "^10.10|^11.0", "phpoption/phpoption": "^1.8"}, "require-dev": {"symfony/symfony": "^6.1|^7.0", "laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^8.0|^9.0", "phpunit/phpunit": "^10.5", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4", "mockery/mockery": "^1.4.2", "nesbot/carbon": "^2.62.1", "php-coveralls/php-coveralls": "^2.7.0"}}, {"version": "6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "b59bd146d43753b6557f2ba6e17b2517767ce2ce"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/b59bd146d43753b6557f2ba6e17b2517767ce2ce", "type": "zip", "shasum": "", "reference": "b59bd146d43753b6557f2ba6e17b2517767ce2ce"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.0.3"}, "time": "2024-04-29T13:33:03+00:00", "require-dev": {"symfony/symfony": "^6.1|^7.0", "laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^8.0|^9.0", "phpunit/phpunit": "^10.5", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4", "mockery/mockery": "^1.4.2", "nesbot/carbon": "^2.62.1", "php-coveralls/php-coveralls": "^2.5.2"}}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "d152c668f8457446bd7a585f368661bc9775ea9d"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/d152c668f8457446bd7a585f368661bc9775ea9d", "type": "zip", "shasum": "", "reference": "d152c668f8457446bd7a585f368661bc9775ea9d"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.0.2"}, "time": "2024-04-29T02:44:32+00:00"}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "27fabbf31f4b440f6c16f8a65582a5d0c5946bc8"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/27fabbf31f4b440f6c16f8a65582a5d0c5946bc8", "type": "zip", "shasum": "", "reference": "27fabbf31f4b440f6c16f8a65582a5d0c5946bc8"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.0.1"}, "time": "2024-04-29T02:32:44+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "e759af2ce1433a8354575e51c3c23fa01730afd8"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/e759af2ce1433a8354575e51c3c23fa01730afd8", "type": "zip", "shasum": "", "reference": "e759af2ce1433a8354575e51c3c23fa01730afd8"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/6.0.0"}, "time": "2024-04-29T02:26:31+00:00"}, {"version": "5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "cd7f530fe1857fb6a406c198c5991a33ca5848a3"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/cd7f530fe1857fb6a406c198c5991a33ca5848a3", "type": "zip", "shasum": "", "reference": "cd7f530fe1857fb6a406c198c5991a33ca5848a3"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.4.0"}, "time": "2023-02-16T18:38:57+00:00", "require": {"php": ">=7.3.0", "ext-json": "*", "illuminate/support": "^6.20.42|^8.22.1|^9.0|^10.0", "illuminate/database": "^6.20.42|^8.22.1|^9.0|^10.0", "phpoption/phpoption": "^1.8"}, "require-dev": {"symfony/symfony": "^5.4.1|^6.1", "laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^5.20|^6.23|^7.0|^8.0", "phpunit/phpunit": "^9.5.11", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4", "mockery/mockery": "^1.4.2", "php-coveralls/php-coveralls": "^2.4.2"}}, {"version": "5.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "36d3cd5c5a91aba40d15efee501a56a94f8432d8"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/36d3cd5c5a91aba40d15efee501a56a94f8432d8", "type": "zip", "shasum": "", "reference": "36d3cd5c5a91aba40d15efee501a56a94f8432d8"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.3.1"}, "time": "2022-11-18T15:53:17+00:00", "require": {"php": ">=7.3.0", "ext-json": "*", "illuminate/support": "^6.20.42|^8.22.1|^9.0", "illuminate/database": "^6.20.42|^8.22.1|^9.0", "phpoption/phpoption": "^1.8"}, "require-dev": {"symfony/symfony": "^5.4.1|^6.1", "laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^5.20|^6.23|^7.0", "phpunit/phpunit": "^9.5.11", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4", "mockery/mockery": "^1.4.2", "php-coveralls/php-coveralls": "^2.4.2"}}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "f576ecee1c5a9239a3eb27acdb63ef521bbc5d43"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/f576ecee1c5a9239a3eb27acdb63ef521bbc5d43", "type": "zip", "shasum": "", "reference": "f576ecee1c5a9239a3eb27acdb63ef521bbc5d43"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.3.0"}, "time": "2022-10-22T15:21:29+00:00"}, {"version": "5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "c9c088a868b5bd987c20a9f970160b9fbf9c51c3"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/c9c088a868b5bd987c20a9f970160b9fbf9c51c3", "type": "zip", "shasum": "", "reference": "c9c088a868b5bd987c20a9f970160b9fbf9c51c3"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.2.1"}, "time": "2022-08-19T01:14:38+00:00"}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "f6bc5f0f8c494b9ec8493a5573bae9231bb99492"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/f6bc5f0f8c494b9ec8493a5573bae9231bb99492", "type": "zip", "shasum": "", "reference": "f6bc5f0f8c494b9ec8493a5573bae9231bb99492"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.2.0"}, "time": "2022-02-09T03:32:14+00:00", "require": {"php": ">=7.3.0", "ext-json": "*", "illuminate/support": "^6.20.42|^8.22.1|^9.0", "illuminate/database": "^6.20.42|^8.22.1|^9.0", "phpoption/phpoption": "1.8.1"}}, {"version": "5.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "f0dba1ed39f9e2aa768e1d0fefb82b9507a5698a"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/f0dba1ed39f9e2aa768e1d0fefb82b9507a5698a", "type": "zip", "shasum": "", "reference": "f0dba1ed39f9e2aa768e1d0fefb82b9507a5698a"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.1.1"}, "time": "2021-12-26T18:08:20+00:00", "require": {"php": ">=7.3.0", "ext-json": "*", "illuminate/support": "^6.20.42|^8.22.1", "illuminate/database": "^6.20.42|^8.22.1", "phpoption/phpoption": "1.8.1"}, "require-dev": {"symfony/symfony": "^5.4.1|^6.1", "laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^5.20|^6.23", "phpunit/phpunit": "^9.5.11", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4", "mockery/mockery": "^1.4.2", "php-coveralls/php-coveralls": "^2.4.2"}}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "26633aa7384f1d536edc08c8845063fb3c7d668b"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/26633aa7384f1d536edc08c8845063fb3c7d668b", "type": "zip", "shasum": "", "reference": "26633aa7384f1d536edc08c8845063fb3c7d668b"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.1.0"}, "time": "2021-10-25T01:23:59+00:00", "require": {"php": ">=7.3.0", "ext-json": "*", "illuminate/support": "^6.0|^7.0|^8.0", "illuminate/database": "^6.20.12|^7.30.3|^8.22.1"}, "require-dev": {"laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^5.9|^6.6", "phpunit/phpunit": "^9.5", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4", "mockery/mockery": "^1.4.2", "php-coveralls/php-coveralls": "^2.4.2"}}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "b319682561505f86346dce42ffbd7408aae032db"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/b319682561505f86346dce42ffbd7408aae032db", "type": "zip", "shasum": "", "reference": "b319682561505f86346dce42ffbd7408aae032db"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.0.2"}, "time": "2021-10-03T17:33:25+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "5ba7637474518720354c4506d4c9177aa5d35991"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/5ba7637474518720354c4506d4c9177aa5d35991", "type": "zip", "shasum": "", "reference": "5ba7637474518720354c4506d4c9177aa5d35991"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.0.1"}, "time": "2021-09-24T01:22:31+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "68924b4fb2b65073db30fedfc8a1d6e302773d2b"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/68924b4fb2b65073db30fedfc8a1d6e302773d2b", "type": "zip", "shasum": "", "reference": "68924b4fb2b65073db30fedfc8a1d6e302773d2b"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/5.0.0"}, "time": "2021-02-11T18:10:30+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "a1ae0bffdb680b2dd0e079e489bb728bfd68d118"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/a1ae0bffdb680b2dd0e079e489bb728bfd68d118", "type": "zip", "shasum": "", "reference": "a1ae0bffdb680b2dd0e079e489bb728bfd68d118"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/4.1.0"}, "time": "2020-11-01T14:31:56+00:00", "require": {"php": ">=7.3.0", "ext-json": "*", "illuminate/support": "^6.0|^7.0|^8.0", "illuminate/database": "^6.0|^7.0|^8.0"}, "require-dev": {"laravel/legacy-factories": "^1.0.4", "orchestra/testbench": "^4.0|^5.0|^6.0", "phpunit/phpunit": "^8.2.4|^9.0", "php-coveralls/php-coveralls": "^2.1"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "1f34a0f2817a9db38569f11ea42ecea134f43bfa"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/1f34a0f2817a9db38569f11ea42ecea134f43bfa", "type": "zip", "shasum": "", "reference": "1f34a0f2817a9db38569f11ea42ecea134f43bfa"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/4.0.0"}, "time": "2020-10-15T00:26:51+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "49dbb430380a71b981dc7ea31a92201abe985a2c"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/49dbb430380a71b981dc7ea31a92201abe985a2c", "type": "zip", "shasum": "", "reference": "49dbb430380a71b981dc7ea31a92201abe985a2c"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/3.0.0"}, "time": "2020-09-13T21:29:12+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "21f95c9f376d09a3665fd0c0c517643d7d9093eb"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/21f95c9f376d09a3665fd0c0c517643d7d9093eb", "type": "zip", "shasum": "", "reference": "21f95c9f376d09a3665fd0c0c517643d7d9093eb"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/master"}, "time": "2020-06-16T03:02:17+00:00", "require": {"php": ">=7.2.0", "ext-json": "*", "illuminate/support": "^5.6|^6.0|^7.0", "illuminate/database": "^5.6|^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^3.3|^4.0|^5.0", "phpunit/phpunit": "^8.0|^9.0", "php-coveralls/php-coveralls": "^2.1"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "1c60c28f9601a16c352412f40f910f3cb01c78ab"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/1c60c28f9601a16c352412f40f910f3cb01c78ab", "type": "zip", "shasum": "", "reference": "1c60c28f9601a16c352412f40f910f3cb01c78ab"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/2.1.0"}, "time": "2020-03-07T00:41:23+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "47e0a93bf2d9a27051ebfb3fc54a74c500bfc7cb"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/47e0a93bf2d9a27051ebfb3fc54a74c500bfc7cb", "type": "zip", "shasum": "", "reference": "47e0a93bf2d9a27051ebfb3fc54a74c500bfc7cb"}, "support": {"issues": "https://github.com/plank/laravel-metable/issues", "source": "https://github.com/plank/laravel-metable/tree/master"}, "time": "2020-03-05T05:29:11+00:00", "require": {"php": ">=7.2.0", "ext-json": "*", "illuminate/support": "^5.6|^6.0", "illuminate/database": "^5.6|^6.0"}, "require-dev": {"orchestra/testbench": "^3.3|^4.0", "phpunit/phpunit": "^8.0", "php-coveralls/php-coveralls": "^2.1"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "d4b29b2d7c77e4b9720d99d3ad45e956f1042c70"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/d4b29b2d7c77e4b9720d99d3ad45e956f1042c70", "type": "zip", "shasum": "", "reference": "d4b29b2d7c77e4b9720d99d3ad45e956f1042c70"}, "time": "2019-09-13T01:23:25+00:00", "funding": "__unset"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "414aa3707d8f91170e505ed7228bb74e31d8e35b"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/414aa3707d8f91170e505ed7228bb74e31d8e35b", "type": "zip", "shasum": "", "reference": "414aa3707d8f91170e505ed7228bb74e31d8e35b"}, "time": "2018-05-15T13:15:21+00:00", "require": {"php": ">=7.0.0", "illuminate/support": "~5.2", "illuminate/database": "~5.2"}, "require-dev": {"orchestra/testbench": "~3.2", "phpunit/phpunit": "~5.7", "satooshi/php-coveralls": "^1.0.1"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "5dc080a99e8417a058dba7f3d0574732ca9a8c7b"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/5dc080a99e8417a058dba7f3d0574732ca9a8c7b", "type": "zip", "shasum": "", "reference": "5dc080a99e8417a058dba7f3d0574732ca9a8c7b"}, "time": "2017-08-20T22:46:51+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "5451280682a3f13b40ec6d3c22b87e7658e9de66"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/5451280682a3f13b40ec6d3c22b87e7658e9de66", "type": "zip", "shasum": "", "reference": "5451280682a3f13b40ec6d3c22b87e7658e9de66"}, "time": "2017-03-21T15:00:08+00:00", "extra": "__unset"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "372282b24db28985a5b64809ea01810aa5bd3772"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/372282b24db28985a5b64809ea01810aa5bd3772", "type": "zip", "shasum": "", "reference": "372282b24db28985a5b64809ea01810aa5bd3772"}, "time": "2017-02-03T19:30:10+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "ceeae548b50666f16041ac0405364fd14d116e6b"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/ceeae548b50666f16041ac0405364fd14d116e6b", "type": "zip", "shasum": "", "reference": "ceeae548b50666f16041ac0405364fd14d116e6b"}, "time": "2017-01-30T16:49:19+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/plank/laravel-metable.git", "type": "git", "reference": "1e2023e5104ac8fe1d5a17ac1d1d61e1f3434d3b"}, "dist": {"url": "https://api.github.com/repos/plank/laravel-metable/zipball/1e2023e5104ac8fe1d5a17ac1d1d61e1f3434d3b", "type": "zip", "shasum": "", "reference": "1e2023e5104ac8fe1d5a17ac1d1d61e1f3434d3b"}, "time": "2017-01-16T19:28:30+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 28 Feb 2025 00:52:18 GMT"}