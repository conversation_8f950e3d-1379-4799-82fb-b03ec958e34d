{"minified": "composer/2.0", "packages": {"league/flysystem-local": [{"name": "league/flysystem-local", "description": "Local filesystem adapter for Flysystem.", "keywords": ["file", "filesystem", "files", "local", "Flysystem"], "homepage": "", "version": "3.30.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/6691915f77c7fb69adfb87dcd550052dc184ee10", "type": "zip", "shasum": "", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10"}, "type": "library", "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.30.0"}, "funding": [], "time": "2025-05-21T10:34:19+00:00", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "require": {"php": "^8.0.2", "ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0"}}, {"version": "3.29.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/e0e8d52ce4b2ed154148453d321e97c8e931bd27", "type": "zip", "shasum": "", "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27"}, "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.29.0"}, "time": "2024-08-09T21:24:39+00:00"}, {"version": "3.28.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "13f22ea8be526ea58c2ddff9e158ef7c296e4f40"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/13f22ea8be526ea58c2ddff9e158ef7c296e4f40", "type": "zip", "shasum": "", "reference": "13f22ea8be526ea58c2ddff9e158ef7c296e4f40"}, "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.28.0"}, "time": "2024-05-06T20:05:52+00:00"}, {"version": "3.25.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "61a6a90d6e999e4ddd9ce5adb356de0939060b92"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/61a6a90d6e999e4ddd9ce5adb356de0939060b92", "type": "zip", "shasum": "", "reference": "61a6a90d6e999e4ddd9ce5adb356de0939060b92"}, "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.25.1"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2024-03-15T19:58:44+00:00"}, {"version": "3.23.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "b884d2bf9b53bb4804a56d2df4902bb51e253f00"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/b884d2bf9b53bb4804a56d2df4902bb51e253f00", "type": "zip", "shasum": "", "reference": "b884d2bf9b53bb4804a56d2df4902bb51e253f00"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.23.1"}, "time": "2024-01-26T18:25:23+00:00"}, {"version": "3.23.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "5cf046ba5f059460e86a997c504dd781a39a109b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/5cf046ba5f059460e86a997c504dd781a39a109b", "type": "zip", "shasum": "", "reference": "5cf046ba5f059460e86a997c504dd781a39a109b"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.23.0"}, "time": "2023-12-04T10:14:46+00:00"}, {"version": "3.22.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "42dfb4eaafc4accd248180f0dd66f17073b40c4c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/42dfb4eaafc4accd248180f0dd66f17073b40c4c", "type": "zip", "shasum": "", "reference": "42dfb4eaafc4accd248180f0dd66f17073b40c4c"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.22.0"}, "time": "2023-11-18T20:52:53+00:00"}, {"version": "3.21.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "470eb1c09eaabd49ebd908ae06f23983ba3ecfe7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/470eb1c09eaabd49ebd908ae06f23983ba3ecfe7", "type": "zip", "shasum": "", "reference": "470eb1c09eaabd49ebd908ae06f23983ba3ecfe7"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.21.0"}, "time": "2023-11-18T13:41:42+00:00"}, {"version": "3.20.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "0be6ac0a09ce672ea81ba4119d85a62da85b389b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/0be6ac0a09ce672ea81ba4119d85a62da85b389b", "type": "zip", "shasum": "", "reference": "0be6ac0a09ce672ea81ba4119d85a62da85b389b"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.20.0"}, "time": "2023-11-14T11:54:45+00:00"}, {"version": "3.19.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "8d868217f9eeb4e9a7320db5ccad825e9a7a4076"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/8d868217f9eeb4e9a7320db5ccad825e9a7a4076", "type": "zip", "shasum": "", "reference": "8d868217f9eeb4e9a7320db5ccad825e9a7a4076"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.19.0"}, "time": "2023-11-06T20:35:28+00:00"}, {"version": "3.18.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "e7381ef7643f658b87efb7dbe98fe538fb1bbf32"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/e7381ef7643f658b87efb7dbe98fe538fb1bbf32", "type": "zip", "shasum": "", "reference": "e7381ef7643f658b87efb7dbe98fe538fb1bbf32"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.18.0"}, "time": "2023-10-19T20:07:13+00:00"}, {"version": "3.16.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "ec7383f25642e6fd4bb0c9554fc2311245391781"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/ec7383f25642e6fd4bb0c9554fc2311245391781", "type": "zip", "shasum": "", "reference": "ec7383f25642e6fd4bb0c9554fc2311245391781"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.16.0"}, "time": "2023-08-30T10:23:59+00:00"}, {"version": "3.15.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem-local.git", "type": "git", "reference": "543f64c397fefdf9cfeac443ffb6beff602796b3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/543f64c397fefdf9cfeac443ffb6beff602796b3", "type": "zip", "shasum": "", "reference": "543f64c397fefdf9cfeac443ffb6beff602796b3"}, "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.15.0"}, "time": "2023-05-02T20:02:14+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 25 Jun 2025 13:30:49 GMT"}