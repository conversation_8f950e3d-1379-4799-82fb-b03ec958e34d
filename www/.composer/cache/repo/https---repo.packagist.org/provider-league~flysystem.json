{"minified": "composer/2.0", "packages": {"league/flysystem": [{"name": "league/flysystem", "description": "File storage abstraction for PHP", "keywords": ["file", "filesystem", "storage", "s3", "WebDAV", "cloud", "aws", "files", "sftp", "ftp", "filesystems"], "homepage": "", "version": "3.30.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2203e3151755d874bb2943649dae1eb8533ac93e", "type": "zip", "shasum": "", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e"}, "type": "library", "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.30.0"}, "funding": [], "time": "2025-06-25T13:29:59+00:00", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "require": {"php": "^8.0.2", "league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0"}, "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3|^2", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^1.10", "phpseclib/phpseclib": "^3.0.36", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "mongodb/mongodb": "^1.2|^2", "sabre/dav": "^4.6.0", "guzzlehttp/psr7": "^2.6"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "symfony/http-client": "<5.2", "guzzlehttp/ringphp": "<1.1.1", "guzzlehttp/guzzle": "<7.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "phpseclib/phpseclib": "3.0.15"}}, {"version": "3.29.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/edc1bb7c86fab0776c3287dbd19b5fa278347319", "type": "zip", "shasum": "", "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.29.1"}, "time": "2024-10-08T08:58:34+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^1.10", "phpseclib/phpseclib": "^3.0.36", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "mongodb/mongodb": "^1.2", "sabre/dav": "^4.6.0", "guzzlehttp/psr7": "^2.6"}}, {"version": "3.29.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0adc0d9a51852e170e0028a60bd271726626d3f0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0adc0d9a51852e170e0028a60bd271726626d3f0", "type": "zip", "shasum": "", "reference": "0adc0d9a51852e170e0028a60bd271726626d3f0"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.29.0"}, "time": "2024-09-29T11:59:11+00:00"}, {"version": "3.28.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e611adab2b1ae2e3072fa72d62c62f52c2bf1f0c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e611adab2b1ae2e3072fa72d62c62f52c2bf1f0c", "type": "zip", "shasum": "", "reference": "e611adab2b1ae2e3072fa72d62c62f52c2bf1f0c"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.28.0"}, "time": "2024-05-22T10:09:12+00:00"}, {"version": "3.27.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4729745b1ab737908c7d055148c9a6b3e959832f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4729745b1ab737908c7d055148c9a6b3e959832f", "type": "zip", "shasum": "", "reference": "4729745b1ab737908c7d055148c9a6b3e959832f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.27.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2024-04-07T19:17:50+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^1.10", "phpseclib/phpseclib": "^3.0.36", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "sabre/dav": "^4.6.0"}}, {"version": "3.26.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "072735c56cc0da00e10716dd90d5a7f7b40b36be"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/072735c56cc0da00e10716dd90d5a7f7b40b36be", "type": "zip", "shasum": "", "reference": "072735c56cc0da00e10716dd90d5a7f7b40b36be"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.26.0"}, "time": "2024-03-25T11:49:53+00:00"}, {"version": "3.25.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "abbd664eb4381102c559d358420989f835208f18"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/abbd664eb4381102c559d358420989f835208f18", "type": "zip", "shasum": "", "reference": "abbd664eb4381102c559d358420989f835208f18"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.25.1"}, "time": "2024-03-16T12:53:19+00:00"}, {"version": "3.25.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4c44347133618cccd9b3df1729647a1577b4ad99"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4c44347133618cccd9b3df1729647a1577b4ad99", "type": "zip", "shasum": "", "reference": "4c44347133618cccd9b3df1729647a1577b4ad99"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.25.0"}, "time": "2024-03-09T17:06:45+00:00"}, {"version": "3.24.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b25a361508c407563b34fac6f64a8a17a8819675"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b25a361508c407563b34fac6f64a8a17a8819675", "type": "zip", "shasum": "", "reference": "b25a361508c407563b34fac6f64a8a17a8819675"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.24.0"}, "time": "2024-02-04T12:10:17+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^1.10", "phpseclib/phpseclib": "^3.0.34", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "sabre/dav": "^4.6.0"}}, {"version": "3.23.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "199e1aebbe3e62bd39f4d4fc8c61ce0b3786197e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/199e1aebbe3e62bd39f4d4fc8c61ce0b3786197e", "type": "zip", "shasum": "", "reference": "199e1aebbe3e62bd39f4d4fc8c61ce0b3786197e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.23.1"}, "time": "2024-01-26T18:42:03+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^1.10", "phpseclib/phpseclib": "^3.0.34", "aws/aws-sdk-php": "^3.220.0", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "sabre/dav": "^4.3.1"}}, {"version": "3.23.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "d4ad81e2b67396e33dc9d7e54ec74ccf73151dcc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/d4ad81e2b67396e33dc9d7e54ec74ccf73151dcc", "type": "zip", "shasum": "", "reference": "d4ad81e2b67396e33dc9d7e54ec74ccf73151dcc"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.23.0"}, "time": "2023-12-04T10:16:17+00:00"}, {"version": "3.22.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "d18526ee587f265f091f47bb83f1d9a001ef6f36"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/d18526ee587f265f091f47bb83f1d9a001ef6f36", "type": "zip", "shasum": "", "reference": "d18526ee587f265f091f47bb83f1d9a001ef6f36"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.22.0"}, "time": "2023-12-03T18:35:53+00:00"}, {"version": "3.21.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a326d8a2d007e4ca327a57470846e34363789258"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a326d8a2d007e4ca327a57470846e34363789258", "type": "zip", "shasum": "", "reference": "a326d8a2d007e4ca327a57470846e34363789258"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.21.0"}, "time": "2023-11-18T13:59:15+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^1.10", "phpseclib/phpseclib": "^3.0.14", "aws/aws-sdk-php": "^3.220.0", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "sabre/dav": "^4.3.1"}}, {"version": "3.20.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "fc4a8264a79b76437ba26b6874895ec2fb359be3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/fc4a8264a79b76437ba26b6874895ec2fb359be3", "type": "zip", "shasum": "", "reference": "fc4a8264a79b76437ba26b6874895ec2fb359be3"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.20.0"}, "time": "2023-11-14T11:54:45+00:00"}, {"version": "3.19.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1b2aa10f2326e0351399b8ce68e287d8e9209a83"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1b2aa10f2326e0351399b8ce68e287d8e9209a83", "type": "zip", "shasum": "", "reference": "1b2aa10f2326e0351399b8ce68e287d8e9209a83"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.19.0"}, "time": "2023-11-07T09:04:28+00:00"}, {"version": "3.18.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "015633a05aee22490495159237a5944091d8281e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/015633a05aee22490495159237a5944091d8281e", "type": "zip", "shasum": "", "reference": "015633a05aee22490495159237a5944091d8281e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.18.0"}, "time": "2023-10-20T17:59:40+00:00"}, {"version": "3.17.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "bd4c9b26849d82364119c68429541f1631fba94b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/bd4c9b26849d82364119c68429541f1631fba94b", "type": "zip", "shasum": "", "reference": "bd4c9b26849d82364119c68429541f1631fba94b"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.17.0"}, "time": "2023-10-05T20:15:05+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^3.0.14", "aws/aws-sdk-php": "^3.220.0", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "sabre/dav": "^4.3.1"}}, {"version": "3.16.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4fdf372ca6b63c6e281b1c01a624349ccb757729"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4fdf372ca6b63c6e281b1c01a624349ccb757729", "type": "zip", "shasum": "", "reference": "4fdf372ca6b63c6e281b1c01a624349ccb757729"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.16.0"}, "time": "2023-09-07T19:22:17+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11|^10.0", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^3.0.14", "aws/aws-sdk-php": "^3.220.0", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.1", "sabre/dav": "^4.3.1"}}, {"version": "3.15.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a141d430414fcb8bf797a18716b09f759a385bed"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a141d430414fcb8bf797a18716b09f759a385bed", "type": "zip", "shasum": "", "reference": "a141d430414fcb8bf797a18716b09f759a385bed"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.15.1"}, "time": "2023-05-04T09:04:26+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^3.0.14", "aws/aws-sdk-php": "^3.220.0", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.1", "sabre/dav": "^4.3.1"}, "conflict": {"symfony/http-client": "<5.2", "guzzlehttp/ringphp": "<1.1.1", "guzzlehttp/guzzle": "<7.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "phpseclib/phpseclib": "3.0.15"}}, {"version": "3.15.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a6836305388c0e34e1ba470cbd2235884d9ecdb7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a6836305388c0e34e1ba470cbd2235884d9ecdb7", "type": "zip", "shasum": "", "reference": "a6836305388c0e34e1ba470cbd2235884d9ecdb7"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.15.0"}, "time": "2023-05-04T08:15:59+00:00"}, {"version": "3.14.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e2a279d7f47d9098e479e8b21f7fb8b8de230158"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e2a279d7f47d9098e479e8b21f7fb8b8de230158", "type": "zip", "shasum": "", "reference": "e2a279d7f47d9098e479e8b21f7fb8b8de230158"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.14.0"}, "time": "2023-04-11T18:11:47+00:00", "require": {"php": "^8.0.2", "league/mime-type-detection": "^1.0.0"}}, {"version": "3.13.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1a402d49b75d61ac33a09661fa8583bbaba11636"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1a402d49b75d61ac33a09661fa8583bbaba11636", "type": "zip", "shasum": "", "reference": "1a402d49b75d61ac33a09661fa8583bbaba11636"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.13.0"}, "time": "2023-04-11T17:39:37+00:00"}, {"version": "3.12.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "81e87e74dd5213795c7846d65089712d2dda90ce"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/81e87e74dd5213795c7846d65089712d2dda90ce", "type": "zip", "shasum": "", "reference": "81e87e74dd5213795c7846d65089712d2dda90ce"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.12.3"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2023-02-18T15:32:41+00:00"}, {"version": "3.12.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f6377c709d2275ed6feaf63e44be7a7162b0e77f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f6377c709d2275ed6feaf63e44be7a7162b0e77f", "type": "zip", "shasum": "", "reference": "f6377c709d2275ed6feaf63e44be7a7162b0e77f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.12.2"}, "time": "2023-01-19T12:02:19+00:00"}, {"version": "3.12.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b934123c1f11ada6363d057d691e3065fa6d6d49"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b934123c1f11ada6363d057d691e3065fa6d6d49", "type": "zip", "shasum": "", "reference": "b934123c1f11ada6363d057d691e3065fa6d6d49"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.12.1"}, "time": "2023-01-06T16:34:48+00:00"}, {"version": "3.12.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "2aef65a47e44f2d6f9938f720f6dd697e7ba7b76"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2aef65a47e44f2d6f9938f720f6dd697e7ba7b76", "type": "zip", "shasum": "", "reference": "2aef65a47e44f2d6f9938f720f6dd697e7ba7b76"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.12.0"}, "time": "2022-12-20T20:21:10+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^3.0.14", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.1", "sabre/dav": "^4.3.1"}}, {"version": "3.11.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "7e423e5dd240a60adfab9bde058d7668863b7731"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7e423e5dd240a60adfab9bde058d7668863b7731", "type": "zip", "shasum": "", "reference": "7e423e5dd240a60adfab9bde058d7668863b7731"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.11.0"}, "time": "2022-12-02T14:39:57+00:00"}, {"version": "3.10.4", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a7790f3dd1b27af81d380e6b2afa77c16ab7e181"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a7790f3dd1b27af81d380e6b2afa77c16ab7e181", "type": "zip", "shasum": "", "reference": "a7790f3dd1b27af81d380e6b2afa77c16ab7e181"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.10.4"}, "time": "2022-11-26T19:48:01+00:00"}, {"version": "3.10.3", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8013fb046c6a244b2b1b75cc95d732ed6bcdeb8a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8013fb046c6a244b2b1b75cc95d732ed6bcdeb8a", "type": "zip", "shasum": "", "reference": "8013fb046c6a244b2b1b75cc95d732ed6bcdeb8a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.10.3"}, "time": "2022-11-14T10:42:43+00:00"}, {"version": "3.10.2", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b9bd194b016114d6ff6765c09d40c7d427e4e3f6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b9bd194b016114d6ff6765c09d40c7d427e4e3f6", "type": "zip", "shasum": "", "reference": "b9bd194b016114d6ff6765c09d40c7d427e4e3f6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.10.2"}, "time": "2022-10-25T07:01:47+00:00"}, {"version": "3.10.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9857d7208a94fc63c7bf09caf223280e59ac7274"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9857d7208a94fc63c7bf09caf223280e59ac7274", "type": "zip", "shasum": "", "reference": "9857d7208a94fc63c7bf09caf223280e59ac7274"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.10.1"}, "time": "2022-10-21T18:57:47+00:00"}, {"version": "3.10.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "839e794c443e759713aba2324d6d3d14acb31b4f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/839e794c443e759713aba2324d6d3d14acb31b4f", "type": "zip", "shasum": "", "reference": "839e794c443e759713aba2324d6d3d14acb31b4f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.10.0"}, "time": "2022-10-21T18:18:46+00:00"}, {"version": "3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "60f3760352fe08e918bc3b1acae4e91af092ebe1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/60f3760352fe08e918bc3b1acae4e91af092ebe1", "type": "zip", "shasum": "", "reference": "60f3760352fe08e918bc3b1acae4e91af092ebe1"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.9.0"}, "time": "2022-10-18T21:02:43+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^3.0.14", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.3.1"}}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3d2ed6215e096e900662bd8f993fc5ad81cc4135"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3d2ed6215e096e900662bd8f993fc5ad81cc4135", "type": "zip", "shasum": "", "reference": "3d2ed6215e096e900662bd8f993fc5ad81cc4135"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.8.0"}, "time": "2022-10-18T06:54:34+00:00"}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0deb0ff21094cbd37b13cbda085c076312708e6c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0deb0ff21094cbd37b13cbda085c076312708e6c", "type": "zip", "shasum": "", "reference": "0deb0ff21094cbd37b13cbda085c076312708e6c"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.7.0"}, "time": "2022-10-17T07:23:36+00:00"}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8eded334b9894dc90ebdcb7be81e3a1c9413f709"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8eded334b9894dc90ebdcb7be81e3a1c9413f709", "type": "zip", "shasum": "", "reference": "8eded334b9894dc90ebdcb7be81e3a1c9413f709"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.6.0"}, "time": "2022-10-13T20:05:14+00:00"}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c73c4eb31f2e883b3897ab5591aa2dbc48112433"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c73c4eb31f2e883b3897ab5591aa2dbc48112433", "type": "zip", "shasum": "", "reference": "c73c4eb31f2e883b3897ab5591aa2dbc48112433"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.5.2"}, "time": "2022-09-23T18:59:16+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f14993c6e394450ac4649da35264df0544d0234e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f14993c6e394450ac4649da35264df0544d0234e", "type": "zip", "shasum": "", "reference": "f14993c6e394450ac4649da35264df0544d0234e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.5.1"}, "time": "2022-09-18T18:23:19+00:00"}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b7bb46dee497b57e0c378315f2a711824cb46c38"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b7bb46dee497b57e0c378315f2a711824cb46c38", "type": "zip", "shasum": "", "reference": "b7bb46dee497b57e0c378315f2a711824cb46c38"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.5.0"}, "time": "2022-09-17T21:06:02+00:00"}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "5972d2a966e236674286e3a2281139ce74e4415d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/5972d2a966e236674286e3a2281139ce74e4415d", "type": "zip", "shasum": "", "reference": "5972d2a966e236674286e3a2281139ce74e4415d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.4.0"}, "time": "2022-09-16T20:57:23+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "d8295793b3e2f91aa39e1feb2d5bfce772891ae2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/d8295793b3e2f91aa39e1feb2d5bfce772891ae2", "type": "zip", "shasum": "", "reference": "d8295793b3e2f91aa39e1feb2d5bfce772891ae2"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.3.0"}, "time": "2022-09-09T11:11:42+00:00"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "81aea9e5217084c7850cd36e1587ee4aad721c6b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/81aea9e5217084c7850cd36e1587ee4aad721c6b", "type": "zip", "shasum": "", "reference": "81aea9e5217084c7850cd36e1587ee4aad721c6b"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.2.1"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-08-14T20:48:34+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.3.1"}, "conflict": {"symfony/http-client": "<5.2", "guzzlehttp/ringphp": "<1.1.1", "guzzlehttp/guzzle": "<7.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0"}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "ed0ecc7f9b5c2f4a9872185846974a808a3b052a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/ed0ecc7f9b5c2f4a9872185846974a808a3b052a", "type": "zip", "shasum": "", "reference": "ed0ecc7f9b5c2f4a9872185846974a808a3b052a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.2.0"}, "time": "2022-07-26T07:26:36+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1a941703dfb649f9b821e7bc425e782f576a805e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1a941703dfb649f9b821e7bc425e782f576a805e", "type": "zip", "shasum": "", "reference": "1a941703dfb649f9b821e7bc425e782f576a805e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.1.1"}, "time": "2022-07-18T09:59:40+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "34a68067b7ae3b836ea5e57e1fc432478372a4f5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/34a68067b7ae3b836ea5e57e1fc432478372a4f5", "type": "zip", "shasum": "", "reference": "34a68067b7ae3b836ea5e57e1fc432478372a4f5"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.1.0"}, "time": "2022-06-29T17:29:54+00:00"}, {"version": "3.0.23", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "5b461a96d01144744077343908a2c196682c71e6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/5b461a96d01144744077343908a2c196682c71e6", "type": "zip", "shasum": "", "reference": "5b461a96d01144744077343908a2c196682c71e6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.23"}, "time": "2022-06-29T08:19:13+00:00"}, {"version": "3.0.22", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "bed1fbd81c7f30700d80dc2691074ea5a95c69f6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/bed1fbd81c7f30700d80dc2691074ea5a95c69f6", "type": "zip", "shasum": "", "reference": "bed1fbd81c7f30700d80dc2691074ea5a95c69f6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.22"}, "time": "2022-06-29T07:28:53+00:00"}, {"version": "3.0.21", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8f1fcf9d2304ff77a006aa36dd2cb5f236999b12"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8f1fcf9d2304ff77a006aa36dd2cb5f236999b12", "type": "zip", "shasum": "", "reference": "8f1fcf9d2304ff77a006aa36dd2cb5f236999b12"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.21"}, "time": "2022-06-12T17:54:28+00:00"}, {"version": "3.0.20", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "42a2f47dcf39944e2aee1b660ee55ab6ef69b535"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/42a2f47dcf39944e2aee1b660ee55ab6ef69b535", "type": "zip", "shasum": "", "reference": "42a2f47dcf39944e2aee1b660ee55ab6ef69b535"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.20"}, "time": "2022-05-25T19:18:39+00:00"}, {"version": "3.0.19", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "670df21225d68d165a8df38587ac3f41caf608f8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/670df21225d68d165a8df38587ac3f41caf608f8", "type": "zip", "shasum": "", "reference": "670df21225d68d165a8df38587ac3f41caf608f8"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.19"}, "time": "2022-05-03T21:19:02+00:00"}, {"version": "3.0.18", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c8e137e594948240b03372e012344b07c61b9193"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c8e137e594948240b03372e012344b07c61b9193", "type": "zip", "shasum": "", "reference": "c8e137e594948240b03372e012344b07c61b9193"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.18"}, "time": "2022-04-25T18:55:04+00:00"}, {"version": "3.0.17", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "29eb78cac0be0c22237c5e0f6f98234d97037d79"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/29eb78cac0be0c22237c5e0f6f98234d97037d79", "type": "zip", "shasum": "", "reference": "29eb78cac0be0c22237c5e0f6f98234d97037d79"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.17"}, "time": "2022-04-14T14:57:13+00:00"}, {"version": "3.0.16", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "dea729954c596bdb6cdaecba6f73df9f3e2c4255"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dea729954c596bdb6cdaecba6f73df9f3e2c4255", "type": "zip", "shasum": "", "reference": "dea729954c596bdb6cdaecba6f73df9f3e2c4255"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.16"}, "time": "2022-04-11T13:32:22+00:00"}, {"version": "3.0.15", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3b71cd136dc0331ee87b636b25f4ee339368c718"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3b71cd136dc0331ee87b636b25f4ee339368c718", "type": "zip", "shasum": "", "reference": "3b71cd136dc0331ee87b636b25f4ee339368c718"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.15"}, "time": "2022-04-08T18:36:06+00:00"}, {"version": "3.0.14", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "46a5450352540e89cb8e7eb20c58b5b4aae481f6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/46a5450352540e89cb8e7eb20c58b5b4aae481f6", "type": "zip", "shasum": "", "reference": "46a5450352540e89cb8e7eb20c58b5b4aae481f6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.14"}, "time": "2022-04-06T18:17:37+00:00"}, {"version": "3.0.13", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "15dc1ccb2db8daef507c4d3e501565bae42a9f0e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/15dc1ccb2db8daef507c4d3e501565bae42a9f0e", "type": "zip", "shasum": "", "reference": "15dc1ccb2db8daef507c4d3e501565bae42a9f0e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.13"}, "time": "2022-04-02T08:55:13+00:00"}, {"version": "3.0.12", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4744d96fb2456d9808be3ad596a2520b902996e2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4744d96fb2456d9808be3ad596a2520b902996e2", "type": "zip", "shasum": "", "reference": "4744d96fb2456d9808be3ad596a2520b902996e2"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.12"}, "time": "2022-03-12T19:32:12+00:00"}, {"version": "3.0.11", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1ca148713c23cadeb9d7526973f81fb4a04090a3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1ca148713c23cadeb9d7526973f81fb4a04090a3", "type": "zip", "shasum": "", "reference": "1ca148713c23cadeb9d7526973f81fb4a04090a3"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.11"}, "time": "2022-03-04T16:40:17+00:00"}, {"version": "3.0.10", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "bbc5026adb5a423dfcdcecec74c7e15943ff6115"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/bbc5026adb5a423dfcdcecec74c7e15943ff6115", "type": "zip", "shasum": "", "reference": "bbc5026adb5a423dfcdcecec74c7e15943ff6115"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.10"}, "time": "2022-02-26T11:09:13+00:00"}, {"version": "3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "fb0801a60b7f9ea4188f01c25cb48aed26db7fb6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/fb0801a60b7f9ea4188f01c25cb48aed26db7fb6", "type": "zip", "shasum": "", "reference": "fb0801a60b7f9ea4188f01c25cb48aed26db7fb6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.9"}, "time": "2022-02-22T07:37:40+00:00", "conflict": {"symfony/http-client": "<5.2", "guzzlehttp/ringphp": "<1.1.1", "guzzlehttp/guzzle": "<7.0"}}, {"version": "3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "30f2c7069b2625da5b126ac66cbea7618a3db8b6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/30f2c7069b2625da5b126ac66cbea7618a3db8b6", "type": "zip", "shasum": "", "reference": "30f2c7069b2625da5b126ac66cbea7618a3db8b6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.8"}, "time": "2022-02-16T18:51:54+00:00"}, {"version": "3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "6f60c44080ebcd62ee8bef25edba4ba58400c052"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/6f60c44080ebcd62ee8bef25edba4ba58400c052", "type": "zip", "shasum": "", "reference": "6f60c44080ebcd62ee8bef25edba4ba58400c052"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.7"}, "time": "2022-02-14T20:25:21+00:00"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c034497259500326ffe54ad59207aaaf50230920"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c034497259500326ffe54ad59207aaaf50230920", "type": "zip", "shasum": "", "reference": "c034497259500326ffe54ad59207aaaf50230920"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.6"}, "time": "2022-02-14T19:14:19+00:00"}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0c82591fd8950d499612ada7104c5eba3191e200"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0c82591fd8950d499612ada7104c5eba3191e200", "type": "zip", "shasum": "", "reference": "0c82591fd8950d499612ada7104c5eba3191e200"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.5"}, "time": "2022-02-12T19:38:15+00:00", "require": {"php": "^8.0.2", "ext-json": "*", "league/mime-type-detection": "^1.0.0"}, "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "microsoft/azure-storage-blob": "^1.1", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.1"}}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "aef771741948595bfbed8e0342dd340695a368ff"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/aef771741948595bfbed8e0342dd340695a368ff", "type": "zip", "shasum": "", "reference": "aef771741948595bfbed8e0342dd340695a368ff"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.4"}, "time": "2022-02-10T16:14:03+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "ext-ftp": "*", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.1"}}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9f8b4260031bfef5497477da53dd57b2aaff0e06"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9f8b4260031bfef5497477da53dd57b2aaff0e06", "type": "zip", "shasum": "", "reference": "9f8b4260031bfef5497477da53dd57b2aaff0e06"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.3"}, "time": "2022-01-31T19:41:04+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "21be6aefb1f5beb9ea6a27b86e1d5ed65b655a8d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/21be6aefb1f5beb9ea6a27b86e1d5ed65b655a8d", "type": "zip", "shasum": "", "reference": "21be6aefb1f5beb9ea6a27b86e1d5ed65b655a8d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.2"}, "time": "2022-01-30T13:36:35+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "72451e411636591ee9ba2ff1c6efbcec46eed97a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/72451e411636591ee9ba2ff1c6efbcec46eed97a", "type": "zip", "shasum": "", "reference": "72451e411636591ee9ba2ff1c6efbcec46eed97a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.1"}, "time": "2022-01-15T18:52:01+00:00", "require-dev": {"ext-zip": "*", "ext-fileinfo": "*", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.2", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.1"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "cd9b487888fb33e25595d62110f14681a9e4b434"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/cd9b487888fb33e25595d62110f14681a9e4b434", "type": "zip", "shasum": "", "reference": "cd9b487888fb33e25595d62110f14681a9e4b434"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.0.0"}, "time": "2022-01-13T21:24:17+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8aaffb653c5777781b0f7f69a5d937baf7ab6cdb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8aaffb653c5777781b0f7f69a5d937baf7ab6cdb", "type": "zip", "shasum": "", "reference": "8aaffb653c5777781b0f7f69a5d937baf7ab6cdb"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.5.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-09-17T21:02:32+00:00", "require": {"php": "^7.2 || ^8.0", "ext-json": "*", "league/mime-type-detection": "^1.0.0"}, "require-dev": {"ext-fileinfo": "*", "ext-ftp": "*", "phpunit/phpunit": "^8.5 || ^9.4", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.2", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.1"}, "conflict": {"guzzlehttp/ringphp": "<1.1.1"}}, {"version": "2.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9392c5f1df57d865c406ee65e5012d566686be12"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9392c5f1df57d865c406ee65e5012d566686be12", "type": "zip", "shasum": "", "reference": "9392c5f1df57d865c406ee65e5012d566686be12"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.4.5"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-04-25T18:39:39+00:00"}, {"version": "2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "837431df8650d9d941d5fdb137181fbb2145da9e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/837431df8650d9d941d5fdb137181fbb2145da9e", "type": "zip", "shasum": "", "reference": "837431df8650d9d941d5fdb137181fbb2145da9e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.4.4"}, "time": "2022-04-14T14:56:29+00:00"}, {"version": "2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4c56e451e10e3d9478f42f44c3d744bc17cdcede"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4c56e451e10e3d9478f42f44c3d744bc17cdcede", "type": "zip", "shasum": "", "reference": "4c56e451e10e3d9478f42f44c3d744bc17cdcede"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.4.3"}, "time": "2022-02-16T18:44:00+00:00"}, {"version": "2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "ca8e4c10535be5c59c03f491310b60b9d6a60e24"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/ca8e4c10535be5c59c03f491310b60b9d6a60e24", "type": "zip", "shasum": "", "reference": "ca8e4c10535be5c59c03f491310b60b9d6a60e24"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.4.2"}, "time": "2022-01-31T19:40:25+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "88cf6a53c0cf63820ea3b21681576421591dbfad"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/88cf6a53c0cf63820ea3b21681576421591dbfad", "type": "zip", "shasum": "", "reference": "88cf6a53c0cf63820ea3b21681576421591dbfad"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.4.1"}, "time": "2022-01-30T13:27:10+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4a6f9f7d7b97c938f2fd25aeb9ff9a6a34c79f2f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4a6f9f7d7b97c938f2fd25aeb9ff9a6a34c79f2f", "type": "zip", "shasum": "", "reference": "4a6f9f7d7b97c938f2fd25aeb9ff9a6a34c79f2f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.4.0"}, "time": "2022-01-04T17:09:29+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "^8.5 || ^9.4", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^3.2", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.1"}}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4b6da3e75b5e8eee53bb5ee46ded15a532843f80"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4b6da3e75b5e8eee53bb5ee46ded15a532843f80", "type": "zip", "shasum": "", "reference": "4b6da3e75b5e8eee53bb5ee46ded15a532843f80"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.3.2"}, "time": "2021-11-28T20:19:08+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8f037e8f3321d5fd4178dcd4d91498220cebd688"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8f037e8f3321d5fd4178dcd4d91498220cebd688", "type": "zip", "shasum": "", "reference": "8f037e8f3321d5fd4178dcd4d91498220cebd688"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.3.1"}, "time": "2021-11-04T20:29:23+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "499313f8b65f9a4dae1c779cd974d59a6fcf0f15"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/499313f8b65f9a4dae1c779cd974d59a6fcf0f15", "type": "zip", "shasum": "", "reference": "499313f8b65f9a4dae1c779cd974d59a6fcf0f15"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.3.0"}, "time": "2021-09-22T06:09:19+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "^8.5 || ^9.4", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^2.16", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "sabre/dav": "^4.1"}}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "811bdc2d52a07eafbb6cb68a7b368b0668b362c8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/811bdc2d52a07eafbb6cb68a7b368b0668b362c8", "type": "zip", "shasum": "", "reference": "811bdc2d52a07eafbb6cb68a7b368b0668b362c8"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.2.3"}, "time": "2021-08-18T19:59:31+00:00"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "629cd411b1eb56808ce85b1dd6f9b5bf422932ad"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/629cd411b1eb56808ce85b1dd6f9b5bf422932ad", "type": "zip", "shasum": "", "reference": "629cd411b1eb56808ce85b1dd6f9b5bf422932ad"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.2.2"}, "time": "2021-08-18T08:51:30+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f186fa328e0687b562fdf530f2642e1109ce261f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f186fa328e0687b562fdf530f2642e1109ce261f", "type": "zip", "shasum": "", "reference": "f186fa328e0687b562fdf530f2642e1109ce261f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.2.1"}, "time": "2021-08-17T13:57:50+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "29a3ba148287db3142412ef34f6fdcbc22c957cd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/29a3ba148287db3142412ef34f6fdcbc22c957cd", "type": "zip", "shasum": "", "reference": "29a3ba148287db3142412ef34f6fdcbc22c957cd"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.2.0"}, "time": "2021-07-20T16:54:08+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a3c694de9f7e844b76f9d1b61296ebf6e8d89d74"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a3c694de9f7e844b76f9d1b61296ebf6e8d89d74", "type": "zip", "shasum": "", "reference": "a3c694de9f7e844b76f9d1b61296ebf6e8d89d74"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.1.1"}, "time": "2021-06-23T22:07:10+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a025209072f2d3a50b7bfd683a67fa0a2e6d2721"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a025209072f2d3a50b7bfd683a67fa0a2e6d2721", "type": "zip", "shasum": "", "reference": "a025209072f2d3a50b7bfd683a67fa0a2e6d2721"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.1.0"}, "time": "2021-05-25T18:28:31+00:00"}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "82ad1e9a11745dba58775de2e8f47600b0a3331d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/82ad1e9a11745dba58775de2e8f47600b0a3331d", "type": "zip", "shasum": "", "reference": "82ad1e9a11745dba58775de2e8f47600b0a3331d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.8"}, "time": "2021-05-15T19:53:59+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "^8.5 || ^9.4", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^2.16", "google/cloud-storage": "^1.23", "async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0"}}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f1ecc5c7bf193da14fbc34198ffb16d979ad7d5a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f1ecc5c7bf193da14fbc34198ffb16d979ad7d5a", "type": "zip", "shasum": "", "reference": "f1ecc5c7bf193da14fbc34198ffb16d979ad7d5a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.7"}, "time": "2021-05-13T19:36:24+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "75a762cf494aec728350e1a98d9cb5a5d2105f39"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/75a762cf494aec728350e1a98d9cb5a5d2105f39", "type": "zip", "shasum": "", "reference": "75a762cf494aec728350e1a98d9cb5a5d2105f39"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.6"}, "time": "2021-05-13T10:42:03+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "27ea64cc9d61ae7b6a5f04bebf062d89dd18e8f7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/27ea64cc9d61ae7b6a5f04bebf062d89dd18e8f7", "type": "zip", "shasum": "", "reference": "27ea64cc9d61ae7b6a5f04bebf062d89dd18e8f7"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.5"}, "time": "2021-04-11T15:03:35+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "7cbbb7222e8d8a34e71273d243dfcf383ed6779f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7cbbb7222e8d8a34e71273d243dfcf383ed6779f", "type": "zip", "shasum": "", "reference": "7cbbb7222e8d8a34e71273d243dfcf383ed6779f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.4"}, "time": "2021-02-12T19:37:50+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e2755df0ae790769729ff877197862fbac020c7d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e2755df0ae790769729ff877197862fbac020c7d", "type": "zip", "shasum": "", "reference": "e2755df0ae790769729ff877197862fbac020c7d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.3"}, "time": "2021-02-09T21:10:56+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1d68c2325a56b6b847f3a40b9564cabfa7bb2594"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1d68c2325a56b6b847f3a40b9564cabfa7bb2594", "type": "zip", "shasum": "", "reference": "1d68c2325a56b6b847f3a40b9564cabfa7bb2594"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.2"}, "time": "2020-12-28T17:02:23+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "dd47636ea3f9fe1a6c2f2935e3a0f25d430f87bb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dd47636ea3f9fe1a6c2f2935e3a0f25d430f87bb", "type": "zip", "shasum": "", "reference": "dd47636ea3f9fe1a6c2f2935e3a0f25d430f87bb"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.1"}, "time": "2020-12-28T16:57:17+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "88ab4780a5cc573fb943c8a6987da880b3ec0474"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/88ab4780a5cc573fb943c8a6987da880b3ec0474", "type": "zip", "shasum": "", "reference": "88ab4780a5cc573fb943c8a6987da880b3ec0474"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.0"}, "time": "2020-11-24T14:30:57+00:00"}, {"version": "2.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "7a32c3e98ed6a3da81c529b508bd58d9294a0e4e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7a32c3e98ed6a3da81c529b508bd58d9294a0e4e", "type": "zip", "shasum": "", "reference": "7a32c3e98ed6a3da81c529b508bd58d9294a0e4e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.0-RC1"}, "time": "2020-10-20T21:08:21+00:00", "require": {"php": "^7.2", "ext-json": "*", "league/mime-type-detection": "^1.0.0"}, "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "friendsofphp/php-cs-fixer": "^2.16"}}, {"version": "2.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f91127d6b65e4e795f49fcd507e712654f852105"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f91127d6b65e4e795f49fcd507e712654f852105", "type": "zip", "shasum": "", "reference": "f91127d6b65e4e795f49fcd507e712654f852105"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.x"}, "time": "2020-08-23T07:49:36+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0"}}, {"version": "2.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "ecf61223c738581a32ef69c11b3e71c44ca709e4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/ecf61223c738581a32ef69c11b3e71c44ca709e4", "type": "zip", "shasum": "", "reference": "ecf61223c738581a32ef69c11b3e71c44ca709e4"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.0-beta.2"}, "time": "2020-08-08T11:22:59+00:00", "require-dev": {"phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0"}}, {"version": "2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "957ccf2c876b0df8e0997ab1a3a12e6f95bc9f54"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/957ccf2c876b0df8e0997ab1a3a12e6f95bc9f54", "type": "zip", "shasum": "", "reference": "957ccf2c876b0df8e0997ab1a3a12e6f95bc9f54"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.0-beta.1"}, "time": "2020-08-04T20:24:36+00:00", "require-dev": {"phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.26", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4"}}, {"version": "2.0.0-alpha.4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3f927d31a98def4778ccf2f2e2ab971628a09a26"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3f927d31a98def4778ccf2f2e2ab971628a09a26", "type": "zip", "shasum": "", "reference": "3f927d31a98def4778ccf2f2e2ab971628a09a26"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.x"}, "time": "2020-07-26T07:35:08+00:00"}, {"version": "2.0.0-alpha.3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0d8cdd573af8b6ad2baa60e4e9ba8a48b86e9bb6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0d8cdd573af8b6ad2baa60e4e9ba8a48b86e9bb6", "type": "zip", "shasum": "", "reference": "0d8cdd573af8b6ad2baa60e4e9ba8a48b86e9bb6"}, "time": "2020-03-21T15:57:57+00:00", "require-dev": {"phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.9", "phpseclib/phpseclib": "^2.0", "aws/aws-sdk-php": "^3.132.4"}}, {"version": "2.0.0-alpha.2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e11236176cad258c68577a32101f5f67a2948d23"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e11236176cad258c68577a32101f5f67a2948d23", "type": "zip", "shasum": "", "reference": "e11236176cad258c68577a32101f5f67a2948d23"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.0.0-alpha.2"}, "time": "2020-03-17T18:17:35+00:00"}, {"version": "2.0.0-alpha.1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "450143cd41530a020781cd3883375e5fb414ef72"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/450143cd41530a020781cd3883375e5fb414ef72", "type": "zip", "shasum": "", "reference": "450143cd41530a020781cd3883375e5fb414ef72"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/2.x"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2020-03-09T07:34:24+00:00"}, {"description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["filesystem", "abstraction", "storage", "s3", "WebDAV", "cloud", "aws", "remote", "files", "sftp", "rackspace", "ftp", "Cloud Files", "dropbox", "filesystems", "file systems", "copy.com"], "version": "1.1.10", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "type": "zip", "shasum": "", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00", "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": "^7.2.5 || ^8.0", "ext-fileinfo": "*", "league/mime-type-detection": "^1.3"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}}, {"version": "1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "094defdb4a7001845300334e7c1ee2335925ef99"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/094defdb4a7001845300334e7c1ee2335925ef99", "type": "zip", "shasum": "", "reference": "094defdb4a7001845300334e7c1ee2335925ef99"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.9"}, "time": "2021-12-09T09:40:50+00:00"}, {"version": "1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c995bb0c23c58c9813d081f9523c9b7bb496698e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c995bb0c23c58c9813d081f9523c9b7bb496698e", "type": "zip", "shasum": "", "reference": "c995bb0c23c58c9813d081f9523c9b7bb496698e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.8"}, "time": "2021-11-28T21:50:23+00:00"}, {"version": "1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3ca8f158ee21efa4bbd4f74bea5730c9b9261e2d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3ca8f158ee21efa4bbd4f74bea5730c9b9261e2d", "type": "zip", "shasum": "", "reference": "3ca8f158ee21efa4bbd4f74bea5730c9b9261e2d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.7"}, "time": "2021-11-25T19:40:58+00:00"}, {"version": "1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "627be7fcde84c71aa0f15097fcf48fd5f2be5287"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/627be7fcde84c71aa0f15097fcf48fd5f2be5287", "type": "zip", "shasum": "", "reference": "627be7fcde84c71aa0f15097fcf48fd5f2be5287"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.6"}, "time": "2021-11-21T11:04:36+00:00"}, {"version": "1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "18634df356bfd4119fe3d6156bdb990c414c14ea"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/18634df356bfd4119fe3d6156bdb990c414c14ea", "type": "zip", "shasum": "", "reference": "18634df356bfd4119fe3d6156bdb990c414c14ea"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.5"}, "time": "2021-08-17T13:49:42+00:00"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f3ad69181b8afed2c9edf7be5a2918144ff4ea32"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f3ad69181b8afed2c9edf7be5a2918144ff4ea32", "type": "zip", "shasum": "", "reference": "f3ad69181b8afed2c9edf7be5a2918144ff4ea32"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.4"}, "time": "2021-06-23T21:56:05+00:00"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9be3b16c877d477357c015cec057548cf9b2a14a", "type": "zip", "shasum": "", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.x"}, "time": "2020-08-23T07:39:11+00:00", "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "63cd8c14708b9544d3f61d3c15b747fda1c95c6e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/63cd8c14708b9544d3f61d3c15b747fda1c95c6e", "type": "zip", "shasum": "", "reference": "63cd8c14708b9544d3f61d3c15b747fda1c95c6e"}, "time": "2020-08-18T10:57:55+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "6e96f54d82e71f71c4108da33ee96a7f57083710"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/6e96f54d82e71f71c4108da33ee96a7f57083710", "type": "zip", "shasum": "", "reference": "6e96f54d82e71f71c4108da33ee96a7f57083710"}, "time": "2020-08-12T14:23:41+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "481c0174b9c99b189959e2bb9d6f52188ed1f692"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/481c0174b9c99b189959e2bb9d6f52188ed1f692", "type": "zip", "shasum": "", "reference": "481c0174b9c99b189959e2bb9d6f52188ed1f692"}, "time": "2020-08-09T15:57:10+00:00"}, {"version": "1.0.70", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "585824702f534f8d3cf7fab7225e8466cc4b7493"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/585824702f534f8d3cf7fab7225e8466cc4b7493", "type": "zip", "shasum": "", "reference": "585824702f534f8d3cf7fab7225e8466cc4b7493"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.70"}, "time": "2020-07-26T07:20:36+00:00", "require": {"php": ">=5.5.9", "ext-fileinfo": "*"}, "require-dev": {"phpspec/phpspec": "^3.4 || ^4.0 || ^5.0 || ^6.0", "phpunit/phpunit": "^5.7.26"}}, {"version": "1.0.69", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "7106f78428a344bc4f643c233a94e48795f10967"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7106f78428a344bc4f643c233a94e48795f10967", "type": "zip", "shasum": "", "reference": "7106f78428a344bc4f643c233a94e48795f10967"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.69"}, "time": "2020-05-18T15:13:39+00:00", "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.26"}}, {"version": "1.0.68", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3e4198372276ec99ac3409a21d7c9d1ced9026e4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3e4198372276ec99ac3409a21d7c9d1ced9026e4", "type": "zip", "shasum": "", "reference": "3e4198372276ec99ac3409a21d7c9d1ced9026e4"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.x"}, "time": "2020-05-12T20:33:44+00:00"}, {"version": "1.0.67", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "5b1f36c75c4bdde981294c2a0ebdb437ee6f275e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/5b1f36c75c4bdde981294c2a0ebdb437ee6f275e", "type": "zip", "shasum": "", "reference": "5b1f36c75c4bdde981294c2a0ebdb437ee6f275e"}, "time": "2020-04-16T13:21:26+00:00"}, {"version": "1.0.66", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "021569195e15f8209b1c4bebb78bd66aa4f08c21"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/021569195e15f8209b1c4bebb78bd66aa4f08c21", "type": "zip", "shasum": "", "reference": "021569195e15f8209b1c4bebb78bd66aa4f08c21"}, "time": "2020-03-17T18:58:12+00:00"}, {"version": "1.0.65", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8f17b3ba67097aafb8318cd5c553b1acf7c891c8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8f17b3ba67097aafb8318cd5c553b1acf7c891c8", "type": "zip", "shasum": "", "reference": "8f17b3ba67097aafb8318cd5c553b1acf7c891c8"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.65"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2020-03-08T18:53:20+00:00"}, {"version": "1.0.64", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "d13c43dbd4b791f815215959105a008515d1a2e0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/d13c43dbd4b791f815215959105a008515d1a2e0", "type": "zip", "shasum": "", "reference": "d13c43dbd4b791f815215959105a008515d1a2e0"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.64"}, "time": "2020-02-05T18:14:17+00:00", "funding": "__unset"}, {"version": "1.0.63", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8132daec326565036bc8e8d1876f77ec183a7bd6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8132daec326565036bc8e8d1876f77ec183a7bd6", "type": "zip", "shasum": "", "reference": "8132daec326565036bc8e8d1876f77ec183a7bd6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2020-01-04T16:30:31+00:00", "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.10"}}, {"version": "1.0.62", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "14dd5d7dff5fbc29ca9a2a53ff109760e40d91a0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/14dd5d7dff5fbc29ca9a2a53ff109760e40d91a0", "type": "zip", "shasum": "", "reference": "14dd5d7dff5fbc29ca9a2a53ff109760e40d91a0"}, "time": "2019-12-29T14:46:55+00:00"}, {"version": "1.0.61", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4fb13c01784a6c9f165a351e996871488ca2d8c9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4fb13c01784a6c9f165a351e996871488ca2d8c9", "type": "zip", "shasum": "", "reference": "4fb13c01784a6c9f165a351e996871488ca2d8c9"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.61"}, "time": "2019-12-08T21:46:50+00:00"}, {"version": "1.0.60", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "03a118aa6c3d14376958da57cef0938567ef313d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/03a118aa6c3d14376958da57cef0938567ef313d", "type": "zip", "shasum": "", "reference": "03a118aa6c3d14376958da57cef0938567ef313d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2019-12-08T21:00:05+00:00"}, {"version": "1.0.59", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1426da21dae81e1f3fe1074a166eb6dd3045f810"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1426da21dae81e1f3fe1074a166eb6dd3045f810", "type": "zip", "shasum": "", "reference": "1426da21dae81e1f3fe1074a166eb6dd3045f810"}, "time": "2019-12-08T12:25:29+00:00"}, {"version": "1.0.58", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "244a1c0e7f4d20cd848a21dfe718c69e47fed451"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/244a1c0e7f4d20cd848a21dfe718c69e47fed451", "type": "zip", "shasum": "", "reference": "244a1c0e7f4d20cd848a21dfe718c69e47fed451"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.58"}, "time": "2019-12-08T12:12:00+00:00"}, {"version": "1.0.57", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0e9db7f0b96b9f12dcf6f65bc34b72b1a30ea55a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0e9db7f0b96b9f12dcf6f65bc34b72b1a30ea55a", "type": "zip", "shasum": "", "reference": "0e9db7f0b96b9f12dcf6f65bc34b72b1a30ea55a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2019-10-16T21:01:05+00:00"}, {"version": "1.0.56", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "90e3f83cb10ef6b058d70f95267030e7a6236518"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/90e3f83cb10ef6b058d70f95267030e7a6236518", "type": "zip", "shasum": "", "reference": "90e3f83cb10ef6b058d70f95267030e7a6236518"}, "time": "2019-10-12T13:05:59+00:00"}, {"version": "1.0.55", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "33c91155537c6dc899eacdc54a13ac6303f156e6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/33c91155537c6dc899eacdc54a13ac6303f156e6", "type": "zip", "shasum": "", "reference": "33c91155537c6dc899eacdc54a13ac6303f156e6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.55"}, "time": "2019-08-24T11:17:19+00:00"}, {"version": "1.0.54", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c681ed22508947a941f113d6cf51bdcf4b84faf9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c681ed22508947a941f113d6cf51bdcf4b84faf9", "type": "zip", "shasum": "", "reference": "c681ed22508947a941f113d6cf51bdcf4b84faf9"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.54"}, "time": "2019-08-23T21:50:05+00:00"}, {"version": "1.0.53", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "08e12b7628f035600634a5e76d95b5eb66cea674"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/08e12b7628f035600634a5e76d95b5eb66cea674", "type": "zip", "shasum": "", "reference": "08e12b7628f035600634a5e76d95b5eb66cea674"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2019-06-18T20:09:29+00:00"}, {"version": "1.0.52", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c5a5097156387970e6f0ccfcdf03f752856f3391"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c5a5097156387970e6f0ccfcdf03f752856f3391", "type": "zip", "shasum": "", "reference": "c5a5097156387970e6f0ccfcdf03f752856f3391"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.52"}, "time": "2019-05-20T20:21:14+00:00"}, {"version": "1.0.51", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "755ba7bf3fb9031e6581d091db84d78275874396"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/755ba7bf3fb9031e6581d091db84d78275874396", "type": "zip", "shasum": "", "reference": "755ba7bf3fb9031e6581d091db84d78275874396"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2019-03-30T13:22:34+00:00"}, {"version": "1.0.50", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "dab4e7624efa543a943be978008f439c333f2249"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dab4e7624efa543a943be978008f439c333f2249", "type": "zip", "shasum": "", "reference": "dab4e7624efa543a943be978008f439c333f2249"}, "time": "2019-02-01T08:50:36+00:00"}, {"version": "1.0.49", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a63cc83d8a931b271be45148fa39ba7156782ffd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a63cc83d8a931b271be45148fa39ba7156782ffd", "type": "zip", "shasum": "", "reference": "a63cc83d8a931b271be45148fa39ba7156782ffd"}, "time": "2018-11-23T23:41:29+00:00"}, {"version": "1.0.48", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a6ded5b2f6055e2db97b4b859fdfca2b952b78aa"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a6ded5b2f6055e2db97b4b859fdfca2b952b78aa", "type": "zip", "shasum": "", "reference": "a6ded5b2f6055e2db97b4b859fdfca2b952b78aa"}, "time": "2018-10-15T13:53:10+00:00"}, {"version": "1.0.47", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a11e4a75f256bdacf99d20780ce42d3b8272975c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a11e4a75f256bdacf99d20780ce42d3b8272975c", "type": "zip", "shasum": "", "reference": "a11e4a75f256bdacf99d20780ce42d3b8272975c"}, "time": "2018-09-14T15:30:29+00:00"}, {"version": "1.0.46", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f3e0d925c18b92cf3ce84ea5cc58d62a1762a2b2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f3e0d925c18b92cf3ce84ea5cc58d62a1762a2b2", "type": "zip", "shasum": "", "reference": "f3e0d925c18b92cf3ce84ea5cc58d62a1762a2b2"}, "time": "2018-08-22T07:45:22+00:00", "require": {"php": ">=5.5.9"}, "require-dev": {"ext-fileinfo": "*", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.10"}}, {"version": "1.0.45", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a99f94e63b512d75f851b181afcdf0ee9ebef7e6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a99f94e63b512d75f851b181afcdf0ee9ebef7e6", "type": "zip", "shasum": "", "reference": "a99f94e63b512d75f851b181afcdf0ee9ebef7e6"}, "time": "2018-05-07T08:44:23+00:00", "require-dev": {"ext-fileinfo": "*", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7"}}, {"version": "1.0.44", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "168dbe519737221dc87d17385cde33073881fd02"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/168dbe519737221dc87d17385cde33073881fd02", "type": "zip", "shasum": "", "reference": "168dbe519737221dc87d17385cde33073881fd02"}, "time": "2018-04-06T09:58:14+00:00"}, {"version": "1.0.43", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1ce7cc142d906ba58dc54c82915d355a9191c8a8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1ce7cc142d906ba58dc54c82915d355a9191c8a8", "type": "zip", "shasum": "", "reference": "1ce7cc142d906ba58dc54c82915d355a9191c8a8"}, "time": "2018-03-01T10:27:04+00:00"}, {"version": "1.0.42", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "09eabc54e199950041aef258a85847676496fe8e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/09eabc54e199950041aef258a85847676496fe8e", "type": "zip", "shasum": "", "reference": "09eabc54e199950041aef258a85847676496fe8e"}, "time": "2018-01-27T16:03:56+00:00"}, {"version": "1.0.41", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f400aa98912c561ba625ea4065031b7a41e5a155"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f400aa98912c561ba625ea4065031b7a41e5a155", "type": "zip", "shasum": "", "reference": "f400aa98912c561ba625ea4065031b7a41e5a155"}, "time": "2017-08-06T17:41:04+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.8", "mockery/mockery": "~0.9", "phpspec/phpspec": "^2.2"}, "suggest": {"ext-fileinfo": "Required for MimeType", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}}, {"version": "1.0.40", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3828f0b24e2c1918bb362d57a53205d6dc8fde61"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3828f0b24e2c1918bb362d57a53205d6dc8fde61", "type": "zip", "shasum": "", "reference": "3828f0b24e2c1918bb362d57a53205d6dc8fde61"}, "time": "2017-04-28T10:15:08+00:00", "suggest": {"ext-fileinfo": "Required for MimeType", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-copy": "Allows you to use Copy.com storage", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage"}}, {"version": "1.0.39", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "2474325ee924134bb05848663b12531f6f2e9fbe"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2474325ee924134bb05848663b12531f6f2e9fbe", "type": "zip", "shasum": "", "reference": "2474325ee924134bb05848663b12531f6f2e9fbe"}, "time": "2017-04-25T15:24:43+00:00"}, {"version": "1.0.38", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4ba6e13f5116204b21c3afdf400ecf2b9eb1c482"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4ba6e13f5116204b21c3afdf400ecf2b9eb1c482", "type": "zip", "shasum": "", "reference": "4ba6e13f5116204b21c3afdf400ecf2b9eb1c482"}, "time": "2017-04-22T18:59:19+00:00"}, {"version": "1.0.37", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "78b5cc4feb61a882302df4fbaf63b7662e5e4ccd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/78b5cc4feb61a882302df4fbaf63b7662e5e4ccd", "type": "zip", "shasum": "", "reference": "78b5cc4feb61a882302df4fbaf63b7662e5e4ccd"}, "time": "2017-03-22T15:43:14+00:00", "suggest": {"ext-fileinfo": "Required for MimeType", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-copy": "Allows you to use Copy.com storage", "league/flysystem-dropbox": "Allows you to use Dropbox storage", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter"}}, {"version": "1.0.36", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "d9c1698582dfbfbd092ec9c5c3325f862cdb3297"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/d9c1698582dfbfbd092ec9c5c3325f862cdb3297", "type": "zip", "shasum": "", "reference": "d9c1698582dfbfbd092ec9c5c3325f862cdb3297"}, "time": "2017-03-18T16:02:30+00:00"}, {"version": "1.0.35", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "dda7f3ab94158a002d9846a97dc18ebfb7acc062"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dda7f3ab94158a002d9846a97dc18ebfb7acc062", "type": "zip", "shasum": "", "reference": "dda7f3ab94158a002d9846a97dc18ebfb7acc062"}, "time": "2017-02-09T11:33:58+00:00"}, {"version": "1.0.34", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "469ad53c13ea19a0e54e3e5d70f61227ddcc0299"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/469ad53c13ea19a0e54e3e5d70f61227ddcc0299", "type": "zip", "shasum": "", "reference": "469ad53c13ea19a0e54e3e5d70f61227ddcc0299"}, "time": "2017-01-30T17:41:17+00:00"}, {"version": "1.0.33", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "5c7f98498b12d47f9de90ec9186a90000125777c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/5c7f98498b12d47f9de90ec9186a90000125777c", "type": "zip", "shasum": "", "reference": "5c7f98498b12d47f9de90ec9186a90000125777c"}, "time": "2017-01-23T10:32:09+00:00"}, {"version": "1.0.32", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "1b5c4a0031697f46e779a9d1b309c2e1b24daeab"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/1b5c4a0031697f46e779a9d1b309c2e1b24daeab", "type": "zip", "shasum": "", "reference": "1b5c4a0031697f46e779a9d1b309c2e1b24daeab"}, "time": "2016-10-19T20:38:46+00:00"}, {"version": "1.0.31", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "521a233642773505222a5dd53231ccf5b0607d5a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/521a233642773505222a5dd53231ccf5b0607d5a", "type": "zip", "shasum": "", "reference": "521a233642773505222a5dd53231ccf5b0607d5a"}, "time": "2016-10-19T13:15:54+00:00"}, {"version": "1.0.30", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "75138bb14160f3ba963750aa95e08d1d394fb198"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/75138bb14160f3ba963750aa95e08d1d394fb198", "type": "zip", "shasum": "", "reference": "75138bb14160f3ba963750aa95e08d1d394fb198"}, "time": "2016-10-18T16:20:02+00:00"}, {"version": "1.0.29", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b732cc946c48cf102ddffd41ffcd54901a28ccdb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b732cc946c48cf102ddffd41ffcd54901a28ccdb", "type": "zip", "shasum": "", "reference": "b732cc946c48cf102ddffd41ffcd54901a28ccdb"}, "time": "2016-10-17T23:14:19+00:00", "require": {"php": ">=5.6.0"}}, {"version": "1.0.28", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a9663643ff2d16d7f66ed1e0d3212c5491bc9044"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a9663643ff2d16d7f66ed1e0d3212c5491bc9044", "type": "zip", "shasum": "", "reference": "a9663643ff2d16d7f66ed1e0d3212c5491bc9044"}, "time": "2016-10-07T12:20:37+00:00", "require": {"php": ">=5.4.0"}}, {"version": "1.0.27", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "50e2045ed70a7e75a5e30bc3662904f3b67af8a9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/50e2045ed70a7e75a5e30bc3662904f3b67af8a9", "type": "zip", "shasum": "", "reference": "50e2045ed70a7e75a5e30bc3662904f3b67af8a9"}, "time": "2016-08-10T08:55:11+00:00"}, {"version": "1.0.26", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "fb3b30dca320b36931ea878fea17ebe136fba1b0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/fb3b30dca320b36931ea878fea17ebe136fba1b0", "type": "zip", "shasum": "", "reference": "fb3b30dca320b36931ea878fea17ebe136fba1b0"}, "time": "2016-08-03T09:49:11+00:00"}, {"version": "1.0.25", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a76afa4035931be0c78ca8efc6abf3902362f437"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a76afa4035931be0c78ca8efc6abf3902362f437", "type": "zip", "shasum": "", "reference": "a76afa4035931be0c78ca8efc6abf3902362f437"}, "time": "2016-07-18T12:22:57+00:00"}, {"version": "1.0.24", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9aca859a303fdca30370f42b8c611d9cf0dedf4b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9aca859a303fdca30370f42b8c611d9cf0dedf4b", "type": "zip", "shasum": "", "reference": "9aca859a303fdca30370f42b8c611d9cf0dedf4b"}, "time": "2016-06-03T19:11:39+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.8 || ~5.0", "mockery/mockery": "~0.9", "phpspec/phpspec": "^2.2"}}, {"version": "1.0.23", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8af628823fd83776b47725d9a3a697093e7dc31f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8af628823fd83776b47725d9a3a697093e7dc31f", "type": "zip", "shasum": "", "reference": "8af628823fd83776b47725d9a3a697093e7dc31f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.23"}, "time": "2016-06-03T18:34:03+00:00"}, {"version": "1.0.22", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "bd73a91703969a2d20ab4bfbf971d6c2cbe36612"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/bd73a91703969a2d20ab4bfbf971d6c2cbe36612", "type": "zip", "shasum": "", "reference": "bd73a91703969a2d20ab4bfbf971d6c2cbe36612"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.22"}, "time": "2016-04-28T06:53:12+00:00"}, {"version": "1.0.21", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "35a83cf67d80d7040f306c77b0a84b9fbcc4fbfb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/35a83cf67d80d7040f306c77b0a84b9fbcc4fbfb", "type": "zip", "shasum": "", "reference": "35a83cf67d80d7040f306c77b0a84b9fbcc4fbfb"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2016-04-22T10:56:25+00:00"}, {"version": "1.0.20", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e87a786e3ae12a25cf78a71bb07b4b384bfaa83a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e87a786e3ae12a25cf78a71bb07b4b384bfaa83a", "type": "zip", "shasum": "", "reference": "e87a786e3ae12a25cf78a71bb07b4b384bfaa83a"}, "time": "2016-03-14T21:54:11+00:00"}, {"version": "1.0.19", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "635ee279a522233a567984756bf9da1db243d9bf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/635ee279a522233a567984756bf9da1db243d9bf", "type": "zip", "shasum": "", "reference": "635ee279a522233a567984756bf9da1db243d9bf"}, "time": "2016-03-12T19:04:33+00:00"}, {"version": "1.0.18", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b334d6c5f95364948e06d2f620ab93d084074c6e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b334d6c5f95364948e06d2f620ab93d084074c6e", "type": "zip", "shasum": "", "reference": "b334d6c5f95364948e06d2f620ab93d084074c6e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.18"}, "time": "2016-03-07T21:01:43+00:00"}, {"version": "1.0.17", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "02f5b6c9a8b9278c8381e3361e7bd9d641c740ca"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/02f5b6c9a8b9278c8381e3361e7bd9d641c740ca", "type": "zip", "shasum": "", "reference": "02f5b6c9a8b9278c8381e3361e7bd9d641c740ca"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2016-02-19T15:35:38+00:00"}, {"version": "1.0.16", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "183e1a610664baf6dcd6fceda415baf43cbdc031"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/183e1a610664baf6dcd6fceda415baf43cbdc031", "type": "zip", "shasum": "", "reference": "183e1a610664baf6dcd6fceda415baf43cbdc031"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.16"}, "time": "2015-12-19T20:16:43+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.8", "mockery/mockery": "~0.9", "phpspec/phpspec": "^2.2", "phpspec/prophecy-phpunit": "~1.0"}}, {"version": "1.0.15", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "31525caf9e8772683672fefd8a1ca0c0736020f4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/31525caf9e8772683672fefd8a1ca0c0736020f4", "type": "zip", "shasum": "", "reference": "31525caf9e8772683672fefd8a1ca0c0736020f4"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2015-09-30T22:26:59+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.1", "mockery/mockery": "~0.9", "phpspec/phpspec": "^2.2", "phpspec/prophecy-phpunit": "~1.0"}}, {"version": "1.0.14", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "202c7775d959f377e2e302d93e7127c47604438a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/202c7775d959f377e2e302d93e7127c47604438a", "type": "zip", "shasum": "", "reference": "202c7775d959f377e2e302d93e7127c47604438a"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/twistor-configurable-permissions"}, "time": "2015-09-22T19:18:28+00:00"}, {"version": "1.0.13", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "76caed01a57527599b55919f0746104f288b3d56"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/76caed01a57527599b55919f0746104f288b3d56", "type": "zip", "shasum": "", "reference": "76caed01a57527599b55919f0746104f288b3d56"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.13"}, "time": "2015-09-20T13:05:34+00:00"}, {"version": "1.0.12", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "7323424a9d39c24e597ed3f2144419dfbb52e086"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7323424a9d39c24e597ed3f2144419dfbb52e086", "type": "zip", "shasum": "", "reference": "7323424a9d39c24e597ed3f2144419dfbb52e086"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2015-09-05T12:06:41+00:00", "conflict": "__unset"}, {"version": "1.0.11", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c16222fdc02467eaa12cb6d6d0e65527741f6040"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c16222fdc02467eaa12cb6d6d0e65527741f6040", "type": "zip", "shasum": "", "reference": "c16222fdc02467eaa12cb6d6d0e65527741f6040"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.11"}, "time": "2015-07-28T20:41:58+00:00"}, {"version": "1.0.10", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3475ce0b1b5cab41f012905c4c58ad645088f7c9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3475ce0b1b5cab41f012905c4c58ad645088f7c9", "type": "zip", "shasum": "", "reference": "3475ce0b1b5cab41f012905c4c58ad645088f7c9"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2015-07-21T19:35:53+00:00"}, {"version": "1.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c42bed650525ec9ea1a83b715e37f4fc690f99aa"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c42bed650525ec9ea1a83b715e37f4fc690f99aa", "type": "zip", "shasum": "", "reference": "c42bed650525ec9ea1a83b715e37f4fc690f99aa"}, "time": "2015-07-13T09:25:28+00:00"}, {"version": "1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "941a320939cf9421bb6573fad417c841f8b862f3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/941a320939cf9421bb6573fad417c841f8b862f3", "type": "zip", "shasum": "", "reference": "941a320939cf9421bb6573fad417c841f8b862f3"}, "time": "2015-07-12T21:01:33+00:00"}, {"version": "1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b58431785768abbb4f00c10e66a065095a0693ef"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b58431785768abbb4f00c10e66a065095a0693ef", "type": "zip", "shasum": "", "reference": "b58431785768abbb4f00c10e66a065095a0693ef"}, "time": "2015-07-11T14:58:06+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e87d33ecc3a9fe2b1931856dc9921628f8c18cd5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e87d33ecc3a9fe2b1931856dc9921628f8c18cd5", "type": "zip", "shasum": "", "reference": "e87d33ecc3a9fe2b1931856dc9921628f8c18cd5"}, "time": "2015-07-08T20:43:57+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f70cf7d229b9f96a076b74b20c31f1762c8ef9dc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f70cf7d229b9f96a076b74b20c31f1762c8ef9dc", "type": "zip", "shasum": "", "reference": "f70cf7d229b9f96a076b74b20c31f1762c8ef9dc"}, "time": "2015-07-08T12:45:24+00:00"}, {"description": "Many filesystems, one API.", "keywords": ["filesystem", "storage", "s3", "WebDAV", "cloud", "aws", "remote", "files", "sftp", "rackspace", "ftp", "Cloud Files", "dropbox", "file systems", "copy.com"], "version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "56862959b45131ad33e5a7727a25db19f2cf090b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/56862959b45131ad33e5a7727a25db19f2cf090b", "type": "zip", "shasum": "", "reference": "56862959b45131ad33e5a7727a25db19f2cf090b"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.0.4"}, "time": "2015-06-07T21:27:37+00:00", "require": {"php": ">=5.4.0", "ext-mbstring": "*"}, "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.1", "mockery/mockery": "~0.9", "predis/predis": "~1.0", "tedivm/stash": "~0.12.0", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0", "phpspec/prophecy-phpunit": "~1.0"}, "suggest": {"ext-fileinfo": "Required for MimeType", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-copy": "Allows you to use Copy.com storage", "league/flysystem-dropbox": "Allows you to use Dropbox storage", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "predis/predis": "Allows you to use Predis for caching"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3c2400a99ccc3be6884d40361890010449c6b447"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3c2400a99ccc3be6884d40361890010449c6b447", "type": "zip", "shasum": "", "reference": "3c2400a99ccc3be6884d40361890010449c6b447"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2015-03-29T14:01:43+00:00", "require": {"php": ">=5.4.0"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "51cd7cd7ee0defbaafc6ec0d3620110a5d71e11a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/51cd7cd7ee0defbaafc6ec0d3620110a5d71e11a", "type": "zip", "shasum": "", "reference": "51cd7cd7ee0defbaafc6ec0d3620110a5d71e11a"}, "time": "2015-03-10T11:04:14+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.1", "mockery/mockery": "~0.9", "predis/predis": "~1.0", "tedivm/stash": "~0.12.0", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0.0", "phpspec/prophecy-phpunit": "~1.0"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "efa1d394bb45b0984a863854c608c607b9c2a9a4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/efa1d394bb45b0984a863854c608c607b9c2a9a4", "type": "zip", "shasum": "", "reference": "efa1d394bb45b0984a863854c608c607b9c2a9a4"}, "time": "2015-01-23T09:43:34+00:00", "require-dev": {"ext-fileinfo": "*", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9", "predis/predis": "~1.0", "tedivm/stash": "~0.12.0", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0.0", "phpspec/prophecy-phpunit": "~1.0"}, "suggest": {"ext-fileinfo": "Required for MimeType", "league/flysystem-aws-s3-v2": "Use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-copy": "Allows you to use Copy.com storage", "league/flysystem-dropbox": "Use Dropbox storage", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "predis/predis": "Allows you to use Predis for caching"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0ba6b6dfd6f456905ee8d6b0bcaab7ec89419b93"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0ba6b6dfd6f456905ee8d6b0bcaab7ec89419b93", "type": "zip", "shasum": "", "reference": "0ba6b6dfd6f456905ee8d6b0bcaab7ec89419b93"}, "time": "2015-01-19T19:19:07+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "1.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c0afa9513c0e555a607652cb56f81bd991f295f3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c0afa9513c0e555a607652cb56f81bd991f295f3", "type": "zip", "shasum": "", "reference": "c0afa9513c0e555a607652cb56f81bd991f295f3"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/refactor/cacheless-filesystem"}, "time": "2015-01-18T00:19:19+00:00", "suggest": {"ext-fileinfo": "Required for MimeType", "league/event": "Required for EventableFilesystem", "league/flysystem-aws-s3-v2": "Use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-copy": "Allows you to use Copy.com storage", "league/flysystem-dropbox": "Use Dropbox storage", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "predis/predis": "Allows you to use Predis for caching"}}, {"version": "0.5.12", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e046fe60d761e30691c642c9cc6a4089f724e805"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e046fe60d761e30691c642c9cc6a4089f724e805", "type": "zip", "shasum": "", "reference": "e046fe60d761e30691c642c9cc6a4089f724e805"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.12"}, "time": "2014-11-05T13:39:29+00:00", "extra": {"branch-alias": {"dev-master": "0.5-dev"}}, "require-dev": {"ext-fileinfo": "*", "league/event": "~1.0", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9", "aws/aws-sdk-php": "~2.4", "predis/predis": "~1.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.10.0", "sabre/dav": "~2.0.2", "barracuda/copy": "~1.1.4", "guzzlehttp/guzzle": "~4.0", "phpseclib/phpseclib": "~0.3.5", "tedivm/stash": "~0.12.0", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0"}, "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S3 storage", "barracuda/copy": "Allows you to use Copy.com storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "ext-fileinfo": "Required for MimeType", "guzzlehttp/guzzle": "Allows you to use http files (reading only)", "league/event": "Required for EventableFilesystem", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching", "rackspace/php-opencloud": "Allows you to use Rackspace Cloud Files", "sabre/dav": "Enables WebDav support", "tedivm/stash": "Allows you to use Stash as cache implementation"}}, {"version": "0.5.11", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c829ddfb0291a593fccfed1d62d593b13960d194"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c829ddfb0291a593fccfed1d62d593b13960d194", "type": "zip", "shasum": "", "reference": "c829ddfb0291a593fccfed1d62d593b13960d194"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.11"}, "time": "2014-11-05T12:12:40+00:00"}, {"version": "0.5.10", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "96fe7ec1ebed35e3dc1e1dce4bbdab2eca0140a2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/96fe7ec1ebed35e3dc1e1dce4bbdab2eca0140a2", "type": "zip", "shasum": "", "reference": "96fe7ec1ebed35e3dc1e1dce4bbdab2eca0140a2"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.10"}, "time": "2014-10-28T11:35:42+00:00"}, {"description": "Filesystem abstraction, but easy.", "keywords": ["filesystem", "storage", "s3", "WebDAV", "aws", "remote", "files", "sftp", "ftp", "dropbox", "file systems"], "version": "0.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "a41d156d767a49a0f66f9c96059d3b9ca224faf7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/a41d156d767a49a0f66f9c96059d3b9ca224faf7", "type": "zip", "shasum": "", "reference": "a41d156d767a49a0f66f9c96059d3b9ca224faf7"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.9"}, "time": "2014-10-18T15:41:49+00:00"}, {"version": "0.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "2b8e053fb4ce1aa9146dfcb36002c378be29b03c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2b8e053fb4ce1aa9146dfcb36002c378be29b03c", "type": "zip", "shasum": "", "reference": "2b8e053fb4ce1aa9146dfcb36002c378be29b03c"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.8"}, "time": "2014-10-17T14:21:58+00:00"}, {"version": "0.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "63759ca1f3bb25d52946f684b54fe3821d084215"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/63759ca1f3bb25d52946f684b54fe3821d084215", "type": "zip", "shasum": "", "reference": "63759ca1f3bb25d52946f684b54fe3821d084215"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2014-09-16T06:55:04+00:00", "require-dev": {"league/event": "~1.0", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9", "aws/aws-sdk-php": "~2.4", "predis/predis": "~1.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.10.0", "sabre/dav": "~2.0.2", "barracuda/copy": "~1.1.4", "guzzlehttp/guzzle": "~4.0", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0"}, "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S3 storage", "barracuda/copy": "Allows you to use Copy.com storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "guzzlehttp/guzzle": "Allows you to use http files (reading only)", "league/event": "Required for EventableFilesystem", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching", "rackspace/php-opencloud": "Allows you to use Rackspace Cloud Files", "sabre/dav": "Enables WebDav support"}}, {"version": "0.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "6f626418670641bc643962cd85b2d33ec4c87137"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/6f626418670641bc643962cd85b2d33ec4c87137", "type": "zip", "shasum": "", "reference": "6f626418670641bc643962cd85b2d33ec4c87137"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.6"}, "time": "2014-09-06T17:49:44+00:00", "require-dev": {"league/event": "~0.3", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9@dev", "aws/aws-sdk-php": "~2.4", "predis/predis": "~1.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~2.0.2", "barracuda/copy": "~1.1.4", "guzzlehttp/guzzle": "~4.0", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0"}}, {"version": "0.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "17bb2a0f522f3d325b6406a9db0292bc7484ade5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/17bb2a0f522f3d325b6406a9db0292bc7484ade5", "type": "zip", "shasum": "", "reference": "17bb2a0f522f3d325b6406a9db0292bc7484ade5"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2014-08-26T11:05:47+00:00", "extra": {"branch-alias": {"dev-master": "0.6-dev"}}, "require-dev": {"league/event": "~0.3", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9@dev", "aws/aws-sdk-php": "~2.4", "predis/predis": "~1.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~2.0.2", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0"}, "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S3 storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "league/event": "Required for EventableFilesystem", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching", "rackspace/php-opencloud": "Allows you to use Rackspace Cloud Files", "sabre/dav": "Enables WebDav support"}}, {"version": "0.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "33acb69d2db7c1e4c5c59083da4e680546d40a12"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/33acb69d2db7c1e4c5c59083da4e680546d40a12", "type": "zip", "shasum": "", "reference": "33acb69d2db7c1e4c5c59083da4e680546d40a12"}, "time": "2014-08-24T09:33:41+00:00", "require-dev": {"league/event": "~0.2.0", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9@dev", "aws/aws-sdk-php": "~2.4", "predis/predis": "~1.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~2.0.2", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.1", "phpspec/phpspec": "~2.0"}}, {"keywords": ["filesystem", "s3", "WebDAV", "aws", "remote", "sftp", "ftp", "dropbox"], "version": "0.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "305a64e3407cc8cc5e8d5657ce151bb159885ccb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/305a64e3407cc8cc5e8d5657ce151bb159885ccb", "type": "zip", "shasum": "", "reference": "305a64e3407cc8cc5e8d5657ce151bb159885ccb"}, "time": "2014-07-25T11:46:12+00:00", "extra": {"branch-alias": {"dev-master": "0.5-dev"}}, "require-dev": {"league/event": "~0.2.0", "phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9@dev", "aws/aws-sdk-php": "~2.4", "predis/predis": "~0.8.4", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~2.0.2", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.1"}}, {"version": "0.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9b0b94fb10bbe20d68538186896725435dcedd5a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9b0b94fb10bbe20d68538186896725435dcedd5a", "type": "zip", "shasum": "", "reference": "9b0b94fb10bbe20d68538186896725435dcedd5a"}, "time": "2014-07-21T17:15:28+00:00"}, {"version": "0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e8968e6b75f0549b37a3cf4524a927f6040fc36d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e8968e6b75f0549b37a3cf4524a927f6040fc36d", "type": "zip", "shasum": "", "reference": "e8968e6b75f0549b37a3cf4524a927f6040fc36d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.5.1"}, "time": "2014-07-21T10:40:24+00:00"}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "883c2086cf9670b2b6edfc5c05c6eaeb7bd8b6db"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/883c2086cf9670b2b6edfc5c05c6eaeb7bd8b6db", "type": "zip", "shasum": "", "reference": "883c2086cf9670b2b6edfc5c05c6eaeb7bd8b6db"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/master"}, "time": "2014-07-16T19:44:55+00:00"}, {"version": "0.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "69fbc26ce7a88edcf141a747a1e5008a25a3b826"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/69fbc26ce7a88edcf141a747a1e5008a25a3b826", "type": "zip", "shasum": "", "reference": "69fbc26ce7a88edcf141a747a1e5008a25a3b826"}, "time": "2014-05-13T19:56:59+00:00", "extra": {"branch-alias": {"dev-master": "0.4-dev"}}, "require-dev": {"phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9", "aws/aws-sdk-php": "~2.5", "predis/predis": "~0.8.4", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.1"}, "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S3 storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching", "rackspace/php-opencloud": "Allows you to use Rackspace Cloud Files", "sabre/dav": "Enables WebDav support"}, "require": "__unset"}, {"version": "0.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "513d46e18f71276f4bd5ab5f9e486ec32d80d4c5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/513d46e18f71276f4bd5ab5f9e486ec32d80d4c5", "type": "zip", "shasum": "", "reference": "513d46e18f71276f4bd5ab5f9e486ec32d80d4c5"}, "time": "2014-05-08T16:04:49+00:00"}, {"version": "0.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "2420e2788d1f00f40a98b431eef16ae7e3b90fb5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2420e2788d1f00f40a98b431eef16ae7e3b90fb5", "type": "zip", "shasum": "", "reference": "2420e2788d1f00f40a98b431eef16ae7e3b90fb5"}, "time": "2014-05-07T13:37:59+00:00"}, {"version": "0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "ecb6e267d1773cbce4d949de689f35b601352e50"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/ecb6e267d1773cbce4d949de689f35b601352e50", "type": "zip", "shasum": "", "reference": "ecb6e267d1773cbce4d949de689f35b601352e50"}, "time": "2014-04-30T10:33:27+00:00", "require-dev": {"phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9", "aws/aws-sdk-php": "~2.5", "predis/predis": "~0.8.4", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}, "extra": "__unset"}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "51b92a5e6978a076e86889d49dbf5650b2bedd44"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/51b92a5e6978a076e86889d49dbf5650b2bedd44", "type": "zip", "shasum": "", "reference": "51b92a5e6978a076e86889d49dbf5650b2bedd44"}, "time": "2014-04-24T08:31:08+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "02fc9c928ac08deb38d9acb771b370eb39a0cce6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/02fc9c928ac08deb38d9acb771b370eb39a0cce6", "type": "zip", "shasum": "", "reference": "02fc9c928ac08deb38d9acb771b370eb39a0cce6"}, "time": "2014-04-19T13:31:04+00:00"}, {"version": "0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "69cdc8887ee1152deffc68a9a45d17c1a30115d8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/69cdc8887ee1152deffc68a9a45d17c1a30115d8", "type": "zip", "shasum": "", "reference": "69cdc8887ee1152deffc68a9a45d17c1a30115d8"}, "time": "2014-04-17T10:14:09+00:00"}, {"version": "0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "6858526140a730c8cbfcb1a4bfb5e0b5da5aadd6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/6858526140a730c8cbfcb1a4bfb5e0b5da5aadd6", "type": "zip", "shasum": "", "reference": "6858526140a730c8cbfcb1a4bfb5e0b5da5aadd6"}, "time": "2014-04-16T19:45:31+00:00"}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "387e012fa93878e3865ed8db1aa9daba8bf2f9c5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/387e012fa93878e3865ed8db1aa9daba8bf2f9c5", "type": "zip", "shasum": "", "reference": "387e012fa93878e3865ed8db1aa9daba8bf2f9c5"}, "time": "2014-04-11T20:02:14+00:00"}, {"version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "5b9f50b905bb65ce6022f47fb9b0f58ae7e1650c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/5b9f50b905bb65ce6022f47fb9b0f58ae7e1650c", "type": "zip", "shasum": "", "reference": "5b9f50b905bb65ce6022f47fb9b0f58ae7e1650c"}, "time": "2014-04-11T18:23:42+00:00"}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c60d4fd31d11fcaa8b6d6a87da11fc23d8c337c2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c60d4fd31d11fcaa8b6d6a87da11fc23d8c337c2", "type": "zip", "shasum": "", "reference": "c60d4fd31d11fcaa8b6d6a87da11fc23d8c337c2"}, "time": "2014-04-09T14:47:10+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f5d9978f38828cdfa18da5930df7ebc4d30fa1da"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f5d9978f38828cdfa18da5930df7ebc4d30fa1da", "type": "zip", "shasum": "", "reference": "f5d9978f38828cdfa18da5930df7ebc4d30fa1da"}, "time": "2014-03-22T21:53:52+00:00", "require-dev": {"phpunit/phpunit": "~4.0", "aws/aws-sdk-php": "~2.5", "predis/predis": "~0.8.4", "mockery/mockery": "~0.9.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}}, {"version": "0.2.15", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "fd261fa2950deba75eb53f7a941282f7aba5c72e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/fd261fa2950deba75eb53f7a941282f7aba5c72e", "type": "zip", "shasum": "", "reference": "fd261fa2950deba75eb53f7a941282f7aba5c72e"}, "time": "2014-03-11T07:44:38+00:00"}, {"version": "0.2.14", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "b6cdd3922fdb4836d4e88afa56980773c3e7c6f6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/b6cdd3922fdb4836d4e88afa56980773c3e7c6f6", "type": "zip", "shasum": "", "reference": "b6cdd3922fdb4836d4e88afa56980773c3e7c6f6"}, "time": "2014-03-09T09:23:05+00:00", "require-dev": {"phpunit/phpunit": "~4.1", "aws/aws-sdk-php": "~2.5", "predis/predis": "~0.8.4", "mockery/mockery": "~0.9.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}}, {"version": "0.2.13", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "2cd198f682234dc8ca0907d15d9f54ffe4fd3d10"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2cd198f682234dc8ca0907d15d9f54ffe4fd3d10", "type": "zip", "shasum": "", "reference": "2cd198f682234dc8ca0907d15d9f54ffe4fd3d10"}, "time": "2014-03-04T16:58:11+00:00", "require-dev": {"aws/aws-sdk-php": "~2.5", "predis/predis": "~0.8.4", "mockery/mockery": "~0.9.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}}, {"version": "0.2.12", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "e49521172f06ff5a9f98fc9af2806e7a7bb0c525"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/e49521172f06ff5a9f98fc9af2806e7a7bb0c525", "type": "zip", "shasum": "", "reference": "e49521172f06ff5a9f98fc9af2806e7a7bb0c525"}, "time": "2014-02-16T10:47:47+00:00", "require-dev": {"aws/aws-sdk-php": "~2.5", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}}, {"version": "0.2.11", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0c09ccab11d7cbfdb5d7a5882f8c7d61ea07bc9d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0c09ccab11d7cbfdb5d7a5882f8c7d61ea07bc9d", "type": "zip", "shasum": "", "reference": "0c09ccab11d7cbfdb5d7a5882f8c7d61ea07bc9d"}, "time": "2014-02-16T09:03:24+00:00"}, {"version": "0.2.10", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "dedd25eae936f7b7aa2c1760c7b97d72a36384ad"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dedd25eae936f7b7aa2c1760c7b97d72a36384ad", "type": "zip", "shasum": "", "reference": "dedd25eae936f7b7aa2c1760c7b97d72a36384ad"}, "time": "2014-02-13T20:49:59+00:00"}, {"version": "0.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "3bcf1b8bdeefaf349ecaa04e1f952b41052f7758"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3bcf1b8bdeefaf349ecaa04e1f952b41052f7758", "type": "zip", "shasum": "", "reference": "3bcf1b8bdeefaf349ecaa04e1f952b41052f7758"}, "time": "2014-02-11T20:29:42+00:00"}, {"version": "0.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "095f87dae99a63b4079f0d005ab5139c5bfd84ee"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/095f87dae99a63b4079f0d005ab5139c5bfd84ee", "type": "zip", "shasum": "", "reference": "095f87dae99a63b4079f0d005ab5139c5bfd84ee"}, "time": "2014-02-10T12:07:14+00:00"}, {"version": "0.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "604abcdb804c0203090a5873d63409cb58cb869d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/604abcdb804c0203090a5873d63409cb58cb869d", "type": "zip", "shasum": "", "reference": "604abcdb804c0203090a5873d63409cb58cb869d"}, "time": "2014-02-09T12:55:30+00:00"}, {"version": "0.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "c0932c4380c0d1313654be1de085dad81fbe71c1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c0932c4380c0d1313654be1de085dad81fbe71c1", "type": "zip", "shasum": "", "reference": "c0932c4380c0d1313654be1de085dad81fbe71c1"}, "time": "2014-02-06T14:43:49+00:00"}, {"version": "0.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f5f97f59c7f269169a0e14092a3b7bf2a2392adb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f5f97f59c7f269169a0e14092a3b7bf2a2392adb", "type": "zip", "shasum": "", "reference": "f5f97f59c7f269169a0e14092a3b7bf2a2392adb"}, "time": "2014-02-06T14:08:30+00:00"}, {"version": "0.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "eafee72d6ad6b396486280071a482be5959da6af"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/eafee72d6ad6b396486280071a482be5959da6af", "type": "zip", "shasum": "", "reference": "eafee72d6ad6b396486280071a482be5959da6af"}, "time": "2014-02-01T18:53:12+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1", "rackspace/php-opencloud": "~1.9.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}}, {"version": "0.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "073bcbd3af4553ea65af3cf91d1de12795b14711"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/073bcbd3af4553ea65af3cf91d1de12795b14711", "type": "zip", "shasum": "", "reference": "073bcbd3af4553ea65af3cf91d1de12795b14711"}, "time": "2014-02-01T10:46:09+00:00", "autoload": {"psr-0": {"League\\Flysystem": "src/"}}}, {"version": "0.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "9a4e4ab3c3f71fd80f456894bd04fe4a1e537df5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9a4e4ab3c3f71fd80f456894bd04fe4a1e537df5", "type": "zip", "shasum": "", "reference": "9a4e4ab3c3f71fd80f456894bd04fe4a1e537df5"}, "time": "2014-01-30T09:56:44+00:00"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8ab32f5ab9404c4804410e560598ef85b29476f5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8ab32f5ab9404c4804410e560598ef85b29476f5", "type": "zip", "shasum": "", "reference": "8ab32f5ab9404c4804410e560598ef85b29476f5"}, "time": "2014-01-27T07:36:04+00:00", "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S3 storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching", "sabre/dav": "Enables WebDav support"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "abad8c6bbee3447fdc945e6549f43ec9108f98bf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/abad8c6bbee3447fdc945e6549f43ec9108f98bf", "type": "zip", "shasum": "", "reference": "abad8c6bbee3447fdc945e6549f43ec9108f98bf"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.2.0"}, "time": "2014-01-15T07:33:01+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1", "sabre/dav": "~1.8.7", "phpseclib/phpseclib": "~0.3.5", "league/phpunit-coverage-listener": "~1.0"}}, {"keywords": ["filesystem", "s3", "aws", "remote", "sftp", "ftp", "dropbox"], "version": "0.1.20", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "638f8f66fd84f5ff42a114f1199555fbf59747b8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/638f8f66fd84f5ff42a114f1199555fbf59747b8", "type": "zip", "shasum": "", "reference": "638f8f66fd84f5ff42a114f1199555fbf59747b8"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.20"}, "time": "2014-01-08T16:38:22+00:00", "autoload": {"psr-0": {"Flysystem": "src/"}}}, {"version": "0.1.19", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "83895c745be843d0289d0df0d98b85c47bc1f925"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/83895c745be843d0289d0df0d98b85c47bc1f925", "type": "zip", "shasum": "", "reference": "83895c745be843d0289d0df0d98b85c47bc1f925"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.19"}, "time": "2014-01-04T15:30:01+00:00"}, {"version": "0.1.18", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "edb69214aaaad76e3e7b82a3df0b58d7ea7df032"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/edb69214aaaad76e3e7b82a3df0b58d7ea7df032", "type": "zip", "shasum": "", "reference": "edb69214aaaad76e3e7b82a3df0b58d7ea7df032"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.18"}, "time": "2013-12-29T21:27:57+00:00"}, {"version": "0.1.17", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4fd9d70c5a3021a113a08123bdb961047cb0c5fd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4fd9d70c5a3021a113a08123bdb961047cb0c5fd", "type": "zip", "shasum": "", "reference": "4fd9d70c5a3021a113a08123bdb961047cb0c5fd"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.17"}, "time": "2013-12-13T11:40:04+00:00"}, {"version": "0.1.16", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "dfa49f12083ff958f713e54c7d77f3c491f35405"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dfa49f12083ff958f713e54c7d77f3c491f35405", "type": "zip", "shasum": "", "reference": "dfa49f12083ff958f713e54c7d77f3c491f35405"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.16"}, "time": "2013-12-12T13:54:20+00:00"}, {"version": "0.1.15", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "8d17962ec45f5bd624ac9aefb1c0a3b023d61aa2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/8d17962ec45f5bd624ac9aefb1c0a3b023d61aa2", "type": "zip", "shasum": "", "reference": "8d17962ec45f5bd624ac9aefb1c0a3b023d61aa2"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.15"}, "time": "2013-12-10T15:31:13+00:00"}, {"version": "0.1.14", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "868167e2a8981e11b0328e45ec2f072d38b87f2e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/868167e2a8981e11b0328e45ec2f072d38b87f2e", "type": "zip", "shasum": "", "reference": "868167e2a8981e11b0328e45ec2f072d38b87f2e"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.14"}, "time": "2013-12-02T12:53:34+00:00"}, {"version": "0.1.13", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4ab50b72b9cc6ab4185cea1408b220c57720c9b9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4ab50b72b9cc6ab4185cea1408b220c57720c9b9", "type": "zip", "shasum": "", "reference": "4ab50b72b9cc6ab4185cea1408b220c57720c9b9"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.13"}, "time": "2013-11-27T16:31:28+00:00"}, {"keywords": ["filesystem", "s3", "aws", "dropbox"], "version": "0.1.12", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "feaa621a14b08294d63812e9078c6c0a62abd051"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/feaa621a14b08294d63812e9078c6c0a62abd051", "type": "zip", "shasum": "", "reference": "feaa621a14b08294d63812e9078c6c0a62abd051"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.12"}, "time": "2013-11-24T14:24:55+00:00"}, {"version": "0.1.11", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "0c4359810190fdfcc73a4d1ab8d7a28f1ab059ff"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0c4359810190fdfcc73a4d1ab8d7a28f1ab059ff", "type": "zip", "shasum": "", "reference": "0c4359810190fdfcc73a4d1ab8d7a28f1ab059ff"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.11"}, "time": "2013-11-22T10:34:20+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1", "sabre/dav": "~1.8.7", "league/phpunit-coverage-listener": "~1.0"}}, {"version": "0.1.10", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "27adbaae7e103b1f6ec06f378fb18240285f23c7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/27adbaae7e103b1f6ec06f378fb18240285f23c7", "type": "zip", "shasum": "", "reference": "27adbaae7e103b1f6ec06f378fb18240285f23c7"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.10"}, "time": "2013-11-21T10:15:52+00:00"}, {"version": "0.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "df96d6d3b9710897b5d30eb3b3ff9af6ca5e0a4f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/df96d6d3b9710897b5d30eb3b3ff9af6ca5e0a4f", "type": "zip", "shasum": "", "reference": "df96d6d3b9710897b5d30eb3b3ff9af6ca5e0a4f"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.9"}, "time": "2013-11-19T17:42:07+00:00"}, {"version": "0.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "4d69e04a00c69c0e7fc8ababc808723ef9da02e4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4d69e04a00c69c0e7fc8ababc808723ef9da02e4", "type": "zip", "shasum": "", "reference": "4d69e04a00c69c0e7fc8ababc808723ef9da02e4"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.8"}, "time": "2013-11-17T16:24:43+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1", "league/phpunit-coverage-listener": "~1.0"}, "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S3 storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching"}}, {"version": "0.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "f30b2224f5b759920d727798c1a0405225264563"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/f30b2224f5b759920d727798c1a0405225264563", "type": "zip", "shasum": "", "reference": "f30b2224f5b759920d727798c1a0405225264563"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.7"}, "time": "2013-11-17T14:47:17+00:00"}, {"version": "0.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "ca0783d52cb52a300cd0a45fd049adceabaa19aa"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/ca0783d52cb52a300cd0a45fd049adceabaa19aa", "type": "zip", "shasum": "", "reference": "ca0783d52cb52a300cd0a45fd049adceabaa19aa"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.6"}, "time": "2013-11-16T15:13:05+00:00"}, {"version": "0.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "7840fc9c55e9b7d60e8278d3a1412f8154f57796"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7840fc9c55e9b7d60e8278d3a1412f8154f57796", "type": "zip", "shasum": "", "reference": "7840fc9c55e9b7d60e8278d3a1412f8154f57796"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.5"}, "time": "2013-11-12T10:40:09+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1"}}, {"version": "0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "00553774f9357654a46a83ebd97ba95b48fddcf2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/00553774f9357654a46a83ebd97ba95b48fddcf2", "type": "zip", "shasum": "", "reference": "00553774f9357654a46a83ebd97ba95b48fddcf2"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.4"}, "time": "2013-11-05T09:53:56+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1", "phpseclib/phpseclib": "~0.3.5"}, "suggest": {"aws/aws-sdk-php": "Allows you to use AWS S2 storage", "dropbox/dropbox-sdk": "Allows you to use Dropbox storage", "phpseclib/phpseclib": "Allows SFTP server storage", "predis/predis": "Allows you to use Predis for caching"}}, {"version": "0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "eea6737805c7d6f410fc989051bf8d51f4a4e39d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/eea6737805c7d6f410fc989051bf8d51f4a4e39d", "type": "zip", "shasum": "", "reference": "eea6737805c7d6f410fc989051bf8d51f4a4e39d"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.3"}, "time": "2013-11-03T22:16:00+00:00", "require-dev": {"aws/aws-sdk-php": "~2.4.7", "predis/predis": "~0.8.4", "mockery/mockery": "~0.8.0", "dropbox/dropbox-sdk": "~1.1.1"}, "suggest": "__unset"}, {"version": "0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "36d18e0f0164e3c20a038db503b1896bb3d975a6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/36d18e0f0164e3c20a038db503b1896bb3d975a6", "type": "zip", "shasum": "", "reference": "36d18e0f0164e3c20a038db503b1896bb3d975a6"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.2"}, "time": "2013-11-03T22:14:32+00:00"}, {"keywords": [], "version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "56bd1e3a5d7caa284ce6f7f10f637e8cf5eac9a1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/56bd1e3a5d7caa284ce6f7f10f637e8cf5eac9a1", "type": "zip", "shasum": "", "reference": "56bd1e3a5d7caa284ce6f7f10f637e8cf5eac9a1"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.1"}, "time": "2013-10-29T13:01:58+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/flysystem.git", "type": "git", "reference": "58f96fb9c08c354732a9bfae72c5543b572fd114"}, "dist": {"url": "https://api.github.com/repos/thephpleague/flysystem/zipball/58f96fb9c08c354732a9bfae72c5543b572fd114", "type": "zip", "shasum": "", "reference": "58f96fb9c08c354732a9bfae72c5543b572fd114"}, "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/0.1.0"}, "time": "2013-10-28T15:56:37+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-pwh8-d4fr-nywn", "affectedVersions": "<1.1.4|>=2.0.0,<2.1.1"}], "last-modified": "Wed, 25 Jun 2025 13:30:34 GMT"}