{"minified": "composer/2.0", "packages": {"filament/notifications": [{"name": "filament/notifications", "description": "Easily add beautiful notifications to any Livewire app.", "keywords": [], "homepage": "https://github.com/filamentphp/filament", "version": "v4.0.0-beta15", "version_normalized": "*******-beta15", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "2af39994a90b0a85f9e7356161604606676ec3a0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/2af39994a90b0a85f9e7356161604606676ec3a0", "type": "zip", "shasum": "", "reference": "2af39994a90b0a85f9e7356161604606676ec3a0"}, "type": "library", "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "funding": [], "time": "2025-07-08T21:00:05+00:00", "autoload": {"files": ["src/Testing/helpers.php"], "psr-4": {"Filament\\Notifications\\": "src"}}, "extra": {"laravel": {"providers": ["Filament\\Notifications\\NotificationsServiceProvider"]}}, "require": {"php": "^8.2", "filament/actions": "self.version", "filament/support": "self.version"}}, {"version": "v4.0.0-beta14", "version_normalized": "*******-beta14", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "11344f83eae04943cb2a4af7631dd891d2a945a4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/11344f83eae04943cb2a4af7631dd891d2a945a4", "type": "zip", "shasum": "", "reference": "11344f83eae04943cb2a4af7631dd891d2a945a4"}, "time": "2025-07-08T12:08:45+00:00"}, {"version": "v4.0.0-beta13", "version_normalized": "*******-beta13", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b774343ba4cb1a8d1ba055436f06c2417cd223ef"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b774343ba4cb1a8d1ba055436f06c2417cd223ef", "type": "zip", "shasum": "", "reference": "b774343ba4cb1a8d1ba055436f06c2417cd223ef"}, "time": "2025-06-15T21:55:22+00:00"}, {"version": "v4.0.0-beta12", "version_normalized": "*******-beta12"}, {"version": "v4.0.0-beta11", "version_normalized": "*******-beta11"}, {"version": "v4.0.0-beta10", "version_normalized": "*******-beta10"}, {"version": "v4.0.0-beta9", "version_normalized": "*******-beta9"}, {"version": "v4.0.0-beta8", "version_normalized": "*******-beta8"}, {"version": "v4.0.0-beta7", "version_normalized": "*******-beta7"}, {"version": "v4.0.0-beta6", "version_normalized": "*******-beta6"}, {"version": "v4.0.0-beta5", "version_normalized": "*******-beta5"}, {"version": "v4.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a4d3f6f64b66b52ead2a2474e132d80bc3332278"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a4d3f6f64b66b52ead2a2474e132d80bc3332278", "type": "zip", "shasum": "", "reference": "a4d3f6f64b66b52ead2a2474e132d80bc3332278"}, "time": "2025-06-10T09:16:15+00:00"}, {"version": "v4.0.0-beta3", "version_normalized": "*******-beta3"}, {"version": "v4.0.0-beta2", "version_normalized": "*******-beta2"}, {"version": "v4.0.0-beta1", "version_normalized": "*******-beta1"}, {"version": "v4.0.0-alpha8", "version_normalized": "*******-alpha8", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ba1c3c18fe70b3073165e11d6684f00783b3d9b2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ba1c3c18fe70b3073165e11d6684f00783b3d9b2", "type": "zip", "shasum": "", "reference": "ba1c3c18fe70b3073165e11d6684f00783b3d9b2"}, "time": "2025-06-02T14:23:39+00:00"}, {"version": "v4.0.0-alpha7", "version_normalized": "*******-alpha7", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d18e15710ed433a7219f693ca88772a30ee26849"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d18e15710ed433a7219f693ca88772a30ee26849", "type": "zip", "shasum": "", "reference": "d18e15710ed433a7219f693ca88772a30ee26849"}, "time": "2025-05-19T07:28:18+00:00"}, {"version": "v4.0.0-alpha6", "version_normalized": "*******-alpha6", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "1764de34803593aa6974ba2aa86f777b8f3238c0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/1764de34803593aa6974ba2aa86f777b8f3238c0", "type": "zip", "shasum": "", "reference": "1764de34803593aa6974ba2aa86f777b8f3238c0"}, "time": "2025-04-28T10:09:58+00:00"}, {"version": "v4.0.0-alpha5", "version_normalized": "*******-alpha5"}, {"version": "v4.0.0-alpha4", "version_normalized": "*******-alpha4"}, {"version": "v4.0.0-alpha3", "version_normalized": "*******-alpha3"}, {"version": "v4.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9db0fc11ea8d658c39956c6ea2fce249be6415dd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9db0fc11ea8d658c39956c6ea2fce249be6415dd", "type": "zip", "shasum": "", "reference": "9db0fc11ea8d658c39956c6ea2fce249be6415dd"}, "time": "2025-04-23T06:32:12+00:00"}, {"version": "v4.0.0-alpha1", "version_normalized": "*******-alpha1"}, {"version": "v3.3.31", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "adc118c7fc34a423f3c01d6936ad0316f489949c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/adc118c7fc34a423f3c01d6936ad0316f489949c", "type": "zip", "shasum": "", "reference": "adc118c7fc34a423f3c01d6936ad0316f489949c"}, "time": "2025-07-08T20:42:18+00:00", "autoload": {"files": ["src/Testing/Autoload.php"], "psr-4": {"Filament\\Notifications\\": "src"}}, "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^10.45|^11.0|^12.0", "illuminate/filesystem": "^10.45|^11.0|^12.0", "illuminate/notifications": "^10.45|^11.0|^12.0", "illuminate/support": "^10.45|^11.0|^12.0", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.3.30", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "356f50e24798a6f06522bfa5123c6ffd054171d3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/356f50e24798a6f06522bfa5123c6ffd054171d3", "type": "zip", "shasum": "", "reference": "356f50e24798a6f06522bfa5123c6ffd054171d3"}, "time": "2025-05-21T08:44:14+00:00"}, {"version": "v3.3.29", "version_normalized": "********"}, {"version": "v3.3.28", "version_normalized": "********"}, {"version": "v3.3.27", "version_normalized": "********"}, {"version": "v3.3.26", "version_normalized": "********"}, {"version": "v3.3.25", "version_normalized": "********"}, {"version": "v3.3.24", "version_normalized": "********"}, {"version": "v3.3.23", "version_normalized": "********"}, {"version": "v3.3.22", "version_normalized": "********"}, {"version": "v3.3.21", "version_normalized": "********"}, {"version": "v3.3.20", "version_normalized": "********"}, {"version": "v3.3.19", "version_normalized": "********"}, {"version": "v3.3.18", "version_normalized": "********"}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "edf7960621b2181b4c2fc040b0712fbd5dd036ef"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/edf7960621b2181b4c2fc040b0712fbd5dd036ef", "type": "zip", "shasum": "", "reference": "edf7960621b2181b4c2fc040b0712fbd5dd036ef"}, "time": "2025-04-23T06:39:49+00:00"}, {"version": "v3.3.14", "version_normalized": "********"}, {"version": "v3.3.13", "version_normalized": "********"}, {"version": "v3.3.12", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d4bb90c77a3e88ab833cab71d36b185b3670a314"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d4bb90c77a3e88ab833cab71d36b185b3670a314", "type": "zip", "shasum": "", "reference": "d4bb90c77a3e88ab833cab71d36b185b3670a314"}, "time": "2025-04-02T09:55:26+00:00"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********"}, {"version": "v3.3.9", "version_normalized": "*******"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "25d37ce5c74fcd339490b1cf89c4a4d3db3eb87d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/25d37ce5c74fcd339490b1cf89c4a4d3db3eb87d", "type": "zip", "shasum": "", "reference": "25d37ce5c74fcd339490b1cf89c4a4d3db3eb87d"}, "time": "2025-03-11T16:33:09+00:00"}, {"version": "v3.3.6", "version_normalized": "*******"}, {"version": "v3.3.5", "version_normalized": "*******"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "8cfe18e5d04ba72d777753ed632bbcf3408236a2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/8cfe18e5d04ba72d777753ed632bbcf3408236a2", "type": "zip", "shasum": "", "reference": "8cfe18e5d04ba72d777753ed632bbcf3408236a2"}, "time": "2025-02-25T08:18:58+00:00"}, {"version": "v3.3.2", "version_normalized": "*******"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******"}, {"version": "v3.2.142", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5c7809f5672ec721f09a65b3a6394bd79b2a9ee2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5c7809f5672ec721f09a65b3a6394bd79b2a9ee2", "type": "zip", "shasum": "", "reference": "5c7809f5672ec721f09a65b3a6394bd79b2a9ee2"}, "time": "2025-02-19T08:42:24+00:00", "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^10.45|^11.0", "illuminate/filesystem": "^10.45|^11.0", "illuminate/notifications": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.2.141", "version_normalized": "*********"}, {"version": "v3.2.140", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "1b5c8cf1e8cf2022e437637d7cceb4ad378982a4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/1b5c8cf1e8cf2022e437637d7cceb4ad378982a4", "type": "zip", "shasum": "", "reference": "1b5c8cf1e8cf2022e437637d7cceb4ad378982a4"}, "time": "2025-02-10T08:14:22+00:00"}, {"version": "v3.2.139", "version_normalized": "*********"}, {"version": "v3.2.138", "version_normalized": "*********"}, {"version": "v3.2.137", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "214845e9f613716304707441c8227d2d70b2aabf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/214845e9f613716304707441c8227d2d70b2aabf", "type": "zip", "shasum": "", "reference": "214845e9f613716304707441c8227d2d70b2aabf"}, "time": "2025-01-31T11:08:23+00:00"}, {"version": "v3.2.136", "version_normalized": "*********"}, {"version": "v3.2.135", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e864c50bc0b6e9eb46b5e3d93a672a66f80a0fbe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e864c50bc0b6e9eb46b5e3d93a672a66f80a0fbe", "type": "zip", "shasum": "", "reference": "e864c50bc0b6e9eb46b5e3d93a672a66f80a0fbe"}, "time": "2025-01-24T09:27:49+00:00"}, {"version": "v3.2.134", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "7f0f0e721a0cdb49b10f4d1b5b1fc435c35ea5df"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/7f0f0e721a0cdb49b10f4d1b5b1fc435c35ea5df", "type": "zip", "shasum": "", "reference": "7f0f0e721a0cdb49b10f4d1b5b1fc435c35ea5df"}, "time": "2025-01-22T10:23:12+00:00"}, {"version": "v3.2.133", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "16cf5dbcbaf88cd9fff8d06aad866cb314b8bb64"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/16cf5dbcbaf88cd9fff8d06aad866cb314b8bb64", "type": "zip", "shasum": "", "reference": "16cf5dbcbaf88cd9fff8d06aad866cb314b8bb64"}, "time": "2025-01-10T12:48:23+00:00"}, {"version": "v3.2.132", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "c19df07c801c5550de0d30957c5a316f53019533"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/c19df07c801c5550de0d30957c5a316f53019533", "type": "zip", "shasum": "", "reference": "c19df07c801c5550de0d30957c5a316f53019533"}, "time": "2024-10-23T07:36:14+00:00"}, {"version": "v3.2.131", "version_normalized": "*********"}, {"version": "v3.2.130", "version_normalized": "*********"}, {"version": "v3.2.129", "version_normalized": "*********"}, {"version": "v3.2.128", "version_normalized": "*********"}, {"version": "v3.2.127", "version_normalized": "*********"}, {"version": "v3.2.126", "version_normalized": "*********"}, {"version": "v3.2.125", "version_normalized": "*********"}, {"version": "v3.2.124", "version_normalized": "*********"}, {"version": "v3.2.123", "version_normalized": "*********"}, {"version": "v3.2.122", "version_normalized": "*********"}, {"version": "v3.2.121", "version_normalized": "*********"}, {"version": "v3.2.120", "version_normalized": "*********"}, {"version": "v3.2.119", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b56b155efd4290459d944a1b3d515b5aaf54846d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b56b155efd4290459d944a1b3d515b5aaf54846d", "type": "zip", "shasum": "", "reference": "b56b155efd4290459d944a1b3d515b5aaf54846d"}, "time": "2024-10-16T12:07:28+00:00"}, {"version": "v3.2.118", "version_normalized": "*********"}, {"version": "v3.2.117", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a5f684b690354630210fc9a90bd06da9b1f6ae82"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a5f684b690354630210fc9a90bd06da9b1f6ae82", "type": "zip", "shasum": "", "reference": "a5f684b690354630210fc9a90bd06da9b1f6ae82"}, "time": "2024-10-08T14:24:11+00:00"}, {"version": "v3.2.116", "version_normalized": "*********"}, {"version": "v3.2.115", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0272612e1d54e0520f8717b24c71b9b70f198c8f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0272612e1d54e0520f8717b24c71b9b70f198c8f", "type": "zip", "shasum": "", "reference": "0272612e1d54e0520f8717b24c71b9b70f198c8f"}, "time": "2024-09-27T13:16:07+00:00"}, {"version": "v3.2.114", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "03ea56e0729c98c65831ab0215285a7cb1c4117f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/03ea56e0729c98c65831ab0215285a7cb1c4117f", "type": "zip", "shasum": "", "reference": "03ea56e0729c98c65831ab0215285a7cb1c4117f"}, "time": "2024-07-31T11:53:11+00:00"}, {"version": "v3.2.113", "version_normalized": "*********"}, {"version": "v3.2.112", "version_normalized": "*********"}, {"version": "v3.2.111", "version_normalized": "*********"}, {"version": "v3.2.110", "version_normalized": "*********"}, {"version": "v3.2.109", "version_normalized": "*********"}, {"version": "v3.2.108", "version_normalized": "*********"}, {"version": "v3.2.107", "version_normalized": "*********"}, {"version": "v3.2.106", "version_normalized": "*********"}, {"version": "v3.2.105", "version_normalized": "*********"}, {"version": "v3.2.104", "version_normalized": "*********"}, {"version": "v3.2.103", "version_normalized": "*********"}, {"version": "v3.2.102", "version_normalized": "*********"}, {"version": "v3.2.101", "version_normalized": "*********"}, {"version": "v3.2.100", "version_normalized": "*********"}, {"version": "v3.2.99", "version_normalized": "********"}, {"version": "v3.2.98", "version_normalized": "********"}, {"version": "v3.2.97", "version_normalized": "********"}, {"version": "v3.2.96", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "df0aa8997e90fb9409ea6baf5b4c9bf10c592563"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/df0aa8997e90fb9409ea6baf5b4c9bf10c592563", "type": "zip", "shasum": "", "reference": "df0aa8997e90fb9409ea6baf5b4c9bf10c592563"}, "time": "2024-07-10T17:10:55+00:00"}, {"version": "v3.2.95", "version_normalized": "********"}, {"version": "v3.2.94", "version_normalized": "********"}, {"version": "v3.2.93", "version_normalized": "********"}, {"version": "v3.2.92", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a78b0be5e5b2a598e65ba62ae5cfbb3d464f6bbb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a78b0be5e5b2a598e65ba62ae5cfbb3d464f6bbb", "type": "zip", "shasum": "", "reference": "a78b0be5e5b2a598e65ba62ae5cfbb3d464f6bbb"}, "time": "2024-05-30T12:37:03+00:00"}, {"version": "v3.2.91", "version_normalized": "********"}, {"version": "v3.2.90", "version_normalized": "********"}, {"version": "v3.2.89", "version_normalized": "********"}, {"version": "v3.2.88", "version_normalized": "********"}, {"version": "v3.2.87", "version_normalized": "********"}, {"version": "v3.2.87-beta1", "version_normalized": "********-beta1"}, {"version": "v3.2.86", "version_normalized": "********"}, {"version": "v3.2.85", "version_normalized": "********"}, {"version": "v3.2.84", "version_normalized": "********"}, {"version": "v3.2.83", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "646ed10673b03144ff8341a6c42bea04f6443b81"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/646ed10673b03144ff8341a6c42bea04f6443b81", "type": "zip", "shasum": "", "reference": "646ed10673b03144ff8341a6c42bea04f6443b81"}, "time": "2024-05-08T16:21:24+00:00"}, {"version": "v3.2.82", "version_normalized": "********"}, {"version": "v3.2.81", "version_normalized": "********"}, {"version": "v3.2.80", "version_normalized": "********"}, {"version": "v3.2.79", "version_normalized": "********"}, {"version": "v3.2.78", "version_normalized": "********"}, {"version": "v3.2.77", "version_normalized": "********"}, {"version": "v3.2.76", "version_normalized": "********"}, {"version": "v3.2.75", "version_normalized": "********"}, {"version": "v3.2.74", "version_normalized": "********"}, {"version": "v3.2.73", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f06016bea87b8b688eeafbea19f24720062cd87e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f06016bea87b8b688eeafbea19f24720062cd87e", "type": "zip", "shasum": "", "reference": "f06016bea87b8b688eeafbea19f24720062cd87e"}, "time": "2024-05-03T12:25:54+00:00"}, {"version": "v3.2.72", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a37c926d2edbcb07e6fd8a354ce2f5daa59ad92e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a37c926d2edbcb07e6fd8a354ce2f5daa59ad92e", "type": "zip", "shasum": "", "reference": "a37c926d2edbcb07e6fd8a354ce2f5daa59ad92e"}, "time": "2024-04-28T08:39:09+00:00"}, {"version": "v3.2.71", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "dcc47b498c2a5a89296c2f46da651a8aa5e0bf55"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/dcc47b498c2a5a89296c2f46da651a8aa5e0bf55", "type": "zip", "shasum": "", "reference": "dcc47b498c2a5a89296c2f46da651a8aa5e0bf55"}, "time": "2024-04-18T11:26:15+00:00"}, {"version": "v3.2.70", "version_normalized": "********"}, {"version": "v3.2.69", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5731f4b9eb2b3f292b45fbb9ce173ee7d6b7dddb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5731f4b9eb2b3f292b45fbb9ce173ee7d6b7dddb", "type": "zip", "shasum": "", "reference": "5731f4b9eb2b3f292b45fbb9ce173ee7d6b7dddb"}, "time": "2024-04-16T15:42:43+00:00"}, {"version": "v3.2.68", "version_normalized": "********"}, {"version": "v3.2.67", "version_normalized": "********"}, {"version": "v3.2.66", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "067117fb0708dfd04955faafcfc82cd6d182e52f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/067117fb0708dfd04955faafcfc82cd6d182e52f", "type": "zip", "shasum": "", "reference": "067117fb0708dfd04955faafcfc82cd6d182e52f"}, "time": "2024-04-11T21:38:28+00:00"}, {"version": "v3.2.65", "version_normalized": "********"}, {"version": "v3.2.64", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e644a09c1b68b20b197aeaa1db14423f9e526447"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e644a09c1b68b20b197aeaa1db14423f9e526447", "type": "zip", "shasum": "", "reference": "e644a09c1b68b20b197aeaa1db14423f9e526447"}, "time": "2024-04-11T07:40:39+00:00"}, {"version": "v3.2.63", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "27efac9801a7688e991b4c8e029c52922222fc8a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/27efac9801a7688e991b4c8e029c52922222fc8a", "type": "zip", "shasum": "", "reference": "27efac9801a7688e991b4c8e029c52922222fc8a"}, "time": "2024-04-05T21:55:20+00:00"}, {"version": "v3.2.62", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0739152934bd238b838e1abd1d3c9b037f9e6da3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0739152934bd238b838e1abd1d3c9b037f9e6da3", "type": "zip", "shasum": "", "reference": "0739152934bd238b838e1abd1d3c9b037f9e6da3"}, "time": "2024-04-01T18:40:59+00:00"}, {"version": "v3.2.61", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "069a37c9f260918741fd80167859d7578ea18887"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/069a37c9f260918741fd80167859d7578ea18887", "type": "zip", "shasum": "", "reference": "069a37c9f260918741fd80167859d7578ea18887"}, "time": "2024-03-27T12:36:01+00:00"}, {"version": "v3.2.60", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f9a790ee2d5103ea87c88e6d545ea6ceb770cbe5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f9a790ee2d5103ea87c88e6d545ea6ceb770cbe5", "type": "zip", "shasum": "", "reference": "f9a790ee2d5103ea87c88e6d545ea6ceb770cbe5"}, "time": "2024-03-19T00:28:12+00:00"}, {"version": "v3.2.59", "version_normalized": "********"}, {"version": "v3.2.58", "version_normalized": "********"}, {"version": "v3.2.57", "version_normalized": "********"}, {"version": "v3.2.56", "version_normalized": "********"}, {"version": "v3.2.55", "version_normalized": "********"}, {"version": "v3.2.54", "version_normalized": "********"}, {"version": "v3.2.53", "version_normalized": "********"}, {"version": "v3.2.52", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a25fe75eeebc61d4a6e2bfa88e9e501a5f8020bd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a25fe75eeebc61d4a6e2bfa88e9e501a5f8020bd", "type": "zip", "shasum": "", "reference": "a25fe75eeebc61d4a6e2bfa88e9e501a5f8020bd"}, "time": "2024-03-14T10:54:06+00:00"}, {"version": "v3.2.51", "version_normalized": "********"}, {"version": "v3.2.50", "version_normalized": "********"}, {"version": "v3.2.49", "version_normalized": "********"}, {"version": "v3.2.48", "version_normalized": "********"}, {"version": "v3.2.47", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "93c1d72b12ff1bed520d082a1292ab25a566a8d8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/93c1d72b12ff1bed520d082a1292ab25a566a8d8", "type": "zip", "shasum": "", "reference": "93c1d72b12ff1bed520d082a1292ab25a566a8d8"}, "time": "2024-02-29T12:30:04+00:00"}, {"version": "v3.2.46", "version_normalized": "********"}, {"version": "v3.2.45", "version_normalized": "********"}, {"version": "v3.2.44", "version_normalized": "********"}, {"version": "v3.2.43", "version_normalized": "********"}, {"version": "v3.2.42", "version_normalized": "********"}, {"version": "v3.2.41", "version_normalized": "********"}, {"version": "v3.2.40", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e6809dd500ce6b061c3bfb7d19dc03248572611e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e6809dd500ce6b061c3bfb7d19dc03248572611e", "type": "zip", "shasum": "", "reference": "e6809dd500ce6b061c3bfb7d19dc03248572611e"}, "time": "2024-02-27T15:32:43+00:00"}, {"version": "v3.2.39", "version_normalized": "********"}, {"version": "v3.2.38", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ae0f5c20df16f5ba28d524f3b2b286753db42cf7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ae0f5c20df16f5ba28d524f3b2b286753db42cf7", "type": "zip", "shasum": "", "reference": "ae0f5c20df16f5ba28d524f3b2b286753db42cf7"}, "time": "2024-02-26T20:23:03+00:00", "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^10.45", "illuminate/filesystem": "^10.45", "illuminate/notifications": "^10.45", "illuminate/support": "^10.45", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.2.37", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "41f3b06547c8ea204049ebee296a6f42bc9be086"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/41f3b06547c8ea204049ebee296a6f42bc9be086", "type": "zip", "shasum": "", "reference": "41f3b06547c8ea204049ebee296a6f42bc9be086"}, "time": "2024-02-23T22:26:08+00:00"}, {"version": "v3.2.36", "version_normalized": "********"}, {"version": "v3.2.35", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "4786bb652bf6d64a9e68cf8302cf761f8dff3c4b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/4786bb652bf6d64a9e68cf8302cf761f8dff3c4b", "type": "zip", "shasum": "", "reference": "4786bb652bf6d64a9e68cf8302cf761f8dff3c4b"}, "time": "2024-02-07T18:46:44+00:00", "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^10.0", "illuminate/filesystem": "^10.0", "illuminate/notifications": "^10.0", "illuminate/support": "^10.0", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.2.34", "version_normalized": "********"}, {"version": "v3.2.33", "version_normalized": "********"}, {"version": "v3.2.32", "version_normalized": "********"}, {"version": "v3.2.31", "version_normalized": "********"}, {"version": "v3.2.30", "version_normalized": "********"}, {"version": "v3.2.29", "version_normalized": "********"}, {"version": "v3.2.28", "version_normalized": "********"}, {"version": "v3.2.27", "version_normalized": "********"}, {"version": "v3.2.26", "version_normalized": "********"}, {"version": "v3.2.25", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "c5f4f51d949fafc52643f2be654a4da92422836c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/c5f4f51d949fafc52643f2be654a4da92422836c", "type": "zip", "shasum": "", "reference": "c5f4f51d949fafc52643f2be654a4da92422836c"}, "time": "2024-01-19T14:01:21+00:00"}, {"version": "v3.2.25-beta1", "version_normalized": "********-beta1"}, {"version": "v3.2.24", "version_normalized": "********"}, {"version": "v3.2.23", "version_normalized": "********"}, {"version": "v3.2.22", "version_normalized": "********"}, {"version": "v3.2.21", "version_normalized": "********"}, {"version": "v3.2.20", "version_normalized": "********"}, {"version": "v3.2.19", "version_normalized": "********"}, {"version": "v3.2.18", "version_normalized": "********"}, {"version": "v3.2.17", "version_normalized": "********"}, {"version": "v3.2.16", "version_normalized": "********"}, {"version": "v3.2.15", "version_normalized": "********"}, {"version": "v3.2.14", "version_normalized": "********"}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******"}, {"version": "v3.2.8", "version_normalized": "*******"}, {"version": "v3.2.7", "version_normalized": "*******"}, {"version": "v3.2.6", "version_normalized": "*******"}, {"version": "v3.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "34ceed1e2063b1ffeaf368c47fc6579003538cee"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/34ceed1e2063b1ffeaf368c47fc6579003538cee", "type": "zip", "shasum": "", "reference": "34ceed1e2063b1ffeaf368c47fc6579003538cee"}, "time": "2024-01-18T11:11:30+00:00"}, {"version": "v3.2.4", "version_normalized": "*******"}, {"version": "v3.2.3", "version_normalized": "*******"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ce2daf3389cfff46fd0a15bd49264290d59b077c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ce2daf3389cfff46fd0a15bd49264290d59b077c", "type": "zip", "shasum": "", "reference": "ce2daf3389cfff46fd0a15bd49264290d59b077c"}, "time": "2024-01-15T20:39:02+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "eaf2eccd085fa1fc67d0f13c99ae32da8d3ab93c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/eaf2eccd085fa1fc67d0f13c99ae32da8d3ab93c", "type": "zip", "shasum": "", "reference": "eaf2eccd085fa1fc67d0f13c99ae32da8d3ab93c"}, "time": "2024-01-15T12:25:21+00:00"}, {"version": "v3.2.0", "version_normalized": "*******"}, {"version": "v3.1.47", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "4f5634f9df312050efa3a035c543add6cec0e3a2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/4f5634f9df312050efa3a035c543add6cec0e3a2", "type": "zip", "shasum": "", "reference": "4f5634f9df312050efa3a035c543add6cec0e3a2"}, "time": "2023-12-28T15:54:27+00:00"}, {"version": "v3.1.46", "version_normalized": "********"}, {"version": "v3.1.45", "version_normalized": "********"}, {"version": "v3.1.44", "version_normalized": "********"}, {"version": "v3.1.43", "version_normalized": "********"}, {"version": "v3.1.42", "version_normalized": "********"}, {"version": "v3.1.41", "version_normalized": "********"}, {"version": "v3.1.40", "version_normalized": "********"}, {"version": "v3.1.39", "version_normalized": "********"}, {"version": "v3.1.38", "version_normalized": "********"}, {"version": "v3.1.37", "version_normalized": "********"}, {"version": "v3.1.36", "version_normalized": "********"}, {"version": "v3.1.35", "version_normalized": "********"}, {"version": "v3.1.34", "version_normalized": "********"}, {"version": "v3.1.33", "version_normalized": "********"}, {"version": "v3.1.32", "version_normalized": "********"}, {"version": "v3.1.31", "version_normalized": "********"}, {"version": "v3.1.30", "version_normalized": "********"}, {"version": "v3.1.29", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9a84cdd8c6c89b0447effabfc454c346994d78d9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9a84cdd8c6c89b0447effabfc454c346994d78d9", "type": "zip", "shasum": "", "reference": "9a84cdd8c6c89b0447effabfc454c346994d78d9"}, "time": "2023-12-24T21:16:05+00:00"}, {"version": "v3.1.28", "version_normalized": "********"}, {"version": "v3.1.27", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5b9255c0793751534461cace12febdc0d4cc05bb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5b9255c0793751534461cace12febdc0d4cc05bb", "type": "zip", "shasum": "", "reference": "5b9255c0793751534461cace12febdc0d4cc05bb"}, "time": "2023-12-17T22:25:42+00:00"}, {"version": "v3.1.26", "version_normalized": "********"}, {"version": "v3.1.25", "version_normalized": "********"}, {"version": "v3.1.24", "version_normalized": "********"}, {"version": "v3.1.23", "version_normalized": "********"}, {"version": "v3.1.22", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "702dfd12d46e8b431bad6e6b80f63f6638ce6230"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/702dfd12d46e8b431bad6e6b80f63f6638ce6230", "type": "zip", "shasum": "", "reference": "702dfd12d46e8b431bad6e6b80f63f6638ce6230"}, "time": "2023-12-14T16:20:58+00:00"}, {"version": "v3.1.21", "version_normalized": "********"}, {"version": "v3.1.20", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "6ed2dc4a4616bf344db7decab0432c36c0900b09"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/6ed2dc4a4616bf344db7decab0432c36c0900b09", "type": "zip", "shasum": "", "reference": "6ed2dc4a4616bf344db7decab0432c36c0900b09"}, "time": "2023-12-12T14:20:34+00:00"}, {"version": "v3.1.19", "version_normalized": "********"}, {"version": "v3.1.18", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "13029b0f257ce9f0f9691b5fe8de933c581d03bc"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/13029b0f257ce9f0f9691b5fe8de933c581d03bc", "type": "zip", "shasum": "", "reference": "13029b0f257ce9f0f9691b5fe8de933c581d03bc"}, "time": "2023-12-07T12:32:30+00:00"}, {"version": "v3.1.17", "version_normalized": "********"}, {"version": "v3.1.16", "version_normalized": "********"}, {"version": "v3.1.15", "version_normalized": "********"}, {"version": "v3.1.14", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b0a917a041bee3819a3b58c543d82ba463ffb20c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b0a917a041bee3819a3b58c543d82ba463ffb20c", "type": "zip", "shasum": "", "reference": "b0a917a041bee3819a3b58c543d82ba463ffb20c"}, "time": "2023-12-02T22:24:00+00:00"}, {"version": "v3.1.13", "version_normalized": "********"}, {"version": "v3.1.12", "version_normalized": "********"}, {"version": "v3.1.11", "version_normalized": "********"}, {"version": "v3.1.10", "version_normalized": "********"}, {"version": "v3.1.9", "version_normalized": "*******"}, {"version": "v3.1.8", "version_normalized": "*******"}, {"version": "v3.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0ee13375c95d7e1f633c9ddc2ba092da1d45e239"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0ee13375c95d7e1f633c9ddc2ba092da1d45e239", "type": "zip", "shasum": "", "reference": "0ee13375c95d7e1f633c9ddc2ba092da1d45e239"}, "time": "2023-12-01T09:44:31+00:00"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "81aa47d8964b84926f9511dae2943c1d0e15aa22"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/81aa47d8964b84926f9511dae2943c1d0e15aa22", "type": "zip", "shasum": "", "reference": "81aa47d8964b84926f9511dae2943c1d0e15aa22"}, "time": "2023-11-29T15:29:41+00:00"}, {"version": "v3.1.5", "version_normalized": "*******"}, {"version": "v3.1.4", "version_normalized": "*******"}, {"version": "v3.1.3", "version_normalized": "*******"}, {"version": "v3.1.2", "version_normalized": "*******"}, {"version": "v3.1.1", "version_normalized": "*******"}, {"version": "v3.1.0", "version_normalized": "*******"}, {"version": "v3.1.0-alpha4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "c2248039a7cfb507c5e8a4cc202637b73cf39782"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/c2248039a7cfb507c5e8a4cc202637b73cf39782", "type": "zip", "shasum": "", "reference": "c2248039a7cfb507c5e8a4cc202637b73cf39782"}, "time": "2023-11-20T15:56:46+00:00"}, {"version": "v3.1.0-alpha3", "version_normalized": "*******-alpha3"}, {"version": "v3.1.0-alpha2", "version_normalized": "*******-alpha2"}, {"version": "v3.1.0-alpha1", "version_normalized": "*******-alpha1"}, {"version": "v3.0.103", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b1953086f02bb76a4032ca0e229161ce32132d00"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b1953086f02bb76a4032ca0e229161ce32132d00", "type": "zip", "shasum": "", "reference": "b1953086f02bb76a4032ca0e229161ce32132d00"}, "time": "2023-11-25T23:47:31+00:00"}, {"version": "v3.0.102", "version_normalized": "*********"}, {"version": "v3.0.101", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "88324fa22de9d3b9cc94e3feebb786fe00a35b31"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/88324fa22de9d3b9cc94e3feebb786fe00a35b31", "type": "zip", "shasum": "", "reference": "88324fa22de9d3b9cc94e3feebb786fe00a35b31"}, "time": "2023-11-21T10:52:57+00:00"}, {"version": "v3.0.100", "version_normalized": "*********"}, {"version": "v3.0.99", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "65ac499a30fe00e9e2b21cd873e7a10e28f9efb8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/65ac499a30fe00e9e2b21cd873e7a10e28f9efb8", "type": "zip", "shasum": "", "reference": "65ac499a30fe00e9e2b21cd873e7a10e28f9efb8"}, "time": "2023-11-14T12:07:17+00:00"}, {"version": "v3.0.98", "version_normalized": "********"}, {"version": "v3.0.97", "version_normalized": "********"}, {"version": "v3.0.96", "version_normalized": "********"}, {"version": "v3.0.95", "version_normalized": "********"}, {"version": "v3.0.94", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b1de7b5fdcb70c74244ab446b309de910bb15aa3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b1de7b5fdcb70c74244ab446b309de910bb15aa3", "type": "zip", "shasum": "", "reference": "b1de7b5fdcb70c74244ab446b309de910bb15aa3"}, "time": "2023-11-09T10:42:45+00:00"}, {"version": "v3.0.93", "version_normalized": "********"}, {"version": "v3.0.92", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "341d60b65808467c16a392b203bb4b772a314395"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/341d60b65808467c16a392b203bb4b772a314395", "type": "zip", "shasum": "", "reference": "341d60b65808467c16a392b203bb4b772a314395"}, "time": "2023-11-05T09:01:18+00:00"}, {"version": "v3.0.91", "version_normalized": "********"}, {"version": "v3.0.90", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e740c3b5c4d4d247bd27b9dac95c3b0a765542a6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e740c3b5c4d4d247bd27b9dac95c3b0a765542a6", "type": "zip", "shasum": "", "reference": "e740c3b5c4d4d247bd27b9dac95c3b0a765542a6"}, "time": "2023-11-03T09:01:15+00:00"}, {"version": "v3.0.89", "version_normalized": "********"}, {"version": "v3.0.88", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b54eb25ec100e65b960fb8b90c9d38c132eb5bb6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b54eb25ec100e65b960fb8b90c9d38c132eb5bb6", "type": "zip", "shasum": "", "reference": "b54eb25ec100e65b960fb8b90c9d38c132eb5bb6"}, "time": "2023-10-27T14:34:57+00:00"}, {"version": "v3.0.87", "version_normalized": "********"}, {"version": "v3.0.86", "version_normalized": "********"}, {"version": "v3.0.85", "version_normalized": "********"}, {"version": "v3.0.84", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "87d7187d4eb6776a42c70bbf6b93eb7fa3f2760d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/87d7187d4eb6776a42c70bbf6b93eb7fa3f2760d", "type": "zip", "shasum": "", "reference": "87d7187d4eb6776a42c70bbf6b93eb7fa3f2760d"}, "time": "2023-10-26T13:32:05+00:00"}, {"version": "v3.0.83", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "dcb37f82172ed1f7b2594cce7beaee7e2f3034a7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/dcb37f82172ed1f7b2594cce7beaee7e2f3034a7", "type": "zip", "shasum": "", "reference": "dcb37f82172ed1f7b2594cce7beaee7e2f3034a7"}, "time": "2023-10-23T11:42:10+00:00"}, {"version": "v3.0.82", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5c7abcd60771f077d5c3e92561646de13c58b9fe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5c7abcd60771f077d5c3e92561646de13c58b9fe", "type": "zip", "shasum": "", "reference": "5c7abcd60771f077d5c3e92561646de13c58b9fe"}, "time": "2023-10-17T14:55:15+00:00"}, {"version": "v3.0.81", "version_normalized": "********"}, {"version": "v3.0.80", "version_normalized": "********"}, {"version": "v3.0.79", "version_normalized": "********"}, {"version": "v3.0.78", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ae21e99853c0df0a093001e6db36b844f6fec535"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ae21e99853c0df0a093001e6db36b844f6fec535", "type": "zip", "shasum": "", "reference": "ae21e99853c0df0a093001e6db36b844f6fec535"}, "time": "2023-10-13T19:26:43+00:00"}, {"version": "v3.0.77", "version_normalized": "********"}, {"version": "v3.0.76", "version_normalized": "********"}, {"version": "v3.0.75", "version_normalized": "********"}, {"version": "v3.0.74", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "72e1e9ca8abb794f9a86ed98ee6a61971244cd1a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/72e1e9ca8abb794f9a86ed98ee6a61971244cd1a", "type": "zip", "shasum": "", "reference": "72e1e9ca8abb794f9a86ed98ee6a61971244cd1a"}, "time": "2023-10-12T12:23:40+00:00"}, {"version": "v3.0.73", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "4fe60598157c53b712c46944614fb6a57fb93c04"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/4fe60598157c53b712c46944614fb6a57fb93c04", "type": "zip", "shasum": "", "reference": "4fe60598157c53b712c46944614fb6a57fb93c04"}, "time": "2023-10-05T15:18:17+00:00"}, {"version": "v3.0.72", "version_normalized": "********"}, {"version": "v3.0.71", "version_normalized": "********"}, {"version": "v3.0.70", "version_normalized": "********"}, {"version": "v3.0.69", "version_normalized": "********"}, {"version": "v3.0.68", "version_normalized": "********"}, {"version": "v3.0.67", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "489d3caff1e5b2dae46bd1cb926e2fe7567d1e8b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/489d3caff1e5b2dae46bd1cb926e2fe7567d1e8b", "type": "zip", "shasum": "", "reference": "489d3caff1e5b2dae46bd1cb926e2fe7567d1e8b"}, "time": "2023-10-03T18:16:40+00:00"}, {"version": "v3.0.66", "version_normalized": "********"}, {"version": "v3.0.65", "version_normalized": "********"}, {"version": "v3.0.64", "version_normalized": "********"}, {"version": "v3.0.63", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "bcca58686b750084d9052bfc71b56ff2d0defa6f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/bcca58686b750084d9052bfc71b56ff2d0defa6f", "type": "zip", "shasum": "", "reference": "bcca58686b750084d9052bfc71b56ff2d0defa6f"}, "time": "2023-09-30T14:07:48+00:00"}, {"version": "v3.0.62", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f9eb47d71e53f3f31af995ce09facb895fe605d4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f9eb47d71e53f3f31af995ce09facb895fe605d4", "type": "zip", "shasum": "", "reference": "f9eb47d71e53f3f31af995ce09facb895fe605d4"}, "time": "2023-09-25T19:23:46+00:00"}, {"version": "v3.0.61", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "77cae83005e092d072a6a867cf457d5f1959aa9a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/77cae83005e092d072a6a867cf457d5f1959aa9a", "type": "zip", "shasum": "", "reference": "77cae83005e092d072a6a867cf457d5f1959aa9a"}, "time": "2023-09-25T12:42:21+00:00"}, {"version": "v3.0.60", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "cd7ec21f025b06070ffd20f967f77eca49a8a9ce"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/cd7ec21f025b06070ffd20f967f77eca49a8a9ce", "type": "zip", "shasum": "", "reference": "cd7ec21f025b06070ffd20f967f77eca49a8a9ce"}, "time": "2023-09-21T21:29:13+00:00"}, {"version": "v3.0.59", "version_normalized": "********"}, {"version": "v3.0.58", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "7885b9e9a48e20782a4e0662141bc6a3fa2ecdd6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/7885b9e9a48e20782a4e0662141bc6a3fa2ecdd6", "type": "zip", "shasum": "", "reference": "7885b9e9a48e20782a4e0662141bc6a3fa2ecdd6"}, "time": "2023-09-21T18:39:37+00:00"}, {"version": "v3.0.57", "version_normalized": "********"}, {"version": "v3.0.56", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "288355f86d973a69bd483836b9a38d000b8b2ad2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/288355f86d973a69bd483836b9a38d000b8b2ad2", "type": "zip", "shasum": "", "reference": "288355f86d973a69bd483836b9a38d000b8b2ad2"}, "time": "2023-09-18T10:30:34+00:00"}, {"version": "v3.0.55", "version_normalized": "********"}, {"version": "v3.0.54", "version_normalized": "********"}, {"version": "v3.0.53", "version_normalized": "********"}, {"version": "v3.0.52", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b675b8310bb4984334bc5b01b3b770a6b0d56a83"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b675b8310bb4984334bc5b01b3b770a6b0d56a83", "type": "zip", "shasum": "", "reference": "b675b8310bb4984334bc5b01b3b770a6b0d56a83"}, "time": "2023-09-06T14:23:07+00:00"}, {"version": "v3.0.51", "version_normalized": "********"}, {"version": "v3.0.50", "version_normalized": "********"}, {"version": "v3.0.49", "version_normalized": "********"}, {"version": "v3.0.48", "version_normalized": "********"}, {"version": "v3.0.47", "version_normalized": "********"}, {"version": "v3.0.46", "version_normalized": "********"}, {"version": "v3.0.45", "version_normalized": "********"}, {"version": "v3.0.44", "version_normalized": "********"}, {"version": "v3.0.43", "version_normalized": "********"}, {"version": "v3.0.42", "version_normalized": "********"}, {"version": "v3.0.41", "version_normalized": "********"}, {"version": "v3.0.40", "version_normalized": "********"}, {"version": "v3.0.39", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "274501cb3217bd70827761111266e68d1ee8450d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/274501cb3217bd70827761111266e68d1ee8450d", "type": "zip", "shasum": "", "reference": "274501cb3217bd70827761111266e68d1ee8450d"}, "time": "2023-08-31T11:14:40+00:00"}, {"version": "v3.0.38", "version_normalized": "********"}, {"version": "v3.0.37", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e2625ddb10aaad18f42d45d2dc66e48490bb11e2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e2625ddb10aaad18f42d45d2dc66e48490bb11e2", "type": "zip", "shasum": "", "reference": "e2625ddb10aaad18f42d45d2dc66e48490bb11e2"}, "time": "2023-08-29T20:13:03+00:00"}, {"version": "v3.0.36", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f99fcb0ed34b3f8dddc1db984f61df7c3729aa4f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f99fcb0ed34b3f8dddc1db984f61df7c3729aa4f", "type": "zip", "shasum": "", "reference": "f99fcb0ed34b3f8dddc1db984f61df7c3729aa4f"}, "time": "2023-08-24T15:59:53+00:00"}, {"version": "v3.0.35", "version_normalized": "********"}, {"version": "v3.0.34", "version_normalized": "********"}, {"version": "v3.0.33", "version_normalized": "********"}, {"version": "v3.0.32", "version_normalized": "********"}, {"version": "v3.0.31", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "2035d7a14b3071aec49b2cd5fc2a239935921390"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/2035d7a14b3071aec49b2cd5fc2a239935921390", "type": "zip", "shasum": "", "reference": "2035d7a14b3071aec49b2cd5fc2a239935921390"}, "time": "2023-08-23T12:39:30+00:00"}, {"version": "v3.0.30", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "99f29cdc67496ee3fbaebec7b7e1290883398c18"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/99f29cdc67496ee3fbaebec7b7e1290883398c18", "type": "zip", "shasum": "", "reference": "99f29cdc67496ee3fbaebec7b7e1290883398c18"}, "time": "2023-08-21T09:36:32+00:00"}, {"version": "v3.0.29", "version_normalized": "********"}, {"version": "v3.0.28", "version_normalized": "********"}, {"version": "v3.0.27", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "6bc7669380dcfdf4eb519cbe6e641c97c34c6db6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/6bc7669380dcfdf4eb519cbe6e641c97c34c6db6", "type": "zip", "shasum": "", "reference": "6bc7669380dcfdf4eb519cbe6e641c97c34c6db6"}, "time": "2023-08-17T12:59:40+00:00"}, {"version": "v3.0.26", "version_normalized": "********"}, {"version": "v3.0.25", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ad3b0837bfcb6d17b113d2253841a42798b672bc"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ad3b0837bfcb6d17b113d2253841a42798b672bc", "type": "zip", "shasum": "", "reference": "ad3b0837bfcb6d17b113d2253841a42798b672bc"}, "time": "2023-08-17T08:51:00+00:00"}, {"version": "v3.0.24", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "6a9d9a8df0345a307d7adc0d4ce1f131ab4f6bee"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/6a9d9a8df0345a307d7adc0d4ce1f131ab4f6bee", "type": "zip", "shasum": "", "reference": "6a9d9a8df0345a307d7adc0d4ce1f131ab4f6bee"}, "time": "2023-08-14T14:23:49+00:00"}, {"version": "v3.0.23", "version_normalized": "********"}, {"version": "v3.0.22", "version_normalized": "********"}, {"version": "v3.0.21", "version_normalized": "********"}, {"version": "v3.0.20", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d25c59a0bc01c450418f20f8ff3551e0ae0e6b8d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d25c59a0bc01c450418f20f8ff3551e0ae0e6b8d", "type": "zip", "shasum": "", "reference": "d25c59a0bc01c450418f20f8ff3551e0ae0e6b8d"}, "time": "2023-08-10T10:44:16+00:00"}, {"version": "v3.0.19", "version_normalized": "********"}, {"version": "v3.0.18", "version_normalized": "********"}, {"version": "v3.0.17", "version_normalized": "********"}, {"version": "v3.0.16", "version_normalized": "********"}, {"version": "v3.0.15", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "09755a0505d9118ffecc4ab509494d35403c4809"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/09755a0505d9118ffecc4ab509494d35403c4809", "type": "zip", "shasum": "", "reference": "09755a0505d9118ffecc4ab509494d35403c4809"}, "time": "2023-08-06T22:54:20+00:00"}, {"version": "v3.0.14", "version_normalized": "********"}, {"version": "v3.0.13", "version_normalized": "********"}, {"version": "v3.0.12", "version_normalized": "********"}, {"version": "v3.0.11", "version_normalized": "********"}, {"version": "v3.0.10", "version_normalized": "********"}, {"version": "v3.0.9", "version_normalized": "*******"}, {"version": "v3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f34509af1eaffcc92d9a1a00a98b965b13b8faeb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f34509af1eaffcc92d9a1a00a98b965b13b8faeb", "type": "zip", "shasum": "", "reference": "f34509af1eaffcc92d9a1a00a98b965b13b8faeb"}, "time": "2023-08-03T09:29:45+00:00", "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^9.0|^10.0", "illuminate/filesystem": "^9.0|^10.0", "illuminate/notifications": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.0.7", "version_normalized": "*******"}, {"version": "v3.0.6", "version_normalized": "*******"}, {"version": "v3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "70f836c0e7917709f3651111a2274e08e866ee93"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/70f836c0e7917709f3651111a2274e08e866ee93", "type": "zip", "shasum": "", "reference": "70f836c0e7917709f3651111a2274e08e866ee93"}, "time": "2023-08-02T12:50:45+00:00"}, {"version": "v3.0.4", "version_normalized": "*******"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "df7957ec1ee92d238bc0e306b9acb3d879598c93"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/df7957ec1ee92d238bc0e306b9acb3d879598c93", "type": "zip", "shasum": "", "reference": "df7957ec1ee92d238bc0e306b9acb3d879598c93"}, "time": "2023-08-02T09:39:52+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "01657acf248c2d3e61d46bfd566ce51e8bc29158"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/01657acf248c2d3e61d46bfd566ce51e8bc29158", "type": "zip", "shasum": "", "reference": "01657acf248c2d3e61d46bfd566ce51e8bc29158"}, "time": "2023-08-01T22:15:53+00:00"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e60e5e688ceb65cf8bc7263ef5ad7e09be8b549c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e60e5e688ceb65cf8bc7263ef5ad7e09be8b549c", "type": "zip", "shasum": "", "reference": "e60e5e688ceb65cf8bc7263ef5ad7e09be8b549c"}, "time": "2023-08-01T22:10:15+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "1bdffb881ec8eedecf004e5b51032c256080d4c3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/1bdffb881ec8eedecf004e5b51032c256080d4c3", "type": "zip", "shasum": "", "reference": "1bdffb881ec8eedecf004e5b51032c256080d4c3"}, "time": "2023-08-01T12:40:52+00:00"}, {"version": "v3.0.0-beta28", "version_normalized": "*******-beta28"}, {"version": "v3.0.0-beta27", "version_normalized": "*******-beta27", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d42d6d5c0ecd2b2329839cc4e2263d445c6a65cf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d42d6d5c0ecd2b2329839cc4e2263d445c6a65cf", "type": "zip", "shasum": "", "reference": "d42d6d5c0ecd2b2329839cc4e2263d445c6a65cf"}, "time": "2023-08-01T07:38:12+00:00"}, {"version": "v3.0.0-beta26", "version_normalized": "*******-beta26", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b257847d26d8e8fdb5534f8ca0dd70179d350d05"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b257847d26d8e8fdb5534f8ca0dd70179d350d05", "type": "zip", "shasum": "", "reference": "b257847d26d8e8fdb5534f8ca0dd70179d350d05"}, "time": "2023-07-31T20:12:06+00:00"}, {"version": "v3.0.0-beta25", "version_normalized": "*******-beta25"}, {"version": "v3.0.0-beta24", "version_normalized": "*******-beta24"}, {"version": "v3.0.0-beta23", "version_normalized": "*******-beta23"}, {"version": "v3.0.0-beta22", "version_normalized": "*******-beta22", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "6c9215af8c645d048329480449577a2c8a56c5c5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/6c9215af8c645d048329480449577a2c8a56c5c5", "type": "zip", "shasum": "", "reference": "6c9215af8c645d048329480449577a2c8a56c5c5"}, "time": "2023-07-31T18:20:19+00:00"}, {"version": "v3.0.0-beta21", "version_normalized": "*******-beta21", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "acb0d420b2496d2b142e6665bc72ce070b4877a6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/acb0d420b2496d2b142e6665bc72ce070b4877a6", "type": "zip", "shasum": "", "reference": "acb0d420b2496d2b142e6665bc72ce070b4877a6"}, "time": "2023-07-29T10:44:47+00:00"}, {"version": "v3.0.0-beta20", "version_normalized": "*******-beta20"}, {"version": "v3.0.0-beta19", "version_normalized": "*******-beta19"}, {"version": "v3.0.0-beta18", "version_normalized": "*******-beta18", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "fd61662769a289e32281ca8bc972afc46632da97"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/fd61662769a289e32281ca8bc972afc46632da97", "type": "zip", "shasum": "", "reference": "fd61662769a289e32281ca8bc972afc46632da97"}, "time": "2023-07-29T09:13:23+00:00"}, {"version": "v3.0.0-beta17", "version_normalized": "*******-beta17", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "fdd4ffc3e4e2f8811a0260866d0fbd5ee075f675"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/fdd4ffc3e4e2f8811a0260866d0fbd5ee075f675", "type": "zip", "shasum": "", "reference": "fdd4ffc3e4e2f8811a0260866d0fbd5ee075f675"}, "time": "2023-07-26T18:32:33+00:00"}, {"version": "v3.0.0-beta16", "version_normalized": "*******-beta16"}, {"version": "v3.0.0-beta15", "version_normalized": "*******-beta15"}, {"version": "v3.0.0-beta14", "version_normalized": "*******-beta14"}, {"version": "v3.0.0-beta13", "version_normalized": "*******-beta13"}, {"version": "v3.0.0-beta12", "version_normalized": "*******-beta12"}, {"version": "v3.0.0-beta11", "version_normalized": "*******-beta11", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f83b4250f9abf8bbe085d3a8cf9e98b831a863ad"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f83b4250f9abf8bbe085d3a8cf9e98b831a863ad", "type": "zip", "shasum": "", "reference": "f83b4250f9abf8bbe085d3a8cf9e98b831a863ad"}, "time": "2023-07-25T18:02:08+00:00"}, {"version": "v3.0.0-beta10", "version_normalized": "*******-beta10"}, {"version": "v3.0.0-beta9", "version_normalized": "*******-beta9", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ddcbbaa676ae188e03dd8ff81fd751a8c34d759d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ddcbbaa676ae188e03dd8ff81fd751a8c34d759d", "type": "zip", "shasum": "", "reference": "ddcbbaa676ae188e03dd8ff81fd751a8c34d759d"}, "time": "2023-07-23T18:57:56+00:00"}, {"version": "v3.0.0-beta8", "version_normalized": "*******-beta8"}, {"version": "v3.0.0-beta7", "version_normalized": "*******-beta7"}, {"version": "v3.0.0-beta6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "86b31d4378fd761008f273cb90684dd59b622686"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/86b31d4378fd761008f273cb90684dd59b622686", "type": "zip", "shasum": "", "reference": "86b31d4378fd761008f273cb90684dd59b622686"}, "time": "2023-07-23T12:47:02+00:00"}, {"version": "v3.0.0-beta5", "version_normalized": "*******-beta5"}, {"version": "v3.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0f44ca6c313f1bd78007e0653f704dfb983a8d56"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0f44ca6c313f1bd78007e0653f704dfb983a8d56", "type": "zip", "shasum": "", "reference": "0f44ca6c313f1bd78007e0653f704dfb983a8d56"}, "time": "2023-07-21T16:13:06+00:00"}, {"version": "v3.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5dd76b4f4d65214e3acbe12db4d03f2b1b9b2a47"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5dd76b4f4d65214e3acbe12db4d03f2b1b9b2a47", "type": "zip", "shasum": "", "reference": "5dd76b4f4d65214e3acbe12db4d03f2b1b9b2a47"}, "time": "2023-07-20T15:46:56+00:00"}, {"version": "v3.0.0-beta2", "version_normalized": "*******-beta2"}, {"version": "v3.0.0-beta1", "version_normalized": "*******-beta1"}, {"description": "Effortlessly build TALL-powered notifications.", "version": "v3.0.0-alpha137", "version_normalized": "*******-alpha137", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9c34489f6002bea88b7879d825074aebace7c9b8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9c34489f6002bea88b7879d825074aebace7c9b8", "type": "zip", "shasum": "", "reference": "9c34489f6002bea88b7879d825074aebace7c9b8"}, "time": "2023-10-03T11:02:09+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^9.0|^10.0", "illuminate/filesystem": "^9.0|^10.0", "illuminate/notifications": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.0.0-alpha136", "version_normalized": "*******-alpha136"}, {"version": "v3.0.0-alpha135", "version_normalized": "*******-alpha135"}, {"version": "v3.0.0-alpha132", "version_normalized": "*******-alpha132", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "079ff4b92bdefa9102fa01ffa99866414cf8db8b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/079ff4b92bdefa9102fa01ffa99866414cf8db8b", "type": "zip", "shasum": "", "reference": "079ff4b92bdefa9102fa01ffa99866414cf8db8b"}, "time": "2023-08-21T07:29:26+00:00"}, {"version": "v3.0.0-alpha131", "version_normalized": "*******-alpha131", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d953c6a63810b0b0a51840b49c08f294378d7ad8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d953c6a63810b0b0a51840b49c08f294378d7ad8", "type": "zip", "shasum": "", "reference": "d953c6a63810b0b0a51840b49c08f294378d7ad8"}, "time": "2023-08-17T07:39:16+00:00"}, {"version": "v3.0.0-alpha130", "version_normalized": "*******-alpha130", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "64a7bc44be6781eb49b3e86a5b6d7abc618effbf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/64a7bc44be6781eb49b3e86a5b6d7abc618effbf", "type": "zip", "shasum": "", "reference": "64a7bc44be6781eb49b3e86a5b6d7abc618effbf"}, "time": "2023-08-14T07:56:24+00:00"}, {"version": "v3.0.0-alpha129", "version_normalized": "*******-alpha129", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "015ae8a5c76d9ee05dfb38d0e27a10779302c13b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/015ae8a5c76d9ee05dfb38d0e27a10779302c13b", "type": "zip", "shasum": "", "reference": "015ae8a5c76d9ee05dfb38d0e27a10779302c13b"}, "time": "2023-07-03T09:28:06+00:00"}, {"version": "v3.0.0-alpha128", "version_normalized": "*******-alpha128"}, {"version": "v3.0.0-alpha127", "version_normalized": "*******-alpha127"}, {"version": "v3.0.0-alpha126", "version_normalized": "*******-alpha126"}, {"version": "v3.0.0-alpha125", "version_normalized": "*******-alpha125"}, {"version": "v3.0.0-alpha124", "version_normalized": "*******-alpha124"}, {"version": "v3.0.0-alpha123", "version_normalized": "*******-alpha123"}, {"version": "v3.0.0-alpha122", "version_normalized": "*******-alpha122"}, {"version": "v3.0.0-alpha121", "version_normalized": "*******-alpha121"}, {"version": "v3.0.0-alpha120", "version_normalized": "*******-alpha120", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "64a020227cae8935e6a106fcbadfa51a758d55c8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/64a020227cae8935e6a106fcbadfa51a758d55c8", "type": "zip", "shasum": "", "reference": "64a020227cae8935e6a106fcbadfa51a758d55c8"}, "time": "2023-06-29T14:08:35+00:00"}, {"version": "v3.0.0-alpha119", "version_normalized": "*******-alpha119"}, {"version": "v3.0.0-alpha118", "version_normalized": "*******-alpha118", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f701753f2e6bc270c4bbe462e12150b127c651db"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f701753f2e6bc270c4bbe462e12150b127c651db", "type": "zip", "shasum": "", "reference": "f701753f2e6bc270c4bbe462e12150b127c651db"}, "time": "2023-06-26T15:19:12+00:00"}, {"version": "v3.0.0-alpha117", "version_normalized": "*******-alpha117"}, {"version": "v3.0.0-alpha116", "version_normalized": "*******-alpha116", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "cfe22b8bc3ccebc6a09ad35316c0e0af03ac61fb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/cfe22b8bc3ccebc6a09ad35316c0e0af03ac61fb", "type": "zip", "shasum": "", "reference": "cfe22b8bc3ccebc6a09ad35316c0e0af03ac61fb"}, "time": "2023-06-23T08:16:15+00:00"}, {"version": "v3.0.0-alpha115", "version_normalized": "*******-alpha115", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "3ed5a56df8538944029c7a29c956d08196e60687"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/3ed5a56df8538944029c7a29c956d08196e60687", "type": "zip", "shasum": "", "reference": "3ed5a56df8538944029c7a29c956d08196e60687"}, "time": "2023-06-22T21:54:46+00:00"}, {"version": "v3.0.0-alpha114", "version_normalized": "*******-alpha114", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "099cd68d2910c7b08c7989055e36bc760bfbaa1d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/099cd68d2910c7b08c7989055e36bc760bfbaa1d", "type": "zip", "shasum": "", "reference": "099cd68d2910c7b08c7989055e36bc760bfbaa1d"}, "time": "2023-06-21T09:45:22+00:00"}, {"version": "v3.0.0-alpha113", "version_normalized": "*******-alpha113"}, {"version": "v3.0.0-alpha112", "version_normalized": "*******-alpha112"}, {"version": "v3.0.0-alpha111", "version_normalized": "*******-alpha111", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "1afccd8a2680ac294c5637eb7979b9a1ad792541"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/1afccd8a2680ac294c5637eb7979b9a1ad792541", "type": "zip", "shasum": "", "reference": "1afccd8a2680ac294c5637eb7979b9a1ad792541"}, "time": "2023-06-19T10:53:37+00:00"}, {"version": "v3.0.0-alpha110", "version_normalized": "*******-alpha110", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "af6da81a91455437d9d47951a8a30300b8995164"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/af6da81a91455437d9d47951a8a30300b8995164", "type": "zip", "shasum": "", "reference": "af6da81a91455437d9d47951a8a30300b8995164"}, "time": "2023-06-14T08:19:41+00:00"}, {"version": "v3.0.0-alpha109", "version_normalized": "*******-alpha109", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "8a4b41915b6de862f271255e4a4d572cbed05f2c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/8a4b41915b6de862f271255e4a4d572cbed05f2c", "type": "zip", "shasum": "", "reference": "8a4b41915b6de862f271255e4a4d572cbed05f2c"}, "time": "2023-06-11T17:08:34+00:00"}, {"version": "v3.0.0-alpha108", "version_normalized": "*******-alpha108", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f84a7fb211151c39623740096bd2de8fa31a034c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f84a7fb211151c39623740096bd2de8fa31a034c", "type": "zip", "shasum": "", "reference": "f84a7fb211151c39623740096bd2de8fa31a034c"}, "time": "2023-06-02T10:07:39+00:00"}, {"version": "v3.0.0-alpha107", "version_normalized": "*******-alpha107", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d133e3f0379fde6340573ed4afd5ac50e4558cf7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d133e3f0379fde6340573ed4afd5ac50e4558cf7", "type": "zip", "shasum": "", "reference": "d133e3f0379fde6340573ed4afd5ac50e4558cf7"}, "time": "2023-05-30T17:28:14+00:00"}, {"version": "v3.0.0-alpha106", "version_normalized": "*******-alpha106"}, {"version": "v3.0.0-alpha105", "version_normalized": "*******-alpha105"}, {"version": "v3.0.0-alpha104", "version_normalized": "*******-alpha104", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "2ad5ad2982ba6a3f6577c14f54238c9905279449"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/2ad5ad2982ba6a3f6577c14f54238c9905279449", "type": "zip", "shasum": "", "reference": "2ad5ad2982ba6a3f6577c14f54238c9905279449"}, "time": "2023-05-20T17:47:05+00:00"}, {"version": "v3.0.0-alpha103", "version_normalized": "*******-alpha103"}, {"version": "v3.0.0-alpha102", "version_normalized": "*******-alpha102"}, {"version": "v3.0.0-alpha101", "version_normalized": "*******-alpha101"}, {"version": "v3.0.0-alpha100", "version_normalized": "*******-alpha100", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "70c6a43e31246bc702934dcd757a0e0f84331faa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/70c6a43e31246bc702934dcd757a0e0f84331faa", "type": "zip", "shasum": "", "reference": "70c6a43e31246bc702934dcd757a0e0f84331faa"}, "time": "2023-05-15T13:02:32+00:00"}, {"version": "v3.0.0-alpha99", "version_normalized": "*******-alpha99"}, {"version": "v3.0.0-alpha98", "version_normalized": "*******-alpha98"}, {"version": "v3.0.0-alpha97", "version_normalized": "*******-alpha97"}, {"version": "v3.0.0-alpha96", "version_normalized": "*******-alpha96"}, {"version": "v3.0.0-alpha95", "version_normalized": "*******-alpha95"}, {"version": "v3.0.0-alpha94", "version_normalized": "*******-alpha94", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "c0f051f33b0a7bbe849a08a35a4de6f689bb44a7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/c0f051f33b0a7bbe849a08a35a4de6f689bb44a7", "type": "zip", "shasum": "", "reference": "c0f051f33b0a7bbe849a08a35a4de6f689bb44a7"}, "time": "2023-05-13T07:22:51+00:00"}, {"version": "v3.0.0-alpha93", "version_normalized": "*******-alpha93"}, {"version": "v3.0.0-alpha92", "version_normalized": "*******-alpha92"}, {"version": "v3.0.0-alpha91", "version_normalized": "*******-alpha91", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "7f26a8a1fa6afba6cd2744ffccb80344841fcd44"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/7f26a8a1fa6afba6cd2744ffccb80344841fcd44", "type": "zip", "shasum": "", "reference": "7f26a8a1fa6afba6cd2744ffccb80344841fcd44"}, "time": "2023-05-10T20:21:43+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-heroicons": "^2.0", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^9.0|^10.0", "illuminate/filesystem": "^9.0|^10.0", "illuminate/notifications": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.0.0-alpha90", "version_normalized": "*******-alpha90", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "1f24009001d48bbe0a586e67632ea02faed7d97b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/1f24009001d48bbe0a586e67632ea02faed7d97b", "type": "zip", "shasum": "", "reference": "1f24009001d48bbe0a586e67632ea02faed7d97b"}, "time": "2023-05-10T11:39:18+00:00"}, {"version": "v3.0.0-alpha89", "version_normalized": "*******-alpha89", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a45fb32a136a76c987199e4652a2129d7da24f7f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a45fb32a136a76c987199e4652a2129d7da24f7f", "type": "zip", "shasum": "", "reference": "a45fb32a136a76c987199e4652a2129d7da24f7f"}, "time": "2023-05-09T20:02:57+00:00"}, {"version": "v3.0.0-alpha88", "version_normalized": "*******-alpha88", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "bc75d6b904cd1e1e1312139dd2282d94ffa77d2b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/bc75d6b904cd1e1e1312139dd2282d94ffa77d2b", "type": "zip", "shasum": "", "reference": "bc75d6b904cd1e1e1312139dd2282d94ffa77d2b"}, "time": "2023-05-04T23:10:53+00:00"}, {"version": "v3.0.0-alpha87", "version_normalized": "*******-alpha87"}, {"version": "v3.0.0-alpha86", "version_normalized": "*******-alpha86"}, {"version": "v3.0.0-alpha85", "version_normalized": "*******-alpha85"}, {"version": "v3.0.0-alpha84", "version_normalized": "*******-alpha84"}, {"version": "v3.0.0-alpha83", "version_normalized": "*******-alpha83"}, {"version": "v3.0.0-alpha82", "version_normalized": "*******-alpha82"}, {"version": "v3.0.0-alpha81", "version_normalized": "*******-alpha81", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "8edba6bcf9a1cb0d12a33c7e34d0e9a5dd262bb5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/8edba6bcf9a1cb0d12a33c7e34d0e9a5dd262bb5", "type": "zip", "shasum": "", "reference": "8edba6bcf9a1cb0d12a33c7e34d0e9a5dd262bb5"}, "time": "2023-05-02T21:50:52+00:00"}, {"version": "v3.0.0-alpha80", "version_normalized": "*******-alpha80"}, {"version": "v3.0.0-alpha79", "version_normalized": "*******-alpha79"}, {"version": "v3.0.0-alpha78", "version_normalized": "*******-alpha78", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e87043fea9c80c20a914c25985db1ecf3fb9f464"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e87043fea9c80c20a914c25985db1ecf3fb9f464", "type": "zip", "shasum": "", "reference": "e87043fea9c80c20a914c25985db1ecf3fb9f464"}, "time": "2023-04-30T18:15:18+00:00"}, {"version": "v3.0.0-alpha77", "version_normalized": "*******-alpha77", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "269bec657d6b14b431c9f1aff7d8fbec0f2a1d5b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/269bec657d6b14b431c9f1aff7d8fbec0f2a1d5b", "type": "zip", "shasum": "", "reference": "269bec657d6b14b431c9f1aff7d8fbec0f2a1d5b"}, "time": "2023-04-24T08:35:16+00:00"}, {"version": "v3.0.0-alpha76", "version_normalized": "*******-alpha76"}, {"version": "v3.0.0-alpha75", "version_normalized": "*******-alpha75", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "550d1365e7b980627cbf0e6b6b73f9e1379b0834"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/550d1365e7b980627cbf0e6b6b73f9e1379b0834", "type": "zip", "shasum": "", "reference": "550d1365e7b980627cbf0e6b6b73f9e1379b0834"}, "time": "2023-04-16T09:05:53+00:00"}, {"version": "v3.0.0-alpha74", "version_normalized": "*******-alpha74", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "43df1e644c49b096f0b574b0ed0e31f5a634a8bb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/43df1e644c49b096f0b574b0ed0e31f5a634a8bb", "type": "zip", "shasum": "", "reference": "43df1e644c49b096f0b574b0ed0e31f5a634a8bb"}, "time": "2023-04-11T10:31:25+00:00"}, {"version": "v3.0.0-alpha73", "version_normalized": "*******-alpha73"}, {"version": "v3.0.0-alpha72", "version_normalized": "*******-alpha72", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "372347bb4a068096b37bbabca8d8b532460b7d33"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/372347bb4a068096b37bbabca8d8b532460b7d33", "type": "zip", "shasum": "", "reference": "372347bb4a068096b37bbabca8d8b532460b7d33"}, "time": "2023-04-11T10:06:05+00:00"}, {"version": "v3.0.0-alpha71", "version_normalized": "*******-alpha71", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9801b4f7506b0e1eada7a504c736c73a9dc611ed"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9801b4f7506b0e1eada7a504c736c73a9dc611ed", "type": "zip", "shasum": "", "reference": "9801b4f7506b0e1eada7a504c736c73a9dc611ed"}, "time": "2023-03-30T21:42:43+00:00"}, {"version": "v3.0.0-alpha70", "version_normalized": "*******-alpha70"}, {"version": "v3.0.0-alpha69", "version_normalized": "*******-alpha69", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "43e6a3d079dd3317625e5ef0d6ec9d545f9b4341"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/43e6a3d079dd3317625e5ef0d6ec9d545f9b4341", "type": "zip", "shasum": "", "reference": "43e6a3d079dd3317625e5ef0d6ec9d545f9b4341"}, "time": "2023-03-25T17:36:30+00:00"}, {"version": "v3.0.0-alpha68", "version_normalized": "*******-alpha68"}, {"version": "v3.0.0-alpha67", "version_normalized": "*******-alpha67", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "652f8902d7fe7bd8e11c8a6c18e4972e33780170"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/652f8902d7fe7bd8e11c8a6c18e4972e33780170", "type": "zip", "shasum": "", "reference": "652f8902d7fe7bd8e11c8a6c18e4972e33780170"}, "time": "2023-03-13T14:41:38+00:00"}, {"version": "v3.0.0-alpha66", "version_normalized": "*******-alpha66"}, {"version": "v3.0.0-alpha65", "version_normalized": "*******-alpha65"}, {"version": "v3.0.0-alpha64", "version_normalized": "*******-alpha64"}, {"version": "v3.0.0-alpha63", "version_normalized": "*******-alpha63"}, {"version": "v3.0.0-alpha62", "version_normalized": "*******-alpha62", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "edbc3f692970de88ab1d83a5308fd4a85686fbd7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/edbc3f692970de88ab1d83a5308fd4a85686fbd7", "type": "zip", "shasum": "", "reference": "edbc3f692970de88ab1d83a5308fd4a85686fbd7"}, "time": "2023-03-12T12:27:47+00:00"}, {"version": "v3.0.0-alpha61", "version_normalized": "*******-alpha61", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "c92afa3fef99af2cfa1d911247b011480436d503"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/c92afa3fef99af2cfa1d911247b011480436d503", "type": "zip", "shasum": "", "reference": "c92afa3fef99af2cfa1d911247b011480436d503"}, "time": "2023-03-04T20:47:02+00:00"}, {"version": "v3.0.0-alpha60", "version_normalized": "*******-alpha60"}, {"version": "v3.0.0-alpha59", "version_normalized": "*******-alpha59"}, {"version": "v3.0.0-alpha58", "version_normalized": "*******-alpha58"}, {"version": "v3.0.0-alpha57", "version_normalized": "*******-alpha57"}, {"version": "v3.0.0-alpha56", "version_normalized": "*******-alpha56"}, {"version": "v3.0.0-alpha55", "version_normalized": "*******-alpha55", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "fe63f745f625c8c072621047f03b7c1a88e3b098"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/fe63f745f625c8c072621047f03b7c1a88e3b098", "type": "zip", "shasum": "", "reference": "fe63f745f625c8c072621047f03b7c1a88e3b098"}, "time": "2023-03-01T22:54:56+00:00"}, {"version": "v3.0.0-alpha54", "version_normalized": "*******-alpha54"}, {"version": "v3.0.0-alpha53", "version_normalized": "*******-alpha53"}, {"version": "v3.0.0-alpha52", "version_normalized": "*******-alpha52"}, {"version": "v3.0.0-alpha51", "version_normalized": "*******-alpha51"}, {"version": "v3.0.0-alpha50", "version_normalized": "*******-alpha50"}, {"version": "v3.0.0-alpha49", "version_normalized": "*******-alpha49"}, {"version": "v3.0.0-alpha48", "version_normalized": "*******-alpha48"}, {"version": "v3.0.0-alpha47", "version_normalized": "*******-alpha47", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a7cd12f6682cfee35a977ecbe0ed3fdcdd857d16"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a7cd12f6682cfee35a977ecbe0ed3fdcdd857d16", "type": "zip", "shasum": "", "reference": "a7cd12f6682cfee35a977ecbe0ed3fdcdd857d16"}, "time": "2023-02-27T13:06:07+00:00"}, {"version": "v3.0.0-alpha46", "version_normalized": "*******-alpha46", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "b6fd9d44db99ce9f95d58625aef83f7d6f513aab"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/b6fd9d44db99ce9f95d58625aef83f7d6f513aab", "type": "zip", "shasum": "", "reference": "b6fd9d44db99ce9f95d58625aef83f7d6f513aab"}, "time": "2023-02-24T11:28:45+00:00"}, {"version": "v3.0.0-alpha45", "version_normalized": "*******-alpha45", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f456cd979e73b85004c3f8bcde5aa75e1e69e8a8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f456cd979e73b85004c3f8bcde5aa75e1e69e8a8", "type": "zip", "shasum": "", "reference": "f456cd979e73b85004c3f8bcde5aa75e1e69e8a8"}, "time": "2023-02-23T16:41:16+00:00"}, {"version": "v3.0.0-alpha44", "version_normalized": "*******-alpha44", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d2c76914b0dba2c53400739688dd896b48387cce"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d2c76914b0dba2c53400739688dd896b48387cce", "type": "zip", "shasum": "", "reference": "d2c76914b0dba2c53400739688dd896b48387cce"}, "time": "2023-02-20T23:07:46+00:00"}, {"version": "v3.0.0-alpha43", "version_normalized": "*******-alpha43", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "da0281d1552dbf27a52286acc1467b9450884a64"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/da0281d1552dbf27a52286acc1467b9450884a64", "type": "zip", "shasum": "", "reference": "da0281d1552dbf27a52286acc1467b9450884a64"}, "time": "2023-02-18T19:00:23+00:00"}, {"version": "v3.0.0-alpha42", "version_normalized": "*******-alpha42"}, {"version": "v3.0.0-alpha41", "version_normalized": "*******-alpha41"}, {"version": "v3.0.0-alpha40", "version_normalized": "*******-alpha40"}, {"version": "v3.0.0-alpha39", "version_normalized": "*******-alpha39", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9b1985cd8cba1630f64f3cdde8202f1c30683a23"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9b1985cd8cba1630f64f3cdde8202f1c30683a23", "type": "zip", "shasum": "", "reference": "9b1985cd8cba1630f64f3cdde8202f1c30683a23"}, "time": "2023-02-16T08:47:28+00:00"}, {"version": "v3.0.0-alpha38", "version_normalized": "*******-alpha38"}, {"version": "v3.0.0-alpha37", "version_normalized": "*******-alpha37"}, {"version": "v3.0.0-alpha36", "version_normalized": "*******-alpha36", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e8082d03552452b6aa75b94478c6143decded8d1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e8082d03552452b6aa75b94478c6143decded8d1", "type": "zip", "shasum": "", "reference": "e8082d03552452b6aa75b94478c6143decded8d1"}, "time": "2023-02-08T23:25:37+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-heroicons": "^2.0", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^9.0", "illuminate/filesystem": "^9.0", "illuminate/notifications": "^9.0", "illuminate/support": "^9.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v3.0.0-alpha35", "version_normalized": "*******-alpha35"}, {"version": "v3.0.0-alpha34", "version_normalized": "*******-alpha34", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "dcb56a7c94f5e3c6a813b01fb55264e4be03c54c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/dcb56a7c94f5e3c6a813b01fb55264e4be03c54c", "type": "zip", "shasum": "", "reference": "dcb56a7c94f5e3c6a813b01fb55264e4be03c54c"}, "time": "2023-01-31T20:23:11+00:00"}, {"version": "v3.0.0-alpha33", "version_normalized": "*******-alpha33"}, {"version": "v3.0.0-alpha32", "version_normalized": "*******-alpha32"}, {"version": "v3.0.0-alpha31", "version_normalized": "*******-alpha31"}, {"version": "v3.0.0-alpha30", "version_normalized": "*******-alpha30"}, {"version": "v3.0.0-alpha29", "version_normalized": "*******-alpha29"}, {"version": "v3.0.0-alpha28", "version_normalized": "*******-alpha28"}, {"version": "v3.0.0-alpha27", "version_normalized": "*******-alpha27", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "058f45449c6e56f3c278cf760551cd655b1e0228"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/058f45449c6e56f3c278cf760551cd655b1e0228", "type": "zip", "shasum": "", "reference": "058f45449c6e56f3c278cf760551cd655b1e0228"}, "time": "2023-01-31T14:23:35+00:00"}, {"version": "v3.0.0-alpha26", "version_normalized": "*******-alpha26"}, {"version": "v3.0.0-alpha25", "version_normalized": "*******-alpha25"}, {"version": "v3.0.0-alpha24", "version_normalized": "*******-alpha24", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "6c2e1bf9e48b8c85ff09eee1b45d10bc5a90a8a3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/6c2e1bf9e48b8c85ff09eee1b45d10bc5a90a8a3", "type": "zip", "shasum": "", "reference": "6c2e1bf9e48b8c85ff09eee1b45d10bc5a90a8a3"}, "time": "2023-01-29T11:30:46+00:00"}, {"version": "v3.0.0-alpha23", "version_normalized": "*******-alpha23"}, {"version": "v3.0.0-alpha10", "version_normalized": "*******-alpha10"}, {"version": "v3.0.0-alpha9", "version_normalized": "*******-alpha9"}, {"version": "v3.0.0-alpha8", "version_normalized": "*******-alpha8", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "26c677534ecad0c9b07d7d87ad83ca8acc436ac5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/26c677534ecad0c9b07d7d87ad83ca8acc436ac5", "type": "zip", "shasum": "", "reference": "26c677534ecad0c9b07d7d87ad83ca8acc436ac5"}, "time": "2023-01-27T15:12:42+00:00"}, {"version": "v3.0.0-alpha7", "version_normalized": "*******-alpha7"}, {"version": "v3.0.0-alpha6", "version_normalized": "*******-alpha6", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "26d55934f56940b6b08ad26b408ff9ba2b615791"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/26d55934f56940b6b08ad26b408ff9ba2b615791", "type": "zip", "shasum": "", "reference": "26d55934f56940b6b08ad26b408ff9ba2b615791"}, "time": "2023-01-22T12:50:58+00:00"}, {"version": "v3.0.0-alpha5", "version_normalized": "*******-alpha5"}, {"version": "v3.0.0-alpha4", "version_normalized": "*******-alpha4"}, {"version": "v3.0.0-alpha3", "version_normalized": "*******-alpha3"}, {"version": "v3.0.0-alpha2", "version_normalized": "*******-alpha2"}, {"version": "v3.0.0-alpha1", "version_normalized": "*******-alpha1"}, {"version": "v2.17.58", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f972041f9a5d8aac35ec0e7c193a96f2a44b1032"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f972041f9a5d8aac35ec0e7c193a96f2a44b1032", "type": "zip", "shasum": "", "reference": "f972041f9a5d8aac35ec0e7c193a96f2a44b1032"}, "time": "2025-03-25T10:29:13+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-heroicons": "^1.2", "filament/support": "self.version", "illuminate/contracts": "^8.6|^9.0|^10.0", "illuminate/filesystem": "^8.6|^9.0|^10.0", "illuminate/notifications": "^8.6|^9.0|^10.0", "illuminate/support": "^8.6|^9.0|^10.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v2.17.57", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "092b09455bb8360b0d3c7b57691df024a5768812"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/092b09455bb8360b0d3c7b57691df024a5768812", "type": "zip", "shasum": "", "reference": "092b09455bb8360b0d3c7b57691df024a5768812"}, "time": "2025-03-21T08:32:03+00:00"}, {"version": "v2.17.56", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d28fd12dbb4602f24f94d88730128d28f0f565db"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d28fd12dbb4602f24f94d88730128d28f0f565db", "type": "zip", "shasum": "", "reference": "d28fd12dbb4602f24f94d88730128d28f0f565db"}, "time": "2023-07-03T09:23:01+00:00"}, {"version": "v2.17.55", "version_normalized": "*********"}, {"version": "v2.17.54", "version_normalized": "*********"}, {"version": "v2.17.53", "version_normalized": "*********"}, {"version": "v2.17.52", "version_normalized": "*********"}, {"version": "v2.17.51", "version_normalized": "*********"}, {"version": "v2.17.50", "version_normalized": "*********"}, {"version": "v2.17.49", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "619a730d88943c9415ae1c61085445c2a76199ce"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/619a730d88943c9415ae1c61085445c2a76199ce", "type": "zip", "shasum": "", "reference": "619a730d88943c9415ae1c61085445c2a76199ce"}, "time": "2023-06-12T14:24:44+00:00"}, {"version": "v2.17.48", "version_normalized": "*********"}, {"version": "v2.17.47", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "2a0a808bff7b6eb036aa7d652e2167c3b3d7bd29"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/2a0a808bff7b6eb036aa7d652e2167c3b3d7bd29", "type": "zip", "shasum": "", "reference": "2a0a808bff7b6eb036aa7d652e2167c3b3d7bd29"}, "time": "2023-06-06T11:21:19+00:00"}, {"version": "v2.17.46", "version_normalized": "*********"}, {"version": "v2.17.45", "version_normalized": "*********"}, {"version": "v2.17.44", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a9bee8c8b7cdf1df798311acdf57895d73dd3af7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a9bee8c8b7cdf1df798311acdf57895d73dd3af7", "type": "zip", "shasum": "", "reference": "a9bee8c8b7cdf1df798311acdf57895d73dd3af7"}, "time": "2023-05-30T10:44:51+00:00"}, {"version": "v2.17.43", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0ef9f1f3481a08f357b8e78bcb4bbcd8111a1252"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0ef9f1f3481a08f357b8e78bcb4bbcd8111a1252", "type": "zip", "shasum": "", "reference": "0ef9f1f3481a08f357b8e78bcb4bbcd8111a1252"}, "time": "2023-05-04T23:08:09+00:00"}, {"version": "v2.17.42", "version_normalized": "*********"}, {"version": "v2.17.41", "version_normalized": "*********"}, {"version": "v2.17.40", "version_normalized": "*********"}, {"version": "v2.17.39", "version_normalized": "*********"}, {"version": "v2.17.38", "version_normalized": "*********"}, {"version": "v2.17.37", "version_normalized": "*********"}, {"version": "v2.17.36", "version_normalized": "*********"}, {"version": "v2.17.35", "version_normalized": "*********"}, {"version": "v2.17.34", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "42c18bf35a634f17c61ee1f8694ec3ac440903f9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/42c18bf35a634f17c61ee1f8694ec3ac440903f9", "type": "zip", "shasum": "", "reference": "42c18bf35a634f17c61ee1f8694ec3ac440903f9"}, "time": "2023-04-21T08:32:52+00:00"}, {"version": "v2.17.32", "version_normalized": "*********"}, {"version": "v2.17.31", "version_normalized": "*********"}, {"version": "v2.17.30", "version_normalized": "*********"}, {"version": "v2.17.29", "version_normalized": "*********"}, {"version": "v2.17.28", "version_normalized": "*********"}, {"version": "v2.17.27", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "c47a7d9c6dcdf2486558e5b825d5f9ddf061be64"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/c47a7d9c6dcdf2486558e5b825d5f9ddf061be64", "type": "zip", "shasum": "", "reference": "c47a7d9c6dcdf2486558e5b825d5f9ddf061be64"}, "time": "2023-04-17T13:08:51+00:00"}, {"version": "v2.17.26", "version_normalized": "*********"}, {"version": "v2.17.25", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "eb55ddad40eafdbc097dca789907e69afa7f9a0d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/eb55ddad40eafdbc097dca789907e69afa7f9a0d", "type": "zip", "shasum": "", "reference": "eb55ddad40eafdbc097dca789907e69afa7f9a0d"}, "time": "2023-04-11T10:42:19+00:00"}, {"version": "v2.17.24", "version_normalized": "*********"}, {"version": "v2.17.23", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d8d467b61a3109e9f3ceac0db54c5fb071b95971"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d8d467b61a3109e9f3ceac0db54c5fb071b95971", "type": "zip", "shasum": "", "reference": "d8d467b61a3109e9f3ceac0db54c5fb071b95971"}, "time": "2023-03-30T17:39:12+00:00"}, {"version": "v2.17.22", "version_normalized": "*********"}, {"version": "v2.17.21", "version_normalized": "*********"}, {"version": "v2.17.20", "version_normalized": "*********"}, {"version": "v2.17.19", "version_normalized": "*********"}, {"version": "v2.17.18", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "39858f8341123c4e9be7db603249af5f3c7cbb90"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/39858f8341123c4e9be7db603249af5f3c7cbb90", "type": "zip", "shasum": "", "reference": "39858f8341123c4e9be7db603249af5f3c7cbb90"}, "time": "2023-03-25T17:22:22+00:00"}, {"version": "v2.17.17", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "6fc4a11302a83a451d4a28787e8ccca3f7e781a9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/6fc4a11302a83a451d4a28787e8ccca3f7e781a9", "type": "zip", "shasum": "", "reference": "6fc4a11302a83a451d4a28787e8ccca3f7e781a9"}, "time": "2023-03-12T12:20:28+00:00"}, {"version": "v2.17.16", "version_normalized": "*********"}, {"version": "v2.17.15", "version_normalized": "*********"}, {"version": "v2.17.14", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "67880bb30552f9ded48bcf293ee9f92976e04143"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/67880bb30552f9ded48bcf293ee9f92976e04143", "type": "zip", "shasum": "", "reference": "67880bb30552f9ded48bcf293ee9f92976e04143"}, "time": "2023-03-01T22:52:03+00:00"}, {"version": "v2.17.13", "version_normalized": "*********"}, {"version": "v2.17.12", "version_normalized": "*********"}, {"version": "v2.17.11", "version_normalized": "*********"}, {"version": "v2.17.10", "version_normalized": "*********"}, {"version": "v2.17.9", "version_normalized": "********"}, {"version": "v2.17.8", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "98a3434533e9b0d501b17774d4c1d2bae1b24913"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/98a3434533e9b0d501b17774d4c1d2bae1b24913", "type": "zip", "shasum": "", "reference": "98a3434533e9b0d501b17774d4c1d2bae1b24913"}, "time": "2023-02-23T21:55:22+00:00"}, {"version": "v2.17.7", "version_normalized": "********"}, {"version": "v2.17.6", "version_normalized": "********"}, {"version": "v2.17.5", "version_normalized": "********"}, {"version": "v2.17.4", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "bd10ae1c744ed5c8e988ed7e4668bf6975e2cea1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/bd10ae1c744ed5c8e988ed7e4668bf6975e2cea1", "type": "zip", "shasum": "", "reference": "bd10ae1c744ed5c8e988ed7e4668bf6975e2cea1"}, "time": "2023-02-20T22:54:33+00:00"}, {"version": "v2.17.3", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9d36c8aa6881a3451bacd39d88507978a959696c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9d36c8aa6881a3451bacd39d88507978a959696c", "type": "zip", "shasum": "", "reference": "9d36c8aa6881a3451bacd39d88507978a959696c"}, "time": "2023-02-14T20:59:43+00:00"}, {"version": "v2.17.2", "version_normalized": "********"}, {"version": "v2.17.1", "version_normalized": "********"}, {"version": "v2.17.0", "version_normalized": "********"}, {"version": "v2.16.70", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d8ffd5fed8c9f2c95a7e9ed663cbab5ad8618841"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d8ffd5fed8c9f2c95a7e9ed663cbab5ad8618841", "type": "zip", "shasum": "", "reference": "d8ffd5fed8c9f2c95a7e9ed663cbab5ad8618841"}, "time": "2023-02-09T00:05:43+00:00"}, {"version": "v2.16.69", "version_normalized": "*********"}, {"version": "v2.16.68", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ac289e6da65f9c30cfead2e905d86b8f69b81641"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ac289e6da65f9c30cfead2e905d86b8f69b81641", "type": "zip", "shasum": "", "reference": "ac289e6da65f9c30cfead2e905d86b8f69b81641"}, "time": "2023-02-01T14:43:31+00:00"}, {"version": "v2.16.67", "version_normalized": "*********"}, {"version": "v2.16.66", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "06eb5c14796d72ec3dbb0cc13be03a7801e4ab0f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/06eb5c14796d72ec3dbb0cc13be03a7801e4ab0f", "type": "zip", "shasum": "", "reference": "06eb5c14796d72ec3dbb0cc13be03a7801e4ab0f"}, "time": "2023-01-25T18:16:07+00:00"}, {"version": "v2.16.65", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "fe6247865c83809fe5bf1a87d0492445f6cef03e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/fe6247865c83809fe5bf1a87d0492445f6cef03e", "type": "zip", "shasum": "", "reference": "fe6247865c83809fe5bf1a87d0492445f6cef03e"}, "time": "2023-01-12T10:12:17+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-heroicons": "^1.2", "filament/support": "self.version", "illuminate/contracts": "^8.6|^9.0", "illuminate/filesystem": "^8.6|^9.0", "illuminate/notifications": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v2.16.64", "version_normalized": "*********"}, {"version": "v2.16.63", "version_normalized": "*********"}, {"version": "v2.16.62", "version_normalized": "*********"}, {"version": "v2.16.61", "version_normalized": "*********"}, {"version": "v2.16.60", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "91fc0d87202da378b422200ed9eb4cb0e7933453"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/91fc0d87202da378b422200ed9eb4cb0e7933453", "type": "zip", "shasum": "", "reference": "91fc0d87202da378b422200ed9eb4cb0e7933453"}, "time": "2022-12-07T12:06:28+00:00"}, {"version": "v2.16.59", "version_normalized": "*********"}, {"version": "v2.16.58", "version_normalized": "*********"}, {"version": "v2.16.57", "version_normalized": "*********"}, {"version": "v2.16.56", "version_normalized": "*********"}, {"version": "v2.16.55", "version_normalized": "*********"}, {"version": "v2.16.54", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ad4cc6e1140649c3c2d193b6b8d7fdd7d2a6d02e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ad4cc6e1140649c3c2d193b6b8d7fdd7d2a6d02e", "type": "zip", "shasum": "", "reference": "ad4cc6e1140649c3c2d193b6b8d7fdd7d2a6d02e"}, "time": "2022-11-22T10:28:58+00:00"}, {"version": "v2.16.53", "version_normalized": "*********"}, {"version": "v2.16.52", "version_normalized": "*********"}, {"version": "v2.16.51", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ea4c5b9386be5bfd1578db30a5e224ced22fc105"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ea4c5b9386be5bfd1578db30a5e224ced22fc105", "type": "zip", "shasum": "", "reference": "ea4c5b9386be5bfd1578db30a5e224ced22fc105"}, "time": "2022-11-17T10:14:22+00:00", "autoload": {"psr-4": {"Filament\\Notifications\\": "src"}}}, {"version": "v2.16.50", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "bf6804b1a5e77aeb78d0c52e77d326c3bc98d6f2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/bf6804b1a5e77aeb78d0c52e77d326c3bc98d6f2", "type": "zip", "shasum": "", "reference": "bf6804b1a5e77aeb78d0c52e77d326c3bc98d6f2"}, "time": "2022-11-15T19:30:08+00:00"}, {"version": "v2.16.49", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9155b644bdeb694c9d760c340a7110369a97757e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9155b644bdeb694c9d760c340a7110369a97757e", "type": "zip", "shasum": "", "reference": "9155b644bdeb694c9d760c340a7110369a97757e"}, "time": "2022-11-11T16:52:47+00:00"}, {"version": "v2.16.48", "version_normalized": "*********"}, {"version": "v2.16.47", "version_normalized": "*********"}, {"version": "v2.16.46", "version_normalized": "*********"}, {"version": "v2.16.45", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "45a17e50dbffe17852811c4181309e23deb24b97"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/45a17e50dbffe17852811c4181309e23deb24b97", "type": "zip", "shasum": "", "reference": "45a17e50dbffe17852811c4181309e23deb24b97"}, "time": "2022-11-09T16:25:58+00:00"}, {"version": "v2.16.44", "version_normalized": "*********"}, {"version": "v2.16.43", "version_normalized": "*********"}, {"version": "v2.16.42", "version_normalized": "*********"}, {"version": "v2.16.41", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ff9137bb1fa5568f923ae5afd35ec90c20459aa5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ff9137bb1fa5568f923ae5afd35ec90c20459aa5", "type": "zip", "shasum": "", "reference": "ff9137bb1fa5568f923ae5afd35ec90c20459aa5"}, "time": "2022-10-30T20:32:21+00:00"}, {"version": "v2.16.40", "version_normalized": "*********"}, {"version": "v2.16.39", "version_normalized": "*********"}, {"version": "v2.16.38", "version_normalized": "*********"}, {"version": "v2.16.37", "version_normalized": "*********"}, {"version": "v2.16.36", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0f83057a6bc95d031080c785235bbbd8405b0314"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0f83057a6bc95d031080c785235bbbd8405b0314", "type": "zip", "shasum": "", "reference": "0f83057a6bc95d031080c785235bbbd8405b0314"}, "time": "2022-10-22T11:52:00+00:00"}, {"version": "v2.16.35", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d4d9194eb9b0147d69354f9868ac7144cddf9cb3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d4d9194eb9b0147d69354f9868ac7144cddf9cb3", "type": "zip", "shasum": "", "reference": "d4d9194eb9b0147d69354f9868ac7144cddf9cb3"}, "time": "2022-10-17T08:43:26+00:00"}, {"version": "v2.16.34", "version_normalized": "*********"}, {"version": "v2.16.33", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "e2d2c75f5472ea1738eaeb05b62407123bae8abe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/e2d2c75f5472ea1738eaeb05b62407123bae8abe", "type": "zip", "shasum": "", "reference": "e2d2c75f5472ea1738eaeb05b62407123bae8abe"}, "time": "2022-10-13T14:39:43+00:00"}, {"version": "v2.16.32", "version_normalized": "*********"}, {"version": "v2.16.31", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "f3bcbfa24217c0739c5671b4621c1a43f2e736f9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/f3bcbfa24217c0739c5671b4621c1a43f2e736f9", "type": "zip", "shasum": "", "reference": "f3bcbfa24217c0739c5671b4621c1a43f2e736f9"}, "time": "2022-10-11T15:17:31+00:00"}, {"version": "v2.16.29", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d3e65d53137d00cfdaa1d1bf4b5f0036779a8ffb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d3e65d53137d00cfdaa1d1bf4b5f0036779a8ffb", "type": "zip", "shasum": "", "reference": "d3e65d53137d00cfdaa1d1bf4b5f0036779a8ffb"}, "time": "2022-10-10T17:53:27+00:00"}, {"version": "v2.16.28", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "567fe8d44ae855f75c8c6c29467d81ca1255656b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/567fe8d44ae855f75c8c6c29467d81ca1255656b", "type": "zip", "shasum": "", "reference": "567fe8d44ae855f75c8c6c29467d81ca1255656b"}, "time": "2022-10-10T08:49:44+00:00"}, {"version": "v2.16.27", "version_normalized": "*********"}, {"version": "v2.16.26", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d4ce73aaf998f8472f2526ecdcbb55cffea8de8b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d4ce73aaf998f8472f2526ecdcbb55cffea8de8b", "type": "zip", "shasum": "", "reference": "d4ce73aaf998f8472f2526ecdcbb55cffea8de8b"}, "time": "2022-10-08T21:36:11+00:00"}, {"version": "v2.16.25", "version_normalized": "*********"}, {"version": "v2.16.24", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5576ffb0f426effa7e3c11aa5578180d685af5e6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5576ffb0f426effa7e3c11aa5578180d685af5e6", "type": "zip", "shasum": "", "reference": "5576ffb0f426effa7e3c11aa5578180d685af5e6"}, "time": "2022-10-07T13:19:43+00:00"}, {"version": "v2.16.23", "version_normalized": "*********"}, {"version": "v2.16.22", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "52a596d9c1843aad31f19f7774caf15e70c6cc31"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/52a596d9c1843aad31f19f7774caf15e70c6cc31", "type": "zip", "shasum": "", "reference": "52a596d9c1843aad31f19f7774caf15e70c6cc31"}, "time": "2022-10-06T12:44:43+00:00"}, {"version": "v2.16.21", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "466ea57d8744f834e4c2bcf30bf66e7b82c41e89"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/466ea57d8744f834e4c2bcf30bf66e7b82c41e89", "type": "zip", "shasum": "", "reference": "466ea57d8744f834e4c2bcf30bf66e7b82c41e89"}, "time": "2022-09-30T09:40:22+00:00"}, {"version": "v2.16.20", "version_normalized": "*********"}, {"version": "v2.16.19", "version_normalized": "*********"}, {"version": "v2.16.18", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "559c821e44887058c168e7e883b23267df841e70"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/559c821e44887058c168e7e883b23267df841e70", "type": "zip", "shasum": "", "reference": "559c821e44887058c168e7e883b23267df841e70"}, "time": "2022-09-23T10:20:40+00:00"}, {"version": "v2.16.17", "version_normalized": "*********"}, {"version": "v2.16.16", "version_normalized": "*********"}, {"version": "v2.16.15", "version_normalized": "*********"}, {"version": "v2.16.14", "version_normalized": "*********"}, {"version": "v2.16.13", "version_normalized": "*********"}, {"version": "v2.16.12", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5165b6bdffc93ef2176136f5425409faf994a81c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5165b6bdffc93ef2176136f5425409faf994a81c", "type": "zip", "shasum": "", "reference": "5165b6bdffc93ef2176136f5425409faf994a81c"}, "time": "2022-09-22T19:57:30+00:00"}, {"version": "v2.16.11", "version_normalized": "*********"}, {"version": "v2.16.10", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "0605d4a99bd72962d6b795473c9fd4d00bf64c35"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/0605d4a99bd72962d6b795473c9fd4d00bf64c35", "type": "zip", "shasum": "", "reference": "0605d4a99bd72962d6b795473c9fd4d00bf64c35"}, "time": "2022-09-19T09:55:24+00:00"}, {"version": "v2.16.9", "version_normalized": "********"}, {"version": "v2.16.8", "version_normalized": "********"}, {"version": "v2.16.7", "version_normalized": "********"}, {"version": "v2.16.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "93919a0d067c82001ca8168eb0ac599d3c63967a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/93919a0d067c82001ca8168eb0ac599d3c63967a", "type": "zip", "shasum": "", "reference": "93919a0d067c82001ca8168eb0ac599d3c63967a"}, "time": "2022-09-17T17:15:43+00:00"}, {"version": "v2.16.5", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "8bc1cbfb4aede05113d499677be870d71b87ddf1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/8bc1cbfb4aede05113d499677be870d71b87ddf1", "type": "zip", "shasum": "", "reference": "8bc1cbfb4aede05113d499677be870d71b87ddf1"}, "time": "2022-09-16T10:23:15+00:00"}, {"version": "v2.16.4", "version_normalized": "********"}, {"version": "v2.16.3", "version_normalized": "********"}, {"version": "v2.16.2", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "3c704738306123337acebe0ba990c6fd84b4842c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/3c704738306123337acebe0ba990c6fd84b4842c", "type": "zip", "shasum": "", "reference": "3c704738306123337acebe0ba990c6fd84b4842c"}, "time": "2022-09-12T15:57:43+00:00"}, {"version": "v2.16.1", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "7c82359cc3521892aa5ab47e50ef9670a1a2f240"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/7c82359cc3521892aa5ab47e50ef9670a1a2f240", "type": "zip", "shasum": "", "reference": "7c82359cc3521892aa5ab47e50ef9670a1a2f240"}, "time": "2022-09-10T14:22:10+00:00"}, {"version": "v2.16.0", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "197ecc05b9963da9d8937f9b9d693354c9191dfa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/197ecc05b9963da9d8937f9b9d693354c9191dfa", "type": "zip", "shasum": "", "reference": "197ecc05b9963da9d8937f9b9d693354c9191dfa"}, "time": "2022-09-10T12:35:15+00:00"}, {"version": "v2.15.49", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5f148469ec5466688b5fc01a23d48e64a6b88b1e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5f148469ec5466688b5fc01a23d48e64a6b88b1e", "type": "zip", "shasum": "", "reference": "5f148469ec5466688b5fc01a23d48e64a6b88b1e"}, "time": "2022-09-01T22:09:39+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-heroicons": "^1.2", "filament/support": "self.version", "illuminate/contracts": "^8.6|^9.0", "illuminate/filesystem": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v2.15.48", "version_normalized": "*********"}, {"version": "v2.15.47", "version_normalized": "*********"}, {"version": "v2.15.46", "version_normalized": "*********"}, {"version": "v2.15.45", "version_normalized": "*********"}, {"version": "v2.15.44", "version_normalized": "*********"}, {"version": "v2.15.43", "version_normalized": "*********"}, {"version": "v2.15.42", "version_normalized": "*********"}, {"version": "v2.15.41", "version_normalized": "*********"}, {"version": "v2.15.40", "version_normalized": "*********"}, {"version": "v2.15.39", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "df443e9f7d92c3c9f7c796e87d26003a3ae27a09"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/df443e9f7d92c3c9f7c796e87d26003a3ae27a09", "type": "zip", "shasum": "", "reference": "df443e9f7d92c3c9f7c796e87d26003a3ae27a09"}, "time": "2022-08-31T10:18:24+00:00"}, {"version": "v2.15.38", "version_normalized": "*********"}, {"version": "v2.15.37", "version_normalized": "*********"}, {"version": "v2.15.36", "version_normalized": "*********"}, {"version": "v2.15.35", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "ee71b91e7b3b81e30cd43ccac686b20f4389ae93"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/ee71b91e7b3b81e30cd43ccac686b20f4389ae93", "type": "zip", "shasum": "", "reference": "ee71b91e7b3b81e30cd43ccac686b20f4389ae93"}, "time": "2022-08-31T06:31:35+00:00"}, {"version": "v2.15.34", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "55a3d3ee0e99a8d4e60c87232718cbcdbcf3e494"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/55a3d3ee0e99a8d4e60c87232718cbcdbcf3e494", "type": "zip", "shasum": "", "reference": "55a3d3ee0e99a8d4e60c87232718cbcdbcf3e494"}, "time": "2022-08-30T22:08:46+00:00"}, {"version": "v2.15.33", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "7b58c41d56d13536b950e2d88fe716beed24e304"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/7b58c41d56d13536b950e2d88fe716beed24e304", "type": "zip", "shasum": "", "reference": "7b58c41d56d13536b950e2d88fe716beed24e304"}, "time": "2022-08-28T20:22:14+00:00"}, {"version": "v2.15.32", "version_normalized": "*********"}, {"version": "v2.15.31", "version_normalized": "*********"}, {"version": "v2.15.30", "version_normalized": "*********"}, {"version": "v2.15.29", "version_normalized": "*********"}, {"version": "v2.15.28", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "682aa64221cb0e6d6c67fa6e4fff596b9918749a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/682aa64221cb0e6d6c67fa6e4fff596b9918749a", "type": "zip", "shasum": "", "reference": "682aa64221cb0e6d6c67fa6e4fff596b9918749a"}, "time": "2022-08-26T18:16:52+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-heroicons": "^1.2", "filament/support": "self.version", "illuminate/contracts": "^8.6|^9.0", "illuminate/filesystem": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "livewire/livewire": "^2.6", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v2.15.27", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "08b375a305a293eb80ba5099c98fa2253449bef6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/08b375a305a293eb80ba5099c98fa2253449bef6", "type": "zip", "shasum": "", "reference": "08b375a305a293eb80ba5099c98fa2253449bef6"}, "time": "2022-08-25T16:40:45+00:00"}, {"version": "v2.15.26", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "a72331e19eec77e3068ce348db51470edccf4155"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/a72331e19eec77e3068ce348db51470edccf4155", "type": "zip", "shasum": "", "reference": "a72331e19eec77e3068ce348db51470edccf4155"}, "time": "2022-08-22T22:07:12+00:00"}, {"version": "v2.15.25", "version_normalized": "*********"}, {"version": "v2.15.24", "version_normalized": "*********"}, {"version": "v2.15.23", "version_normalized": "*********"}, {"version": "v2.15.22", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9a42a06554199ebd0cc4605a4ab36e71d13fdab9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9a42a06554199ebd0cc4605a4ab36e71d13fdab9", "type": "zip", "shasum": "", "reference": "9a42a06554199ebd0cc4605a4ab36e71d13fdab9"}, "time": "2022-08-20T20:09:05+00:00"}, {"version": "v2.15.21", "version_normalized": "*********"}, {"version": "v2.15.20", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "9a5100b97e4b65d98c8ae610e68c0d530c9db1a8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/9a5100b97e4b65d98c8ae610e68c0d530c9db1a8", "type": "zip", "shasum": "", "reference": "9a5100b97e4b65d98c8ae610e68c0d530c9db1a8"}, "time": "2022-08-08T09:45:06+00:00"}, {"version": "v2.15.19", "version_normalized": "*********"}, {"version": "v2.15.19-beta1", "version_normalized": "*********-beta1"}, {"version": "v2.15.18", "version_normalized": "*********"}, {"version": "v2.15.17", "version_normalized": "*********"}, {"version": "v2.15.16", "version_normalized": "*********"}, {"version": "v2.15.15", "version_normalized": "*********"}, {"version": "v2.15.14", "version_normalized": "*********"}, {"version": "v2.15.13", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "fa88922541f4e4b0553213f1c3314739f93f0e04"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/fa88922541f4e4b0553213f1c3314739f93f0e04", "type": "zip", "shasum": "", "reference": "fa88922541f4e4b0553213f1c3314739f93f0e04"}, "time": "2022-08-04T21:29:35+00:00"}, {"version": "v2.15.12", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "d94d9d80b39c07bac86848f56632db1ddafcfc4d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/d94d9d80b39c07bac86848f56632db1ddafcfc4d", "type": "zip", "shasum": "", "reference": "d94d9d80b39c07bac86848f56632db1ddafcfc4d"}, "time": "2022-08-03T11:31:27+00:00"}, {"version": "v2.15.11", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "cb68422135943eb7cb1603bf7cd801cc47620a0f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/cb68422135943eb7cb1603bf7cd801cc47620a0f", "type": "zip", "shasum": "", "reference": "cb68422135943eb7cb1603bf7cd801cc47620a0f"}, "time": "2022-08-01T21:24:53+00:00"}, {"version": "v2.15.10", "version_normalized": "*********"}, {"version": "v2.15.9", "version_normalized": "********"}, {"version": "v2.15.8", "version_normalized": "********"}, {"version": "v2.15.7", "version_normalized": "********"}, {"version": "v2.15.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "4bdd20ab23f691ce37da45776a6ff9509c5df320"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/4bdd20ab23f691ce37da45776a6ff9509c5df320", "type": "zip", "shasum": "", "reference": "4bdd20ab23f691ce37da45776a6ff9509c5df320"}, "time": "2022-08-01T19:46:40+00:00"}, {"version": "v2.15.5", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "709b88ab374142808417fd76ebe8e3ff0d430bdd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/709b88ab374142808417fd76ebe8e3ff0d430bdd", "type": "zip", "shasum": "", "reference": "709b88ab374142808417fd76ebe8e3ff0d430bdd"}, "time": "2022-07-26T14:00:46+00:00"}, {"version": "v2.15.4", "version_normalized": "********"}, {"version": "v2.15.3", "version_normalized": "********"}, {"version": "v2.15.2", "version_normalized": "********"}, {"version": "v2.15.1", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/notifications.git", "type": "git", "reference": "5d11dc9614ddd6e6d4589bb44b000be36860055c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/notifications/zipball/5d11dc9614ddd6e6d4589bb44b000be36860055c", "type": "zip", "shasum": "", "reference": "5d11dc9614ddd6e6d4589bb44b000be36860055c"}, "time": "2022-07-25T21:47:52+00:00"}]}, "security-advisories": [], "last-modified": "Tu<PERSON>, 08 Jul 2025 21:00:14 GMT"}