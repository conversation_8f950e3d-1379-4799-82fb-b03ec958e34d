{"minified": "composer/2.0", "packages": {"knuckleswtf/pastel": [{"name": "knuckleswtf/pastel", "description": "Write your API docs in Markdown and get them converted into pretty HTML🎨", "keywords": ["documentation", "markdown", "generation"], "homepage": "", "version": "1.3.7", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "f26642b7f7506c3c4f17aee3e0010546ce0069d0"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/f26642b7f7506c3c4f17aee3e0010546ce0069d0", "type": "zip", "shasum": "", "reference": "f26642b7f7506c3c4f17aee3e0010546ce0069d0"}, "type": "library", "time": "2021-03-27T17:22:04+00:00", "autoload": {"files": ["helpers.php"], "psr-4": {"Knuckles\\Pastel\\": "src/"}}, "bin": ["pastel"], "require": {"php": ">=7.2.5", "mnapoli/silly": "~1.0", "illuminate/support": "^5.8|^6.0|^7.0|^8.0", "illuminate/view": "^5.8|^6.0|^7.0|^8.0", "mnapoli/front-yaml": "^1.7.0", "windwalker/renderer": "3.*", "ext-json": "*", "shalvah/clara": "^2.0.0"}, "require-dev": {"phpunit/phpunit": "^9.1", "league/flysystem": "^2.0@dev", "ext-dom": "*", "ext-libxml": "*", "phpstan/phpstan": "^0.12.23"}, "replace": {"mpociot/documentarian": "*"}, "abandoned": true, "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.3.7"}}, {"version": "1.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "0ce32f5c80bc732392afbaf51983a861211b823a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/0ce32f5c80bc732392afbaf51983a861211b823a", "type": "zip", "shasum": "", "reference": "0ce32f5c80bc732392afbaf51983a861211b823a"}, "time": "2020-09-10T14:13:02+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.3.6"}}, {"version": "1.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "c2bef79ee35322be4b9f8a7380b5c8d899c0e781"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/c2bef79ee35322be4b9f8a7380b5c8d899c0e781", "type": "zip", "shasum": "", "reference": "c2bef79ee35322be4b9f8a7380b5c8d899c0e781"}, "time": "2020-09-07T22:31:25+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/master"}}, {"version": "1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "2370abc192a1e9009bb8245fcf12a3be53f8f857"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/2370abc192a1e9009bb8245fcf12a3be53f8f857", "type": "zip", "shasum": "", "reference": "2370abc192a1e9009bb8245fcf12a3be53f8f857"}, "time": "2020-07-14T19:34:24+00:00", "require": {"php": ">=7.2.5", "mnapoli/silly": "~1.0", "illuminate/view": "^6.0|^7.0|^8.0", "mnapoli/front-yaml": "^1.7.0", "windwalker/renderer": "3.*", "ext-json": "*", "shalvah/clara": "^2.0.0"}, "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.3.4"}}, {"description": "Generate pretty API documentation from Markdown.", "version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "208a665fc53bd88e9c5ae0e604218d57906b40e6"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/208a665fc53bd88e9c5ae0e604218d57906b40e6", "type": "zip", "shasum": "", "reference": "208a665fc53bd88e9c5ae0e604218d57906b40e6"}, "time": "2020-05-21T11:44:49+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.3.3"}}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "582e36d8a52cf90f2837ec09ff939e64118e7259"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/582e36d8a52cf90f2837ec09ff939e64118e7259", "type": "zip", "shasum": "", "reference": "582e36d8a52cf90f2837ec09ff939e64118e7259"}, "time": "2020-05-10T13:16:37+00:00", "require": {"php": ">=7.2.5", "mnapoli/silly": "~1.0", "illuminate/view": "^6.0|^7.0", "mnapoli/front-yaml": "^1.6 ", "windwalker/renderer": "3.*", "ext-json": "*", "shalvah/clara": "^2.0.0"}, "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.3.2"}}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "09a86b4c719262f5276fad13311f04779e245fa3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/09a86b4c719262f5276fad13311f04779e245fa3", "type": "zip", "shasum": "", "reference": "09a86b4c719262f5276fad13311f04779e245fa3"}, "time": "2020-05-06T21:18:42+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/master"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "93694fe67e2f3abedf50b229ba53a25d1f983fb0"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/93694fe67e2f3abedf50b229ba53a25d1f983fb0", "type": "zip", "shasum": "", "reference": "93694fe67e2f3abedf50b229ba53a25d1f983fb0"}, "time": "2020-04-11T14:36:15+00:00", "require": {"php": ">=7.3", "mnapoli/silly": "~1.0", "illuminate/view": "^6.0|^7.0", "mnapoli/front-yaml": "^1.6 ", "windwalker/renderer": "3.*", "ext-json": "*", "shalvah/clara": "^2.0.0"}, "require-dev": {"phpunit/phpunit": "^9.1", "league/flysystem": "^2.0@dev", "ext-dom": "*", "ext-libxml": "*"}, "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.3.0"}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "7b5a371e82189241abe8dcf0d2f7b2a96c155d0c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/7b5a371e82189241abe8dcf0d2f7b2a96c155d0c", "type": "zip", "shasum": "", "reference": "7b5a371e82189241abe8dcf0d2f7b2a96c155d0c"}, "time": "2020-04-10T23:04:54+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.2.1"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "2feca300594b6d52bd9d51c9d67db26aad922b4b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/2feca300594b6d52bd9d51c9d67db26aad922b4b", "type": "zip", "shasum": "", "reference": "2feca300594b6d52bd9d51c9d67db26aad922b4b"}, "time": "2020-04-10T22:37:47+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/master"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "e7fbc15f3c9d31aa2d040cf9109c903712ef956f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/e7fbc15f3c9d31aa2d040cf9109c903712ef956f", "type": "zip", "shasum": "", "reference": "e7fbc15f3c9d31aa2d040cf9109c903712ef956f"}, "time": "2020-04-10T03:39:14+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "a0b5424212843c801e43324bffd3505ff133d240"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/a0b5424212843c801e43324bffd3505ff133d240", "type": "zip", "shasum": "", "reference": "a0b5424212843c801e43324bffd3505ff133d240"}, "time": "2020-04-10T03:10:18+00:00", "require-dev": {"phpunit/phpunit": "^9.1", "league/flysystem": "^2.0", "ext-dom": "*", "ext-libxml": "*"}, "replace": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "977bb3414cb597e4159aa310877551de801c1e82"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/977bb3414cb597e4159aa310877551de801c1e82", "type": "zip", "shasum": "", "reference": "977bb3414cb597e4159aa310877551de801c1e82"}, "time": "2020-04-09T19:05:40+00:00", "require": {"php": ">=7.3", "mnapoli/silly": "~1.0", "illuminate/view": "^6.0|^7.0", "mnapoli/front-yaml": "^1.6 ", "windwalker/renderer": "3.*", "league/flysystem": "^2.0", "ext-json": "*", "shalvah/clara": "^1.1.0"}, "require-dev": {"phpunit/phpunit": "^8.5", "ext-dom": "*", "ext-libxml": "*"}, "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/1.0.0"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "239d5c9bde256378d77aca6b0a623a2eed67b071"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/239d5c9bde256378d77aca6b0a623a2eed67b071", "type": "zip", "shasum": "", "reference": "239d5c9bde256378d77aca6b0a623a2eed67b071"}, "time": "2020-04-08T18:08:44+00:00", "autoload": {"files": ["helpers.php"], "psr-4": {"Shalvah\\Pastel\\": "src/"}}, "require": {"php": ">=7.3", "mnapoli/silly": "~1.0", "illuminate/view": "^6.0|^7.0", "mnapoli/front-yaml": "^1.6 ", "windwalker/renderer": "3.*", "league/flysystem": "^2.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/0.2.0"}}, {"description": "Generate beautiful API documentation from Markdown.", "version": "0.1.0", "version_normalized": "*******", "license": ["Apache 2"], "source": {"url": "https://github.com/knuckleswtf/pastel.git", "type": "git", "reference": "5e7bb68b9ecc928415f9dba7586f42d132e0f932"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/pastel/zipball/5e7bb68b9ecc928415f9dba7586f42d132e0f932", "type": "zip", "shasum": "", "reference": "5e7bb68b9ecc928415f9dba7586f42d132e0f932"}, "time": "2020-04-07T21:28:33+00:00", "support": {"issues": "https://github.com/knuckleswtf/pastel/issues", "source": "https://github.com/knuckleswtf/pastel/tree/0.1.0"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 13:01:01 GMT"}