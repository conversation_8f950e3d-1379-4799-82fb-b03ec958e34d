{"minified": "composer/2.0", "packages": {"myclabs/php-enum": [{"name": "myclabs/php-enum", "description": "PHP Enum implementation", "keywords": ["enum"], "homepage": "https://github.com/myclabs/php-enum", "version": "1.8.5", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "e7be26966b7398204a234f8673fdad5ac6277802"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/e7be26966b7398204a234f8673fdad5ac6277802", "type": "zip", "shasum": "", "reference": "e7be26966b7398204a234f8673fdad5ac6277802"}, "type": "library", "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.5"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2025-01-14T11:49:03+00:00", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "require": {"php": "^7.3 || ^8.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2 || ^5.2"}}, {"homepage": "http://github.com/myclabs/php-enum", "version": "1.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/a867478eae49c9f59ece437ae7f9506bfaa27483", "type": "zip", "shasum": "", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.4"}, "time": "2022-08-04T09:53:51+00:00", "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}}, {"version": "1.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "b942d263c641ddb5190929ff840c68f78713e937"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/b942d263c641ddb5190929ff840c68f78713e937", "type": "zip", "shasum": "", "reference": "b942d263c641ddb5190929ff840c68f78713e937"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.3"}, "time": "2021-07-05T08:18:36+00:00", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}}, {"version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "8bef486e885cae67ced6e43257300e8acc3f06ad"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/8bef486e885cae67ced6e43257300e8acc3f06ad", "type": "zip", "shasum": "", "reference": "8bef486e885cae67ced6e43257300e8acc3f06ad"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.2"}, "time": "2021-07-04T17:44:39+00:00"}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "55555d31a622b4bc9662664132a0533ae6ef47b1"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/55555d31a622b4bc9662664132a0533ae6ef47b1", "type": "zip", "shasum": "", "reference": "55555d31a622b4bc9662664132a0533ae6ef47b1"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.1"}, "time": "2021-06-29T09:20:05+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "46cf3d8498b095bd33727b13fd5707263af99421"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/46cf3d8498b095bd33727b13fd5707263af99421", "type": "zip", "shasum": "", "reference": "46cf3d8498b095bd33727b13fd5707263af99421"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.0"}, "time": "2021-02-15T16:11:48+00:00", "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.5.1"}}, {"version": "1.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "type": "zip", "shasum": "", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "time": "2020-11-14T18:14:52+00:00", "require": {"php": ">=7.1", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}}, {"version": "1.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "5f36467c7a87e20fbdc51e524fd8f9d1de80187c"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/5f36467c7a87e20fbdc51e524fd8f9d1de80187c", "type": "zip", "shasum": "", "reference": "5f36467c7a87e20fbdc51e524fd8f9d1de80187c"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/master"}, "time": "2020-02-14T08:15:52+00:00", "funding": "__unset"}, {"version": "1.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "aef7b9f343a677744d7410485a60fe16c46ca1ed"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/aef7b9f343a677744d7410485a60fe16c46ca1ed", "type": "zip", "shasum": "", "reference": "aef7b9f343a677744d7410485a60fe16c46ca1ed"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.5"}, "time": "2020-02-13T09:43:36+00:00"}, {"version": "1.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "c3f3b36602748871098069f4f2f8224ca34df899"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/c3f3b36602748871098069f4f2f8224ca34df899", "type": "zip", "shasum": "", "reference": "c3f3b36602748871098069f4f2f8224ca34df899"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.4"}, "time": "2020-02-07T08:35:07+00:00"}, {"version": "1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "ff059b00bd7d6f9541e650cad38e89d9cee7ada1"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/ff059b00bd7d6f9541e650cad38e89d9cee7ada1", "type": "zip", "shasum": "", "reference": "ff059b00bd7d6f9541e650cad38e89d9cee7ada1"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/master"}, "time": "2020-02-07T08:07:48+00:00", "require": {"php": ">=7.1", "ext-json": "*", "vimeo/psalm": "^3.8"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*"}}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "45f01adf6922df6082bcda36619deb466e826acf"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/45f01adf6922df6082bcda36619deb466e826acf", "type": "zip", "shasum": "", "reference": "45f01adf6922df6082bcda36619deb466e826acf"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.2"}, "time": "2019-08-19T13:53:00+00:00", "require": {"php": ">=7.1", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7|^6.0", "squizlabs/php_codesniffer": "1.*"}}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "f46847626b8739de22e4ebc6b56010f317d4448d"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/f46847626b8739de22e4ebc6b56010f317d4448d", "type": "zip", "shasum": "", "reference": "f46847626b8739de22e4ebc6b56010f317d4448d"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.1"}, "time": "2019-05-05T10:12:03+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "e00b7b3d8d2ef0da908ea9d3df627271b93a9253"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/e00b7b3d8d2ef0da908ea9d3df627271b93a9253", "type": "zip", "shasum": "", "reference": "e00b7b3d8d2ef0da908ea9d3df627271b93a9253"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.0"}, "time": "2019-04-23T12:00:56+00:00", "require": {"php": ">=7.2", "ext-json": "*"}}, {"version": "1.6.6", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "32c4202886c51fbe5cc3a7c34ec5c9a4a790345e"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/32c4202886c51fbe5cc3a7c34ec5c9a4a790345e", "type": "zip", "shasum": "", "reference": "32c4202886c51fbe5cc3a7c34ec5c9a4a790345e"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/master"}, "time": "2019-02-04T21:18:49+00:00", "require": {"php": ">=5.4", "ext-json": "*"}}, {"version": "1.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "5b9c57d6687607b0177c8233c96045e3fc3b80f9"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/5b9c57d6687607b0177c8233c96045e3fc3b80f9", "type": "zip", "shasum": "", "reference": "5b9c57d6687607b0177c8233c96045e3fc3b80f9"}, "time": "2019-02-04T18:09:58+00:00", "require": {"php": ">=5.4"}}, {"version": "1.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "550d2334d77f91b0816a5cbd6965272fe20146b8"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/550d2334d77f91b0816a5cbd6965272fe20146b8", "type": "zip", "shasum": "", "reference": "550d2334d77f91b0816a5cbd6965272fe20146b8"}, "time": "2018-10-30T14:36:18+00:00"}, {"version": "1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "a8284c7c540caf9988e339a404cadcdf1e8b165d"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/a8284c7c540caf9988e339a404cadcdf1e8b165d", "type": "zip", "shasum": "", "reference": "a8284c7c540caf9988e339a404cadcdf1e8b165d"}, "time": "2018-10-27T21:15:22+00:00"}, {"version": "1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "ca2f4090a7ecae6f0c67fc9bd07cfb51cdf04219"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/ca2f4090a7ecae6f0c67fc9bd07cfb51cdf04219", "type": "zip", "shasum": "", "reference": "ca2f4090a7ecae6f0c67fc9bd07cfb51cdf04219"}, "time": "2018-08-01T21:05:54+00:00"}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "8c5649e4ed99acd53a40eada270154dcac089f7e"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/8c5649e4ed99acd53a40eada270154dcac089f7e", "type": "zip", "shasum": "", "reference": "8c5649e4ed99acd53a40eada270154dcac089f7e"}, "time": "2018-05-09T08:09:15+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "ca4a819736039bfaf53a8a0102ba2bb7962652fd"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/ca4a819736039bfaf53a8a0102ba2bb7962652fd", "type": "zip", "shasum": "", "reference": "ca4a819736039bfaf53a8a0102ba2bb7962652fd"}, "time": "2018-05-07T06:08:12+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "3ed7088cfd0a0e06534b7f8b0eee82acea574fac"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/3ed7088cfd0a0e06534b7f8b0eee82acea574fac", "type": "zip", "shasum": "", "reference": "3ed7088cfd0a0e06534b7f8b0eee82acea574fac"}, "time": "2017-06-28T16:24:08+00:00", "require": {"php": ">=5.3"}}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "61f4a24da5be216301447f3278ea6562609d61f5"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/61f4a24da5be216301447f3278ea6562609d61f5", "type": "zip", "shasum": "", "reference": "61f4a24da5be216301447f3278ea6562609d61f5"}, "time": "2017-03-26T10:24:21+00:00", "require-dev": {"phpunit/phpunit": "4.*", "squizlabs/php_codesniffer": "1.*"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "e42fa9d2ae5dd660dbd0fb573d94c61e5a0dbb02"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/e42fa9d2ae5dd660dbd0fb573d94c61e5a0dbb02", "type": "zip", "shasum": "", "reference": "e42fa9d2ae5dd660dbd0fb573d94c61e5a0dbb02"}, "time": "2016-10-09T21:43:05+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "e83e992a5b5a0858ef7adbc9239a749d71fb6979"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/e83e992a5b5a0858ef7adbc9239a749d71fb6979", "type": "zip", "shasum": "", "reference": "e83e992a5b5a0858ef7adbc9239a749d71fb6979"}, "time": "2016-08-01T15:48:47+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "07da9d1a7469941ae05b046193fac4c83bdb0d7e"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/07da9d1a7469941ae05b046193fac4c83bdb0d7e", "type": "zip", "shasum": "", "reference": "07da9d1a7469941ae05b046193fac4c83bdb0d7e"}, "time": "2015-07-22T16:14:03+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "7c4cd65efc984e80c70522b0aa50545ea51dfff4"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/7c4cd65efc984e80c70522b0aa50545ea51dfff4", "type": "zip", "shasum": "", "reference": "7c4cd65efc984e80c70522b0aa50545ea51dfff4"}, "time": "2015-05-19T05:28:18+00:00"}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "e5976f17636cd68cf07ee5fc3d036c0fd30d6297"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/e5976f17636cd68cf07ee5fc3d036c0fd30d6297", "type": "zip", "shasum": "", "reference": "e5976f17636cd68cf07ee5fc3d036c0fd30d6297"}, "time": "2015-02-15T21:39:11+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "5fa55b5135a68fa077d480b1cf916e97b9b8151c"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/5fa55b5135a68fa077d480b1cf916e97b9b8151c", "type": "zip", "shasum": "", "reference": "5fa55b5135a68fa077d480b1cf916e97b9b8151c"}, "time": "2015-02-03T20:43:56+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "c0bcd731d26d53d3db280cca0af33b3cd99fbafe"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/c0bcd731d26d53d3db280cca0af33b3cd99fbafe", "type": "zip", "shasum": "", "reference": "c0bcd731d26d53d3db280cca0af33b3cd99fbafe"}, "time": "2015-01-30T22:06:24+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "authors": [], "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "b52c2f215f5b251693369309ea7f537f9d92ec5e"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/b52c2f215f5b251693369309ea7f537f9d92ec5e", "type": "zip", "shasum": "", "reference": "b52c2f215f5b251693369309ea7f537f9d92ec5e"}, "time": "2013-11-11T18:29:08+00:00", "autoload": {"psr-0": {"MyCLabs": "src/"}}, "require": "__unset", "require-dev": "__unset"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "ab9e3122fb4f24bff81db37652b88cefe2b4b89c"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/ab9e3122fb4f24bff81db37652b88cefe2b4b89c", "type": "zip", "shasum": "", "reference": "ab9e3122fb4f24bff81db37652b88cefe2b4b89c"}, "time": "2013-07-24T12:08:12+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "dad3bc66c0851a81562f9648b2d076687a979db3"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/dad3bc66c0851a81562f9648b2d076687a979db3", "type": "zip", "shasum": "", "reference": "dad3bc66c0851a81562f9648b2d076687a979db3"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.1.0"}, "time": "2013-03-20T22:20:22+00:00"}, {"homepage": "http://github.com/myc-sense/php-enum", "version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "30301419f881ddf544d49c105a740ba3d29e14f4"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/30301419f881ddf544d49c105a740ba3d29e14f4", "type": "zip", "shasum": "", "reference": "30301419f881ddf544d49c105a740ba3d29e14f4"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.0.1"}, "time": "2013-03-19T14:00:48+00:00", "autoload": {"psr-0": {"Mycsense": "src/"}}}, {"keywords": [], "homepage": "", "version": "1.0.0", "version_normalized": "*******", "license": [], "source": {"url": "https://github.com/myclabs/php-enum.git", "type": "git", "reference": "03e6624e4d7fa40be76b21d867c0929a621c1aa6"}, "dist": {"url": "https://api.github.com/repos/myclabs/php-enum/zipball/03e6624e4d7fa40be76b21d867c0929a621c1aa6", "type": "zip", "shasum": "", "reference": "03e6624e4d7fa40be76b21d867c0929a621c1aa6"}, "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.0.0"}, "time": "2013-03-19T13:53:17+00:00"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 14 Jan 2025 11:49:54 GMT"}