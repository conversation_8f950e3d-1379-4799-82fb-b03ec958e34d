{"minified": "composer/2.0", "packages": {"cakephp/chronos": [{"name": "cakephp/chronos", "description": "A simple API extension for DateTime.", "keywords": ["time", "date", "datetime"], "homepage": "https://cakephp.org", "version": "3.1.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "The CakePHP Team", "homepage": "https://cakephp.org"}], "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "786d69e1ee4b735765cbdb5521b9603e9b98d650"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/786d69e1ee4b735765cbdb5521b9603e9b98d650", "type": "zip", "shasum": "", "reference": "786d69e1ee4b735765cbdb5521b9603e9b98d650"}, "type": "library", "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos"}, "funding": [], "time": "2024-07-18T03:18:04+00:00", "autoload": {"psr-4": {"Cake\\Chronos\\": "src/"}}, "require": {"php": ">=8.1", "psr/clock": "^1.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^10.1.0 || ^11.1.3"}, "provide": {"psr/clock-implementation": "1.0"}}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "9cb035acd10152a6b74df936986f15c4e6015bd3"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/9cb035acd10152a6b74df936986f15c4e6015bd3", "type": "zip", "shasum": "", "reference": "9cb035acd10152a6b74df936986f15c4e6015bd3"}, "time": "2023-10-17T07:41:48+00:00", "require": {"php": ">=8.1"}, "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^10.1.0"}, "provide": "__unset"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "54164f6775ae5c8a930583983d62c9ee0815fcf0"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/54164f6775ae5c8a930583983d62c9ee0815fcf0", "type": "zip", "shasum": "", "reference": "54164f6775ae5c8a930583983d62c9ee0815fcf0"}, "time": "2023-10-02T16:47:43+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "635fa0cbb5c8cffcae224b8ef6d7ad2c77178382"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/635fa0cbb5c8cffcae224b8ef6d7ad2c77178382", "type": "zip", "shasum": "", "reference": "635fa0cbb5c8cffcae224b8ef6d7ad2c77178382"}, "time": "2023-09-29T02:55:40+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "a89857afd81938e0f67ec6eb01197c2bdac66719"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/a89857afd81938e0f67ec6eb01197c2bdac66719", "type": "zip", "shasum": "", "reference": "a89857afd81938e0f67ec6eb01197c2bdac66719"}, "time": "2023-09-09T03:47:34+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "414dbfb80069e19f74731755a27601b5bb60c8ec"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/414dbfb80069e19f74731755a27601b5bb60c8ec", "type": "zip", "shasum": "", "reference": "414dbfb80069e19f74731755a27601b5bb60c8ec"}, "time": "2023-07-30T00:53:32+00:00"}, {"version": "3.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "d26e6bb30a62c5c8d779afa33bc1d83373e16d20"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/d26e6bb30a62c5c8d779afa33bc1d83373e16d20", "type": "zip", "shasum": "", "reference": "d26e6bb30a62c5c8d779afa33bc1d83373e16d20"}, "time": "2023-04-26T08:33:29+00:00", "require-dev": {"cakephp/cakephp-codesniffer": "^5.0", "phpunit/phpunit": "^9.5"}}, {"version": "3.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "5e0875d54e47d779618f8dd17df9a1bd0f9c557d"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/5e0875d54e47d779618f8dd17df9a1bd0f9c557d", "type": "zip", "shasum": "", "reference": "5e0875d54e47d779618f8dd17df9a1bd0f9c557d"}, "time": "2023-03-14T18:13:08+00:00"}, {"version": "3.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "33c64be459da221f87939dd05842043cbf4e0635"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/33c64be459da221f87939dd05842043cbf4e0635", "type": "zip", "shasum": "", "reference": "33c64be459da221f87939dd05842043cbf4e0635"}, "time": "2022-12-17T16:05:40+00:00"}, {"version": "2.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "b0321ab7658af9e7abcb3dd876f226e6f3dbb81f"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/b0321ab7658af9e7abcb3dd876f226e6f3dbb81f", "type": "zip", "shasum": "", "reference": "b0321ab7658af9e7abcb3dd876f226e6f3dbb81f"}, "time": "2024-07-30T22:26:11+00:00", "autoload": {"files": ["src/carbon_compat.php"], "psr-4": {"Cake\\Chronos\\": "src/"}}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.0 || ^9.0", "cakephp/cakephp-codesniffer": "^4.5"}}, {"version": "2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "03208c18eb3267490662e68671bb9c22aa804492"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/03208c18eb3267490662e68671bb9c22aa804492", "type": "zip", "shasum": "", "reference": "03208c18eb3267490662e68671bb9c22aa804492"}, "time": "2023-11-03T05:26:08+00:00"}, {"version": "2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "96f28ddfceba2ff56e0d2405c28d789bd546ff55"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/96f28ddfceba2ff56e0d2405c28d789bd546ff55", "type": "zip", "shasum": "", "reference": "96f28ddfceba2ff56e0d2405c28d789bd546ff55"}, "time": "2023-10-17T08:00:24+00:00"}, {"version": "2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "79b58b76899cdba114ea45d18c79f8af9a9192b0"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/79b58b76899cdba114ea45d18c79f8af9a9192b0", "type": "zip", "shasum": "", "reference": "79b58b76899cdba114ea45d18c79f8af9a9192b0"}, "time": "2023-10-10T11:22:20+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "dd583900b26971e84d56c482d6c5fc16961bd103"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/dd583900b26971e84d56c482d6c5fc16961bd103", "type": "zip", "shasum": "", "reference": "dd583900b26971e84d56c482d6c5fc16961bd103"}, "time": "2023-09-12T03:12:29+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "9c7e438cba4eed1796ec19ad3874defa9eb9aeac"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/9c7e438cba4eed1796ec19ad3874defa9eb9aeac", "type": "zip", "shasum": "", "reference": "9c7e438cba4eed1796ec19ad3874defa9eb9aeac"}, "time": "2023-08-06T22:54:27+00:00"}, {"version": "2.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "1ee6e6ff6bfebf712f6d3fd9f2dec859c7f05b66"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/1ee6e6ff6bfebf712f6d3fd9f2dec859c7f05b66", "type": "zip", "shasum": "", "reference": "1ee6e6ff6bfebf712f6d3fd9f2dec859c7f05b66"}, "time": "2023-01-17T04:53:50+00:00"}, {"version": "2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "1d28b2b4a9283d946089da87ff921400846d056a"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/1d28b2b4a9283d946089da87ff921400846d056a", "type": "zip", "shasum": "", "reference": "1d28b2b4a9283d946089da87ff921400846d056a"}, "time": "2022-12-12T08:45:58+00:00"}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "b5d962b7ae615ec5dc053e1d5b57d561fa9a231c"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/b5d962b7ae615ec5dc053e1d5b57d561fa9a231c", "type": "zip", "shasum": "", "reference": "b5d962b7ae615ec5dc053e1d5b57d561fa9a231c"}, "time": "2023-04-19T15:05:35+00:00"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "a21b7b633f483c4cf525d200219d200f551ee38b"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/a21b7b633f483c4cf525d200219d200f551ee38b", "type": "zip", "shasum": "", "reference": "a21b7b633f483c4cf525d200219d200f551ee38b"}, "time": "2022-11-08T02:17:04+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "1075511e200c2333793f4625829e40607a4d1cc9"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/1075511e200c2333793f4625829e40607a4d1cc9", "type": "zip", "shasum": "", "reference": "1075511e200c2333793f4625829e40607a4d1cc9"}, "time": "2022-10-21T08:22:45+00:00"}, {"homepage": "http://cakephp.org", "version": "2.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "The CakePHP Team", "homepage": "http://cakephp.org"}], "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "3ecd6e7ae191c676570cd1bed51fd561de4606dd"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/3ecd6e7ae191c676570cd1bed51fd561de4606dd", "type": "zip", "shasum": "", "reference": "3ecd6e7ae191c676570cd1bed51fd561de4606dd"}, "support": {"irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos"}, "time": "2021-10-17T02:44:05+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "556e14da67307ffc2e68beeb7df0694dc8d1207d"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/556e14da67307ffc2e68beeb7df0694dc8d1207d", "type": "zip", "shasum": "", "reference": "556e14da67307ffc2e68beeb7df0694dc8d1207d"}, "time": "2021-06-17T13:49:10+00:00"}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "1d187c71587c97520c00491f626e0f255144953e"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/1d187c71587c97520c00491f626e0f255144953e", "type": "zip", "shasum": "", "reference": "1d187c71587c97520c00491f626e0f255144953e"}, "time": "2021-04-07T01:06:46+00:00", "require-dev": {"phpunit/phpunit": "^8.0 || ^9.0", "cakephp/cakephp-codesniffer": "^4.0"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "5929b743aec34b3a2f74c70c383706f7f12e2725"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/5929b743aec34b3a2f74c70c383706f7f12e2725", "type": "zip", "shasum": "", "reference": "5929b743aec34b3a2f74c70c383706f7f12e2725"}, "time": "2021-02-06T03:37:44+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "3f119d1eb8105ed2c43bb72e8fc5c78487620089"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/3f119d1eb8105ed2c43bb72e8fc5c78487620089", "type": "zip", "shasum": "", "reference": "3f119d1eb8105ed2c43bb72e8fc5c78487620089"}, "time": "2021-01-16T14:35:30+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "30baea51824076719921c6c2d720bfd6b49e6dca"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/30baea51824076719921c6c2d720bfd6b49e6dca", "type": "zip", "shasum": "", "reference": "30baea51824076719921c6c2d720bfd6b49e6dca"}, "time": "2020-08-22T02:42:12+00:00", "require-dev": {"phpunit/phpunit": "^8.0", "cakephp/cakephp-codesniffer": "^4.0", "phpbench/phpbench": "^1.0@dev"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "9309d85c33c65917c0e582c0a6b07df56fe27dd9"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/9309d85c33c65917c0e582c0a6b07df56fe27dd9", "type": "zip", "shasum": "", "reference": "9309d85c33c65917c0e582c0a6b07df56fe27dd9"}, "time": "2020-05-26T01:27:20+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "7e77b7d43e23b78363b6a1dc592de1c3d557347b"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/7e77b7d43e23b78363b6a1dc592de1c3d557347b", "type": "zip", "shasum": "", "reference": "7e77b7d43e23b78363b6a1dc592de1c3d557347b"}, "time": "2020-04-21T14:14:05+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "0b176c8e8abb18b552b789f60250188a74d534ab"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/0b176c8e8abb18b552b789f60250188a74d534ab", "type": "zip", "shasum": "", "reference": "0b176c8e8abb18b552b789f60250188a74d534ab"}, "time": "2020-02-26T20:24:12+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "e1044d27cabf12d19097b436001aa96a3c2e4b0a"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/e1044d27cabf12d19097b436001aa96a3c2e4b0a", "type": "zip", "shasum": "", "reference": "e1044d27cabf12d19097b436001aa96a3c2e4b0a"}, "time": "2020-02-08T02:38:43+00:00", "funding": "__unset"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "779054d4c7ca88fc086b2cdd1f02aaf0df9ccb01"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/779054d4c7ca88fc086b2cdd1f02aaf0df9ccb01", "type": "zip", "shasum": "", "reference": "779054d4c7ca88fc086b2cdd1f02aaf0df9ccb01"}, "time": "2019-12-01T01:32:36+00:00", "require-dev": {"phpunit/phpunit": "^8.0", "cakephp/cakephp-codesniffer": "dev-next", "phpbench/phpbench": "^1.0@dev"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "f6bbc15b4f333f16361125bda95b839e9bbc3a92"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/f6bbc15b4f333f16361125bda95b839e9bbc3a92", "type": "zip", "shasum": "", "reference": "f6bbc15b4f333f16361125bda95b839e9bbc3a92"}, "time": "2019-11-30T02:35:42+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "ba2bab98849e7bf29b02dd634ada49ab36472959"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/ba2bab98849e7bf29b02dd634ada49ab36472959", "type": "zip", "shasum": "", "reference": "ba2bab98849e7bf29b02dd634ada49ab36472959"}, "time": "2019-11-30T02:33:19+00:00", "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "<6.0 || ^7.0", "athletic/athletic": "~0.1", "cakephp/cakephp-codesniffer": "^3.0", "phpbench/phpbench": "@dev"}}, {"version": "1.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "0292f06e8cc23fc82f0574889da2d8bf27b613c1"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/0292f06e8cc23fc82f0574889da2d8bf27b613c1", "type": "zip", "shasum": "", "reference": "0292f06e8cc23fc82f0574889da2d8bf27b613c1"}, "time": "2019-06-17T15:19:18+00:00", "require": {"php": "^5.5.9|^7"}, "require-dev": {"phpunit/phpunit": "<6.0 || ^7.0", "athletic/athletic": "~0.1", "cakephp/cakephp-codesniffer": "^3.0", "phpbench/phpbench": "@dev", "phpstan/phpstan": "^0.6.4"}}, {"version": "1.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "c981a911149151d5eceefccf1980f4554fc42619"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/c981a911149151d5eceefccf1980f4554fc42619", "type": "zip", "shasum": "", "reference": "c981a911149151d5eceefccf1980f4554fc42619"}, "time": "2019-06-06T07:08:18+00:00"}, {"version": "1.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "5af476617f8a2919a829b92df4f8c3c62e23f4c5"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/5af476617f8a2919a829b92df4f8c3c62e23f4c5", "type": "zip", "shasum": "", "reference": "5af476617f8a2919a829b92df4f8c3c62e23f4c5"}, "time": "2019-05-30T13:29:03+00:00"}, {"version": "1.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "8a2b005a2db173e1b5493002afb8e1e13c71a62a"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/8a2b005a2db173e1b5493002afb8e1e13c71a62a", "type": "zip", "shasum": "", "reference": "8a2b005a2db173e1b5493002afb8e1e13c71a62a"}, "time": "2019-04-23T19:00:57+00:00"}, {"version": "1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "ebda7326d4a65e53bc5bb915ebbbeee98f1926b0"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/ebda7326d4a65e53bc5bb915ebbbeee98f1926b0", "type": "zip", "shasum": "", "reference": "ebda7326d4a65e53bc5bb915ebbbeee98f1926b0"}, "time": "2019-02-11T02:08:31+00:00"}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "395110125ff577f080fa064dca5c5608a4e77ee1"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/395110125ff577f080fa064dca5c5608a4e77ee1", "type": "zip", "shasum": "", "reference": "395110125ff577f080fa064dca5c5608a4e77ee1"}, "time": "2018-10-18T22:02:21+00:00"}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "30f5b26bcf76a5e53ecc274700ad1ec49dc05567"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/30f5b26bcf76a5e53ecc274700ad1ec49dc05567", "type": "zip", "shasum": "", "reference": "30f5b26bcf76a5e53ecc274700ad1ec49dc05567"}, "time": "2018-07-11T18:51:56+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "243bb7bc3411b549e190703dbef49a96be5b4233"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/243bb7bc3411b549e190703dbef49a96be5b4233", "type": "zip", "shasum": "", "reference": "243bb7bc3411b549e190703dbef49a96be5b4233"}, "time": "2018-06-23T01:51:58+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "a0293d2cd157d1b47844f882a65a9cba11a72c72"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/a0293d2cd157d1b47844f882a65a9cba11a72c72", "type": "zip", "shasum": "", "reference": "a0293d2cd157d1b47844f882a65a9cba11a72c72"}, "time": "2018-06-21T02:02:26+00:00"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "85bcaea6a832684b32ef54b2487b0c14a172e9e6"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/85bcaea6a832684b32ef54b2487b0c14a172e9e6", "type": "zip", "shasum": "", "reference": "85bcaea6a832684b32ef54b2487b0c14a172e9e6"}, "time": "2018-01-13T12:19:50+00:00", "autoload": {"files": ["src/carbon_compat.php"], "psr-4": {"Cake\\Chronos\\": "src"}}, "require-dev": {"phpunit/phpunit": "<6.0", "athletic/athletic": "~0.1", "cakephp/cakephp-codesniffer": "~2.3", "phpbench/phpbench": "@dev", "phpstan/phpstan": "^0.6.4"}}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "56d98330d366a469745848b07540373846c40561"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/56d98330d366a469745848b07540373846c40561", "type": "zip", "shasum": "", "reference": "56d98330d366a469745848b07540373846c40561"}, "time": "2017-12-25T22:42:18+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "0621b191334d8dcb56907688986dd24eb8c38234"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/0621b191334d8dcb56907688986dd24eb8c38234", "type": "zip", "shasum": "", "reference": "0621b191334d8dcb56907688986dd24eb8c38234"}, "time": "2017-04-27T01:27:49+00:00", "require": {"php": ">=5.5.9"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "26385a9a2966350f7c5df34a6a54e641a538131e"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/26385a9a2966350f7c5df34a6a54e641a538131e", "type": "zip", "shasum": "", "reference": "26385a9a2966350f7c5df34a6a54e641a538131e"}, "time": "2017-03-31T14:53:04+00:00", "require-dev": {"phpunit/phpunit": "<6.0", "athletic/athletic": "~0.1", "cakephp/cakephp-codesniffer": "~2.3"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "5146ab942a4c3aea06789da5f3fa5e601c9dc6df"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/5146ab942a4c3aea06789da5f3fa5e601c9dc6df", "type": "zip", "shasum": "", "reference": "5146ab942a4c3aea06789da5f3fa5e601c9dc6df"}, "time": "2017-02-15T08:51:03+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "16753ad209f5b478dd35caadcd609104762fc157"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/16753ad209f5b478dd35caadcd609104762fc157", "type": "zip", "shasum": "", "reference": "16753ad209f5b478dd35caadcd609104762fc157"}, "time": "2017-01-09T02:11:45+00:00", "require-dev": {"phpunit/phpunit": "*", "athletic/athletic": "~0.1", "cakephp/cakephp-codesniffer": "dev-master"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "016e7bcc080dbf67baf71a8646efb1d8c50389e9"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/016e7bcc080dbf67baf71a8646efb1d8c50389e9", "type": "zip", "shasum": "", "reference": "016e7bcc080dbf67baf71a8646efb1d8c50389e9"}, "time": "2016-12-21T21:09:39+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "e08b87dba94a4a8d46cda6529806674ec55c9e6c"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/e08b87dba94a4a8d46cda6529806674ec55c9e6c", "type": "zip", "shasum": "", "reference": "e08b87dba94a4a8d46cda6529806674ec55c9e6c"}, "time": "2016-12-09T06:28:06+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "ea73846325017e03ea796ad96e2c529d35e936b6"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/ea73846325017e03ea796ad96e2c529d35e936b6", "type": "zip", "shasum": "", "reference": "ea73846325017e03ea796ad96e2c529d35e936b6"}, "time": "2016-11-15T14:52:59+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "a40b330d84a74975119c10a4e890391e0611e5ef"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/a40b330d84a74975119c10a4e890391e0611e5ef", "type": "zip", "shasum": "", "reference": "a40b330d84a74975119c10a4e890391e0611e5ef"}, "time": "2016-07-14T05:16:14+00:00"}, {"version": "0.4.11", "version_normalized": "********", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "39db65d38488d6edd88bab5f33e19b1205ceeeda"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/39db65d38488d6edd88bab5f33e19b1205ceeeda", "type": "zip", "shasum": "", "reference": "39db65d38488d6edd88bab5f33e19b1205ceeeda"}, "time": "2016-06-15T06:26:37+00:00"}, {"version": "0.4.10", "version_normalized": "********", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "7281524a286702037a330948935b60b202a6ce09"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/7281524a286702037a330948935b60b202a6ce09", "type": "zip", "shasum": "", "reference": "7281524a286702037a330948935b60b202a6ce09"}, "time": "2016-06-14T14:53:34+00:00"}, {"version": "0.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "b29a9b3cc8ae2f35d4ca9a32bc4bef001d6ee125"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/b29a9b3cc8ae2f35d4ca9a32bc4bef001d6ee125", "type": "zip", "shasum": "", "reference": "b29a9b3cc8ae2f35d4ca9a32bc4bef001d6ee125"}, "time": "2016-04-16T18:14:39+00:00"}, {"version": "0.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "aef0ba4ffcd1d962dc66b4d51b0d111db089cfcf"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/aef0ba4ffcd1d962dc66b4d51b0d111db089cfcf", "type": "zip", "shasum": "", "reference": "aef0ba4ffcd1d962dc66b4d51b0d111db089cfcf"}, "time": "2016-03-10T01:50:25+00:00"}, {"version": "0.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "8f90c9b8cc813be21fb07ac487dc90ddbdf2fb0b"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/8f90c9b8cc813be21fb07ac487dc90ddbdf2fb0b", "type": "zip", "shasum": "", "reference": "8f90c9b8cc813be21fb07ac487dc90ddbdf2fb0b"}, "time": "2016-02-14T20:36:45+00:00"}, {"version": "0.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "14c54f91e35bbdb9e70b18530c6de930a474cd42"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/14c54f91e35bbdb9e70b18530c6de930a474cd42", "type": "zip", "shasum": "", "reference": "14c54f91e35bbdb9e70b18530c6de930a474cd42"}, "time": "2016-02-07T15:48:49+00:00"}, {"version": "0.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "f29f6e746954850a9c2b7862f15b591271b0a9e0"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/f29f6e746954850a9c2b7862f15b591271b0a9e0", "type": "zip", "shasum": "", "reference": "f29f6e746954850a9c2b7862f15b591271b0a9e0"}, "time": "2016-01-02T12:05:36+00:00", "require": {"php": ">=5.5.10"}}, {"version": "0.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "e45b3c152c6fe969d09b6a1ed18445b6ab4239e2"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/e45b3c152c6fe969d09b6a1ed18445b6ab4239e2", "type": "zip", "shasum": "", "reference": "e45b3c152c6fe969d09b6a1ed18445b6ab4239e2"}, "time": "2016-01-01T13:27:56+00:00", "require": {"php": ">=5.5.8"}, "require-dev": {"phpunit/phpunit": "~4.0", "athletic/athletic": "~0.1", "cakephp/cakephp-codesniffer": "dev-master"}}, {"version": "0.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "99ed157c932eaa1c176e7b16de045ade8daf70ea"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/99ed157c932eaa1c176e7b16de045ade8daf70ea", "type": "zip", "shasum": "", "reference": "99ed157c932eaa1c176e7b16de045ade8daf70ea"}, "time": "2015-12-06T18:21:41+00:00"}, {"version": "0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "4b0d3748ea5ce5ac603118a08cf2fbe8dd295696"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/4b0d3748ea5ce5ac603118a08cf2fbe8dd295696", "type": "zip", "shasum": "", "reference": "4b0d3748ea5ce5ac603118a08cf2fbe8dd295696"}, "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos/tree/0.4.2"}, "time": "2015-11-22T13:46:53+00:00", "require": {"php": ">=5.5.0"}}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "3e2a7f5e4f0af05a63b8839d3db646251cab1b9e"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/3e2a7f5e4f0af05a63b8839d3db646251cab1b9e", "type": "zip", "shasum": "", "reference": "3e2a7f5e4f0af05a63b8839d3db646251cab1b9e"}, "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos/tree/0.4.1"}, "time": "2015-11-17T02:58:27+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "e1d13dceeb9ead268520082296dfa234244b9c97"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/e1d13dceeb9ead268520082296dfa234244b9c97", "type": "zip", "shasum": "", "reference": "e1d13dceeb9ead268520082296dfa234244b9c97"}, "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos/tree/0.4.0"}, "time": "2015-11-16T07:12:12+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "7e8f43c382d28a0786275886f2af9e5fd6dcb975"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/7e8f43c382d28a0786275886f2af9e5fd6dcb975", "type": "zip", "shasum": "", "reference": "7e8f43c382d28a0786275886f2af9e5fd6dcb975"}, "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos/tree/0.3.0"}, "time": "2015-10-29T05:39:57+00:00", "autoload": {"psr-4": {"Cake\\Chronos\\": "src"}}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "f11ac8885f2f19ffcc7c5d811b4ac3029cb7689d"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/f11ac8885f2f19ffcc7c5d811b4ac3029cb7689d", "type": "zip", "shasum": "", "reference": "f11ac8885f2f19ffcc7c5d811b4ac3029cb7689d"}, "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos/tree/0.2.0"}, "time": "2015-10-26T20:02:48+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/cakephp/chronos.git", "type": "git", "reference": "b9a47e2e47207e3177d2e5355d281cf7a3db6d69"}, "dist": {"url": "https://api.github.com/repos/cakephp/chronos/zipball/b9a47e2e47207e3177d2e5355d281cf7a3db6d69", "type": "zip", "shasum": "", "reference": "b9a47e2e47207e3177d2e5355d281cf7a3db6d69"}, "support": {"issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos/tree/0.1.0"}, "time": "2015-10-12T02:25:06+00:00", "require-dev": {"phpunit/phpunit": "~4.0", "cakephp/cakephp-codesniffer": "dev-master"}}]}, "security-advisories": [], "last-modified": "Tue, 06 Aug 2024 17:17:37 GMT"}