{"minified": "composer/2.0", "packages": {"fakerphp/faker": [{"name": "fakerphp/faker", "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["faker", "fixtures", "data"], "homepage": "", "version": "v1.24.1", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "type": "zip", "shasum": "", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "type": "library", "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "funding": [], "time": "2024-11-21T13:46:39+00:00", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "require-dev": {"ext-intl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality.", "doctrine/orm": "Required to use Faker\\ORM\\Doctrine"}, "conflict": {"fzaninotto/faker": "*"}}, {"version": "v1.24.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "a136842a532bac9ecd8a1c723852b09915d7db50"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/a136842a532bac9ecd8a1c723852b09915d7db50", "type": "zip", "shasum": "", "reference": "a136842a532bac9ecd8a1c723852b09915d7db50"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.0"}, "time": "2024-11-07T15:11:20+00:00"}, {"version": "v1.23.1", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/bfb4fe148adbf78eff521199619b93a52ae3554b", "type": "zip", "shasum": "", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.23.1"}, "time": "2024-01-02T13:46:09+00:00"}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "e3daa170d00fde61ea7719ef47bb09bb8f1d9b01"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e3daa170d00fde61ea7719ef47bb09bb8f1d9b01", "type": "zip", "shasum": "", "reference": "e3daa170d00fde61ea7719ef47bb09bb8f1d9b01"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.23.0"}, "time": "2023-06-12T08:44:38+00:00", "extra": {"branch-alias": {"dev-main": "v1.21-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "f85772abd508bd04e20bb4b1bbe260a68d0066d2"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/f85772abd508bd04e20bb4b1bbe260a68d0066d2", "type": "zip", "shasum": "", "reference": "f85772abd508bd04e20bb4b1bbe260a68d0066d2"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.22.0"}, "time": "2023-05-14T12:31:37+00:00"}, {"version": "v1.21.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "92efad6a967f0b79c499705c69b662f738cc9e4d"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/92efad6a967f0b79c499705c69b662f738cc9e4d", "type": "zip", "shasum": "", "reference": "92efad6a967f0b79c499705c69b662f738cc9e4d"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.21.0"}, "time": "2022-12-13T13:54:32+00:00"}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "37f751c67a5372d4e26353bd9384bc03744ec77b"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/37f751c67a5372d4e26353bd9384bc03744ec77b", "type": "zip", "shasum": "", "reference": "37f751c67a5372d4e26353bd9384bc03744ec77b"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.20.0"}, "time": "2022-07-20T13:12:54+00:00", "extra": {"branch-alias": {"dev-main": "v1.20-dev"}}, "require": {"php": "^7.1 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "require-dev": {"ext-intl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "symfony/phpunit-bridge": "^4.4 || ^5.2"}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "d7f08a622b3346766325488aa32ddc93ccdecc75"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/d7f08a622b3346766325488aa32ddc93ccdecc75", "type": "zip", "shasum": "", "reference": "d7f08a622b3346766325488aa32ddc93ccdecc75"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.19.0"}, "time": "2022-02-02T17:38:57+00:00", "extra": {"branch-alias": {"dev-main": "v1.19-dev"}}}, {"version": "v1.18.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "2e77a868f6540695cf5ebf21e5ab472c65f47567"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/2e77a868f6540695cf5ebf21e5ab472c65f47567", "type": "zip", "shasum": "", "reference": "2e77a868f6540695cf5ebf21e5ab472c65f47567"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.18.0"}, "time": "2022-01-23T17:56:23+00:00", "extra": {"branch-alias": {"dev-main": "v1.18-dev"}}, "require-dev": {"ext-intl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "symfony/phpunit-bridge": "^4.4 || ^5.2"}, "suggest": {"ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "b85e9d44eae8c52cca7aa0939483611f7232b669"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/b85e9d44eae8c52cca7aa0939483611f7232b669", "type": "zip", "shasum": "", "reference": "b85e9d44eae8c52cca7aa0939483611f7232b669"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.17.0"}, "time": "2021-12-05T17:14:47+00:00", "extra": {"branch-alias": {"dev-main": "v1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "271d384d216e5e5c468a6b28feedf95d49f83b35"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/271d384d216e5e5c468a6b28feedf95d49f83b35", "type": "zip", "shasum": "", "reference": "271d384d216e5e5c468a6b28feedf95d49f83b35"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.16.0"}, "time": "2021-09-06T14:53:37+00:00", "extra": {"branch-alias": {"dev-main": "v1.16-dev"}}, "require": {"php": "^7.1 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2"}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "89c6201c74db25fa759ff16e78a4d8f32547770e"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/89c6201c74db25fa759ff16e78a4d8f32547770e", "type": "zip", "shasum": "", "reference": "89c6201c74db25fa759ff16e78a4d8f32547770e"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.15.0"}, "time": "2021-07-06T20:39:40+00:00", "extra": {"branch-alias": {"dev-main": "v1.15-dev"}}, "require": {"php": "^7.1 || ^8.0", "psr/container": "^1.0", "symfony/deprecation-contracts": "^2.2"}}, {"version": "v1.14.1", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "ed22aee8d17c7b396f74a58b1e7fefa4f90d5ef1"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/ed22aee8d17c7b396f74a58b1e7fefa4f90d5ef1", "type": "zip", "shasum": "", "reference": "ed22aee8d17c7b396f74a58b1e7fefa4f90d5ef1"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v.1.14.1"}, "time": "2021-03-30T06:27:33+00:00"}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "cd46398f42c2ab191ffb0b57c07e911c7c28eb25"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/cd46398f42c2ab191ffb0b57c07e911c7c28eb25", "type": "zip", "shasum": "", "reference": "cd46398f42c2ab191ffb0b57c07e911c7c28eb25"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.14.0"}, "time": "2021-03-29T08:56:34+00:00", "extra": "__unset"}, {"version": "v1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "ab3f5364d01f2c2c16113442fb987d26e4004913"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/ab3f5364d01f2c2c16113442fb987d26e4004913", "type": "zip", "shasum": "", "reference": "ab3f5364d01f2c2c16113442fb987d26e4004913"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.13.0"}, "time": "2020-12-18T16:50:48+00:00", "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"ext-intl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.4.2"}, "suggest": "__unset"}, {"version": "v1.12.1", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "841e8bdde345cc1ea9f98e776959e7531cadea0e"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/841e8bdde345cc1ea9f98e776959e7531cadea0e", "type": "zip", "shasum": "", "reference": "841e8bdde345cc1ea9f98e776959e7531cadea0e"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.12.1"}, "time": "2020-12-11T10:39:41+00:00"}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "9aa6c9e289860951e6b4d010c7a841802d015cd8"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/9aa6c9e289860951e6b4d010c7a841802d015cd8", "type": "zip", "shasum": "", "reference": "9aa6c9e289860951e6b4d010c7a841802d015cd8"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.12.0"}, "time": "2020-11-23T09:33:08+00:00"}, {"version": "1.11.3", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "e38025a9afed4a581e4aef422fb62c6333097e5a"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e38025a9afed4a581e4aef422fb62c6333097e5a", "type": "zip", "shasum": "", "reference": "e38025a9afed4a581e4aef422fb62c6333097e5a"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/1.11.3"}, "time": "2020-10-28T15:44:31+00:00", "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.4.2"}}, {"version": "1.11.2", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "68a167242c831a46dec9df1d995506496cc16d48"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/68a167242c831a46dec9df1d995506496cc16d48", "type": "zip", "shasum": "", "reference": "68a167242c831a46dec9df1d995506496cc16d48"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/1.11.2"}, "time": "2020-10-28T15:42:38+00:00"}, {"version": "1.11.1", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "2d2bc8c4ef510df6c28219e734668b52a8fbdc51"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/2d2bc8c4ef510df6c28219e734668b52a8fbdc51", "type": "zip", "shasum": "", "reference": "2d2bc8c4ef510df6c28219e734668b52a8fbdc51"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/1.11.1"}, "time": "2020-10-28T15:32:16+00:00"}, {"version": "v1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "f228dc5112bafc14c77d40a2acc0c48058e184b0"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/f228dc5112bafc14c77d40a2acc0c48058e184b0", "type": "zip", "shasum": "", "reference": "f228dc5112bafc14c77d40a2acc0c48058e184b0"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.11.0"}, "time": "2020-11-15T20:27:00+00:00", "require-dev": {"ext-intl": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.4.2"}}, {"version": "v1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "f2713a5016faaf6ffc60e9d456dedb5ebf0efcae"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/f2713a5016faaf6ffc60e9d456dedb5ebf0efcae", "type": "zip", "shasum": "", "reference": "f2713a5016faaf6ffc60e9d456dedb5ebf0efcae"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.10.1"}, "time": "2020-10-28T09:32:46+00:00", "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.4.2"}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "7ef0c9fda7b576e8d01d6cb1c7b28f12286bb217"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/7ef0c9fda7b576e8d01d6cb1c7b28f12286bb217", "type": "zip", "shasum": "", "reference": "7ef0c9fda7b576e8d01d6cb1c7b28f12286bb217"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.10.0"}, "time": "2020-10-27T13:44:20+00:00", "conflict": {"fzaninotto/faker": "*", "ergebnis/faker": "*"}}, {"version": "v1.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "84220cf137a9344acffb10374e781fed785ff307"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/84220cf137a9344acffb10374e781fed785ff307", "type": "zip", "shasum": "", "reference": "84220cf137a9344acffb10374e781fed785ff307"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.9.2"}, "time": "2020-12-11T14:32:13+00:00", "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "conflict": {"fzaninotto/faker": "!=1.9.2", "ergebnis/faker": "!=1.9.2"}, "replace": {"fzaninotto/faker": "1.9.2", "ergebnis/faker": "1.9.2"}}, {"version": "v1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "b07d15b7f196f2eba51e70ac1bd7352918c8bf2f"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/b07d15b7f196f2eba51e70ac1bd7352918c8bf2f", "type": "zip", "shasum": "", "reference": "b07d15b7f196f2eba51e70ac1bd7352918c8bf2f"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.9.1"}, "time": "2020-10-27T12:08:29+00:00", "conflict": {"fzaninotto/faker": "!=1.9.1", "ergebnis/faker": "!=1.9.1"}, "replace": {"fzaninotto/faker": "1.9.1", "ergebnis/faker": "1.9.1"}}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "27a216cbe72327b2d6369fab721a5843be71e57d"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/27a216cbe72327b2d6369fab721a5843be71e57d", "type": "zip", "shasum": "", "reference": "27a216cbe72327b2d6369fab721a5843be71e57d"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.9.0"}, "time": "2019-11-14T13:13:06+00:00", "extra": {"branch-alias": []}, "conflict": "__unset", "replace": "__unset"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/f72816b43e74063c8b10357394b6bba8cb1c10de", "type": "zip", "shasum": "", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.8.0"}, "time": "2018-07-12T10:23:15+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^1.5", "ext-intl": "*"}}, {"version": "v1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/d3ed4cc37051c1ca52d22d76b437d14809fc7e0d", "type": "zip", "shasum": "", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.7.1"}, "time": "2017-08-15T16:48:10+00:00", "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0", "squizlabs/php_codesniffer": "^1.5", "ext-intl": "*"}}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "dd4564efb00c557559af29c2964fba764d0dfacc"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/dd4564efb00c557559af29c2964fba764d0dfacc", "type": "zip", "shasum": "", "reference": "dd4564efb00c557559af29c2964fba764d0dfacc"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.7.0"}, "time": "2017-08-15T16:34:15+00:00", "extra": {"branch-alias": []}}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "d0190b156bcca848d401fb80f31f504f37141c8d"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/d0190b156bcca848d401fb80f31f504f37141c8d", "type": "zip", "shasum": "", "reference": "d0190b156bcca848d401fb80f31f504f37141c8d"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.5.0"}, "time": "2015-05-29T06:29:14+00:00", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "suggest": {"ext-intl": "*"}}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "010c7efedd88bf31141a02719f51fb44c732d5a0"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/010c7efedd88bf31141a02719f51fb44c732d5a0", "type": "zip", "shasum": "", "reference": "010c7efedd88bf31141a02719f51fb44c732d5a0"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.4.0"}, "time": "2014-06-04T14:43:02+00:00", "autoload": {"psr-0": {"Faker": "src/", "Faker\\PHPUnit": "test/"}}, "extra": {"branch-alias": []}, "suggest": "__unset"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "1d143fd8caf4d264602450bc01d7484af788706b"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/1d143fd8caf4d264602450bc01d7484af788706b", "type": "zip", "shasum": "", "reference": "1d143fd8caf4d264602450bc01d7484af788706b"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.3.0"}, "time": "2013-12-16T21:56:48+00:00", "extra": {"branch-alias": {"dev-master": "1.3.0-dev"}}, "require-dev": "__unset"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "4ad4bc4b5c8d3c0f3cf55d2fedc2f65b313ec62f"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/4ad4bc4b5c8d3c0f3cf55d2fedc2f65b313ec62f", "type": "zip", "shasum": "", "reference": "4ad4bc4b5c8d3c0f3cf55d2fedc2f65b313ec62f"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.2.0"}, "time": "2013-06-09T18:05:57+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "3bf14b6cb8b98be3a3a3f9fed66ecb2517bf87ac"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/3bf14b6cb8b98be3a3a3f9fed66ecb2517bf87ac", "type": "zip", "shasum": "", "reference": "3bf14b6cb8b98be3a3a3f9fed66ecb2517bf87ac"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.1.0"}, "time": "2012-10-29T09:32:06+00:00", "autoload": {"psr-0": {"Faker": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/FakerPHP/Faker.git", "type": "git", "reference": "f3d296e385dd4a46a5ddf507539a6c150c32983e"}, "dist": {"url": "https://api.github.com/repos/FakerPHP/Faker/zipball/f3d296e385dd4a46a5ddf507539a6c150c32983e", "type": "zip", "shasum": "", "reference": "f3d296e385dd4a46a5ddf507539a6c150c32983e"}, "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.0.0"}, "time": "2012-07-10T14:43:46+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 21 Nov 2024 13:47:19 GMT"}