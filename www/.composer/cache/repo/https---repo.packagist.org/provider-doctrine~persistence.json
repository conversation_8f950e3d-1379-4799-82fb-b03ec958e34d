{"minified": "composer/2.0", "packages": {"doctrine/persistence": [{"name": "doctrine/persistence", "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "keywords": ["orm", "persistence", "object", "odm", "mapper"], "homepage": "https://www.doctrine-project.org/projects/persistence.html", "version": "4.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "45004aca79189474f113cbe3a53847c2115a55fa"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/45004aca79189474f113cbe3a53847c2115a55fa", "type": "zip", "shasum": "", "reference": "45004aca79189474f113cbe3a53847c2115a55fa"}, "type": "library", "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/4.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-11-01T21:49:07+00:00", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "require": {"php": "^8.1", "doctrine/event-manager": "^1 || ^2", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/coding-standard": "^12", "phpunit/phpunit": "^9.6", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "conflict": {"doctrine/common": "<2.10"}}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "0ea965320cec355dba75031c1b23d4c78362e3ff"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/0ea965320cec355dba75031c1b23d4c78362e3ff", "type": "zip", "shasum": "", "reference": "0ea965320cec355dba75031c1b23d4c78362e3ff"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.4.0"}, "time": "2024-10-30T19:48:12+00:00", "require": {"php": "^7.2 || ^8.0", "doctrine/event-manager": "^1 || ^2", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/coding-standard": "^12", "doctrine/common": "^3.0", "phpunit/phpunit": "^8.5.38 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}}, {"version": "3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "b337726451f5d530df338fc7f68dee8781b49779"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/b337726451f5d530df338fc7f68dee8781b49779", "type": "zip", "shasum": "", "reference": "b337726451f5d530df338fc7f68dee8781b49779"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.3.3"}, "time": "2024-06-20T10:14:30+00:00", "require-dev": {"phpstan/phpstan": "1.11.1", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/coding-standard": "^12", "doctrine/common": "^3.0", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.30.0 || 5.24.0"}}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "477da35bd0255e032826f440b94b3e37f2d56f42"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/477da35bd0255e032826f440b94b3e37f2d56f42", "type": "zip", "shasum": "", "reference": "477da35bd0255e032826f440b94b3e37f2d56f42"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.3.2"}, "time": "2024-03-12T14:54:36+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "1.9.4", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/coding-standard": "^11", "doctrine/common": "^3.0", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.30.0 || 5.3.0"}}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "b6fd1f126b13c1f7e7321f7338b14a19116b5de4"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/b6fd1f126b13c1f7e7321f7338b14a19116b5de4", "type": "zip", "shasum": "", "reference": "b6fd1f126b13c1f7e7321f7338b14a19116b5de4"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.3.1"}, "time": "2024-03-01T19:53:13+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "f75d11b1bcd0a9b75a9e1cabd80ffe3bc0164bcc"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/f75d11b1bcd0a9b75a9e1cabd80ffe3bc0164bcc", "type": "zip", "shasum": "", "reference": "f75d11b1bcd0a9b75a9e1cabd80ffe3bc0164bcc"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.3.0"}, "time": "2024-03-01T10:11:31+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "63fee8c33bef740db6730eb2a750cd3da6495603"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/63fee8c33bef740db6730eb2a750cd3da6495603", "type": "zip", "shasum": "", "reference": "63fee8c33bef740db6730eb2a750cd3da6495603"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.2.0"}, "time": "2023-05-17T18:32:04+00:00"}, {"version": "3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "8bf8ab15960787f1a49d405f6eb8c787b4841119"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/8bf8ab15960787f1a49d405f6eb8c787b4841119", "type": "zip", "shasum": "", "reference": "8bf8ab15960787f1a49d405f6eb8c787b4841119"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.1.4"}, "time": "2023-02-03T11:13:07+00:00"}, {"version": "3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "920da294b4bb0bb527f2a91ed60c18213435880f"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/920da294b4bb0bb527f2a91ed60c18213435880f", "type": "zip", "shasum": "", "reference": "920da294b4bb0bb527f2a91ed60c18213435880f"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.1.3"}, "time": "2023-01-19T13:39:42+00:00"}, {"version": "3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "b44d128311af55275dbed6a4558ca59a2b9f9387"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/b44d128311af55275dbed6a4558ca59a2b9f9387", "type": "zip", "shasum": "", "reference": "b44d128311af55275dbed6a4558ca59a2b9f9387"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.1.2"}, "time": "2022-12-19T13:58:18+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "d87426f07dd66f97cfdcf5210925e483b6c993b5"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/d87426f07dd66f97cfdcf5210925e483b6c993b5", "type": "zip", "shasum": "", "reference": "d87426f07dd66f97cfdcf5210925e483b6c993b5"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.1.1"}, "time": "2022-12-13T21:21:05+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "1.8.8", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/annotations": "^1.7", "doctrine/coding-standard": "^10", "doctrine/common": "^3.0", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.29.0"}, "conflict": {"doctrine/annotations": "<1.7 || >=2.0", "doctrine/common": "<2.10"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "2a9c70a5e21f8968c5a46b79f819ea52f322080b"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/2a9c70a5e21f8968c5a46b79f819ea52f322080b", "type": "zip", "shasum": "", "reference": "2a9c70a5e21f8968c5a46b79f819ea52f322080b"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.1.0"}, "time": "2022-11-18T14:10:19+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "05612da375f8a3931161f435f91d6704926e6ec5"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/05612da375f8a3931161f435f91d6704926e6ec5", "type": "zip", "shasum": "", "reference": "05612da375f8a3931161f435f91d6704926e6ec5"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.0.4"}, "time": "2022-10-13T07:34:14+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "ac6fce61f037d7e54dbb2435f5b5648d86548e23"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/ac6fce61f037d7e54dbb2435f5b5648d86548e23", "type": "zip", "shasum": "", "reference": "ac6fce61f037d7e54dbb2435f5b5648d86548e23"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.0.3"}, "time": "2022-08-04T21:14:21+00:00", "require": {"php": "^7.2 || ^8.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "1.5.0", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/annotations": "^1.7", "doctrine/coding-standard": "^9.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.22.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "25ec98a8cbd1f850e60fdb62c0ef77c162da8704"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/25ec98a8cbd1f850e60fdb62c0ef77c162da8704", "type": "zip", "shasum": "", "reference": "25ec98a8cbd1f850e60fdb62c0ef77c162da8704"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.0.2"}, "time": "2022-05-06T06:10:05+00:00", "require": {"php": "^7.2 || ^8.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "b7f12af9973c3861ca5489dd065c85fb2b99436c"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/b7f12af9973c3861ca5489dd065c85fb2b99436c", "type": "zip", "shasum": "", "reference": "b7f12af9973c3861ca5489dd065c85fb2b99436c"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.0.1"}, "time": "2022-05-02T17:41:58+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "e6103cb9e0982a4312e05ad94bb7be3f8e2d5869"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/e6103cb9e0982a4312e05ad94bb7be3f8e2d5869", "type": "zip", "shasum": "", "reference": "e6103cb9e0982a4312e05ad94bb7be3f8e2d5869"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.0.0"}, "time": "2022-04-15T10:39:55+00:00"}, {"homepage": "https://doctrine-project.org/projects/persistence.html", "version": "2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "e36f22765f4d10a7748228babbf73da5edfeed3c"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/e36f22765f4d10a7748228babbf73da5edfeed3c", "type": "zip", "shasum": "", "reference": "e36f22765f4d10a7748228babbf73da5edfeed3c"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.7"}, "time": "2023-02-03T15:51:16+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "src/Common", "Doctrine\\Persistence\\": "src/Persistence"}}, "require": {"php": "^7.1 || ^8.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1 || ^2", "psr/cache": "^1.0 || ^2.0 || ^3.0", "doctrine/deprecations": "^0.5.3 || ^1"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "~1.4.10 || 1.9.4", "doctrine/annotations": "^1 || ^2", "doctrine/coding-standard": "^9 || ^11", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.30.0 || 5.3.0"}, "conflict": {"doctrine/annotations": "<1.0 || >=3.0", "doctrine/common": "<2.10"}}, {"version": "2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "7729fc2a7e5efc8bbfa408a3b8adeb8f5b84f5d1"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/7729fc2a7e5efc8bbfa408a3b8adeb8f5b84f5d1", "type": "zip", "shasum": "", "reference": "7729fc2a7e5efc8bbfa408a3b8adeb8f5b84f5d1"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.6"}, "time": "2023-01-01T18:11:10+00:00"}, {"version": "2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "38670dd6ac8f2d997a0b5b6c98cb380ef0ba9bd3"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/38670dd6ac8f2d997a0b5b6c98cb380ef0ba9bd3", "type": "zip", "shasum": "", "reference": "38670dd6ac8f2d997a0b5b6c98cb380ef0ba9bd3"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.5"}, "time": "2022-10-13T07:17:40+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "~1.4.10 || 1.8.8", "doctrine/annotations": "^1.0", "doctrine/coding-standard": "^9 || ^10", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.29.0"}, "conflict": {"doctrine/annotations": "<1.0 || >=2.0", "doctrine/common": "<2.10"}}, {"version": "2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "830c2ba42093e0e428eca37568ab36bd8008bc17"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/830c2ba42093e0e428eca37568ab36bd8008bc17", "type": "zip", "shasum": "", "reference": "830c2ba42093e0e428eca37568ab36bd8008bc17"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.4"}, "time": "2022-08-06T22:06:57+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "doctrine/deprecations": "^0.5.3 || ^1"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "~1.4.10 || 1.5.0", "doctrine/annotations": "^1.0", "doctrine/coding-standard": "^9.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.22.0"}}, {"version": "2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "d7edf274b6d35ad82328e223439cc2bb2f92bd9e"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/d7edf274b6d35ad82328e223439cc2bb2f92bd9e", "type": "zip", "shasum": "", "reference": "d7edf274b6d35ad82328e223439cc2bb2f92bd9e"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.3"}, "time": "2022-05-03T09:16:53+00:00"}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "4276c6cbc0ca692c190f650a2678623bf04af2d2"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/4276c6cbc0ca692c190f650a2678623bf04af2d2", "type": "zip", "shasum": "", "reference": "4276c6cbc0ca692c190f650a2678623bf04af2d2"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.2"}, "time": "2022-05-02T17:29:02+00:00"}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "4473480044c88f30e0e8288e7123b60c7eb9efa3"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/4473480044c88f30e0e8288e7123b60c7eb9efa3", "type": "zip", "shasum": "", "reference": "4473480044c88f30e0e8288e7123b60c7eb9efa3"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.1"}, "funding": [], "time": "2022-04-14T21:47:17+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "doctrine/deprecations": "^0.5.3"}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "f8776dd9a0bdcd838812951a75f4ada72065a82a"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/f8776dd9a0bdcd838812951a75f4ada72065a82a", "type": "zip", "shasum": "", "reference": "f8776dd9a0bdcd838812951a75f4ada72065a82a"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.5.0"}, "time": "2022-04-06T14:59:40+00:00"}, {"version": "2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "b07e347a24e7a19a2b6462e00a6dff899e4c2dd2"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/b07e347a24e7a19a2b6462e00a6dff899e4c2dd2", "type": "zip", "shasum": "", "reference": "b07e347a24e7a19a2b6462e00a6dff899e4c2dd2"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.4.2"}, "time": "2022-03-28T11:48:21+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "092a52b71410ac1795287bb5135704ef07d18dd0"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/092a52b71410ac1795287bb5135704ef07d18dd0", "type": "zip", "shasum": "", "reference": "092a52b71410ac1795287bb5135704ef07d18dd0"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.4.1"}, "time": "2022-03-22T06:44:40+00:00", "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "1.4.6", "doctrine/annotations": "^1.0", "doctrine/coding-standard": "^9.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.21.0"}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "e7c8edcf98e819638af00e7b3cbbbd7734b9b2fb"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/e7c8edcf98e819638af00e7b3cbbbd7734b9b2fb", "type": "zip", "shasum": "", "reference": "e7c8edcf98e819638af00e7b3cbbbd7734b9b2fb"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.4.0"}, "time": "2022-03-13T16:11:46+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "f8af155c1e7963f3d2b4415097d55757bbaa53d8"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/f8af155c1e7963f3d2b4415097d55757bbaa53d8", "type": "zip", "shasum": "", "reference": "f8af155c1e7963f3d2b4415097d55757bbaa53d8"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.3.0"}, "time": "2022-01-09T19:58:46+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "1.2.0", "doctrine/annotations": "^1.0", "doctrine/coding-standard": "^6.0 || ^9.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "symfony/cache": "^4.4 || ^5.0 || ^6.0", "vimeo/psalm": "4.13.1"}, "conflict": {"doctrine/annotations": "<1.0 || >=2.0", "doctrine/common": "<2.10@dev"}}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "cdbcf72c9aa890800897e489d89aa89b76374452"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/cdbcf72c9aa890800897e489d89aa89b76374452", "type": "zip", "shasum": "", "reference": "cdbcf72c9aa890800897e489d89aa89b76374452"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.2.4"}, "time": "2022-01-09T19:54:43+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "doctrine/deprecations": "^0.5.3"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "1.2.0", "doctrine/coding-standard": "^6.0 || ^9.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "symfony/cache": "^4.4 || ^5.0 || ^6.0", "vimeo/psalm": "4.13.1"}, "conflict": {"doctrine/common": "<2.10@dev"}}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "5e7bdbbfe9811c06e1f745d1c166647d5c47d6ee"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/5e7bdbbfe9811c06e1f745d1c166647d5c47d6ee", "type": "zip", "shasum": "", "reference": "5e7bdbbfe9811c06e1f745d1c166647d5c47d6ee"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.2.3"}, "time": "2021-10-25T19:59:10+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0|^2.0|^3.0", "doctrine/deprecations": "^0.5.3"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "0.12.84", "doctrine/coding-standard": "^6.0 || ^9.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "symfony/cache": "^4.4|^5.0", "vimeo/psalm": "4.7.0"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "4ce4712e6dc84a156176a0fbbb11954a25c93103"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/4ce4712e6dc84a156176a0fbbb11954a25c93103", "type": "zip", "shasum": "", "reference": "4ce4712e6dc84a156176a0fbbb11954a25c93103"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.2.2"}, "time": "2021-08-10T19:01:29+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "d138f3ab5f761055cab1054070377cfd3222e368"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/d138f3ab5f761055cab1054070377cfd3222e368", "type": "zip", "shasum": "", "reference": "d138f3ab5f761055cab1054070377cfd3222e368"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.2.1"}, "time": "2021-05-19T07:07:01+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "08d3e6e666de85c7748fa27bfd63bb15ac29eab2"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/08d3e6e666de85c7748fa27bfd63bb15ac29eab2", "type": "zip", "shasum": "", "reference": "08d3e6e666de85c7748fa27bfd63bb15ac29eab2"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.2.0"}, "time": "2021-05-15T18:37:00+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "psr/cache": "^1.0|^2.0|^3.0"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "bac0f7fc16fc294e4c38b8763de21657dcf803f0"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/bac0f7fc16fc294e4c38b8763de21657dcf803f0", "type": "zip", "shasum": "", "reference": "bac0f7fc16fc294e4c38b8763de21657dcf803f0"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.1.1"}, "time": "2021-04-30T12:44:08+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0|^2.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "^0.12", "doctrine/coding-standard": "^6.0 || ^8.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "symfony/cache": "^4.4", "vimeo/psalm": "^4.3.1"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "9899c16934053880876b920a3b8b02ed2337ac1d"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/9899c16934053880876b920a3b8b02ed2337ac1d", "type": "zip", "shasum": "", "reference": "9899c16934053880876b920a3b8b02ed2337ac1d"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.1.0"}, "time": "2020-10-24T22:13:54+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "^0.12", "doctrine/coding-standard": "^6.0 || ^8.0", "doctrine/common": "^3.0", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "3bc796882c9f69526b7833b46ba3cd0c06b0460f"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/3bc796882c9f69526b7833b46ba3cd0c06b0460f", "type": "zip", "shasum": "", "reference": "3bc796882c9f69526b7833b46ba3cd0c06b0460f"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.0.1"}, "time": "2020-10-24T22:04:45+00:00", "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "phpstan/phpstan": "^0.12", "doctrine/coding-standard": "^6.0 || ^8.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "1dee036f22cd5dc0bc12132f1d1c38415907be55"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/1dee036f22cd5dc0bc12132f1d1c38415907be55", "type": "zip", "shasum": "", "reference": "1dee036f22cd5dc0bc12132f1d1c38415907be55"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-05-12T19:32:44+00:00", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require": {"php": "^7.1", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2"}, "require-dev": {"phpstan/phpstan": "^0.11", "doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.11"}}, {"version": "1.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "type": "zip", "shasum": "", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "time": "2020-06-20T12:56:16+00:00", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "require": {"php": "^7.1 || ^8.0", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2"}, "require-dev": {"phpstan/phpstan": "^0.11", "doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}}, {"version": "1.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0", "type": "zip", "shasum": "", "reference": "0af483f91bada1c9ded6c2cfd26ab7d5ab2094e0"}, "time": "2020-03-21T15:13:52+00:00", "require": {"php": "^7.1", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2"}, "require-dev": {"phpstan/phpstan": "^0.11", "doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}}, {"version": "1.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "5dd3ac5eebef2d0b074daa4440bb18f93132dee4"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/5dd3ac5eebef2d0b074daa4440bb18f93132dee4", "type": "zip", "shasum": "", "reference": "5dd3ac5eebef2d0b074daa4440bb18f93132dee4"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.6"}, "time": "2020-01-16T22:06:23+00:00", "require": {"php": "^7.1", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.1"}, "funding": "__unset"}, {"version": "1.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "be70c016fdcd44a428405ee062ebcdd01a6867cd"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/be70c016fdcd44a428405ee062ebcdd01a6867cd", "type": "zip", "shasum": "", "reference": "be70c016fdcd44a428405ee062ebcdd01a6867cd"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.5"}, "time": "2020-01-14T18:44:12+00:00", "require": {"php": "^7.1", "doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.0"}}, {"version": "1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "ff7e08b0f814be2cd20c52dc3c8a262579376b94"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/ff7e08b0f814be2cd20c52dc3c8a262579376b94", "type": "zip", "shasum": "", "reference": "ff7e08b0f814be2cd20c52dc3c8a262579376b94"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.4"}, "time": "2020-01-09T19:49:17+00:00"}, {"version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "99b196bbd4715a94fa100fac664a351ffa46d6a5"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/99b196bbd4715a94fa100fac664a351ffa46d6a5", "type": "zip", "shasum": "", "reference": "99b196bbd4715a94fa100fac664a351ffa46d6a5"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.3"}, "time": "2019-12-13T10:43:02+00:00"}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "cdddb0439aef0b45f22c479f936e5782ebc3f336"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/cdddb0439aef0b45f22c479f936e5782ebc3f336", "type": "zip", "shasum": "", "reference": "cdddb0439aef0b45f22c479f936e5782ebc3f336"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "time": "2019-12-13T09:23:26+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "5fde0b8c1261e5089ece1525e68be2be27c8b2a6"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/5fde0b8c1261e5089ece1525e68be2be27c8b2a6", "type": "zip", "shasum": "", "reference": "5fde0b8c1261e5089ece1525e68be2be27c8b2a6"}, "time": "2019-12-13T07:19:40+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "fb81d2bccc0b891393367c8bb1fcc4cf8efcc761"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/fb81d2bccc0b891393367c8bb1fcc4cf8efcc761", "type": "zip", "shasum": "", "reference": "fb81d2bccc0b891393367c8bb1fcc4cf8efcc761"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.0"}, "time": "2019-12-12T14:01:13+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "43526ae63312942e5316100bb3ed589ba1aba491"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/43526ae63312942e5316100bb3ed589ba1aba491", "type": "zip", "shasum": "", "reference": "43526ae63312942e5316100bb3ed589ba1aba491"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.2.0"}, "time": "2019-04-23T12:39:21+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require-dev": {"phpstan/phpstan": "^0.8", "doctrine/coding-standard": "^5.0", "phpunit/phpunit": "^7.0"}}, {"version": "1.1.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "3da7c9d125591ca83944f477e65ed3d7b4617c48"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/3da7c9d125591ca83944f477e65ed3d7b4617c48", "type": "zip", "shasum": "", "reference": "3da7c9d125591ca83944f477e65ed3d7b4617c48"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.1.1"}, "time": "2019-04-23T08:28:24+00:00", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "c0f1c17602afc18b4cbd8e1c8125f264c9cf7d38"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/c0f1c17602afc18b4cbd8e1c8125f264c9cf7d38", "type": "zip", "shasum": "", "reference": "c0f1c17602afc18b4cbd8e1c8125f264c9cf7d38"}, "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/master"}, "time": "2018-11-21T00:33:13+00:00"}, {"description": "Doctrine Persistence abstractions.", "keywords": ["persistence"], "version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "af1ec238659a83e320f03e0e454e200f689b4b97"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/af1ec238659a83e320f03e0e454e200f689b4b97", "type": "zip", "shasum": "", "reference": "af1ec238659a83e320f03e0e454e200f689b4b97"}, "time": "2018-07-12T12:37:50+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require-dev": {"phpstan/phpstan": "^0.8", "doctrine/coding-standard": "^4.0", "phpunit/phpunit": "^7.0"}, "conflict": {"doctrine/common": "<2.9@dev"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/persistence.git", "type": "git", "reference": "17896f6d56a2794a1619e019596ae627aabd8fd5"}, "dist": {"url": "https://api.github.com/repos/doctrine/persistence/zipball/17896f6d56a2794a1619e019596ae627aabd8fd5", "type": "zip", "shasum": "", "reference": "17896f6d56a2794a1619e019596ae627aabd8fd5"}, "time": "2018-06-14T18:57:48+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 02 Nov 2024 10:06:33 GMT"}