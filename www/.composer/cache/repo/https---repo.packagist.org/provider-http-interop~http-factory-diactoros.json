{"minified": "composer/2.0", "packages": {"http-interop/http-factory-diactoros": [{"name": "http-interop/http-factory-diactoros", "description": "An HTTP Factory using Zend Diactoros", "keywords": ["http", "factory", "psr-7", "psr-17"], "homepage": "", "version": "1.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "source": {"url": "https://github.com/http-interop/http-factory-diactoros.git", "type": "git", "reference": "7d19904a7e1047d316211e4453a30f5bdd504294"}, "dist": {"url": "https://api.github.com/repos/http-interop/http-factory-diactoros/zipball/7d19904a7e1047d316211e4453a30f5bdd504294", "type": "zip", "shasum": "", "reference": "7d19904a7e1047d316211e4453a30f5bdd504294"}, "type": "library", "support": {"issues": "https://github.com/http-interop/http-factory-diactoros/issues", "source": "https://github.com/http-interop/http-factory-diactoros/tree/master"}, "time": "2018-07-31T19:31:26+00:00", "autoload": {"psr-4": {"Http\\Factory\\Diactoros\\": "src/"}}, "require": {"psr/http-factory": "^1.0", "zendframework/zend-diactoros": "^1.3"}, "require-dev": {"http-interop/http-factory-tests": "^0.5", "phpunit/phpunit": "^6.5"}, "provide": {"psr/http-factory-implementation": "^1.0"}}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/http-interop/http-factory-diactoros.git", "type": "git", "reference": "60e10aa7d1788107d2b8d3080c70bb9debd9116f"}, "dist": {"url": "https://api.github.com/repos/http-interop/http-factory-diactoros/zipball/60e10aa7d1788107d2b8d3080c70bb9debd9116f", "type": "zip", "shasum": "", "reference": "60e10aa7d1788107d2b8d3080c70bb9debd9116f"}, "time": "2017-03-29T10:51:41+00:00", "require": {"http-interop/http-factory": "^0.3", "zendframework/zend-diactoros": "^1.3"}, "require-dev": {"http-interop/http-factory-tests": "^0.3"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/http-interop/http-factory-diactoros.git", "type": "git", "reference": "7ee54aa8fdc9c948066e0d056f02ade0c0c4cfeb"}, "dist": {"url": "https://api.github.com/repos/http-interop/http-factory-diactoros/zipball/7ee54aa8fdc9c948066e0d056f02ade0c0c4cfeb", "type": "zip", "shasum": "", "reference": "7ee54aa8fdc9c948066e0d056f02ade0c0c4cfeb"}, "time": "2016-12-14T15:05:23+00:00", "require": {"zendframework/zend-diactoros": "^1.3", "http-interop/http-factory": "^0.2"}, "require-dev": {"http-interop/http-factory-tests": "^0.2"}, "provide": "__unset"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/http-interop/http-factory-diactoros.git", "type": "git", "reference": "97d66426bc677b13a268f9977b75596fd40475c9"}, "dist": {"url": "https://api.github.com/repos/http-interop/http-factory-diactoros/zipball/97d66426bc677b13a268f9977b75596fd40475c9", "type": "zip", "shasum": "", "reference": "97d66426bc677b13a268f9977b75596fd40475c9"}, "time": "2016-11-28T14:23:28+00:00", "require": {"http-interop/http-factory": "dev-master", "zendframework/zend-diactoros": "^1.3"}, "require-dev": {"http-interop/http-factory-tests": "dev-master"}}]}, "security-advisories": [], "last-modified": "Sat, 06 Apr 2024 08:45:25 GMT"}