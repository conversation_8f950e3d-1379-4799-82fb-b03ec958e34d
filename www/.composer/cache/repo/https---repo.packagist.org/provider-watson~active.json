{"minified": "composer/2.0", "packages": {"watson/active": [{"name": "watson/active", "description": "<PERSON><PERSON> helper for recognising the current route, controller and action", "keywords": ["routing", "laravel", "active"], "homepage": "", "version": "7.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "02cdd392dbb56a181348ca9a078963d6b9d60ab9"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/02cdd392dbb56a181348ca9a078963d6b9d60ab9", "type": "zip", "shasum": "", "reference": "02cdd392dbb56a181348ca9a078963d6b9d60ab9"}, "type": "library", "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/7.2.0"}, "funding": [], "time": "2025-02-25T23:22:01+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Watson\\Active\\": "src/"}}, "extra": {"laravel": {"aliases": {"Active": "Watson\\Watson\\Facades\\Active"}, "providers": ["Watson\\Active\\ActiveServiceProvider"]}}, "require": {"php": "^8.2", "illuminate/config": "^10.0|^11.0||^12.0", "illuminate/http": "^10.0|^11.0||^12.0", "illuminate/routing": "^10.0|^11.0||^12.0", "illuminate/support": "^10.0|^11.0||^12.0"}, "require-dev": {"phpunit/phpunit": "^9.0|^10.5|^11.0", "mockery/mockery": "^1.5.1"}}, {"version": "7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "861be09a8d2ce225701f442899812506cbe5674e"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/861be09a8d2ce225701f442899812506cbe5674e", "type": "zip", "shasum": "", "reference": "861be09a8d2ce225701f442899812506cbe5674e"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/7.1.0"}, "time": "2024-02-28T21:52:01+00:00", "require": {"php": "^8.2", "illuminate/config": "^10.0|^11.0", "illuminate/http": "^10.0|^11.0", "illuminate/routing": "^10.0|^11.0", "illuminate/support": "^10.0|^11.0"}}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "ae143167005e1fae6f8a2d92c592d294b49f95d5"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/ae143167005e1fae6f8a2d92c592d294b49f95d5", "type": "zip", "shasum": "", "reference": "ae143167005e1fae6f8a2d92c592d294b49f95d5"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/7.0.0"}, "time": "2023-02-15T02:30:02+00:00", "require": {"php": "^8.1", "illuminate/config": "^9.0|^10.0", "illuminate/http": "^9.0|^10.0", "illuminate/routing": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "mockery/mockery": "^1.5.1"}}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "acef9f7c8d9a122bd9fd4047437fbf76c760891d"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/acef9f7c8d9a122bd9fd4047437fbf76c760891d", "type": "zip", "shasum": "", "reference": "acef9f7c8d9a122bd9fd4047437fbf76c760891d"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/6.1.0"}, "time": "2022-01-26T09:54:27+00:00", "require": {"php": "^8.0", "illuminate/config": "^8.0|^9.0", "illuminate/http": "^8.0|^9.0", "illuminate/routing": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "mockery/mockery": "^1.3.1"}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "54ec1fc30b0faf5bd2fe2d4e18e57e0c4c134d7c"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/54ec1fc30b0faf5bd2fe2d4e18e57e0c4c134d7c", "type": "zip", "shasum": "", "reference": "54ec1fc30b0faf5bd2fe2d4e18e57e0c4c134d7c"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/6.0.1"}, "time": "2020-11-30T22:37:43+00:00", "require": {"php": "^7.3|^8.0", "illuminate/config": "^8.0", "illuminate/http": "^8.0", "illuminate/routing": "^8.0", "illuminate/support": "^8.0"}}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "d40b71141dab95062429fa56bfd5034062aa3791"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/d40b71141dab95062429fa56bfd5034062aa3791", "type": "zip", "shasum": "", "reference": "d40b71141dab95062429fa56bfd5034062aa3791"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/6.0.0"}, "time": "2020-09-09T08:42:38+00:00", "require": {"php": "^7.3", "illuminate/config": "^8.0", "illuminate/http": "^8.0", "illuminate/routing": "^8.0", "illuminate/support": "^8.0"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "3136d3ba5c364c4469a164a356b689109a60a1bf"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/3136d3ba5c364c4469a164a356b689109a60a1bf", "type": "zip", "shasum": "", "reference": "3136d3ba5c364c4469a164a356b689109a60a1bf"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/master"}, "time": "2020-03-03T20:25:22+00:00", "require": {"php": ">=7.2", "illuminate/config": "^7.0", "illuminate/http": "^7.0", "illuminate/routing": "^7.0", "illuminate/support": "^7.0"}, "require-dev": {"phpunit/phpunit": "~8.0", "mockery/mockery": "~1.0"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "9d198202c6c883918a21902eb999a9b8acf98674"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/9d198202c6c883918a21902eb999a9b8acf98674", "type": "zip", "shasum": "", "reference": "9d198202c6c883918a21902eb999a9b8acf98674"}, "time": "2019-09-03T17:22:27+00:00", "require": {"php": ">=7.2", "illuminate/config": "^6.0", "illuminate/http": "^6.0", "illuminate/routing": "^6.0", "illuminate/support": "^6.0"}, "funding": "__unset"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "4faa30a31d2bb870c852ea38330f3fa1c1350f34"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/4faa30a31d2bb870c852ea38330f3fa1c1350f34", "type": "zip", "shasum": "", "reference": "4faa30a31d2bb870c852ea38330f3fa1c1350f34"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/3.1.0"}, "time": "2019-03-25T04:56:13+00:00", "require": {"php": ">=7.2", "illuminate/config": "~5.5", "illuminate/http": "~5.5", "illuminate/routing": "~5.5", "illuminate/support": "~5.5"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "576e4072c666bda22182a7266475065f2e7001b3"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/576e4072c666bda22182a7266475065f2e7001b3", "type": "zip", "shasum": "", "reference": "576e4072c666bda22182a7266475065f2e7001b3"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/master"}, "time": "2018-09-07T02:53:52+00:00", "require": {"php": ">=7.0", "illuminate/config": "~5.5", "illuminate/http": "~5.5", "illuminate/routing": "~5.5", "illuminate/support": "~5.5"}, "require-dev": {"phpunit/phpunit": "~6.0", "mockery/mockery": "~1.0"}}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "b29ed98e831d31695279d509167d05c1f57495fe"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/b29ed98e831d31695279d509167d05c1f57495fe", "type": "zip", "shasum": "", "reference": "b29ed98e831d31695279d509167d05c1f57495fe"}, "time": "2017-08-26T01:42:01+00:00", "require": {"php": ">=5.5.0", "illuminate/config": "~5.2", "illuminate/http": "~5.2", "illuminate/routing": "~5.2", "illuminate/support": "~5.2"}, "require-dev": {"phpunit/phpunit": "~4.0", "mockery/mockery": "0.9.*"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "6019c05a14ed1a84cf0acae80a1a52df191eed3d"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/6019c05a14ed1a84cf0acae80a1a52df191eed3d", "type": "zip", "shasum": "", "reference": "6019c05a14ed1a84cf0acae80a1a52df191eed3d"}, "time": "2017-07-09T07:37:50+00:00", "require": {"php": ">=5.5.0", "illuminate/config": "~5.0", "illuminate/http": "~5.0", "illuminate/routing": "~5.0", "illuminate/support": "~5.0"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "be090ea9c6d18bbb6f76998f44946c7f16ec5906"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/be090ea9c6d18bbb6f76998f44946c7f16ec5906", "type": "zip", "shasum": "", "reference": "be090ea9c6d18bbb6f76998f44946c7f16ec5906"}, "time": "2017-05-07T20:27:05+00:00", "extra": "__unset"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "51f7dc592271d6f06c58a389005e3c504b200f1f"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/51f7dc592271d6f06c58a389005e3c504b200f1f", "type": "zip", "shasum": "", "reference": "51f7dc592271d6f06c58a389005e3c504b200f1f"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/2.0.3"}, "time": "2015-11-20T06:01:23+00:00", "require": {"php": ">=5.5.0", "illuminate/http": "~5.0", "illuminate/routing": "~5.0", "illuminate/support": "~5.0"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "5fd3eb0d861a02a1c9667bcc395ea888fcfb4eb6"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/5fd3eb0d861a02a1c9667bcc395ea888fcfb4eb6", "type": "zip", "shasum": "", "reference": "5fd3eb0d861a02a1c9667bcc395ea888fcfb4eb6"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/2.0.2"}, "time": "2015-11-20T05:51:52+00:00"}, {"description": "Laravel 4 helper for recognising the current route, controller and action", "version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "7443522d5e57cbd3589e13081f2ab708ddc23742"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/7443522d5e57cbd3589e13081f2ab708ddc23742", "type": "zip", "shasum": "", "reference": "7443522d5e57cbd3589e13081f2ab708ddc23742"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/2.0.1"}, "time": "2015-11-20T05:29:54+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "f2973015d889b9d2ca5b6d2cdf507ba4895f1243"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/f2973015d889b9d2ca5b6d2cdf507ba4895f1243", "type": "zip", "shasum": "", "reference": "f2973015d889b9d2ca5b6d2cdf507ba4895f1243"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/2.0.0"}, "time": "2015-11-20T05:23:52+00:00", "require": {"php": ">=5.4.0", "illuminate/http": "~5.0", "illuminate/routing": "~5.0", "illuminate/support": "~5.0"}}, {"version": "1.3.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "dwight<PERSON><EMAIL>"}], "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "f854893b62ca335f3e9ca71289172569e319aeed"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/f854893b62ca335f3e9ca71289172569e319aeed", "type": "zip", "shasum": "", "reference": "f854893b62ca335f3e9ca71289172569e319aeed"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.3.1"}, "time": "2015-03-17T12:09:12+00:00", "autoload": {"files": ["src/ActiveHelpers.php"], "psr-4": {"Watson\\Active\\": "src/"}}, "require": {"php": ">=5.4.0", "illuminate/http": "~4.2 || ~5.0", "illuminate/routing": "~4.2 || ~5.0", "illuminate/support": "~4.2 || ~5.0"}, "require-dev": {"phpunit/phpunit": "4.5.*", "mockery/mockery": "0.9.*"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "17b11a7c7e97f18a23da6603ee03e0b6ea2c7f69"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/17b11a7c7e97f18a23da6603ee03e0b6ea2c7f69", "type": "zip", "shasum": "", "reference": "17b11a7c7e97f18a23da6603ee03e0b6ea2c7f69"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.3.0"}, "time": "2015-03-17T11:39:15+00:00"}, {"version": "1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "bb7c081c2b514fac90fbfc511c0813e14de5f846"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/bb7c081c2b514fac90fbfc511c0813e14de5f846", "type": "zip", "shasum": "", "reference": "bb7c081c2b514fac90fbfc511c0813e14de5f846"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/master"}, "time": "2014-07-24T10:29:52+00:00", "require": {"php": ">=5.3.0", "illuminate/http": "4.2.*", "illuminate/routing": "4.2.*", "illuminate/support": "4.2.*"}, "require-dev": {"phpunit/phpunit": "4.1.*", "mockery/mockery": "0.9.*"}}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "eadd3aecf6d316d666b833b7f636e94c9679b041"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/eadd3aecf6d316d666b833b7f636e94c9679b041", "type": "zip", "shasum": "", "reference": "eadd3aecf6d316d666b833b7f636e94c9679b041"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.2.3"}, "time": "2014-07-18T06:24:07+00:00"}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "0eb3812b3511aead6d6a433edfc8311aa86a87c6"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/0eb3812b3511aead6d6a433edfc8311aa86a87c6", "type": "zip", "shasum": "", "reference": "0eb3812b3511aead6d6a433edfc8311aa86a87c6"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.2.2"}, "time": "2014-07-10T01:47:16+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "990359786d1d7607553d108ed323c83deb3c1c25"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/990359786d1d7607553d108ed323c83deb3c1c25", "type": "zip", "shasum": "", "reference": "990359786d1d7607553d108ed323c83deb3c1c25"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.2.1"}, "time": "2014-07-03T04:00:38+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "3fe4d011b8320afbe91fd573381b5728a2e87756"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/3fe4d011b8320afbe91fd573381b5728a2e87756", "type": "zip", "shasum": "", "reference": "3fe4d011b8320afbe91fd573381b5728a2e87756"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.2.0"}, "time": "2014-05-27T06:21:47+00:00", "autoload": {"files": ["src/Watson/Active/ActiveHelpers.php"], "psr-4": {"Watson\\": "src/<PERSON>"}}, "require": {"php": ">=5.3.0", "illuminate/http": "4.2.x", "illuminate/routing": "4.2.x", "illuminate/support": "4.2.x"}, "require-dev": {"phpunit/phpunit": "4.0.*", "mockery/mockery": "0.9.*"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "06b99589b78a6a2f93d07c2b05c419604d0b966d"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/06b99589b78a6a2f93d07c2b05c419604d0b966d", "type": "zip", "shasum": "", "reference": "06b99589b78a6a2f93d07c2b05c419604d0b966d"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.1.1"}, "time": "2014-05-27T06:12:10+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "e5a187b6fcc67332c7bb992cf7fa6a81bb2db698"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/e5a187b6fcc67332c7bb992cf7fa6a81bb2db698", "type": "zip", "shasum": "", "reference": "e5a187b6fcc67332c7bb992cf7fa6a81bb2db698"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.1.0"}, "time": "2014-05-27T06:08:57+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "ed4cb4ec1a8927aa2c23a8e0b2a422e4958c2d2b"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/ed4cb4ec1a8927aa2c23a8e0b2a422e4958c2d2b", "type": "zip", "shasum": "", "reference": "ed4cb4ec1a8927aa2c23a8e0b2a422e4958c2d2b"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.0.5"}, "time": "2014-03-14T06:12:39+00:00", "require": {"php": ">=5.3.0", "illuminate/http": "~4.0", "illuminate/routing": "~4.0", "illuminate/support": "~4.0"}, "require-dev": {"phpunit/phpunit": "3.7.*", "mockery/mockery": "dev-master"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "f81529023b5688b5bc48648bacbc4e2ffa5d2feb"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/f81529023b5688b5bc48648bacbc4e2ffa5d2feb", "type": "zip", "shasum": "", "reference": "f81529023b5688b5bc48648bacbc4e2ffa5d2feb"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.0.4"}, "time": "2014-03-14T03:12:49+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "00055c7f65b2869e1e2e423b9636873b791a6c17"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/00055c7f65b2869e1e2e423b9636873b791a6c17", "type": "zip", "shasum": "", "reference": "00055c7f65b2869e1e2e423b9636873b791a6c17"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/master"}, "time": "2014-03-11T12:39:38+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "08d2f6bb9e5766e228071bdc6277186c0edc8701"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/08d2f6bb9e5766e228071bdc6277186c0edc8701", "type": "zip", "shasum": "", "reference": "08d2f6bb9e5766e228071bdc6277186c0edc8701"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.0.2"}, "time": "2014-03-07T03:23:08+00:00", "require": {"php": ">=5.3.0", "illuminate/http": "4.1.x", "illuminate/routing": "4.1.x", "illuminate/support": "4.1.x"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "71dc912e02b975aed2c3da9e0c811e1c7108a120"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/71dc912e02b975aed2c3da9e0c811e1c7108a120", "type": "zip", "shasum": "", "reference": "71dc912e02b975aed2c3da9e0c811e1c7108a120"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.0.1"}, "time": "2014-03-06T10:20:56+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dwightwatson/active.git", "type": "git", "reference": "168bf4ac66425d7dd1b8e721e24c349e566764eb"}, "dist": {"url": "https://api.github.com/repos/dwightwatson/active/zipball/168bf4ac66425d7dd1b8e721e24c349e566764eb", "type": "zip", "shasum": "", "reference": "168bf4ac66425d7dd1b8e721e24c349e566764eb"}, "support": {"issues": "https://github.com/dwightwatson/active/issues", "source": "https://github.com/dwightwatson/active/tree/1.0.0"}, "time": "2014-03-06T10:07:57+00:00"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 25 Feb 2025 23:22:42 GMT"}