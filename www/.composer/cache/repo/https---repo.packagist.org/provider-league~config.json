{"minified": "composer/2.0", "packages": {"league/config": [{"name": "league/config", "description": "Define configuration arrays with strict schemas and access values with dot notation", "keywords": ["configuration", "schema", "config", "array", "dot", "nested", "dot-access"], "homepage": "https://config.thephpleague.com", "version": "v1.2.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "source": {"url": "https://github.com/thephpleague/config.git", "type": "git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "type": "zip", "shasum": "", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "type": "library", "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2022-12-11T20:36:23+00:00", "autoload": {"psr-4": {"League\\Config\\": "src"}}, "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "require": {"php": "^7.4 || ^8.0", "dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/config.git", "type": "git", "reference": "a9d39eeeb6cc49d10a6e6c36f22c4c1f4a767f3e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/config/zipball/a9d39eeeb6cc49d10a6e6c36f22c4c1f4a767f3e", "type": "zip", "shasum": "", "reference": "a9d39eeeb6cc49d10a6e6c36f22c4c1f4a767f3e"}, "time": "2021-08-14T12:15:32+00:00", "require-dev": {"phpstan/phpstan": "^0.12.90", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/config.git", "type": "git", "reference": "20d42d88f12a76ff862e17af4f14a5a4bbfd0925"}, "dist": {"url": "https://api.github.com/repos/thephpleague/config/zipball/20d42d88f12a76ff862e17af4f14a5a4bbfd0925", "type": "zip", "shasum": "", "reference": "20d42d88f12a76ff862e17af4f14a5a4bbfd0925"}, "time": "2021-06-19T15:52:37+00:00", "require": {"php": "^7.4 || ^8.0", "dflydev/dot-access-data": "^3.0", "nette/schema": "^1.2"}}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/config.git", "type": "git", "reference": "71bd9e5910375a301727b045ffe92e9e1c6aed6f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/config/zipball/71bd9e5910375a301727b045ffe92e9e1c6aed6f", "type": "zip", "shasum": "", "reference": "71bd9e5910375a301727b045ffe92e9e1c6aed6f"}, "time": "2021-05-31T20:53:49+00:00", "extra": {"branch-alias": {"dev-main": "1.1-dev"}}, "require": {"php": "^7.2.5 || ^8.0", "dflydev/dot-access-data": "^3.0", "nette/schema": "^1.1"}, "require-dev": {"phpstan/phpstan": "^0.12.88", "phpunit/phpunit": "^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "unleashedtech/php-coding-standard": "^3.0", "vimeo/psalm": "^4.7.3"}, "conflict": {"scrutinizer/ocular": "1.7.*", "vimeo/psalm": "3.15.0"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/config.git", "type": "git", "reference": "7ca63580dd184abac2c6c25d3ec6c839bb0b17ef"}, "dist": {"url": "https://api.github.com/repos/thephpleague/config/zipball/7ca63580dd184abac2c6c25d3ec6c839bb0b17ef", "type": "zip", "shasum": "", "reference": "7ca63580dd184abac2c6c25d3ec6c839bb0b17ef"}, "time": "2021-05-31T11:29:39+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 22 Mar 2024 00:13:20 GMT"}