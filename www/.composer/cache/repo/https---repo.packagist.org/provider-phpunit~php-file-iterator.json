{"minified": "composer/2.0", "packages": {"phpunit/php-file-iterator": [{"name": "phpunit/php-file-iterator", "description": "FilterIterator implementation that filters files based on a list of suffixes.", "keywords": ["filesystem", "iterator"], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "version": "6.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "961bc913d42fe24a257bfff826a5068079ac7782"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/961bc913d42fe24a257bfff826a5068079ac7782", "type": "zip", "shasum": "", "reference": "961bc913d42fe24a257bfff826a5068079ac7782"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:37+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "118cfaaa8bc5aef3287bf315b6060b1174754af6"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-file-iterator/zipball/118cfaaa8bc5aef3287bf315b6060b1174754af6", "type": "zip", "shasum": "", "reference": "118cfaaa8bc5aef3287bf315b6060b1174754af6"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/5.1.0"}, "time": "2024-08-27T05:02:59+00:00", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "6ed896bf50bbbfe4d504a33ed5886278c78e4a26"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/6ed896bf50bbbfe4d504a33ed5886278c78e4a26", "type": "zip", "shasum": "", "reference": "6ed896bf50bbbfe4d504a33ed5886278c78e4a26"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/5.0.1"}, "time": "2024-07-03T05:06:37+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "99e95c94ad9500daca992354fa09d7b99abe2210"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/99e95c94ad9500daca992354fa09d7b99abe2210", "type": "zip", "shasum": "", "reference": "99e95c94ad9500daca992354fa09d7b99abe2210"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/5.0.0"}, "time": "2024-02-02T06:05:04+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "type": "zip", "shasum": "", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "time": "2023-08-31T06:24:48+00:00", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "5647d65443818959172645e7ed999217360654b6"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/5647d65443818959172645e7ed999217360654b6", "type": "zip", "shasum": "", "reference": "5647d65443818959172645e7ed999217360654b6"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.0.2"}, "time": "2023-05-07T09:13:23+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "fd9329ab3368f59fe1fe808a189c51086bd4b6bd"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/fd9329ab3368f59fe1fe808a189c51086bd4b6bd", "type": "zip", "shasum": "", "reference": "fd9329ab3368f59fe1fe808a189c51086bd4b6bd"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.0.1"}, "time": "2023-02-10T16:53:14+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "7d66d4e816d34e90acec9db9d8d94b5cfbfe926f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/7d66d4e816d34e90acec9db9d8d94b5cfbfe926f", "type": "zip", "shasum": "", "reference": "7d66d4e816d34e90acec9db9d8d94b5cfbfe926f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.0.0"}, "time": "2023-02-03T06:55:11+00:00"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "type": "zip", "shasum": "", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "time": "2021-12-02T12:48:52+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "aa4be8575f26070b100fccb67faabb28f21f66f8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/aa4be8575f26070b100fccb67faabb28f21f66f8", "type": "zip", "shasum": "", "reference": "aa4be8575f26070b100fccb67faabb28f21f66f8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.5"}, "time": "2020-09-28T05:57:25+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "25fefc5b19835ca653877fe081644a3f8c1d915e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/25fefc5b19835ca653877fe081644a3f8c1d915e", "type": "zip", "shasum": "", "reference": "25fefc5b19835ca653877fe081644a3f8c1d915e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.4"}, "time": "2020-07-11T05:18:21+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "8e282e5f5e2db5fb2271b3962ad69875c34a6f41"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/8e282e5f5e2db5fb2271b3962ad69875c34a6f41", "type": "zip", "shasum": "", "reference": "8e282e5f5e2db5fb2271b3962ad69875c34a6f41"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/master"}, "time": "2020-06-26T11:50:37+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "eba15e538f2bb3fe018b7bbb47d2fe32d404bfd2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/eba15e538f2bb3fe018b7bbb47d2fe32d404bfd2", "type": "zip", "shasum": "", "reference": "eba15e538f2bb3fe018b7bbb47d2fe32d404bfd2"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.2"}, "time": "2020-06-15T12:54:35+00:00", "require": {"php": "^7.3"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "4ac5b3e13df14829daa60a2eb4fdd2f2b7d33cf4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/4ac5b3e13df14829daa60a2eb4fdd2f2b7d33cf4", "type": "zip", "shasum": "", "reference": "4ac5b3e13df14829daa60a2eb4fdd2f2b7d33cf4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.1"}, "time": "2020-04-18T05:02:12+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "354d4a5faa7449a377a18b94a2026ca3415e3d7a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/354d4a5faa7449a377a18b94a2026ca3415e3d7a", "type": "zip", "shasum": "", "reference": "354d4a5faa7449a377a18b94a2026ca3415e3d7a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.0"}, "time": "2020-02-07T06:05:22+00:00", "funding": "__unset"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "69deeb8664f611f156a924154985fbd4911eb36b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/69deeb8664f611f156a924154985fbd4911eb36b", "type": "zip", "shasum": "", "reference": "69deeb8664f611f156a924154985fbd4911eb36b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:39:50+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "type": "zip", "shasum": "", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.5"}, "time": "2021-12-02T12:42:26+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "28af674ff175d0768a5a978e6de83f697d4a7f05"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-file-iterator/zipball/28af674ff175d0768a5a978e6de83f697d4a7f05", "type": "zip", "shasum": "", "reference": "28af674ff175d0768a5a978e6de83f697d4a7f05"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.4"}, "time": "2021-07-19T06:46:01+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/4b49fb70f067272b659ef0174ff9ca40fdaa6357", "type": "zip", "shasum": "", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.3"}, "time": "2020-11-30T08:25:21+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "type": "zip", "shasum": "", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.2"}, "time": "2018-09-13T20:33:42+00:00", "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "funding": "__unset"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "cecbc684605bb0cc288828eb5d65d93d5c676d3c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cecbc684605bb0cc288828eb5d65d93d5c676d3c", "type": "zip", "shasum": "", "reference": "cecbc684605bb0cc288828eb5d65d93d5c676d3c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/master"}, "time": "2018-06-11T11:44:00+00:00", "require-dev": "__unset"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "e20525b0c2945c7c317fff95660698cb3d2a53bc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/e20525b0c2945c7c317fff95660698cb3d2a53bc", "type": "zip", "shasum": "", "reference": "e20525b0c2945c7c317fff95660698cb3d2a53bc"}, "time": "2018-05-28T12:13:49+00:00"}, {"version": "1.4.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "type": "zip", "shasum": "", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.4.5"}, "time": "2017-11-27T13:52:08+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "3d478c5f9e8bb02b484a3fdc82693a460d7ee268"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/3d478c5f9e8bb02b484a3fdc82693a460d7ee268", "type": "zip", "shasum": "", "reference": "3d478c5f9e8bb02b484a3fdc82693a460d7ee268"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.4.4"}, "time": "2017-11-27T07:49:00+00:00"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "8ebba84e5bd74fc5fdeb916b38749016c7232f93"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/8ebba84e5bd74fc5fdeb916b38749016c7232f93", "type": "zip", "shasum": "", "reference": "8ebba84e5bd74fc5fdeb916b38749016c7232f93"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.4"}, "time": "2017-11-24T15:00:59+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "3cc8f69b3028d0f96a9078e6295d86e9bf019be5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/3cc8f69b3028d0f96a9078e6295d86e9bf019be5", "type": "zip", "shasum": "", "reference": "3cc8f69b3028d0f96a9078e6295d86e9bf019be5"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/master"}, "time": "2016-10-03T07:40:28+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "type": "zip", "shasum": "", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0"}, "time": "2015-06-21T13:08:43+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "a923bb15680d0089e2316f7a4af8f437046e96bb"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-file-iterator/zipball/a923bb15680d0089e2316f7a4af8f437046e96bb", "type": "zip", "shasum": "", "reference": "a923bb15680d0089e2316f7a4af8f437046e96bb"}, "time": "2015-04-02T05:19:05+00:00"}, {"version": "1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "acd690379117b042d1c8af1fafd61bde001bf6bb"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/acd690379117b042d1c8af1fafd61bde001bf6bb", "type": "zip", "shasum": "", "reference": "acd690379117b042d1c8af1fafd61bde001bf6bb"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.3.4"}, "time": "2013-10-10T15:34:57+00:00", "autoload": {"classmap": ["File/"]}, "include-path": [""], "extra": "__unset"}, {"homepage": "http://www.phpunit.de/", "version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "16a78140ed2fc01b945cfa539665fadc6a038029"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/16a78140ed2fc01b945cfa539665fadc6a038029", "type": "zip", "shasum": "", "reference": "16a78140ed2fc01b945cfa539665fadc6a038029"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.3.3"}, "time": "2012-10-11T11:44:38+00:00"}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "type": "git", "reference": "46b0610951db3a918ee7842bc0d471e72c1d0d46"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/46b0610951db3a918ee7842bc0d471e72c1d0d46", "type": "zip", "shasum": "", "reference": "46b0610951db3a918ee7842bc0d471e72c1d0d46"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.3.2"}, "time": "2012-09-22T00:00:00+00:00", "autoload": {"files": ["File/Iterator/Autoload.php"]}, "require": {"php": ">=5.2.7"}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:58:46 GMT"}