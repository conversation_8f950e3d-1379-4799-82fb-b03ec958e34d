{"minified": "composer/2.0", "packages": {"graham-campbell/markdown": [{"name": "graham-campbell/markdown", "description": "Markdown Is A CommonMark Wrapper For Laravel", "keywords": ["framework", "markdown", "laravel", "<PERSON>", "Graham<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commonmark", "common mark"], "homepage": "", "version": "v16.0.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "040221ff01f167fbe53a047b0e9ac15cbb3b6d79"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/040221ff01f167fbe53a047b0e9ac15cbb3b6d79", "type": "zip", "shasum": "", "reference": "040221ff01f167fbe53a047b0e9ac15cbb3b6d79"}, "type": "library", "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v16.0.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/markdown", "type": "tidelift"}], "time": "2025-03-02T23:41:30+00:00", "autoload": {"psr-4": {"GrahamCampbell\\Markdown\\": "src/"}}, "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}}, "require": {"illuminate/contracts": "^10.44 || ^11.0 || ^12.0", "illuminate/filesystem": "^10.44 || ^11.0 || ^12.0", "illuminate/support": "^10.44 || ^11.0 || ^12.0", "illuminate/view": "^10.44 || ^11.0 || ^12.0", "league/commonmark": "^2.6.1", "php": "^8.1"}, "require-dev": {"graham-campbell/analyzer": "^5.0", "graham-campbell/testbench": "^6.2", "mockery/mockery": "^1.6.6", "phpunit/phpunit": "^10.5.45 || ^11.5.10"}}, {"version": "v15.3.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "042c23ddc5bde61ed09b77a8a17119d70c8e8419"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/042c23ddc5bde61ed09b77a8a17119d70c8e8419", "type": "zip", "shasum": "", "reference": "042c23ddc5bde61ed09b77a8a17119d70c8e8419"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v15.3.0"}, "time": "2025-03-02T22:13:22+00:00", "require": {"php": "^7.4.15 || ^8.0.2", "illuminate/contracts": "^8.75 || ^9.0 || ^10.0 || ^11.0", "illuminate/filesystem": "^8.75 || ^9.0 || ^10.0 || ^11.0", "illuminate/support": "^8.75 || ^9.0 || ^10.0 || ^11.0", "illuminate/view": "^8.75 || ^9.0 || ^10.0 || ^11.0", "league/commonmark": "^2.6.1"}, "require-dev": {"graham-campbell/analyzer": "^4.2.1", "graham-campbell/testbench": "^6.2", "mockery/mockery": "^1.6.6", "phpunit/phpunit": "^9.6.17 || ^10.5.13"}}, {"version": "v15.2.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "d594fc197b9068de5e234a890be361807a1ab34f"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/d594fc197b9068de5e234a890be361807a1ab34f", "type": "zip", "shasum": "", "reference": "d594fc197b9068de5e234a890be361807a1ab34f"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v15.2.0"}, "time": "2024-03-17T23:07:39+00:00", "require": {"php": "^7.4.15 || ^8.0.2", "illuminate/contracts": "^8.75 || ^9.0 || ^10.0 || ^11.0", "illuminate/filesystem": "^8.75 || ^9.0 || ^10.0 || ^11.0", "illuminate/support": "^8.75 || ^9.0 || ^10.0 || ^11.0", "illuminate/view": "^8.75 || ^9.0 || ^10.0 || ^11.0", "league/commonmark": "^2.4.2"}, "require-dev": {"graham-campbell/analyzer": "^4.1", "graham-campbell/testbench": "^6.1", "mockery/mockery": "^1.6.6", "phpunit/phpunit": "^9.6.17 || ^10.5.13"}}, {"version": "v15.1.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "94917d0d712c26788095ad2b5eafd9b33cd43095"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/94917d0d712c26788095ad2b5eafd9b33cd43095", "type": "zip", "shasum": "", "reference": "94917d0d712c26788095ad2b5eafd9b33cd43095"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v15.1.0"}, "time": "2023-12-04T02:43:19+00:00", "require": {"php": "^7.4.15 || ^8.0.2", "illuminate/contracts": "^8.75 || ^9.0 || ^10.0", "illuminate/filesystem": "^8.75 || ^9.0 || ^10.0", "illuminate/support": "^8.75 || ^9.0 || ^10.0", "illuminate/view": "^8.75 || ^9.0 || ^10.0", "league/commonmark": "^2.4.1"}, "require-dev": {"graham-campbell/analyzer": "^4.1", "graham-campbell/testbench": "^6.1", "mockery/mockery": "^1.6.6", "phpunit/phpunit": "^9.6.15 || ^10.4.2"}}, {"version": "v15.0.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "3c0bcf904ec02acb1afd0e23e7c170ac5199fc14"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/3c0bcf904ec02acb1afd0e23e7c170ac5199fc14", "type": "zip", "shasum": "", "reference": "3c0bcf904ec02acb1afd0e23e7c170ac5199fc14"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v15.0.0"}, "time": "2023-02-26T14:22:13+00:00", "require": {"php": "^7.4.15 || ^8.0.2", "illuminate/contracts": "^8.75 || ^9.0 || ^10.0", "illuminate/filesystem": "^8.75 || ^9.0 || ^10.0", "illuminate/support": "^8.75 || ^9.0 || ^10.0", "illuminate/view": "^8.75 || ^9.0 || ^10.0", "league/commonmark": "^2.3.9"}, "require-dev": {"graham-campbell/analyzer": "^4.0", "graham-campbell/testbench": "^6.0", "mockery/mockery": "^1.5.1", "phpunit/phpunit": "^9.6.3 || ^10.0.12"}}, {"version": "v14.0.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "36dc081ad00ee5abedff939cfccbfc5008eed8eb"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/36dc081ad00ee5abedff939cfccbfc5008eed8eb", "type": "zip", "shasum": "", "reference": "36dc081ad00ee5abedff939cfccbfc5008eed8eb"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v14.0.0"}, "time": "2022-05-30T21:37:30+00:00", "require": {"php": "^7.4.15 || ^8.0.2", "illuminate/contracts": "^8.75 || ^9.0", "illuminate/filesystem": "^8.75 || ^9.0", "illuminate/support": "^8.75 || ^9.0", "illuminate/view": "^8.75 || ^9.0", "league/commonmark": "^2.3.1"}, "require-dev": {"graham-campbell/analyzer": "^3.1", "graham-campbell/testbench": "^5.7", "mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5"}}, {"version": "v13.1.2", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "275f96e5b1a2f86f3239eb2c2c5262790725f4ba"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/275f96e5b1a2f86f3239eb2c2c5262790725f4ba", "type": "zip", "shasum": "", "reference": "275f96e5b1a2f86f3239eb2c2c5262790725f4ba"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v13.1.2"}, "time": "2021-11-21T15:23:56+00:00", "require": {"php": "^7.2.5 || ^8.0", "illuminate/contracts": "^6.0 || ^7.0 || ^8.0", "illuminate/filesystem": "^6.0 || ^7.0 || ^8.0", "illuminate/support": "^6.0 || ^7.0 || ^8.0", "illuminate/view": "^6.0 || ^7.0 || ^8.0", "league/commonmark": "^1.5"}, "require-dev": {"graham-campbell/analyzer": "^3.0", "graham-campbell/testbench": "^5.6", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5.8 || ^9.3.7"}}, {"version": "v13.1.1", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "d25b873e5c5870edc4de7d980808f1a8e092a9c7"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/d25b873e5c5870edc4de7d980808f1a8e092a9c7", "type": "zip", "shasum": "", "reference": "d25b873e5c5870edc4de7d980808f1a8e092a9c7"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v13.1.1"}, "time": "2020-08-22T14:18:21+00:00", "require-dev": {"graham-campbell/analyzer": "^3.0", "graham-campbell/testbench": "^5.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5.8 || ^9.3.7"}}, {"version": "v13.1.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "bd95ce6bb31b9d9732e42907c983b9f8e31c8854"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/bd95ce6bb31b9d9732e42907c983b9f8e31c8854", "type": "zip", "shasum": "", "reference": "bd95ce6bb31b9d9732e42907c983b9f8e31c8854"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/13.1"}, "time": "2020-08-14T18:55:51+00:00"}, {"version": "v13.0.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "3b8476c22fc90236456b2b1998361e9ea8408027"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/3b8476c22fc90236456b2b1998361e9ea8408027", "type": "zip", "shasum": "", "reference": "3b8476c22fc90236456b2b1998361e9ea8408027"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v13.0.0"}, "time": "2020-07-14T15:20:33+00:00", "require": {"php": "^7.2.5", "illuminate/contracts": "^6.0 || ^7.0", "illuminate/support": "^6.0 || ^7.0", "illuminate/view": "^6.0 || ^7.0", "league/commonmark": "^1.5"}, "require-dev": {"graham-campbell/analyzer": "^3.0", "graham-campbell/testbench": "^5.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5 || ^9.0"}}, {"version": "v12.0.2", "version_normalized": "1*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "584eb9f24004238b80ee98b6e7be82f0933554dd"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/584eb9f24004238b80ee98b6e7be82f0933554dd", "type": "zip", "shasum": "", "reference": "584eb9f24004238b80ee98b6e7be82f0933554dd"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v12.0.2"}, "time": "2020-04-14T16:14:52+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "12.0-dev"}}, "require": {"php": "^7.2.5", "illuminate/contracts": "^6.0|^7.0", "illuminate/support": "^6.0|^7.0", "illuminate/view": "^6.0|^7.0", "league/commonmark": "^1.3"}, "require-dev": {"graham-campbell/analyzer": "^3.0", "graham-campbell/testbench": "^5.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5|^9.0"}}, {"version": "v12.0.1", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "87488460da59a686390d8b3b60c3f6c27ccfb38c"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/87488460da59a686390d8b3b60c3f6c27ccfb38c", "type": "zip", "shasum": "", "reference": "87488460da59a686390d8b3b60c3f6c27ccfb38c"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v12.0.1"}, "time": "2020-04-14T14:37:09+00:00"}, {"version": "v12.0.0", "version_normalized": "1*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "1566129f213adf7f508e0bebc2399102c4c78cd6"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/1566129f213adf7f508e0bebc2399102c4c78cd6", "type": "zip", "shasum": "", "reference": "1566129f213adf7f508e0bebc2399102c4c78cd6"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v12.0.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://www.patreon.com/GrahamJCampbell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/markdown", "type": "tidelift"}], "time": "2020-03-01T11:40:45+00:00"}, {"version": "v11.2.1", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "7ead48c43098b562707a30650843d4279786b0d9"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/7ead48c43098b562707a30650843d4279786b0d9", "type": "zip", "shasum": "", "reference": "7ead48c43098b562707a30650843d4279786b0d9"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/11.2"}, "time": "2020-04-14T14:11:12+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "11.2-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "^5.5|^6.0|^7.0", "illuminate/support": "^5.5|^6.0|^7.0", "illuminate/view": "^5.5|^6.0|^7.0", "league/commonmark": "^1.3"}, "require-dev": {"graham-campbell/analyzer": "^2.4", "graham-campbell/testbench": "^5.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^6.5|^7.0|^8.0"}}, {"version": "v11.2.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "f6271cb869fcf91a9066cd61729750641f5299e9"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/f6271cb869fcf91a9066cd61729750641f5299e9", "type": "zip", "shasum": "", "reference": "f6271cb869fcf91a9066cd61729750641f5299e9"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/master"}, "funding": [], "time": "2020-01-25T13:19:30+00:00", "require": {"php": "^7.1.3", "illuminate/contracts": "^5.5|^6.0|^7.0", "illuminate/support": "^5.5|^6.0|^7.0", "illuminate/view": "^5.5|^6.0|^7.0", "league/commonmark": "^1.0"}, "require-dev": {"graham-campbell/analyzer": "^2.4", "graham-campbell/testbench": "^5.4", "league/commonmark-extras": "^1.0", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^6.5|^7.0|^8.0"}}, {"description": "Markdown Is A CommonMark Wrapper For Laravel 5", "version": "v11.1.0", "version_normalized": "1*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "80f545b417db2103122c7f094bfa5bb2444b28e1"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/80f545b417db2103122c7f094bfa5bb2444b28e1", "type": "zip", "shasum": "", "reference": "80f545b417db2103122c7f094bfa5bb2444b28e1"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v11.1.0"}, "time": "2019-08-26T16:31:24+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "11.1-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "^5.5|^6.0", "illuminate/support": "^5.5|^6.0", "illuminate/view": "^5.5|^6.0", "league/commonmark": "^1.0"}, "require-dev": {"graham-campbell/analyzer": "^2.1", "graham-campbell/testbench": "^5.2", "league/commonmark-extras": "^1.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5|^7.0|^8.0"}, "funding": "__unset"}, {"version": "v11.0.0", "version_normalized": "1*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "90f68debfc6611654b16efc5539f5a3b597d55d9"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/90f68debfc6611654b16efc5539f5a3b597d55d9", "type": "zip", "shasum": "", "reference": "90f68debfc6611654b16efc5539f5a3b597d55d9"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/master"}, "time": "2019-07-01T22:59:29+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "11.0-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "5.5.*|5.6.*|5.7.*|5.8.*", "illuminate/support": "5.5.*|5.6.*|5.7.*|5.8.*", "illuminate/view": "5.5.*|5.6.*|5.7.*|5.8.*", "league/commonmark": "^1.0"}}, {"version": "v10.3.1", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "c9978137be1e6896d9a2ca39aa3d2c62d0700ca1"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/c9978137be1e6896d9a2ca39aa3d2c62d0700ca1", "type": "zip", "shasum": "", "reference": "c9978137be1e6896d9a2ca39aa3d2c62d0700ca1"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v10.3.1"}, "time": "2019-06-30T22:39:31+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "10.3-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "5.5.*|5.6.*|5.7.*|5.8.*", "illuminate/support": "5.5.*|5.6.*|5.7.*|5.8.*", "illuminate/view": "5.5.*|5.6.*|5.7.*|5.8.*", "league/commonmark": "^0.18"}, "require-dev": {"graham-campbell/analyzer": "^2.1", "graham-campbell/testbench": "^5.2", "league/commonmark-extras": "^0.1.5", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5|^7.0|^8.0"}}, {"version": "v10.3.0", "version_normalized": "********", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "e076ed4bc8e98f0444b996acdd6042f6f45fe7c2"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/e076ed4bc8e98f0444b996acdd6042f6f45fe7c2", "type": "zip", "shasum": "", "reference": "e076ed4bc8e98f0444b996acdd6042f6f45fe7c2"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/master"}, "time": "2019-02-17T19:48:19+00:00"}, {"version": "v10.2.0", "version_normalized": "1*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "fdb55c7953c85c60f742294a9cd2b3b010bca0b6"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/fdb55c7953c85c60f742294a9cd2b3b010bca0b6", "type": "zip", "shasum": "", "reference": "fdb55c7953c85c60f742294a9cd2b3b010bca0b6"}, "time": "2018-09-29T10:19:14+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "10.2-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "5.5.*|5.6.*|5.7.*", "illuminate/support": "5.5.*|5.6.*|5.7.*", "illuminate/view": "5.5.*|5.6.*|5.7.*", "league/commonmark": "^0.18"}, "require-dev": {"graham-campbell/analyzer": "^2.1", "graham-campbell/testbench": "^5.1", "league/commonmark-extras": "^0.1.5", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5|^7.0"}}, {"version": "v10.1.0", "version_normalized": "1*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "1b685b0b31f1aa92a199251960d3a8807c9be047"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/1b685b0b31f1aa92a199251960d3a8807c9be047", "type": "zip", "shasum": "", "reference": "1b685b0b31f1aa92a199251960d3a8807c9be047"}, "time": "2018-08-23T12:03:07+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "10.1-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "5.5.*|5.6.*|5.7.*", "illuminate/support": "5.5.*|5.6.*|5.7.*", "illuminate/view": "5.5.*|5.6.*|5.7.*", "league/commonmark": "^0.17"}, "require-dev": {"graham-campbell/analyzer": "^2.1", "graham-campbell/testbench": "^5.1", "league/commonmark-extras": "^0.1.4", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5|^7.0"}}, {"version": "v10.0.0", "version_normalized": "10.0.0.0", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "359701e5e3986ba18d1b3d390e898a370b8f00d6"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/359701e5e3986ba18d1b3d390e898a370b8f00d6", "type": "zip", "shasum": "", "reference": "359701e5e3986ba18d1b3d390e898a370b8f00d6"}, "time": "2018-03-01T21:04:35+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "10.0-dev"}}, "require": {"php": "^7.1.3", "illuminate/contracts": "5.5.*|5.6.*", "illuminate/support": "5.5.*|5.6.*", "illuminate/view": "5.5.*|5.6.*", "league/commonmark": "^0.17"}, "require-dev": {"graham-campbell/analyzer": "^2.0", "graham-campbell/testbench": "^5.0", "league/commonmark-extras": "^0.1.4", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5|^7.0"}}, {"version": "v9.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "2c62d673869c635de368698d53f78eb7c10b18ce"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/2c62d673869c635de368698d53f78eb7c10b18ce", "type": "zip", "shasum": "", "reference": "2c62d673869c635de368698d53f78eb7c10b18ce"}, "time": "2018-01-02T18:18:48+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "9.0-dev"}}, "require": {"php": "^7.0", "illuminate/contracts": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "illuminate/support": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "illuminate/view": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "league/commonmark": "^0.17"}, "require-dev": {"graham-campbell/analyzer": "^2.0", "graham-campbell/testbench": "^4.0|^5.0", "league/commonmark-extras": "^0.1.4", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5"}}, {"version": "v8.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "f144479771fba3cac973639a1e705f6c9c2262fd"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/f144479771fba3cac973639a1e705f6c9c2262fd", "type": "zip", "shasum": "", "reference": "f144479771fba3cac973639a1e705f6c9c2262fd"}, "time": "2017-12-10T12:02:26+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "8.1-dev"}}, "require": {"php": "^7.0", "illuminate/contracts": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "illuminate/support": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "illuminate/view": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "league/commonmark": "^0.15|^0.16"}, "require-dev": {"graham-campbell/analyzer": "^1.1", "graham-campbell/testbench": "^4.0", "league/commonmark-extras": "^0.1.2", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.1"}}, {"version": "v8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "b06ee43a9fccb6f222cc618be031db4a00b94dfe"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/b06ee43a9fccb6f222cc618be031db4a00b94dfe", "type": "zip", "shasum": "", "reference": "b06ee43a9fccb6f222cc618be031db4a00b94dfe"}, "time": "2017-08-06T20:51:09+00:00", "extra": {"laravel": {"providers": ["GrahamCampbell\\Markdown\\MarkdownServiceProvider"]}, "branch-alias": {"dev-master": "8.0-dev"}}, "require": {"php": "^7.0", "illuminate/contracts": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "illuminate/support": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "illuminate/view": "5.1.*|5.2.*|5.3.*|5.4.*|5.5.*", "league/commonmark": "^0.15"}, "require-dev": {"graham-campbell/analyzer": "^1.1", "graham-campbell/testbench": "^4.0", "league/commonmark-extras": "^0.1.2", "mockery/mockery": "dev-master#c90a17247147543081e4d00f46911e422b49e583", "phpunit/phpunit": "^6.0"}}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "507783993b40f4b118f201e8a8bec9e908b3ab1d"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/507783993b40f4b118f201e8a8bec9e908b3ab1d", "type": "zip", "shasum": "", "reference": "507783993b40f4b118f201e8a8bec9e908b3ab1d"}, "time": "2017-01-01T13:30:44+00:00", "extra": {"branch-alias": {"dev-master": "7.1-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.1.*|5.2.*|5.3.*|5.4.*", "illuminate/support": "5.1.*|5.2.*|5.3.*|5.4.*", "illuminate/view": "5.1.*|5.2.*|5.3.*|5.4.*", "league/commonmark": "^0.15"}, "require-dev": {"graham-campbell/testbench": "^3.1", "league/commonmark-extras": "^0.1.2", "mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.8|^5.0"}}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "3880211af508324c58845536a790a6127f159233"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/3880211af508324c58845536a790a6127f159233", "type": "zip", "shasum": "", "reference": "3880211af508324c58845536a790a6127f159233"}, "time": "2016-09-20T12:25:34+00:00", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.1.*|5.2.*|5.3.*", "illuminate/support": "5.1.*|5.2.*|5.3.*", "illuminate/view": "5.1.*|5.2.*|5.3.*", "league/commonmark": "^0.15"}}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "2208ec910d9880d6bca28d332ae7f3b7350355d1"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/2208ec910d9880d6bca28d332ae7f3b7350355d1", "type": "zip", "shasum": "", "reference": "2208ec910d9880d6bca28d332ae7f3b7350355d1"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v6.1.0"}, "time": "2016-04-26T14:28:21+00:00", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.1.*|5.2.*|5.3.*", "illuminate/support": "5.1.*|5.2.*|5.3.*", "illuminate/view": "5.1.*|5.2.*|5.3.*", "league/commonmark": "^0.13"}, "require-dev": {"graham-campbell/testbench": "^3.1", "league/commonmark-extras": "^0.1", "mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.8|^5.0"}}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "29e4f49b40a3b588cc6154d4e5e2df90d89fd898"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/29e4f49b40a3b588cc6154d4e5e2df90d89fd898", "type": "zip", "shasum": "", "reference": "29e4f49b40a3b588cc6154d4e5e2df90d89fd898"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/master"}, "time": "2016-01-30T14:29:18+00:00", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.1.*|5.2.*", "illuminate/support": "5.1.*|5.2.*", "illuminate/view": "5.1.*|5.2.*", "league/commonmark": "^0.13"}}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "0449499d4b9272e5a83a5606b97b2377a0ed767e"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/0449499d4b9272e5a83a5606b97b2377a0ed767e", "type": "zip", "shasum": "", "reference": "0449499d4b9272e5a83a5606b97b2377a0ed767e"}, "time": "2016-01-15T21:32:07+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "8c587081a4beadd49839899ad649c2639697a563"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/8c587081a4beadd49839899ad649c2639697a563", "type": "zip", "shasum": "", "reference": "8c587081a4beadd49839899ad649c2639697a563"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/v5.3.0"}, "time": "2015-11-14T11:57:14+00:00", "extra": {"branch-alias": {"dev-master": "5.3-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.1.*|5.2.*", "illuminate/support": "5.1.*|5.2.*", "illuminate/view": "5.1.*|5.2.*", "league/commonmark": "^0.12"}, "require-dev": {"graham-campbell/testbench": "^3.1", "mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.8|^5.0"}}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "7498cd47107ab6f617937e68195f3453b0ef0537"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/7498cd47107ab6f617937e68195f3453b0ef0537", "type": "zip", "shasum": "", "reference": "7498cd47107ab6f617937e68195f3453b0ef0537"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/master"}, "time": "2015-11-05T15:09:56+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.0.*|5.1.*", "illuminate/support": "5.0.*|5.1.*", "illuminate/view": "5.0.*|5.1.*", "league/commonmark": "0.12.*"}, "require-dev": {"graham-campbell/testbench": "~3.0", "mockery/mockery": "^0.9.4", "phpunit/phpunit": "~4.8|~5.0"}}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "990e9ef977376331ba1ed50fe36adfbe7e34677f"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/990e9ef977376331ba1ed50fe36adfbe7e34677f", "type": "zip", "shasum": "", "reference": "990e9ef977376331ba1ed50fe36adfbe7e34677f"}, "time": "2015-10-06T16:53:34+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.0.*|5.1.*", "illuminate/support": "5.0.*|5.1.*", "illuminate/view": "5.0.*|5.1.*", "league/commonmark": "0.11.*"}}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "52c764b822cb35d5663ea178f399db228bfd16b7"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/52c764b822cb35d5663ea178f399db228bfd16b7", "type": "zip", "shasum": "", "reference": "52c764b822cb35d5663ea178f399db228bfd16b7"}, "time": "2015-09-19T22:30:34+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require-dev": {"graham-campbell/testbench": "~3.0", "mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.7.6"}}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "6e9a967e23f6c8480ad04d217ecfabda1aea4b09"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/6e9a967e23f6c8480ad04d217ecfabda1aea4b09", "type": "zip", "shasum": "", "reference": "6e9a967e23f6c8480ad04d217ecfabda1aea4b09"}, "time": "2015-07-25T14:48:25+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.0.*|5.1.*", "illuminate/support": "5.0.*|5.1.*", "illuminate/view": "5.0.*|5.1.*", "league/commonmark": "0.10.*"}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "6120fbeff05767616a0436635f67f7ee964f4f40"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/6120fbeff05767616a0436635f67f7ee964f4f40", "type": "zip", "shasum": "", "reference": "6120fbeff05767616a0436635f67f7ee964f4f40"}, "time": "2015-06-26T17:23:07+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require": {"php": ">=5.5.9", "illuminate/contracts": "5.0.*|5.1.*", "illuminate/support": "5.0.*|5.1.*", "illuminate/view": "5.0.*|5.1.*", "league/commonmark": "0.9.*"}}, {"version": "v3.1.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "b08e2610cccdeb160d7127a83d78607f2022624f"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/b08e2610cccdeb160d7127a83d78607f2022624f", "type": "zip", "shasum": "", "reference": "b08e2610cccdeb160d7127a83d78607f2022624f"}, "time": "2015-06-19T08:37:44+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "require-dev": {"graham-campbell/testbench": "~2.1"}}, {"version": "v3.1.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "f57d81dadbb92fc1ccfc18ae772ee25059ef2a57"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/f57d81dadbb92fc1ccfc18ae772ee25059ef2a57", "type": "zip", "shasum": "", "reference": "f57d81dadbb92fc1ccfc18ae772ee25059ef2a57"}, "time": "2015-05-20T22:50:07+00:00", "require": {"php": ">=5.5.9", "illuminate/contracts": "5.0.*|5.1.*", "illuminate/support": "5.0.*|5.1.*", "illuminate/view": "5.0.*|5.1.*", "league/commonmark": "0.8.*"}}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "32ba7796fe6a3ee78b40b8083e290bc166b612bd"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/32ba7796fe6a3ee78b40b8083e290bc166b612bd", "type": "zip", "shasum": "", "reference": "32ba7796fe6a3ee78b40b8083e290bc166b612bd"}, "time": "2015-05-07T13:59:12+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "require": {"php": ">=5.4.7", "illuminate/contracts": "5.0.*", "illuminate/support": "5.0.*", "illuminate/view": "5.0.*", "league/commonmark": "0.8.*"}, "require-dev": {"graham-campbell/testbench": "~2.0"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "dfc7e1525a55c6851fb85723a421bce819fd3c2a"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/dfc7e1525a55c6851fb85723a421bce819fd3c2a", "type": "zip", "shasum": "", "reference": "dfc7e1525a55c6851fb85723a421bce819fd3c2a"}, "time": "2015-02-18T11:41:14+00:00", "require": {"php": ">=5.4.7", "illuminate/contracts": "5.0.*", "illuminate/support": "5.0.*", "illuminate/view": "5.0.*", "league/commonmark": "0.7.*"}}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "315a7fe194492bf90c45370ed50744e9528212bb"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/315a7fe194492bf90c45370ed50744e9528212bb", "type": "zip", "shasum": "", "reference": "315a7fe194492bf90c45370ed50744e9528212bb"}, "time": "2015-02-05T19:38:06+00:00", "require": {"php": ">=5.4.7", "illuminate/contracts": "5.0.*", "illuminate/support": "5.0.*", "illuminate/view": "5.0.*", "league/commonmark": "0.6.*"}}, {"description": "Markdown Is A Parsedown Extra Wrapper For Laravel 4.1/4.2", "keywords": ["framework", "markdown", "laravel", "<PERSON>", "Graham<PERSON><PERSON><PERSON>", "parsedown", "parsedown wrapper", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markdown wrapper", "Parsedown Extra", "parsedown extra wrapper"], "version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "cf1d0f1c5a68cc632f4e2fd70cc09ca6e6efe2be"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/cf1d0f1c5a68cc632f4e2fd70cc09ca6e6efe2be", "type": "zip", "shasum": "", "reference": "cf1d0f1c5a68cc632f4e2fd70cc09ca6e6efe2be"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/2.1"}, "time": "2015-02-02T15:38:37+00:00", "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown": "1.5.*", "erusev/parsedown-extra": "0.7.*"}, "require-dev": {"graham-campbell/testbench": "~1.0"}, "extra": "__unset"}, {"version": "v2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "7c659621747cf291c344149b20383205f041f259"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/7c659621747cf291c344149b20383205f041f259", "type": "zip", "shasum": "", "reference": "7c659621747cf291c344149b20383205f041f259"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/2.0"}, "time": "2015-02-02T15:35:24+00:00", "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown": "~1.1.4", "erusev/parsedown-extra": "~0.2.6"}}, {"version": "v2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "9b79f2ace1772281284f570ca64931d550421786"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/9b79f2ace1772281284f570ca64931d550421786", "type": "zip", "shasum": "", "reference": "9b79f2ace1772281284f570ca64931d550421786"}, "time": "2015-01-04T22:11:30+00:00", "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown": "~1.1", "erusev/parsedown-extra": "~0.2.6"}}, {"version": "v2.0.5", "version_normalized": "*******", "license": ["Apache-2.0"], "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "d316f586fcaf59ecdf9d2f60f26da277cd6c8e8d"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/d316f586fcaf59ecdf9d2f60f26da277cd6c8e8d", "type": "zip", "shasum": "", "reference": "d316f586fcaf59ecdf9d2f60f26da277cd6c8e8d"}, "time": "2014-10-05T14:49:46+00:00", "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown": "~1.1", "erusev/parsedown-extra": "~0.2.2"}}, {"description": "Markdown Is A Parsedown Extra Wrapper For Laravel 4.1+", "version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "2bbb6a353cc4bbc02f3bec106cdd6ae7ef816470"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/2bbb6a353cc4bbc02f3bec106cdd6ae7ef816470", "type": "zip", "shasum": "", "reference": "2bbb6a353cc4bbc02f3bec106cdd6ae7ef816470"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/master"}, "time": "2014-08-28T17:11:32+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown": "~1.0.1", "erusev/parsedown-extra": "~0.2.1"}}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "7b5c26c82b90e5a40e92f0395041929949a754e8"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/7b5c26c82b90e5a40e92f0395041929949a754e8", "type": "zip", "shasum": "", "reference": "7b5c26c82b90e5a40e92f0395041929949a754e8"}, "time": "2014-08-18T11:48:59+00:00", "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown": "1.0.*", "erusev/parsedown-extra": "0.1.*"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "ec32465adc838e3659ee0c62de57d30611a4bd0d"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/ec32465adc838e3659ee0c62de57d30611a4bd0d", "type": "zip", "shasum": "", "reference": "ec32465adc838e3659ee0c62de57d30611a4bd0d"}, "time": "2014-08-09T15:15:57+00:00", "require": {"php": ">=5.4.7", "illuminate/support": "~4.1", "illuminate/view": "~4.1", "erusev/parsedown-extra": "~0.1"}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "35a0211fd91c43cee7058aa2fb7c871b33921fd1"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/35a0211fd91c43cee7058aa2fb7c871b33921fd1", "type": "zip", "shasum": "", "reference": "35a0211fd91c43cee7058aa2fb7c871b33921fd1"}, "time": "2014-08-02T20:54:46+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "1dc33d8cef79c694c011a352e018122ad88546e2"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/1dc33d8cef79c694c011a352e018122ad88546e2", "type": "zip", "shasum": "", "reference": "1dc33d8cef79c694c011a352e018122ad88546e2"}, "time": "2014-07-20T21:49:39+00:00"}, {"version": "v2.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "126abdcfdd1cef998941d988954fb87197c30eb8"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/126abdcfdd1cef998941d988954fb87197c30eb8", "type": "zip", "shasum": "", "reference": "126abdcfdd1cef998941d988954fb87197c30eb8"}, "time": "2014-07-16T11:07:13+00:00"}, {"description": "Markdown Is A Simple PHP Markdown Wrapper For Laravel 4.1", "keywords": ["framework", "markdown", "laravel", "<PERSON>", "Graham<PERSON><PERSON><PERSON>", "php markdown", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markdown wrapper", "php markdown wrapper"], "version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "4ac8f8bbce7edf9254a304302b4b1efb7abdf8dc"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/4ac8f8bbce7edf9254a304302b4b1efb7abdf8dc", "type": "zip", "shasum": "", "reference": "4ac8f8bbce7edf9254a304302b4b1efb7abdf8dc"}, "time": "2014-04-21T09:47:03+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": ">=5.4.7", "laravel/framework": "4.1.*", "symfony/config": "2.4.*", "symfony/event-dispatcher": "2.4.*", "symfony/filesystem": "2.4.*", "nazar-pc/php-markdown-next": "2.0.*"}, "require-dev": {"graham-campbell/testbench": "0.3.*"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "d69737cf04ef3ff787f73e1452b46a8239f6e482"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/d69737cf04ef3ff787f73e1452b46a8239f6e482", "type": "zip", "shasum": "", "reference": "d69737cf04ef3ff787f73e1452b46a8239f6e482"}, "time": "2014-02-12T22:14:28+00:00", "autoload": {"psr-4": {"GrahamCampbell\\Markdown\\": "src/", "GrahamCampbell\\Tests\\Markdown\\": "tests/"}}, "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=5.4.7", "laravel/framework": "4.1.*", "symfony/config": "2.4.*", "symfony/filesystem": "2.4.*", "nazar-pc/php-markdown-next": "2.0.*"}, "require-dev": {"graham-campbell/testbench": "0.2.*"}}, {"version": "v1.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "9b0afb02ea1d6ab070789bfbeb085365704f2dcb"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/9b0afb02ea1d6ab070789bfbeb085365704f2dcb", "type": "zip", "shasum": "", "reference": "9b0afb02ea1d6ab070789bfbeb085365704f2dcb"}, "time": "2014-02-08T20:18:10+00:00"}, {"version": "v1.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "2c9dea2130ccd18fc1a03831ad17ebbc502f8326"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/2c9dea2130ccd18fc1a03831ad17ebbc502f8326", "type": "zip", "shasum": "", "reference": "2c9dea2130ccd18fc1a03831ad17ebbc502f8326"}, "time": "2014-01-21T23:11:58+00:00", "autoload": {"psr-0": {"GrahamCampbell\\Markdown": "src/", "GrahamCampbell\\Tests\\Markdown": "tests/"}}}, {"description": "Markdown Is A Simple PHP Markdown Wrapper For Laravel 4", "version": "v0.2.0-alpha", "version_normalized": "*******-alpha", "license": ["Apache License 2.0"], "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "6073171f3d5c5544138b95099b6107e0f3e670f2"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/6073171f3d5c5544138b95099b6107e0f3e670f2", "type": "zip", "shasum": "", "reference": "6073171f3d5c5544138b95099b6107e0f3e670f2"}, "time": "2013-12-12T00:34:54+00:00", "autoload": {"psr-0": {"GrahamCampbell\\Markdown": "src/"}}, "require": {"php": ">=5.3.3", "orchestra/testbench": "2.0.*|2.1.*", "michelf/php-markdown": "1.4.*"}, "require-dev": {"mockery/mockery": "0.8.*", "phpunit/phpunit": "3.7.*"}, "extra": "__unset"}, {"description": "Markdown Is A Simple Parsedown Wrapper For Laravel 4", "keywords": ["framework", "markdown", "laravel", "<PERSON>", "Graham<PERSON><PERSON><PERSON>", "parsedown", "parsedown wrapper", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "version": "v0.1.0-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/GrahamCampbell/Laravel-Markdown.git", "type": "git", "reference": "160ffd01efe56f204612d7919ea4d8d1a0328fe5"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/<PERSON><PERSON>-<PERSON>down/zipball/160ffd01efe56f204612d7919ea4d8d1a0328fe5", "type": "zip", "shasum": "", "reference": "160ffd01efe56f204612d7919ea4d8d1a0328fe5"}, "support": {"issues": "https://github.com/GrahamCampbell/<PERSON>vel-Markdown/issues", "source": "https://github.com/GrahamCampbell/Laravel-Markdown/tree/V0.1.0-alpha"}, "time": "2013-12-10T22:37:03+00:00", "require": {"php": ">=5.3.3", "orchestra/testbench": "~2", "erusev/parsedown": "0.7.*"}, "require-dev": {"mockery/mockery": "0.8.*@stable", "phpunit/phpunit": "3.7.*@stable"}}]}, "security-advisories": [], "last-modified": "Sun, 02 Mar 2025 23:42:22 GMT"}