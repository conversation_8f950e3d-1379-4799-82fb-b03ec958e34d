{"minified": "composer/2.0", "packages": {"symfony/mailer": [{"name": "symfony/mailer", "description": "Helps sending emails", "keywords": [], "homepage": "https://symfony.com", "version": "v7.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b5db5105b290bdbea5ab27b89c69effcf1cb3368"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b5db5105b290bdbea5ab27b89c69effcf1cb3368", "type": "zip", "shasum": "", "reference": "b5db5105b290bdbea5ab27b89c69effcf1cb3368"}, "type": "library", "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0f375bbbde96ae8c78e4aa3e63aabd486e33364c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0f375bbbde96ae8c78e4aa3e63aabd486e33364c", "type": "zip", "shasum": "", "reference": "0f375bbbde96ae8c78e4aa3e63aabd486e33364c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.0"}, "time": "2025-04-04T09:51:09+00:00"}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.0-BETA1"}}, {"version": "v7.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3de468732617dbefa23738240349e3eebe5e837c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3de468732617dbefa23738240349e3eebe5e837c", "type": "zip", "shasum": "", "reference": "3de468732617dbefa23738240349e3eebe5e837c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.8"}, "time": "2025-06-27T19:53:16+00:00"}, {"version": "v7.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "998692469d6e698c6eadc7ef37a6530a9eabb356"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/998692469d6e698c6eadc7ef37a6530a9eabb356", "type": "zip", "shasum": "", "reference": "998692469d6e698c6eadc7ef37a6530a9eabb356"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.6"}, "time": "2025-04-04T09:50:51+00:00"}, {"version": "v7.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f3871b182c44997cf039f3b462af4a48fb85f9d3"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f3871b182c44997cf039f3b462af4a48fb85f9d3", "type": "zip", "shasum": "", "reference": "f3871b182c44997cf039f3b462af4a48fb85f9d3"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.3"}, "time": "2025-01-27T11:08:17+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e4d358702fb66e4c8a2af08e90e7271a62de39cc"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e4d358702fb66e4c8a2af08e90e7271a62de39cc", "type": "zip", "shasum": "", "reference": "e4d358702fb66e4c8a2af08e90e7271a62de39cc"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.0"}, "time": "2024-11-25T15:21:05+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "abbcc8b84a81d08ee1a0e6ca60a90d0aaf24e79b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/abbcc8b84a81d08ee1a0e6ca60a90d0aaf24e79b", "type": "zip", "shasum": "", "reference": "abbcc8b84a81d08ee1a0e6ca60a90d0aaf24e79b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.0-RC1"}, "time": "2024-11-09T06:58:08+00:00"}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "a305f46bb8bd635620dcdcec485b2f52aa1345d7"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/a305f46bb8bd635620dcdcec485b2f52aa1345d7", "type": "zip", "shasum": "", "reference": "a305f46bb8bd635620dcdcec485b2f52aa1345d7"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.0-BETA1"}, "time": "2024-10-06T12:00:20+00:00"}, {"version": "v7.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e3790ddd7448cc6797fbd06749db70d147992321"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e3790ddd7448cc6797fbd06749db70d147992321", "type": "zip", "shasum": "", "reference": "e3790ddd7448cc6797fbd06749db70d147992321"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.11"}, "time": "2025-01-27T10:57:12+00:00", "require": {"php": ">=8.2", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "69c9948451fb3a6a4d47dc8261d1794734e76cdd"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/69c9948451fb3a6a4d47dc8261d1794734e76cdd", "type": "zip", "shasum": "", "reference": "69c9948451fb3a6a4d47dc8261d1794734e76cdd"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bbf21460c56f29810da3df3e206e38dfbb01e80b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bbf21460c56f29810da3df3e206e38dfbb01e80b", "type": "zip", "shasum": "", "reference": "bbf21460c56f29810da3df3e206e38dfbb01e80b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.5"}, "time": "2024-09-08T12:32:26+00:00"}, {"version": "v7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "8fcff0af9043c8f8a8e229437cea363e282f9aee"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/8fcff0af9043c8f8a8e229437cea363e282f9aee", "type": "zip", "shasum": "", "reference": "8fcff0af9043c8f8a8e229437cea363e282f9aee"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.2"}, "time": "2024-06-28T08:00:31+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "2eaad2e167cae930f25a3d731fec8b2ded5e751e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/2eaad2e167cae930f25a3d731fec8b2ded5e751e", "type": "zip", "shasum": "", "reference": "2eaad2e167cae930f25a3d731fec8b2ded5e751e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "1528f3fb85d1cbed8bf68a19d5428de662c29d7e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/1528f3fb85d1cbed8bf68a19d5428de662c29d7e", "type": "zip", "shasum": "", "reference": "1528f3fb85d1cbed8bf68a19d5428de662c29d7e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.0"}, "time": "2024-05-31T07:45:05+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6a8f4907e0d3b1dee97400c6034c03918da7c4f1"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6a8f4907e0d3b1dee97400c6034c03918da7c4f1", "type": "zip", "shasum": "", "reference": "6a8f4907e0d3b1dee97400c6034c03918da7c4f1"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.0-RC1"}, "time": "2024-05-17T10:55:18+00:00"}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "d891fb20d441bc82237e50b0042afa8859b2666a"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/d891fb20d441bc82237e50b0042afa8859b2666a", "type": "zip", "shasum": "", "reference": "d891fb20d441bc82237e50b0042afa8859b2666a"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.0-BETA1"}, "time": "2024-05-02T08:41:59+00:00"}, {"version": "v7.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6cf146bc806c6ce8999a91072c1841004c0ecc86"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6cf146bc806c6ce8999a91072c1841004c0ecc86", "type": "zip", "shasum": "", "reference": "6cf146bc806c6ce8999a91072c1841004c0ecc86"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.9"}, "time": "2024-06-28T07:59:17+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "644c2b5ddd778666298d73c1a8c0949f74ef6649"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/644c2b5ddd778666298d73c1a8c0949f74ef6649", "type": "zip", "shasum": "", "reference": "644c2b5ddd778666298d73c1a8c0949f74ef6649"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "4ff41a7c7998a88cfdc31b5841ef64d9246fc56a"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/4ff41a7c7998a88cfdc31b5841ef64d9246fc56a", "type": "zip", "shasum": "", "reference": "4ff41a7c7998a88cfdc31b5841ef64d9246fc56a"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "eb0c3187c7ddfde12d8aa0e1fa5fb29e730a41e0"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/eb0c3187c7ddfde12d8aa0e1fa5fb29e730a41e0", "type": "zip", "shasum": "", "reference": "eb0c3187c7ddfde12d8aa0e1fa5fb29e730a41e0"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.6"}, "time": "2024-03-28T09:20:36+00:00"}, {"version": "v7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "72e16d87bf50a3ce195b9470c06bb9d7b816ea85"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/72e16d87bf50a3ce195b9470c06bb9d7b816ea85", "type": "zip", "shasum": "", "reference": "72e16d87bf50a3ce195b9470c06bb9d7b816ea85"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.4"}, "time": "2024-02-03T21:34:19+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "2f71c0f6d62d28784783fdc5477e19dd57065d78"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/2f71c0f6d62d28784783fdc5477e19dd57065d78", "type": "zip", "shasum": "", "reference": "2f71c0f6d62d28784783fdc5477e19dd57065d78"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.3"}, "time": "2024-01-29T15:41:16+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c51c8f411062ef8fec837c76b0dad15dd5a6ab16"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c51c8f411062ef8fec837c76b0dad15dd5a6ab16", "type": "zip", "shasum": "", "reference": "c51c8f411062ef8fec837c76b0dad15dd5a6ab16"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.2"}, "time": "2023-12-19T11:23:03+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "5a0ff09429d34763a5569596b1793df1f07ac2d0"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/5a0ff09429d34763a5569596b1793df1f07ac2d0", "type": "zip", "shasum": "", "reference": "5a0ff09429d34763a5569596b1793df1f07ac2d0"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.0"}, "time": "2023-11-14T09:46:33+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "aa2f6d984ba55e5edca39a9bdda6713392b24a5d"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/aa2f6d984ba55e5edca39a9bdda6713392b24a5d", "type": "zip", "shasum": "", "reference": "aa2f6d984ba55e5edca39a9bdda6713392b24a5d"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.0-BETA3"}, "time": "2023-11-07T15:10:37+00:00"}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "8871442fc9a3406a3fd73bef4977d4d6d2826924"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/8871442fc9a3406a3fd73bef4977d4d6d2826924", "type": "zip", "shasum": "", "reference": "8871442fc9a3406a3fd73bef4977d4d6d2826924"}, "support": {"source": "https://github.com/symfony/mailer/tree/v7.0.0-BETA1"}, "time": "2023-10-02T10:37:53+00:00"}, {"version": "v6.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "a480322ddf8e54de262c9bca31fdcbe26b553de5"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/a480322ddf8e54de262c9bca31fdcbe26b553de5", "type": "zip", "shasum": "", "reference": "a480322ddf8e54de262c9bca31fdcbe26b553de5"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.23"}, "time": "2025-06-26T21:24:02+00:00", "require": {"php": ">=8.1", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}}, {"version": "v6.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/ada2809ccd4ec27aba9fc344e3efdaec624c6438", "type": "zip", "shasum": "", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.21"}, "time": "2025-04-26T23:47:35+00:00"}, {"version": "v6.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e93a6ae2767d7f7578c2b7961d9d8e27580b2b11"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e93a6ae2767d7f7578c2b7961d9d8e27580b2b11", "type": "zip", "shasum": "", "reference": "e93a6ae2767d7f7578c2b7961d9d8e27580b2b11"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.18"}, "time": "2025-01-24T15:27:15+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c2f7e0d8d7ac8fe25faccf5d8cac462805db2663"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c2f7e0d8d7ac8fe25faccf5d8cac462805db2663", "type": "zip", "shasum": "", "reference": "c2f7e0d8d7ac8fe25faccf5d8cac462805db2663"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b6a25408c569ae2366b3f663a4edad19420a9c26"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b6a25408c569ae2366b3f663a4edad19420a9c26", "type": "zip", "shasum": "", "reference": "b6a25408c569ae2366b3f663a4edad19420a9c26"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.12"}, "time": "2024-09-08T12:30:05+00:00"}, {"version": "v6.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e2d56f180f5b8c5e7c0fbea872bb1f529b6d6d45"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e2d56f180f5b8c5e7c0fbea872bb1f529b6d6d45", "type": "zip", "shasum": "", "reference": "e2d56f180f5b8c5e7c0fbea872bb1f529b6d6d45"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.9"}, "time": "2024-06-28T07:59:05+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "76326421d44c07f7824b19487cfbf87870b37efc"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/76326421d44c07f7824b19487cfbf87870b37efc", "type": "zip", "shasum": "", "reference": "76326421d44c07f7824b19487cfbf87870b37efc"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "2c446d4e446995bed983c0b5bb9ff837e8de7dbd"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/2c446d4e446995bed983c0b5bb9ff837e8de7dbd", "type": "zip", "shasum": "", "reference": "2c446d4e446995bed983c0b5bb9ff837e8de7dbd"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "677f34a6f4b4559e08acf73ae0aec460479e5859"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/677f34a6f4b4559e08acf73ae0aec460479e5859", "type": "zip", "shasum": "", "reference": "677f34a6f4b4559e08acf73ae0aec460479e5859"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.6"}, "time": "2024-03-27T21:14:17+00:00"}, {"version": "v6.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "791c5d31a8204cf3db0c66faab70282307f4376b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/791c5d31a8204cf3db0c66faab70282307f4376b", "type": "zip", "shasum": "", "reference": "791c5d31a8204cf3db0c66faab70282307f4376b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.4"}, "time": "2024-02-03T21:33:47+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "74412c62f88a85a41b61f0b71ab0afcaad6f03ee"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/74412c62f88a85a41b61f0b71ab0afcaad6f03ee", "type": "zip", "shasum": "", "reference": "74412c62f88a85a41b61f0b71ab0afcaad6f03ee"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.3"}, "time": "2024-01-29T15:01:07+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6da89e5c9202f129717a770a03183fb140720168"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6da89e5c9202f129717a770a03183fb140720168", "type": "zip", "shasum": "", "reference": "6da89e5c9202f129717a770a03183fb140720168"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.2"}, "time": "2023-12-19T09:12:31+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "ca8dcf8892cdc5b4358ecf2528429bb5e706f7ba"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/ca8dcf8892cdc5b4358ecf2528429bb5e706f7ba", "type": "zip", "shasum": "", "reference": "ca8dcf8892cdc5b4358ecf2528429bb5e706f7ba"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.0"}, "time": "2023-11-12T18:02:22+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "aa9865cafee4200a1088ad8bc456a0d8ab9d6b66"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/aa9865cafee4200a1088ad8bc456a0d8ab9d6b66", "type": "zip", "shasum": "", "reference": "aa9865cafee4200a1088ad8bc456a0d8ab9d6b66"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.0-BETA3"}, "time": "2023-11-06T17:20:05+00:00"}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c445b16d85e5f9daab3f220699f19ef649ade5cc"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c445b16d85e5f9daab3f220699f19ef649ade5cc", "type": "zip", "shasum": "", "reference": "c445b16d85e5f9daab3f220699f19ef649ade5cc"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.0-BETA1"}, "time": "2023-10-01T08:43:16+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3cbb745658179fb1a68ba87a4a4f16ee99dcb821"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3cbb745658179fb1a68ba87a4a4f16ee99dcb821", "type": "zip", "shasum": "", "reference": "3cbb745658179fb1a68ba87a4a4f16ee99dcb821"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.3.12"}, "time": "2024-01-29T14:46:07+00:00", "require": {"php": ">=8.1", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^6.2", "symfony/service-contracts": "^2.5|^3"}, "require-dev": {"symfony/console": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/messenger": "^6.2", "symfony/twig-bridge": "^6.2"}}, {"version": "v6.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e35910bb1d58b8ea173bbf35729340d1c3e52bf4"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e35910bb1d58b8ea173bbf35729340d1c3e52bf4", "type": "zip", "shasum": "", "reference": "e35910bb1d58b8ea173bbf35729340d1c3e52bf4"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.3.11"}, "time": "2023-12-18T10:27:58+00:00"}, {"version": "v6.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "d89611a7830d51b5e118bca38e390dea92f9ea06"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/d89611a7830d51b5e118bca38e390dea92f9ea06", "type": "zip", "shasum": "", "reference": "d89611a7830d51b5e118bca38e390dea92f9ea06"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.3.5"}, "time": "2023-09-06T09:47:15+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "7b03d9be1dea29bfec0a6c7b603f5072a4c97435"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/7b03d9be1dea29bfec0a6c7b603f5072a4c97435", "type": "zip", "shasum": "", "reference": "7b03d9be1dea29bfec0a6c7b603f5072a4c97435"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.3.0"}, "time": "2023-05-29T12:49:39+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "dd614e6b469fdf64c14543d50bc3bb56e05455f7"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/dd614e6b469fdf64c14543d50bc3bb56e05455f7", "type": "zip", "shasum": "", "reference": "dd614e6b469fdf64c14543d50bc3bb56e05455f7"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.3.0-RC1"}, "time": "2023-04-24T14:22:26+00:00"}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mailer/tree/v6.3.0-BETA1"}}, {"version": "v6.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "47cfaeba096eec5d1aee117f2acbab8e8a8573e5"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/47cfaeba096eec5d1aee117f2acbab8e8a8573e5", "type": "zip", "shasum": "", "reference": "47cfaeba096eec5d1aee117f2acbab8e8a8573e5"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.12"}, "time": "2023-05-29T12:46:33+00:00", "require": {"php": ">=8.1", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^6.2", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}}, {"version": "v6.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bfcfa015c67e19c6fdb7ca6fe70700af1e740a17"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bfcfa015c67e19c6fdb7ca6fe70700af1e740a17", "type": "zip", "shasum": "", "reference": "bfcfa015c67e19c6fdb7ca6fe70700af1e740a17"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.8"}, "time": "2023-03-14T15:00:05+00:00"}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e4f84c633b72ec70efc50b8016871c3bc43e691e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e4f84c633b72ec70efc50b8016871c3bc43e691e", "type": "zip", "shasum": "", "reference": "e4f84c633b72ec70efc50b8016871c3bc43e691e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.7"}, "time": "2023-02-21T10:35:38+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "29729ac0b4e5113f24c39c46746bd6afb79e0aaa"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/29729ac0b4e5113f24c39c46746bd6afb79e0aaa", "type": "zip", "shasum": "", "reference": "29729ac0b4e5113f24c39c46746bd6afb79e0aaa"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.5"}, "time": "2023-01-10T18:53:53+00:00", "require-dev": {"symfony/console": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^6.2", "symfony/twig-bridge": "^6.2"}}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b355ad81f1d2987c47dcd3b04d5dce669e1e62e6"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b355ad81f1d2987c47dcd3b04d5dce669e1e62e6", "type": "zip", "shasum": "", "reference": "b355ad81f1d2987c47dcd3b04d5dce669e1e62e6"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.2"}, "time": "2022-12-14T16:11:27+00:00", "require": {"php": ">=8.1", "egulias/email-validator": "^2.1.10|^3", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^6.2", "symfony/service-contracts": "^1.1|^2|^3"}}, {"version": "v6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "a18c3dd41cfcf011e3866802e39b9ae9e541deaf"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/a18c3dd41cfcf011e3866802e39b9ae9e541deaf", "type": "zip", "shasum": "", "reference": "a18c3dd41cfcf011e3866802e39b9ae9e541deaf"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.1"}, "time": "2022-12-06T16:54:23+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "7b355fca167fa5302c77bccdfa0af4d7abc6bd8c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/7b355fca167fa5302c77bccdfa0af4d7abc6bd8c", "type": "zip", "shasum": "", "reference": "7b355fca167fa5302c77bccdfa0af4d7abc6bd8c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.0"}, "time": "2022-11-28T17:18:31+00:00", "conflict": {"symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2"}}, {"version": "v6.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.0-RC2"}}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3b584f725c07f5fcf2d83bd6111059dda14e641b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3b584f725c07f5fcf2d83bd6111059dda14e641b", "type": "zip", "shasum": "", "reference": "3b584f725c07f5fcf2d83bd6111059dda14e641b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.0-RC1"}, "time": "2022-11-04T07:42:34+00:00", "require-dev": {"symfony/console": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^6.2"}}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "062efa7e258bfbd293e682d04b172d3192e10251"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/062efa7e258bfbd293e682d04b172d3192e10251", "type": "zip", "shasum": "", "reference": "062efa7e258bfbd293e682d04b172d3192e10251"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.0-BETA2"}, "time": "2022-10-28T16:24:13+00:00"}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "252c9baa1d0912ceb32ed7f8c6fad699a244ebe8"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/252c9baa1d0912ceb32ed7f8c6fad699a244ebe8", "type": "zip", "shasum": "", "reference": "252c9baa1d0912ceb32ed7f8c6fad699a244ebe8"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.2.0-BETA1"}, "time": "2022-10-18T13:21:06+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bf9b967cfefe5a2139aa6b2d11803e5a5855aefe"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bf9b967cfefe5a2139aa6b2d11803e5a5855aefe", "type": "zip", "shasum": "", "reference": "bf9b967cfefe5a2139aa6b2d11803e5a5855aefe"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.11"}, "time": "2023-01-10T18:53:01+00:00", "require": {"php": ">=8.1", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^5.4|^6.0"}, "conflict": {"symfony/http-kernel": "<5.4"}}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "dbffeda2da8050fe6cd5661876037702860df790"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/dbffeda2da8050fe6cd5661876037702860df790", "type": "zip", "shasum": "", "reference": "dbffeda2da8050fe6cd5661876037702860df790"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.9"}, "time": "2022-12-14T16:05:20+00:00", "require": {"php": ">=8.1", "egulias/email-validator": "^2.1.10|^3", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}}, {"version": "v6.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "42eb71e4bac099cff22fef1b8eae493eb4fd058f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/42eb71e4bac099cff22fef1b8eae493eb4fd058f", "type": "zip", "shasum": "", "reference": "42eb71e4bac099cff22fef1b8eae493eb4fd058f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.8"}, "time": "2022-11-04T07:40:42+00:00"}, {"version": "v6.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "7e19813c0b43387c55665780c4caea505cc48391"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/7e19813c0b43387c55665780c4caea505cc48391", "type": "zip", "shasum": "", "reference": "7e19813c0b43387c55665780c4caea505cc48391"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.7"}, "time": "2022-10-28T16:23:08+00:00"}, {"version": "v6.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e1b32deb9efc48def0c76b876860ad36f2123e89"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e1b32deb9efc48def0c76b876860ad36f2123e89", "type": "zip", "shasum": "", "reference": "e1b32deb9efc48def0c76b876860ad36f2123e89"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.5"}, "time": "2022-08-29T06:58:39+00:00"}, {"version": "v6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "55a7cb8f8518d35e2a039daaec6e1ee20509510e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/55a7cb8f8518d35e2a039daaec6e1ee20509510e", "type": "zip", "shasum": "", "reference": "55a7cb8f8518d35e2a039daaec6e1ee20509510e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.4"}, "time": "2022-08-03T05:16:05+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b2db228a93278863d1567f90d7caf26922dbfede"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b2db228a93278863d1567f90d7caf26922dbfede", "type": "zip", "shasum": "", "reference": "b2db228a93278863d1567f90d7caf26922dbfede"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.3"}, "time": "2022-07-27T15:50:51+00:00"}, {"version": "v6.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "8fa150355115ea09238858ae3cfaf249fd1fd5ed"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/8fa150355115ea09238858ae3cfaf249fd1fd5ed", "type": "zip", "shasum": "", "reference": "8fa150355115ea09238858ae3cfaf249fd1fd5ed"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.2"}, "time": "2022-06-19T13:21:48+00:00"}, {"version": "v6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "db6a19a5c896139901c2de59fc9849379e0ff3b6"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/db6a19a5c896139901c2de59fc9849379e0ff3b6", "type": "zip", "shasum": "", "reference": "db6a19a5c896139901c2de59fc9849379e0ff3b6"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.1"}, "time": "2022-06-06T19:15:01+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bc4338d7729aafaaac8559e1a4680ee97b8bfedb"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bc4338d7729aafaaac8559e1a4680ee97b8bfedb", "type": "zip", "shasum": "", "reference": "bc4338d7729aafaaac8559e1a4680ee97b8bfedb"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.0"}, "time": "2022-04-27T17:11:01+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.0-BETA2"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "4d3727bf1521b3333aadfec01d858cfac27558ab"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/4d3727bf1521b3333aadfec01d858cfac27558ab", "type": "zip", "shasum": "", "reference": "4d3727bf1521b3333aadfec01d858cfac27558ab"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.1.0-BETA1"}, "time": "2022-04-12T16:22:53+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "cd60799210c488f545ddde2444dc1aa548322872"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/cd60799210c488f545ddde2444dc1aa548322872", "type": "zip", "shasum": "", "reference": "cd60799210c488f545ddde2444dc1aa548322872"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.19"}, "time": "2023-01-11T11:50:03+00:00", "require": {"php": ">=8.0.2", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0d4562cd13f1e5b78b578120ae5cbd5527ec1534"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0d4562cd13f1e5b78b578120ae5cbd5527ec1534", "type": "zip", "shasum": "", "reference": "0d4562cd13f1e5b78b578120ae5cbd5527ec1534"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.17"}, "time": "2022-12-14T15:52:41+00:00", "require": {"php": ">=8.0.2", "egulias/email-validator": "^2.1.10|^3", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}}, {"version": "v6.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "aa47b34ab09fa03106d9e156632e4c6176b962da"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/aa47b34ab09fa03106d9e156632e4c6176b962da", "type": "zip", "shasum": "", "reference": "aa47b34ab09fa03106d9e156632e4c6176b962da"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.16"}, "time": "2022-11-04T07:39:59+00:00"}, {"version": "v6.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "5eaa3f1404cafa842e953ae16c35757b7356fb32"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/5eaa3f1404cafa842e953ae16c35757b7356fb32", "type": "zip", "shasum": "", "reference": "5eaa3f1404cafa842e953ae16c35757b7356fb32"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.15"}, "time": "2022-10-28T16:22:58+00:00"}, {"version": "v6.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6269c872ab4792e8facbf8af27a2fbee8429f217"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6269c872ab4792e8facbf8af27a2fbee8429f217", "type": "zip", "shasum": "", "reference": "6269c872ab4792e8facbf8af27a2fbee8429f217"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.13"}, "time": "2022-08-29T06:49:22+00:00"}, {"version": "v6.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "45aad5f8cfb481130e83dc4cb867c0f576182ea9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/45aad5f8cfb481130e83dc4cb867c0f576182ea9", "type": "zip", "shasum": "", "reference": "45aad5f8cfb481130e83dc4cb867c0f576182ea9"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.12"}, "time": "2022-08-03T05:17:36+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "4a787a257addd412eac53157d459f87f8e335037"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/4a787a257addd412eac53157d459f87f8e335037", "type": "zip", "shasum": "", "reference": "4a787a257addd412eac53157d459f87f8e335037"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.11"}, "time": "2022-07-27T15:50:26+00:00"}, {"version": "v6.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9b60de35f0b4eed09ee2b25195a478b86acd128d"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9b60de35f0b4eed09ee2b25195a478b86acd128d", "type": "zip", "shasum": "", "reference": "9b60de35f0b4eed09ee2b25195a478b86acd128d"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.10"}, "time": "2022-06-19T12:07:20+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "706af6b3e99ebcbc639c9c664f5579aaa869409b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/706af6b3e99ebcbc639c9c664f5579aaa869409b", "type": "zip", "shasum": "", "reference": "706af6b3e99ebcbc639c9c664f5579aaa869409b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.8"}, "time": "2022-04-27T17:10:30+00:00"}, {"version": "v6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f7343f94e7afecca2ad840b078f9d80200e1bd27"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f7343f94e7afecca2ad840b078f9d80200e1bd27", "type": "zip", "shasum": "", "reference": "f7343f94e7afecca2ad840b078f9d80200e1bd27"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.7"}, "time": "2022-03-18T16:06:28+00:00"}, {"version": "v6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0f4772db6521a1beb44529aa2c0c1e56f671be8f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0f4772db6521a1beb44529aa2c0c1e56f671be8f", "type": "zip", "shasum": "", "reference": "0f4772db6521a1beb44529aa2c0c1e56f671be8f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.5"}, "time": "2022-02-25T10:48:52+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "d958befe7dbee9d2b2157ef6dfa9b103efa94f82"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/d958befe7dbee9d2b2157ef6dfa9b103efa94f82", "type": "zip", "shasum": "", "reference": "d958befe7dbee9d2b2157ef6dfa9b103efa94f82"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.3"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3e2ea3aa326977907fc435a2b6d406fc46e55005"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3e2ea3aa326977907fc435a2b6d406fc46e55005", "type": "zip", "shasum": "", "reference": "3e2ea3aa326977907fc435a2b6d406fc46e55005"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.2"}, "time": "2021-12-11T16:36:28+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "d0547edd8de59bb30c3fe9db14473ed1fee5ef8a"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/d0547edd8de59bb30c3fe9db14473ed1fee5ef8a", "type": "zip", "shasum": "", "reference": "d0547edd8de59bb30c3fe9db14473ed1fee5ef8a"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "18ecd88578291a7773c330ad833ede6c76ae29e3"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/18ecd88578291a7773c330ad833ede6c76ae29e3", "type": "zip", "shasum": "", "reference": "18ecd88578291a7773c330ad833ede6c76ae29e3"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.0"}, "time": "2021-11-23T19:05:29+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f5419bbbc15af7e3af5f0267961ce3ce8dc35f1c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f5419bbbc15af7e3af5f0267961ce3ce8dc35f1c", "type": "zip", "shasum": "", "reference": "f5419bbbc15af7e3af5f0267961ce3ce8dc35f1c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.0-BETA1"}, "time": "2021-11-03T13:44:55+00:00", "require": {"php": ">=8.0.2", "egulias/email-validator": "^2.1.10|^3", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2.0|^3.0"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/messenger": "^5.4|^6.0"}}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f732e1fafdf0f4a2d865e91f1018aaca174aeed9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f732e1fafdf0f4a2d865e91f1018aaca174aeed9", "type": "zip", "shasum": "", "reference": "f732e1fafdf0f4a2d865e91f1018aaca174aeed9"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3|^4", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2.6|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}, "require-dev": {"symfony/http-client": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0"}, "conflict": {"symfony/http-kernel": "<4.4"}}, {"version": "v5.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "2a5e44d984872cb161b24fc4dedba7fd7632185f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/2a5e44d984872cb161b24fc4dedba7fd7632185f", "type": "zip", "shasum": "", "reference": "2a5e44d984872cb161b24fc4dedba7fd7632185f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.44"}, "time": "2024-09-08T09:19:02+00:00"}, {"version": "v5.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "13f4fac7f8450381122f0f4cd750a63c2821c7fa"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/13f4fac7f8450381122f0f4cd750a63c2821c7fa", "type": "zip", "shasum": "", "reference": "13f4fac7f8450381122f0f4cd750a63c2821c7fa"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.41"}, "time": "2024-06-27T20:38:52+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "54fec5a552238d071502facd85c34af83a20dc38"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/54fec5a552238d071502facd85c34af83a20dc38", "type": "zip", "shasum": "", "reference": "54fec5a552238d071502facd85c34af83a20dc38"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "93543ff1554d1098b0f80bb01ff27e9da123af5c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/93543ff1554d1098b0f80bb01ff27e9da123af5c", "type": "zip", "shasum": "", "reference": "93543ff1554d1098b0f80bb01ff27e9da123af5c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "1d0ef27f1b19b9a0175a0e130d1df3113e5a130e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/1d0ef27f1b19b9a0175a0e130d1df3113e5a130e", "type": "zip", "shasum": "", "reference": "1d0ef27f1b19b9a0175a0e130d1df3113e5a130e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.38"}, "time": "2024-03-19T10:19:25+00:00"}, {"version": "v5.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b57d722f2bf6e1dc08df9c86efbfdcaaba89693b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b57d722f2bf6e1dc08df9c86efbfdcaaba89693b", "type": "zip", "shasum": "", "reference": "b57d722f2bf6e1dc08df9c86efbfdcaaba89693b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.36"}, "time": "2024-02-01T13:42:14+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "664724b0fb4646dee30859d0ed9131a2d7633320"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/664724b0fb4646dee30859d0ed9131a2d7633320", "type": "zip", "shasum": "", "reference": "664724b0fb4646dee30859d0ed9131a2d7633320"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.35"}, "time": "2024-01-29T07:33:37+00:00"}, {"version": "v5.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0d2c0e0fdd07c80d95eadcdbba6af41e9aafcfa5"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0d2c0e0fdd07c80d95eadcdbba6af41e9aafcfa5", "type": "zip", "shasum": "", "reference": "0d2c0e0fdd07c80d95eadcdbba6af41e9aafcfa5"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.34"}, "time": "2023-12-02T08:41:43+00:00"}, {"version": "v5.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "5ca8a7628a5ee69767047dd0f4cf4c9521c999b8"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/5ca8a7628a5ee69767047dd0f4cf4c9521c999b8", "type": "zip", "shasum": "", "reference": "5ca8a7628a5ee69767047dd0f4cf4c9521c999b8"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.31"}, "time": "2023-11-03T16:16:43+00:00"}, {"version": "v5.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6330cd465dfd8b7a07515757a1c37069075f7b0b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6330cd465dfd8b7a07515757a1c37069075f7b0b", "type": "zip", "shasum": "", "reference": "6330cd465dfd8b7a07515757a1c37069075f7b0b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.22"}, "time": "2023-03-10T10:15:32+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "60c5f5a29399ff591fadd99da345a4a8bf048c41"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/60c5f5a29399ff591fadd99da345a4a8bf048c41", "type": "zip", "shasum": "", "reference": "60c5f5a29399ff591fadd99da345a4a8bf048c41"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.21"}, "time": "2023-02-21T10:48:16+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "66081dc01cfc04fdea08bbd253f44627ec5591dd"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/66081dc01cfc04fdea08bbd253f44627ec5591dd", "type": "zip", "shasum": "", "reference": "66081dc01cfc04fdea08bbd253f44627ec5591dd"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.19"}, "time": "2023-01-09T05:43:46+00:00", "require-dev": {"symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^4.4|^5.0|^6.0"}}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "fd816412b76447890efedaf9ddfe8632589ce10c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/fd816412b76447890efedaf9ddfe8632589ce10c", "type": "zip", "shasum": "", "reference": "fd816412b76447890efedaf9ddfe8632589ce10c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.17"}, "time": "2022-12-14T15:45:23+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2.6|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}}, {"version": "v5.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "501ac1c388b18390547aa138253de2c5e44e931e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/501ac1c388b18390547aa138253de2c5e44e931e", "type": "zip", "shasum": "", "reference": "501ac1c388b18390547aa138253de2c5e44e931e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.16"}, "time": "2022-11-04T07:37:26+00:00"}, {"version": "v5.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "926f4deddb60d40024e6058fd8f94e70e4024930"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/926f4deddb60d40024e6058fd8f94e70e4024930", "type": "zip", "shasum": "", "reference": "926f4deddb60d40024e6058fd8f94e70e4024930"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.15"}, "time": "2022-10-27T07:55:40+00:00"}, {"version": "v5.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "63bf36a5150ac0bfed1c4d0a4e0b114a57b77e11"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/63bf36a5150ac0bfed1c4d0a4e0b114a57b77e11", "type": "zip", "shasum": "", "reference": "63bf36a5150ac0bfed1c4d0a4e0b114a57b77e11"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.13"}, "time": "2022-08-29T06:47:07+00:00"}, {"version": "v5.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "076043af11e58b20a68d2fd93f59cdbc6e8fdd00"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/076043af11e58b20a68d2fd93f59cdbc6e8fdd00", "type": "zip", "shasum": "", "reference": "076043af11e58b20a68d2fd93f59cdbc6e8fdd00"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.12"}, "time": "2022-08-03T05:17:26+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9f34f71ec05cef8a0d434988476ee9fd32075a6c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9f34f71ec05cef8a0d434988476ee9fd32075a6c", "type": "zip", "shasum": "", "reference": "9f34f71ec05cef8a0d434988476ee9fd32075a6c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.11"}, "time": "2022-07-24T16:05:20+00:00"}, {"version": "v5.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3e2e5939089938f7150ad448e23d6092338ca991"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3e2e5939089938f7150ad448e23d6092338ca991", "type": "zip", "shasum": "", "reference": "3e2e5939089938f7150ad448e23d6092338ca991"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.10"}, "time": "2022-06-19T12:03:50+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c6e7aa958cb2884d68562264f421ffea59cdad41"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c6e7aa958cb2884d68562264f421ffea59cdad41", "type": "zip", "shasum": "", "reference": "c6e7aa958cb2884d68562264f421ffea59cdad41"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.8"}, "time": "2022-04-27T17:10:22+00:00"}, {"version": "v5.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "03332035eef89557db9eb7ead4e899685d5962b9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/03332035eef89557db9eb7ead4e899685d5962b9", "type": "zip", "shasum": "", "reference": "03332035eef89557db9eb7ead4e899685d5962b9"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.7"}, "time": "2022-03-18T16:00:30+00:00"}, {"version": "v5.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f6e927ec95c957131e6b2c78790e1a6d4c576447"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f6e927ec95c957131e6b2c78790e1a6d4c576447", "type": "zip", "shasum": "", "reference": "f6e927ec95c957131e6b2c78790e1a6d4c576447"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.5"}, "time": "2022-02-25T10:48:33+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "391a2ac6bf8ab298caa7b63826edc9500412ed16"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/391a2ac6bf8ab298caa7b63826edc9500412ed16", "type": "zip", "shasum": "", "reference": "391a2ac6bf8ab298caa7b63826edc9500412ed16"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.3"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "309ba427654351dcad9691bef817b96920ebd2cf"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/309ba427654351dcad9691bef817b96920ebd2cf", "type": "zip", "shasum": "", "reference": "309ba427654351dcad9691bef817b96920ebd2cf"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.2"}, "time": "2021-12-11T16:33:38+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "2823212575ed3a6adfc5bc34ebb47b5395285048"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/2823212575ed3a6adfc5bc34ebb47b5395285048", "type": "zip", "shasum": "", "reference": "2823212575ed3a6adfc5bc34ebb47b5395285048"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.0"}, "time": "2021-11-23T10:19:22+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0c374e3357c14891aae1e9d156efd0ff0eb6ecae"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0c374e3357c14891aae1e9d156efd0ff0eb6ecae", "type": "zip", "shasum": "", "reference": "0c374e3357c14891aae1e9d156efd0ff0eb6ecae"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.0-BETA1"}, "time": "2021-11-03T09:24:47+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2.6|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2", "symfony/messenger": "^4.4|^5.0|^6.0"}}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "8ff0b648d030a7bd79cefe05104cc1d3853523d2"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/8ff0b648d030a7bd79cefe05104cc1d3853523d2", "type": "zip", "shasum": "", "reference": "8ff0b648d030a7bd79cefe05104cc1d3853523d2"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2.6", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2", "symfony/messenger": "^4.4|^5.0"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bef14df46e4570aa786bd75dcee0d5be0910553d"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bef14df46e4570aa786bd75dcee0d5be0910553d", "type": "zip", "shasum": "", "reference": "bef14df46e4570aa786bd75dcee0d5be0910553d"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.13"}, "time": "2021-12-11T14:20:54+00:00"}, {"version": "v5.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c1f83da2296741110be35dd779f2a9e412cec466"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c1f83da2296741110be35dd779f2a9e412cec466", "type": "zip", "shasum": "", "reference": "c1f83da2296741110be35dd779f2a9e412cec466"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.9"}, "time": "2021-07-23T15:55:36+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.4"}}, {"version": "v5.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6ebd500f14402344fe4369ee88bfec27a567a24d"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6ebd500f14402344fe4369ee88bfec27a567a24d", "type": "zip", "shasum": "", "reference": "6ebd500f14402344fe4369ee88bfec27a567a24d"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.3"}, "time": "2021-06-24T08:13:00+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/log": "~1.0", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2.6", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3b7f45a1f5488032da33c00f619909b4a6bf57d6"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3b7f45a1f5488032da33c00f619909b4a6bf57d6", "type": "zip", "shasum": "", "reference": "3b7f45a1f5488032da33c00f619909b4a6bf57d6"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.0"}, "time": "2021-05-27T12:56:16+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9f599b02641281b53969cf5497851dee9542bdfc"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9f599b02641281b53969cf5497851dee9542bdfc", "type": "zip", "shasum": "", "reference": "9f599b02641281b53969cf5497851dee9542bdfc"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.0-RC1"}, "time": "2021-05-12T15:01:34+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2.6", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.3.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f2dbd83553680ab18e35c51275bdfb1c6c5aa4bf"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f2dbd83553680ab18e35c51275bdfb1c6c5aa4bf", "type": "zip", "shasum": "", "reference": "f2dbd83553680ab18e35c51275bdfb1c6c5aa4bf"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.0-BETA4"}, "time": "2021-05-10T16:43:01+00:00"}, {"version": "v5.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "7d79b856eaec8837d2e94d546e70372d1a5fead0"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/7d79b856eaec8837d2e94d546e70372d1a5fead0", "type": "zip", "shasum": "", "reference": "7d79b856eaec8837d2e94d546e70372d1a5fead0"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.0-BETA2"}, "time": "2021-04-29T09:19:46+00:00", "require-dev": {"symfony/amazon-mailer": "^4.4|^5.0", "symfony/google-mailer": "^4.4|^5.0", "symfony/http-client-contracts": "^1.1|^2", "symfony/mailjet-mailer": "^4.4|^5.0", "symfony/mailgun-mailer": "^4.4|^5.0", "symfony/mailchimp-mailer": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/postmark-mailer": "^4.4|^5.0", "symfony/sendgrid-mailer": "^4.4|^5.0"}}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "4a695e493578dfc0fca33e8201dbb24910fd4818"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/4a695e493578dfc0fca33e8201dbb24910fd4818", "type": "zip", "shasum": "", "reference": "4a695e493578dfc0fca33e8201dbb24910fd4818"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.3.0-BETA1"}, "time": "2021-04-16T17:25:45+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "75ea7f9e54c1bc49b6d7e3d6b20422f85b7ea3e4"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/75ea7f9e54c1bc49b6d7e3d6b20422f85b7ea3e4", "type": "zip", "shasum": "", "reference": "75ea7f9e54c1bc49b6d7e3d6b20422f85b7ea3e4"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.12"}, "time": "2021-07-23T15:54:19+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2.6", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3f357d259cfb7928823f314766a44f4f9e49f682"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3f357d259cfb7928823f314766a44f4f9e49f682", "type": "zip", "shasum": "", "reference": "3f357d259cfb7928823f314766a44f4f9e49f682"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.11"}, "time": "2021-06-24T08:07:28+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2.6", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9b4d874082b337759ce7c4ef608ecf63982a4472"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9b4d874082b337759ce7c4ef608ecf63982a4472", "type": "zip", "shasum": "", "reference": "9b4d874082b337759ce7c4ef608ecf63982a4472"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.10"}, "time": "2021-05-27T12:55:18+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c50b304246e56c8574c203bce440f0eafb107ae9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c50b304246e56c8574c203bce440f0eafb107ae9", "type": "zip", "shasum": "", "reference": "c50b304246e56c8574c203bce440f0eafb107ae9"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.7"}, "time": "2021-04-23T11:25:09+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "04475b8368b6c7a559581ee8a0650c919ec79274"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/04475b8368b6c7a559581ee8a0650c919ec79274", "type": "zip", "shasum": "", "reference": "04475b8368b6c7a559581ee8a0650c919ec79274"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.6"}, "time": "2021-03-12T13:18:39+00:00"}, {"version": "v5.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9cc469d0ca8fe0e7d46089bdb06793259817f2c9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9cc469d0ca8fe0e7d46089bdb06793259817f2c9", "type": "zip", "shasum": "", "reference": "9cc469d0ca8fe0e7d46089bdb06793259817f2c9"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.5"}, "time": "2021-03-07T16:08:20+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10|^3", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0ca4de7d25e520dfaf59da41c02381264f285d7d"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0ca4de7d25e520dfaf59da41c02381264f285d7d", "type": "zip", "shasum": "", "reference": "0ca4de7d25e520dfaf59da41c02381264f285d7d"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.4"}, "time": "2021-02-22T15:48:39+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^5.2", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "1efa11a8f59b8ba706aa6ee112c4675dce4dccf6"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/1efa11a8f59b8ba706aa6ee112c4675dce4dccf6", "type": "zip", "shasum": "", "reference": "1efa11a8f59b8ba706aa6ee112c4675dce4dccf6"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.3"}, "time": "2021-02-02T06:10:15+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "eeeabec5511d14aebba1808da959a3e31375e1f4"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/eeeabec5511d14aebba1808da959a3e31375e1f4", "type": "zip", "shasum": "", "reference": "eeeabec5511d14aebba1808da959a3e31375e1f4"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.2"}, "time": "2021-01-27T10:15:41+00:00"}, {"description": "Symfony Mailer Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3f34fa977efca75ad17f1416ecb4605f27dbb75e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3f34fa977efca75ad17f1416ecb4605f27dbb75e", "type": "zip", "shasum": "", "reference": "3f34fa977efca75ad17f1416ecb4605f27dbb75e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.1"}, "time": "2020-12-18T08:03:05+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f30fe13d4c9049f3c1f63be2c7f580779b058522"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f30fe13d4c9049f3c1f63be2c7f580779b058522", "type": "zip", "shasum": "", "reference": "f30fe13d4c9049f3c1f63be2c7f580779b058522"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.0"}, "time": "2020-10-28T21:46:03+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.0-RC2"}}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9e6571926775a09fa70c9f15788c1b03e05724c4"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9e6571926775a09fa70c9f15788c1b03e05724c4", "type": "zip", "shasum": "", "reference": "9e6571926775a09fa70c9f15788c1b03e05724c4"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:08:07+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "d5f59487d1197fc30e13dbb5d8da2d143040d405"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/d5f59487d1197fc30e13dbb5d8da2d143040d405", "type": "zip", "shasum": "", "reference": "d5f59487d1197fc30e13dbb5d8da2d143040d405"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b5ad65c2f222e35b1c273ebc9bc8c39092b95001"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b5ad65c2f222e35b1c273ebc9bc8c39092b95001", "type": "zip", "shasum": "", "reference": "b5ad65c2f222e35b1c273ebc9bc8c39092b95001"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.2.0-BETA1"}, "time": "2020-09-24T14:10:25+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Helps sending emails", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3c7ab7a402acdb453dcdd6e0b2982caacfcc9b9f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3c7ab7a402acdb453dcdd6e0b2982caacfcc9b9f", "type": "zip", "shasum": "", "reference": "3c7ab7a402acdb453dcdd6e0b2982caacfcc9b9f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "require-dev": {"symfony/amazon-mailer": "^4.4|^5.0", "symfony/google-mailer": "^4.4|^5.0", "symfony/http-client-contracts": "^1.1|^2", "symfony/mailgun-mailer": "^4.4|^5.0", "symfony/mailchimp-mailer": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/postmark-mailer": "^4.4|^5.0", "symfony/sendgrid-mailer": "^4.4|^5.0"}, "extra": "__unset"}, {"description": "Symfony Mailer Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bc6c244e27dbb1d843565b9905e50f318a9e675c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bc6c244e27dbb1d843565b9905e50f318a9e675c", "type": "zip", "shasum": "", "reference": "bc6c244e27dbb1d843565b9905e50f318a9e675c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.10"}, "time": "2020-12-18T08:02:46+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "a8be34b60e81c54f764f5ecee2a143b1dfbc60dc"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/a8be34b60e81c54f764f5ecee2a143b1dfbc60dc", "type": "zip", "shasum": "", "reference": "a8be34b60e81c54f764f5ecee2a143b1dfbc60dc"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.9"}, "time": "2020-10-28T21:31:18+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "fa5cc9f894a5d082e7e46bfdd44f5dd83529f0ba"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/fa5cc9f894a5d082e7e46bfdd44f5dd83529f0ba", "type": "zip", "shasum": "", "reference": "fa5cc9f894a5d082e7e46bfdd44f5dd83529f0ba"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "0c4f93173b7e315f4035c401b8ddfa9b149b389c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/0c4f93173b7e315f4035c401b8ddfa9b149b389c", "type": "zip", "shasum": "", "reference": "0c4f93173b7e315f4035c401b8ddfa9b149b389c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.7"}, "time": "2020-09-07T05:10:28+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.6"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f728de093c1f5360c52a6a929e70852a6c3fb2dc"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f728de093c1f5360c52a6a929e70852a6c3fb2dc", "type": "zip", "shasum": "", "reference": "f728de093c1f5360c52a6a929e70852a6c3fb2dc"}, "support": {"source": "https://github.com/symfony/mailer/tree/5.1"}, "time": "2020-08-31T19:52:58+00:00"}, {"version": "v5.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f1db70ed62033aa27b9db1bbf14dba9004bea61a"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f1db70ed62033aa27b9db1bbf14dba9004bea61a", "type": "zip", "shasum": "", "reference": "f1db70ed62033aa27b9db1bbf14dba9004bea61a"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.4"}, "time": "2020-08-21T09:48:22+00:00"}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "90c5023ca4be2d2f403a1b6e068395c516a97fce"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/90c5023ca4be2d2f403a1b6e068395c516a97fce", "type": "zip", "shasum": "", "reference": "90c5023ca4be2d2f403a1b6e068395c516a97fce"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.3"}, "time": "2020-07-20T14:15:11+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "203f236f1c2f4d8980f3c4120c8032c8cfe1e893"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/203f236f1c2f4d8980f3c4120c8032c8cfe1e893", "type": "zip", "shasum": "", "reference": "203f236f1c2f4d8980f3c4120c8032c8cfe1e893"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.2"}, "time": "2020-06-11T21:20:02+00:00"}, {"version": "v5.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/5.1"}}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "7308b053e16a4f43e2aaf32dc11f72174f59247e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/7308b053e16a4f43e2aaf32dc11f72174f59247e", "type": "zip", "shasum": "", "reference": "7308b053e16a4f43e2aaf32dc11f72174f59247e"}, "time": "2020-05-30T20:35:19+00:00"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "58ce3efd337b2dd0708366683ed08b8ac8d59200"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/58ce3efd337b2dd0708366683ed08b8ac8d59200", "type": "zip", "shasum": "", "reference": "58ce3efd337b2dd0708366683ed08b8ac8d59200"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bcd3b3980cd528a13f74824933d8a85c18d9e054"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bcd3b3980cd528a13f74824933d8a85c18d9e054", "type": "zip", "shasum": "", "reference": "bcd3b3980cd528a13f74824933d8a85c18d9e054"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.1.0-BETA1"}, "time": "2020-05-04T15:54:21+00:00", "require": {"php": "^7.2.5", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "ae0579ff80c1f9b6db5a7a7053733b2568cb9001"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/ae0579ff80c1f9b6db5a7a7053733b2568cb9001", "type": "zip", "shasum": "", "reference": "ae0579ff80c1f9b6db5a7a7053733b2568cb9001"}, "support": {"source": "https://github.com/symfony/mailer/tree/5.0"}, "time": "2020-07-15T10:53:08+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "55a7735cfb7a6b11c322aac2fd93b11e688da870"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/55a7735cfb7a6b11c322aac2fd93b11e688da870", "type": "zip", "shasum": "", "reference": "55a7735cfb7a6b11c322aac2fd93b11e688da870"}, "time": "2020-06-11T21:19:34+00:00"}, {"version": "v5.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "99689159f32cfa71027cc92e47c0fbb373f0aba0"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/99689159f32cfa71027cc92e47c0fbb373f0aba0", "type": "zip", "shasum": "", "reference": "99689159f32cfa71027cc92e47c0fbb373f0aba0"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.0.9"}, "time": "2020-05-30T20:12:43+00:00"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b473af272a4375d832d060b2bc9aac185536443d"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b473af272a4375d832d060b2bc9aac185536443d", "type": "zip", "shasum": "", "reference": "b473af272a4375d832d060b2bc9aac185536443d"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.0.8"}, "time": "2020-04-28T17:58:55+00:00", "require": {"php": "^7.2.5", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e0a736fc8b6839f5e6428f6aa4685e5aa033208c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e0a736fc8b6839f5e6428f6aa4685e5aa033208c", "type": "zip", "shasum": "", "reference": "e0a736fc8b6839f5e6428f6aa4685e5aa033208c"}, "support": {"source": "https://github.com/symfony/mailer/tree/5.0"}, "time": "2020-03-27T16:56:45+00:00"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "cd70cc2c0778b2e86c5c32263ff89433f528e740"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/cd70cc2c0778b2e86c5c32263ff89433f528e740", "type": "zip", "shasum": "", "reference": "cd70cc2c0778b2e86c5c32263ff89433f528e740"}, "time": "2020-03-16T12:10:54+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "fd0da3996c6fe31b76a354ac749a864522308243"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/fd0da3996c6fe31b76a354ac749a864522308243", "type": "zip", "shasum": "", "reference": "fd0da3996c6fe31b76a354ac749a864522308243"}, "funding": [], "time": "2020-02-08T17:00:58+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9410890bfe9b8700312b4e04ac38819b66e3b907"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9410890bfe9b8700312b4e04ac38819b66e3b907", "type": "zip", "shasum": "", "reference": "9410890bfe9b8700312b4e04ac38819b66e3b907"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.0.4"}, "time": "2020-01-04T14:08:26+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/5.0"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "82644dc053ccf80e2d67fd79976a2c0d08bb53c2"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/82644dc053ccf80e2d67fd79976a2c0d08bb53c2", "type": "zip", "shasum": "", "reference": "82644dc053ccf80e2d67fd79976a2c0d08bb53c2"}, "time": "2019-12-07T16:40:37+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f7478f49f27d8c55582dcb482f1ca8a90e0dfa30"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f7478f49f27d8c55582dcb482f1ca8a90e0dfa30", "type": "zip", "shasum": "", "reference": "f7478f49f27d8c55582dcb482f1ca8a90e0dfa30"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.0.1"}, "time": "2019-12-01T08:48:26+00:00"}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "43a7b522213a5d39267269f180fe00a235d17ae2"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/43a7b522213a5d39267269f180fe00a235d17ae2", "type": "zip", "shasum": "", "reference": "43a7b522213a5d39267269f180fe00a235d17ae2"}, "support": {"source": "https://github.com/symfony/mailer/tree/5.0"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "16e8c273cc337477c719a7cb01a0513e9c450276"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/16e8c273cc337477c719a7cb01a0513e9c450276", "type": "zip", "shasum": "", "reference": "16e8c273cc337477c719a7cb01a0513e9c450276"}, "support": {"source": "https://github.com/symfony/mailer/tree/v5.0.0-RC1"}, "time": "2019-11-16T15:24:47+00:00", "require": {"php": "^7.2.9", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "681397ff4079d888933feef33c8339fb357d41af"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/681397ff4079d888933feef33c8339fb357d41af", "type": "zip", "shasum": "", "reference": "681397ff4079d888933feef33c8339fb357d41af"}, "support": {"source": "https://github.com/symfony/mailer/tree/master"}, "time": "2019-11-12T14:58:10+00:00"}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mailer/tree/v5.0.0-BETA1"}}, {"description": "Helps sending emails", "version": "v4.4.49", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "554b8c0dc2db9d74e760fd6b726f527364f03302"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/554b8c0dc2db9d74e760fd6b726f527364f03302", "type": "zip", "shasum": "", "reference": "554b8c0dc2db9d74e760fd6b726f527364f03302"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-04T06:30:35+00:00", "require": {"php": ">=7.1.3", "egulias/email-validator": "^2.1.10|^3", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.4.21|^5.2.6", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/http-kernel": "<4.4", "symfony/sendgrid-mailer": "<4.4"}, "extra": "__unset"}, {"version": "v4.4.48", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "3fe0ba37c541cc0d32456781a1c9563fada2174f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/3fe0ba37c541cc0d32456781a1c9563fada2174f", "type": "zip", "shasum": "", "reference": "3fe0ba37c541cc0d32456781a1c9563fada2174f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.48"}, "time": "2022-10-10T16:46:39+00:00"}, {"version": "v4.4.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "877cfb31a1589dcfee525ce82475f76ef20fbb30"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/877cfb31a1589dcfee525ce82475f76ef20fbb30", "type": "zip", "shasum": "", "reference": "877cfb31a1589dcfee525ce82475f76ef20fbb30"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.46"}, "time": "2022-08-27T06:29:57+00:00"}, {"version": "v4.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f1d82847aa98972aada1ed4a0a3bdb6b93c31db1"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f1d82847aa98972aada1ed4a0a3bdb6b93c31db1", "type": "zip", "shasum": "", "reference": "f1d82847aa98972aada1ed4a0a3bdb6b93c31db1"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.45"}, "time": "2022-08-02T17:05:58+00:00"}, {"version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "5f514a86368c53cf0d6c67aa40f7771fdf1ceb0c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/5f514a86368c53cf0d6c67aa40f7771fdf1ceb0c", "type": "zip", "shasum": "", "reference": "5f514a86368c53cf0d6c67aa40f7771fdf1ceb0c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"version": "v4.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c93adcadbc7f2b483902f4961ab23dd31528669f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c93adcadbc7f2b483902f4961ab23dd31528669f", "type": "zip", "shasum": "", "reference": "c93adcadbc7f2b483902f4961ab23dd31528669f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.43"}, "time": "2022-05-16T21:33:45+00:00"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "62d909879078b31f338b26a2701c8f8802343769"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/62d909879078b31f338b26a2701c8f8802343769", "type": "zip", "shasum": "", "reference": "62d909879078b31f338b26a2701c8f8802343769"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.41"}, "time": "2022-04-27T04:12:05+00:00"}, {"version": "v4.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "e3304841fd41e1e59225919ed1a7e50986a4cb6f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/e3304841fd41e1e59225919ed1a7e50986a4cb6f", "type": "zip", "shasum": "", "reference": "e3304841fd41e1e59225919ed1a7e50986a4cb6f"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.40"}, "time": "2022-03-18T08:12:19+00:00"}, {"version": "v4.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "cb843f81cbe16bfd2e17e23d619adb2dddfd0bde"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/cb843f81cbe16bfd2e17e23d619adb2dddfd0bde", "type": "zip", "shasum": "", "reference": "cb843f81cbe16bfd2e17e23d619adb2dddfd0bde"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.38"}, "time": "2022-02-25T07:51:37+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "603703c41cc3765156b22143b7a585dd10e4cfb6"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/603703c41cc3765156b22143b7a585dd10e4cfb6", "type": "zip", "shasum": "", "reference": "603703c41cc3765156b22143b7a585dd10e4cfb6"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "7c58943257767a0a031758e6507b5e2bff8ed3c9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/7c58943257767a0a031758e6507b5e2bff8ed3c9", "type": "zip", "shasum": "", "reference": "7c58943257767a0a031758e6507b5e2bff8ed3c9"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.36"}, "time": "2021-12-11T14:02:40+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "edcd1e89670d6b939a8222110ad5e13ab135bd22"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/edcd1e89670d6b939a8222110ad5e13ab135bd22", "type": "zip", "shasum": "", "reference": "edcd1e89670d6b939a8222110ad5e13ab135bd22"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.27"}, "time": "2021-07-23T15:41:52+00:00"}, {"version": "v4.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "ec33c7832b5d74b7fe34b3d353a3a032e34da580"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/ec33c7832b5d74b7fe34b3d353a3a032e34da580", "type": "zip", "shasum": "", "reference": "ec33c7832b5d74b7fe34b3d353a3a032e34da580"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.26"}, "time": "2021-06-10T13:25:38+00:00", "require": {"php": ">=7.1.3", "egulias/email-validator": "^2.1.10|^3", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.4.21|^5.2.6", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "cccd01ae52a4424fa00c6325bf95b4f44fedc10e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/cccd01ae52a4424fa00c6325bf95b4f44fedc10e", "type": "zip", "shasum": "", "reference": "cccd01ae52a4424fa00c6325bf95b4f44fedc10e"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.25"}, "time": "2021-05-27T12:49:44+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "327107fc7fd82a9f30357c9babee4acd5a7efd04"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/327107fc7fd82a9f30357c9babee4acd5a7efd04", "type": "zip", "shasum": "", "reference": "327107fc7fd82a9f30357c9babee4acd5a7efd04"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.22"}, "time": "2021-04-16T12:10:02+00:00"}, {"version": "v4.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "76b64a4105634d89e1f7ee7f75851740fe8fe66b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/76b64a4105634d89e1f7ee7f75851740fe8fe66b", "type": "zip", "shasum": "", "reference": "76b64a4105634d89e1f7ee7f75851740fe8fe66b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.21"}, "time": "2021-03-12T11:23:44+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "df3209aaa4008b9bb851410ab31be12d9b744773"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/df3209aaa4008b9bb851410ab31be12d9b744773", "type": "zip", "shasum": "", "reference": "df3209aaa4008b9bb851410ab31be12d9b744773"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.20"}, "time": "2021-02-22T15:36:50+00:00", "require": {"php": ">=7.1.3", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "38b6dcb4bb265fa4f003b0f42dd42fb91cbd877b"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/38b6dcb4bb265fa4f003b0f42dd42fb91cbd877b", "type": "zip", "shasum": "", "reference": "38b6dcb4bb265fa4f003b0f42dd42fb91cbd877b"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00"}, {"description": "Symfony Mailer Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "34d37f7b5cbe21c22eba947c32a07e93223e36ee"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/34d37f7b5cbe21c22eba947c32a07e93223e36ee", "type": "zip", "shasum": "", "reference": "34d37f7b5cbe21c22eba947c32a07e93223e36ee"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.18"}, "time": "2020-12-18T07:41:31+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "ee9d1337d46d46d9022da7711c2f80614e3d9cda"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/ee9d1337d46d46d9022da7711c2f80614e3d9cda", "type": "zip", "shasum": "", "reference": "ee9d1337d46d46d9022da7711c2f80614e3d9cda"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.17"}, "time": "2020-10-28T20:42:29+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "6429415051bbabfdfef8cad04abe97d7f974f8a6"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/6429415051bbabfdfef8cad04abe97d7f974f8a6", "type": "zip", "shasum": "", "reference": "6429415051bbabfdfef8cad04abe97d7f974f8a6"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "c1018952b49058802c2aa1ea831935e996b7f5d3"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/c1018952b49058802c2aa1ea831935e996b7f5d3", "type": "zip", "shasum": "", "reference": "c1018952b49058802c2aa1ea831935e996b7f5d3"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.15"}, "time": "2020-09-06T17:12:23+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.14"}}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "ad570d866132ff13e37a6587fe468d1d814c0a70"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/ad570d866132ff13e37a6587fe468d1d814c0a70", "type": "zip", "shasum": "", "reference": "ad570d866132ff13e37a6587fe468d1d814c0a70"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}, "time": "2020-08-19T17:05:08+00:00"}, {"version": "v4.4.12", "version_normalized": "********", "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.12"}}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "bbc265cc1072afb336d36503a618e3c8fb5a3810"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/bbc265cc1072afb336d36503a618e3c8fb5a3810", "type": "zip", "shasum": "", "reference": "bbc265cc1072afb336d36503a618e3c8fb5a3810"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.11"}, "time": "2020-07-15T06:28:59+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "fd16ff23c18146be32b1ea51f6cc43dc505ce361"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/fd16ff23c18146be32b1ea51f6cc43dc505ce361", "type": "zip", "shasum": "", "reference": "fd16ff23c18146be32b1ea51f6cc43dc505ce361"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}, "time": "2020-06-10T05:55:43+00:00"}, {"version": "v4.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "a304d790ca286fd4ff7ebae649e153d20ec5d6de"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/a304d790ca286fd4ff7ebae649e153d20ec5d6de", "type": "zip", "shasum": "", "reference": "a304d790ca286fd4ff7ebae649e153d20ec5d6de"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.9"}, "time": "2020-05-30T20:06:45+00:00"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "939553797698f6702fb00bdc2870bfa23f976473"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/939553797698f6702fb00bdc2870bfa23f976473", "type": "zip", "shasum": "", "reference": "939553797698f6702fb00bdc2870bfa23f976473"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}, "time": "2020-04-23T12:41:43+00:00", "require": {"php": "^7.1.3", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}}, {"version": "v4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "449856e70ccb1c91ebd75d6fb287ffe21be9fafe"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/449856e70ccb1c91ebd75d6fb287ffe21be9fafe", "type": "zip", "shasum": "", "reference": "449856e70ccb1c91ebd75d6fb287ffe21be9fafe"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.7"}, "time": "2020-03-27T16:54:36+00:00"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "b7c9b94a8bda86ca0499482ad4e5fb824aede8d8"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/b7c9b94a8bda86ca0499482ad4e5fb824aede8d8", "type": "zip", "shasum": "", "reference": "b7c9b94a8bda86ca0499482ad4e5fb824aede8d8"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}, "time": "2020-03-16T11:24:17+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f79a2cc091c14c2d78cade2f8b772f568fb63c43"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f79a2cc091c14c2d78cade2f8b772f568fb63c43", "type": "zip", "shasum": "", "reference": "f79a2cc091c14c2d78cade2f8b772f568fb63c43"}, "funding": [], "time": "2020-02-07T16:56:37+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "74c15502242b4f23dd28643a351db781ea2cd85a"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/74c15502242b4f23dd28643a351db781ea2cd85a", "type": "zip", "shasum": "", "reference": "74c15502242b4f23dd28643a351db781ea2cd85a"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.4"}, "time": "2020-01-04T13:00:46+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "1e6e1d074be84eab728b617aa08349b8a00b6f44"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/1e6e1d074be84eab728b617aa08349b8a00b6f44", "type": "zip", "shasum": "", "reference": "1e6e1d074be84eab728b617aa08349b8a00b6f44"}, "time": "2019-12-02T16:18:01+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "052577b99fa74165a609083e9039bbc2a3b74601"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/052577b99fa74165a609083e9039bbc2a3b74601", "type": "zip", "shasum": "", "reference": "052577b99fa74165a609083e9039bbc2a3b74601"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.1"}, "time": "2019-12-01T08:46:01+00:00"}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "050b93ce9d307de9567908aa8ab8d6fa3b970921"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/050b93ce9d307de9567908aa8ab8d6fa3b970921", "type": "zip", "shasum": "", "reference": "050b93ce9d307de9567908aa8ab8d6fa3b970921"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}, "time": "2019-11-14T14:24:33+00:00"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.0-RC1"}}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "f73d7c8001986b5faf2c0002e23e4fb6356b1773"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/f73d7c8001986b5faf2c0002e23e4fb6356b1773", "type": "zip", "shasum": "", "reference": "f73d7c8001986b5faf2c0002e23e4fb6356b1773"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.4"}, "time": "2019-11-12T17:18:47+00:00"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mailer/tree/v4.4.0-BETA1"}}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "9f1067430479527a21d9dc8461d97f4fbd1907de"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/9f1067430479527a21d9dc8461d97f4fbd1907de", "type": "zip", "shasum": "", "reference": "9f1067430479527a21d9dc8461d97f4fbd1907de"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.3"}, "time": "2020-01-01T11:51:43+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require": {"php": "^7.1.3", "egulias/email-validator": "^2.1.10", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.3.3"}, "require-dev": {"symfony/amazon-mailer": "^4.3", "symfony/google-mailer": "^4.3", "symfony/http-client-contracts": "^1.1", "symfony/mailgun-mailer": "^4.3.3", "symfony/mailchimp-mailer": "^4.3.3", "symfony/postmark-mailer": "^4.3.3", "symfony/sendgrid-mailer": "^4.3.3"}, "conflict": "__unset"}, {"version": "v4.3.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.10"}}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "a2e19255ce8de3a9c4e5228fde33ca9390af787c"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/a2e19255ce8de3a9c4e5228fde33ca9390af787c", "type": "zip", "shasum": "", "reference": "a2e19255ce8de3a9c4e5228fde33ca9390af787c"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.7"}, "time": "2019-10-30T12:58:49+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.6"}}, {"version": "v4.3.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.7"}}, {"version": "v4.3.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.6"}}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "135418bd4b558a2be90ba92e35f586934f3798e9"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/135418bd4b558a2be90ba92e35f586934f3798e9", "type": "zip", "shasum": "", "reference": "135418bd4b558a2be90ba92e35f586934f3798e9"}, "support": {"source": "https://github.com/symfony/mailer/tree/4.3"}, "time": "2019-09-25T14:27:22+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "35ee889540acc47ba4603397037f8fba39caa83f"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/35ee889540acc47ba4603397037f8fba39caa83f", "type": "zip", "shasum": "", "reference": "35ee889540acc47ba4603397037f8fba39caa83f"}, "time": "2019-08-22T08:16:11+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "83e23084c155fe1f3eef2a169481725ee4903f7e"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/83e23084c155fe1f3eef2a169481725ee4903f7e", "type": "zip", "shasum": "", "reference": "83e23084c155fe1f3eef2a169481725ee4903f7e"}, "time": "2019-07-18T18:01:20+00:00", "require": {"php": "^7.1.3", "egulias/email-validator": "^2.0", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.3.3"}}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "623c5e5a8303a936a1a265dc08b488ac43977dce"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/623c5e5a8303a936a1a265dc08b488ac43977dce", "type": "zip", "shasum": "", "reference": "623c5e5a8303a936a1a265dc08b488ac43977dce"}, "time": "2019-06-26T08:48:20+00:00", "require": {"php": "^7.1.3", "egulias/email-validator": "^2.0", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.3", "symfony/mime": "^4.3"}, "require-dev": {"symfony/amazon-mailer": "^4.3", "symfony/google-mailer": "^4.3", "symfony/http-client-contracts": "^1.1", "symfony/mailgun-mailer": "^4.3.2", "symfony/mailchimp-mailer": "^4.3", "symfony/postmark-mailer": "^4.3", "symfony/sendgrid-mailer": "^4.3"}}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "20542d3cda3c2bd214e0dd967d8b2590fae71ba3"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/20542d3cda3c2bd214e0dd967d8b2590fae71ba3", "type": "zip", "shasum": "", "reference": "20542d3cda3c2bd214e0dd967d8b2590fae71ba3"}, "time": "2019-06-04T13:59:49+00:00", "require-dev": {"symfony/amazon-mailer": "^4.3", "symfony/google-mailer": "^4.3", "symfony/http-client-contracts": "^1.1", "symfony/mailgun-mailer": "^4.3", "symfony/mailchimp-mailer": "^4.3", "symfony/postmark-mailer": "^4.3", "symfony/sendgrid-mailer": "^4.3"}}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "041ab07ae12263234476f8f1b3c47b85bcad89a4"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/041ab07ae12263234476f8f1b3c47b85bcad89a4", "type": "zip", "shasum": "", "reference": "041ab07ae12263234476f8f1b3c47b85bcad89a4"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.0-RC1"}, "time": "2019-05-28T11:49:33+00:00"}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "14515910b9897984f1af1c4ff3a8041785044d06"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/14515910b9897984f1af1c4ff3a8041785044d06", "type": "zip", "shasum": "", "reference": "14515910b9897984f1af1c4ff3a8041785044d06"}, "support": {"source": "https://github.com/symfony/mailer/tree/v4.3.0-BETA2"}, "time": "2019-05-20T09:28:54+00:00", "require-dev": {"symfony/amazon-mailer": "^4.3", "symfony/google-mailer": "^4.3", "symfony/mailgun-mailer": "^4.3", "symfony/mailchimp-mailer": "^4.3", "symfony/postmark-mailer": "^4.3", "symfony/sendgrid-mailer": "^4.3"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mailer.git", "type": "git", "reference": "15ad71ec94267ade6021a75978c0e3dbb0c4d440"}, "dist": {"url": "https://api.github.com/repos/symfony/mailer/zipball/15ad71ec94267ade6021a75978c0e3dbb0c4d440", "type": "zip", "shasum": "", "reference": "15ad71ec94267ade6021a75978c0e3dbb0c4d440"}, "support": {"source": "https://github.com/symfony/mailer/tree/master"}, "time": "2019-04-26T06:12:58+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 28 Jun 2025 08:28:32 GMT"}