{"minified": "composer/2.0", "packages": {"filament/support": [{"name": "filament/support", "description": "Core helper methods and foundation code for all Filament packages.", "keywords": [], "homepage": "https://github.com/filamentphp/filament", "version": "v4.0.0-beta15", "version_normalized": "*******-beta15", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "44e0820e598593417ef4cab22bb0bda33fe3eea6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/44e0820e598593417ef4cab22bb0bda33fe3eea6", "type": "zip", "shasum": "", "reference": "44e0820e598593417ef4cab22bb0bda33fe3eea6"}, "type": "library", "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "funding": [], "time": "2025-07-08T21:00:16+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Filament\\Support\\": "src"}}, "extra": {"laravel": {"providers": ["Filament\\Support\\SupportServiceProvider"]}}, "require": {"php": "^8.2", "ext-intl": "*", "blade-ui-kit/blade-heroicons": "^2.5", "danharrin/livewire-rate-limiting": "^2.0", "illuminate/contracts": "^11.28|^12.0", "kirschbaum-development/eloquent-power-joins": "^4.0", "league/uri-components": "^7.0", "livewire/livewire": "^3.5", "nette/php-generator": "^4.0", "ryangjchandler/blade-capture-directive": "^1.0", "spatie/invade": "^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^7.0", "symfony/console": "^7.0"}}, {"version": "v4.0.0-beta14", "version_normalized": "*******-beta14", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a8d8344518753b584966fca38b21b935f474902b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a8d8344518753b584966fca38b21b935f474902b", "type": "zip", "shasum": "", "reference": "a8d8344518753b584966fca38b21b935f474902b"}, "time": "2025-07-08T12:08:47+00:00"}, {"version": "v4.0.0-beta13", "version_normalized": "*******-beta13", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f29e07d96a4713b0f25a8134fefa897a886f6b92"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f29e07d96a4713b0f25a8134fefa897a886f6b92", "type": "zip", "shasum": "", "reference": "f29e07d96a4713b0f25a8134fefa897a886f6b92"}, "time": "2025-07-04T10:24:12+00:00"}, {"version": "v4.0.0-beta12", "version_normalized": "*******-beta12", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "82924ab1c3195ff69f48a85823d0066d87a8e2df"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/82924ab1c3195ff69f48a85823d0066d87a8e2df", "type": "zip", "shasum": "", "reference": "82924ab1c3195ff69f48a85823d0066d87a8e2df"}, "time": "2025-07-03T11:23:55+00:00"}, {"version": "v4.0.0-beta11", "version_normalized": "*******-beta11", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "af7a8a3249494821261777f760401fae8ab1987f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/af7a8a3249494821261777f760401fae8ab1987f", "type": "zip", "shasum": "", "reference": "af7a8a3249494821261777f760401fae8ab1987f"}, "time": "2025-06-30T11:28:33+00:00"}, {"version": "v4.0.0-beta10", "version_normalized": "*******-beta10", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f1fcf9ccfbd64bebc04099097259582e9f8f8e58"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f1fcf9ccfbd64bebc04099097259582e9f8f8e58", "type": "zip", "shasum": "", "reference": "f1fcf9ccfbd64bebc04099097259582e9f8f8e58"}, "time": "2025-06-26T12:58:59+00:00"}, {"version": "v4.0.0-beta9", "version_normalized": "*******-beta9", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "adcfa3e8d7b9afc5475d4a645b9c1ccf6262de88"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/adcfa3e8d7b9afc5475d4a645b9c1ccf6262de88", "type": "zip", "shasum": "", "reference": "adcfa3e8d7b9afc5475d4a645b9c1ccf6262de88"}, "time": "2025-06-18T22:12:56+00:00"}, {"version": "v4.0.0-beta8", "version_normalized": "*******-beta8"}, {"version": "v4.0.0-beta7", "version_normalized": "*******-beta7"}, {"version": "v4.0.0-beta6", "version_normalized": "*******-beta6"}, {"version": "v4.0.0-beta5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2b095a228b4cd7d70a5d61b70273a5bd9448ffe4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2b095a228b4cd7d70a5d61b70273a5bd9448ffe4", "type": "zip", "shasum": "", "reference": "2b095a228b4cd7d70a5d61b70273a5bd9448ffe4"}, "time": "2025-06-15T21:55:20+00:00"}, {"version": "v4.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a571372f5bd20a2e054cf0678218cfdfbfc0058a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a571372f5bd20a2e054cf0678218cfdfbfc0058a", "type": "zip", "shasum": "", "reference": "a571372f5bd20a2e054cf0678218cfdfbfc0058a"}, "time": "2025-06-13T09:07:25+00:00"}, {"version": "v4.0.0-beta3", "version_normalized": "*******-beta3"}, {"version": "v4.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f71d51fdce38ee6c7d2dc62e09bdde460c0258fa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f71d51fdce38ee6c7d2dc62e09bdde460c0258fa", "type": "zip", "shasum": "", "reference": "f71d51fdce38ee6c7d2dc62e09bdde460c0258fa"}, "time": "2025-06-11T15:42:21+00:00"}, {"version": "v4.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "845b035463714520814e822ef6712d5658e1f946"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/845b035463714520814e822ef6712d5658e1f946", "type": "zip", "shasum": "", "reference": "845b035463714520814e822ef6712d5658e1f946"}, "time": "2025-06-10T09:15:55+00:00"}, {"version": "v4.0.0-alpha8", "version_normalized": "*******-alpha8", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "19d421f67585dbfe04ebba1fbc7e37d172c652b5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/19d421f67585dbfe04ebba1fbc7e37d172c652b5", "type": "zip", "shasum": "", "reference": "19d421f67585dbfe04ebba1fbc7e37d172c652b5"}, "time": "2025-06-02T14:23:45+00:00"}, {"version": "v4.0.0-alpha7", "version_normalized": "*******-alpha7", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "957bddad0d156ad00a16fa22cc19e11703f252d5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/957bddad0d156ad00a16fa22cc19e11703f252d5", "type": "zip", "shasum": "", "reference": "957bddad0d156ad00a16fa22cc19e11703f252d5"}, "time": "2025-05-19T07:28:43+00:00", "require": {"php": "^8.2", "blade-ui-kit/blade-heroicons": "^2.5", "danharrin/livewire-rate-limiting": "^2.0", "illuminate/contracts": "^11.28|^12.0", "kirschbaum-development/eloquent-power-joins": "^4.0", "league/uri-components": "^7.0", "livewire/livewire": "^3.5", "nette/php-generator": "^4.0", "ryangjchandler/blade-capture-directive": "^1.0", "spatie/invade": "^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^7.0", "symfony/console": "^7.0"}}, {"version": "v4.0.0-alpha6", "version_normalized": "*******-alpha6", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f938b08ab6e8ffe30e8d2d478523e341debaa288"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f938b08ab6e8ffe30e8d2d478523e341debaa288", "type": "zip", "shasum": "", "reference": "f938b08ab6e8ffe30e8d2d478523e341debaa288"}, "time": "2025-05-07T15:28:35+00:00"}, {"version": "v4.0.0-alpha5", "version_normalized": "*******-alpha5", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e8576fda6c00cd8f9946db0f10eba06ae24a1d6a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e8576fda6c00cd8f9946db0f10eba06ae24a1d6a", "type": "zip", "shasum": "", "reference": "e8576fda6c00cd8f9946db0f10eba06ae24a1d6a"}, "time": "2025-04-29T19:20:30+00:00", "require": {"php": "^8.2", "blade-ui-kit/blade-heroicons": "^2.5", "danharrin/livewire-rate-limiting": "^2.0", "ext-intl": "*", "illuminate/contracts": "^11.15|^12.0", "kirschbaum-development/eloquent-power-joins": "^4.0", "league/uri-components": "^7.0", "livewire/livewire": "^3.5", "nette/php-generator": "^4.0", "ryangjchandler/blade-capture-directive": "^1.0", "spatie/invade": "^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^7.0", "symfony/console": "^7.0"}}, {"version": "v4.0.0-alpha4", "version_normalized": "*******-alpha4"}, {"version": "v4.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e430f57efe7ebf3d1171bd1003373ab4f0ec08f6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e430f57efe7ebf3d1171bd1003373ab4f0ec08f6", "type": "zip", "shasum": "", "reference": "e430f57efe7ebf3d1171bd1003373ab4f0ec08f6"}, "time": "2025-04-28T10:09:31+00:00"}, {"version": "v4.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "feff336c4f4f591a4ecc6210259c9f0407cc16ab"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/feff336c4f4f591a4ecc6210259c9f0407cc16ab", "type": "zip", "shasum": "", "reference": "feff336c4f4f591a4ecc6210259c9f0407cc16ab"}, "time": "2025-04-25T10:57:35+00:00"}, {"version": "v4.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b4bc52d53db5329261814ce6b2a048bd163c20dc"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b4bc52d53db5329261814ce6b2a048bd163c20dc", "type": "zip", "shasum": "", "reference": "b4bc52d53db5329261814ce6b2a048bd163c20dc"}, "time": "2025-04-23T06:32:16+00:00"}, {"version": "v3.3.31", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ec8c7e9e6b49d4f1150a19bfd6fc585c717a857d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ec8c7e9e6b49d4f1150a19bfd6fc585c717a857d", "type": "zip", "shasum": "", "reference": "ec8c7e9e6b49d4f1150a19bfd6fc585c717a857d"}, "time": "2025-07-08T20:42:46+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.5", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0|^12.0", "illuminate/support": "^10.45|^11.0|^12.0", "illuminate/view": "^10.45|^11.0|^12.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "^3.5", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.3.30", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7b4d26dd15204b9f45c29dcfcf8f5d778e1b303f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7b4d26dd15204b9f45c29dcfcf8f5d778e1b303f", "type": "zip", "shasum": "", "reference": "7b4d26dd15204b9f45c29dcfcf8f5d778e1b303f"}, "time": "2025-07-01T09:34:09+00:00"}, {"version": "v3.3.29", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a85378ff783a278c5db43abacb0dbf4232cbafe9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a85378ff783a278c5db43abacb0dbf4232cbafe9", "type": "zip", "shasum": "", "reference": "a85378ff783a278c5db43abacb0dbf4232cbafe9"}, "time": "2025-06-22T10:41:34+00:00"}, {"version": "v3.3.28", "version_normalized": "********"}, {"version": "v3.3.27", "version_normalized": "********"}, {"version": "v3.3.26", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7d850347ffbd8c84d84040ffb8c5ceb0f709a9fe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7d850347ffbd8c84d84040ffb8c5ceb0f709a9fe", "type": "zip", "shasum": "", "reference": "7d850347ffbd8c84d84040ffb8c5ceb0f709a9fe"}, "time": "2025-06-12T15:02:34+00:00"}, {"version": "v3.3.25", "version_normalized": "********"}, {"version": "v3.3.24", "version_normalized": "********"}, {"version": "v3.3.23", "version_normalized": "********"}, {"version": "v3.3.22", "version_normalized": "********"}, {"version": "v3.3.21", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5c140580d7feeabb4d2b0007c854676ae87be1b3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5c140580d7feeabb4d2b0007c854676ae87be1b3", "type": "zip", "shasum": "", "reference": "5c140580d7feeabb4d2b0007c854676ae87be1b3"}, "time": "2025-06-10T16:10:55+00:00"}, {"version": "v3.3.20", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4f9793ad3339301ca53ea6f2c984734f7ac38ce7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4f9793ad3339301ca53ea6f2c984734f7ac38ce7", "type": "zip", "shasum": "", "reference": "4f9793ad3339301ca53ea6f2c984734f7ac38ce7"}, "time": "2025-06-03T06:16:13+00:00"}, {"version": "v3.3.19", "version_normalized": "********"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "537663fa2c5057aa236a189255b623b5124d32d8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/537663fa2c5057aa236a189255b623b5124d32d8", "type": "zip", "shasum": "", "reference": "537663fa2c5057aa236a189255b623b5124d32d8"}, "time": "2025-05-21T08:45:20+00:00"}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0ab49fdb2bc937257d6f8e1f7b97a03216a43656"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0ab49fdb2bc937257d6f8e1f7b97a03216a43656", "type": "zip", "shasum": "", "reference": "0ab49fdb2bc937257d6f8e1f7b97a03216a43656"}, "time": "2025-04-30T09:16:34+00:00"}, {"version": "v3.3.14", "version_normalized": "********"}, {"version": "v3.3.13", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "819ebbd60a72b4ee3078d4771bb75d551fbb0b43"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/819ebbd60a72b4ee3078d4771bb75d551fbb0b43", "type": "zip", "shasum": "", "reference": "819ebbd60a72b4ee3078d4771bb75d551fbb0b43"}, "time": "2025-04-23T06:39:48+00:00"}, {"version": "v3.3.12", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ca955875a0e1c67aba92a1b5bb86841dd9f9ec43"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ca955875a0e1c67aba92a1b5bb86841dd9f9ec43", "type": "zip", "shasum": "", "reference": "ca955875a0e1c67aba92a1b5bb86841dd9f9ec43"}, "time": "2025-04-18T12:09:53+00:00"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fbe203104322d83dedb7da0422db0779a063f165"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fbe203104322d83dedb7da0422db0779a063f165", "type": "zip", "shasum": "", "reference": "fbe203104322d83dedb7da0422db0779a063f165"}, "time": "2025-04-07T10:58:23+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c4fb346e1483d5e3affb8588bd74a4b29866535b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c4fb346e1483d5e3affb8588bd74a4b29866535b", "type": "zip", "shasum": "", "reference": "c4fb346e1483d5e3affb8588bd74a4b29866535b"}, "time": "2025-04-07T10:06:06+00:00"}, {"version": "v3.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "19c40e9bd51e083705fa9a701b0e6d043ba1563c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/19c40e9bd51e083705fa9a701b0e6d043ba1563c", "type": "zip", "shasum": "", "reference": "19c40e9bd51e083705fa9a701b0e6d043ba1563c"}, "time": "2025-04-02T09:54:54+00:00"}, {"version": "v3.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cf3fa32f6e419ca768e88ac061dc3c47d01ed401"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cf3fa32f6e419ca768e88ac061dc3c47d01ed401", "type": "zip", "shasum": "", "reference": "cf3fa32f6e419ca768e88ac061dc3c47d01ed401"}, "time": "2025-03-20T09:29:02+00:00"}, {"version": "v3.3.6", "version_normalized": "*******"}, {"version": "v3.3.5", "version_normalized": "*******"}, {"version": "v3.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fb5ff99b8f7559815434c109d505c12c141510da"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fb5ff99b8f7559815434c109d505c12c141510da", "type": "zip", "shasum": "", "reference": "fb5ff99b8f7559815434c109d505c12c141510da"}, "time": "2025-03-05T09:26:25+00:00"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "90898e972db25db5749d992151790bbfce87563e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/90898e972db25db5749d992151790bbfce87563e", "type": "zip", "shasum": "", "reference": "90898e972db25db5749d992151790bbfce87563e"}, "time": "2025-03-03T09:07:23+00:00"}, {"version": "v3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0126880bcb3ffe99561170fd5b7c677c675dcf56"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0126880bcb3ffe99561170fd5b7c677c675dcf56", "type": "zip", "shasum": "", "reference": "0126880bcb3ffe99561170fd5b7c677c675dcf56"}, "time": "2025-03-03T08:12:21+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9ae768216b8f1cd6921946c98364b95f51053b20"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9ae768216b8f1cd6921946c98364b95f51053b20", "type": "zip", "shasum": "", "reference": "9ae768216b8f1cd6921946c98364b95f51053b20"}, "time": "2025-02-25T08:19:17+00:00"}, {"version": "v3.2.142", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "84b4fe507e250ed37e69d92854753761ad5d7908"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/84b4fe507e250ed37e69d92854753761ad5d7908", "type": "zip", "shasum": "", "reference": "84b4fe507e250ed37e69d92854753761ad5d7908"}, "time": "2025-02-20T23:14:48+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.5", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "^3.5", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.141", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e6ad230676d6f19fef88f34d0dd153b69996298d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e6ad230676d6f19fef88f34d0dd153b69996298d", "type": "zip", "shasum": "", "reference": "e6ad230676d6f19fef88f34d0dd153b69996298d"}, "time": "2025-02-19T08:42:39+00:00"}, {"version": "v3.2.140", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "95223f4bbfaa8c817efeacb4e2e7ca6928297610"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/95223f4bbfaa8c817efeacb4e2e7ca6928297610", "type": "zip", "shasum": "", "reference": "95223f4bbfaa8c817efeacb4e2e7ca6928297610"}, "time": "2025-02-10T08:14:57+00:00"}, {"version": "v3.2.139", "version_normalized": "*********"}, {"version": "v3.2.138", "version_normalized": "*********"}, {"version": "v3.2.137", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ca0ff1fa43d743e06de9c2f50ccea98e23785c7d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ca0ff1fa43d743e06de9c2f50ccea98e23785c7d", "type": "zip", "shasum": "", "reference": "ca0ff1fa43d743e06de9c2f50ccea98e23785c7d"}, "time": "2025-01-24T09:28:01+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.5", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "3.5.12", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.136", "version_normalized": "*********"}, {"version": "v3.2.135", "version_normalized": "*********"}, {"version": "v3.2.134", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9a537b6fb5426ef1b1d0bc324e3f6aaeba707533"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9a537b6fb5426ef1b1d0bc324e3f6aaeba707533", "type": "zip", "shasum": "", "reference": "9a537b6fb5426ef1b1d0bc324e3f6aaeba707533"}, "time": "2025-01-22T10:23:33+00:00"}, {"version": "v3.2.133", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "38fd76ae4f96b53e6f98cbb7bad3f05ad5032cee"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/38fd76ae4f96b53e6f98cbb7bad3f05ad5032cee", "type": "zip", "shasum": "", "reference": "38fd76ae4f96b53e6f98cbb7bad3f05ad5032cee"}, "time": "2025-01-10T12:48:52+00:00"}, {"version": "v3.2.132", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0bd91d5b937b0ae50394a976ba5fed9506581943"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0bd91d5b937b0ae50394a976ba5fed9506581943", "type": "zip", "shasum": "", "reference": "0bd91d5b937b0ae50394a976ba5fed9506581943"}, "time": "2024-12-31T13:16:28+00:00"}, {"version": "v3.2.131", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ddc16d8da50d73f7300671b70c9dcb942d845d9d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ddc16d8da50d73f7300671b70c9dcb942d845d9d", "type": "zip", "shasum": "", "reference": "ddc16d8da50d73f7300671b70c9dcb942d845d9d"}, "time": "2024-12-17T13:03:15+00:00"}, {"version": "v3.2.130", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1654851f04733f48faed7235b032b2c5842b5629"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1654851f04733f48faed7235b032b2c5842b5629", "type": "zip", "shasum": "", "reference": "1654851f04733f48faed7235b032b2c5842b5629"}, "time": "2024-12-11T12:57:14+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.5", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "^3.5", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.129", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e8aed9684d5c58ff7dde9517c7f1af44d575d871"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e8aed9684d5c58ff7dde9517c7f1af44d575d871", "type": "zip", "shasum": "", "reference": "e8aed9684d5c58ff7dde9517c7f1af44d575d871"}, "time": "2024-12-11T09:05:54+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.5", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "3.5.12", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.128", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "437d4f3305458f29c32ef4de5ef1d9dbdc74c3fe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/437d4f3305458f29c32ef4de5ef1d9dbdc74c3fe", "type": "zip", "shasum": "", "reference": "437d4f3305458f29c32ef4de5ef1d9dbdc74c3fe"}, "time": "2024-12-05T08:56:49+00:00"}, {"version": "v3.2.127", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a720fb2508a1d84a9b35aedc9991d4b53d18fea6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a720fb2508a1d84a9b35aedc9991d4b53d18fea6", "type": "zip", "shasum": "", "reference": "a720fb2508a1d84a9b35aedc9991d4b53d18fea6"}, "time": "2024-11-29T09:31:13+00:00"}, {"version": "v3.2.126", "version_normalized": "*********"}, {"version": "v3.2.125", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4bd6a02e096742ec68d0ed955b285579b21d02a4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4bd6a02e096742ec68d0ed955b285579b21d02a4", "type": "zip", "shasum": "", "reference": "4bd6a02e096742ec68d0ed955b285579b21d02a4"}, "time": "2024-11-27T16:52:24+00:00"}, {"version": "v3.2.124", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "13b1e485d3bc993950c9e61a3f6a8cb05efd2b96"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/13b1e485d3bc993950c9e61a3f6a8cb05efd2b96", "type": "zip", "shasum": "", "reference": "13b1e485d3bc993950c9e61a3f6a8cb05efd2b96"}, "time": "2024-11-13T16:35:51+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "^3.4.10", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.123", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e7174cee7e1d08205f7120d0dcc0d00d9bdd4d32"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e7174cee7e1d08205f7120d0dcc0d00d9bdd4d32", "type": "zip", "shasum": "", "reference": "e7174cee7e1d08205f7120d0dcc0d00d9bdd4d32"}, "time": "2024-10-31T13:38:25+00:00"}, {"version": "v3.2.122", "version_normalized": "*********"}, {"version": "v3.2.121", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fa8ada5101be964daa8f112616945a8f2ca5929e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fa8ada5101be964daa8f112616945a8f2ca5929e", "type": "zip", "shasum": "", "reference": "fa8ada5101be964daa8f112616945a8f2ca5929e"}, "time": "2024-10-24T13:47:33+00:00"}, {"version": "v3.2.120", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1b63dfd79ea37e940efad438717935081a61ba24"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1b63dfd79ea37e940efad438717935081a61ba24", "type": "zip", "shasum": "", "reference": "1b63dfd79ea37e940efad438717935081a61ba24"}, "time": "2024-10-23T07:36:38+00:00"}, {"version": "v3.2.119", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "85d83838ec0290fffba2be3f99a8b0840da1dc01"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/85d83838ec0290fffba2be3f99a8b0840da1dc01", "type": "zip", "shasum": "", "reference": "85d83838ec0290fffba2be3f99a8b0840da1dc01"}, "time": "2024-10-16T12:07:44+00:00"}, {"version": "v3.2.118", "version_normalized": "*********"}, {"version": "v3.2.117", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "31fcff80b873b4decdba10d5f7010310e12c8e94"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/31fcff80b873b4decdba10d5f7010310e12c8e94", "type": "zip", "shasum": "", "reference": "31fcff80b873b4decdba10d5f7010310e12c8e94"}, "time": "2024-10-08T14:24:29+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0|^4.0", "livewire/livewire": "^3.4.10 <= 3.5.6", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.116", "version_normalized": "*********"}, {"version": "v3.2.115", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6dba51efd6f2a32db21bc8684cd663915ab0e4d7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6dba51efd6f2a32db21bc8684cd663915ab0e4d7", "type": "zip", "shasum": "", "reference": "6dba51efd6f2a32db21bc8684cd663915ab0e4d7"}, "time": "2024-09-27T13:16:20+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0", "livewire/livewire": "^3.4.10 <= 3.5.6", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.114", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2183eb1149ef9ab742256155adf2afedda322e6d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2183eb1149ef9ab742256155adf2afedda322e6d", "type": "zip", "shasum": "", "reference": "2183eb1149ef9ab742256155adf2afedda322e6d"}, "time": "2024-09-23T14:10:13+00:00"}, {"version": "v3.2.113", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d2825e1c116e50d440bb3e7ac56ebcb8d1ddf184"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d2825e1c116e50d440bb3e7ac56ebcb8d1ddf184", "type": "zip", "shasum": "", "reference": "d2825e1c116e50d440bb3e7ac56ebcb8d1ddf184"}, "time": "2024-09-17T08:30:37+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "kirschbaum-development/eloquent-power-joins": "^3.0", "livewire/livewire": "^3.4.10", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.112", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d07086506d39f318398c13a0b8d689f75cbc14d6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d07086506d39f318398c13a0b8d689f75cbc14d6", "type": "zip", "shasum": "", "reference": "d07086506d39f318398c13a0b8d689f75cbc14d6"}, "time": "2024-09-11T08:25:46+00:00"}, {"version": "v3.2.111", "version_normalized": "*********"}, {"version": "v3.2.110", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "78e25428c754fcbb30c321d5dda439c760de9837"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/78e25428c754fcbb30c321d5dda439c760de9837", "type": "zip", "shasum": "", "reference": "78e25428c754fcbb30c321d5dda439c760de9837"}, "time": "2024-08-26T07:22:57+00:00"}, {"version": "v3.2.109", "version_normalized": "*********"}, {"version": "v3.2.108", "version_normalized": "*********"}, {"version": "v3.2.107", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f00b84d43d157c85837108a7eeaceabbaf6044ab"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f00b84d43d157c85837108a7eeaceabbaf6044ab", "type": "zip", "shasum": "", "reference": "f00b84d43d157c85837108a7eeaceabbaf6044ab"}, "time": "2024-08-22T12:08:05+00:00"}, {"version": "v3.2.106", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7cded440dee149f86f5d157164bc57e4e18f6842"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7cded440dee149f86f5d157164bc57e4e18f6842", "type": "zip", "shasum": "", "reference": "7cded440dee149f86f5d157164bc57e4e18f6842"}, "time": "2024-08-21T06:07:30+00:00"}, {"version": "v3.2.105", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "544e521a310b1b4e7783d8ad1781ef7b1915232a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/544e521a310b1b4e7783d8ad1781ef7b1915232a", "type": "zip", "shasum": "", "reference": "544e521a310b1b4e7783d8ad1781ef7b1915232a"}, "time": "2024-08-20T12:51:40+00:00"}, {"version": "v3.2.104", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d8dbbc00e5d35b610e29888e8890964de08ae248"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d8dbbc00e5d35b610e29888e8890964de08ae248", "type": "zip", "shasum": "", "reference": "d8dbbc00e5d35b610e29888e8890964de08ae248"}, "time": "2024-08-20T11:45:13+00:00"}, {"version": "v3.2.103", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4c6927d94175e250412f4486e00c3abc1d313057"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4c6927d94175e250412f4486e00c3abc1d313057", "type": "zip", "shasum": "", "reference": "4c6927d94175e250412f4486e00c3abc1d313057"}, "time": "2024-08-20T08:32:55+00:00"}, {"version": "v3.2.102", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7dc30bc972b5c29572bd285bfb7c3f24e909522a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7dc30bc972b5c29572bd285bfb7c3f24e909522a", "type": "zip", "shasum": "", "reference": "7dc30bc972b5c29572bd285bfb7c3f24e909522a"}, "time": "2024-08-13T12:36:11+00:00"}, {"version": "v3.2.101", "version_normalized": "*********"}, {"version": "v3.2.100", "version_normalized": "*********"}, {"version": "v3.2.99", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "749c4a5901197c54bf09513817962762485b2478"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/749c4a5901197c54bf09513817962762485b2478", "type": "zip", "shasum": "", "reference": "749c4a5901197c54bf09513817962762485b2478"}, "time": "2024-08-12T16:38:51+00:00"}, {"version": "v3.2.98", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6e6758661b9768c586506e5c82bab7f5fca9757d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6e6758661b9768c586506e5c82bab7f5fca9757d", "type": "zip", "shasum": "", "reference": "6e6758661b9768c586506e5c82bab7f5fca9757d"}, "time": "2024-08-09T12:23:43+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2|^4.0", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.4.10", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.97", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "94c5349b8fed252499314bd2149fadf96ddaf6d6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/94c5349b8fed252499314bd2149fadf96ddaf6d6", "type": "zip", "shasum": "", "reference": "94c5349b8fed252499314bd2149fadf96ddaf6d6"}, "time": "2024-07-31T11:53:25+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.4.10", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.96", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4cec1377278853882103a09cafdc1b258f995ee4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4cec1377278853882103a09cafdc1b258f995ee4", "type": "zip", "shasum": "", "reference": "4cec1377278853882103a09cafdc1b258f995ee4"}, "time": "2024-07-24T12:10:41+00:00"}, {"version": "v3.2.95", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cb0ba7fea69d80c2612f22a3cdf29a81722e5fcb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cb0ba7fea69d80c2612f22a3cdf29a81722e5fcb", "type": "zip", "shasum": "", "reference": "cb0ba7fea69d80c2612f22a3cdf29a81722e5fcb"}, "time": "2024-07-18T10:43:21+00:00"}, {"version": "v3.2.94", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "118843099abbdc3fea21f417a02e89b50c807329"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/118843099abbdc3fea21f417a02e89b50c807329", "type": "zip", "shasum": "", "reference": "118843099abbdc3fea21f417a02e89b50c807329"}, "time": "2024-07-17T10:41:07+00:00"}, {"version": "v3.2.93", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "29b7d19163dc2e34e89f290e39d5657784ca5df9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/29b7d19163dc2e34e89f290e39d5657784ca5df9", "type": "zip", "shasum": "", "reference": "29b7d19163dc2e34e89f290e39d5657784ca5df9"}, "time": "2024-07-10T17:11:07+00:00"}, {"version": "v3.2.92", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "852ec3783349d799141c302a4944d5303ece985d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/852ec3783349d799141c302a4944d5303ece985d", "type": "zip", "shasum": "", "reference": "852ec3783349d799141c302a4944d5303ece985d"}, "time": "2024-06-14T10:24:22+00:00"}, {"version": "v3.2.91", "version_normalized": "********"}, {"version": "v3.2.90", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5a4394d1bc9fde801a170141b3842a84b58c1336"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5a4394d1bc9fde801a170141b3842a84b58c1336", "type": "zip", "shasum": "", "reference": "5a4394d1bc9fde801a170141b3842a84b58c1336"}, "time": "2024-06-13T07:48:21+00:00"}, {"version": "v3.2.89", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a62d52739d661042123da30fa2b8e64e46bef901"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a62d52739d661042123da30fa2b8e64e46bef901", "type": "zip", "shasum": "", "reference": "a62d52739d661042123da30fa2b8e64e46bef901"}, "time": "2024-06-10T12:07:53+00:00"}, {"version": "v3.2.88", "version_normalized": "********"}, {"version": "v3.2.87", "version_normalized": "********"}, {"version": "v3.2.87-beta1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9c23c494d83a6fbac4b5e2016952619b3e7e69f7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9c23c494d83a6fbac4b5e2016952619b3e7e69f7", "type": "zip", "shasum": "", "reference": "9c23c494d83a6fbac4b5e2016952619b3e7e69f7"}, "time": "2024-06-05T09:38:45+00:00"}, {"version": "v3.2.86", "version_normalized": "********"}, {"version": "v3.2.85", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "67e5a58a47f4411a25bd47b931556aa570944f4e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/67e5a58a47f4411a25bd47b931556aa570944f4e", "type": "zip", "shasum": "", "reference": "67e5a58a47f4411a25bd47b931556aa570944f4e"}, "time": "2024-05-30T18:32:10+00:00"}, {"version": "v3.2.84", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "10007d6df94db92c08cac03dbcc170bac3d81a75"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/10007d6df94db92c08cac03dbcc170bac3d81a75", "type": "zip", "shasum": "", "reference": "10007d6df94db92c08cac03dbcc170bac3d81a75"}, "time": "2024-05-30T12:37:19+00:00"}, {"version": "v3.2.83", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f32b841309d01c4538d8f128d67ec213969747e9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f32b841309d01c4538d8f128d67ec213969747e9", "type": "zip", "shasum": "", "reference": "f32b841309d01c4538d8f128d67ec213969747e9"}, "time": "2024-05-23T12:08:37+00:00"}, {"version": "v3.2.82", "version_normalized": "********"}, {"version": "v3.2.81", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1435caac4c2a454ab0eb11fc6590628eda36aa0b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1435caac4c2a454ab0eb11fc6590628eda36aa0b", "type": "zip", "shasum": "", "reference": "1435caac4c2a454ab0eb11fc6590628eda36aa0b"}, "time": "2024-05-20T16:26:41+00:00"}, {"version": "v3.2.80", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b49a62028f77a4b7daf5b4caad5d933665cc995a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b49a62028f77a4b7daf5b4caad5d933665cc995a", "type": "zip", "shasum": "", "reference": "b49a62028f77a4b7daf5b4caad5d933665cc995a"}, "time": "2024-05-13T11:05:17+00:00"}, {"version": "v3.2.79", "version_normalized": "********"}, {"version": "v3.2.78", "version_normalized": "********"}, {"version": "v3.2.77", "version_normalized": "********"}, {"version": "v3.2.76", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2ed906a82070c07da5367ae71f52826698d17b9e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2ed906a82070c07da5367ae71f52826698d17b9e", "type": "zip", "shasum": "", "reference": "2ed906a82070c07da5367ae71f52826698d17b9e"}, "time": "2024-05-09T08:34:31+00:00"}, {"version": "v3.2.75", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3338eb0798cb6bb5daf08a86a4baaedf0fc3d073"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3338eb0798cb6bb5daf08a86a4baaedf0fc3d073", "type": "zip", "shasum": "", "reference": "3338eb0798cb6bb5daf08a86a4baaedf0fc3d073"}, "time": "2024-05-08T20:47:46+00:00"}, {"version": "v3.2.74", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f2c822c3605d797f45f61719153143e72e11aed3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f2c822c3605d797f45f61719153143e72e11aed3", "type": "zip", "shasum": "", "reference": "f2c822c3605d797f45f61719153143e72e11aed3"}, "time": "2024-05-08T16:21:32+00:00"}, {"version": "v3.2.73", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "49637c225f9eb72380a3dc54e9fd951563955770"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/49637c225f9eb72380a3dc54e9fd951563955770", "type": "zip", "shasum": "", "reference": "49637c225f9eb72380a3dc54e9fd951563955770"}, "time": "2024-05-03T12:26:17+00:00"}, {"version": "v3.2.72", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "169fa6303337ad1f5d0203b9d34baa76bc938384"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/169fa6303337ad1f5d0203b9d34baa76bc938384", "type": "zip", "shasum": "", "reference": "169fa6303337ad1f5d0203b9d34baa76bc938384"}, "time": "2024-04-28T08:39:28+00:00"}, {"version": "v3.2.71", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "594f8d38e365578b6d91455c5beade6c54def5a4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/594f8d38e365578b6d91455c5beade6c54def5a4", "type": "zip", "shasum": "", "reference": "594f8d38e365578b6d91455c5beade6c54def5a4"}, "time": "2024-04-21T22:24:20+00:00"}, {"version": "v3.2.70", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e6bd0dee0dda9f70b6f278747a5780c676edf21b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e6bd0dee0dda9f70b6f278747a5780c676edf21b", "type": "zip", "shasum": "", "reference": "e6bd0dee0dda9f70b6f278747a5780c676edf21b"}, "time": "2024-04-18T11:26:31+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.4.9", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/console": "^6.0|^7.0"}}, {"version": "v3.2.69", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fc9038785de9d49802c36c1aef341af8dfa4dbe0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fc9038785de9d49802c36c1aef341af8dfa4dbe0", "type": "zip", "shasum": "", "reference": "fc9038785de9d49802c36c1aef341af8dfa4dbe0"}, "time": "2024-04-16T17:58:12+00:00"}, {"version": "v3.2.68", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b78f60b65ca383da52cdb5117a01badbf96070e8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b78f60b65ca383da52cdb5117a01badbf96070e8", "type": "zip", "shasum": "", "reference": "b78f60b65ca383da52cdb5117a01badbf96070e8"}, "time": "2024-04-16T15:43:09+00:00"}, {"version": "v3.2.67", "version_normalized": "********"}, {"version": "v3.2.66", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4b629597f5c2130abe0701c82e4da5b266bcbafa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4b629597f5c2130abe0701c82e4da5b266bcbafa", "type": "zip", "shasum": "", "reference": "4b629597f5c2130abe0701c82e4da5b266bcbafa"}, "time": "2024-04-11T21:38:59+00:00"}, {"version": "v3.2.65", "version_normalized": "********"}, {"version": "v3.2.64", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "54beefbc75237f8d42a9eb4692f07029e76a1e0a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/54beefbc75237f8d42a9eb4692f07029e76a1e0a", "type": "zip", "shasum": "", "reference": "54beefbc75237f8d42a9eb4692f07029e76a1e0a"}, "time": "2024-04-11T07:40:56+00:00"}, {"version": "v3.2.63", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "17ddf2035ac79183bd61806bc0c7d4851228f2a1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/17ddf2035ac79183bd61806bc0c7d4851228f2a1", "type": "zip", "shasum": "", "reference": "17ddf2035ac79183bd61806bc0c7d4851228f2a1"}, "time": "2024-04-05T21:55:36+00:00"}, {"version": "v3.2.62", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7b156b35791a7ff990621ec59fc8ec8cfdbb0782"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7b156b35791a7ff990621ec59fc8ec8cfdbb0782", "type": "zip", "shasum": "", "reference": "7b156b35791a7ff990621ec59fc8ec8cfdbb0782"}, "time": "2024-04-01T18:41:17+00:00"}, {"version": "v3.2.61", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6f7f6fe72f8c206bd28297fbf41f863358c95c07"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6f7f6fe72f8c206bd28297fbf41f863358c95c07", "type": "zip", "shasum": "", "reference": "6f7f6fe72f8c206bd28297fbf41f863358c95c07"}, "time": "2024-03-27T12:36:25+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.4.9", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1|^7.0"}}, {"version": "v3.2.60", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "05c0c56bdb66226dc8d239ac91bc973a0dd33edb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/05c0c56bdb66226dc8d239ac91bc973a0dd33edb", "type": "zip", "shasum": "", "reference": "05c0c56bdb66226dc8d239ac91bc973a0dd33edb"}, "time": "2024-03-23T20:59:06+00:00"}, {"version": "v3.2.59", "version_normalized": "********"}, {"version": "v3.2.58", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1bb8f468624b0b06f20bc5da858cdb5b6a97d53f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1bb8f468624b0b06f20bc5da858cdb5b6a97d53f", "type": "zip", "shasum": "", "reference": "1bb8f468624b0b06f20bc5da858cdb5b6a97d53f"}, "time": "2024-03-21T22:43:36+00:00"}, {"version": "v3.2.57", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f10144d4758d7873ee8d471530a4455a1105ca53"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f10144d4758d7873ee8d471530a4455a1105ca53", "type": "zip", "shasum": "", "reference": "f10144d4758d7873ee8d471530a4455a1105ca53"}, "time": "2024-03-14T14:14:06+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.4.9", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.2.56", "version_normalized": "********"}, {"version": "v3.2.55", "version_normalized": "********"}, {"version": "v3.2.54", "version_normalized": "********"}, {"version": "v3.2.53", "version_normalized": "********"}, {"version": "v3.2.52", "version_normalized": "********"}, {"version": "v3.2.51", "version_normalized": "********"}, {"version": "v3.2.50", "version_normalized": "********"}, {"version": "v3.2.49", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b17bc4e8eb1b9fc1970384d60ca78fb230f3d9e4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b17bc4e8eb1b9fc1970384d60ca78fb230f3d9e4", "type": "zip", "shasum": "", "reference": "b17bc4e8eb1b9fc1970384d60ca78fb230f3d9e4"}, "time": "2024-03-14T10:54:28+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.2.3", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.2.48", "version_normalized": "********"}, {"version": "v3.2.47", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "811a57072a6e8677e42d1b745291def5f26a8d62"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/811a57072a6e8677e42d1b745291def5f26a8d62", "type": "zip", "shasum": "", "reference": "811a57072a6e8677e42d1b745291def5f26a8d62"}, "time": "2024-03-08T12:54:38+00:00"}, {"version": "v3.2.46", "version_normalized": "********"}, {"version": "v3.2.45", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f60a06fc11ce0520bf8296c350fa6e3ba2556632"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f60a06fc11ce0520bf8296c350fa6e3ba2556632", "type": "zip", "shasum": "", "reference": "f60a06fc11ce0520bf8296c350fa6e3ba2556632"}, "time": "2024-03-07T13:18:04+00:00"}, {"version": "v3.2.44", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1f5ba74e8755f5a11c0bf53907d8d9ed1f8d868d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1f5ba74e8755f5a11c0bf53907d8d9ed1f8d868d", "type": "zip", "shasum": "", "reference": "1f5ba74e8755f5a11c0bf53907d8d9ed1f8d868d"}, "time": "2024-03-05T14:33:52+00:00"}, {"version": "v3.2.43", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6dcf24890fdf69ffeb2d49f29a8a4c51cf7d4fc3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6dcf24890fdf69ffeb2d49f29a8a4c51cf7d4fc3", "type": "zip", "shasum": "", "reference": "6dcf24890fdf69ffeb2d49f29a8a4c51cf7d4fc3"}, "time": "2024-03-04T12:09:59+00:00"}, {"version": "v3.2.42", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1a186d3553f07f496522acab111373e2dbfd2b6b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1a186d3553f07f496522acab111373e2dbfd2b6b", "type": "zip", "shasum": "", "reference": "1a186d3553f07f496522acab111373e2dbfd2b6b"}, "time": "2024-03-01T12:24:02+00:00"}, {"version": "v3.2.41", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a5fb3a25e85474bdacb70f21b657b28ff0c4decf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a5fb3a25e85474bdacb70f21b657b28ff0c4decf", "type": "zip", "shasum": "", "reference": "a5fb3a25e85474bdacb70f21b657b28ff0c4decf"}, "time": "2024-02-29T12:30:23+00:00"}, {"version": "v3.2.40", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c3788e1f63ffb9e15ed6e584c750c20a26720768"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c3788e1f63ffb9e15ed6e584c750c20a26720768", "type": "zip", "shasum": "", "reference": "c3788e1f63ffb9e15ed6e584c750c20a26720768"}, "time": "2024-02-28T12:18:32+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "ext-intl": "*", "illuminate/contracts": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "livewire/livewire": "^3.2.3", "ryangjchandler/blade-capture-directive": "^0.2|^0.3|^1.0", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.2.39", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9f375682575c9669ccd24a47617b50308cc1a684"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9f375682575c9669ccd24a47617b50308cc1a684", "type": "zip", "shasum": "", "reference": "9f375682575c9669ccd24a47617b50308cc1a684"}, "time": "2024-02-27T15:33:06+00:00"}, {"version": "v3.2.38", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2d2c788d36fea3c115628fac12d3638d52059cd6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2d2c788d36fea3c115628fac12d3638d52059cd6", "type": "zip", "shasum": "", "reference": "2d2c788d36fea3c115628fac12d3638d52059cd6"}, "time": "2024-02-26T20:23:16+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.45", "illuminate/support": "^10.45", "illuminate/view": "^10.45", "livewire/livewire": "^3.2.3", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.2.37", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b9283eca3b999c35afc2e3cb0d69f6acb77e011a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b9283eca3b999c35afc2e3cb0d69f6acb77e011a", "type": "zip", "shasum": "", "reference": "b9283eca3b999c35afc2e3cb0d69f6acb77e011a"}, "time": "2024-02-24T06:52:20+00:00"}, {"version": "v3.2.36", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e2d2a0cf90098822c6262914dc809eab48e28759"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e2d2a0cf90098822c6262914dc809eab48e28759", "type": "zip", "shasum": "", "reference": "e2d2a0cf90098822c6262914dc809eab48e28759"}, "time": "2024-02-23T22:26:32+00:00"}, {"version": "v3.2.35", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "73e3b2f50a6d77df21a302017ec86499ea9353ca"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/73e3b2f50a6d77df21a302017ec86499ea9353ca", "type": "zip", "shasum": "", "reference": "73e3b2f50a6d77df21a302017ec86499ea9353ca"}, "time": "2024-02-20T10:36:45+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.2.1", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "^3.2.3", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.2.34", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c2be482587352c21bd9dc215b2e489c3598a9a06"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c2be482587352c21bd9dc215b2e489c3598a9a06", "type": "zip", "shasum": "", "reference": "c2be482587352c21bd9dc215b2e489c3598a9a06"}, "time": "2024-02-13T06:30:24+00:00"}, {"version": "v3.2.33", "version_normalized": "********"}, {"version": "v3.2.32", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "293e18a96ee4ccfe19ec25221ed9580937bb482e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/293e18a96ee4ccfe19ec25221ed9580937bb482e", "type": "zip", "shasum": "", "reference": "293e18a96ee4ccfe19ec25221ed9580937bb482e"}, "time": "2024-02-12T17:10:18+00:00"}, {"version": "v3.2.31", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "449e5e0a508ca17355911e5a1bbb93553cf4fd5a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/449e5e0a508ca17355911e5a1bbb93553cf4fd5a", "type": "zip", "shasum": "", "reference": "449e5e0a508ca17355911e5a1bbb93553cf4fd5a"}, "time": "2024-02-10T13:49:06+00:00"}, {"version": "v3.2.30", "version_normalized": "********"}, {"version": "v3.2.29", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "351a781d00aa5a5c4791bbe836df0408b5ace1b3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/351a781d00aa5a5c4791bbe836df0408b5ace1b3", "type": "zip", "shasum": "", "reference": "351a781d00aa5a5c4791bbe836df0408b5ace1b3"}, "time": "2024-02-09T17:41:28+00:00"}, {"version": "v3.2.28", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2aa27a1fca6ef4baf59ca6b1714b690374724d49"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2aa27a1fca6ef4baf59ca6b1714b690374724d49", "type": "zip", "shasum": "", "reference": "2aa27a1fca6ef4baf59ca6b1714b690374724d49"}, "time": "2024-02-08T10:08:21+00:00"}, {"version": "v3.2.27", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2e320f7389db0d5529438a11ffa9db55bfe6837b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2e320f7389db0d5529438a11ffa9db55bfe6837b", "type": "zip", "shasum": "", "reference": "2e320f7389db0d5529438a11ffa9db55bfe6837b"}, "time": "2024-02-07T19:40:11+00:00"}, {"version": "v3.2.26", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b2e99248c000000810e339ae81a51b1f7b6be1d2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b2e99248c000000810e339ae81a51b1f7b6be1d2", "type": "zip", "shasum": "", "reference": "b2e99248c000000810e339ae81a51b1f7b6be1d2"}, "time": "2024-02-07T18:46:42+00:00"}, {"version": "v3.2.25", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "afee888de911565926e01ce69df9947835433ba2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/afee888de911565926e01ce69df9947835433ba2", "type": "zip", "shasum": "", "reference": "afee888de911565926e01ce69df9947835433ba2"}, "time": "2024-02-07T11:09:30+00:00"}, {"version": "v3.2.25-beta1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "abec37d612dc517ee1a622a7e6cca46f1a9f7fd6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/abec37d612dc517ee1a622a7e6cca46f1a9f7fd6", "type": "zip", "shasum": "", "reference": "abec37d612dc517ee1a622a7e6cca46f1a9f7fd6"}, "time": "2024-02-05T14:16:55+00:00"}, {"version": "v3.2.24", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "04a7a030120e29f863e09deb1742f7f24ec7bf39"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/04a7a030120e29f863e09deb1742f7f24ec7bf39", "type": "zip", "shasum": "", "reference": "04a7a030120e29f863e09deb1742f7f24ec7bf39"}, "time": "2024-02-05T14:06:12+00:00"}, {"version": "v3.2.23", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "abf44826987b6e1efb0200d0068c48d0cf9d851a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/abf44826987b6e1efb0200d0068c48d0cf9d851a", "type": "zip", "shasum": "", "reference": "abf44826987b6e1efb0200d0068c48d0cf9d851a"}, "time": "2024-02-01T11:30:19+00:00"}, {"version": "v3.2.22", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f6355b9ab1af057b5df1eb4c8bb046e312f57836"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f6355b9ab1af057b5df1eb4c8bb046e312f57836", "type": "zip", "shasum": "", "reference": "f6355b9ab1af057b5df1eb4c8bb046e312f57836"}, "time": "2024-01-31T12:14:03+00:00"}, {"version": "v3.2.21", "version_normalized": "********"}, {"version": "v3.2.20", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3c055c5bfdacc080adbc1d76f9ac8f99c8ca0398"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3c055c5bfdacc080adbc1d76f9ac8f99c8ca0398", "type": "zip", "shasum": "", "reference": "3c055c5bfdacc080adbc1d76f9ac8f99c8ca0398"}, "time": "2024-01-29T15:36:06+00:00"}, {"version": "v3.2.19", "version_normalized": "********"}, {"version": "v3.2.18", "version_normalized": "********"}, {"version": "v3.2.17", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e91541bd6cbfbf5a5904968d249683fff0c6d7f5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e91541bd6cbfbf5a5904968d249683fff0c6d7f5", "type": "zip", "shasum": "", "reference": "e91541bd6cbfbf5a5904968d249683fff0c6d7f5"}, "time": "2024-01-29T13:20:08+00:00"}, {"version": "v3.2.16", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8df5c195047d2849c49c1d20880951f716f111e0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8df5c195047d2849c49c1d20880951f716f111e0", "type": "zip", "shasum": "", "reference": "8df5c195047d2849c49c1d20880951f716f111e0"}, "time": "2024-01-27T23:30:41+00:00"}, {"version": "v3.2.15", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "efeec378372f65bfc0a4caa57a6090b7405acd2a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/efeec378372f65bfc0a4caa57a6090b7405acd2a", "type": "zip", "shasum": "", "reference": "efeec378372f65bfc0a4caa57a6090b7405acd2a"}, "time": "2024-01-26T12:43:18+00:00"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8052f0348753881861aebf123d06982db4c822c2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8052f0348753881861aebf123d06982db4c822c2", "type": "zip", "shasum": "", "reference": "8052f0348753881861aebf123d06982db4c822c2"}, "time": "2024-01-25T13:43:58+00:00"}, {"version": "v3.2.13", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2641adc1bffc8f578d33c924dcaa16aa77a650f6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2641adc1bffc8f578d33c924dcaa16aa77a650f6", "type": "zip", "shasum": "", "reference": "2641adc1bffc8f578d33c924dcaa16aa77a650f6"}, "time": "2024-01-25T12:20:18+00:00"}, {"version": "v3.2.12", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1760882f9a263004d99b13b8ef2009fdaf084716"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1760882f9a263004d99b13b8ef2009fdaf084716", "type": "zip", "shasum": "", "reference": "1760882f9a263004d99b13b8ef2009fdaf084716"}, "time": "2024-01-24T12:34:31+00:00"}, {"version": "v3.2.11", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a84cbd37d7e88adade1f8c9819d412dab0e77c8c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a84cbd37d7e88adade1f8c9819d412dab0e77c8c", "type": "zip", "shasum": "", "reference": "a84cbd37d7e88adade1f8c9819d412dab0e77c8c"}, "time": "2024-01-23T13:24:02+00:00"}, {"version": "v3.2.10", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3b4d5d197c04e5a0b2a250d97c4761a07da9c85e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3b4d5d197c04e5a0b2a250d97c4761a07da9c85e", "type": "zip", "shasum": "", "reference": "3b4d5d197c04e5a0b2a250d97c4761a07da9c85e"}, "time": "2024-01-21T14:44:58+00:00"}, {"version": "v3.2.9", "version_normalized": "*******"}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "37663c414646ea7435121a3fef3d5d2d4f45a81f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/37663c414646ea7435121a3fef3d5d2d4f45a81f", "type": "zip", "shasum": "", "reference": "37663c414646ea7435121a3fef3d5d2d4f45a81f"}, "time": "2024-01-21T00:39:58+00:00"}, {"version": "v3.2.7", "version_normalized": "*******"}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bba54ccc077198761c90ba1a498099d4130cb11c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bba54ccc077198761c90ba1a498099d4130cb11c", "type": "zip", "shasum": "", "reference": "bba54ccc077198761c90ba1a498099d4130cb11c"}, "time": "2024-01-19T14:01:22+00:00"}, {"version": "v3.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f9cbc339421210407a6f80b977d8314154730a21"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f9cbc339421210407a6f80b977d8314154730a21", "type": "zip", "shasum": "", "reference": "f9cbc339421210407a6f80b977d8314154730a21"}, "time": "2024-01-18T21:05:52+00:00"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9ce74026bb8f9278c1085b307e0ba7664b1bfe28"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9ce74026bb8f9278c1085b307e0ba7664b1bfe28", "type": "zip", "shasum": "", "reference": "9ce74026bb8f9278c1085b307e0ba7664b1bfe28"}, "time": "2024-01-18T15:58:39+00:00"}, {"version": "v3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "79ec45f39da8989307b0d17f1fbff19feed0d459"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/79ec45f39da8989307b0d17f1fbff19feed0d459", "type": "zip", "shasum": "", "reference": "79ec45f39da8989307b0d17f1fbff19feed0d459"}, "time": "2024-01-18T11:12:10+00:00"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cf74fe9e49042cc269cfdc3941aa9c9435f955e0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cf74fe9e49042cc269cfdc3941aa9c9435f955e0", "type": "zip", "shasum": "", "reference": "cf74fe9e49042cc269cfdc3941aa9c9435f955e0"}, "time": "2024-01-15T12:25:07+00:00"}, {"version": "v3.2.1", "version_normalized": "*******"}, {"version": "v3.2.0", "version_normalized": "*******"}, {"version": "v3.1.47", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7122e56e5831a6423d7e028c3a2d08255aba08e9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7122e56e5831a6423d7e028c3a2d08255aba08e9", "type": "zip", "shasum": "", "reference": "7122e56e5831a6423d7e028c3a2d08255aba08e9"}, "time": "2024-01-12T11:54:33+00:00"}, {"version": "v3.1.46", "version_normalized": "********"}, {"version": "v3.1.45", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "954fdfb81f6f59dadef8c94ddebd667d8ea1ac17"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/954fdfb81f6f59dadef8c94ddebd667d8ea1ac17", "type": "zip", "shasum": "", "reference": "954fdfb81f6f59dadef8c94ddebd667d8ea1ac17"}, "time": "2024-01-11T16:43:44+00:00"}, {"version": "v3.1.44", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "247b4699937a63f1777b27c092a294b886f8fda6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/247b4699937a63f1777b27c092a294b886f8fda6", "type": "zip", "shasum": "", "reference": "247b4699937a63f1777b27c092a294b886f8fda6"}, "time": "2024-01-11T16:19:25+00:00"}, {"version": "v3.1.43", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ee823f592795e92c92211c57ebf4bb9e3358ff59"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ee823f592795e92c92211c57ebf4bb9e3358ff59", "type": "zip", "shasum": "", "reference": "ee823f592795e92c92211c57ebf4bb9e3358ff59"}, "time": "2024-01-11T12:33:02+00:00"}, {"version": "v3.1.42", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "332308fdd61b67c8d8e7864029d8f6a85b99d49a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/332308fdd61b67c8d8e7864029d8f6a85b99d49a", "type": "zip", "shasum": "", "reference": "332308fdd61b67c8d8e7864029d8f6a85b99d49a"}, "time": "2024-01-08T12:59:20+00:00"}, {"version": "v3.1.41", "version_normalized": "********"}, {"version": "v3.1.40", "version_normalized": "********"}, {"version": "v3.1.39", "version_normalized": "********"}, {"version": "v3.1.38", "version_normalized": "********"}, {"version": "v3.1.37", "version_normalized": "********"}, {"version": "v3.1.36", "version_normalized": "********"}, {"version": "v3.1.35", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bb580e362e66ae8071d8386e13841c1dc1a252b4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bb580e362e66ae8071d8386e13841c1dc1a252b4", "type": "zip", "shasum": "", "reference": "bb580e362e66ae8071d8386e13841c1dc1a252b4"}, "time": "2024-01-04T12:29:08+00:00"}, {"version": "v3.1.34", "version_normalized": "********"}, {"version": "v3.1.33", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "111638aefc5cc0e743e7b80c0ac703e24070c41e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/111638aefc5cc0e743e7b80c0ac703e24070c41e", "type": "zip", "shasum": "", "reference": "111638aefc5cc0e743e7b80c0ac703e24070c41e"}, "time": "2024-01-02T23:07:58+00:00"}, {"version": "v3.1.32", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4aa76ba6c58471b140528a8972859303273d0875"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4aa76ba6c58471b140528a8972859303273d0875", "type": "zip", "shasum": "", "reference": "4aa76ba6c58471b140528a8972859303273d0875"}, "time": "2023-12-30T20:25:15+00:00"}, {"version": "v3.1.31", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "97792dad038a87b7a6bfe465c4f1913252c99017"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/97792dad038a87b7a6bfe465c4f1913252c99017", "type": "zip", "shasum": "", "reference": "97792dad038a87b7a6bfe465c4f1913252c99017"}, "time": "2023-12-24T21:16:58+00:00"}, {"version": "v3.1.30", "version_normalized": "********"}, {"version": "v3.1.29", "version_normalized": "********"}, {"version": "v3.1.28", "version_normalized": "********"}, {"version": "v3.1.27", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f00f44dd79e8e1b3f92dea6e2ef6e1ba9e5396dd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f00f44dd79e8e1b3f92dea6e2ef6e1ba9e5396dd", "type": "zip", "shasum": "", "reference": "f00f44dd79e8e1b3f92dea6e2ef6e1ba9e5396dd"}, "time": "2023-12-22T14:36:21+00:00"}, {"version": "v3.1.26", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d41cd962c9a76790aa1a34bae09a9e645a69506d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d41cd962c9a76790aa1a34bae09a9e645a69506d", "type": "zip", "shasum": "", "reference": "d41cd962c9a76790aa1a34bae09a9e645a69506d"}, "time": "2023-12-21T12:38:29+00:00"}, {"version": "v3.1.25", "version_normalized": "********"}, {"version": "v3.1.24", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "797139451b30a7a9b5bc4b7ecad916ddee1075a7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/797139451b30a7a9b5bc4b7ecad916ddee1075a7", "type": "zip", "shasum": "", "reference": "797139451b30a7a9b5bc4b7ecad916ddee1075a7"}, "time": "2023-12-19T13:24:32+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "^3.2.3", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.1.23", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0bf4888b43d2199801ed9416258e30cded736bcd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0bf4888b43d2199801ed9416258e30cded736bcd", "type": "zip", "shasum": "", "reference": "0bf4888b43d2199801ed9416258e30cded736bcd"}, "time": "2023-12-17T22:26:03+00:00"}, {"version": "v3.1.22", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7f82008931af9cc301e0b9c965dd1bcfbc44d584"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7f82008931af9cc301e0b9c965dd1bcfbc44d584", "type": "zip", "shasum": "", "reference": "7f82008931af9cc301e0b9c965dd1bcfbc44d584"}, "time": "2023-12-14T16:21:19+00:00"}, {"version": "v3.1.21", "version_normalized": "********"}, {"version": "v3.1.20", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "421134621e61a25f05f554a4da0d05a726b10d3d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/421134621e61a25f05f554a4da0d05a726b10d3d", "type": "zip", "shasum": "", "reference": "421134621e61a25f05f554a4da0d05a726b10d3d"}, "time": "2023-12-13T13:06:03+00:00"}, {"version": "v3.1.19", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6fbbade84488aecab62ad40f96564cc07c96573f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6fbbade84488aecab62ad40f96564cc07c96573f", "type": "zip", "shasum": "", "reference": "6fbbade84488aecab62ad40f96564cc07c96573f"}, "time": "2023-12-12T14:20:36+00:00"}, {"version": "v3.1.18", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "09d33cb7462dabb249d7cc32fbafb6f567ed8cd6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/09d33cb7462dabb249d7cc32fbafb6f567ed8cd6", "type": "zip", "shasum": "", "reference": "09d33cb7462dabb249d7cc32fbafb6f567ed8cd6"}, "time": "2023-12-10T00:21:13+00:00"}, {"version": "v3.1.17", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c991278c9e3943b9a97c6b38fd27c329d535b808"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c991278c9e3943b9a97c6b38fd27c329d535b808", "type": "zip", "shasum": "", "reference": "c991278c9e3943b9a97c6b38fd27c329d535b808"}, "time": "2023-12-05T13:29:28+00:00"}, {"version": "v3.1.16", "version_normalized": "********"}, {"version": "v3.1.15", "version_normalized": "********"}, {"version": "v3.1.14", "version_normalized": "********"}, {"version": "v3.1.13", "version_normalized": "********"}, {"version": "v3.1.12", "version_normalized": "********"}, {"version": "v3.1.11", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0a40b0de6636d948d26227e1551eaf792b396692"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0a40b0de6636d948d26227e1551eaf792b396692", "type": "zip", "shasum": "", "reference": "0a40b0de6636d948d26227e1551eaf792b396692"}, "time": "2023-12-04T15:57:25+00:00"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e7d5cad1cf8b2940eda745f0e38d32ba0072da19"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e7d5cad1cf8b2940eda745f0e38d32ba0072da19", "type": "zip", "shasum": "", "reference": "e7d5cad1cf8b2940eda745f0e38d32ba0072da19"}, "time": "2023-12-03T23:42:01+00:00"}, {"version": "v3.1.9", "version_normalized": "*******"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2bdf07a954c9873e6b4c6f2654798d4b38f62a27"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2bdf07a954c9873e6b4c6f2654798d4b38f62a27", "type": "zip", "shasum": "", "reference": "2bdf07a954c9873e6b4c6f2654798d4b38f62a27"}, "time": "2023-12-02T22:24:03+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "^3.0.8", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0|^2.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9e50d666d0cb080d34232e0cbfe4a8beb0050398"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9e50d666d0cb080d34232e0cbfe4a8beb0050398", "type": "zip", "shasum": "", "reference": "9e50d666d0cb080d34232e0cbfe4a8beb0050398"}, "time": "2023-12-01T09:44:29+00:00"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "122c875b87cfbd950ebc4c3908972e971ff61516"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/122c875b87cfbd950ebc4c3908972e971ff61516", "type": "zip", "shasum": "", "reference": "122c875b87cfbd950ebc4c3908972e971ff61516"}, "time": "2023-11-30T15:00:21+00:00"}, {"version": "v3.1.5", "version_normalized": "*******"}, {"version": "v3.1.4", "version_normalized": "*******"}, {"version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e0084dd47b03fdb0085ada1b18dcef663aabf10a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e0084dd47b03fdb0085ada1b18dcef663aabf10a", "type": "zip", "shasum": "", "reference": "e0084dd47b03fdb0085ada1b18dcef663aabf10a"}, "time": "2023-11-29T15:29:41+00:00"}, {"version": "v3.1.2", "version_normalized": "*******"}, {"version": "v3.1.1", "version_normalized": "*******"}, {"version": "v3.1.0", "version_normalized": "*******"}, {"version": "v3.1.0-alpha4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c3ed12fa4d7c252b1341b4d96b160c196fbda509"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c3ed12fa4d7c252b1341b4d96b160c196fbda509", "type": "zip", "shasum": "", "reference": "c3ed12fa4d7c252b1341b4d96b160c196fbda509"}, "time": "2023-11-20T15:56:43+00:00"}, {"version": "v3.1.0-alpha3", "version_normalized": "*******-alpha3"}, {"version": "v3.1.0-alpha2", "version_normalized": "*******-alpha2"}, {"version": "v3.1.0-alpha1", "version_normalized": "*******-alpha1"}, {"version": "v3.0.103", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b8c1ff86a06c06717657bab4b29a2b99d79af14d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b8c1ff86a06c06717657bab4b29a2b99d79af14d", "type": "zip", "shasum": "", "reference": "b8c1ff86a06c06717657bab4b29a2b99d79af14d"}, "time": "2023-11-28T14:47:36+00:00"}, {"version": "v3.0.102", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "79bf863c61d4d5e782369390012d5b76523f8def"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/79bf863c61d4d5e782369390012d5b76523f8def", "type": "zip", "shasum": "", "reference": "79bf863c61d4d5e782369390012d5b76523f8def"}, "time": "2023-11-25T23:47:31+00:00"}, {"version": "v3.0.101", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c84e267ab68bea6899a46330f69f88b4d449171a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c84e267ab68bea6899a46330f69f88b4d449171a", "type": "zip", "shasum": "", "reference": "c84e267ab68bea6899a46330f69f88b4d449171a"}, "time": "2023-11-22T10:40:22+00:00"}, {"version": "v3.0.100", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0d2600d1d9136a53b77eb9b10d3818f036515375"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0d2600d1d9136a53b77eb9b10d3818f036515375", "type": "zip", "shasum": "", "reference": "0d2600d1d9136a53b77eb9b10d3818f036515375"}, "time": "2023-11-21T10:53:01+00:00"}, {"version": "v3.0.99", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "98dc3082665bc2396b80dd6acd54b35a9cd68b4c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/98dc3082665bc2396b80dd6acd54b35a9cd68b4c", "type": "zip", "shasum": "", "reference": "98dc3082665bc2396b80dd6acd54b35a9cd68b4c"}, "time": "2023-11-20T15:19:03+00:00"}, {"version": "v3.0.98", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "25e220044d467a335250a45d1e9a081f6bd9aad2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/25e220044d467a335250a45d1e9a081f6bd9aad2", "type": "zip", "shasum": "", "reference": "25e220044d467a335250a45d1e9a081f6bd9aad2"}, "time": "2023-11-18T17:32:24+00:00"}, {"version": "v3.0.97", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3b62b0c3bbe3fa18a444bf1c5eae09240714fc74"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3b62b0c3bbe3fa18a444bf1c5eae09240714fc74", "type": "zip", "shasum": "", "reference": "3b62b0c3bbe3fa18a444bf1c5eae09240714fc74"}, "time": "2023-11-15T11:34:46+00:00"}, {"version": "v3.0.96", "version_normalized": "********"}, {"version": "v3.0.95", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5af6280da51885e2981a4a3f09f95e9674febb06"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5af6280da51885e2981a4a3f09f95e9674febb06", "type": "zip", "shasum": "", "reference": "5af6280da51885e2981a4a3f09f95e9674febb06"}, "time": "2023-11-14T12:07:38+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "^3.0.8", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.94", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1c54fa86a47414d504e38ff3e89c669dc8cfcad6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1c54fa86a47414d504e38ff3e89c669dc8cfcad6", "type": "zip", "shasum": "", "reference": "1c54fa86a47414d504e38ff3e89c669dc8cfcad6"}, "time": "2023-11-09T10:43:09+00:00"}, {"version": "v3.0.93", "version_normalized": "********"}, {"version": "v3.0.92", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2d8ffecf4fbf13e2c0ef178625ce1cac61bd46a6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2d8ffecf4fbf13e2c0ef178625ce1cac61bd46a6", "type": "zip", "shasum": "", "reference": "2d8ffecf4fbf13e2c0ef178625ce1cac61bd46a6"}, "time": "2023-11-08T11:43:32+00:00"}, {"version": "v3.0.91", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "087b4908d223852ad714ccf7196db9bea7851f9d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/087b4908d223852ad714ccf7196db9bea7851f9d", "type": "zip", "shasum": "", "reference": "087b4908d223852ad714ccf7196db9bea7851f9d"}, "time": "2023-11-05T09:01:21+00:00"}, {"version": "v3.0.90", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7973ff53db3610122c73f0f15d86a744b3d44c8e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7973ff53db3610122c73f0f15d86a744b3d44c8e", "type": "zip", "shasum": "", "reference": "7973ff53db3610122c73f0f15d86a744b3d44c8e"}, "time": "2023-11-04T18:33:20+00:00"}, {"version": "v3.0.89", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "de755478f858031eecf0408d6b9999264357a6da"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/de755478f858031eecf0408d6b9999264357a6da", "type": "zip", "shasum": "", "reference": "de755478f858031eecf0408d6b9999264357a6da"}, "time": "2023-11-03T09:01:32+00:00"}, {"version": "v3.0.88", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5e5884f71d9529754d4f890319336535e42fc621"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5e5884f71d9529754d4f890319336535e42fc621", "type": "zip", "shasum": "", "reference": "5e5884f71d9529754d4f890319336535e42fc621"}, "time": "2023-10-30T09:52:26+00:00"}, {"version": "v3.0.87", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5a9e3f05a32b389a65ab98636a2a2bf1505088ac"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5a9e3f05a32b389a65ab98636a2a2bf1505088ac", "type": "zip", "shasum": "", "reference": "5a9e3f05a32b389a65ab98636a2a2bf1505088ac"}, "time": "2023-10-28T22:29:47+00:00"}, {"version": "v3.0.86", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f29126aab952b6020be636efb0ff88ab8ac1b678"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f29126aab952b6020be636efb0ff88ab8ac1b678", "type": "zip", "shasum": "", "reference": "f29126aab952b6020be636efb0ff88ab8ac1b678"}, "time": "2023-10-27T14:35:17+00:00"}, {"version": "v3.0.85", "version_normalized": "********"}, {"version": "v3.0.84", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ed482418e42ff1af28034d981c439b640c88c6aa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ed482418e42ff1af28034d981c439b640c88c6aa", "type": "zip", "shasum": "", "reference": "ed482418e42ff1af28034d981c439b640c88c6aa"}, "time": "2023-10-26T13:32:24+00:00"}, {"version": "v3.0.83", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "049cfdf99e5327feffe17b5c4e832c112248513e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/049cfdf99e5327feffe17b5c4e832c112248513e", "type": "zip", "shasum": "", "reference": "049cfdf99e5327feffe17b5c4e832c112248513e"}, "time": "2023-10-23T11:42:06+00:00"}, {"version": "v3.0.82", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "619cda1c5151473c38556535d155307d2c567a87"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/619cda1c5151473c38556535d155307d2c567a87", "type": "zip", "shasum": "", "reference": "619cda1c5151473c38556535d155307d2c567a87"}, "time": "2023-10-21T12:54:20+00:00"}, {"version": "v3.0.81", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d906f2e57dd3ed1cc5cc78aac506a8cdca4d4623"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d906f2e57dd3ed1cc5cc78aac506a8cdca4d4623", "type": "zip", "shasum": "", "reference": "d906f2e57dd3ed1cc5cc78aac506a8cdca4d4623"}, "time": "2023-10-19T09:20:16+00:00"}, {"version": "v3.0.80", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f67f077999c50709bd56bafd552ff33517035ce0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f67f077999c50709bd56bafd552ff33517035ce0", "type": "zip", "shasum": "", "reference": "f67f077999c50709bd56bafd552ff33517035ce0"}, "time": "2023-10-18T12:31:30+00:00"}, {"version": "v3.0.79", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "70137994f05ca690ea9ab8f5e66595c7cf9a52e5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/70137994f05ca690ea9ab8f5e66595c7cf9a52e5", "type": "zip", "shasum": "", "reference": "70137994f05ca690ea9ab8f5e66595c7cf9a52e5"}, "time": "2023-10-17T14:55:37+00:00"}, {"version": "v3.0.78", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b7526ec582cff71df8ba7d9508ff24ec6cfa7950"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b7526ec582cff71df8ba7d9508ff24ec6cfa7950", "type": "zip", "shasum": "", "reference": "b7526ec582cff71df8ba7d9508ff24ec6cfa7950"}, "time": "2023-10-16T10:54:36+00:00"}, {"version": "v3.0.77", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b276fe1904cc1999ef34ea493ced39780086d839"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b276fe1904cc1999ef34ea493ced39780086d839", "type": "zip", "shasum": "", "reference": "b276fe1904cc1999ef34ea493ced39780086d839"}, "time": "2023-10-15T11:55:23+00:00"}, {"version": "v3.0.76", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1c3d8f738caa4090511f038c62a7f35985cef6b5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1c3d8f738caa4090511f038c62a7f35985cef6b5", "type": "zip", "shasum": "", "reference": "1c3d8f738caa4090511f038c62a7f35985cef6b5"}, "time": "2023-10-14T11:48:56+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "^3.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.75", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b09b5719fce6b33253358287be70928b45cb9e1a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b09b5719fce6b33253358287be70928b45cb9e1a", "type": "zip", "shasum": "", "reference": "b09b5719fce6b33253358287be70928b45cb9e1a"}, "time": "2023-10-13T19:27:07+00:00"}, {"version": "v3.0.74", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ceaae48422028bbe14a0e7dbd9486e3cf8bb33ba"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ceaae48422028bbe14a0e7dbd9486e3cf8bb33ba", "type": "zip", "shasum": "", "reference": "ceaae48422028bbe14a0e7dbd9486e3cf8bb33ba"}, "time": "2023-10-12T12:23:44+00:00"}, {"version": "v3.0.73", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cd368ba90c88789c06c108728fb820637c9226e0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cd368ba90c88789c06c108728fb820637c9226e0", "type": "zip", "shasum": "", "reference": "cd368ba90c88789c06c108728fb820637c9226e0"}, "time": "2023-10-09T13:12:22+00:00"}, {"version": "v3.0.72", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ba6a766357a46da9078be90565d75cff7ab0188e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ba6a766357a46da9078be90565d75cff7ab0188e", "type": "zip", "shasum": "", "reference": "ba6a766357a46da9078be90565d75cff7ab0188e"}, "time": "2023-10-09T12:13:04+00:00"}, {"version": "v3.0.71", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "085b9c9741d6852d4059428b881c1d93846225bb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/085b9c9741d6852d4059428b881c1d93846225bb", "type": "zip", "shasum": "", "reference": "085b9c9741d6852d4059428b881c1d93846225bb"}, "time": "2023-10-09T07:35:49+00:00"}, {"version": "v3.0.70", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3e704a6f6fdd2424c265d54e36fefded922c8188"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3e704a6f6fdd2424c265d54e36fefded922c8188", "type": "zip", "shasum": "", "reference": "3e704a6f6fdd2424c265d54e36fefded922c8188"}, "time": "2023-10-08T18:26:43+00:00"}, {"version": "v3.0.69", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6c4358cf11b8f09effcaaa995b4e553e7c1a94c8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6c4358cf11b8f09effcaaa995b4e553e7c1a94c8", "type": "zip", "shasum": "", "reference": "6c4358cf11b8f09effcaaa995b4e553e7c1a94c8"}, "time": "2023-10-06T15:56:48+00:00"}, {"version": "v3.0.68", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7daa798f1ff2f1101f63374935d4f368a06239e7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7daa798f1ff2f1101f63374935d4f368a06239e7", "type": "zip", "shasum": "", "reference": "7daa798f1ff2f1101f63374935d4f368a06239e7"}, "time": "2023-10-05T15:18:20+00:00"}, {"version": "v3.0.67", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f619cef19853da30ce36688d96b014ca7d966579"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f619cef19853da30ce36688d96b014ca7d966579", "type": "zip", "shasum": "", "reference": "f619cef19853da30ce36688d96b014ca7d966579"}, "time": "2023-10-03T18:16:49+00:00"}, {"version": "v3.0.66", "version_normalized": "********"}, {"version": "v3.0.65", "version_normalized": "********"}, {"version": "v3.0.64", "version_normalized": "********"}, {"version": "v3.0.63", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d4f1fd574969a628c691c1603115769f438f4618"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d4f1fd574969a628c691c1603115769f438f4618", "type": "zip", "shasum": "", "reference": "d4f1fd574969a628c691c1603115769f438f4618"}, "time": "2023-09-30T14:08:13+00:00"}, {"version": "v3.0.62", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "48f4aa87829678bfe50a6e18a81a6428b47b5948"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/48f4aa87829678bfe50a6e18a81a6428b47b5948", "type": "zip", "shasum": "", "reference": "48f4aa87829678bfe50a6e18a81a6428b47b5948"}, "time": "2023-09-25T19:24:07+00:00"}, {"version": "v3.0.61", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "956e1ad922c8adf7f9b89a3a760fb608a31a298e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/956e1ad922c8adf7f9b89a3a760fb608a31a298e", "type": "zip", "shasum": "", "reference": "956e1ad922c8adf7f9b89a3a760fb608a31a298e"}, "time": "2023-09-25T12:42:26+00:00"}, {"version": "v3.0.60", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "20a59233246b020a4944cf9bbccdb1c06e814c83"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/20a59233246b020a4944cf9bbccdb1c06e814c83", "type": "zip", "shasum": "", "reference": "20a59233246b020a4944cf9bbccdb1c06e814c83"}, "time": "2023-09-22T07:21:49+00:00"}, {"version": "v3.0.59", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8c83b356e3b8d0b437366de623525b5e91972ec9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8c83b356e3b8d0b437366de623525b5e91972ec9", "type": "zip", "shasum": "", "reference": "8c83b356e3b8d0b437366de623525b5e91972ec9"}, "time": "2023-09-21T21:29:39+00:00"}, {"version": "v3.0.58", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0b160d9fc2041f3115215c4ac327630cad19c429"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0b160d9fc2041f3115215c4ac327630cad19c429", "type": "zip", "shasum": "", "reference": "0b160d9fc2041f3115215c4ac327630cad19c429"}, "time": "2023-09-21T18:40:02+00:00"}, {"version": "v3.0.57", "version_normalized": "********"}, {"version": "v3.0.56", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "44a09b61287431ec362d0b3fb17134d19447ed0c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/44a09b61287431ec362d0b3fb17134d19447ed0c", "type": "zip", "shasum": "", "reference": "44a09b61287431ec362d0b3fb17134d19447ed0c"}, "time": "2023-09-20T12:53:31+00:00"}, {"version": "v3.0.55", "version_normalized": "********"}, {"version": "v3.0.54", "version_normalized": "********"}, {"version": "v3.0.53", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9f6fa9a2c03f45eccbca83a083535fb628f73b97"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9f6fa9a2c03f45eccbca83a083535fb628f73b97", "type": "zip", "shasum": "", "reference": "9f6fa9a2c03f45eccbca83a083535fb628f73b97"}, "time": "2023-09-18T10:30:36+00:00"}, {"version": "v3.0.52", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "183354b6fe1af1bbda335818c2e412fb7c1869e9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/183354b6fe1af1bbda335818c2e412fb7c1869e9", "type": "zip", "shasum": "", "reference": "183354b6fe1af1bbda335818c2e412fb7c1869e9"}, "time": "2023-09-16T10:26:04+00:00"}, {"version": "v3.0.51", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9380b4c795980947272581d73cbc5a9af10cd5fb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9380b4c795980947272581d73cbc5a9af10cd5fb", "type": "zip", "shasum": "", "reference": "9380b4c795980947272581d73cbc5a9af10cd5fb"}, "time": "2023-09-15T12:35:47+00:00"}, {"version": "v3.0.50", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2cd32c128b53d482f5e08f28ab1e40377bbdacb6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2cd32c128b53d482f5e08f28ab1e40377bbdacb6", "type": "zip", "shasum": "", "reference": "2cd32c128b53d482f5e08f28ab1e40377bbdacb6"}, "time": "2023-09-14T08:52:06+00:00"}, {"version": "v3.0.49", "version_normalized": "********"}, {"version": "v3.0.48", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0140227c8969179b5c1cd5044be5d0382e4af2e0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0140227c8969179b5c1cd5044be5d0382e4af2e0", "type": "zip", "shasum": "", "reference": "0140227c8969179b5c1cd5044be5d0382e4af2e0"}, "time": "2023-09-10T23:29:54+00:00"}, {"version": "v3.0.47", "version_normalized": "********"}, {"version": "v3.0.46", "version_normalized": "********"}, {"version": "v3.0.45", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "50d862cdeeecfa472cd9d18267e312d5e6878ab4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/50d862cdeeecfa472cd9d18267e312d5e6878ab4", "type": "zip", "shasum": "", "reference": "50d862cdeeecfa472cd9d18267e312d5e6878ab4"}, "time": "2023-09-09T23:05:54+00:00"}, {"version": "v3.0.44", "version_normalized": "********"}, {"version": "v3.0.43", "version_normalized": "********"}, {"version": "v3.0.42", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "75bca535099569f757905b1ade58a9233a4c29c8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/75bca535099569f757905b1ade58a9233a4c29c8", "type": "zip", "shasum": "", "reference": "75bca535099569f757905b1ade58a9233a4c29c8"}, "time": "2023-09-09T14:58:35+00:00"}, {"version": "v3.0.41", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fdc0c91c472712b7e45e54be532b477a4aa91885"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fdc0c91c472712b7e45e54be532b477a4aa91885", "type": "zip", "shasum": "", "reference": "fdc0c91c472712b7e45e54be532b477a4aa91885"}, "time": "2023-09-06T14:23:08+00:00"}, {"version": "v3.0.40", "version_normalized": "********"}, {"version": "v3.0.39", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d0eec9eb810928905108c8588da28f74845e67e2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d0eec9eb810928905108c8588da28f74845e67e2", "type": "zip", "shasum": "", "reference": "d0eec9eb810928905108c8588da28f74845e67e2"}, "time": "2023-09-01T05:50:43+00:00"}, {"version": "v3.0.38", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "49bf916d75cf5fc827888d0434a71c7352b12c5c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/49bf916d75cf5fc827888d0434a71c7352b12c5c", "type": "zip", "shasum": "", "reference": "49bf916d75cf5fc827888d0434a71c7352b12c5c"}, "time": "2023-08-31T11:14:56+00:00"}, {"version": "v3.0.37", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b4e8f17592b02e05fdac9e7801603feb71f1b4cb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b4e8f17592b02e05fdac9e7801603feb71f1b4cb", "type": "zip", "shasum": "", "reference": "b4e8f17592b02e05fdac9e7801603feb71f1b4cb"}, "time": "2023-08-29T20:13:03+00:00"}, {"version": "v3.0.36", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c420d73a915a5525ff81eb3bb81f2d57e911f25d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c420d73a915a5525ff81eb3bb81f2d57e911f25d", "type": "zip", "shasum": "", "reference": "c420d73a915a5525ff81eb3bb81f2d57e911f25d"}, "time": "2023-08-28T07:48:43+00:00"}, {"version": "v3.0.35", "version_normalized": "********"}, {"version": "v3.0.34", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9fefb4d11ce11421ffeecc648afab56315c27ab6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9fefb4d11ce11421ffeecc648afab56315c27ab6", "type": "zip", "shasum": "", "reference": "9fefb4d11ce11421ffeecc648afab56315c27ab6"}, "time": "2023-08-24T21:02:32+00:00"}, {"version": "v3.0.33", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6009d4380e24c207debf22e42be1007c965ed71b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6009d4380e24c207debf22e42be1007c965ed71b", "type": "zip", "shasum": "", "reference": "6009d4380e24c207debf22e42be1007c965ed71b"}, "time": "2023-08-24T15:59:54+00:00"}, {"version": "v3.0.32", "version_normalized": "********"}, {"version": "v3.0.31", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e37d846fa94c273adab91287a777eb1c4d162b91"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e37d846fa94c273adab91287a777eb1c4d162b91", "type": "zip", "shasum": "", "reference": "e37d846fa94c273adab91287a777eb1c4d162b91"}, "time": "2023-08-23T12:39:34+00:00"}, {"version": "v3.0.30", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "522a6e1e7d5acde77d27527f566bbde1ab18bc1b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/522a6e1e7d5acde77d27527f566bbde1ab18bc1b", "type": "zip", "shasum": "", "reference": "522a6e1e7d5acde77d27527f566bbde1ab18bc1b"}, "time": "2023-08-21T10:29:18+00:00"}, {"version": "v3.0.29", "version_normalized": "********"}, {"version": "v3.0.28", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e71448d2f186ad46ae5203b62befcce5dfd83efe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e71448d2f186ad46ae5203b62befcce5dfd83efe", "type": "zip", "shasum": "", "reference": "e71448d2f186ad46ae5203b62befcce5dfd83efe"}, "time": "2023-08-21T09:36:57+00:00"}, {"version": "v3.0.27", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b1ff6fe670f74230c92c12af4e4eb627c2ff14d1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b1ff6fe670f74230c92c12af4e4eb627c2ff14d1", "type": "zip", "shasum": "", "reference": "b1ff6fe670f74230c92c12af4e4eb627c2ff14d1"}, "time": "2023-08-18T15:09:08+00:00"}, {"version": "v3.0.26", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0b252b050531f2cc820ddb9099c8caeffe490ca5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0b252b050531f2cc820ddb9099c8caeffe490ca5", "type": "zip", "shasum": "", "reference": "0b252b050531f2cc820ddb9099c8caeffe490ca5"}, "time": "2023-08-17T12:59:44+00:00"}, {"version": "v3.0.25", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6ec70d89782dcbc334aa91044e3e04e76c773bf2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6ec70d89782dcbc334aa91044e3e04e76c773bf2", "type": "zip", "shasum": "", "reference": "6ec70d89782dcbc334aa91044e3e04e76c773bf2"}, "time": "2023-08-17T08:51:04+00:00"}, {"version": "v3.0.24", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f09ff6ac42174ae93ccd6d2882fec212189d9ece"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f09ff6ac42174ae93ccd6d2882fec212189d9ece", "type": "zip", "shasum": "", "reference": "f09ff6ac42174ae93ccd6d2882fec212189d9ece"}, "time": "2023-08-16T22:47:42+00:00"}, {"version": "v3.0.23", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1ef73c892d8a3d0f003753151300e354a49e4a9d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1ef73c892d8a3d0f003753151300e354a49e4a9d", "type": "zip", "shasum": "", "reference": "1ef73c892d8a3d0f003753151300e354a49e4a9d"}, "time": "2023-08-15T20:18:37+00:00"}, {"version": "v3.0.22", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b28f3ee7f1bace8be53a64c0b250c369beef620d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b28f3ee7f1bace8be53a64c0b250c369beef620d", "type": "zip", "shasum": "", "reference": "b28f3ee7f1bace8be53a64c0b250c369beef620d"}, "time": "2023-08-15T11:21:04+00:00"}, {"version": "v3.0.21", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b727fa00943813310a5a6cc0e811a7503fdfeeb5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b727fa00943813310a5a6cc0e811a7503fdfeeb5", "type": "zip", "shasum": "", "reference": "b727fa00943813310a5a6cc0e811a7503fdfeeb5"}, "time": "2023-08-14T14:24:27+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "v3.0.0-beta.7", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.20", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "81111e3f393ecabd66e36f9a7ce6a6e738744726"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/81111e3f393ecabd66e36f9a7ce6a6e738744726", "type": "zip", "shasum": "", "reference": "81111e3f393ecabd66e36f9a7ce6a6e738744726"}, "time": "2023-08-12T12:12:27+00:00"}, {"version": "v3.0.19", "version_normalized": "********"}, {"version": "v3.0.18", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ea8d4c864e03c660c82c51852879b08d2778e764"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ea8d4c864e03c660c82c51852879b08d2778e764", "type": "zip", "shasum": "", "reference": "ea8d4c864e03c660c82c51852879b08d2778e764"}, "time": "2023-08-11T07:13:13+00:00"}, {"version": "v3.0.17", "version_normalized": "********"}, {"version": "v3.0.16", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "667f5f119674815310fd731b20f3fcc4c768f010"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/667f5f119674815310fd731b20f3fcc4c768f010", "type": "zip", "shasum": "", "reference": "667f5f119674815310fd731b20f3fcc4c768f010"}, "time": "2023-08-10T10:44:34+00:00"}, {"version": "v3.0.15", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "492a2306c8d4dd2495f65a89072dd1b40c1ecde1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/492a2306c8d4dd2495f65a89072dd1b40c1ecde1", "type": "zip", "shasum": "", "reference": "492a2306c8d4dd2495f65a89072dd1b40c1ecde1"}, "time": "2023-08-09T14:31:54+00:00"}, {"version": "v3.0.14", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c845fb6ffe5af3724c3b5925d3ebfca5d6240a85"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c845fb6ffe5af3724c3b5925d3ebfca5d6240a85", "type": "zip", "shasum": "", "reference": "c845fb6ffe5af3724c3b5925d3ebfca5d6240a85"}, "time": "2023-08-09T09:48:16+00:00"}, {"version": "v3.0.13", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ae9317a0a396d084849fc2acc9843cbf3136fa39"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ae9317a0a396d084849fc2acc9843cbf3136fa39", "type": "zip", "shasum": "", "reference": "ae9317a0a396d084849fc2acc9843cbf3136fa39"}, "time": "2023-08-09T06:33:13+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^10.0", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "livewire/livewire": "^3.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.12", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b9379c262ee64fccb3923bfbdc213c384d20729e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b9379c262ee64fccb3923bfbdc213c384d20729e", "type": "zip", "shasum": "", "reference": "b9379c262ee64fccb3923bfbdc213c384d20729e"}, "time": "2023-08-07T23:55:04+00:00"}, {"version": "v3.0.11", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fc7e9c8962f469a3a636b4901dbaf87990bb9cbf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fc7e9c8962f469a3a636b4901dbaf87990bb9cbf", "type": "zip", "shasum": "", "reference": "fc7e9c8962f469a3a636b4901dbaf87990bb9cbf"}, "time": "2023-08-07T10:17:12+00:00"}, {"version": "v3.0.10", "version_normalized": "********"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "10b2f3e4e7fe6956b984ef4616f370334b7c553c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/10b2f3e4e7fe6956b984ef4616f370334b7c553c", "type": "zip", "shasum": "", "reference": "10b2f3e4e7fe6956b984ef4616f370334b7c553c"}, "time": "2023-08-06T22:54:21+00:00"}, {"version": "v3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fe4a51443b3a0925a9122ed7557deedf1371b684"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fe4a51443b3a0925a9122ed7557deedf1371b684", "type": "zip", "shasum": "", "reference": "fe4a51443b3a0925a9122ed7557deedf1371b684"}, "time": "2023-08-05T08:47:49+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "livewire/livewire": "^3.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b96fe0d000ead18f5b1cfe8b0b2009e22faf505a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b96fe0d000ead18f5b1cfe8b0b2009e22faf505a", "type": "zip", "shasum": "", "reference": "b96fe0d000ead18f5b1cfe8b0b2009e22faf505a"}, "time": "2023-08-03T12:41:56+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6ad2910057d9ab449b08c36452858bbcf82bf207"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6ad2910057d9ab449b08c36452858bbcf82bf207", "type": "zip", "shasum": "", "reference": "6ad2910057d9ab449b08c36452858bbcf82bf207"}, "time": "2023-08-03T09:30:13+00:00"}, {"version": "v3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "23dc0f7b2d22effebe4d6304f5e39a7dd821e7a6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/23dc0f7b2d22effebe4d6304f5e39a7dd821e7a6", "type": "zip", "shasum": "", "reference": "23dc0f7b2d22effebe4d6304f5e39a7dd821e7a6"}, "time": "2023-08-02T12:51:05+00:00"}, {"version": "v3.0.4", "version_normalized": "*******"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3c74e1ed74ca20cd968f2055816f898175243566"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3c74e1ed74ca20cd968f2055816f898175243566", "type": "zip", "shasum": "", "reference": "3c74e1ed74ca20cd968f2055816f898175243566"}, "time": "2023-08-02T09:40:18+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "de5504415b963c2a825a021408c3b0506b04a4ab"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/de5504415b963c2a825a021408c3b0506b04a4ab", "type": "zip", "shasum": "", "reference": "de5504415b963c2a825a021408c3b0506b04a4ab"}, "time": "2023-08-01T22:16:08+00:00"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d6350689dabe0dd02373d36f66bfa9a2ce440dd3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d6350689dabe0dd02373d36f66bfa9a2ce440dd3", "type": "zip", "shasum": "", "reference": "d6350689dabe0dd02373d36f66bfa9a2ce440dd3"}, "time": "2023-08-01T22:10:35+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "livewire/livewire": "^3.0", "psr/http-message": "^1.1", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "956d6ffdf2da41798e98ec9bd7088fb42bac218e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/956d6ffdf2da41798e98ec9bd7088fb42bac218e", "type": "zip", "shasum": "", "reference": "956d6ffdf2da41798e98ec9bd7088fb42bac218e"}, "time": "2023-08-01T12:41:15+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "doctrine/dbal": "^3.2", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "livewire/livewire": "^3.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.0-beta28", "version_normalized": "*******-beta28"}, {"version": "v3.0.0-beta27", "version_normalized": "*******-beta27", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5bf2c19303afa673498a10316fad6c6183a312e7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5bf2c19303afa673498a10316fad6c6183a312e7", "type": "zip", "shasum": "", "reference": "5bf2c19303afa673498a10316fad6c6183a312e7"}, "time": "2023-08-01T07:38:37+00:00"}, {"version": "v3.0.0-beta26", "version_normalized": "*******-beta26", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "952953c7f32f070291d636da94cd6cad13d8dd40"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/952953c7f32f070291d636da94cd6cad13d8dd40", "type": "zip", "shasum": "", "reference": "952953c7f32f070291d636da94cd6cad13d8dd40"}, "time": "2023-07-31T23:43:47+00:00"}, {"version": "v3.0.0-beta25", "version_normalized": "*******-beta25", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "780ba4a5021bfd7a8bb6a02a630f74c09719b512"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/780ba4a5021bfd7a8bb6a02a630f74c09719b512", "type": "zip", "shasum": "", "reference": "780ba4a5021bfd7a8bb6a02a630f74c09719b512"}, "time": "2023-07-31T21:29:23+00:00"}, {"version": "v3.0.0-beta24", "version_normalized": "*******-beta24", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e315e3a23040b200b6fb1bdbaa3f74012eb1fc13"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e315e3a23040b200b6fb1bdbaa3f74012eb1fc13", "type": "zip", "shasum": "", "reference": "e315e3a23040b200b6fb1bdbaa3f74012eb1fc13"}, "time": "2023-07-31T21:14:50+00:00"}, {"version": "v3.0.0-beta23", "version_normalized": "*******-beta23", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d9a7c18c4885117ed5cfaf9170fd6df06e773bd2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d9a7c18c4885117ed5cfaf9170fd6df06e773bd2", "type": "zip", "shasum": "", "reference": "d9a7c18c4885117ed5cfaf9170fd6df06e773bd2"}, "time": "2023-07-31T18:20:46+00:00"}, {"version": "v3.0.0-beta22", "version_normalized": "*******-beta22"}, {"version": "v3.0.0-beta21", "version_normalized": "*******-beta21", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cdd50815a58a12231f946b10b22a8befde08fdb4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cdd50815a58a12231f946b10b22a8befde08fdb4", "type": "zip", "shasum": "", "reference": "cdd50815a58a12231f946b10b22a8befde08fdb4"}, "time": "2023-07-29T12:31:33+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-heroicons": "^2.0", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "livewire/livewire": "^3.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.0-beta20", "version_normalized": "*******-beta20"}, {"version": "v3.0.0-beta19", "version_normalized": "*******-beta19", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2354487dcee29e2ad43e1e96b0dc3103f0dc6b62"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2354487dcee29e2ad43e1e96b0dc3103f0dc6b62", "type": "zip", "shasum": "", "reference": "2354487dcee29e2ad43e1e96b0dc3103f0dc6b62"}, "time": "2023-07-29T10:45:07+00:00"}, {"version": "v3.0.0-beta18", "version_normalized": "*******-beta18", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8b7acbcf0924c337e8b4b614fe82251ee43b0da4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8b7acbcf0924c337e8b4b614fe82251ee43b0da4", "type": "zip", "shasum": "", "reference": "8b7acbcf0924c337e8b4b614fe82251ee43b0da4"}, "time": "2023-07-29T09:13:54+00:00"}, {"version": "v3.0.0-beta17", "version_normalized": "*******-beta17", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7ea28733a3ac10507d79d4a0f30907c18d41f342"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7ea28733a3ac10507d79d4a0f30907c18d41f342", "type": "zip", "shasum": "", "reference": "7ea28733a3ac10507d79d4a0f30907c18d41f342"}, "time": "2023-07-26T19:47:56+00:00"}, {"version": "v3.0.0-beta16", "version_normalized": "*******-beta16"}, {"version": "v3.0.0-beta15", "version_normalized": "*******-beta15"}, {"version": "v3.0.0-beta14", "version_normalized": "*******-beta14"}, {"version": "v3.0.0-beta13", "version_normalized": "*******-beta13", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3b09abb35f5472695a9e17bbcbc0ea6d7a543285"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3b09abb35f5472695a9e17bbcbc0ea6d7a543285", "type": "zip", "shasum": "", "reference": "3b09abb35f5472695a9e17bbcbc0ea6d7a543285"}, "time": "2023-07-26T19:39:52+00:00"}, {"version": "v3.0.0-beta12", "version_normalized": "*******-beta12", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3579311721c93088bf1d511a6ce6ca1e70447f7c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3579311721c93088bf1d511a6ce6ca1e70447f7c", "type": "zip", "shasum": "", "reference": "3579311721c93088bf1d511a6ce6ca1e70447f7c"}, "time": "2023-07-26T18:33:12+00:00"}, {"version": "v3.0.0-beta11", "version_normalized": "*******-beta11", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ac567c101fc0beedd4e46c0f6858e51bb3ead373"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ac567c101fc0beedd4e46c0f6858e51bb3ead373", "type": "zip", "shasum": "", "reference": "ac567c101fc0beedd4e46c0f6858e51bb3ead373"}, "time": "2023-07-26T13:02:07+00:00"}, {"version": "v3.0.0-beta10", "version_normalized": "*******-beta10", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "32e17eaf1d0e3654a497c2595084eecc00d0456b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/32e17eaf1d0e3654a497c2595084eecc00d0456b", "type": "zip", "shasum": "", "reference": "32e17eaf1d0e3654a497c2595084eecc00d0456b"}, "time": "2023-07-25T18:02:27+00:00"}, {"version": "v3.0.0-beta9", "version_normalized": "*******-beta9", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3f001147ed025c83d83794c3712f53e80368237f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3f001147ed025c83d83794c3712f53e80368237f", "type": "zip", "shasum": "", "reference": "3f001147ed025c83d83794c3712f53e80368237f"}, "time": "2023-07-24T11:01:59+00:00"}, {"version": "v3.0.0-beta8", "version_normalized": "*******-beta8", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "********************1f497e23c2baabc5584e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/********************1f497e23c2baabc5584e", "type": "zip", "shasum": "", "reference": "********************1f497e23c2baabc5584e"}, "time": "2023-07-24T10:00:57+00:00"}, {"version": "v3.0.0-beta7", "version_normalized": "*******-beta7", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4912aa50cb0e88016af0e3e2860b05b96ea67cca"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4912aa50cb0e88016af0e3e2860b05b96ea67cca", "type": "zip", "shasum": "", "reference": "4912aa50cb0e88016af0e3e2860b05b96ea67cca"}, "time": "2023-07-23T18:58:20+00:00"}, {"version": "v3.0.0-beta6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3022072bf6fecbeb2319f5a6df6fc9d7b6e7bfe2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3022072bf6fecbeb2319f5a6df6fc9d7b6e7bfe2", "type": "zip", "shasum": "", "reference": "3022072bf6fecbeb2319f5a6df6fc9d7b6e7bfe2"}, "time": "2023-07-23T12:47:18+00:00"}, {"version": "v3.0.0-beta5", "version_normalized": "*******-beta5"}, {"version": "v3.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "513d2f540dfde7e816bd1651fe4939bc045922a2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/513d2f540dfde7e816bd1651fe4939bc045922a2", "type": "zip", "shasum": "", "reference": "513d2f540dfde7e816bd1651fe4939bc045922a2"}, "time": "2023-07-21T16:13:35+00:00"}, {"version": "v3.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7597719d29f899e3054135f0253bd9dbdf1efbb5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7597719d29f899e3054135f0253bd9dbdf1efbb5", "type": "zip", "shasum": "", "reference": "7597719d29f899e3054135f0253bd9dbdf1efbb5"}, "time": "2023-07-20T15:47:20+00:00"}, {"version": "v3.0.0-beta2", "version_normalized": "*******-beta2"}, {"version": "v3.0.0-beta1", "version_normalized": "*******-beta1"}, {"description": "Associated helper methods and foundation code for Filament packages.", "version": "v3.0.0-alpha137", "version_normalized": "*******-alpha137", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c0bcc080cf4bc88f976c11d269302993d9d5465e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c0bcc080cf4bc88f976c11d269302993d9d5465e", "type": "zip", "shasum": "", "reference": "c0bcc080cf4bc88f976c11d269302993d9d5465e"}, "time": "2023-10-03T11:02:43+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-icons": "^1.4", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "symfony/html-sanitizer": "^6.1"}}, {"version": "v3.0.0-alpha136", "version_normalized": "*******-alpha136"}, {"version": "v3.0.0-alpha135", "version_normalized": "*******-alpha135"}, {"version": "v3.0.0-alpha132", "version_normalized": "*******-alpha132", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3a43f1751140f8bc2633ca5f4a75e24281625d4e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3a43f1751140f8bc2633ca5f4a75e24281625d4e", "type": "zip", "shasum": "", "reference": "3a43f1751140f8bc2633ca5f4a75e24281625d4e"}, "time": "2023-08-21T07:29:50+00:00"}, {"version": "v3.0.0-alpha131", "version_normalized": "*******-alpha131", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6150d58914edd31655645a4a45e78ca454eaf2e0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6150d58914edd31655645a4a45e78ca454eaf2e0", "type": "zip", "shasum": "", "reference": "6150d58914edd31655645a4a45e78ca454eaf2e0"}, "time": "2023-08-17T07:39:35+00:00"}, {"version": "v3.0.0-alpha130", "version_normalized": "*******-alpha130", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4335b836f539c8a0fabbcdaaaf8fbd659143c246"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4335b836f539c8a0fabbcdaaaf8fbd659143c246", "type": "zip", "shasum": "", "reference": "4335b836f539c8a0fabbcdaaaf8fbd659143c246"}, "time": "2023-08-14T07:57:03+00:00"}, {"version": "v3.0.0-alpha129", "version_normalized": "*******-alpha129", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "637d31133d42f6ecccba3e25c9fb942b94584fdb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/637d31133d42f6ecccba3e25c9fb942b94584fdb", "type": "zip", "shasum": "", "reference": "637d31133d42f6ecccba3e25c9fb942b94584fdb"}, "time": "2023-07-11T09:49:09+00:00"}, {"version": "v3.0.0-alpha128", "version_normalized": "*******-alpha128"}, {"version": "v3.0.0-alpha127", "version_normalized": "*******-alpha127"}, {"version": "v3.0.0-alpha126", "version_normalized": "*******-alpha126"}, {"version": "v3.0.0-alpha125", "version_normalized": "*******-alpha125"}, {"version": "v3.0.0-alpha124", "version_normalized": "*******-alpha124"}, {"version": "v3.0.0-alpha123", "version_normalized": "*******-alpha123"}, {"version": "v3.0.0-alpha122", "version_normalized": "*******-alpha122", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8785f4f51463bae16e9e9ac353c6f36d91df529d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8785f4f51463bae16e9e9ac353c6f36d91df529d", "type": "zip", "shasum": "", "reference": "8785f4f51463bae16e9e9ac353c6f36d91df529d"}, "time": "2023-07-07T21:04:41+00:00"}, {"version": "v3.0.0-alpha121", "version_normalized": "*******-alpha121", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3206c12ac6b76d7f857141ce7f30fc865c13d1fd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3206c12ac6b76d7f857141ce7f30fc865c13d1fd", "type": "zip", "shasum": "", "reference": "3206c12ac6b76d7f857141ce7f30fc865c13d1fd"}, "time": "2023-07-03T09:28:34+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-icons": "^1.4", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v3.0.0-alpha120", "version_normalized": "*******-alpha120", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "576b29d3da739de315ac4d25cdcf1bf7aab64800"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/576b29d3da739de315ac4d25cdcf1bf7aab64800", "type": "zip", "shasum": "", "reference": "576b29d3da739de315ac4d25cdcf1bf7aab64800"}, "time": "2023-06-29T22:14:31+00:00"}, {"version": "v3.0.0-alpha119", "version_normalized": "*******-alpha119", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ad9e78ff9c697947e923d62e7235578e3d1834d8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ad9e78ff9c697947e923d62e7235578e3d1834d8", "type": "zip", "shasum": "", "reference": "ad9e78ff9c697947e923d62e7235578e3d1834d8"}, "time": "2023-06-29T14:09:07+00:00"}, {"version": "v3.0.0-alpha118", "version_normalized": "*******-alpha118", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "596071382dd74f52ac33d3e08afd91ee6897e5c8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/596071382dd74f52ac33d3e08afd91ee6897e5c8", "type": "zip", "shasum": "", "reference": "596071382dd74f52ac33d3e08afd91ee6897e5c8"}, "time": "2023-06-28T10:39:27+00:00"}, {"version": "v3.0.0-alpha117", "version_normalized": "*******-alpha117", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c0141f93263e587516317a2eddc0ed9f9302fc2f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c0141f93263e587516317a2eddc0ed9f9302fc2f", "type": "zip", "shasum": "", "reference": "c0141f93263e587516317a2eddc0ed9f9302fc2f"}, "time": "2023-06-23T08:16:40+00:00"}, {"version": "v3.0.0-alpha116", "version_normalized": "*******-alpha116"}, {"version": "v3.0.0-alpha115", "version_normalized": "*******-alpha115", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ebd2a11ac58f08e255a3c66b296c42e768f091c3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ebd2a11ac58f08e255a3c66b296c42e768f091c3", "type": "zip", "shasum": "", "reference": "ebd2a11ac58f08e255a3c66b296c42e768f091c3"}, "time": "2023-06-22T21:55:06+00:00"}, {"version": "v3.0.0-alpha114", "version_normalized": "*******-alpha114", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "28ad62aa8ea77d5e5210897e3c8a586219861845"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/28ad62aa8ea77d5e5210897e3c8a586219861845", "type": "zip", "shasum": "", "reference": "28ad62aa8ea77d5e5210897e3c8a586219861845"}, "time": "2023-06-21T10:28:21+00:00"}, {"version": "v3.0.0-alpha113", "version_normalized": "*******-alpha113", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "57e65b017e12cd7d3818559b0a513a6fb72a88c3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/57e65b017e12cd7d3818559b0a513a6fb72a88c3", "type": "zip", "shasum": "", "reference": "57e65b017e12cd7d3818559b0a513a6fb72a88c3"}, "time": "2023-06-21T09:46:55+00:00"}, {"version": "v3.0.0-alpha112", "version_normalized": "*******-alpha112", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ac16c9135f235eb0b68d2ae2a6f6598033588864"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ac16c9135f235eb0b68d2ae2a6f6598033588864", "type": "zip", "shasum": "", "reference": "ac16c9135f235eb0b68d2ae2a6f6598033588864"}, "time": "2023-06-21T09:45:38+00:00"}, {"version": "v3.0.0-alpha111", "version_normalized": "*******-alpha111", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "40024a83bc465d78b3a1e2f469c02b4877885831"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/40024a83bc465d78b3a1e2f469c02b4877885831", "type": "zip", "shasum": "", "reference": "40024a83bc465d78b3a1e2f469c02b4877885831"}, "time": "2023-06-19T10:54:05+00:00"}, {"version": "v3.0.0-alpha110", "version_normalized": "*******-alpha110", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d889cbf07631d0a91b1dc313d491983f11c2028a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d889cbf07631d0a91b1dc313d491983f11c2028a", "type": "zip", "shasum": "", "reference": "d889cbf07631d0a91b1dc313d491983f11c2028a"}, "time": "2023-06-14T08:20:13+00:00"}, {"version": "v3.0.0-alpha109", "version_normalized": "*******-alpha109", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0cdbdf3a9adc886dcc330d95851c6899303fec3d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0cdbdf3a9adc886dcc330d95851c6899303fec3d", "type": "zip", "shasum": "", "reference": "0cdbdf3a9adc886dcc330d95851c6899303fec3d"}, "time": "2023-06-11T17:08:45+00:00"}, {"version": "v3.0.0-alpha108", "version_normalized": "*******-alpha108", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6093d142af76af971c314042ea5dc4a4bde42946"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6093d142af76af971c314042ea5dc4a4bde42946", "type": "zip", "shasum": "", "reference": "6093d142af76af971c314042ea5dc4a4bde42946"}, "time": "2023-06-02T10:08:10+00:00", "require": {"php": "^8.1", "blade-ui-kit/blade-icons": "^1.4", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v3.0.0-alpha107", "version_normalized": "*******-alpha107", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d6448022ede18a380c7fe4371df4f645a7aad4e9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d6448022ede18a380c7fe4371df4f645a7aad4e9", "type": "zip", "shasum": "", "reference": "d6448022ede18a380c7fe4371df4f645a7aad4e9"}, "time": "2023-05-31T09:02:11+00:00"}, {"version": "v3.0.0-alpha106", "version_normalized": "*******-alpha106"}, {"version": "v3.0.0-alpha105", "version_normalized": "*******-alpha105", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "efbb110489cafcea0cd8176cbaa745bad883f9d9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/efbb110489cafcea0cd8176cbaa745bad883f9d9", "type": "zip", "shasum": "", "reference": "efbb110489cafcea0cd8176cbaa745bad883f9d9"}, "time": "2023-05-30T17:28:19+00:00"}, {"version": "v3.0.0-alpha104", "version_normalized": "*******-alpha104", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8333c135418768769745fa81e30ead1fcbfb3041"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8333c135418768769745fa81e30ead1fcbfb3041", "type": "zip", "shasum": "", "reference": "8333c135418768769745fa81e30ead1fcbfb3041"}, "time": "2023-05-22T21:22:58+00:00"}, {"version": "v3.0.0-alpha103", "version_normalized": "*******-alpha103"}, {"version": "v3.0.0-alpha102", "version_normalized": "*******-alpha102"}, {"version": "v3.0.0-alpha101", "version_normalized": "*******-alpha101", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a59e44f5a289680bf3a5e79a39af33784606451f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a59e44f5a289680bf3a5e79a39af33784606451f", "type": "zip", "shasum": "", "reference": "a59e44f5a289680bf3a5e79a39af33784606451f"}, "time": "2023-05-20T17:47:31+00:00"}, {"version": "v3.0.0-alpha100", "version_normalized": "*******-alpha100", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bde5da9324579c5aaf1fa4ea109dd96ff05ca863"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bde5da9324579c5aaf1fa4ea109dd96ff05ca863", "type": "zip", "shasum": "", "reference": "bde5da9324579c5aaf1fa4ea109dd96ff05ca863"}, "time": "2023-05-17T16:35:50+00:00"}, {"version": "v3.0.0-alpha99", "version_normalized": "*******-alpha99"}, {"version": "v3.0.0-alpha98", "version_normalized": "*******-alpha98"}, {"version": "v3.0.0-alpha97", "version_normalized": "*******-alpha97", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "23d7bbb08b258e461e8e7dab71e28f3362598f12"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/23d7bbb08b258e461e8e7dab71e28f3362598f12", "type": "zip", "shasum": "", "reference": "23d7bbb08b258e461e8e7dab71e28f3362598f12"}, "time": "2023-05-15T14:16:33+00:00"}, {"version": "v3.0.0-alpha96", "version_normalized": "*******-alpha96"}, {"version": "v3.0.0-alpha95", "version_normalized": "*******-alpha95", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1b9ef389d1697d56c342deeee1d302bba36df688"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1b9ef389d1697d56c342deeee1d302bba36df688", "type": "zip", "shasum": "", "reference": "1b9ef389d1697d56c342deeee1d302bba36df688"}, "time": "2023-05-15T13:02:20+00:00"}, {"version": "v3.0.0-alpha94", "version_normalized": "*******-alpha94", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "09c4a33506437f58a3adbea7bbfd67b3b486f91a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/09c4a33506437f58a3adbea7bbfd67b3b486f91a", "type": "zip", "shasum": "", "reference": "09c4a33506437f58a3adbea7bbfd67b3b486f91a"}, "time": "2023-05-13T07:23:13+00:00"}, {"version": "v3.0.0-alpha93", "version_normalized": "*******-alpha93"}, {"version": "v3.0.0-alpha92", "version_normalized": "*******-alpha92"}, {"version": "v3.0.0-alpha91", "version_normalized": "*******-alpha91", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b8e1a0bf5e4d9eefe9f2074154a60372dd1cc29f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b8e1a0bf5e4d9eefe9f2074154a60372dd1cc29f", "type": "zip", "shasum": "", "reference": "b8e1a0bf5e4d9eefe9f2074154a60372dd1cc29f"}, "time": "2023-05-10T20:21:49+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-icons": "^1.4", "ext-intl": "*", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "spatie/color": "^1.5", "spatie/invade": "^1.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v3.0.0-alpha90", "version_normalized": "*******-alpha90", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b86c3adc262ccad36e3a279bfac057074c352fac"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b86c3adc262ccad36e3a279bfac057074c352fac", "type": "zip", "shasum": "", "reference": "b86c3adc262ccad36e3a279bfac057074c352fac"}, "time": "2023-05-10T11:39:41+00:00"}, {"version": "v3.0.0-alpha89", "version_normalized": "*******-alpha89", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cb0ca9d5280f3268eac9064177451ff402b5c4a8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cb0ca9d5280f3268eac9064177451ff402b5c4a8", "type": "zip", "shasum": "", "reference": "cb0ca9d5280f3268eac9064177451ff402b5c4a8"}, "time": "2023-05-09T20:03:14+00:00"}, {"version": "v3.0.0-alpha88", "version_normalized": "*******-alpha88", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c7efbe1ac5e00cd17838894bd7df0f30d80e22b9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c7efbe1ac5e00cd17838894bd7df0f30d80e22b9", "type": "zip", "shasum": "", "reference": "c7efbe1ac5e00cd17838894bd7df0f30d80e22b9"}, "time": "2023-05-05T19:20:19+00:00"}, {"version": "v3.0.0-alpha87", "version_normalized": "*******-alpha87"}, {"version": "v3.0.0-alpha86", "version_normalized": "*******-alpha86"}, {"version": "v3.0.0-alpha85", "version_normalized": "*******-alpha85"}, {"version": "v3.0.0-alpha84", "version_normalized": "*******-alpha84", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c554eae4688cdacf3e501fd258c8238a9584145e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c554eae4688cdacf3e501fd258c8238a9584145e", "type": "zip", "shasum": "", "reference": "c554eae4688cdacf3e501fd258c8238a9584145e"}, "time": "2023-05-05T14:19:21+00:00"}, {"version": "v3.0.0-alpha83", "version_normalized": "*******-alpha83", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a46e912bc8384dff5c69296c4f5c109f38c74da8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a46e912bc8384dff5c69296c4f5c109f38c74da8", "type": "zip", "shasum": "", "reference": "a46e912bc8384dff5c69296c4f5c109f38c74da8"}, "time": "2023-05-04T23:11:07+00:00"}, {"version": "v3.0.0-alpha82", "version_normalized": "*******-alpha82"}, {"version": "v3.0.0-alpha81", "version_normalized": "*******-alpha81", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "deb4303f554ea97a34919bad5958a4bbb266ca89"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/deb4303f554ea97a34919bad5958a4bbb266ca89", "type": "zip", "shasum": "", "reference": "deb4303f554ea97a34919bad5958a4bbb266ca89"}, "time": "2023-05-03T13:30:43+00:00"}, {"version": "v3.0.0-alpha80", "version_normalized": "*******-alpha80", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7e7dbe7d69f996503ee6173fd7b9120b8219dce5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7e7dbe7d69f996503ee6173fd7b9120b8219dce5", "type": "zip", "shasum": "", "reference": "7e7dbe7d69f996503ee6173fd7b9120b8219dce5"}, "time": "2023-04-30T18:15:43+00:00"}, {"version": "v3.0.0-alpha79", "version_normalized": "*******-alpha79"}, {"version": "v3.0.0-alpha78", "version_normalized": "*******-alpha78"}, {"version": "v3.0.0-alpha77", "version_normalized": "*******-alpha77", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7a881f5bf9963b7eb2db9a78d15aec6e772ba43b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7a881f5bf9963b7eb2db9a78d15aec6e772ba43b", "type": "zip", "shasum": "", "reference": "7a881f5bf9963b7eb2db9a78d15aec6e772ba43b"}, "time": "2023-04-24T15:45:25+00:00"}, {"version": "v3.0.0-alpha76", "version_normalized": "*******-alpha76", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c6d1827425fa235cbd05ea8791b76b7f40fab954"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c6d1827425fa235cbd05ea8791b76b7f40fab954", "type": "zip", "shasum": "", "reference": "c6d1827425fa235cbd05ea8791b76b7f40fab954"}, "time": "2023-04-24T08:35:22+00:00"}, {"version": "v3.0.0-alpha75", "version_normalized": "*******-alpha75", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3c9e862fbfb1da9353f854de8ba9cacb1ff10fac"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3c9e862fbfb1da9353f854de8ba9cacb1ff10fac", "type": "zip", "shasum": "", "reference": "3c9e862fbfb1da9353f854de8ba9cacb1ff10fac"}, "time": "2023-04-16T09:06:27+00:00"}, {"version": "v3.0.0-alpha74", "version_normalized": "*******-alpha74", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d88bd8eab0e573004aa05fb51e7deaf3277d25f7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d88bd8eab0e573004aa05fb51e7deaf3277d25f7", "type": "zip", "shasum": "", "reference": "d88bd8eab0e573004aa05fb51e7deaf3277d25f7"}, "time": "2023-04-11T10:06:20+00:00"}, {"version": "v3.0.0-alpha73", "version_normalized": "*******-alpha73"}, {"version": "v3.0.0-alpha72", "version_normalized": "*******-alpha72"}, {"version": "v3.0.0-alpha71", "version_normalized": "*******-alpha71", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5cfacaf8e714a34f108c000abf706235dcb9b52f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5cfacaf8e714a34f108c000abf706235dcb9b52f", "type": "zip", "shasum": "", "reference": "5cfacaf8e714a34f108c000abf706235dcb9b52f"}, "time": "2023-04-07T11:17:00+00:00"}, {"version": "v3.0.0-alpha70", "version_normalized": "*******-alpha70", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4ea12903f73dadad38d7a26ee545c5375ef18574"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4ea12903f73dadad38d7a26ee545c5375ef18574", "type": "zip", "shasum": "", "reference": "4ea12903f73dadad38d7a26ee545c5375ef18574"}, "time": "2023-03-30T21:43:03+00:00"}, {"version": "v3.0.0-alpha69", "version_normalized": "*******-alpha69", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "47a6c1a417ea8e1168e2882d1d3000b0105d4a6b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/47a6c1a417ea8e1168e2882d1d3000b0105d4a6b", "type": "zip", "shasum": "", "reference": "47a6c1a417ea8e1168e2882d1d3000b0105d4a6b"}, "time": "2023-03-25T17:36:26+00:00"}, {"version": "v3.0.0-alpha68", "version_normalized": "*******-alpha68"}, {"version": "v3.0.0-alpha67", "version_normalized": "*******-alpha67", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1bad452fe6c75c81128a4ee3608a6adb2e311928"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1bad452fe6c75c81128a4ee3608a6adb2e311928", "type": "zip", "shasum": "", "reference": "1bad452fe6c75c81128a4ee3608a6adb2e311928"}, "time": "2023-03-15T10:57:51+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-icons": "^1.4", "illuminate/contracts": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "spatie/color": "^1.5", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v3.0.0-alpha66", "version_normalized": "*******-alpha66"}, {"version": "v3.0.0-alpha65", "version_normalized": "*******-alpha65"}, {"version": "v3.0.0-alpha64", "version_normalized": "*******-alpha64", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "551bae9cde707a410e3106f00ffeef96f0303748"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/551bae9cde707a410e3106f00ffeef96f0303748", "type": "zip", "shasum": "", "reference": "551bae9cde707a410e3106f00ffeef96f0303748"}, "time": "2023-03-13T14:41:41+00:00"}, {"version": "v3.0.0-alpha63", "version_normalized": "*******-alpha63"}, {"version": "v3.0.0-alpha62", "version_normalized": "*******-alpha62", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "04bbdab9e82d1fcd3269dd886c6019bc43e06ccc"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/04bbdab9e82d1fcd3269dd886c6019bc43e06ccc", "type": "zip", "shasum": "", "reference": "04bbdab9e82d1fcd3269dd886c6019bc43e06ccc"}, "time": "2023-03-12T12:28:19+00:00"}, {"version": "v3.0.0-alpha61", "version_normalized": "*******-alpha61", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "63dec548842635bfca87136ca157f66a50622c40"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/63dec548842635bfca87136ca157f66a50622c40", "type": "zip", "shasum": "", "reference": "63dec548842635bfca87136ca157f66a50622c40"}, "time": "2023-03-04T00:24:10+00:00"}, {"version": "v3.0.0-alpha60", "version_normalized": "*******-alpha60"}, {"version": "v3.0.0-alpha59", "version_normalized": "*******-alpha59"}, {"version": "v3.0.0-alpha58", "version_normalized": "*******-alpha58"}, {"version": "v3.0.0-alpha57", "version_normalized": "*******-alpha57"}, {"version": "v3.0.0-alpha56", "version_normalized": "*******-alpha56"}, {"version": "v3.0.0-alpha55", "version_normalized": "*******-alpha55"}, {"version": "v3.0.0-alpha54", "version_normalized": "*******-alpha54"}, {"version": "v3.0.0-alpha53", "version_normalized": "*******-alpha53"}, {"version": "v3.0.0-alpha52", "version_normalized": "*******-alpha52", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9eec16715bf4a7517b5804021557ea4f5d15d94e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9eec16715bf4a7517b5804021557ea4f5d15d94e", "type": "zip", "shasum": "", "reference": "9eec16715bf4a7517b5804021557ea4f5d15d94e"}, "time": "2023-03-02T08:49:20+00:00"}, {"version": "v3.0.0-alpha51", "version_normalized": "*******-alpha51"}, {"version": "v3.0.0-alpha50", "version_normalized": "*******-alpha50"}, {"version": "v3.0.0-alpha49", "version_normalized": "*******-alpha49"}, {"version": "v3.0.0-alpha48", "version_normalized": "*******-alpha48", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2eb62ae2c7ec31c9cf884fdeaa4d5d2423073c0a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2eb62ae2c7ec31c9cf884fdeaa4d5d2423073c0a", "type": "zip", "shasum": "", "reference": "2eb62ae2c7ec31c9cf884fdeaa4d5d2423073c0a"}, "time": "2023-03-01T22:55:13+00:00"}, {"version": "v3.0.0-alpha47", "version_normalized": "*******-alpha47", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f178400c683ca569f92f8436a13b1b0d665cb5a0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f178400c683ca569f92f8436a13b1b0d665cb5a0", "type": "zip", "shasum": "", "reference": "f178400c683ca569f92f8436a13b1b0d665cb5a0"}, "time": "2023-02-27T13:06:35+00:00"}, {"version": "v3.0.0-alpha46", "version_normalized": "*******-alpha46", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d27623a256fa9879287d0f60240d2d344760eaae"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d27623a256fa9879287d0f60240d2d344760eaae", "type": "zip", "shasum": "", "reference": "d27623a256fa9879287d0f60240d2d344760eaae"}, "time": "2023-02-24T11:29:02+00:00"}, {"version": "v3.0.0-alpha45", "version_normalized": "*******-alpha45", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4380791754a5311da8a2dd2ebdc2bdba3dad1a6d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4380791754a5311da8a2dd2ebdc2bdba3dad1a6d", "type": "zip", "shasum": "", "reference": "4380791754a5311da8a2dd2ebdc2bdba3dad1a6d"}, "time": "2023-02-18T21:13:40+00:00"}, {"version": "v3.0.0-alpha44", "version_normalized": "*******-alpha44"}, {"version": "v3.0.0-alpha43", "version_normalized": "*******-alpha43"}, {"version": "v3.0.0-alpha42", "version_normalized": "*******-alpha42", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bd5c4b93c3d943e33d5434befc222683e4634d1c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bd5c4b93c3d943e33d5434befc222683e4634d1c", "type": "zip", "shasum": "", "reference": "bd5c4b93c3d943e33d5434befc222683e4634d1c"}, "time": "2023-02-18T19:00:45+00:00"}, {"version": "v3.0.0-alpha41", "version_normalized": "*******-alpha41"}, {"version": "v3.0.0-alpha40", "version_normalized": "*******-alpha40"}, {"version": "v3.0.0-alpha39", "version_normalized": "*******-alpha39", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "987515dba9cf106be4438e127ca09f03e6eea59b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/987515dba9cf106be4438e127ca09f03e6eea59b", "type": "zip", "shasum": "", "reference": "987515dba9cf106be4438e127ca09f03e6eea59b"}, "time": "2023-02-16T08:48:12+00:00"}, {"version": "v3.0.0-alpha38", "version_normalized": "*******-alpha38"}, {"version": "v3.0.0-alpha37", "version_normalized": "*******-alpha37"}, {"version": "v3.0.0-alpha36", "version_normalized": "*******-alpha36", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "615458981cf280260ff78d2f5371a3defe286983"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/615458981cf280260ff78d2f5371a3defe286983", "type": "zip", "shasum": "", "reference": "615458981cf280260ff78d2f5371a3defe286983"}, "time": "2023-02-13T15:39:31+00:00", "require": {"php": "^8.0", "blade-ui-kit/blade-icons": "^1.4", "illuminate/contracts": "^9.0", "illuminate/support": "^9.0", "illuminate/view": "^9.0", "spatie/color": "^1.5", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v3.0.0-alpha35", "version_normalized": "*******-alpha35", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "af2f6e7f53e2ce743b13d94fd0aeefe2424bf1aa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/af2f6e7f53e2ce743b13d94fd0aeefe2424bf1aa", "type": "zip", "shasum": "", "reference": "af2f6e7f53e2ce743b13d94fd0aeefe2424bf1aa"}, "time": "2023-02-08T23:25:56+00:00"}, {"version": "v3.0.0-alpha34", "version_normalized": "*******-alpha34", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "54e41022baf36d6f88e3f617ee8cdf4b1d055057"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/54e41022baf36d6f88e3f617ee8cdf4b1d055057", "type": "zip", "shasum": "", "reference": "54e41022baf36d6f88e3f617ee8cdf4b1d055057"}, "time": "2023-02-07T23:55:22+00:00"}, {"version": "v3.0.0-alpha33", "version_normalized": "*******-alpha33", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2b8d8f0abc9be36ae557f2bfaa4b1994e8c0a37a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2b8d8f0abc9be36ae557f2bfaa4b1994e8c0a37a", "type": "zip", "shasum": "", "reference": "2b8d8f0abc9be36ae557f2bfaa4b1994e8c0a37a"}, "time": "2023-02-02T16:49:03+00:00"}, {"version": "v3.0.0-alpha32", "version_normalized": "*******-alpha32"}, {"version": "v3.0.0-alpha31", "version_normalized": "*******-alpha31"}, {"version": "v3.0.0-alpha30", "version_normalized": "*******-alpha30"}, {"version": "v3.0.0-alpha29", "version_normalized": "*******-alpha29", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6781218fdbaae88deae03b919b2ce887809c1e2e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6781218fdbaae88deae03b919b2ce887809c1e2e", "type": "zip", "shasum": "", "reference": "6781218fdbaae88deae03b919b2ce887809c1e2e"}, "time": "2023-01-31T20:23:18+00:00"}, {"version": "v3.0.0-alpha28", "version_normalized": "*******-alpha28"}, {"version": "v3.0.0-alpha27", "version_normalized": "*******-alpha27", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "23e46eeda45724a3673ccf77c117eae6a0f56f36"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/23e46eeda45724a3673ccf77c117eae6a0f56f36", "type": "zip", "shasum": "", "reference": "23e46eeda45724a3673ccf77c117eae6a0f56f36"}, "time": "2023-01-31T14:27:53+00:00"}, {"version": "v3.0.0-alpha26", "version_normalized": "*******-alpha26"}, {"version": "v3.0.0-alpha25", "version_normalized": "*******-alpha25"}, {"version": "v3.0.0-alpha24", "version_normalized": "*******-alpha24", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1d22d66279b068893623209e98b22e9c7f6f553d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1d22d66279b068893623209e98b22e9c7f6f553d", "type": "zip", "shasum": "", "reference": "1d22d66279b068893623209e98b22e9c7f6f553d"}, "time": "2023-01-29T11:30:49+00:00"}, {"version": "v3.0.0-alpha23", "version_normalized": "*******-alpha23"}, {"version": "v3.0.0-alpha10", "version_normalized": "*******-alpha10"}, {"version": "v3.0.0-alpha9", "version_normalized": "*******-alpha9"}, {"version": "v3.0.0-alpha8", "version_normalized": "*******-alpha8", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3bfa0eaaf9fab81fcd65171d22f1f3150fa41ccd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3bfa0eaaf9fab81fcd65171d22f1f3150fa41ccd", "type": "zip", "shasum": "", "reference": "3bfa0eaaf9fab81fcd65171d22f1f3150fa41ccd"}, "time": "2023-01-27T15:12:47+00:00"}, {"version": "v3.0.0-alpha7", "version_normalized": "*******-alpha7"}, {"version": "v3.0.0-alpha6", "version_normalized": "*******-alpha6", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8d5a52fb19f3c9f8842e209814099fe7da86c0f0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8d5a52fb19f3c9f8842e209814099fe7da86c0f0", "type": "zip", "shasum": "", "reference": "8d5a52fb19f3c9f8842e209814099fe7da86c0f0"}, "time": "2023-01-26T13:16:07+00:00"}, {"version": "v3.0.0-alpha5", "version_normalized": "*******-alpha5", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "43556a3cb00e1cf1d80d0f961f7ada94bb72a82e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/43556a3cb00e1cf1d80d0f961f7ada94bb72a82e", "type": "zip", "shasum": "", "reference": "43556a3cb00e1cf1d80d0f961f7ada94bb72a82e"}, "time": "2023-01-23T11:14:41+00:00"}, {"version": "v3.0.0-alpha4", "version_normalized": "*******-alpha4"}, {"version": "v3.0.0-alpha3", "version_normalized": "*******-alpha3"}, {"version": "v3.0.0-alpha2", "version_normalized": "*******-alpha2"}, {"version": "v3.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6fb7cee45c6e9fdbaeebd398c4d12d9901d11c20"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6fb7cee45c6e9fdbaeebd398c4d12d9901d11c20", "type": "zip", "shasum": "", "reference": "6fb7cee45c6e9fdbaeebd398c4d12d9901d11c20"}, "time": "2023-01-22T12:51:32+00:00"}, {"version": "v2.17.58", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9982a88704efc58b710c4e6b5000d85a6f4daf56"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9982a88704efc58b710c4e6b5000d85a6f4daf56", "type": "zip", "shasum": "", "reference": "9982a88704efc58b710c4e6b5000d85a6f4daf56"}, "time": "2023-07-03T09:23:04+00:00", "require": {"php": "^8.0", "illuminate/contracts": "^8.6|^9.0|^10.0", "illuminate/support": "^8.6|^9.0|^10.0", "illuminate/view": "^8.6|^9.0|^10.0", "ryangjchandler/blade-capture-directive": "^0.2|^0.3", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v2.17.57", "version_normalized": "*********"}, {"version": "v2.17.56", "version_normalized": "*********"}, {"version": "v2.17.55", "version_normalized": "*********"}, {"version": "v2.17.54", "version_normalized": "*********"}, {"version": "v2.17.53", "version_normalized": "*********"}, {"version": "v2.17.52", "version_normalized": "*********"}, {"version": "v2.17.51", "version_normalized": "*********"}, {"version": "v2.17.50", "version_normalized": "*********"}, {"version": "v2.17.49", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d38558be11f76010fd51f3d91bc440a4d326a224"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d38558be11f76010fd51f3d91bc440a4d326a224", "type": "zip", "shasum": "", "reference": "d38558be11f76010fd51f3d91bc440a4d326a224"}, "time": "2023-06-11T17:06:02+00:00"}, {"version": "v2.17.48", "version_normalized": "*********"}, {"version": "v2.17.47", "version_normalized": "*********"}, {"version": "v2.17.46", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2fc7b60416283c17a7b8ce2f8d65d4d02ed766fb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2fc7b60416283c17a7b8ce2f8d65d4d02ed766fb", "type": "zip", "shasum": "", "reference": "2fc7b60416283c17a7b8ce2f8d65d4d02ed766fb"}, "time": "2023-06-06T11:21:22+00:00"}, {"version": "v2.17.45", "version_normalized": "*********"}, {"version": "v2.17.44", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fe7434dd07b254de0d9cc7d9178540ad8b087cca"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fe7434dd07b254de0d9cc7d9178540ad8b087cca", "type": "zip", "shasum": "", "reference": "fe7434dd07b254de0d9cc7d9178540ad8b087cca"}, "time": "2023-05-30T10:44:54+00:00", "require": {"php": "^8.0", "illuminate/contracts": "^8.6|^9.0|^10.0", "illuminate/support": "^8.6|^9.0|^10.0", "illuminate/view": "^8.6|^9.0|^10.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v2.17.43", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1e03d08cb6df27fd64e658b5316fd34f81554a8d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1e03d08cb6df27fd64e658b5316fd34f81554a8d", "type": "zip", "shasum": "", "reference": "1e03d08cb6df27fd64e658b5316fd34f81554a8d"}, "time": "2023-05-22T21:18:29+00:00"}, {"version": "v2.17.42", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c8130bcb455e452f15eeb120958e8c26fba5fba1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c8130bcb455e452f15eeb120958e8c26fba5fba1", "type": "zip", "shasum": "", "reference": "c8130bcb455e452f15eeb120958e8c26fba5fba1"}, "time": "2023-05-20T17:45:49+00:00"}, {"version": "v2.17.41", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "79be557c4aa426eddb250421a9270724e32a683c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/79be557c4aa426eddb250421a9270724e32a683c", "type": "zip", "shasum": "", "reference": "79be557c4aa426eddb250421a9270724e32a683c"}, "time": "2023-05-17T16:33:21+00:00"}, {"version": "v2.17.40", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e75e02a76edd959304cdbea7815586b720f8d308"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e75e02a76edd959304cdbea7815586b720f8d308", "type": "zip", "shasum": "", "reference": "e75e02a76edd959304cdbea7815586b720f8d308"}, "time": "2023-05-13T07:21:02+00:00"}, {"version": "v2.17.39", "version_normalized": "*********"}, {"version": "v2.17.38", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1a411197122524886c81a74c7044258d64d56308"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1a411197122524886c81a74c7044258d64d56308", "type": "zip", "shasum": "", "reference": "1a411197122524886c81a74c7044258d64d56308"}, "time": "2023-05-04T23:08:15+00:00"}, {"version": "v2.17.37", "version_normalized": "*********"}, {"version": "v2.17.36", "version_normalized": "*********"}, {"version": "v2.17.35", "version_normalized": "*********"}, {"version": "v2.17.34", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2e02ece5a722ddc95facd3a37f3b3a4fd3d355d1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2e02ece5a722ddc95facd3a37f3b3a4fd3d355d1", "type": "zip", "shasum": "", "reference": "2e02ece5a722ddc95facd3a37f3b3a4fd3d355d1"}, "time": "2023-05-01T16:59:19+00:00"}, {"version": "v2.17.32", "version_normalized": "*********"}, {"version": "v2.17.31", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f35da7381871cbc3a5770fdc37ff2e8e18bd711f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f35da7381871cbc3a5770fdc37ff2e8e18bd711f", "type": "zip", "shasum": "", "reference": "f35da7381871cbc3a5770fdc37ff2e8e18bd711f"}, "time": "2023-04-30T16:44:24+00:00"}, {"version": "v2.17.30", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d99eb08d74c560d4fa6ffb79a805cc161834de7c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d99eb08d74c560d4fa6ffb79a805cc161834de7c", "type": "zip", "shasum": "", "reference": "d99eb08d74c560d4fa6ffb79a805cc161834de7c"}, "time": "2023-04-17T13:08:52+00:00"}, {"version": "v2.17.29", "version_normalized": "*********"}, {"version": "v2.17.28", "version_normalized": "*********"}, {"version": "v2.17.27", "version_normalized": "*********"}, {"version": "v2.17.26", "version_normalized": "*********"}, {"version": "v2.17.25", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "66ef8a34829359798d2756f9fbe85280544638e6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/66ef8a34829359798d2756f9fbe85280544638e6", "type": "zip", "shasum": "", "reference": "66ef8a34829359798d2756f9fbe85280544638e6"}, "time": "2023-04-15T21:22:52+00:00"}, {"version": "v2.17.24", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b5b76af7c061fae3069431fb19df38486eb6ba69"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b5b76af7c061fae3069431fb19df38486eb6ba69", "type": "zip", "shasum": "", "reference": "b5b76af7c061fae3069431fb19df38486eb6ba69"}, "time": "2023-04-11T10:42:13+00:00"}, {"version": "v2.17.23", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "af701826d912e86ef5e7ff4497c2a8469f73f52c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/af701826d912e86ef5e7ff4497c2a8469f73f52c", "type": "zip", "shasum": "", "reference": "af701826d912e86ef5e7ff4497c2a8469f73f52c"}, "time": "2023-04-08T13:07:19+00:00"}, {"version": "v2.17.22", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f34a91e0244d323fa45552602c7bb782659a4290"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f34a91e0244d323fa45552602c7bb782659a4290", "type": "zip", "shasum": "", "reference": "f34a91e0244d323fa45552602c7bb782659a4290"}, "time": "2023-04-07T11:25:48+00:00"}, {"version": "v2.17.21", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "633152ca96f0e0a8adf718395783d3d9c62fd070"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/633152ca96f0e0a8adf718395783d3d9c62fd070", "type": "zip", "shasum": "", "reference": "633152ca96f0e0a8adf718395783d3d9c62fd070"}, "time": "2023-03-25T17:23:06+00:00"}, {"version": "v2.17.20", "version_normalized": "*********"}, {"version": "v2.17.19", "version_normalized": "*********"}, {"version": "v2.17.18", "version_normalized": "*********"}, {"version": "v2.17.17", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4bafcc21b9bbe1e73054c315e425999510f49e3d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4bafcc21b9bbe1e73054c315e425999510f49e3d", "type": "zip", "shasum": "", "reference": "4bafcc21b9bbe1e73054c315e425999510f49e3d"}, "time": "2023-03-12T12:20:26+00:00"}, {"version": "v2.17.16", "version_normalized": "*********"}, {"version": "v2.17.15", "version_normalized": "*********"}, {"version": "v2.17.14", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "98d0a12b5ce21842a2ca31cfa2a9d1289ab01955"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/98d0a12b5ce21842a2ca31cfa2a9d1289ab01955", "type": "zip", "shasum": "", "reference": "98d0a12b5ce21842a2ca31cfa2a9d1289ab01955"}, "time": "2023-03-03T09:59:42+00:00"}, {"version": "v2.17.13", "version_normalized": "*********"}, {"version": "v2.17.12", "version_normalized": "*********"}, {"version": "v2.17.11", "version_normalized": "*********"}, {"version": "v2.17.10", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8bc0a3eb471262bb3037a557ee33c56599b41f84"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8bc0a3eb471262bb3037a557ee33c56599b41f84", "type": "zip", "shasum": "", "reference": "8bc0a3eb471262bb3037a557ee33c56599b41f84"}, "time": "2023-03-02T08:47:29+00:00"}, {"version": "v2.17.9", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0a7773de1dbd03bb5cc951e87dc6bea29018e3dd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0a7773de1dbd03bb5cc951e87dc6bea29018e3dd", "type": "zip", "shasum": "", "reference": "0a7773de1dbd03bb5cc951e87dc6bea29018e3dd"}, "time": "2023-03-01T22:52:00+00:00"}, {"version": "v2.17.8", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e5e007796aac8c79b8c62ba28cffd2786f90f5dd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e5e007796aac8c79b8c62ba28cffd2786f90f5dd", "type": "zip", "shasum": "", "reference": "e5e007796aac8c79b8c62ba28cffd2786f90f5dd"}, "time": "2023-02-27T22:58:48+00:00"}, {"version": "v2.17.7", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "af4be75aa40eed0c3aabdd8738900aa0c65946f3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/af4be75aa40eed0c3aabdd8738900aa0c65946f3", "type": "zip", "shasum": "", "reference": "af4be75aa40eed0c3aabdd8738900aa0c65946f3"}, "time": "2023-02-25T11:21:20+00:00"}, {"version": "v2.17.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f214bec84e41f5ad3e2c238e93dfe88d3c735773"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f214bec84e41f5ad3e2c238e93dfe88d3c735773", "type": "zip", "shasum": "", "reference": "f214bec84e41f5ad3e2c238e93dfe88d3c735773"}, "time": "2023-02-23T21:55:29+00:00"}, {"version": "v2.17.5", "version_normalized": "********"}, {"version": "v2.17.4", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a4498f099f9d00ef8e1438abf68f01f1109a42b4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a4498f099f9d00ef8e1438abf68f01f1109a42b4", "type": "zip", "shasum": "", "reference": "a4498f099f9d00ef8e1438abf68f01f1109a42b4"}, "time": "2023-02-20T22:54:49+00:00"}, {"version": "v2.17.3", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bab474878e8048ce6bbe95ef17ed6fc081bbd636"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bab474878e8048ce6bbe95ef17ed6fc081bbd636", "type": "zip", "shasum": "", "reference": "bab474878e8048ce6bbe95ef17ed6fc081bbd636"}, "time": "2023-02-16T08:24:19+00:00"}, {"version": "v2.17.2", "version_normalized": "********"}, {"version": "v2.17.1", "version_normalized": "********"}, {"version": "v2.17.0", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "25700a9bb9cff5cc78d169bed2a2ef11379fbef2"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/25700a9bb9cff5cc78d169bed2a2ef11379fbef2", "type": "zip", "shasum": "", "reference": "25700a9bb9cff5cc78d169bed2a2ef11379fbef2"}, "time": "2023-02-14T21:00:00+00:00"}, {"version": "v2.16.70", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ec71eac0ff6e67610d80fc2b9bc36720cb9ecd77"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ec71eac0ff6e67610d80fc2b9bc36720cb9ecd77", "type": "zip", "shasum": "", "reference": "ec71eac0ff6e67610d80fc2b9bc36720cb9ecd77"}, "time": "2023-02-09T00:05:48+00:00"}, {"version": "v2.16.69", "version_normalized": "*********"}, {"version": "v2.16.68", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "584a0c2de4afdae410b28e46524a68d7ab874c1c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/584a0c2de4afdae410b28e46524a68d7ab874c1c", "type": "zip", "shasum": "", "reference": "584a0c2de4afdae410b28e46524a68d7ab874c1c"}, "time": "2023-02-04T12:26:20+00:00"}, {"version": "v2.16.67", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b980056ed3783cff234e79fcc56f9309d66eeb1b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b980056ed3783cff234e79fcc56f9309d66eeb1b", "type": "zip", "shasum": "", "reference": "b980056ed3783cff234e79fcc56f9309d66eeb1b"}, "time": "2023-02-01T14:44:16+00:00"}, {"version": "v2.16.66", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f3cd2612b3a39b43502862e4be774825d243402b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f3cd2612b3a39b43502862e4be774825d243402b", "type": "zip", "shasum": "", "reference": "f3cd2612b3a39b43502862e4be774825d243402b"}, "time": "2023-01-25T18:16:24+00:00"}, {"version": "v2.16.65", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "07a3d1049ebd9d38208b3382989c7bcac4ddfd36"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/07a3d1049ebd9d38208b3382989c7bcac4ddfd36", "type": "zip", "shasum": "", "reference": "07a3d1049ebd9d38208b3382989c7bcac4ddfd36"}, "time": "2023-01-23T09:34:09+00:00", "require": {"php": "^8.0", "illuminate/contracts": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "illuminate/view": "^8.6|^9.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v2.16.64", "version_normalized": "*********"}, {"version": "v2.16.63", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "25ece77e743fdd31603f18f582ae77384103e082"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/25ece77e743fdd31603f18f582ae77384103e082", "type": "zip", "shasum": "", "reference": "25ece77e743fdd31603f18f582ae77384103e082"}, "time": "2022-12-15T13:28:07+00:00"}, {"version": "v2.16.62", "version_normalized": "*********"}, {"version": "v2.16.61", "version_normalized": "*********"}, {"version": "v2.16.60", "version_normalized": "*********"}, {"version": "v2.16.59", "version_normalized": "*********"}, {"version": "v2.16.58", "version_normalized": "*********"}, {"version": "v2.16.57", "version_normalized": "*********"}, {"version": "v2.16.56", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f38dc2a24a6745e544e2540b2fb2609ad010eb15"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f38dc2a24a6745e544e2540b2fb2609ad010eb15", "type": "zip", "shasum": "", "reference": "f38dc2a24a6745e544e2540b2fb2609ad010eb15"}, "time": "2022-12-06T11:38:11+00:00"}, {"version": "v2.16.55", "version_normalized": "*********"}, {"version": "v2.16.54", "version_normalized": "*********"}, {"version": "v2.16.53", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bce9636e7451039e99e5246ce37c59b35d24482b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bce9636e7451039e99e5246ce37c59b35d24482b", "type": "zip", "shasum": "", "reference": "bce9636e7451039e99e5246ce37c59b35d24482b"}, "time": "2022-12-02T11:11:16+00:00"}, {"version": "v2.16.52", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f21b589a19f4ae3c245175eccb793524891550a3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f21b589a19f4ae3c245175eccb793524891550a3", "type": "zip", "shasum": "", "reference": "f21b589a19f4ae3c245175eccb793524891550a3"}, "time": "2022-11-22T10:28:55+00:00"}, {"version": "v2.16.51", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c1e6807aac01c51ad59867026da23a798f3d911d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c1e6807aac01c51ad59867026da23a798f3d911d", "type": "zip", "shasum": "", "reference": "c1e6807aac01c51ad59867026da23a798f3d911d"}, "time": "2022-11-17T10:14:17+00:00"}, {"version": "v2.16.50", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2b52e0fd9f7e346c2eeb9a07287491818190dd99"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2b52e0fd9f7e346c2eeb9a07287491818190dd99", "type": "zip", "shasum": "", "reference": "2b52e0fd9f7e346c2eeb9a07287491818190dd99"}, "time": "2022-11-15T19:30:00+00:00"}, {"version": "v2.16.49", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "84aa11dfb02708b25a82b70b6f4dcd5427cb95db"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/84aa11dfb02708b25a82b70b6f4dcd5427cb95db", "type": "zip", "shasum": "", "reference": "84aa11dfb02708b25a82b70b6f4dcd5427cb95db"}, "time": "2022-11-14T11:48:26+00:00"}, {"version": "v2.16.48", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a0246447359244a23fb72c5fc46f79df35a54b64"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a0246447359244a23fb72c5fc46f79df35a54b64", "type": "zip", "shasum": "", "reference": "a0246447359244a23fb72c5fc46f79df35a54b64"}, "time": "2022-11-14T11:27:48+00:00"}, {"version": "v2.16.47", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "630974003b32dadd076657bc624802396952efa9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/630974003b32dadd076657bc624802396952efa9", "type": "zip", "shasum": "", "reference": "630974003b32dadd076657bc624802396952efa9"}, "time": "2022-11-12T21:01:59+00:00"}, {"version": "v2.16.46", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "da25e4d0b630855388400c5302e4fc9a58865a7a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/da25e4d0b630855388400c5302e4fc9a58865a7a", "type": "zip", "shasum": "", "reference": "da25e4d0b630855388400c5302e4fc9a58865a7a"}, "time": "2022-11-09T21:59:01+00:00"}, {"version": "v2.16.45", "version_normalized": "*********"}, {"version": "v2.16.44", "version_normalized": "*********"}, {"version": "v2.16.43", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9cfc861feb961f79e57b70cf8703fb0d7b33f108"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9cfc861feb961f79e57b70cf8703fb0d7b33f108", "type": "zip", "shasum": "", "reference": "9cfc861feb961f79e57b70cf8703fb0d7b33f108"}, "time": "2022-11-09T16:26:03+00:00"}, {"version": "v2.16.42", "version_normalized": "*********"}, {"version": "v2.16.41", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "548ab60c617568ecea13ff8ea3cce21a5961afb1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/548ab60c617568ecea13ff8ea3cce21a5961afb1", "type": "zip", "shasum": "", "reference": "548ab60c617568ecea13ff8ea3cce21a5961afb1"}, "time": "2022-11-03T14:40:22+00:00"}, {"version": "v2.16.40", "version_normalized": "*********"}, {"version": "v2.16.39", "version_normalized": "*********"}, {"version": "v2.16.38", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f476ff04e8a44f33411a0f4afabf229989c7ebaa"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f476ff04e8a44f33411a0f4afabf229989c7ebaa", "type": "zip", "shasum": "", "reference": "f476ff04e8a44f33411a0f4afabf229989c7ebaa"}, "time": "2022-11-02T23:07:55+00:00"}, {"version": "v2.16.37", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "aa9da96484a9610a84b346b3984a89468e6ee31e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/aa9da96484a9610a84b346b3984a89468e6ee31e", "type": "zip", "shasum": "", "reference": "aa9da96484a9610a84b346b3984a89468e6ee31e"}, "time": "2022-10-30T20:32:26+00:00"}, {"version": "v2.16.36", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6e26602696ea96a33666667d3fac423176d65e81"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6e26602696ea96a33666667d3fac423176d65e81", "type": "zip", "shasum": "", "reference": "6e26602696ea96a33666667d3fac423176d65e81"}, "time": "2022-10-22T11:51:54+00:00"}, {"version": "v2.16.35", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2118d9bd6b49fca425a7939773883f989df8e2d8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2118d9bd6b49fca425a7939773883f989df8e2d8", "type": "zip", "shasum": "", "reference": "2118d9bd6b49fca425a7939773883f989df8e2d8"}, "time": "2022-10-19T12:44:35+00:00"}, {"version": "v2.16.34", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "51cc3bc3eeed28fc55990ec1b0f2d3e8ac71cdd3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/51cc3bc3eeed28fc55990ec1b0f2d3e8ac71cdd3", "type": "zip", "shasum": "", "reference": "51cc3bc3eeed28fc55990ec1b0f2d3e8ac71cdd3"}, "time": "2022-10-17T08:43:24+00:00"}, {"version": "v2.16.33", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fe40645a7860b6c168a6b3ad8ec9c540dd04eecd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fe40645a7860b6c168a6b3ad8ec9c540dd04eecd", "type": "zip", "shasum": "", "reference": "fe40645a7860b6c168a6b3ad8ec9c540dd04eecd"}, "time": "2022-10-13T14:39:38+00:00"}, {"version": "v2.16.32", "version_normalized": "*********"}, {"version": "v2.16.31", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9c80c3dd061c2cc0cdc0739152411202c870e379"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9c80c3dd061c2cc0cdc0739152411202c870e379", "type": "zip", "shasum": "", "reference": "9c80c3dd061c2cc0cdc0739152411202c870e379"}, "time": "2022-10-11T15:17:36+00:00"}, {"version": "v2.16.29", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "98561eb207eaafde2f451ff0aabbff08b8b06207"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/98561eb207eaafde2f451ff0aabbff08b8b06207", "type": "zip", "shasum": "", "reference": "98561eb207eaafde2f451ff0aabbff08b8b06207"}, "time": "2022-10-10T08:49:40+00:00"}, {"version": "v2.16.28", "version_normalized": "*********"}, {"version": "v2.16.27", "version_normalized": "*********"}, {"version": "v2.16.26", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1b0c0040965941d7554c47cc29666f622eae545c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1b0c0040965941d7554c47cc29666f622eae545c", "type": "zip", "shasum": "", "reference": "1b0c0040965941d7554c47cc29666f622eae545c"}, "time": "2022-10-09T17:18:29+00:00"}, {"version": "v2.16.25", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "df9bf5d3eb78840b5e9ff7fc03c1ca16c408fd88"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/df9bf5d3eb78840b5e9ff7fc03c1ca16c408fd88", "type": "zip", "shasum": "", "reference": "df9bf5d3eb78840b5e9ff7fc03c1ca16c408fd88"}, "time": "2022-10-08T21:36:10+00:00"}, {"version": "v2.16.24", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c827b7b15e5732b21f88cb650818b59edd3dc3cd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c827b7b15e5732b21f88cb650818b59edd3dc3cd", "type": "zip", "shasum": "", "reference": "c827b7b15e5732b21f88cb650818b59edd3dc3cd"}, "time": "2022-10-07T19:05:33+00:00"}, {"version": "v2.16.23", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c2a7c4ab946796248d204acfe012835a53132298"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c2a7c4ab946796248d204acfe012835a53132298", "type": "zip", "shasum": "", "reference": "c2a7c4ab946796248d204acfe012835a53132298"}, "time": "2022-10-07T13:19:55+00:00"}, {"version": "v2.16.22", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "dfce82138d9e7895a31931f4017ef1257cb2eb38"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/dfce82138d9e7895a31931f4017ef1257cb2eb38", "type": "zip", "shasum": "", "reference": "dfce82138d9e7895a31931f4017ef1257cb2eb38"}, "time": "2022-10-06T12:44:43+00:00"}, {"version": "v2.16.21", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a6f4b2a0d63c9d01f05882d660abfdfc3df0703a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a6f4b2a0d63c9d01f05882d660abfdfc3df0703a", "type": "zip", "shasum": "", "reference": "a6f4b2a0d63c9d01f05882d660abfdfc3df0703a"}, "time": "2022-10-04T16:16:45+00:00"}, {"version": "v2.16.20", "version_normalized": "*********"}, {"version": "v2.16.19", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "26447bd562b7676131a7c7c3776e64ce626474a3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/26447bd562b7676131a7c7c3776e64ce626474a3", "type": "zip", "shasum": "", "reference": "26447bd562b7676131a7c7c3776e64ce626474a3"}, "time": "2022-09-30T09:40:25+00:00"}, {"version": "v2.16.18", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "413828a2a8f529ffc800b80e255548f6a4c9a3ce"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/413828a2a8f529ffc800b80e255548f6a4c9a3ce", "type": "zip", "shasum": "", "reference": "413828a2a8f529ffc800b80e255548f6a4c9a3ce"}, "time": "2022-09-29T08:10:54+00:00"}, {"version": "v2.16.17", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d9d2036e8e41c97960e0dca6cd4a499a95d5da15"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d9d2036e8e41c97960e0dca6cd4a499a95d5da15", "type": "zip", "shasum": "", "reference": "d9d2036e8e41c97960e0dca6cd4a499a95d5da15"}, "time": "2022-09-25T11:51:25+00:00"}, {"version": "v2.16.16", "version_normalized": "*********"}, {"version": "v2.16.15", "version_normalized": "*********"}, {"version": "v2.16.14", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c0cc59bd50d926b9f60cf1ea0762982ffd71cb06"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c0cc59bd50d926b9f60cf1ea0762982ffd71cb06", "type": "zip", "shasum": "", "reference": "c0cc59bd50d926b9f60cf1ea0762982ffd71cb06"}, "time": "2022-09-22T19:57:32+00:00"}, {"version": "v2.16.13", "version_normalized": "*********"}, {"version": "v2.16.12", "version_normalized": "*********"}, {"version": "v2.16.11", "version_normalized": "*********"}, {"version": "v2.16.10", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d352abed5770c45c1e8364d17b3516ad4d544b57"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d352abed5770c45c1e8364d17b3516ad4d544b57", "type": "zip", "shasum": "", "reference": "d352abed5770c45c1e8364d17b3516ad4d544b57"}, "time": "2022-09-21T16:32:29+00:00"}, {"version": "v2.16.9", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "292be9fa0538395116bfb4dcfc26ce11a6f23abd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/292be9fa0538395116bfb4dcfc26ce11a6f23abd", "type": "zip", "shasum": "", "reference": "292be9fa0538395116bfb4dcfc26ce11a6f23abd"}, "time": "2022-09-21T08:21:29+00:00"}, {"version": "v2.16.8", "version_normalized": "********"}, {"version": "v2.16.7", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0f12902f58799fb0715853ce029d6c2ce18dc996"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0f12902f58799fb0715853ce029d6c2ce18dc996", "type": "zip", "shasum": "", "reference": "0f12902f58799fb0715853ce029d6c2ce18dc996"}, "time": "2022-09-19T09:55:23+00:00"}, {"version": "v2.16.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "27877ed82f411379dbd0ee4facbffa53bfb3fe51"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/27877ed82f411379dbd0ee4facbffa53bfb3fe51", "type": "zip", "shasum": "", "reference": "27877ed82f411379dbd0ee4facbffa53bfb3fe51"}, "time": "2022-09-17T17:15:37+00:00"}, {"version": "v2.16.5", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d4cb0588141371ed8a49aa472ca5c8ea3b732e96"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d4cb0588141371ed8a49aa472ca5c8ea3b732e96", "type": "zip", "shasum": "", "reference": "d4cb0588141371ed8a49aa472ca5c8ea3b732e96"}, "time": "2022-09-16T10:23:06+00:00"}, {"version": "v2.16.4", "version_normalized": "********"}, {"version": "v2.16.3", "version_normalized": "********"}, {"version": "v2.16.2", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "65d67c72ab370ba193d203f09975a033f627de09"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/65d67c72ab370ba193d203f09975a033f627de09", "type": "zip", "shasum": "", "reference": "65d67c72ab370ba193d203f09975a033f627de09"}, "time": "2022-09-12T15:57:43+00:00"}, {"version": "v2.16.1", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "274f6d3873bf57d20d6183e72cbab35e0d24cab6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/274f6d3873bf57d20d6183e72cbab35e0d24cab6", "type": "zip", "shasum": "", "reference": "274f6d3873bf57d20d6183e72cbab35e0d24cab6"}, "time": "2022-09-10T12:35:22+00:00"}, {"version": "v2.16.0", "version_normalized": "********"}, {"version": "v2.15.49", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5cda848cd7bfbad8b45128f5d1725e46be0fc09c"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5cda848cd7bfbad8b45128f5d1725e46be0fc09c", "type": "zip", "shasum": "", "reference": "5cda848cd7bfbad8b45128f5d1725e46be0fc09c"}, "time": "2022-09-09T04:40:40+00:00"}, {"version": "v2.15.48", "version_normalized": "*********"}, {"version": "v2.15.47", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8af27fd26eb581a4945f847d1561ac3df4991f16"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8af27fd26eb581a4945f847d1561ac3df4991f16", "type": "zip", "shasum": "", "reference": "8af27fd26eb581a4945f847d1561ac3df4991f16"}, "time": "2022-09-08T19:34:09+00:00"}, {"version": "v2.15.46", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "17f88ca042af396427482760dd9626c33a291a91"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/17f88ca042af396427482760dd9626c33a291a91", "type": "zip", "shasum": "", "reference": "17f88ca042af396427482760dd9626c33a291a91"}, "time": "2022-09-08T12:26:01+00:00"}, {"version": "v2.15.45", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "adda5ecdb4007743c219b9318e8becab4fd3a562"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/adda5ecdb4007743c219b9318e8becab4fd3a562", "type": "zip", "shasum": "", "reference": "adda5ecdb4007743c219b9318e8becab4fd3a562"}, "time": "2022-09-02T16:16:29+00:00"}, {"version": "v2.15.44", "version_normalized": "*********"}, {"version": "v2.15.43", "version_normalized": "*********"}, {"version": "v2.15.42", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6268091266fb79b1f380f87af4cff87251bef006"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6268091266fb79b1f380f87af4cff87251bef006", "type": "zip", "shasum": "", "reference": "6268091266fb79b1f380f87af4cff87251bef006"}, "time": "2022-09-02T14:22:15+00:00"}, {"version": "v2.15.41", "version_normalized": "*********"}, {"version": "v2.15.40", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b6ea4ceea8e8fb965227f8f7a9e5ebb3ee879537"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b6ea4ceea8e8fb965227f8f7a9e5ebb3ee879537", "type": "zip", "shasum": "", "reference": "b6ea4ceea8e8fb965227f8f7a9e5ebb3ee879537"}, "time": "2022-09-01T22:09:40+00:00"}, {"version": "v2.15.39", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "739380daad2ac1f7f8a71ba8e17050a4b2c1ed4d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/739380daad2ac1f7f8a71ba8e17050a4b2c1ed4d", "type": "zip", "shasum": "", "reference": "739380daad2ac1f7f8a71ba8e17050a4b2c1ed4d"}, "time": "2022-08-31T13:38:45+00:00"}, {"version": "v2.15.38", "version_normalized": "*********"}, {"version": "v2.15.37", "version_normalized": "*********"}, {"version": "v2.15.36", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "52e492d32ce724770980df9bcdbd8240e24abd51"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/52e492d32ce724770980df9bcdbd8240e24abd51", "type": "zip", "shasum": "", "reference": "52e492d32ce724770980df9bcdbd8240e24abd51"}, "time": "2022-08-31T10:18:19+00:00"}, {"version": "v2.15.35", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8dd8378c5170026825683f87ee476f549896efba"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8dd8378c5170026825683f87ee476f549896efba", "type": "zip", "shasum": "", "reference": "8dd8378c5170026825683f87ee476f549896efba"}, "time": "2022-08-31T06:31:35+00:00"}, {"version": "v2.15.34", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8ecfae4cd42a9d0ecefacf0b3f258d0e2aa073a5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8ecfae4cd42a9d0ecefacf0b3f258d0e2aa073a5", "type": "zip", "shasum": "", "reference": "8ecfae4cd42a9d0ecefacf0b3f258d0e2aa073a5"}, "time": "2022-08-30T22:08:37+00:00"}, {"version": "v2.15.33", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "10d1e4c16230939b6343c5281718a1d93fe8ef6a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/10d1e4c16230939b6343c5281718a1d93fe8ef6a", "type": "zip", "shasum": "", "reference": "10d1e4c16230939b6343c5281718a1d93fe8ef6a"}, "time": "2022-08-30T16:34:57+00:00"}, {"version": "v2.15.32", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b189fbe7d2e203519ca476b03d534f1828b2e750"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b189fbe7d2e203519ca476b03d534f1828b2e750", "type": "zip", "shasum": "", "reference": "b189fbe7d2e203519ca476b03d534f1828b2e750"}, "time": "2022-08-30T07:59:28+00:00"}, {"version": "v2.15.31", "version_normalized": "*********"}, {"version": "v2.15.30", "version_normalized": "*********"}, {"version": "v2.15.29", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "454e193e42ea86b358a7de8d9b9700cc00cff365"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/454e193e42ea86b358a7de8d9b9700cc00cff365", "type": "zip", "shasum": "", "reference": "454e193e42ea86b358a7de8d9b9700cc00cff365"}, "time": "2022-08-28T20:22:20+00:00"}, {"version": "v2.15.28", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3ae87a6ec2d4cac9d140a8d33065a868532e1baf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3ae87a6ec2d4cac9d140a8d33065a868532e1baf", "type": "zip", "shasum": "", "reference": "3ae87a6ec2d4cac9d140a8d33065a868532e1baf"}, "time": "2022-08-25T16:40:58+00:00"}, {"version": "v2.15.27", "version_normalized": "*********"}, {"version": "v2.15.26", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e58fcffd982914fd203c0123bcc2439a7295e3c0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e58fcffd982914fd203c0123bcc2439a7295e3c0", "type": "zip", "shasum": "", "reference": "e58fcffd982914fd203c0123bcc2439a7295e3c0"}, "time": "2022-08-24T12:43:47+00:00"}, {"version": "v2.15.25", "version_normalized": "*********"}, {"version": "v2.15.24", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9fd14eb61e4e320e0229e5ffcbed2a5c1eebab7e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9fd14eb61e4e320e0229e5ffcbed2a5c1eebab7e", "type": "zip", "shasum": "", "reference": "9fd14eb61e4e320e0229e5ffcbed2a5c1eebab7e"}, "time": "2022-08-22T22:07:10+00:00"}, {"version": "v2.15.23", "version_normalized": "*********"}, {"version": "v2.15.22", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "1fe1ae2d2d4fa15d59c4065dc7ece4136074e622"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/1fe1ae2d2d4fa15d59c4065dc7ece4136074e622", "type": "zip", "shasum": "", "reference": "1fe1ae2d2d4fa15d59c4065dc7ece4136074e622"}, "time": "2022-08-20T20:09:11+00:00"}, {"version": "v2.15.21", "version_normalized": "*********"}, {"version": "v2.15.20", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "052587bd0c797cf41af3a7c00da303a7ae0748d1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/052587bd0c797cf41af3a7c00da303a7ae0748d1", "type": "zip", "shasum": "", "reference": "052587bd0c797cf41af3a7c00da303a7ae0748d1"}, "time": "2022-08-19T17:21:16+00:00"}, {"version": "v2.15.19", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "971cd8c11af089bad473700c94b16de58d0eb743"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/971cd8c11af089bad473700c94b16de58d0eb743", "type": "zip", "shasum": "", "reference": "971cd8c11af089bad473700c94b16de58d0eb743"}, "time": "2022-08-16T21:36:34+00:00"}, {"version": "v2.15.19-beta1", "version_normalized": "*********-beta1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f377e88b4ea03b3234299bd98ff2c19c9ecc9aaf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f377e88b4ea03b3234299bd98ff2c19c9ecc9aaf", "type": "zip", "shasum": "", "reference": "f377e88b4ea03b3234299bd98ff2c19c9ecc9aaf"}, "time": "2022-08-11T07:50:28+00:00"}, {"version": "v2.15.18", "version_normalized": "*********"}, {"version": "v2.15.17", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "e3d528fa055dfcac652926f57afcafbc3d19543f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/e3d528fa055dfcac652926f57afcafbc3d19543f", "type": "zip", "shasum": "", "reference": "e3d528fa055dfcac652926f57afcafbc3d19543f"}, "time": "2022-08-09T10:40:01+00:00"}, {"version": "v2.15.16", "version_normalized": "*********"}, {"version": "v2.15.15", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "5673da81bd52bf19bf59833d53ebf903b4cba07a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/5673da81bd52bf19bf59833d53ebf903b4cba07a", "type": "zip", "shasum": "", "reference": "5673da81bd52bf19bf59833d53ebf903b4cba07a"}, "time": "2022-08-08T09:45:37+00:00"}, {"version": "v2.15.14", "version_normalized": "*********"}, {"version": "v2.15.13", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "06a1eedf6fbd356082d4da938536746c2cb560c0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/06a1eedf6fbd356082d4da938536746c2cb560c0", "type": "zip", "shasum": "", "reference": "06a1eedf6fbd356082d4da938536746c2cb560c0"}, "time": "2022-08-04T21:29:34+00:00"}, {"version": "v2.15.12", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6746544ff46046fede974307c4fe8ede8c70d487"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6746544ff46046fede974307c4fe8ede8c70d487", "type": "zip", "shasum": "", "reference": "6746544ff46046fede974307c4fe8ede8c70d487"}, "time": "2022-08-02T21:42:29+00:00"}, {"version": "v2.15.11", "version_normalized": "*********"}, {"version": "v2.15.10", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fb28f3108b49677ff75afd707b215bb63a2c4356"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fb28f3108b49677ff75afd707b215bb63a2c4356", "type": "zip", "shasum": "", "reference": "fb28f3108b49677ff75afd707b215bb63a2c4356"}, "time": "2022-08-01T21:25:21+00:00"}, {"version": "v2.15.9", "version_normalized": "********"}, {"version": "v2.15.8", "version_normalized": "********"}, {"version": "v2.15.7", "version_normalized": "********"}, {"version": "v2.15.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "78350d4b8b35f846724e002194db36b9c226e1da"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/78350d4b8b35f846724e002194db36b9c226e1da", "type": "zip", "shasum": "", "reference": "78350d4b8b35f846724e002194db36b9c226e1da"}, "time": "2022-08-01T19:47:09+00:00", "require": {"php": "^8.0", "illuminate/contracts": "^8.6|^9.0", "illuminate/filesystem": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "illuminate/view": "^8.6|^9.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v2.15.5", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "31b7633e8d5c3e89e5ca651a35d6a7b5243823bb"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/31b7633e8d5c3e89e5ca651a35d6a7b5243823bb", "type": "zip", "shasum": "", "reference": "31b7633e8d5c3e89e5ca651a35d6a7b5243823bb"}, "time": "2022-07-31T12:39:53+00:00", "require": {"php": "^8.0", "illuminate/contracts": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "illuminate/view": "^8.6|^9.0", "spatie/laravel-package-tools": "^1.9", "tgalopin/html-sanitizer": "^1.5"}}, {"version": "v2.15.4", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "cfe31cc0a9051fd25dae73d64fdc5bdef2c995b4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/cfe31cc0a9051fd25dae73d64fdc5bdef2c995b4", "type": "zip", "shasum": "", "reference": "cfe31cc0a9051fd25dae73d64fdc5bdef2c995b4"}, "time": "2022-07-29T10:37:57+00:00"}, {"version": "v2.15.3", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "654138b9a978767532397c9b7911907389a30643"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/654138b9a978767532397c9b7911907389a30643", "type": "zip", "shasum": "", "reference": "654138b9a978767532397c9b7911907389a30643"}, "time": "2022-07-26T14:00:49+00:00"}, {"version": "v2.15.2", "version_normalized": "********"}, {"version": "v2.15.1", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "4d1e6201af216c1fe4da25249b652466dce86290"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/4d1e6201af216c1fe4da25249b652466dce86290", "type": "zip", "shasum": "", "reference": "4d1e6201af216c1fe4da25249b652466dce86290"}, "time": "2022-07-25T21:46:30+00:00"}, {"version": "v2.15.0", "version_normalized": "********"}, {"version": "v2.14.5", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8f2859285ddb51cd7e36a9a83cbe51b614b7c2ee"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8f2859285ddb51cd7e36a9a83cbe51b614b7c2ee", "type": "zip", "shasum": "", "reference": "8f2859285ddb51cd7e36a9a83cbe51b614b7c2ee"}, "time": "2022-07-24T12:30:11+00:00"}, {"version": "v2.14.4", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "26de13969c86dd61768d565c377f8f7da17625fd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/26de13969c86dd61768d565c377f8f7da17625fd", "type": "zip", "shasum": "", "reference": "26de13969c86dd61768d565c377f8f7da17625fd"}, "time": "2022-07-19T06:13:49+00:00"}, {"version": "v2.14.3", "version_normalized": "********"}, {"version": "v2.14.2", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "308364e0a18058aa59b3200b7e83c3eb96f7bc1f"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/308364e0a18058aa59b3200b7e83c3eb96f7bc1f", "type": "zip", "shasum": "", "reference": "308364e0a18058aa59b3200b7e83c3eb96f7bc1f"}, "time": "2022-07-18T19:25:33+00:00"}, {"version": "v2.14.1", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d5200293f91480bfb1afac6700d3ad560c93f6bd"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d5200293f91480bfb1afac6700d3ad560c93f6bd", "type": "zip", "shasum": "", "reference": "d5200293f91480bfb1afac6700d3ad560c93f6bd"}, "time": "2022-07-17T21:24:52+00:00"}, {"version": "v2.14.0", "version_normalized": "********"}, {"version": "v2.13.28", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8ff747f47fd3d9ceba65a18076ada6ee34a708dc"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8ff747f47fd3d9ceba65a18076ada6ee34a708dc", "type": "zip", "shasum": "", "reference": "8ff747f47fd3d9ceba65a18076ada6ee34a708dc"}, "time": "2022-07-17T09:57:54+00:00"}, {"version": "v2.13.27", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "d89b1ae6c55d0ee54e034f84c3e25fd35baf25bf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/d89b1ae6c55d0ee54e034f84c3e25fd35baf25bf", "type": "zip", "shasum": "", "reference": "d89b1ae6c55d0ee54e034f84c3e25fd35baf25bf"}, "time": "2022-07-16T21:31:29+00:00"}, {"version": "v2.13.26", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "124cda486d06d82be1d791255fee42a27b2d1ec8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/124cda486d06d82be1d791255fee42a27b2d1ec8", "type": "zip", "shasum": "", "reference": "124cda486d06d82be1d791255fee42a27b2d1ec8"}, "time": "2022-07-14T09:43:01+00:00"}, {"version": "v2.13.25", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fa71ea66b8a70934ae66b8ab3127535f7c516913"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fa71ea66b8a70934ae66b8ab3127535f7c516913", "type": "zip", "shasum": "", "reference": "fa71ea66b8a70934ae66b8ab3127535f7c516913"}, "time": "2022-07-07T16:28:44+00:00"}, {"version": "v2.13.24", "version_normalized": "*********"}, {"version": "v2.13.23", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9b95785fde998e0d0f5fc21340226180bb379597"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9b95785fde998e0d0f5fc21340226180bb379597", "type": "zip", "shasum": "", "reference": "9b95785fde998e0d0f5fc21340226180bb379597"}, "time": "2022-07-07T07:16:52+00:00"}, {"version": "v2.13.22", "version_normalized": "*********"}, {"version": "v2.13.21", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6f40e798d58644e07d157380332b65b1e63fa7d0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6f40e798d58644e07d157380332b65b1e63fa7d0", "type": "zip", "shasum": "", "reference": "6f40e798d58644e07d157380332b65b1e63fa7d0"}, "time": "2022-07-03T12:51:13+00:00"}, {"version": "v2.13.20", "version_normalized": "*********"}, {"version": "v2.13.19", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "21588b79e45a88a82fc6f72eeddb22a34e57b9db"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/21588b79e45a88a82fc6f72eeddb22a34e57b9db", "type": "zip", "shasum": "", "reference": "21588b79e45a88a82fc6f72eeddb22a34e57b9db"}, "time": "2022-07-03T12:47:22+00:00"}, {"version": "v2.13.18", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "bb45ccf540a1f3520a976be920ceb1af0d7ee758"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/bb45ccf540a1f3520a976be920ceb1af0d7ee758", "type": "zip", "shasum": "", "reference": "bb45ccf540a1f3520a976be920ceb1af0d7ee758"}, "time": "2022-07-03T11:07:13+00:00"}, {"version": "v2.13.17", "version_normalized": "*********"}, {"version": "v2.13.16", "version_normalized": "*********"}, {"version": "v2.13.15", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "22557ecd76e541eb9567f03c4648de4c5f03c97e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/22557ecd76e541eb9567f03c4648de4c5f03c97e", "type": "zip", "shasum": "", "reference": "22557ecd76e541eb9567f03c4648de4c5f03c97e"}, "time": "2022-07-02T18:03:20+00:00", "require": {"php": "^8.0", "illuminate/contracts": "^8.6|^9.0", "illuminate/support": "^8.6|^9.0", "illuminate/view": "^8.6|^9.0", "spatie/laravel-package-tools": "^1.9"}}, {"version": "v2.13.14", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7ede0b21503000d11aed71e088f5f1b268bab0e5"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7ede0b21503000d11aed71e088f5f1b268bab0e5", "type": "zip", "shasum": "", "reference": "7ede0b21503000d11aed71e088f5f1b268bab0e5"}, "time": "2022-06-29T21:09:03+00:00"}, {"version": "v2.13.13", "version_normalized": "*********"}, {"version": "v2.13.12", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a183becd6cc19a7466703e1f4e262f36dfcea32b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a183becd6cc19a7466703e1f4e262f36dfcea32b", "type": "zip", "shasum": "", "reference": "a183becd6cc19a7466703e1f4e262f36dfcea32b"}, "time": "2022-06-27T09:39:36+00:00"}, {"version": "v2.13.11", "version_normalized": "*********"}, {"version": "v2.13.10", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "0ceb543182469cabae1796b3d680d2c3eb98a8cf"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/0ceb543182469cabae1796b3d680d2c3eb98a8cf", "type": "zip", "shasum": "", "reference": "0ceb543182469cabae1796b3d680d2c3eb98a8cf"}, "time": "2022-06-24T10:38:48+00:00"}, {"homepage": "https://github.com/laravel-filament/filament", "version": "v2.13.9", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "67d3936cc24eef742ed12d315f011160884aadbe"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/67d3936cc24eef742ed12d315f011160884aadbe", "type": "zip", "shasum": "", "reference": "67d3936cc24eef742ed12d315f011160884aadbe"}, "support": {"issues": "https://github.com/laravel-filament/filament/issues", "source": "https://github.com/laravel-filament/filament"}, "time": "2022-06-20T22:01:22+00:00"}, {"version": "v2.13.8", "version_normalized": "********"}, {"version": "v2.13.7", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "7dd3bc2062dd6db98c99c9ad41ff5600a85803c4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/7dd3bc2062dd6db98c99c9ad41ff5600a85803c4", "type": "zip", "shasum": "", "reference": "7dd3bc2062dd6db98c99c9ad41ff5600a85803c4"}, "time": "2022-06-19T21:33:09+00:00"}, {"version": "v2.13.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "85cfd14921e6bf65ff96af560acaef8e38966c5b"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/85cfd14921e6bf65ff96af560acaef8e38966c5b", "type": "zip", "shasum": "", "reference": "85cfd14921e6bf65ff96af560acaef8e38966c5b"}, "time": "2022-06-18T13:19:19+00:00"}, {"version": "v2.13.5", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "324afc3898654c4dfd35664b6eb853aba69d474d"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/324afc3898654c4dfd35664b6eb853aba69d474d", "type": "zip", "shasum": "", "reference": "324afc3898654c4dfd35664b6eb853aba69d474d"}, "time": "2022-06-17T21:55:11+00:00"}, {"version": "v2.13.4", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a7b98df8689eb76280865ff45f7eacf05bce6615"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a7b98df8689eb76280865ff45f7eacf05bce6615", "type": "zip", "shasum": "", "reference": "a7b98df8689eb76280865ff45f7eacf05bce6615"}, "time": "2022-06-16T05:53:48+00:00"}, {"version": "v2.13.3", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "38315cc576352f9b25abbd7559ff6ae45d68dfd8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/38315cc576352f9b25abbd7559ff6ae45d68dfd8", "type": "zip", "shasum": "", "reference": "38315cc576352f9b25abbd7559ff6ae45d68dfd8"}, "time": "2022-06-16T04:33:12+00:00"}, {"version": "v2.13.2", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "27306b5354266275b249e4cb881ec2f101565ac7"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/27306b5354266275b249e4cb881ec2f101565ac7", "type": "zip", "shasum": "", "reference": "27306b5354266275b249e4cb881ec2f101565ac7"}, "time": "2022-06-15T10:40:04+00:00"}, {"version": "v2.13.1", "version_normalized": "********"}, {"version": "v2.13.0", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "9a56717fa94f9a1b8eb7ef0dbed934506b27b1b9"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/9a56717fa94f9a1b8eb7ef0dbed934506b27b1b9", "type": "zip", "shasum": "", "reference": "9a56717fa94f9a1b8eb7ef0dbed934506b27b1b9"}, "time": "2022-06-15T09:06:30+00:00"}, {"version": "v2.13.0-beta1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "71b361b0d672b864ed0dce3a34e3462f1aa46b34"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/71b361b0d672b864ed0dce3a34e3462f1aa46b34", "type": "zip", "shasum": "", "reference": "71b361b0d672b864ed0dce3a34e3462f1aa46b34"}, "time": "2022-06-14T08:52:39+00:00"}, {"version": "v2.12.31", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "f76c0f100b45d364b2bd40a89849a49ffbc21c7a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/f76c0f100b45d364b2bd40a89849a49ffbc21c7a", "type": "zip", "shasum": "", "reference": "f76c0f100b45d364b2bd40a89849a49ffbc21c7a"}, "time": "2022-06-12T13:04:15+00:00"}, {"version": "v2.12.30", "version_normalized": "*********"}, {"version": "v2.12.29", "version_normalized": "*********"}, {"version": "v2.12.28", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "512caf82b0e49fcf4cd21996bf0adb5088c48589"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/512caf82b0e49fcf4cd21996bf0adb5088c48589", "type": "zip", "shasum": "", "reference": "512caf82b0e49fcf4cd21996bf0adb5088c48589"}, "time": "2022-06-08T15:10:59+00:00"}, {"version": "v2.12.27", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "dc12170f92a27477acdd311d5e3bdab0da5e343a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/dc12170f92a27477acdd311d5e3bdab0da5e343a", "type": "zip", "shasum": "", "reference": "dc12170f92a27477acdd311d5e3bdab0da5e343a"}, "time": "2022-06-06T08:35:59+00:00"}, {"version": "v2.12.26", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "120c027fa021c80f5d57d1f40f44eaa6c812878a"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/120c027fa021c80f5d57d1f40f44eaa6c812878a", "type": "zip", "shasum": "", "reference": "120c027fa021c80f5d57d1f40f44eaa6c812878a"}, "time": "2022-06-04T12:05:19+00:00"}, {"version": "v2.12.25", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2fe64a437a51aeb58f85a3133eb5350e83c29a40"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2fe64a437a51aeb58f85a3133eb5350e83c29a40", "type": "zip", "shasum": "", "reference": "2fe64a437a51aeb58f85a3133eb5350e83c29a40"}, "time": "2022-05-31T22:27:09+00:00"}, {"version": "v2.12.24", "version_normalized": "*********"}, {"version": "v2.12.23", "version_normalized": "*********"}, {"version": "v2.12.22", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "37e2745bd47c4417e622cae8406b6f934e0de3c8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/37e2745bd47c4417e622cae8406b6f934e0de3c8", "type": "zip", "shasum": "", "reference": "37e2745bd47c4417e622cae8406b6f934e0de3c8"}, "time": "2022-05-29T12:35:06+00:00"}, {"version": "v2.12.21", "version_normalized": "*********"}, {"version": "v2.12.20", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "3e05fc5c00ef0f3c00288cd1ead908cc4bd58d7e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/3e05fc5c00ef0f3c00288cd1ead908cc4bd58d7e", "type": "zip", "shasum": "", "reference": "3e05fc5c00ef0f3c00288cd1ead908cc4bd58d7e"}, "time": "2022-05-26T23:14:29+00:00"}, {"version": "v2.12.19", "version_normalized": "*********"}, {"version": "v2.12.18", "version_normalized": "*********"}, {"version": "v2.12.17", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "08e21a014e11afeb3412a4ce3f45ea35150ac401"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/08e21a014e11afeb3412a4ce3f45ea35150ac401", "type": "zip", "shasum": "", "reference": "08e21a014e11afeb3412a4ce3f45ea35150ac401"}, "time": "2022-05-25T22:23:39+00:00"}, {"version": "v2.12.16", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "fd30229348b4b6274383eea7a63bbf587795a7b4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/fd30229348b4b6274383eea7a63bbf587795a7b4", "type": "zip", "shasum": "", "reference": "fd30229348b4b6274383eea7a63bbf587795a7b4"}, "time": "2022-05-25T10:31:23+00:00"}, {"version": "v2.12.15", "version_normalized": "*********"}, {"version": "v2.12.14", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "2267596027215d7a9eb82ddc729e09a6f2a2619e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/2267596027215d7a9eb82ddc729e09a6f2a2619e", "type": "zip", "shasum": "", "reference": "2267596027215d7a9eb82ddc729e09a6f2a2619e"}, "time": "2022-05-22T12:33:20+00:00"}, {"version": "v2.12.13", "version_normalized": "*********"}, {"version": "v2.12.12", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c49bebe0210dd4d444f568fb79318fd4be12fe54"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c49bebe0210dd4d444f568fb79318fd4be12fe54", "type": "zip", "shasum": "", "reference": "c49bebe0210dd4d444f568fb79318fd4be12fe54"}, "time": "2022-05-20T20:15:13+00:00"}, {"version": "v2.12.11", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "b953ab989d5b0bad919b996d1cd7cbea3c95c347"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/b953ab989d5b0bad919b996d1cd7cbea3c95c347", "type": "zip", "shasum": "", "reference": "b953ab989d5b0bad919b996d1cd7cbea3c95c347"}, "time": "2022-05-18T19:34:43+00:00"}, {"version": "v2.12.10", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "6d3c61ee431e43b8cdf4438a874ec8e065d04bc1"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/6d3c61ee431e43b8cdf4438a874ec8e065d04bc1", "type": "zip", "shasum": "", "reference": "6d3c61ee431e43b8cdf4438a874ec8e065d04bc1"}, "time": "2022-05-14T06:44:01+00:00"}, {"version": "v2.12.9", "version_normalized": "********"}, {"version": "v2.12.8", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "c1d74c090d46c86a777d65c2ea7d279ca396fb62"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/c1d74c090d46c86a777d65c2ea7d279ca396fb62", "type": "zip", "shasum": "", "reference": "c1d74c090d46c86a777d65c2ea7d279ca396fb62"}, "time": "2022-05-13T13:27:11+00:00"}, {"version": "v2.12.7", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "afde23789bbe6c1791c1bce2aa394d6a9dbe4dd0"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/afde23789bbe6c1791c1bce2aa394d6a9dbe4dd0", "type": "zip", "shasum": "", "reference": "afde23789bbe6c1791c1bce2aa394d6a9dbe4dd0"}, "time": "2022-05-12T10:57:39+00:00"}, {"version": "v2.12.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "75673404d49e4c33cfcbf63f8f2ccd2e21b925b3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/75673404d49e4c33cfcbf63f8f2ccd2e21b925b3", "type": "zip", "shasum": "", "reference": "75673404d49e4c33cfcbf63f8f2ccd2e21b925b3"}, "time": "2022-05-12T05:37:35+00:00"}, {"version": "v2.12.5", "version_normalized": "********"}, {"version": "v2.12.4", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "238e60a8d8c589ac3446155328d4aa0c9a17b9d3"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/238e60a8d8c589ac3446155328d4aa0c9a17b9d3", "type": "zip", "shasum": "", "reference": "238e60a8d8c589ac3446155328d4aa0c9a17b9d3"}, "time": "2022-05-11T22:23:42+00:00"}, {"version": "v2.12.3", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "72e5ebd0bdc90ad846c841db3e49ef0edef54096"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/72e5ebd0bdc90ad846c841db3e49ef0edef54096", "type": "zip", "shasum": "", "reference": "72e5ebd0bdc90ad846c841db3e49ef0edef54096"}, "time": "2022-05-09T20:47:46+00:00"}, {"version": "v2.12.2", "version_normalized": "********"}, {"version": "v2.12.1", "version_normalized": "********"}, {"version": "v2.12.0", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "564e297e60b05479112a5e86fb6cb2917ec81c83"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/564e297e60b05479112a5e86fb6cb2917ec81c83", "type": "zip", "shasum": "", "reference": "564e297e60b05479112a5e86fb6cb2917ec81c83"}, "time": "2022-05-09T10:59:50+00:00"}, {"version": "v2.12.0-beta8", "version_normalized": "********-beta8"}, {"version": "v2.12.0-beta7", "version_normalized": "********-beta7"}, {"version": "v2.12.0-beta6", "version_normalized": "********-beta6"}, {"version": "v2.12.0-beta5", "version_normalized": "********-beta5"}, {"version": "v2.12.0-beta4", "version_normalized": "********-beta4"}, {"version": "v2.12.0-beta3", "version_normalized": "********-beta3"}, {"version": "v2.12.0-beta2", "version_normalized": "********-beta2"}, {"version": "v2.12.0-beta1", "version_normalized": "********-beta1"}, {"version": "v2.11.12", "version_normalized": "*********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "8da7729b13bb07cf613473020360754903198de6"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/8da7729b13bb07cf613473020360754903198de6", "type": "zip", "shasum": "", "reference": "8da7729b13bb07cf613473020360754903198de6"}, "time": "2022-05-06T10:12:59+00:00", "autoload": {"psr-4": {"Filament\\Support\\": "src"}}, "require": {"php": "^8.0"}, "extra": "__unset"}, {"version": "v2.11.11", "version_normalized": "*********"}, {"version": "v2.11.10", "version_normalized": "*********"}, {"version": "v2.11.9", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "ef67b967dfc4dea4c14f34696df08fc2e0eec326"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/ef67b967dfc4dea4c14f34696df08fc2e0eec326", "type": "zip", "shasum": "", "reference": "ef67b967dfc4dea4c14f34696df08fc2e0eec326"}, "time": "2022-04-28T10:24:16+00:00"}, {"version": "v2.11.8", "version_normalized": "********"}, {"version": "v2.11.7", "version_normalized": "********"}, {"version": "v2.11.6", "version_normalized": "********", "source": {"url": "https://github.com/filamentphp/support.git", "type": "git", "reference": "a568aceb6fe34a800ffc28c61887f8d8793c4c3e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/support/zipball/a568aceb6fe34a800ffc28c61887f8d8793c4c3e", "type": "zip", "shasum": "", "reference": "a568aceb6fe34a800ffc28c61887f8d8793c4c3e"}, "time": "2022-04-27T00:28:26+00:00"}, {"version": "v2.11.5", "version_normalized": "********"}, {"version": "v2.11.4", "version_normalized": "********"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 08 Jul 2025 21:00:25 GMT"}