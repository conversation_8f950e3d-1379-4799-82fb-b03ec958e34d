{"minified": "composer/2.0", "packages": {"danharrin/date-format-converter": [{"name": "danharrin/date-format-converter", "description": "Convert token-based date formats between standards.", "keywords": [], "homepage": "https://github.com/danharrin/date-format-converter", "version": "v0.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/danharrin/date-format-converter.git", "type": "git", "reference": "7c31171bc981e48726729a5f3a05a2d2b63f0b1e"}, "dist": {"url": "https://api.github.com/repos/danharrin/date-format-converter/zipball/7c31171bc981e48726729a5f3a05a2d2b63f0b1e", "type": "zip", "shasum": "", "reference": "7c31171bc981e48726729a5f3a05a2d2b63f0b1e"}, "type": "library", "support": {"issues": "https://github.com/danharrin/date-format-converter/issues", "source": "https://github.com/danharrin/date-format-converter"}, "funding": [{"url": "https://github.com/danharrin", "type": "github"}], "time": "2024-06-13T09:38:44+00:00", "autoload": {"files": ["src/helpers.php", "src/standards.php"], "psr-4": {"DanHarrin\\DateFormatConverter\\": "src/"}}, "require": {"php": "^7.2|^8.0"}}, {"version": "v0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/danharrin/date-format-converter.git", "type": "git", "reference": "42b6ddc52059d4ba228a67c15adaaa0c039e75f2"}, "dist": {"url": "https://api.github.com/repos/danharrin/date-format-converter/zipball/42b6ddc52059d4ba228a67c15adaaa0c039e75f2", "type": "zip", "shasum": "", "reference": "42b6ddc52059d4ba228a67c15adaaa0c039e75f2"}, "time": "2022-09-29T07:48:20+00:00"}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/danharrin/date-format-converter.git", "type": "git", "reference": "ee448ab0cbe2ea36edb886a01670fc760e388f19"}, "dist": {"url": "https://api.github.com/repos/danharrin/date-format-converter/zipball/ee448ab0cbe2ea36edb886a01670fc760e388f19", "type": "zip", "shasum": "", "reference": "ee448ab0cbe2ea36edb886a01670fc760e388f19"}, "time": "2021-02-10T23:58:47+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/danharrin/date-format-converter.git", "type": "git", "reference": "6ee7b5bc62069ebbe9569a31144c3db074de979b"}, "dist": {"url": "https://api.github.com/repos/danharrin/date-format-converter/zipball/6ee7b5bc62069ebbe9569a31144c3db074de979b", "type": "zip", "shasum": "", "reference": "6ee7b5bc62069ebbe9569a31144c3db074de979b"}, "time": "2021-02-10T22:10:43+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"DanHarrin\\DateFormatConverter\\": "src/"}}}]}, "security-advisories": [], "last-modified": "Thu, 13 Jun 2024 09:39:52 GMT"}