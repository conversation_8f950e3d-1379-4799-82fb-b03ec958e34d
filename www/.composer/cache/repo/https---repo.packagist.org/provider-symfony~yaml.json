{"minified": "composer/2.0", "packages": {"symfony/yaml": [{"name": "symfony/yaml", "description": "Loads and dumps YAML files", "keywords": [], "homepage": "https://symfony.com", "version": "v7.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0c3555045a46ab3cd4cc5a69d161225195230edb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0c3555045a46ab3cd4cc5a69d161225195230edb", "type": "zip", "shasum": "", "reference": "0c3555045a46ab3cd4cc5a69d161225195230edb"}, "type": "library", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-03T06:57:57+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "bin": ["Resources/bin/yaml-lint"], "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "conflict": {"symfony/console": "<6.4"}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "cea40a48279d58dc3efee8112634cb90141156c2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/cea40a48279d58dc3efee8112634cb90141156c2", "type": "zip", "shasum": "", "reference": "cea40a48279d58dc3efee8112634cb90141156c2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.0"}, "time": "2025-04-04T10:10:33+00:00"}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.0-BETA1"}}, {"version": "v7.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "262cbc0765a2fa4793efbdad500236dda66106b1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/262cbc0765a2fa4793efbdad500236dda66106b1", "type": "zip", "shasum": "", "reference": "262cbc0765a2fa4793efbdad500236dda66106b1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.8"}, "time": "2025-06-03T06:57:06+00:00"}, {"version": "v7.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0feafffb843860624ddfd13478f481f4c3cd8b23"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0feafffb843860624ddfd13478f481f4c3cd8b23", "type": "zip", "shasum": "", "reference": "0feafffb843860624ddfd13478f481f4c3cd8b23"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.6"}, "time": "2025-04-04T10:10:11+00:00"}, {"version": "v7.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4c4b6f4cfcd7e52053f0c8bfad0f7f30fb924912"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4c4b6f4cfcd7e52053f0c8bfad0f7f30fb924912", "type": "zip", "shasum": "", "reference": "4c4b6f4cfcd7e52053f0c8bfad0f7f30fb924912"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.5"}, "time": "2025-03-03T07:12:39+00:00"}, {"version": "v7.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ac238f173df0c9c1120f862d0f599e17535a87ec"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ac238f173df0c9c1120f862d0f599e17535a87ec", "type": "zip", "shasum": "", "reference": "ac238f173df0c9c1120f862d0f599e17535a87ec"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.3"}, "time": "2025-01-07T12:55:42+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "099581e99f557e9f16b43c5916c26380b54abb22"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/099581e99f557e9f16b43c5916c26380b54abb22", "type": "zip", "shasum": "", "reference": "099581e99f557e9f16b43c5916c26380b54abb22"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.0"}, "time": "2024-10-23T06:56:12+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.0-BETA1"}}, {"version": "v7.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4921b8c1db90c13ba2ee0520080ef6800912b018"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4921b8c1db90c13ba2ee0520080ef6800912b018", "type": "zip", "shasum": "", "reference": "4921b8c1db90c13ba2ee0520080ef6800912b018"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.11"}, "time": "2025-01-07T12:50:05+00:00", "require": {"php": ">=8.2", "symfony/polyfill-ctype": "^1.8"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3ced3f29e4f0d6bce2170ff26719f1fe9aacc671"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3ced3f29e4f0d6bce2170ff26719f1fe9aacc671", "type": "zip", "shasum": "", "reference": "3ced3f29e4f0d6bce2170ff26719f1fe9aacc671"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4e561c316e135e053bd758bf3b3eb291d9919de4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4e561c316e135e053bd758bf3b3eb291d9919de4", "type": "zip", "shasum": "", "reference": "4e561c316e135e053bd758bf3b3eb291d9919de4"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.5"}, "time": "2024-09-17T12:49:58+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "92e080b851c1c655c786a2da77f188f2dccd0f4b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/92e080b851c1c655c786a2da77f188f2dccd0f4b", "type": "zip", "shasum": "", "reference": "92e080b851c1c655c786a2da77f188f2dccd0f4b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.4"}, "time": "2024-08-12T09:59:40+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "fa34c77015aa6720469db7003567b9f772492bf2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/fa34c77015aa6720469db7003567b9f772492bf2", "type": "zip", "shasum": "", "reference": "fa34c77015aa6720469db7003567b9f772492bf2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c5f718c94e3c37dd77b77484e6cf0b524b2d405e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c5f718c94e3c37dd77b77484e6cf0b524b2d405e", "type": "zip", "shasum": "", "reference": "c5f718c94e3c37dd77b77484e6cf0b524b2d405e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.0"}, "time": "2024-04-28T18:29:00+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.0-BETA1"}}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "89bdddd79e918448ce978be664768ef27b9e5798"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/89bdddd79e918448ce978be664768ef27b9e5798", "type": "zip", "shasum": "", "reference": "89bdddd79e918448ce978be664768ef27b9e5798"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0d3916ae69ea28b59d94b60c4f2b50f4e25adb5c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0d3916ae69ea28b59d94b60c4f2b50f4e25adb5c", "type": "zip", "shasum": "", "reference": "0d3916ae69ea28b59d94b60c4f2b50f4e25adb5c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.7"}, "time": "2024-04-28T11:44:19+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2d4fca631c00700597e9442a0b2451ce234513d3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2d4fca631c00700597e9442a0b2451ce234513d3", "type": "zip", "shasum": "", "reference": "2d4fca631c00700597e9442a0b2451ce234513d3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0055b230c408428b9b5cde7c55659555be5c0278"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0055b230c408428b9b5cde7c55659555be5c0278", "type": "zip", "shasum": "", "reference": "0055b230c408428b9b5cde7c55659555be5c0278"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.0"}, "time": "2023-11-07T10:26:03+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5efffe80603184d275e30c507941fee329f5ee87"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5efffe80603184d275e30c507941fee329f5ee87", "type": "zip", "shasum": "", "reference": "5efffe80603184d275e30c507941fee329f5ee87"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.0-BETA2"}, "time": "2023-10-29T12:50:44+00:00"}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "60bb70d63af1b4e2e6fe46fe389cf169a703bd62"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/60bb70d63af1b4e2e6fe46fe389cf169a703bd62", "type": "zip", "shasum": "", "reference": "60bb70d63af1b4e2e6fe46fe389cf169a703bd62"}, "support": {"source": "https://github.com/symfony/yaml/tree/v7.0.0-BETA1"}, "time": "2023-09-27T14:05:33+00:00"}, {"version": "v6.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "93e29e0deb5f1b2e360adfb389a20d25eb81a27b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/93e29e0deb5f1b2e360adfb389a20d25eb81a27b", "type": "zip", "shasum": "", "reference": "93e29e0deb5f1b2e360adfb389a20d25eb81a27b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.23"}, "time": "2025-06-03T06:46:12+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/console": "<5.4"}}, {"version": "v6.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f01987f45676778b474468aa266fe2eda1f2bc7e", "type": "zip", "shasum": "", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.21"}, "time": "2025-04-04T09:48:44+00:00"}, {"version": "v6.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "28ee818fce4a73ac1474346b94e4b966f665c53f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/28ee818fce4a73ac1474346b94e4b966f665c53f", "type": "zip", "shasum": "", "reference": "28ee818fce4a73ac1474346b94e4b966f665c53f"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.20"}, "time": "2025-02-27T20:15:30+00:00"}, {"version": "v6.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bf598c9d9bb4a22f495a4e26e4c4fce2f8ecefc5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bf598c9d9bb4a22f495a4e26e4c4fce2f8ecefc5", "type": "zip", "shasum": "", "reference": "bf598c9d9bb4a22f495a4e26e4c4fce2f8ecefc5"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.18"}, "time": "2025-01-07T09:44:41+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e99b4e94d124b29ee4cf3140e1b537d2dad8cec9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e99b4e94d124b29ee4cf3140e1b537d2dad8cec9", "type": "zip", "shasum": "", "reference": "e99b4e94d124b29ee4cf3140e1b537d2dad8cec9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "762ee56b2649659380e0ef4d592d807bc17b7971"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/762ee56b2649659380e0ef4d592d807bc17b7971", "type": "zip", "shasum": "", "reference": "762ee56b2649659380e0ef4d592d807bc17b7971"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.12"}, "time": "2024-09-17T12:47:12+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "be37e7f13195e05ab84ca5269365591edd240335"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/be37e7f13195e05ab84ca5269365591edd240335", "type": "zip", "shasum": "", "reference": "be37e7f13195e05ab84ca5269365591edd240335"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.11"}, "time": "2024-08-12T09:55:28+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "52903de178d542850f6f341ba92995d3d63e60c9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/52903de178d542850f6f341ba92995d3d63e60c9", "type": "zip", "shasum": "", "reference": "52903de178d542850f6f341ba92995d3d63e60c9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "53e8b1ef30a65f78eac60fddc5ee7ebbbdb1dee0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/53e8b1ef30a65f78eac60fddc5ee7ebbbdb1dee0", "type": "zip", "shasum": "", "reference": "53e8b1ef30a65f78eac60fddc5ee7ebbbdb1dee0"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.7"}, "time": "2024-04-28T10:28:08+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d75715985f0f94f978e3a8fa42533e10db921b90"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d75715985f0f94f978e3a8fa42533e10db921b90", "type": "zip", "shasum": "", "reference": "d75715985f0f94f978e3a8fa42533e10db921b90"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4f9237a1bb42455d609e6687d2613dde5b41a587"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4f9237a1bb42455d609e6687d2613dde5b41a587", "type": "zip", "shasum": "", "reference": "4f9237a1bb42455d609e6687d2613dde5b41a587"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.0"}, "time": "2023-11-06T11:00:25+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "96d3ed6e28fdffa9d07d874e586c4d5da59798f6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/96d3ed6e28fdffa9d07d874e586c4d5da59798f6", "type": "zip", "shasum": "", "reference": "96d3ed6e28fdffa9d07d874e586c4d5da59798f6"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.0-BETA2"}, "time": "2023-10-29T09:51:40+00:00"}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "63b478dd68d045bea0fb6dbeb8a9c663f35ed7b9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/63b478dd68d045bea0fb6dbeb8a9c663f35ed7b9", "type": "zip", "shasum": "", "reference": "63b478dd68d045bea0fb6dbeb8a9c663f35ed7b9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.0-BETA1"}, "time": "2023-09-25T12:52:38+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8ab9bb61e9b862c9b481af745ff163bc5e5e6246"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8ab9bb61e9b862c9b481af745ff163bc5e5e6246", "type": "zip", "shasum": "", "reference": "8ab9bb61e9b862c9b481af745ff163bc5e5e6246"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.12"}, "time": "2024-01-23T14:35:58+00:00", "require-dev": {"symfony/console": "^5.4|^6.0"}}, {"version": "v6.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3493af8a8dad7fa91c77fa473ba23ecd95334a92"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3493af8a8dad7fa91c77fa473ba23ecd95334a92", "type": "zip", "shasum": "", "reference": "3493af8a8dad7fa91c77fa473ba23ecd95334a92"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.8"}, "time": "2023-11-06T10:58:05+00:00"}, {"version": "v6.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9758b6c69d179936435d0ffb577c3708d57e38a8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9758b6c69d179936435d0ffb577c3708d57e38a8", "type": "zip", "shasum": "", "reference": "9758b6c69d179936435d0ffb577c3708d57e38a8"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.7"}, "time": "2023-10-28T23:31:00+00:00"}, {"version": "v6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e23292e8c07c85b971b44c1c4b87af52133e2add"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e23292e8c07c85b971b44c1c4b87af52133e2add", "type": "zip", "shasum": "", "reference": "e23292e8c07c85b971b44c1c4b87af52133e2add"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.3"}, "time": "2023-07-31T07:08:24+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a9a8337aa641ef2aa39c3e028f9107ec391e5927"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a9a8337aa641ef2aa39c3e028f9107ec391e5927", "type": "zip", "shasum": "", "reference": "a9a8337aa641ef2aa39c3e028f9107ec391e5927"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.0"}, "time": "2023-04-28T13:28:14+00:00", "require": {"php": ">=8.1", "symfony/polyfill-ctype": "^1.8"}}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/yaml/tree/v6.3.0-BETA1"}}, {"version": "v6.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "61916f3861b1e9705b18cfde723921a71dd1559d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/61916f3861b1e9705b18cfde723921a71dd1559d", "type": "zip", "shasum": "", "reference": "61916f3861b1e9705b18cfde723921a71dd1559d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.10"}, "time": "2023-04-28T13:25:36+00:00", "suggest": {"symfony/console": "For validating YAML files using the lint command"}}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e8e6a1d59e050525f27a1f530aa9703423cb7f57"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e8e6a1d59e050525f27a1f530aa9703423cb7f57", "type": "zip", "shasum": "", "reference": "e8e6a1d59e050525f27a1f530aa9703423cb7f57"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.7"}, "time": "2023-02-16T09:57:23+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2bbfbdacc8a15574f8440c4838ce0d7bb6c86b19"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2bbfbdacc8a15574f8440c4838ce0d7bb6c86b19", "type": "zip", "shasum": "", "reference": "2bbfbdacc8a15574f8440c4838ce0d7bb6c86b19"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.5"}, "time": "2023-01-10T18:53:53+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6ed8243aa5f2cb5a57009f826b5e7fb3c4200cf3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6ed8243aa5f2cb5a57009f826b5e7fb3c4200cf3", "type": "zip", "shasum": "", "reference": "6ed8243aa5f2cb5a57009f826b5e7fb3c4200cf3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.2"}, "time": "2022-12-14T16:11:27+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f2570f21bd4adc3589aa3133323273995109bae0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f2570f21bd4adc3589aa3133323273995109bae0", "type": "zip", "shasum": "", "reference": "f2570f21bd4adc3589aa3133323273995109bae0"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.0"}, "time": "2022-11-25T19:00:27+00:00"}, {"version": "v6.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.0-RC2"}}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d3cf3434b78f0ad3df846cebc552f745d01d90ee"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d3cf3434b78f0ad3df846cebc552f745d01d90ee", "type": "zip", "shasum": "", "reference": "d3cf3434b78f0ad3df846cebc552f745d01d90ee"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.0-RC1"}, "time": "2022-11-14T09:35:55+00:00"}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b5fbe4feba6891de7ec1393b0282b13401a74049"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b5fbe4feba6891de7ec1393b0282b13401a74049", "type": "zip", "shasum": "", "reference": "b5fbe4feba6891de7ec1393b0282b13401a74049"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.2.0-BETA1"}, "time": "2022-10-09T08:55:43+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "468e41d297d9c3c850e9de149d67b06a907e20b3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/468e41d297d9c3c850e9de149d67b06a907e20b3", "type": "zip", "shasum": "", "reference": "468e41d297d9c3c850e9de149d67b06a907e20b3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.11"}, "time": "2023-01-10T18:53:01+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ef4e569114404d6d0ae832f8ad3cd78e68cfb4d0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ef4e569114404d6d0ae832f8ad3cd78e68cfb4d0", "type": "zip", "shasum": "", "reference": "ef4e569114404d6d0ae832f8ad3cd78e68cfb4d0"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.9"}, "time": "2022-12-14T16:05:20+00:00"}, {"version": "v6.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "20e9985d3fc6a77289102c47b6a110b6fbd24585"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/20e9985d3fc6a77289102c47b6a110b6fbd24585", "type": "zip", "shasum": "", "reference": "20e9985d3fc6a77289102c47b6a110b6fbd24585"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.8"}, "time": "2022-11-25T18:59:16+00:00"}, {"version": "v6.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "66c6b0cf52b00f74614a2cf7ae7db08ea1095931"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/66c6b0cf52b00f74614a2cf7ae7db08ea1095931", "type": "zip", "shasum": "", "reference": "66c6b0cf52b00f74614a2cf7ae7db08ea1095931"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.6"}, "time": "2022-10-07T08:04:03+00:00"}, {"version": "v6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "86ee4d8fa594ed45e40d86eedfda1bcb66c8d919"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/86ee4d8fa594ed45e40d86eedfda1bcb66c8d919", "type": "zip", "shasum": "", "reference": "86ee4d8fa594ed45e40d86eedfda1bcb66c8d919"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.4"}, "time": "2022-08-02T16:17:38+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "cc48dd42ae1201abced04ae38284e23ce2d2d8f3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/cc48dd42ae1201abced04ae38284e23ce2d2d8f3", "type": "zip", "shasum": "", "reference": "cc48dd42ae1201abced04ae38284e23ce2d2d8f3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.3"}, "time": "2022-07-20T14:45:06+00:00"}, {"version": "v6.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b01c4e7dc6a51cbf114567af04a19789fd1011fe"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b01c4e7dc6a51cbf114567af04a19789fd1011fe", "type": "zip", "shasum": "", "reference": "b01c4e7dc6a51cbf114567af04a19789fd1011fe"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.2"}, "time": "2022-06-20T12:01:07+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "84ce4f9d2d68f306f971a39d949d8f4b5550dba2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/84ce4f9d2d68f306f971a39d949d8f4b5550dba2", "type": "zip", "shasum": "", "reference": "84ce4f9d2d68f306f971a39d949d8f4b5550dba2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.0"}, "time": "2022-04-15T14:25:02+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.0-BETA2"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "85d7045a98d8fa2854310c94e0311dcc46c834d4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/85d7045a98d8fa2854310c94e0311dcc46c834d4", "type": "zip", "shasum": "", "reference": "85d7045a98d8fa2854310c94e0311dcc46c834d4"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.1.0-BETA1"}, "time": "2022-04-14T08:23:11+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "deec3a812a0305a50db8ae689b183f43d915c884"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/deec3a812a0305a50db8ae689b183f43d915c884", "type": "zip", "shasum": "", "reference": "deec3a812a0305a50db8ae689b183f43d915c884"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.19"}, "time": "2023-01-11T11:50:03+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "^1.8"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "76c08913ea1c50541503a4563b2172710189fa29"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/76c08913ea1c50541503a4563b2172710189fa29", "type": "zip", "shasum": "", "reference": "76c08913ea1c50541503a4563b2172710189fa29"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.17"}, "time": "2022-12-14T15:52:41+00:00"}, {"version": "v6.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "eb85bd1b0b297e976f3ada52ad239ef80b4dbd0b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/eb85bd1b0b297e976f3ada52ad239ef80b4dbd0b", "type": "zip", "shasum": "", "reference": "eb85bd1b0b297e976f3ada52ad239ef80b4dbd0b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.16"}, "time": "2022-11-25T18:58:46+00:00"}, {"version": "v6.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e65020d137ad54beb85a67ffe6435e980f35ccf3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e65020d137ad54beb85a67ffe6435e980f35ccf3", "type": "zip", "shasum": "", "reference": "e65020d137ad54beb85a67ffe6435e980f35ccf3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.14"}, "time": "2022-10-07T08:02:12+00:00"}, {"version": "v6.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8c68efb08b038ec02753da6f16e1601a6ed4ef17"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8c68efb08b038ec02753da6f16e1601a6ed4ef17", "type": "zip", "shasum": "", "reference": "8c68efb08b038ec02753da6f16e1601a6ed4ef17"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.12"}, "time": "2022-08-02T16:01:06+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f41d702439aa1ee8db78a711d1822e73073eecbf"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f41d702439aa1ee8db78a711d1822e73073eecbf", "type": "zip", "shasum": "", "reference": "f41d702439aa1ee8db78a711d1822e73073eecbf"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.11"}, "time": "2022-07-20T14:06:08+00:00"}, {"version": "v6.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3b039081c8a6ff8773db2fc11069f2da4960449c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3b039081c8a6ff8773db2fc11069f2da4960449c", "type": "zip", "shasum": "", "reference": "3b039081c8a6ff8773db2fc11069f2da4960449c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.10"}, "time": "2022-06-20T11:58:32+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e77f3ea0b21141d771d4a5655faa54f692b34af5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e77f3ea0b21141d771d4a5655faa54f692b34af5", "type": "zip", "shasum": "", "reference": "e77f3ea0b21141d771d4a5655faa54f692b34af5"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.3"}, "time": "2022-01-26T17:23:29+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ed602f38b8636a2ea21af760d2578f3d2f92fc60"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ed602f38b8636a2ea21af760d2578f3d2f92fc60", "type": "zip", "shasum": "", "reference": "ed602f38b8636a2ea21af760d2578f3d2f92fc60"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.2"}, "time": "2021-12-16T22:13:01+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d34390fe7e8c0fe7e4192c67c27ecf58bc7d3ed7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d34390fe7e8c0fe7e4192c67c27ecf58bc7d3ed7", "type": "zip", "shasum": "", "reference": "d34390fe7e8c0fe7e4192c67c27ecf58bc7d3ed7"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f3064a2e0b5eabaeaf92db0a5913a77098b3b91e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f3064a2e0b5eabaeaf92db0a5913a77098b3b91e", "type": "zip", "shasum": "", "reference": "f3064a2e0b5eabaeaf92db0a5913a77098b3b91e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.0"}, "time": "2021-11-28T15:34:37+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e40ca917c2c5c3b34015d98de2d3e8526055a925"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e40ca917c2c5c3b34015d98de2d3e8526055a925", "type": "zip", "shasum": "", "reference": "e40ca917c2c5c3b34015d98de2d3e8526055a925"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.0-RC1"}, "time": "2021-11-20T17:00:10+00:00"}, {"version": "v6.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "640e162066ff3c8baffa092b539c8911b5086792"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/640e162066ff3c8baffa092b539c8911b5086792", "type": "zip", "shasum": "", "reference": "640e162066ff3c8baffa092b539c8911b5086792"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.0-BETA2"}, "time": "2021-11-14T16:37:47+00:00"}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "78ba96788a30db0846f50085435f332b03ff1c83"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/78ba96788a30db0846f50085435f332b03ff1c83", "type": "zip", "shasum": "", "reference": "78ba96788a30db0846f50085435f332b03ff1c83"}, "support": {"source": "https://github.com/symfony/yaml/tree/v6.0.0-BETA1"}, "time": "2021-10-26T16:02:09+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a454d47278cc16a5db371fe73ae66a78a633371e", "type": "zip", "shasum": "", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "conflict": {"symfony/console": "<5.3"}}, {"version": "v5.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7025b964f123bbf1896d7563db6ec7f1f63e918a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7025b964f123bbf1896d7563db6ec7f1f63e918a", "type": "zip", "shasum": "", "reference": "7025b964f123bbf1896d7563db6ec7f1f63e918a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.44"}, "time": "2024-09-16T14:36:56+00:00"}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "62f96e1cfd4cf518882a36bfedcf1fe4093c1299"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/62f96e1cfd4cf518882a36bfedcf1fe4093c1299", "type": "zip", "shasum": "", "reference": "62f96e1cfd4cf518882a36bfedcf1fe4093c1299"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.43"}, "time": "2024-08-11T17:40:32+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "81cad0ceab3d61fe14fe941ff18a230ac9c80f83"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/81cad0ceab3d61fe14fe941ff18a230ac9c80f83", "type": "zip", "shasum": "", "reference": "81cad0ceab3d61fe14fe941ff18a230ac9c80f83"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bc780e16879000f77a1022163c052f5323b5e640"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bc780e16879000f77a1022163c052f5323b5e640", "type": "zip", "shasum": "", "reference": "bc780e16879000f77a1022163c052f5323b5e640"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.39"}, "time": "2024-04-23T11:57:27+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e78db7f5c70a21f0417a31f414c4a95fe76c07e4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e78db7f5c70a21f0417a31f414c4a95fe76c07e4", "type": "zip", "shasum": "", "reference": "e78db7f5c70a21f0417a31f414c4a95fe76c07e4"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.35"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v5.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f387675d7f5fc4231f7554baa70681f222f73563"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f387675d7f5fc4231f7554baa70681f222f73563", "type": "zip", "shasum": "", "reference": "f387675d7f5fc4231f7554baa70681f222f73563"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.31"}, "time": "2023-11-03T14:41:28+00:00"}, {"version": "v5.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c6980e82a6656f6ebfabfd82f7585794cb122554"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c6980e82a6656f6ebfabfd82f7585794cb122554", "type": "zip", "shasum": "", "reference": "c6980e82a6656f6ebfabfd82f7585794cb122554"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.30"}, "time": "2023-10-27T18:36:14+00:00"}, {"version": "v5.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4cd2e3ea301aadd76a4172756296fe552fb45b0b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4cd2e3ea301aadd76a4172756296fe552fb45b0b", "type": "zip", "shasum": "", "reference": "4cd2e3ea301aadd76a4172756296fe552fb45b0b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.23"}, "time": "2023-04-23T19:33:36+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3713e20d93e46e681e51605d213027e48dab3469"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3713e20d93e46e681e51605d213027e48dab3469", "type": "zip", "shasum": "", "reference": "3713e20d93e46e681e51605d213027e48dab3469"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.21"}, "time": "2023-02-21T19:46:44+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "71c05db20cb9b54d381a28255f17580e2b7e36a5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/71c05db20cb9b54d381a28255f17580e2b7e36a5", "type": "zip", "shasum": "", "reference": "71c05db20cb9b54d381a28255f17580e2b7e36a5"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.19"}, "time": "2023-01-10T18:51:14+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "edcdc11498108f8967fe95118a7ec8624b94760e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/edcdc11498108f8967fe95118a7ec8624b94760e", "type": "zip", "shasum": "", "reference": "edcdc11498108f8967fe95118a7ec8624b94760e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.17"}, "time": "2022-12-13T09:57:04+00:00"}, {"version": "v5.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ebd37c71f62d5ec5f6e27de3e06fee492d4c6298"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ebd37c71f62d5ec5f6e27de3e06fee492d4c6298", "type": "zip", "shasum": "", "reference": "ebd37c71f62d5ec5f6e27de3e06fee492d4c6298"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.16"}, "time": "2022-11-25T16:04:03+00:00"}, {"version": "v5.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e83fe9a72011f07c662da46a05603d66deeeb487"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e83fe9a72011f07c662da46a05603d66deeeb487", "type": "zip", "shasum": "", "reference": "e83fe9a72011f07c662da46a05603d66deeeb487"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.14"}, "time": "2022-10-03T15:15:50+00:00"}, {"version": "v5.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c", "type": "zip", "shasum": "", "reference": "7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.12"}, "time": "2022-08-02T15:52:22+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "05d4ea560f3402c6c116afd99fdc66e60eda227e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/05d4ea560f3402c6c116afd99fdc66e60eda227e", "type": "zip", "shasum": "", "reference": "05d4ea560f3402c6c116afd99fdc66e60eda227e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.11"}, "time": "2022-06-27T16:58:25+00:00"}, {"version": "v5.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "04e42926429d9e8b39c174387ab990bf7817f7a2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/04e42926429d9e8b39c174387ab990bf7817f7a2", "type": "zip", "shasum": "", "reference": "04e42926429d9e8b39c174387ab990bf7817f7a2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.10"}, "time": "2022-06-20T11:50:59+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e80f87d2c9495966768310fc531b487ce64237a2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e80f87d2c9495966768310fc531b487ce64237a2", "type": "zip", "shasum": "", "reference": "e80f87d2c9495966768310fc531b487ce64237a2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.3"}, "time": "2022-01-26T16:32:32+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b9eb163846a61bb32dfc147f7859e274fab38b58"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b9eb163846a61bb32dfc147f7859e274fab38b58", "type": "zip", "shasum": "", "reference": "b9eb163846a61bb32dfc147f7859e274fab38b58"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.2"}, "time": "2021-12-16T21:58:21+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "034ccc0994f1ae3f7499fa5b1f2e75d5e7a94efc"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/034ccc0994f1ae3f7499fa5b1f2e75d5e7a94efc", "type": "zip", "shasum": "", "reference": "034ccc0994f1ae3f7499fa5b1f2e75d5e7a94efc"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.0"}, "time": "2021-11-28T15:25:38+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4fc8ecf03e09759b82cc1dd3e8b1e1b1161b5e55"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4fc8ecf03e09759b82cc1dd3e8b1e1b1161b5e55", "type": "zip", "shasum": "", "reference": "4fc8ecf03e09759b82cc1dd3e8b1e1b1161b5e55"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.0-RC1"}, "time": "2021-11-23T10:19:22+00:00"}, {"version": "v5.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9e6346bac991cedce35aff378994d6ac19291dae"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9e6346bac991cedce35aff378994d6ac19291dae", "type": "zip", "shasum": "", "reference": "9e6346bac991cedce35aff378994d6ac19291dae"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.0-BETA2"}, "time": "2021-11-14T16:37:37+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "^1.8"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c55bc64d9fc7102da2254473a55fa100ddbb746b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c55bc64d9fc7102da2254473a55fa100ddbb746b", "type": "zip", "shasum": "", "reference": "c55bc64d9fc7102da2254473a55fa100ddbb746b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.0-BETA1"}, "time": "2021-10-26T08:20:39+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c441e9d2e340642ac8b951b753dea962d55b669d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c441e9d2e340642ac8b951b753dea962d55b669d", "type": "zip", "shasum": "", "reference": "c441e9d2e340642ac8b951b753dea962d55b669d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.14"}, "time": "2022-01-26T16:05:39+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8"}, "require-dev": {"symfony/console": "^4.4|^5.0"}, "conflict": {"symfony/console": "<4.4"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dc07a318dae6e1af2be51d85f161263e0b6ca03d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dc07a318dae6e1af2be51d85f161263e0b6ca03d", "type": "zip", "shasum": "", "reference": "dc07a318dae6e1af2be51d85f161263e0b6ca03d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.13"}, "time": "2021-11-28T15:23:54+00:00"}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "226638aa877bc4104e619a15f27d8141cd6b4e4a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/226638aa877bc4104e619a15f27d8141cd6b4e4a", "type": "zip", "shasum": "", "reference": "226638aa877bc4104e619a15f27d8141cd6b4e4a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.11"}, "time": "2021-11-20T16:42:42+00:00"}, {"version": "v5.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4500fe63dc9c6ffc32d3b1cb0448c329f9c814b7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4500fe63dc9c6ffc32d3b1cb0448c329f9c814b7", "type": "zip", "shasum": "", "reference": "4500fe63dc9c6ffc32d3b1cb0448c329f9c814b7"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.6"}, "time": "2021-07-29T06:20:01+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "90909bd7352ae57411a93fcd67b09e6199340547"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/90909bd7352ae57411a93fcd67b09e6199340547", "type": "zip", "shasum": "", "reference": "90909bd7352ae57411a93fcd67b09e6199340547"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.4"}, "time": "2021-07-21T12:40:44+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "485c83a2fb5893e2ff21bf4bfc7fdf48b4967229"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/485c83a2fb5893e2ff21bf4bfc7fdf48b4967229", "type": "zip", "shasum": "", "reference": "485c83a2fb5893e2ff21bf4bfc7fdf48b4967229"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.3"}, "time": "2021-06-24T08:13:00+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "71719ab2409401711d619765aa255f9d352a59b2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/71719ab2409401711d619765aa255f9d352a59b2", "type": "zip", "shasum": "", "reference": "71719ab2409401711d619765aa255f9d352a59b2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.2"}, "time": "2021-06-06T09:51:56+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3bbcf262fceb3d8f48175302e6ba0ac96e3a5a11"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3bbcf262fceb3d8f48175302e6ba0ac96e3a5a11", "type": "zip", "shasum": "", "reference": "3bbcf262fceb3d8f48175302e6ba0ac96e3a5a11"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a1385d6211e1e687b4ee993ca425350d81a14c0b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a1385d6211e1e687b4ee993ca425350d81a14c0b", "type": "zip", "shasum": "", "reference": "a1385d6211e1e687b4ee993ca425350d81a14c0b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.0-RC1"}, "time": "2021-05-16T13:08:56+00:00"}, {"version": "v5.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "505f470743c125b7c3f1662f0e726c4f77ec4d85"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/505f470743c125b7c3f1662f0e726c4f77ec4d85", "type": "zip", "shasum": "", "reference": "505f470743c125b7c3f1662f0e726c4f77ec4d85"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.0-BETA2"}, "time": "2021-05-01T00:53:07+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8c98ad27ef5e3136237405b7653fa41498849b6f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8c98ad27ef5e3136237405b7653fa41498849b6f", "type": "zip", "shasum": "", "reference": "8c98ad27ef5e3136237405b7653fa41498849b6f"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.3.0-BETA1"}, "time": "2021-04-11T23:07:08+00:00"}, {"version": "v5.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ffe9c92e1a6c77c3ad5fc3a2ac16f0b8549dae10"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ffe9c92e1a6c77c3ad5fc3a2ac16f0b8549dae10", "type": "zip", "shasum": "", "reference": "ffe9c92e1a6c77c3ad5fc3a2ac16f0b8549dae10"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.14"}, "time": "2021-07-29T06:18:06+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "46c4a1735d1bd6fa9876a9d8014acbe410c5c2f9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/46c4a1735d1bd6fa9876a9d8014acbe410c5c2f9", "type": "zip", "shasum": "", "reference": "46c4a1735d1bd6fa9876a9d8014acbe410c5c2f9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.12"}, "time": "2021-07-21T12:38:00+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9f8fa61787135607fcebf2e4bee50f3b713f0885"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9f8fa61787135607fcebf2e4bee50f3b713f0885", "type": "zip", "shasum": "", "reference": "9f8fa61787135607fcebf2e4bee50f3b713f0885"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.11"}, "time": "2021-06-24T08:07:28+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "cd7930d6a7e0d8ceac299846235bc6e2e032c3a3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/cd7930d6a7e0d8ceac299846235bc6e2e032c3a3", "type": "zip", "shasum": "", "reference": "cd7930d6a7e0d8ceac299846235bc6e2e032c3a3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.10"}, "time": "2021-05-26T17:40:38+00:00"}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d23115e4a3d50520abddccdbec9514baab1084c8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d23115e4a3d50520abddccdbec9514baab1084c8", "type": "zip", "shasum": "", "reference": "d23115e4a3d50520abddccdbec9514baab1084c8"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.9"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "76546cbeddd0a9540b4e4e57eddeec3e9bb444a5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/76546cbeddd0a9540b4e4e57eddeec3e9bb444a5", "type": "zip", "shasum": "", "reference": "76546cbeddd0a9540b4e4e57eddeec3e9bb444a5"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.7"}, "time": "2021-04-29T20:47:09+00:00"}, {"version": "v5.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "298a08ddda623485208506fcee08817807a251dd"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/298a08ddda623485208506fcee08817807a251dd", "type": "zip", "shasum": "", "reference": "298a08ddda623485208506fcee08817807a251dd"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.5"}, "time": "2021-03-06T07:59:01+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7d6ae0cce3c33965af681a4355f1c4de326ed277"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7d6ae0cce3c33965af681a4355f1c4de326ed277", "type": "zip", "shasum": "", "reference": "7d6ae0cce3c33965af681a4355f1c4de326ed277"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.4"}, "time": "2021-02-22T15:48:39+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "338cddc6d74929f6adf19ca5682ac4b8e109cdb0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/338cddc6d74929f6adf19ca5682ac4b8e109cdb0", "type": "zip", "shasum": "", "reference": "338cddc6d74929f6adf19ca5682ac4b8e109cdb0"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.3"}, "time": "2021-02-03T04:42:09+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6bb8b36c6dea8100268512bf46e858c8eb5c545e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6bb8b36c6dea8100268512bf46e858c8eb5c545e", "type": "zip", "shasum": "", "reference": "6bb8b36c6dea8100268512bf46e858c8eb5c545e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.2"}, "time": "2021-01-27T10:01:46+00:00"}, {"description": "Symfony Yaml Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "290ea5e03b8cf9b42c783163123f54441fb06939"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/290ea5e03b8cf9b42c783163123f54441fb06939", "type": "zip", "shasum": "", "reference": "290ea5e03b8cf9b42c783163123f54441fb06939"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.1"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bb73619b2ae5121bbbcd9f191dfd53ded17ae598"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bb73619b2ae5121bbbcd9f191dfd53ded17ae598", "type": "zip", "shasum": "", "reference": "bb73619b2ae5121bbbcd9f191dfd53ded17ae598"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.0"}, "time": "2020-11-28T10:57:20+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "34b513f1bfb3677fc40aac34f8e4397eba4066ed"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/34b513f1bfb3677fc40aac34f8e4397eba4066ed", "type": "zip", "shasum": "", "reference": "34b513f1bfb3677fc40aac34f8e4397eba4066ed"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.0-RC2"}, "time": "2020-11-15T22:55:04+00:00"}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f284e032c3cefefb9943792132251b79a6127ca6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f284e032c3cefefb9943792132251b79a6127ca6", "type": "zip", "shasum": "", "reference": "f284e032c3cefefb9943792132251b79a6127ca6"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.0-RC1"}, "time": "2020-10-24T12:03:25+00:00"}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.0-BETA3"}}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "28ab1c87ffd653dc77e84581fed890c65dcafc19"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/28ab1c87ffd653dc77e84581fed890c65dcafc19", "type": "zip", "shasum": "", "reference": "28ab1c87ffd653dc77e84581fed890c65dcafc19"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.0-BETA2"}, "time": "2020-10-14T17:08:19+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "230b68b6601fb71aa3df000f7eee3ac8859672ef"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/230b68b6601fb71aa3df000f7eee3ac8859672ef", "type": "zip", "shasum": "", "reference": "230b68b6601fb71aa3df000f7eee3ac8859672ef"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.0-BETA1"}, "time": "2020-09-27T03:44:38+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Loads and dumps YAML files", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6bb8b36c6dea8100268512bf46e858c8eb5c545e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6bb8b36c6dea8100268512bf46e858c8eb5c545e", "type": "zip", "shasum": "", "reference": "6bb8b36c6dea8100268512bf46e858c8eb5c545e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "extra": "__unset"}, {"description": "Symfony Yaml Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "290ea5e03b8cf9b42c783163123f54441fb06939"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/290ea5e03b8cf9b42c783163123f54441fb06939", "type": "zip", "shasum": "", "reference": "290ea5e03b8cf9b42c783163123f54441fb06939"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.10"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bb73619b2ae5121bbbcd9f191dfd53ded17ae598"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bb73619b2ae5121bbbcd9f191dfd53ded17ae598", "type": "zip", "shasum": "", "reference": "bb73619b2ae5121bbbcd9f191dfd53ded17ae598"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.9"}, "time": "2020-11-28T10:57:20+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f284e032c3cefefb9943792132251b79a6127ca6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f284e032c3cefefb9943792132251b79a6127ca6", "type": "zip", "shasum": "", "reference": "f284e032c3cefefb9943792132251b79a6127ca6"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.8"}, "time": "2020-10-24T12:03:25+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e147a68cb66a8b510f4b7481fe4da5b2ab65ec6a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e147a68cb66a8b510f4b7481fe4da5b2ab65ec6a", "type": "zip", "shasum": "", "reference": "e147a68cb66a8b510f4b7481fe4da5b2ab65ec6a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.7"}, "time": "2020-09-27T03:44:28+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.6"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a44bd3a91bfbf8db12367fa6ffac9c3eb1a8804a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a44bd3a91bfbf8db12367fa6ffac9c3eb1a8804a", "type": "zip", "shasum": "", "reference": "a44bd3a91bfbf8db12367fa6ffac9c3eb1a8804a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.4"}, "time": "2020-08-26T08:30:57+00:00"}, {"version": "v5.1.4", "version_normalized": "*******"}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ea342353a3ef4f453809acc4ebc55382231d4d23"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ea342353a3ef4f453809acc4ebc55382231d4d23", "type": "zip", "shasum": "", "reference": "ea342353a3ef4f453809acc4ebc55382231d4d23"}, "support": {"source": "https://github.com/symfony/yaml/tree/5.1"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.1"}}, {"version": "v5.1.1", "version_normalized": "*******"}, {"version": "v5.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/5.1"}}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5a87b78d9a3f9b66628dbf4ea7fff500793d655b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5a87b78d9a3f9b66628dbf4ea7fff500793d655b", "type": "zip", "shasum": "", "reference": "5a87b78d9a3f9b66628dbf4ea7fff500793d655b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.1.0-RC1"}, "time": "2020-05-16T09:12:54+00:00", "require": {"php": "^7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ef63108eda3773c117135abb17312c4338082d08"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ef63108eda3773c117135abb17312c4338082d08", "type": "zip", "shasum": "", "reference": "ef63108eda3773c117135abb17312c4338082d08"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2020-05-05T07:46:02+00:00"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "29b60e88ff11a45b708115004fdeacab1ee3dd5d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/29b60e88ff11a45b708115004fdeacab1ee3dd5d", "type": "zip", "shasum": "", "reference": "29b60e88ff11a45b708115004fdeacab1ee3dd5d"}, "support": {"source": "https://github.com/symfony/yaml/tree/5.0"}, "time": "2020-05-20T17:38:26+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8"}, "bin": "__unset"}, {"version": "v5.0.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v5.0.10"}}, {"version": "v5.0.9", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/5.0"}}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "482fb4e710e5af3e0e78015f19aa716ad953392f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/482fb4e710e5af3e0e78015f19aa716ad953392f", "type": "zip", "shasum": "", "reference": "482fb4e710e5af3e0e78015f19aa716ad953392f"}, "time": "2020-04-28T17:58:55+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ad5e9c83ade5bbb3a96a3f30588a0622708caefd"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ad5e9c83ade5bbb3a96a3f30588a0622708caefd", "type": "zip", "shasum": "", "reference": "ad5e9c83ade5bbb3a96a3f30588a0622708caefd"}, "time": "2020-03-30T11:42:42+00:00"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d45d73e4e079efdb6ec9990752c44c30ca0d6347"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d45d73e4e079efdb6ec9990752c44c30ca0d6347", "type": "zip", "shasum": "", "reference": "d45d73e4e079efdb6ec9990752c44c30ca0d6347"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.0.6"}, "time": "2020-03-16T12:10:54+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a4b613d7e44f62941adff5a802cff70adee57d3f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a4b613d7e44f62941adff5a802cff70adee57d3f", "type": "zip", "shasum": "", "reference": "a4b613d7e44f62941adff5a802cff70adee57d3f"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.0.5"}, "time": "2020-02-03T13:51:17+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "69b44e3b8f90949aee2eb3aa9b86ceeb01cbf62a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/69b44e3b8f90949aee2eb3aa9b86ceeb01cbf62a", "type": "zip", "shasum": "", "reference": "69b44e3b8f90949aee2eb3aa9b86ceeb01cbf62a"}, "support": {"source": "https://github.com/symfony/yaml/tree/5.0"}, "time": "2020-01-21T11:12:28+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******"}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "847661e77afa48d99ecfa508e8b60f0b029a19c0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/847661e77afa48d99ecfa508e8b60f0b029a19c0", "type": "zip", "shasum": "", "reference": "847661e77afa48d99ecfa508e8b60f0b029a19c0"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.0.2"}, "time": "2019-12-10T11:06:55+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "51b684480184fa767b97e28eaca67664e48dd3e9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/51b684480184fa767b97e28eaca67664e48dd3e9", "type": "zip", "shasum": "", "reference": "51b684480184fa767b97e28eaca67664e48dd3e9"}, "support": {"source": "https://github.com/symfony/yaml/tree/5.0"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v5.0.0"}}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2b50f047909601cd3cdeb4e3623c6d1d02366fcf"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2b50f047909601cd3cdeb4e3623c6d1d02366fcf", "type": "zip", "shasum": "", "reference": "2b50f047909601cd3cdeb4e3623c6d1d02366fcf"}, "support": {"source": "https://github.com/symfony/yaml/tree/v5.0.0-RC1"}, "time": "2019-11-12T14:58:10+00:00", "require": {"php": "^7.2.9", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/yaml/tree/master"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1"}, {"description": "Loads and dumps YAML files", "version": "v4.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "type": "zip", "shasum": "", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-02T15:47:23+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "conflict": {"symfony/console": "<3.4"}, "extra": "__unset"}, {"version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c2b28c10fb3b7ac67bafa7b8f952cd83f35acde2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c2b28c10fb3b7ac67bafa7b8f952cd83f35acde2", "type": "zip", "shasum": "", "reference": "c2b28c10fb3b7ac67bafa7b8f952cd83f35acde2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.44"}, "time": "2022-06-27T13:16:42+00:00"}, {"version": "v4.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "07e392f0ef78376d080d5353c081a5e5704835bd"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/07e392f0ef78376d080d5353c081a5e5704835bd", "type": "zip", "shasum": "", "reference": "07e392f0ef78376d080d5353c081a5e5704835bd"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.43"}, "time": "2022-06-20T08:31:17+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d7f637cc0f0cc14beb0984f2bb50da560b271311"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d7f637cc0f0cc14beb0984f2bb50da560b271311", "type": "zip", "shasum": "", "reference": "d7f637cc0f0cc14beb0984f2bb50da560b271311"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.37"}, "time": "2022-01-24T20:11:01+00:00"}, {"version": "v4.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a19f7c44ba665fa9d9d415cc4493361381b93f9b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a19f7c44ba665fa9d9d415cc4493361381b93f9b", "type": "zip", "shasum": "", "reference": "a19f7c44ba665fa9d9d415cc4493361381b93f9b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.36"}, "time": "2021-11-25T16:40:00+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2c309e258adeb9970229042be39b360d34986fad"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2c309e258adeb9970229042be39b360d34986fad", "type": "zip", "shasum": "", "reference": "2c309e258adeb9970229042be39b360d34986fad"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.34"}, "time": "2021-11-18T18:49:23+00:00"}, {"version": "v4.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3abcc4db06d4e776825eaa3ed8ad924d5bc7432a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3abcc4db06d4e776825eaa3ed8ad924d5bc7432a", "type": "zip", "shasum": "", "reference": "3abcc4db06d4e776825eaa3ed8ad924d5bc7432a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.29"}, "time": "2021-07-27T16:19:30+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2b5f2ae892b489e51d81616fbc475c8238b533dc"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2b5f2ae892b489e51d81616fbc475c8238b533dc", "type": "zip", "shasum": "", "reference": "2b5f2ae892b489e51d81616fbc475c8238b533dc"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.27"}, "time": "2021-07-21T12:19:41+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}}, {"version": "v4.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e096ef4b4c4c9a2f72c2ac660f54352cd31c60f8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e096ef4b4c4c9a2f72c2ac660f54352cd31c60f8", "type": "zip", "shasum": "", "reference": "e096ef4b4c4c9a2f72c2ac660f54352cd31c60f8"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.26"}, "time": "2021-06-23T19:06:53+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc", "type": "zip", "shasum": "", "reference": "81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.25"}, "time": "2021-05-26T17:39:37+00:00"}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8b6d1b97521e2f125039b3fcb4747584c6dfa0ef"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8b6d1b97521e2f125039b3fcb4747584c6dfa0ef", "type": "zip", "shasum": "", "reference": "8b6d1b97521e2f125039b3fcb4747584c6dfa0ef"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.24"}, "time": "2021-05-16T09:52:47+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1c2fd24147961525eaefb65b11987cab75adab59"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1c2fd24147961525eaefb65b11987cab75adab59", "type": "zip", "shasum": "", "reference": "1c2fd24147961525eaefb65b11987cab75adab59"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.22"}, "time": "2021-04-23T12:09:37+00:00"}, {"version": "v4.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3871c720871029f008928244e56cf43497da7e9d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3871c720871029f008928244e56cf43497da7e9d", "type": "zip", "shasum": "", "reference": "3871c720871029f008928244e56cf43497da7e9d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.21"}, "time": "2021-03-05T17:58:50+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "29e61305e1c79d25f71060903982ead8f533e267"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/29e61305e1c79d25f71060903982ead8f533e267", "type": "zip", "shasum": "", "reference": "29e61305e1c79d25f71060903982ead8f533e267"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.20"}, "time": "2021-02-22T15:36:50+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "17ed9f14c1aa05b1a5cf2e2c5ef2d0be28058ef9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/17ed9f14c1aa05b1a5cf2e2c5ef2d0be28058ef9", "type": "zip", "shasum": "", "reference": "17ed9f14c1aa05b1a5cf2e2c5ef2d0be28058ef9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00"}, {"description": "Symfony Yaml Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bbce94f14d73732340740366fcbe63363663a403"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bbce94f14d73732340740366fcbe63363663a403", "type": "zip", "shasum": "", "reference": "bbce94f14d73732340740366fcbe63363663a403"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.18"}, "time": "2020-12-08T16:59:59+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7531361cf38e4816821b4a12a42542b3c6143ad1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7531361cf38e4816821b4a12a42542b3c6143ad1", "type": "zip", "shasum": "", "reference": "7531361cf38e4816821b4a12a42542b3c6143ad1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.17"}, "time": "2020-11-24T12:28:30+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "543cb4dbd45ed803f08a9a65f27fb149b5dd20c2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/543cb4dbd45ed803f08a9a65f27fb149b5dd20c2", "type": "zip", "shasum": "", "reference": "543cb4dbd45ed803f08a9a65f27fb149b5dd20c2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c7885964b1eceb70b0981556d0a9b01d2d97c8d1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c7885964b1eceb70b0981556d0a9b01d2d97c8d1", "type": "zip", "shasum": "", "reference": "c7885964b1eceb70b0981556d0a9b01d2d97c8d1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.15"}, "time": "2020-09-27T03:36:23+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.14"}}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e2a69525b11a33be51cb00b8d6d13a9258a296b1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e2a69525b11a33be51cb00b8d6d13a9258a296b1", "type": "zip", "shasum": "", "reference": "e2a69525b11a33be51cb00b8d6d13a9258a296b1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.13"}, "time": "2020-08-26T08:30:46+00:00"}, {"version": "v4.4.12", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/4.4"}}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c2d2cc66e892322cfcc03f8f12f8340dbd7a3f8a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c2d2cc66e892322cfcc03f8f12f8340dbd7a3f8a", "type": "zip", "shasum": "", "reference": "c2d2cc66e892322cfcc03f8f12f8340dbd7a3f8a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.11"}, "time": "2020-05-20T08:37:50+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/4.4"}}, {"version": "v4.4.9", "version_normalized": "*******"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b385dce1c0e9f839b384af90188638819433e252"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b385dce1c0e9f839b384af90188638819433e252", "type": "zip", "shasum": "", "reference": "b385dce1c0e9f839b384af90188638819433e252"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.8"}, "time": "2020-04-28T17:55:16+00:00", "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ef166890d821518106da3560086bfcbeb4fadfec"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ef166890d821518106da3560086bfcbeb4fadfec", "type": "zip", "shasum": "", "reference": "ef166890d821518106da3560086bfcbeb4fadfec"}, "support": {"source": "https://github.com/symfony/yaml/tree/4.4"}, "time": "2020-03-30T11:41:10+00:00"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "43d7a46b1f80b4fd2ecfac4a9a4cc1f22d029fbb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/43d7a46b1f80b4fd2ecfac4a9a4cc1f22d029fbb", "type": "zip", "shasum": "", "reference": "43d7a46b1f80b4fd2ecfac4a9a4cc1f22d029fbb"}, "time": "2020-03-16T08:56:54+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "94d005c176db2080e98825d98e01e8b311a97a88"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/94d005c176db2080e98825d98e01e8b311a97a88", "type": "zip", "shasum": "", "reference": "94d005c176db2080e98825d98e01e8b311a97a88"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.5"}, "time": "2020-02-03T10:46:43+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "cd014e425b3668220adb865f53bff64b3ad21767"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/cd014e425b3668220adb865f53bff64b3ad21767", "type": "zip", "shasum": "", "reference": "cd014e425b3668220adb865f53bff64b3ad21767"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.3"}, "time": "2020-01-21T11:12:16+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a08832b974dd5fafe3085a66d41fe4c84bb2628c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a08832b974dd5fafe3085a66d41fe4c84bb2628c", "type": "zip", "shasum": "", "reference": "a08832b974dd5fafe3085a66d41fe4c84bb2628c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.2"}, "time": "2019-12-10T10:33:21+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "76de473358fe802578a415d5bb43c296cf09d211"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/76de473358fe802578a415d5bb43c296cf09d211", "type": "zip", "shasum": "", "reference": "76de473358fe802578a415d5bb43c296cf09d211"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.0"}, "time": "2019-11-12T14:51:11+00:00"}, {"version": "v4.4.0", "version_normalized": "*******"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/4.4"}}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8e0a95493b734ca8195acf3e1f3d89e88b957db5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8e0a95493b734ca8195acf3e1f3d89e88b957db5", "type": "zip", "shasum": "", "reference": "8e0a95493b734ca8195acf3e1f3d89e88b957db5"}, "support": {"source": "https://github.com/symfony/yaml/tree/4.3"}, "time": "2020-01-21T11:09:03+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require-dev": {"symfony/console": "~3.4|~4.0"}}, {"version": "v4.3.10", "version_normalized": "********"}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "324cf4b19c345465fad14f3602050519e09e361d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/324cf4b19c345465fad14f3602050519e09e361d", "type": "zip", "shasum": "", "reference": "324cf4b19c345465fad14f3602050519e09e361d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.3.6"}, "time": "2019-10-30T12:58:49+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v4.3.7"}}, {"version": "v4.3.7", "version_normalized": "*******"}, {"version": "v4.3.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v4.3.6"}}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "41e16350a2a1c7383c4735aa2f9fce74cf3d1178"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/41e16350a2a1c7383c4735aa2f9fce74cf3d1178", "type": "zip", "shasum": "", "reference": "41e16350a2a1c7383c4735aa2f9fce74cf3d1178"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.3.5"}, "time": "2019-09-11T15:41:19+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5a0b7c32dc3ec56fd4abae8a4a71b0cf05013686"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5a0b7c32dc3ec56fd4abae8a4a71b0cf05013686", "type": "zip", "shasum": "", "reference": "5a0b7c32dc3ec56fd4abae8a4a71b0cf05013686"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.3.4"}, "time": "2019-08-20T14:27:59+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "34d29c2acd1ad65688f58452fd48a46bd996d5a6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/34d29c2acd1ad65688f58452fd48a46bd996d5a6", "type": "zip", "shasum": "", "reference": "34d29c2acd1ad65688f58452fd48a46bd996d5a6"}, "support": {"source": "https://github.com/symfony/yaml/tree/4.3"}, "time": "2019-07-24T14:47:54+00:00"}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c60ecf5ba842324433b46f58dc7afc4487dbab99"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c60ecf5ba842324433b46f58dc7afc4487dbab99", "type": "zip", "shasum": "", "reference": "c60ecf5ba842324433b46f58dc7afc4487dbab99"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2019-04-06T14:04:46+00:00"}, {"version": "v4.3.1", "version_normalized": "*******"}, {"version": "v4.3.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v4.3.0"}}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/yaml/tree/master"}}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9468fef6f1c740b96935e9578560a9e9189ca154"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9468fef6f1c740b96935e9578560a9e9189ca154", "type": "zip", "shasum": "", "reference": "9468fef6f1c740b96935e9578560a9e9189ca154"}, "support": {"source": "https://github.com/symfony/yaml/tree/4.2"}, "time": "2019-07-24T14:47:26+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}}, {"version": "v4.2.11", "version_normalized": "********"}, {"version": "v4.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6712daf03ee25b53abb14e7e8e0ede1a770efdb1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6712daf03ee25b53abb14e7e8e0ede1a770efdb1", "type": "zip", "shasum": "", "reference": "6712daf03ee25b53abb14e7e8e0ede1a770efdb1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.2.5"}, "time": "2019-03-30T15:58:42+00:00"}, {"version": "v4.2.9", "version_normalized": "*******"}, {"version": "v4.2.8", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/4.2"}}, {"version": "v4.2.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/yaml/tree/v4.2.5"}}, {"version": "v4.2.6", "version_normalized": "*******"}, {"version": "v4.2.5", "version_normalized": "*******"}, {"version": "v4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "761fa560a937fd7686e5274ff89dcfa87a5047df"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/761fa560a937fd7686e5274ff89dcfa87a5047df", "type": "zip", "shasum": "", "reference": "761fa560a937fd7686e5274ff89dcfa87a5047df"}, "support": {"source": "https://github.com/symfony/yaml/tree/4.2"}, "time": "2019-02-23T15:17:42+00:00"}, {"version": "v4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d461670ee145092b7e2a56c1da7118f19cadadb0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d461670ee145092b7e2a56c1da7118f19cadadb0", "type": "zip", "shasum": "", "reference": "d461670ee145092b7e2a56c1da7118f19cadadb0"}, "time": "2019-01-16T20:35:37+00:00"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d0aa6c0ea484087927b49fd513383a7d36190ca6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d0aa6c0ea484087927b49fd513383a7d36190ca6", "type": "zip", "shasum": "", "reference": "d0aa6c0ea484087927b49fd513383a7d36190ca6"}, "time": "2019-01-03T09:07:35+00:00"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c41175c801e3edfda90f32e292619d10c27103d7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c41175c801e3edfda90f32e292619d10c27103d7", "type": "zip", "shasum": "", "reference": "c41175c801e3edfda90f32e292619d10c27103d7"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2018-11-11T19:52:12+00:00"}, {"version": "v4.2.0", "version_normalized": "*******"}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7228a3e2163ab5623231f126cf8d7701072f9d16"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7228a3e2163ab5623231f126cf8d7701072f9d16", "type": "zip", "shasum": "", "reference": "7228a3e2163ab5623231f126cf8d7701072f9d16"}, "time": "2018-10-02T16:38:08+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "874d9210fe0ad4f6326a45d163ad815a71ad8b38"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/874d9210fe0ad4f6326a45d163ad815a71ad8b38", "type": "zip", "shasum": "", "reference": "874d9210fe0ad4f6326a45d163ad815a71ad8b38"}, "support": {"source": "https://github.com/symfony/yaml/tree/v4.1.12"}, "time": "2019-01-16T19:07:26+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dd31d71146af4e346e710e30b85f4abe3ab6f6a5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dd31d71146af4e346e710e30b85f4abe3ab6f6a5", "type": "zip", "shasum": "", "reference": "dd31d71146af4e346e710e30b85f4abe3ab6f6a5"}, "time": "2019-01-03T09:05:57+00:00"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "fe87e3b24d15ec8948f0280ee867a65ca44fdbaa"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/fe87e3b24d15ec8948f0280ee867a65ca44fdbaa", "type": "zip", "shasum": "", "reference": "fe87e3b24d15ec8948f0280ee867a65ca44fdbaa"}, "time": "2018-11-11T19:51:29+00:00"}, {"version": "v4.1.8", "version_normalized": "*******"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "367e689b2fdc19965be435337b50bc8adf2746c9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/367e689b2fdc19965be435337b50bc8adf2746c9", "type": "zip", "shasum": "", "reference": "367e689b2fdc19965be435337b50bc8adf2746c9"}, "time": "2018-10-02T16:36:10+00:00"}, {"version": "v4.1.6", "version_normalized": "*******"}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ac5af7c14c4f8abf0f77419e8397eff7a370df19"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ac5af7c14c4f8abf0f77419e8397eff7a370df19", "type": "zip", "shasum": "", "reference": "ac5af7c14c4f8abf0f77419e8397eff7a370df19"}, "time": "2018-09-30T03:38:13+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b832cc289608b6d305f62149df91529a2ab3c314"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b832cc289608b6d305f62149df91529a2ab3c314", "type": "zip", "shasum": "", "reference": "b832cc289608b6d305f62149df91529a2ab3c314"}, "time": "2018-08-18T16:52:46+00:00"}, {"version": "v4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "46bc69aa91fc4ab78a96ce67873a6b0c148fd48c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/46bc69aa91fc4ab78a96ce67873a6b0c148fd48c", "type": "zip", "shasum": "", "reference": "46bc69aa91fc4ab78a96ce67873a6b0c148fd48c"}, "time": "2018-07-26T11:24:31+00:00"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "80e4bfa9685fc4a09acc4a857ec16974a9cd944e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/80e4bfa9685fc4a09acc4a857ec16974a9cd944e", "type": "zip", "shasum": "", "reference": "80e4bfa9685fc4a09acc4a857ec16974a9cd944e"}, "time": "2018-05-30T07:26:09+00:00"}, {"version": "v4.1.1", "version_normalized": "*******"}, {"version": "v4.1.0", "version_normalized": "*******"}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0659e9c73eb1cb99e40df23925339c15787fd117"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0659e9c73eb1cb99e40df23925339c15787fd117", "type": "zip", "shasum": "", "reference": "0659e9c73eb1cb99e40df23925339c15787fd117"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2018-05-07T07:14:12+00:00"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "05f66d707cfa60d553e419ec038d5474c2d5d1f2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/05f66d707cfa60d553e419ec038d5474c2d5d1f2", "type": "zip", "shasum": "", "reference": "05f66d707cfa60d553e419ec038d5474c2d5d1f2"}, "support": {"source": "https://github.com/symfony/yaml/tree/4.0"}, "time": "2018-07-26T11:22:46+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "048b1be5fb96e73ff1d065f5e7e23f84415ac907"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/048b1be5fb96e73ff1d065f5e7e23f84415ac907", "type": "zip", "shasum": "", "reference": "048b1be5fb96e73ff1d065f5e7e23f84415ac907"}, "time": "2018-05-07T07:12:24+00:00"}, {"version": "v4.0.12", "version_normalized": "********"}, {"version": "v4.0.11", "version_normalized": "********"}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "275ad099e4cbe612a2acbca14a16dd1c5311324d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/275ad099e4cbe612a2acbca14a16dd1c5311324d", "type": "zip", "shasum": "", "reference": "275ad099e4cbe612a2acbca14a16dd1c5311324d"}, "time": "2018-04-08T08:49:08+00:00", "require": {"php": "^7.1.3"}}, {"version": "v4.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8b34ebb5989df61cbd77eff29a02c4db9ac1069c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8b34ebb5989df61cbd77eff29a02c4db9ac1069c", "type": "zip", "shasum": "", "reference": "8b34ebb5989df61cbd77eff29a02c4db9ac1069c"}, "time": "2018-04-03T05:24:00+00:00"}, {"version": "v4.0.7", "version_normalized": "*******"}, {"version": "v4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "de5f125ea39de846b90b313b2cfb031a0152d223"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/de5f125ea39de846b90b313b2cfb031a0152d223", "type": "zip", "shasum": "", "reference": "de5f125ea39de846b90b313b2cfb031a0152d223"}, "time": "2018-02-19T20:08:53+00:00"}, {"version": "v4.0.5", "version_normalized": "*******"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ffc60bda1d4a00ec0b32eeabf39dc017bf480028"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ffc60bda1d4a00ec0b32eeabf39dc017bf480028", "type": "zip", "shasum": "", "reference": "ffc60bda1d4a00ec0b32eeabf39dc017bf480028"}, "time": "2018-01-21T19:06:11+00:00"}, {"version": "v4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b84f646b9490d2101e2c25ddeec77ceefbda2eee"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b84f646b9490d2101e2c25ddeec77ceefbda2eee", "type": "zip", "shasum": "", "reference": "b84f646b9490d2101e2c25ddeec77ceefbda2eee"}, "time": "2018-01-03T07:38:00+00:00"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a5ee52d155f06ad23b19eb63c31228ff56ad1116"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a5ee52d155f06ad23b19eb63c31228ff56ad1116", "type": "zip", "shasum": "", "reference": "a5ee52d155f06ad23b19eb63c31228ff56ad1116"}, "time": "2017-12-12T08:41:51+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "873417cb9949f07be8852d41e3be5ab6f09e1218"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/873417cb9949f07be8852d41e3be5ab6f09e1218", "type": "zip", "shasum": "", "reference": "873417cb9949f07be8852d41e3be5ab6f09e1218"}, "time": "2017-12-04T18:34:52+00:00"}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7be8741ce5dce9943f41a9269f6828b66e726776"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7be8741ce5dce9943f41a9269f6828b66e726776", "type": "zip", "shasum": "", "reference": "7be8741ce5dce9943f41a9269f6828b66e726776"}, "time": "2017-11-29T13:42:03+00:00"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "686c7fc416b454fe4019f1a225206b133ceb4fc1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/686c7fc416b454fe4019f1a225206b133ceb4fc1", "type": "zip", "shasum": "", "reference": "686c7fc416b454fe4019f1a225206b133ceb4fc1"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2017-11-10T19:37:45+00:00"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f63664afe959bb1289147112342506a8e215e271"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f63664afe959bb1289147112342506a8e215e271", "type": "zip", "shasum": "", "reference": "f63664afe959bb1289147112342506a8e215e271"}, "time": "2017-10-24T14:36:35+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "348343c4805c860250d8a50cd41b151da3b08881"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/348343c4805c860250d8a50cd41b151da3b08881", "type": "zip", "shasum": "", "reference": "348343c4805c860250d8a50cd41b151da3b08881"}, "time": "2017-10-09T14:53:01+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "88289caa3c166321883f67fe5130188ebbb47094"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/88289caa3c166321883f67fe5130188ebbb47094", "type": "zip", "shasum": "", "reference": "88289caa3c166321883f67fe5130188ebbb47094"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00", "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.46"}}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ec3c2ac4d881a4684c1f0317d2107f1a4152bad9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ec3c2ac4d881a4684c1f0317d2107f1a4152bad9", "type": "zip", "shasum": "", "reference": "ec3c2ac4d881a4684c1f0317d2107f1a4152bad9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.45"}, "time": "2020-09-18T15:58:55+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4152e36b0f305c2a57aa0233dee56ec27bca4f06"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4152e36b0f305c2a57aa0233dee56ec27bca4f06", "type": "zip", "shasum": "", "reference": "4152e36b0f305c2a57aa0233dee56ec27bca4f06"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.44"}, "time": "2020-08-26T06:32:27+00:00"}, {"version": "v3.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e7fa05917ae931332a42d65b577ece4d497aad81"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e7fa05917ae931332a42d65b577ece4d497aad81", "type": "zip", "shasum": "", "reference": "e7fa05917ae931332a42d65b577ece4d497aad81"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.43"}, "time": "2020-07-23T09:37:51+00:00"}, {"version": "v3.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7233ac2bfdde24d672f5305f2b3f6b5d741ef8eb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7233ac2bfdde24d672f5305f2b3f6b5d741ef8eb", "type": "zip", "shasum": "", "reference": "7233ac2bfdde24d672f5305f2b3f6b5d741ef8eb"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}, "time": "2020-05-11T07:51:54+00:00"}, {"version": "v3.4.41", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.41"}}, {"version": "v3.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8fef49ac1357f4e05c997a1f139467ccb186bffa"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8fef49ac1357f4e05c997a1f139467ccb186bffa", "type": "zip", "shasum": "", "reference": "8fef49ac1357f4e05c997a1f139467ccb186bffa"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.40"}, "time": "2020-04-24T10:16:04+00:00"}, {"version": "v3.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e701b47e11749970f63803879c4febb520f07b6c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e701b47e11749970f63803879c4febb520f07b6c", "type": "zip", "shasum": "", "reference": "e701b47e11749970f63803879c4febb520f07b6c"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}, "time": "2020-03-25T12:02:26+00:00"}, {"version": "v3.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bc63e15160866e8730a1f738541b194c401f72bf"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bc63e15160866e8730a1f738541b194c401f72bf", "type": "zip", "shasum": "", "reference": "bc63e15160866e8730a1f738541b194c401f72bf"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.38"}, "time": "2020-01-16T19:04:26+00:00"}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "aa46bc2233097d5212332c907f9911533acfbf80"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/aa46bc2233097d5212332c907f9911533acfbf80", "type": "zip", "shasum": "", "reference": "aa46bc2233097d5212332c907f9911533acfbf80"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}, "time": "2020-01-13T08:00:59+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dab657db15207879217fc81df4f875947bf68804"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dab657db15207879217fc81df4f875947bf68804", "type": "zip", "shasum": "", "reference": "dab657db15207879217fc81df4f875947bf68804"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.34"}, "time": "2019-10-24T15:33:53+00:00"}, {"version": "v3.4.35", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}}, {"version": "v3.4.34", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.34"}}, {"version": "v3.4.33", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}}, {"version": "v3.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "768f817446da74a776a31eea335540f9dcb53942"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/768f817446da74a776a31eea335540f9dcb53942", "type": "zip", "shasum": "", "reference": "768f817446da74a776a31eea335540f9dcb53942"}, "time": "2019-09-10T10:38:46+00:00"}, {"version": "v3.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3dc414b7db30695bae671a1d86013d03f4ae9834"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3dc414b7db30695bae671a1d86013d03f4ae9834", "type": "zip", "shasum": "", "reference": "3dc414b7db30695bae671a1d86013d03f4ae9834"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.31"}, "time": "2019-08-20T13:31:17+00:00"}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "051d045c684148060ebfc9affb7e3f5e0899d40b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/051d045c684148060ebfc9affb7e3f5e0899d40b", "type": "zip", "shasum": "", "reference": "051d045c684148060ebfc9affb7e3f5e0899d40b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.30"}, "time": "2019-07-24T13:01:31+00:00"}, {"version": "v3.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "212a27b731e5bfb735679d1ffaac82bd6a1dc996"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/212a27b731e5bfb735679d1ffaac82bd6a1dc996", "type": "zip", "shasum": "", "reference": "212a27b731e5bfb735679d1ffaac82bd6a1dc996"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}, "time": "2019-03-25T07:48:46+00:00"}, {"version": "v3.4.28", "version_normalized": "********"}, {"version": "v3.4.27", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.24"}}, {"version": "v3.4.26", "version_normalized": "********"}, {"version": "v3.4.25", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}}, {"version": "v3.4.24", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v3.4.24"}}, {"version": "v3.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "57f1ce82c997f5a8701b89ef970e36bb657fd09c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/57f1ce82c997f5a8701b89ef970e36bb657fd09c", "type": "zip", "shasum": "", "reference": "57f1ce82c997f5a8701b89ef970e36bb657fd09c"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.4"}, "time": "2019-02-23T15:06:07+00:00"}, {"version": "v3.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ba11776e9e6c15ad5759a07bffb15899bac75c2d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ba11776e9e6c15ad5759a07bffb15899bac75c2d", "type": "zip", "shasum": "", "reference": "ba11776e9e6c15ad5759a07bffb15899bac75c2d"}, "time": "2019-01-16T10:59:17+00:00"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "554a59a1ccbaac238a89b19c8e551a556fd0e2ea"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/554a59a1ccbaac238a89b19c8e551a556fd0e2ea", "type": "zip", "shasum": "", "reference": "554a59a1ccbaac238a89b19c8e551a556fd0e2ea"}, "time": "2019-01-01T13:45:19+00:00"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "291e13d808bec481eab83f301f7bff3e699ef603"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/291e13d808bec481eab83f301f7bff3e699ef603", "type": "zip", "shasum": "", "reference": "291e13d808bec481eab83f301f7bff3e699ef603"}, "time": "2018-11-11T19:48:54+00:00"}, {"version": "v3.4.19", "version_normalized": "********"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "640b6c27fed4066d64b64d5903a86043f4a4de7f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/640b6c27fed4066d64b64d5903a86043f4a4de7f", "type": "zip", "shasum": "", "reference": "640b6c27fed4066d64b64d5903a86043f4a4de7f"}, "time": "2018-10-02T16:33:53+00:00"}, {"version": "v3.4.17", "version_normalized": "********"}, {"version": "v3.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "61973ecda60e9f3561e929e19c07d4878b960fc1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/61973ecda60e9f3561e929e19c07d4878b960fc1", "type": "zip", "shasum": "", "reference": "61973ecda60e9f3561e929e19c07d4878b960fc1"}, "time": "2018-09-24T08:15:45+00:00"}, {"version": "v3.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c2f4812ead9f847cb69e90917ca7502e6892d6b8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c2f4812ead9f847cb69e90917ca7502e6892d6b8", "type": "zip", "shasum": "", "reference": "c2f4812ead9f847cb69e90917ca7502e6892d6b8"}, "time": "2018-08-10T07:34:36+00:00"}, {"version": "v3.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "810af2d35fc72b6cf5c01116806d2b65ccaaf2e2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/810af2d35fc72b6cf5c01116806d2b65ccaaf2e2", "type": "zip", "shasum": "", "reference": "810af2d35fc72b6cf5c01116806d2b65ccaaf2e2"}, "time": "2018-07-26T11:19:56+00:00"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c5010cc1692ce1fa328b1fb666961eb3d4a85bb0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c5010cc1692ce1fa328b1fb666961eb3d4a85bb0", "type": "zip", "shasum": "", "reference": "c5010cc1692ce1fa328b1fb666961eb3d4a85bb0"}, "time": "2018-05-03T23:18:14+00:00"}, {"version": "v3.4.12", "version_normalized": "********"}, {"version": "v3.4.11", "version_normalized": "********"}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "033cfa61ef06ee0847e056e530201842b6e926c3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/033cfa61ef06ee0847e056e530201842b6e926c3", "type": "zip", "shasum": "", "reference": "033cfa61ef06ee0847e056e530201842b6e926c3"}, "time": "2018-04-08T08:21:29+00:00", "require": {"php": "^5.5.9|>=7.0.8"}}, {"version": "v3.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a42f9da85c7c38d59f5e53f076fe81a091f894d0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a42f9da85c7c38d59f5e53f076fe81a091f894d0", "type": "zip", "shasum": "", "reference": "a42f9da85c7c38d59f5e53f076fe81a091f894d0"}, "time": "2018-04-03T05:14:20+00:00"}, {"version": "v3.4.7", "version_normalized": "*******"}, {"version": "v3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6af42631dcf89e9c616242c900d6c52bd53bd1bb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6af42631dcf89e9c616242c900d6c52bd53bd1bb", "type": "zip", "shasum": "", "reference": "6af42631dcf89e9c616242c900d6c52bd53bd1bb"}, "time": "2018-02-16T09:50:28+00:00"}, {"version": "v3.4.5", "version_normalized": "*******"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "eab73b6c21d27ae4cd037c417618dfd4befb0bfe"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/eab73b6c21d27ae4cd037c417618dfd4befb0bfe", "type": "zip", "shasum": "", "reference": "eab73b6c21d27ae4cd037c417618dfd4befb0bfe"}, "time": "2018-01-21T19:05:02+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "25c192f25721a74084272671f658797d9e0e0146"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/25c192f25721a74084272671f658797d9e0e0146", "type": "zip", "shasum": "", "reference": "25c192f25721a74084272671f658797d9e0e0146"}, "time": "2018-01-03T07:37:34+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "afe0cd38486505c9703707707d91450cfc1bd536"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/afe0cd38486505c9703707707d91450cfc1bd536", "type": "zip", "shasum": "", "reference": "afe0cd38486505c9703707707d91450cfc1bd536"}, "time": "2017-12-11T20:38:23+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f6a99b95b338799645fe9f7880d7d4ca1bf79cc1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f6a99b95b338799645fe9f7880d7d4ca1bf79cc1", "type": "zip", "shasum": "", "reference": "f6a99b95b338799645fe9f7880d7d4ca1bf79cc1"}, "time": "2017-12-04T18:15:22+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b3d0c9c11be3831b84825967dc6b52b5a7b84e04"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b3d0c9c11be3831b84825967dc6b52b5a7b84e04", "type": "zip", "shasum": "", "reference": "b3d0c9c11be3831b84825967dc6b52b5a7b84e04"}, "time": "2017-11-29T13:28:14+00:00"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "14568820af0af65e8ae97c3999b58cc94ea408bf"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/14568820af0af65e8ae97c3999b58cc94ea408bf", "type": "zip", "shasum": "", "reference": "14568820af0af65e8ae97c3999b58cc94ea408bf"}, "time": "2017-11-10T19:03:56+00:00"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e99024f324a7fa927c3a7992ed6f359e3a5ccc3b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e99024f324a7fa927c3a7992ed6f359e3a5ccc3b", "type": "zip", "shasum": "", "reference": "e99024f324a7fa927c3a7992ed6f359e3a5ccc3b"}, "time": "2017-10-09T14:52:13+00:00"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "af615970e265543a26ee712c958404eb9b7ac93d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/af615970e265543a26ee712c958404eb9b7ac93d", "type": "zip", "shasum": "", "reference": "af615970e265543a26ee712c958404eb9b7ac93d"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.3"}, "time": "2018-01-20T15:04:53+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "require-dev": {"symfony/console": "~2.8|~3.0"}, "conflict": "__unset"}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7c80d81b5805589be151b85b0df785f0dc3269cf"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7c80d81b5805589be151b85b0df785f0dc3269cf", "type": "zip", "shasum": "", "reference": "7c80d81b5805589be151b85b0df785f0dc3269cf"}, "time": "2018-01-03T07:37:11+00:00"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "36353762fdca3a0ecdce4640764efc885df979f6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/36353762fdca3a0ecdce4640764efc885df979f6", "type": "zip", "shasum": "", "reference": "36353762fdca3a0ecdce4640764efc885df979f6"}, "time": "2017-12-04T14:51:35+00:00"}, {"version": "v3.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0938408c4faa518d95230deabb5f595bf0de31b9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0938408c4faa518d95230deabb5f595bf0de31b9", "type": "zip", "shasum": "", "reference": "0938408c4faa518d95230deabb5f595bf0de31b9"}, "time": "2017-11-10T18:26:04+00:00"}, {"version": "v3.3.12", "version_normalized": "********"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8c7bf1e7d5d6b05a690b715729cb4cd0c0a99c46"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8c7bf1e7d5d6b05a690b715729cb4cd0c0a99c46", "type": "zip", "shasum": "", "reference": "8c7bf1e7d5d6b05a690b715729cb4cd0c0a99c46"}, "time": "2017-10-05T14:43:42+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1d8c2a99c80862bdc3af94c1781bf70f86bccac0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1d8c2a99c80862bdc3af94c1781bf70f86bccac0", "type": "zip", "shasum": "", "reference": "1d8c2a99c80862bdc3af94c1781bf70f86bccac0"}, "time": "2017-07-29T21:54:42+00:00"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ddc23324e6cfe066f3dd34a37ff494fa80b617ed"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ddc23324e6cfe066f3dd34a37ff494fa80b617ed", "type": "zip", "shasum": "", "reference": "ddc23324e6cfe066f3dd34a37ff494fa80b617ed"}, "time": "2017-07-23T12:43:26+00:00", "require": {"php": ">=5.5.9"}}, {"version": "v3.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1f93a8d19b8241617f5074a123e282575b821df8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1f93a8d19b8241617f5074a123e282575b821df8", "type": "zip", "shasum": "", "reference": "1f93a8d19b8241617f5074a123e282575b821df8"}, "time": "2017-06-15T12:58:50+00:00"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9752a30000a8ca9f4b34b5227d15d0101b96b063"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9752a30000a8ca9f4b34b5227d15d0101b96b063", "type": "zip", "shasum": "", "reference": "9752a30000a8ca9f4b34b5227d15d0101b96b063"}, "time": "2017-06-02T22:05:06+00:00"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "885db865f6b2b918404a1fae28f9ac640f71f994"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/885db865f6b2b918404a1fae28f9ac640f71f994", "type": "zip", "shasum": "", "reference": "885db865f6b2b918404a1fae28f9ac640f71f994"}, "time": "2017-05-28T10:56:20+00:00"}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "924d16e27b592d8bb8aa8ef531005a3f564f64e7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/924d16e27b592d8bb8aa8ef531005a3f564f64e7", "type": "zip", "shasum": "", "reference": "924d16e27b592d8bb8aa8ef531005a3f564f64e7"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2017-05-15T12:04:53+00:00"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dcbd318c241341c82444a8d92b7a0ddf6419f496"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dcbd318c241341c82444a8d92b7a0ddf6419f496", "type": "zip", "shasum": "", "reference": "dcbd318c241341c82444a8d92b7a0ddf6419f496"}, "time": "2017-05-01T15:01:29+00:00"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "78a0c5d7d43713212aac73d7c6a56754a5c26cea"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/78a0c5d7d43713212aac73d7c6a56754a5c26cea", "type": "zip", "shasum": "", "reference": "78a0c5d7d43713212aac73d7c6a56754a5c26cea"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.2"}, "time": "2017-06-02T09:43:35+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4cdb9fec28fba88203a71f6d095018867f7a2065"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4cdb9fec28fba88203a71f6d095018867f7a2065", "type": "zip", "shasum": "", "reference": "4cdb9fec28fba88203a71f6d095018867f7a2065"}, "time": "2017-05-25T23:42:36+00:00"}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "acec26fcf7f3031e094e910b94b002fa53d4e4d6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/acec26fcf7f3031e094e910b94b002fa53d4e4d6", "type": "zip", "shasum": "", "reference": "acec26fcf7f3031e094e910b94b002fa53d4e4d6"}, "time": "2017-05-01T14:55:58+00:00"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "62b4cdb99d52cb1ff253c465eb1532a80cebb621"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/62b4cdb99d52cb1ff253c465eb1532a80cebb621", "type": "zip", "shasum": "", "reference": "62b4cdb99d52cb1ff253c465eb1532a80cebb621"}, "time": "2017-03-20T09:45:15+00:00"}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "093e416ad096355149e265ea2e4cc1f9ee40ab1a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/093e416ad096355149e265ea2e4cc1f9ee40ab1a", "type": "zip", "shasum": "", "reference": "093e416ad096355149e265ea2e4cc1f9ee40ab1a"}, "time": "2017-03-07T16:47:02+00:00"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9724c684646fcb5387d579b4bfaa63ee0b0c64c8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9724c684646fcb5387d579b4bfaa63ee0b0c64c8", "type": "zip", "shasum": "", "reference": "9724c684646fcb5387d579b4bfaa63ee0b0c64c8"}, "time": "2017-02-16T22:46:52+00:00"}, {"version": "v3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e1718c6bf57e1efbb8793ada951584b2ab27775b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e1718c6bf57e1efbb8793ada951584b2ab27775b", "type": "zip", "shasum": "", "reference": "e1718c6bf57e1efbb8793ada951584b2ab27775b"}, "time": "2017-01-21T17:06:35+00:00"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "50eadbd7926e31842893c957eca362b21592a97d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/50eadbd7926e31842893c957eca362b21592a97d", "type": "zip", "shasum": "", "reference": "50eadbd7926e31842893c957eca362b21592a97d"}, "time": "2017-01-03T13:51:32+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a7095af4b97a0955f85c8989106c249fa649011f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a7095af4b97a0955f85c8989106c249fa649011f", "type": "zip", "shasum": "", "reference": "a7095af4b97a0955f85c8989106c249fa649011f"}, "time": "2016-12-10T10:07:06+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f2300ba8fbb002c028710b92e1906e7457410693"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f2300ba8fbb002c028710b92e1906e7457410693", "type": "zip", "shasum": "", "reference": "f2300ba8fbb002c028710b92e1906e7457410693"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2016-11-18T21:17:59+00:00"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "92e2f6deeda11ffaf001caa2e43f7fc13f0eee19"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/92e2f6deeda11ffaf001caa2e43f7fc13f0eee19", "type": "zip", "shasum": "", "reference": "92e2f6deeda11ffaf001caa2e43f7fc13f0eee19"}, "time": "2016-11-14T16:20:13+00:00"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d2cab32980d8de786744fabdec95991a0ebd7ea8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d2cab32980d8de786744fabdec95991a0ebd7ea8", "type": "zip", "shasum": "", "reference": "d2cab32980d8de786744fabdec95991a0ebd7ea8"}, "time": "2016-10-24T18:44:12+00:00"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "752bc2a0dff0168742c92ec87261f7e09b7935fb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/752bc2a0dff0168742c92ec87261f7e09b7935fb", "type": "zip", "shasum": "", "reference": "752bc2a0dff0168742c92ec87261f7e09b7935fb"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.1"}, "time": "2017-01-21T17:01:39+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "require-dev": "__unset", "suggest": "__unset"}, {"version": "v3.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "df9f28171678090da266b0bf7e505125bd7da250"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/df9f28171678090da266b0bf7e505125bd7da250", "type": "zip", "shasum": "", "reference": "df9f28171678090da266b0bf7e505125bd7da250"}, "time": "2017-01-03T13:51:21+00:00"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "da61ca54e755bda2108f97ce67c37f0713df15b2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/da61ca54e755bda2108f97ce67c37f0713df15b2", "type": "zip", "shasum": "", "reference": "da61ca54e755bda2108f97ce67c37f0713df15b2"}, "time": "2016-12-03T10:04:08+00:00"}, {"version": "v3.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9da375317228e54f4ea1b013b30fa47417e84943"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9da375317228e54f4ea1b013b30fa47417e84943", "type": "zip", "shasum": "", "reference": "9da375317228e54f4ea1b013b30fa47417e84943"}, "time": "2016-11-18T21:05:29+00:00"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7ff51b06c6c3d5cc6686df69004a42c69df09e27"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7ff51b06c6c3d5cc6686df69004a42c69df09e27", "type": "zip", "shasum": "", "reference": "7ff51b06c6c3d5cc6686df69004a42c69df09e27"}, "time": "2016-10-24T18:41:13+00:00"}, {"version": "v3.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "368b9738d4033c8b93454cb0dbd45d305135a6d3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/368b9738d4033c8b93454cb0dbd45d305135a6d3", "type": "zip", "shasum": "", "reference": "368b9738d4033c8b93454cb0dbd45d305135a6d3"}, "time": "2016-09-25T08:27:07+00:00"}, {"version": "v3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f291ed25eb1435bddbe8a96caaef16469c2a092d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f291ed25eb1435bddbe8a96caaef16469c2a092d", "type": "zip", "shasum": "", "reference": "f291ed25eb1435bddbe8a96caaef16469c2a092d"}, "time": "2016-09-02T02:12:52+00:00"}, {"version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1819adf2066880c7967df7180f4f662b6f0567ac"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1819adf2066880c7967df7180f4f662b6f0567ac", "type": "zip", "shasum": "", "reference": "1819adf2066880c7967df7180f4f662b6f0567ac"}, "time": "2016-07-17T14:02:08+00:00"}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2884c26ce4c1d61aebf423a8b912950fe7c764de"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2884c26ce4c1d61aebf423a8b912950fe7c764de", "type": "zip", "shasum": "", "reference": "2884c26ce4c1d61aebf423a8b912950fe7c764de"}, "time": "2016-06-29T05:41:56+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c5a7e7fc273c758b92b85dcb9c46149ccda89623"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c5a7e7fc273c758b92b85dcb9c46149ccda89623", "type": "zip", "shasum": "", "reference": "c5a7e7fc273c758b92b85dcb9c46149ccda89623"}, "time": "2016-06-14T11:18:07+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "eca51b7b65eb9be6af88ad7cc91685f1556f5c9a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/eca51b7b65eb9be6af88ad7cc91685f1556f5c9a", "type": "zip", "shasum": "", "reference": "eca51b7b65eb9be6af88ad7cc91685f1556f5c9a"}, "time": "2016-05-26T21:46:24+00:00"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4c2e68f685c2d5ce4f051c0bb6ea4ef37fc0143c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4c2e68f685c2d5ce4f051c0bb6ea4ef37fc0143c", "type": "zip", "shasum": "", "reference": "4c2e68f685c2d5ce4f051c0bb6ea4ef37fc0143c"}, "time": "2016-05-24T10:06:56+00:00"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "407e31ad9742ace5c3d01642f02a3b2e6062bae5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/407e31ad9742ace5c3d01642f02a3b2e6062bae5", "type": "zip", "shasum": "", "reference": "407e31ad9742ace5c3d01642f02a3b2e6062bae5"}, "support": {"source": "https://github.com/symfony/yaml/tree/master"}, "time": "2016-03-30T14:44:34+00:00"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2a9101d2bfee061caa0f978c9c4e6a84d5eaed96"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2a9101d2bfee061caa0f978c9c4e6a84d5eaed96", "type": "zip", "shasum": "", "reference": "2a9101d2bfee061caa0f978c9c4e6a84d5eaed96"}, "support": {"source": "https://github.com/symfony/yaml/tree/3.0"}, "time": "2016-07-17T13:54:30+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d7dfe7f9915916195ed146cf81a5ac74b5095751"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d7dfe7f9915916195ed146cf81a5ac74b5095751", "type": "zip", "shasum": "", "reference": "d7dfe7f9915916195ed146cf81a5ac74b5095751"}, "time": "2016-06-29T05:40:00+00:00"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "fd2f945960998753de764109a26bcb8d08783bfa"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/fd2f945960998753de764109a26bcb8d08783bfa", "type": "zip", "shasum": "", "reference": "fd2f945960998753de764109a26bcb8d08783bfa"}, "time": "2016-06-06T11:33:26+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0047c8366744a16de7516622c5b7355336afae96"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0047c8366744a16de7516622c5b7355336afae96", "type": "zip", "shasum": "", "reference": "0047c8366744a16de7516622c5b7355336afae96"}, "time": "2016-03-04T07:55:57+00:00"}, {"version": "v3.0.5", "version_normalized": "*******"}, {"version": "v3.0.4", "version_normalized": "*******"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b5ba64cd67ecd6887f63868fa781ca094bd1377c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b5ba64cd67ecd6887f63868fa781ca094bd1377c", "type": "zip", "shasum": "", "reference": "b5ba64cd67ecd6887f63868fa781ca094bd1377c"}, "time": "2016-02-23T15:16:06+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3cf0709d7fe936e97bee9e954382e449003f1d9a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3cf0709d7fe936e97bee9e954382e449003f1d9a", "type": "zip", "shasum": "", "reference": "3cf0709d7fe936e97bee9e954382e449003f1d9a"}, "time": "2016-02-02T13:44:19+00:00"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3df409958a646dad2bc5046c3fb671ee24a1a691"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3df409958a646dad2bc5046c3fb671ee24a1a691", "type": "zip", "shasum": "", "reference": "3df409958a646dad2bc5046c3fb671ee24a1a691"}, "time": "2015-12-26T13:39:53+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4113f97104ffdcb734e15c27fbe27a7e1a3ac3b0"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4113f97104ffdcb734e15c27fbe27a7e1a3ac3b0", "type": "zip", "shasum": "", "reference": "4113f97104ffdcb734e15c27fbe27a7e1a3ac3b0"}, "time": "2015-11-30T12:36:17+00:00"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3d9f37b845568054d386b31370d4b6f5eb234dd1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3d9f37b845568054d386b31370d4b6f5eb234dd1", "type": "zip", "shasum": "", "reference": "3d9f37b845568054d386b31370d4b6f5eb234dd1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v3.0.0-BETA1"}, "time": "2015-11-10T10:42:06+00:00"}, {"version": "v2.8.52", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "02c1859112aa779d9ab394ae4f3381911d84052b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/02c1859112aa779d9ab394ae4f3381911d84052b", "type": "zip", "shasum": "", "reference": "02c1859112aa779d9ab394ae4f3381911d84052b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.8.52"}, "time": "2018-11-11T11:18:13+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v2.8.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/v2.8.50"}}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0e16589861f192dbffb19b06683ce3ef58f7f99d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0e16589861f192dbffb19b06683ce3ef58f7f99d", "type": "zip", "shasum": "", "reference": "0e16589861f192dbffb19b06683ce3ef58f7f99d"}, "time": "2018-10-02T16:27:16+00:00"}, {"version": "v2.8.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5baf0f821b14eee8ca415e6a0361a9fa140c002c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5baf0f821b14eee8ca415e6a0361a9fa140c002c", "type": "zip", "shasum": "", "reference": "5baf0f821b14eee8ca415e6a0361a9fa140c002c"}, "time": "2018-08-29T13:11:53+00:00"}, {"version": "v2.8.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "fbf876678e29dc634430dcf0096e216eb0004467"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/fbf876678e29dc634430dcf0096e216eb0004467", "type": "zip", "shasum": "", "reference": "fbf876678e29dc634430dcf0096e216eb0004467"}, "time": "2018-07-26T09:03:18+00:00"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "51356b7a2ff7c9fd06b2f1681cc463bb62b5c1ff"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/51356b7a2ff7c9fd06b2f1681cc463bb62b5c1ff", "type": "zip", "shasum": "", "reference": "51356b7a2ff7c9fd06b2f1681cc463bb62b5c1ff"}, "time": "2018-05-01T22:52:40+00:00"}, {"version": "v2.8.42", "version_normalized": "********"}, {"version": "v2.8.41", "version_normalized": "********"}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d20bd2bdee063863e426297af41eda45ccad6f7e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d20bd2bdee063863e426297af41eda45ccad6f7e", "type": "zip", "shasum": "", "reference": "d20bd2bdee063863e426297af41eda45ccad6f7e"}, "time": "2018-04-08T07:53:13+00:00", "require": {"php": ">=5.3.9"}}, {"version": "v2.8.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "be720fcfae4614df204190d57795351059946a77"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/be720fcfae4614df204190d57795351059946a77", "type": "zip", "shasum": "", "reference": "be720fcfae4614df204190d57795351059946a77"}, "time": "2018-01-03T07:36:31+00:00"}, {"version": "v2.8.37", "version_normalized": "********"}, {"version": "v2.8.36", "version_normalized": "********"}, {"version": "v2.8.35", "version_normalized": "********"}, {"version": "v2.8.34", "version_normalized": "********"}, {"version": "v2.8.33", "version_normalized": "********"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "968ef42161e4bc04200119da473077f9e7015128"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/968ef42161e4bc04200119da473077f9e7015128", "type": "zip", "shasum": "", "reference": "968ef42161e4bc04200119da473077f9e7015128"}, "time": "2017-11-29T09:33:18+00:00"}, {"version": "v2.8.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d819bf267e901727141fe828ae888486fd21236e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d819bf267e901727141fe828ae888486fd21236e", "type": "zip", "shasum": "", "reference": "d819bf267e901727141fe828ae888486fd21236e"}, "time": "2017-11-05T15:25:56+00:00"}, {"version": "v2.8.30", "version_normalized": "********"}, {"version": "v2.8.29", "version_normalized": "********"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "842fb6df22180244b4c65935ce1a88d324e5ff9e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/842fb6df22180244b4c65935ce1a88d324e5ff9e", "type": "zip", "shasum": "", "reference": "842fb6df22180244b4c65935ce1a88d324e5ff9e"}, "time": "2017-10-05T14:38:30+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4c29dec8d489c4e37cf87ccd7166cd0b0e6a45c5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4c29dec8d489c4e37cf87ccd7166cd0b0e6a45c5", "type": "zip", "shasum": "", "reference": "4c29dec8d489c4e37cf87ccd7166cd0b0e6a45c5"}, "time": "2017-06-01T20:52:29+00:00"}, {"version": "v2.8.26", "version_normalized": "********"}, {"version": "v2.8.25", "version_normalized": "********"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********"}, {"version": "v2.8.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "93ccdde79f4b079c7558da4656a3cb1c50c68e02"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/93ccdde79f4b079c7558da4656a3cb1c50c68e02", "type": "zip", "shasum": "", "reference": "93ccdde79f4b079c7558da4656a3cb1c50c68e02"}, "time": "2017-05-01T14:31:55+00:00"}, {"version": "v2.8.20", "version_normalized": "********"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "286d84891690b0e2515874717e49360d1c98a703"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/286d84891690b0e2515874717e49360d1c98a703", "type": "zip", "shasum": "", "reference": "286d84891690b0e2515874717e49360d1c98a703"}, "time": "2017-03-20T09:41:44+00:00"}, {"version": "v2.8.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2a7bab3c16f6f452c47818fdd08f3b1e49ffcf7d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2a7bab3c16f6f452c47818fdd08f3b1e49ffcf7d", "type": "zip", "shasum": "", "reference": "2a7bab3c16f6f452c47818fdd08f3b1e49ffcf7d"}, "time": "2017-03-01T18:13:50+00:00"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "322a8c2dfbca15ad6b1b27e182899f98ec0e0153"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/322a8c2dfbca15ad6b1b27e182899f98ec0e0153", "type": "zip", "shasum": "", "reference": "322a8c2dfbca15ad6b1b27e182899f98ec0e0153"}, "time": "2017-01-21T16:40:50+00:00"}, {"version": "v2.8.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dbe61fed9cd4a44c5b1d14e5e7b1a8640cfb2bf2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dbe61fed9cd4a44c5b1d14e5e7b1a8640cfb2bf2", "type": "zip", "shasum": "", "reference": "dbe61fed9cd4a44c5b1d14e5e7b1a8640cfb2bf2"}, "time": "2017-01-03T13:49:52+00:00"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "befb26a3713c97af90d25dd12e75621ef14d91ff"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/befb26a3713c97af90d25dd12e75621ef14d91ff", "type": "zip", "shasum": "", "reference": "befb26a3713c97af90d25dd12e75621ef14d91ff"}, "time": "2016-11-14T16:15:57+00:00"}, {"version": "v2.8.14", "version_normalized": "********"}, {"version": "v2.8.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "396784cd06b91f3db576f248f2402d547a077787"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/396784cd06b91f3db576f248f2402d547a077787", "type": "zip", "shasum": "", "reference": "396784cd06b91f3db576f248f2402d547a077787"}, "time": "2016-10-21T20:59:10+00:00"}, {"version": "v2.8.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e7540734bad981fe59f8ef14b6fc194ae9df8d9c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e7540734bad981fe59f8ef14b6fc194ae9df8d9c", "type": "zip", "shasum": "", "reference": "e7540734bad981fe59f8ef14b6fc194ae9df8d9c"}, "time": "2016-09-02T01:57:56+00:00"}, {"version": "v2.8.11", "version_normalized": "********"}, {"version": "v2.8.10", "version_normalized": "********"}, {"version": "v2.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "0ceab136f43ed9d3e97b3eea32a7855dc50c121d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/0ceab136f43ed9d3e97b3eea32a7855dc50c121d", "type": "zip", "shasum": "", "reference": "0ceab136f43ed9d3e97b3eea32a7855dc50c121d"}, "time": "2016-07-17T09:06:15+00:00"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dba4bb5846798cd12f32e2d8f3f35d77045773c8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dba4bb5846798cd12f32e2d8f3f35d77045773c8", "type": "zip", "shasum": "", "reference": "dba4bb5846798cd12f32e2d8f3f35d77045773c8"}, "time": "2016-06-29T05:29:29+00:00"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "815fabf3f48c7d1df345a69d1ad1a88f59757b34"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/815fabf3f48c7d1df345a69d1ad1a88f59757b34", "type": "zip", "shasum": "", "reference": "815fabf3f48c7d1df345a69d1ad1a88f59757b34"}, "time": "2016-06-06T11:11:27+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e4fbcc65f90909c999ac3b4dfa699ee6563a9940"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e4fbcc65f90909c999ac3b4dfa699ee6563a9940", "type": "zip", "shasum": "", "reference": "e4fbcc65f90909c999ac3b4dfa699ee6563a9940"}, "time": "2016-03-29T19:00:15+00:00"}, {"version": "v2.8.5", "version_normalized": "*******"}, {"version": "v2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "584e52cb8f788a887553ba82db6caacb1d6260bb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/584e52cb8f788a887553ba82db6caacb1d6260bb", "type": "zip", "shasum": "", "reference": "584e52cb8f788a887553ba82db6caacb1d6260bb"}, "time": "2016-03-04T07:54:35+00:00"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2a4ee40acb880c56f29fb1b8886e7ffe94f3b995"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2a4ee40acb880c56f29fb1b8886e7ffe94f3b995", "type": "zip", "shasum": "", "reference": "2a4ee40acb880c56f29fb1b8886e7ffe94f3b995"}, "time": "2016-02-23T07:41:20+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "34c8a4b51e751e7ea869b8262f883d008a2b81b8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/34c8a4b51e751e7ea869b8262f883d008a2b81b8", "type": "zip", "shasum": "", "reference": "34c8a4b51e751e7ea869b8262f883d008a2b81b8"}, "time": "2016-01-13T10:28:07+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ac84cbb98b68a6abbc9f5149eb96ccc7b07b8966"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ac84cbb98b68a6abbc9f5149eb96ccc7b07b8966", "type": "zip", "shasum": "", "reference": "ac84cbb98b68a6abbc9f5149eb96ccc7b07b8966"}, "time": "2015-12-26T13:37:56+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f79824187de95064a2f5038904c4d7f0227fedb5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f79824187de95064a2f5038904c4d7f0227fedb5", "type": "zip", "shasum": "", "reference": "f79824187de95064a2f5038904c4d7f0227fedb5"}, "time": "2015-11-30T12:35:10+00:00"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5ff00ea2999343d42398fcb793bbfc64c92235ff"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5ff00ea2999343d42398fcb793bbfc64c92235ff", "type": "zip", "shasum": "", "reference": "5ff00ea2999343d42398fcb793bbfc64c92235ff"}, "time": "2015-11-05T09:04:44+00:00"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5bbd7e6eb54413d52ae73720ef5edadd37e3fc9e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5bbd7e6eb54413d52ae73720ef5edadd37e3fc9e", "type": "zip", "shasum": "", "reference": "5bbd7e6eb54413d52ae73720ef5edadd37e3fc9e"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.7.51"}, "time": "2018-05-01T22:30:49+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "~1.8"}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/yaml/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "********"}, {"version": "v2.7.47", "version_normalized": "********"}, {"version": "v2.7.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e2c80e6ddcf0b03ce3b4223dd50affeb6bc5a6a4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e2c80e6ddcf0b03ce3b4223dd50affeb6bc5a6a4", "type": "zip", "shasum": "", "reference": "e2c80e6ddcf0b03ce3b4223dd50affeb6bc5a6a4"}, "time": "2018-04-06T11:01:31+00:00", "require": {"php": ">=5.3.9"}}, {"version": "v2.7.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b7f540832f96d31ad4201ba008d6abf529ae9722"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b7f540832f96d31ad4201ba008d6abf529ae9722", "type": "zip", "shasum": "", "reference": "b7f540832f96d31ad4201ba008d6abf529ae9722"}, "time": "2018-01-03T07:23:28+00:00"}, {"version": "v2.7.44", "version_normalized": "********"}, {"version": "v2.7.43", "version_normalized": "********"}, {"version": "v2.7.42", "version_normalized": "********"}, {"version": "v2.7.41", "version_normalized": "********"}, {"version": "v2.7.40", "version_normalized": "********"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ad601e4f5eee2e4f336f2d318d06b2594f92ddb9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ad601e4f5eee2e4f336f2d318d06b2594f92ddb9", "type": "zip", "shasum": "", "reference": "ad601e4f5eee2e4f336f2d318d06b2594f92ddb9"}, "time": "2017-11-20T09:04:28+00:00"}, {"version": "v2.7.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "88036f65093341da0b427b26c01cd0e7dad94309"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/88036f65093341da0b427b26c01cd0e7dad94309", "type": "zip", "shasum": "", "reference": "88036f65093341da0b427b26c01cd0e7dad94309"}, "time": "2017-10-29T09:49:53+00:00"}, {"version": "v2.7.37", "version_normalized": "********"}, {"version": "v2.7.36", "version_normalized": "********"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "eb877f1c8d16107971e7fb93f7649dd0f6d11a25"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/eb877f1c8d16107971e7fb93f7649dd0f6d11a25", "type": "zip", "shasum": "", "reference": "eb877f1c8d16107971e7fb93f7649dd0f6d11a25"}, "time": "2017-10-03T22:19:48+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dee17ff429c904941b444c6e7358e5d0db855c30"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dee17ff429c904941b444c6e7358e5d0db855c30", "type": "zip", "shasum": "", "reference": "dee17ff429c904941b444c6e7358e5d0db855c30"}, "time": "2017-06-01T20:44:56+00:00"}, {"version": "v2.7.33", "version_normalized": "********"}, {"version": "v2.7.32", "version_normalized": "********"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********"}, {"version": "v2.7.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b8e3f3b9b575ce4271b41041b1fa4b438d0c4273"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b8e3f3b9b575ce4271b41041b1fa4b438d0c4273", "type": "zip", "shasum": "", "reference": "b8e3f3b9b575ce4271b41041b1fa4b438d0c4273"}, "time": "2017-04-29T15:58:46+00:00"}, {"version": "v2.7.27", "version_normalized": "********"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "596e1fa8e29158bbb674b3dd63831de36ecc8b51"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/596e1fa8e29158bbb674b3dd63831de36ecc8b51", "type": "zip", "shasum": "", "reference": "596e1fa8e29158bbb674b3dd63831de36ecc8b51"}, "time": "2017-03-20T09:41:03+00:00"}, {"version": "v2.7.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7f98ec63731d5d5995bf29f6a6b6c29ad649da07"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7f98ec63731d5d5995bf29f6a6b6c29ad649da07", "type": "zip", "shasum": "", "reference": "7f98ec63731d5d5995bf29f6a6b6c29ad649da07"}, "time": "2017-03-01T14:39:50+00:00"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "149dd4e4176b6df9b2e446f7cdefa53a4ab4ecdf"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/149dd4e4176b6df9b2e446f7cdefa53a4ab4ecdf", "type": "zip", "shasum": "", "reference": "149dd4e4176b6df9b2e446f7cdefa53a4ab4ecdf"}, "time": "2017-01-13T16:26:16+00:00"}, {"version": "v2.7.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "809772e5bcbe9ef352aef84eaf2bd1bdf440ceb4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/809772e5bcbe9ef352aef84eaf2bd1bdf440ceb4", "type": "zip", "shasum": "", "reference": "809772e5bcbe9ef352aef84eaf2bd1bdf440ceb4"}, "time": "2017-01-03T13:44:39+00:00"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "db8dae7ff732262fbcbec0d209200ae3ec5e23b4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/db8dae7ff732262fbcbec0d209200ae3ec5e23b4", "type": "zip", "shasum": "", "reference": "db8dae7ff732262fbcbec0d209200ae3ec5e23b4"}, "time": "2016-11-09T17:27:48+00:00"}, {"version": "v2.7.21", "version_normalized": "********"}, {"version": "v2.7.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1a9d264853100c597649f0218bb3c08c7bb3cbe8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1a9d264853100c597649f0218bb3c08c7bb3cbe8", "type": "zip", "shasum": "", "reference": "1a9d264853100c597649f0218bb3c08c7bb3cbe8"}, "time": "2016-08-31T13:10:08+00:00"}, {"version": "v2.7.19", "version_normalized": "********"}, {"version": "v2.7.18", "version_normalized": "********"}, {"version": "v2.7.17", "version_normalized": "********"}, {"version": "v2.7.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "434114468bd22fd7f9dc32695d3d80ab62afaf2f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/434114468bd22fd7f9dc32695d3d80ab62afaf2f", "type": "zip", "shasum": "", "reference": "434114468bd22fd7f9dc32695d3d80ab62afaf2f"}, "time": "2016-07-11T07:20:55+00:00"}, {"version": "v2.7.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d529c0f64a688cc79b84a0aaaf0ac88a1b5aa266"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d529c0f64a688cc79b84a0aaaf0ac88a1b5aa266", "type": "zip", "shasum": "", "reference": "d529c0f64a688cc79b84a0aaaf0ac88a1b5aa266"}, "time": "2016-06-28T06:24:06+00:00"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4c2dc513db5a7b30fc770dedf830ca5bd1a3246e"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4c2dc513db5a7b30fc770dedf830ca5bd1a3246e", "type": "zip", "shasum": "", "reference": "4c2dc513db5a7b30fc770dedf830ca5bd1a3246e"}, "time": "2016-06-06T09:00:08+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e9877e0c01cdb4f3daf04784b2a8315ee8cb5b68"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e9877e0c01cdb4f3daf04784b2a8315ee8cb5b68", "type": "zip", "shasum": "", "reference": "e9877e0c01cdb4f3daf04784b2a8315ee8cb5b68"}, "time": "2016-03-04T07:52:28+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "fc0ad7a7450ed4434c9a397796a9f56199de2053"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/fc0ad7a7450ed4434c9a397796a9f56199de2053", "type": "zip", "shasum": "", "reference": "fc0ad7a7450ed4434c9a397796a9f56199de2053"}, "time": "2016-02-23T07:38:51+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a91e8af3dcde226e00be2e1c068764eef7b4c153"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a91e8af3dcde226e00be2e1c068764eef7b4c153", "type": "zip", "shasum": "", "reference": "a91e8af3dcde226e00be2e1c068764eef7b4c153"}, "time": "2016-01-13T10:26:43+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1d96518eb7775ba42704652dff32269236c5adb4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1d96518eb7775ba42704652dff32269236c5adb4", "type": "zip", "shasum": "", "reference": "1d96518eb7775ba42704652dff32269236c5adb4"}, "time": "2015-12-26T13:37:43+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4cfcd7a9fceba662b3c036b7d9a91f6197af046c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4cfcd7a9fceba662b3c036b7d9a91f6197af046c", "type": "zip", "shasum": "", "reference": "4cfcd7a9fceba662b3c036b7d9a91f6197af046c"}, "time": "2015-11-18T13:41:01+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "eca9019c88fbe250164affd107bc8057771f3f4d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/eca9019c88fbe250164affd107bc8057771f3f4d", "type": "zip", "shasum": "", "reference": "eca9019c88fbe250164affd107bc8057771f3f4d"}, "time": "2015-10-11T09:39:48+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "31cb2ad0155c95b88ee55fe12bc7ff92232c1770"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/31cb2ad0155c95b88ee55fe12bc7ff92232c1770", "type": "zip", "shasum": "", "reference": "31cb2ad0155c95b88ee55fe12bc7ff92232c1770"}, "time": "2015-09-14T14:14:09+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "2dc7b06c065df96cc686c66da2705e5e18aef661"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/2dc7b06c065df96cc686c66da2705e5e18aef661", "type": "zip", "shasum": "", "reference": "2dc7b06c065df96cc686c66da2705e5e18aef661"}, "support": {"source": "https://github.com/symfony/Yaml/tree/2.7"}, "time": "2015-08-24T07:13:45+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "71340e996171474a53f3d29111d046be4ad8a0ff"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/71340e996171474a53f3d29111d046be4ad8a0ff", "type": "zip", "shasum": "", "reference": "71340e996171474a53f3d29111d046be4ad8a0ff"}, "time": "2015-07-28T14:07:07+00:00"}, {"version": "v2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "4bfbe0ed3909bfddd75b70c094391ec1f142f860"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/4bfbe0ed3909bfddd75b70c094391ec1f142f860", "type": "zip", "shasum": "", "reference": "4bfbe0ed3909bfddd75b70c094391ec1f142f860"}, "time": "2015-07-01T11:25:50+00:00"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "9808e75c609a14f6db02f70fccf4ca4aab53c160"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/9808e75c609a14f6db02f70fccf4ca4aab53c160", "type": "zip", "shasum": "", "reference": "9808e75c609a14f6db02f70fccf4ca4aab53c160"}, "time": "2015-06-10T15:30:22+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "4a29a5248aed4fb45f626a7bbbd330291492f5c3"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/4a29a5248aed4fb45f626a7bbbd330291492f5c3", "type": "zip", "shasum": "", "reference": "4a29a5248aed4fb45f626a7bbbd330291492f5c3"}, "time": "2015-05-02T15:21:08+00:00"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "43e7bee10c88f23252ee1b6b25140dc9917c3d96"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/43e7bee10c88f23252ee1b6b25140dc9917c3d96", "type": "zip", "shasum": "", "reference": "43e7bee10c88f23252ee1b6b25140dc9917c3d96"}, "time": "2015-04-10T07:23:38+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Yaml\\": ""}}, "target-dir": "Symfony/Component/Yaml"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c044d1744b8e91aaaa0d9bac683ab87ec7cbf359"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c044d1744b8e91aaaa0d9bac683ab87ec7cbf359", "type": "zip", "shasum": "", "reference": "c044d1744b8e91aaaa0d9bac683ab87ec7cbf359"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.6.11"}, "time": "2015-07-26T08:59:42+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "c044d1744b8e91aaaa0d9bac683ab87ec7cbf359"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/c044d1744b8e91aaaa0d9bac683ab87ec7cbf359", "type": "zip", "shasum": "", "reference": "c044d1744b8e91aaaa0d9bac683ab87ec7cbf359"}, "support": {"source": "https://github.com/symfony/Yaml/tree/2.6"}}, {"version": "v2.6.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "446353cc05339e676fb57e35232d2bfd055a47cd"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/446353cc05339e676fb57e35232d2bfd055a47cd", "type": "zip", "shasum": "", "reference": "446353cc05339e676fb57e35232d2bfd055a47cd"}, "time": "2015-06-30T16:10:16+00:00"}, {"version": "v2.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "f157ab074e453ecd4c0fa775f721f6e67a99d9e2"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/f157ab074e453ecd4c0fa775f721f6e67a99d9e2", "type": "zip", "shasum": "", "reference": "f157ab074e453ecd4c0fa775f721f6e67a99d9e2"}, "time": "2015-05-02T15:18:45+00:00"}, {"version": "v2.6.8", "version_normalized": "*******"}, {"version": "v2.6.7", "version_normalized": "*******"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "174f009ed36379a801109955fc5a71a49fe62dd4"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/174f009ed36379a801109955fc5a71a49fe62dd4", "type": "zip", "shasum": "", "reference": "174f009ed36379a801109955fc5a71a49fe62dd4"}, "time": "2015-03-30T15:54:10+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "0cd8e72071e46e15fc072270ae39ea1b66b10a9d"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/0cd8e72071e46e15fc072270ae39ea1b66b10a9d", "type": "zip", "shasum": "", "reference": "0cd8e72071e46e15fc072270ae39ea1b66b10a9d"}, "time": "2015-03-12T10:28:44+00:00"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "60ed7751671113cf1ee7d7778e691642c2e9acd8"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/60ed7751671113cf1ee7d7778e691642c2e9acd8", "type": "zip", "shasum": "", "reference": "60ed7751671113cf1ee7d7778e691642c2e9acd8"}, "time": "2015-01-25T04:39:26+00:00", "require-dev": "__unset"}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "82462a90848a52c2533aa6b598b107d68076b018"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/82462a90848a52c2533aa6b598b107d68076b018", "type": "zip", "shasum": "", "reference": "82462a90848a52c2533aa6b598b107d68076b018"}, "time": "2015-01-03T15:33:07+00:00"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "3346fc090a3eb6b53d408db2903b241af51dcb20"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/3346fc090a3eb6b53d408db2903b241af51dcb20", "type": "zip", "shasum": "", "reference": "3346fc090a3eb6b53d408db2903b241af51dcb20"}, "time": "2014-12-02T20:19:20+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "51c845cf3e4bfc182d1d5c05ed1c7338361d86f8"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/51c845cf3e4bfc182d1d5c05ed1c7338361d86f8", "type": "zip", "shasum": "", "reference": "51c845cf3e4bfc182d1d5c05ed1c7338361d86f8"}, "time": "2014-11-20T13:24:23+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ff1193da791bfbad1a8d506cf91ee4575e3450fe"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ff1193da791bfbad1a8d506cf91ee4575e3450fe", "type": "zip", "shasum": "", "reference": "ff1193da791bfbad1a8d506cf91ee4575e3450fe"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.6.0-BETA1"}, "time": "2014-11-03T03:55:50+00:00"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "6eb40abc613d80828d55a40811c7a3ca491bc926"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/6eb40abc613d80828d55a40811c7a3ca491bc926", "type": "zip", "shasum": "", "reference": "6eb40abc613d80828d55a40811c7a3ca491bc926"}, "support": {"source": "https://github.com/symfony/Yaml/tree/v2.5.11"}, "time": "2015-01-25T04:37:39+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}}, {"version": "v2.5.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/Yaml/tree/2.5"}}, {"version": "v2.5.10", "version_normalized": "********"}, {"version": "v2.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "522b6c2f45504521fd411a6b1632958bfee20845"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/522b6c2f45504521fd411a6b1632958bfee20845", "type": "zip", "shasum": "", "reference": "522b6c2f45504521fd411a6b1632958bfee20845"}, "time": "2015-01-03T14:48:25+00:00"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "b6ad726d415aa787ac4deddc75e7ad7a1bcc84bd"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/b6ad726d415aa787ac4deddc75e7ad7a1bcc84bd", "type": "zip", "shasum": "", "reference": "b6ad726d415aa787ac4deddc75e7ad7a1bcc84bd"}, "time": "2014-12-02T20:15:53+00:00"}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "900d38bc8f74a50343ce65dd1c1e9819658ee56b"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/900d38bc8f74a50343ce65dd1c1e9819658ee56b", "type": "zip", "shasum": "", "reference": "900d38bc8f74a50343ce65dd1c1e9819658ee56b"}, "time": "2014-11-20T13:22:25+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "2d9f527449cabfa8543dd7fa3a466d6ae83d6726"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/2d9f527449cabfa8543dd7fa3a466d6ae83d6726", "type": "zip", "shasum": "", "reference": "2d9f527449cabfa8543dd7fa3a466d6ae83d6726"}, "time": "2014-10-01T05:50:18+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "b1dbc53593b98c2d694ebf383660ac9134d30b96"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/b1dbc53593b98c2d694ebf383660ac9134d30b96", "type": "zip", "shasum": "", "reference": "b1dbc53593b98c2d694ebf383660ac9134d30b96"}, "time": "2014-09-22T09:14:18+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "01a7695bcfb013d0a15c6757e15aae120342986f"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/01a7695bcfb013d0a15c6757e15aae120342986f", "type": "zip", "shasum": "", "reference": "01a7695bcfb013d0a15c6757e15aae120342986f"}, "time": "2014-08-31T03:22:04+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "5a75366ae9ca8b4792cd0083e4ca4dff9fe96f1f"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/5a75366ae9ca8b4792cd0083e4ca4dff9fe96f1f", "type": "zip", "shasum": "", "reference": "5a75366ae9ca8b4792cd0083e4ca4dff9fe96f1f"}, "time": "2014-08-05T09:00:40+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "f868ecdbcc0276b6158dfbf08b9e98ce07f014e1"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/f868ecdbcc0276b6158dfbf08b9e98ce07f014e1", "type": "zip", "shasum": "", "reference": "f868ecdbcc0276b6158dfbf08b9e98ce07f014e1"}, "time": "2014-07-09T09:05:48+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "1057e87364c0b38b50f5695fc9df9dd189036bec"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/1057e87364c0b38b50f5695fc9df9dd189036bec", "type": "zip", "shasum": "", "reference": "1057e87364c0b38b50f5695fc9df9dd189036bec"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "b4b09c68ec2f2727574544ef0173684281a5033c"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/b4b09c68ec2f2727574544ef0173684281a5033c", "type": "zip", "shasum": "", "reference": "b4b09c68ec2f2727574544ef0173684281a5033c"}, "time": "2014-05-16T14:25:18+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "05c2435795f3362967e96d039358f046b9e19cf1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/05c2435795f3362967e96d039358f046b9e19cf1", "type": "zip", "shasum": "", "reference": "05c2435795f3362967e96d039358f046b9e19cf1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.5.0-BETA2"}, "time": "2014-04-18T20:40:13+00:00"}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "32c8010e8bf2b1445331ffeca50397e4d5428779"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/32c8010e8bf2b1445331ffeca50397e4d5428779", "type": "zip", "shasum": "", "reference": "32c8010e8bf2b1445331ffeca50397e4d5428779"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.5.0-BETA1"}, "time": "2014-03-26T11:51:10+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "297fd84aa9377e54268e9ff74cb81377350e230f"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/297fd84aa9377e54268e9ff74cb81377350e230f", "type": "zip", "shasum": "", "reference": "297fd84aa9377e54268e9ff74cb81377350e230f"}, "support": {"source": "https://github.com/symfony/Yaml/tree/2.4"}, "time": "2014-09-22T08:51:05+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "8716cb8773bd12b7879f1fdbe9fec8fc4921b224"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/8716cb8773bd12b7879f1fdbe9fec8fc4921b224", "type": "zip", "shasum": "", "reference": "8716cb8773bd12b7879f1fdbe9fec8fc4921b224"}, "time": "2014-08-31T03:18:18+00:00"}, {"version": "v2.4.8", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "e63eb0f9edbf4c48c425604a05fd4b04c8a7629a"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/e63eb0f9edbf4c48c425604a05fd4b04c8a7629a", "type": "zip", "shasum": "", "reference": "e63eb0f9edbf4c48c425604a05fd4b04c8a7629a"}, "time": "2014-07-09T09:04:55+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "e7f79223d60b10bea434d9e4ebcb47ef394d85d1"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/e7f79223d60b10bea434d9e4ebcb47ef394d85d1", "type": "zip", "shasum": "", "reference": "e7f79223d60b10bea434d9e4ebcb47ef394d85d1"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "fd22bb88c3a6f73c898b39bec185a9e211b06265"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/fd22bb88c3a6f73c898b39bec185a9e211b06265", "type": "zip", "shasum": "", "reference": "fd22bb88c3a6f73c898b39bec185a9e211b06265"}, "time": "2014-05-12T09:27:48+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "65539ecde838f9c0d18b006b2101e3deb4b5c9ff"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/65539ecde838f9c0d18b006b2101e3deb4b5c9ff", "type": "zip", "shasum": "", "reference": "65539ecde838f9c0d18b006b2101e3deb4b5c9ff"}, "time": "2014-04-18T20:37:09+00:00"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "77a41c2835ab7cfe8bf6d15e25d3af8f3eb3bacd"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/77a41c2835ab7cfe8bf6d15e25d3af8f3eb3bacd", "type": "zip", "shasum": "", "reference": "77a41c2835ab7cfe8bf6d15e25d3af8f3eb3bacd"}, "time": "2014-03-12T18:29:58+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "bb6ddaf8956139d1b8c360b4b713ed0138e876b3"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/bb6ddaf8956139d1b8c360b4b713ed0138e876b3", "type": "zip", "shasum": "", "reference": "bb6ddaf8956139d1b8c360b4b713ed0138e876b3"}, "time": "2014-01-07T13:28:54+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "4e1a237fc48145fae114b96458d799746ad89aa0"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/4e1a237fc48145fae114b96458d799746ad89aa0", "type": "zip", "shasum": "", "reference": "4e1a237fc48145fae114b96458d799746ad89aa0"}, "time": "2013-12-28T08:12:03+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "1ae235a1b9d3ad3d9f3860ff20acc072df95b7f5"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/1ae235a1b9d3ad3d9f3860ff20acc072df95b7f5", "type": "zip", "shasum": "", "reference": "1ae235a1b9d3ad3d9f3860ff20acc072df95b7f5"}, "time": "2013-11-26T16:40:27+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/Yaml.git", "type": "git", "reference": "6f5246e9aa721e17e860743838af20650778343d"}, "dist": {"url": "https://api.github.com/repos/symfony/Yaml/zipball/6f5246e9aa721e17e860743838af20650778343d", "type": "zip", "shasum": "", "reference": "6f5246e9aa721e17e860743838af20650778343d"}, "time": "2013-10-17T11:48:11+00:00"}, {"version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6f5246e9aa721e17e860743838af20650778343d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6f5246e9aa721e17e860743838af20650778343d", "type": "zip", "shasum": "", "reference": "6f5246e9aa721e17e860743838af20650778343d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.4.0-RC1"}}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9db2f621df5b40897e57d08836a5ab184188f8aa"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9db2f621df5b40897e57d08836a5ab184188f8aa", "type": "zip", "shasum": "", "reference": "9db2f621df5b40897e57d08836a5ab184188f8aa"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.4.0-BETA1"}, "time": "2013-09-22T18:04:51+00:00"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2cb5f366f9e0df014fc93de46cc416ba0a3055f8"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2cb5f366f9e0df014fc93de46cc416ba0a3055f8", "type": "zip", "shasum": "", "reference": "2cb5f366f9e0df014fc93de46cc416ba0a3055f8"}, "support": {"source": "https://github.com/symfony/yaml/tree/2.3"}, "time": "2016-05-30T08:10:17+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.3.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d5a6f4c771ae5d3faedda30eeb8db4cfb40a59fe"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d5a6f4c771ae5d3faedda30eeb8db4cfb40a59fe", "type": "zip", "shasum": "", "reference": "d5a6f4c771ae5d3faedda30eeb8db4cfb40a59fe"}, "time": "2016-03-04T07:12:08+00:00"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "af253bbd4841b3f6b088a0a35307ecc8c53da2c9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/af253bbd4841b3f6b088a0a35307ecc8c53da2c9", "type": "zip", "shasum": "", "reference": "af253bbd4841b3f6b088a0a35307ecc8c53da2c9"}, "time": "2016-02-17T21:15:58+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b202959e06e6c64c6516f19645e98fd4d9284da7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b202959e06e6c64c6516f19645e98fd4d9284da7", "type": "zip", "shasum": "", "reference": "b202959e06e6c64c6516f19645e98fd4d9284da7"}, "time": "2016-01-13T08:54:41+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8f5afcffca793f82fb28e941b6234185c099964f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8f5afcffca793f82fb28e941b6234185c099964f", "type": "zip", "shasum": "", "reference": "8f5afcffca793f82fb28e941b6234185c099964f"}, "time": "2015-12-19T09:10:51+00:00"}, {"version": "v2.3.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6d7a3b7cfdd5095e5f4318ad4c2ed20c71c74764"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6d7a3b7cfdd5095e5f4318ad4c2ed20c71c74764", "type": "zip", "shasum": "", "reference": "6d7a3b7cfdd5095e5f4318ad4c2ed20c71c74764"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d8434746e6f12b45fa1bdca1baadddd7e02b54af"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d8434746e6f12b45fa1bdca1baadddd7e02b54af", "type": "zip", "shasum": "", "reference": "d8434746e6f12b45fa1bdca1baadddd7e02b54af"}, "time": "2015-10-11T09:37:49+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Yaml\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c5bf1240e11167adb8619accc12af637b6ad8350"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c5bf1240e11167adb8619accc12af637b6ad8350", "type": "zip", "shasum": "", "reference": "c5bf1240e11167adb8619accc12af637b6ad8350"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.33"}, "time": "2015-09-13T17:26:34+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "162a8860e58bcd9d316e04e3af8ff96e63cb1cdb"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/162a8860e58bcd9d316e04e3af8ff96e63cb1cdb", "type": "zip", "shasum": "", "reference": "162a8860e58bcd9d316e04e3af8ff96e63cb1cdb"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.32"}, "time": "2015-08-11T07:25:28+00:00"}, {"version": "v2.3.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c956b2c07393b09ca8eecf3697a6d843635f355b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c956b2c07393b09ca8eecf3697a6d843635f355b", "type": "zip", "shasum": "", "reference": "c956b2c07393b09ca8eecf3697a6d843635f355b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.31"}, "time": "2015-06-30T13:39:12+00:00"}, {"version": "v2.3.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "263b44e71b25090a45cec9dc944bb89ef8bfb79c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/263b44e71b25090a45cec9dc944bb89ef8bfb79c", "type": "zip", "shasum": "", "reference": "263b44e71b25090a45cec9dc944bb89ef8bfb79c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.30"}, "time": "2015-05-01T14:06:45+00:00"}, {"version": "v2.3.29", "version_normalized": "********"}, {"version": "v2.3.28", "version_normalized": "********"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "51a25493b0413dca6a8377d8efec4e2154ee3e54"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/51a25493b0413dca6a8377d8efec4e2154ee3e54", "type": "zip", "shasum": "", "reference": "51a25493b0413dca6a8377d8efec4e2154ee3e54"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.27"}, "time": "2015-03-30T15:33:35+00:00"}, {"version": "v2.3.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "aeb83d10b5ab9c9dd9993fc340a190a935646de7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/aeb83d10b5ab9c9dd9993fc340a190a935646de7", "type": "zip", "shasum": "", "reference": "aeb83d10b5ab9c9dd9993fc340a190a935646de7"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.26"}, "time": "2015-03-07T19:12:23+00:00"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "11a44f3c52686517c12c9737126890dc232640a3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/11a44f3c52686517c12c9737126890dc232640a3", "type": "zip", "shasum": "", "reference": "11a44f3c52686517c12c9737126890dc232640a3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.25"}, "time": "2015-01-25T03:54:01+00:00", "require-dev": "__unset"}, {"version": "v2.3.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a52353bbc7b813819cec08635acd6b98b5f57e28"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a52353bbc7b813819cec08635acd6b98b5f57e28", "type": "zip", "shasum": "", "reference": "a52353bbc7b813819cec08635acd6b98b5f57e28"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.24"}, "time": "2015-01-03T10:53:56+00:00"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "12b26e23e220b65aa9eaa1b2ff1c923423d206b9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/12b26e23e220b65aa9eaa1b2ff1c923423d206b9", "type": "zip", "shasum": "", "reference": "12b26e23e220b65aa9eaa1b2ff1c923423d206b9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.23"}, "time": "2014-12-02T19:42:47+00:00"}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5ea930e341b90bf9ef1fed76ab85d551bce20bc2"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5ea930e341b90bf9ef1fed76ab85d551bce20bc2", "type": "zip", "shasum": "", "reference": "5ea930e341b90bf9ef1fed76ab85d551bce20bc2"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.22"}, "time": "2014-11-17T16:27:42+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "34687c6236f1dfcebc874fbebd8da74d90f9f64f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/34687c6236f1dfcebc874fbebd8da74d90f9f64f", "type": "zip", "shasum": "", "reference": "34687c6236f1dfcebc874fbebd8da74d90f9f64f"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.21"}, "time": "2014-10-01T05:38:33+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2f2c974f22397a072fab19f9212673f82ef15a69"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2f2c974f22397a072fab19f9212673f82ef15a69", "type": "zip", "shasum": "", "reference": "2f2c974f22397a072fab19f9212673f82ef15a69"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.20"}, "time": "2014-09-22T08:32:35+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "71ceeca3d8164f0c5313127b24ad056071ccf2c6"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/71ceeca3d8164f0c5313127b24ad056071ccf2c6", "type": "zip", "shasum": "", "reference": "71ceeca3d8164f0c5313127b24ad056071ccf2c6"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.19"}, "time": "2014-08-28T01:42:35+00:00"}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "ec2c1cd7b55321f199c3653c30ef497e852678cd"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/ec2c1cd7b55321f199c3653c30ef497e852678cd", "type": "zip", "shasum": "", "reference": "ec2c1cd7b55321f199c3653c30ef497e852678cd"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.18"}, "time": "2014-07-07T10:13:42+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "41b2e66b1f3149746a5d7c06d6148accd7ed9f89"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/41b2e66b1f3149746a5d7c06d6148accd7ed9f89", "type": "zip", "shasum": "", "reference": "41b2e66b1f3149746a5d7c06d6148accd7ed9f89"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2e257c292cfce88bf6c894a03d0fe8d782055aee"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2e257c292cfce88bf6c894a03d0fe8d782055aee", "type": "zip", "shasum": "", "reference": "2e257c292cfce88bf6c894a03d0fe8d782055aee"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.16"}, "time": "2014-05-12T09:13:35+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c10f8ed05d6e539b93eb5acc7763c9a639456c6b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c10f8ed05d6e539b93eb5acc7763c9a639456c6b", "type": "zip", "shasum": "", "reference": "c10f8ed05d6e539b93eb5acc7763c9a639456c6b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.13"}, "time": "2014-04-18T20:35:25+00:00"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3acf34f6993db3d873fa77ac2cb6e595db00b88d"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3acf34f6993db3d873fa77ac2cb6e595db00b88d", "type": "zip", "shasum": "", "reference": "3acf34f6993db3d873fa77ac2cb6e595db00b88d"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.12"}, "time": "2014-03-04T16:04:39+00:00"}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "40cd9e201a7dfef7aeae7788f7cf1463bea891db"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/40cd9e201a7dfef7aeae7788f7cf1463bea891db", "type": "zip", "shasum": "", "reference": "40cd9e201a7dfef7aeae7788f7cf1463bea891db"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.11"}, "time": "2014-02-25T21:36:55+00:00"}, {"version": "v2.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d6ce55d18a957591312a1c1dd7098d72db0e2ed5"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d6ce55d18a957591312a1c1dd7098d72db0e2ed5", "type": "zip", "shasum": "", "reference": "d6ce55d18a957591312a1c1dd7098d72db0e2ed5"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.10"}, "time": "2014-01-07T13:19:25+00:00"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "490c1c47049aa3ffab9ed6295a7b3a0e809e8519"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/490c1c47049aa3ffab9ed6295a7b3a0e809e8519", "type": "zip", "shasum": "", "reference": "490c1c47049aa3ffab9ed6295a7b3a0e809e8519"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.9"}, "time": "2013-12-28T07:46:05+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5fc7c047f9498028dd1c5031f14ae76a7aef283c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5fc7c047f9498028dd1c5031f14ae76a7aef283c", "type": "zip", "shasum": "", "reference": "5fc7c047f9498028dd1c5031f14ae76a7aef283c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.8"}, "time": "2013-11-25T14:49:41+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c1bda5b459d792cb253de12c65beba3040163b2b"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c1bda5b459d792cb253de12c65beba3040163b2b", "type": "zip", "shasum": "", "reference": "c1bda5b459d792cb253de12c65beba3040163b2b"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.7"}, "time": "2013-10-17T11:48:01+00:00"}, {"version": "v2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6bb881b948368482e1abf1a75c08bcf88a1c5fc3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6bb881b948368482e1abf1a75c08bcf88a1c5fc3", "type": "zip", "shasum": "", "reference": "6bb881b948368482e1abf1a75c08bcf88a1c5fc3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.6"}, "time": "2013-09-22T18:04:39+00:00"}, {"version": "v2.3.5", "version_normalized": "*******"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5a279f1b5f5e1045a6c432354d9ea727ff3a9847"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5a279f1b5f5e1045a6c432354d9ea727ff3a9847", "type": "zip", "shasum": "", "reference": "5a279f1b5f5e1045a6c432354d9ea727ff3a9847"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.4"}, "time": "2013-08-24T15:26:22+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3837ccd0830ef44ed5c5844403bf6f69f7998758"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3837ccd0830ef44ed5c5844403bf6f69f7998758", "type": "zip", "shasum": "", "reference": "3837ccd0830ef44ed5c5844403bf6f69f7998758"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.3"}, "time": "2013-07-21T12:12:18+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d7e5b5a1e98c37f5cf4dd35fbaa2bfb03f524477"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d7e5b5a1e98c37f5cf4dd35fbaa2bfb03f524477", "type": "zip", "shasum": "", "reference": "d7e5b5a1e98c37f5cf4dd35fbaa2bfb03f524477"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.2"}, "time": "2013-07-11T19:36:36+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "246b25461b4ea52f8d38ea655ef04b3a02b9c3b4"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/246b25461b4ea52f8d38ea655ef04b3a02b9c3b4", "type": "zip", "shasum": "", "reference": "246b25461b4ea52f8d38ea655ef04b3a02b9c3b4"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.3.1"}, "time": "2013-05-10T18:12:13+00:00"}, {"version": "v2.3.0", "version_normalized": "*******"}, {"version": "v2.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4f3569bb23953116281fb3a642c4266ce84a2f44"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4f3569bb23953116281fb3a642c4266ce84a2f44", "type": "zip", "shasum": "", "reference": "4f3569bb23953116281fb3a642c4266ce84a2f44"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.11"}, "time": "2013-11-25T10:21:43+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "a3e81e895bde9964f0956e747badf2890aed5c05"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/a3e81e895bde9964f0956e747badf2890aed5c05", "type": "zip", "shasum": "", "reference": "a3e81e895bde9964f0956e747badf2890aed5c05"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.10"}, "time": "2013-10-10T22:23:27+00:00"}, {"version": "v2.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "bb697b6617b90c91b2a65d24ce32035afe5e3418"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/bb697b6617b90c91b2a65d24ce32035afe5e3418", "type": "zip", "shasum": "", "reference": "bb697b6617b90c91b2a65d24ce32035afe5e3418"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.9"}, "time": "2013-09-22T17:30:19+00:00"}, {"version": "v2.2.8", "version_normalized": "*******"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "d135717c1a42cb566cc09433658e7e8dbbe30b0a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/d135717c1a42cb566cc09433658e7e8dbbe30b0a", "type": "zip", "shasum": "", "reference": "d135717c1a42cb566cc09433658e7e8dbbe30b0a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.6"}, "time": "2013-08-24T06:36:00+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "dbc00aea34556f3ad6c1e31819bdbd9bb898f164"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/dbc00aea34556f3ad6c1e31819bdbd9bb898f164", "type": "zip", "shasum": "", "reference": "dbc00aea34556f3ad6c1e31819bdbd9bb898f164"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.5"}, "time": "2013-07-11T09:28:01+00:00"}, {"version": "v2.2.4", "version_normalized": "*******"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "2412f9950036ade5e89581e648edbe04e8fe2681"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/2412f9950036ade5e89581e648edbe04e8fe2681", "type": "zip", "shasum": "", "reference": "2412f9950036ade5e89581e648edbe04e8fe2681"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.3"}, "time": "2013-05-10T18:08:31+00:00"}, {"version": "v2.2.2", "version_normalized": "*******"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3f6d4ab3fd8226ab4ba0be9fc8a238f4338b79ab"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3f6d4ab3fd8226ab4ba0be9fc8a238f4338b79ab", "type": "zip", "shasum": "", "reference": "3f6d4ab3fd8226ab4ba0be9fc8a238f4338b79ab"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.1"}, "time": "2013-03-23T07:49:54+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "b293027f4030998a752a1ac06e80ae9e6bf6a763"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/b293027f4030998a752a1ac06e80ae9e6bf6a763", "type": "zip", "shasum": "", "reference": "b293027f4030998a752a1ac06e80ae9e6bf6a763"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.2.0"}, "time": "2013-01-27T16:49:19+00:00"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "347a7a02204433c6926ecc3f13e805bdc30e8f9f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/347a7a02204433c6926ecc3f13e805bdc30e8f9f", "type": "zip", "shasum": "", "reference": "347a7a02204433c6926ecc3f13e805bdc30e8f9f"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.13"}, "time": "2013-05-10T00:09:46+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Yaml": ""}}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********"}, {"version": "v2.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4ef80f5c5081ad9a7e08184fc9f25c62d87e408c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4ef80f5c5081ad9a7e08184fc9f25c62d87e408c", "type": "zip", "shasum": "", "reference": "4ef80f5c5081ad9a7e08184fc9f25c62d87e408c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.10"}, "time": "2013-04-12T10:27:36+00:00"}, {"version": "v2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1238ede850fb8c6946a6c44963d38bbf9cad6dd1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1238ede850fb8c6946a6c44963d38bbf9cad6dd1", "type": "zip", "shasum": "", "reference": "1238ede850fb8c6946a6c44963d38bbf9cad6dd1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.9"}, "time": "2013-03-23T01:54:33+00:00"}, {"version": "v2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "882b70fb7072e3f1fa95d249fd527a4e3998dc1a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/882b70fb7072e3f1fa95d249fd527a4e3998dc1a", "type": "zip", "shasum": "", "reference": "882b70fb7072e3f1fa95d249fd527a4e3998dc1a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.8"}, "time": "2013-01-27T16:12:43+00:00"}, {"version": "v2.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "1ab2bd470518b75daca8b43d4293b4c0fafe6bca"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/1ab2bd470518b75daca8b43d4293b4c0fafe6bca", "type": "zip", "shasum": "", "reference": "1ab2bd470518b75daca8b43d4293b4c0fafe6bca"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.7"}, "time": "2013-01-17T21:21:51+00:00"}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "4fdae9fa28b7abd1135afc5227a4e59af4b64e5a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/4fdae9fa28b7abd1135afc5227a4e59af4b64e5a", "type": "zip", "shasum": "", "reference": "4fdae9fa28b7abd1135afc5227a4e59af4b64e5a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.6"}, "time": "2012-12-06T10:00:55+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e1aff6871fd39607d7ce638ff6b1f971cde6fe6a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e1aff6871fd39607d7ce638ff6b1f971cde6fe6a", "type": "zip", "shasum": "", "reference": "e1aff6871fd39607d7ce638ff6b1f971cde6fe6a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.4"}, "time": "2012-11-08T09:51:48+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "9f3d53aee27329fc2b2294cd032454384059aa72"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/9f3d53aee27329fc2b2294cd032454384059aa72", "type": "zip", "shasum": "", "reference": "9f3d53aee27329fc2b2294cd032454384059aa72"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.3"}, "time": "2012-10-29T11:15:41+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "f18e004fc975707bb4695df1dbbe9b0d8c8b7715"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/f18e004fc975707bb4695df1dbbe9b0d8c8b7715", "type": "zip", "shasum": "", "reference": "f18e004fc975707bb4695df1dbbe9b0d8c8b7715"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.1.2"}, "time": "2012-08-22T13:48:41+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******"}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "c508a879829976e30f05e16b96a7ed024ee82ce1"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/c508a879829976e30f05e16b96a7ed024ee82ce1", "type": "zip", "shasum": "", "reference": "c508a879829976e30f05e16b96a7ed024ee82ce1"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.25"}, "time": "2013-01-18T09:52:07+00:00", "require": {"php": ">=5.3.2"}, "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********"}, {"version": "v2.0.23", "version_normalized": "********"}, {"version": "v2.0.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "67069f62cf99dec4c6263579a472c6175a7ab2b3"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/67069f62cf99dec4c6263579a472c6175a7ab2b3", "type": "zip", "shasum": "", "reference": "67069f62cf99dec4c6263579a472c6175a7ab2b3"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.22"}, "time": "2013-01-17T21:16:19+00:00"}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "04edc329027cf987462fa4d74c1112d186212f4f"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/04edc329027cf987462fa4d74c1112d186212f4f", "type": "zip", "shasum": "", "reference": "04edc329027cf987462fa4d74c1112d186212f4f"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.21"}, "time": "2012-07-09T12:43:50+00:00"}, {"version": "v2.0.20", "version_normalized": "********"}, {"version": "v2.0.19", "version_normalized": "********"}, {"version": "v2.0.18", "version_normalized": "********"}, {"version": "v2.0.17", "version_normalized": "********"}, {"version": "v2.0.16", "version_normalized": "********"}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "7eb70264627e696aa7d2825a88d7cef053e8a302"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/7eb70264627e696aa7d2825a88d7cef053e8a302", "type": "zip", "shasum": "", "reference": "7eb70264627e696aa7d2825a88d7cef053e8a302"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.15"}, "time": "2012-05-18T17:37:58+00:00"}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "8c927aeea740bd14f5ea7b5d1e169da30f66b2f9"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/8c927aeea740bd14f5ea7b5d1e169da30f66b2f9", "type": "zip", "shasum": "", "reference": "8c927aeea740bd14f5ea7b5d1e169da30f66b2f9"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.14"}, "time": "2012-05-15T16:56:32+00:00"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "3e0a19133730f60ad0fb3b9190208eaa9475d4d7"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/3e0a19133730f60ad0fb3b9190208eaa9475d4d7", "type": "zip", "shasum": "", "reference": "3e0a19133730f60ad0fb3b9190208eaa9475d4d7"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.13"}, "time": "2012-02-22T09:10:37+00:00"}, {"version": "v2.0.12", "version_normalized": "********"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "5a83afdb51c116320b0e41b9f1265eabc5605f27"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/5a83afdb51c116320b0e41b9f1265eabc5605f27", "type": "zip", "shasum": "", "reference": "5a83afdb51c116320b0e41b9f1265eabc5605f27"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.10"}, "time": "2012-01-13T15:48:38+00:00"}, {"version": "v2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6de6e5a9c77406ea5fa5ee030a45f3717b55279c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6de6e5a9c77406ea5fa5ee030a45f3717b55279c", "type": "zip", "shasum": "", "reference": "6de6e5a9c77406ea5fa5ee030a45f3717b55279c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.9"}, "time": "2012-01-05T13:51:20+00:00"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6756c8f3c03cfbf4edb3a7ed1e9214cfd0e79e9a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6756c8f3c03cfbf4edb3a7ed1e9214cfd0e79e9a", "type": "zip", "shasum": "", "reference": "6756c8f3c03cfbf4edb3a7ed1e9214cfd0e79e9a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.7"}, "time": "2011-11-17T05:58:47+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "21cb2c0816ce88e794cc59f8401c10b73ef5c76c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/21cb2c0816ce88e794cc59f8401c10b73ef5c76c", "type": "zip", "shasum": "", "reference": "21cb2c0816ce88e794cc59f8401c10b73ef5c76c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.6"}, "time": "2011-11-04T07:47:39+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "6d7a0b450ff7c77664a1396a95df57ebc64c355c"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/6d7a0b450ff7c77664a1396a95df57ebc64c355c", "type": "zip", "shasum": "", "reference": "6d7a0b450ff7c77664a1396a95df57ebc64c355c"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/yaml.git", "type": "git", "reference": "e60c0d68640e662e88d879f0df139e022335a92a"}, "dist": {"url": "https://api.github.com/repos/symfony/yaml/zipball/e60c0d68640e662e88d879f0df139e022335a92a", "type": "zip", "shasum": "", "reference": "e60c0d68640e662e88d879f0df139e022335a92a"}, "support": {"source": "https://github.com/symfony/yaml/tree/v2.0.4"}, "time": "2011-09-26T22:55:43+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [{"advisoryId": "PKSA-4y51-dgkc-x4zc", "affectedVersions": ">=2.0.0,<2.0.22"}, {"advisoryId": "PKSA-xxgb-wq2d-7gpg", "affectedVersions": ">=2.0.0,<2.0.22|>=2.1.0,<2.1.7"}], "last-modified": "Sat, 28 Jun 2025 08:28:32 GMT"}