{"minified": "composer/2.0", "packages": {"athletic/athletic": [{"name": "athletic/athletic", "description": "PHP Benchmarking Framework", "keywords": ["profiling", "benchmarking", "benchmark"], "homepage": "", "version": "v0.1.8", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "51fe4b6e5298dd8af187825a4e57745898e37f0e"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/51fe4b6e5298dd8af187825a4e57745898e37f0e", "type": "zip", "shasum": "", "reference": "51fe4b6e5298dd8af187825a4e57745898e37f0e"}, "type": "library", "time": "2014-06-03T18:32:22+00:00", "autoload": {"psr-0": {"Athletic": "src/"}}, "bin": ["bin/athletic"], "require": {"php": ">=5.3.9", "zeptech/annotations": "1.1.*", "nategood/commando": "0.2.1", "pimple/pimple": ">=1.0,<3.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "mikey179/vfsstream": "1.2.*", "mockery/mockery": "0.8.*", "satooshi/php-coveralls": "0.6.*"}, "abandoned": true, "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/v0.1.8"}}, {"version": "v0.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "43fcde883cc511ec0c10f48c2adb685a94a89669"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/43fcde883cc511ec0c10f48c2adb685a94a89669", "type": "zip", "shasum": "", "reference": "43fcde883cc511ec0c10f48c2adb685a94a89669"}, "time": "2013-11-10T15:03:50+00:00", "autoload": {"psr-0": {"Athletic": "src/", "Athletic/Tests": "tests/"}}, "require": {"php": ">=5.3.0", "zeptech/annotations": "1.1.0", "nategood/commando": "0.2.1", "pimple/pimple": "v1.0.2"}, "require-dev": {"phpunit/phpunit": "3.7.*", "mikey179/vfsstream": "1.3.*@dev", "mockery/mockery": "dev-master@dev", "satooshi/php-coveralls": "dev-master"}, "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/v0.1.7"}}, {"version": "v0.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "e2249a46b90b0ab7c56606975026a31e7e338c24"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/e2249a46b90b0ab7c56606975026a31e7e338c24", "type": "zip", "shasum": "", "reference": "e2249a46b90b0ab7c56606975026a31e7e338c24"}, "time": "2013-08-02T23:09:46+00:00", "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/master"}}, {"version": "v0.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "59b95aaa1be889991151fdf8b3f898e6705c9763"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/59b95aaa1be889991151fdf8b3f898e6705c9763", "type": "zip", "shasum": "", "reference": "59b95aaa1be889991151fdf8b3f898e6705c9763"}, "time": "2013-06-28T13:31:06+00:00", "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/v0.1.5"}, "bin": "__unset"}, {"version": "v0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "cf43305530194af77943a7465156664fa6672068"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/cf43305530194af77943a7465156664fa6672068", "type": "zip", "shasum": "", "reference": "cf43305530194af77943a7465156664fa6672068"}, "time": "2013-06-28T13:11:05+00:00", "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/v0.1.4"}}, {"version": "v0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "f5b2e3aa4c5b11236227466f561b10b0e8de986d"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/f5b2e3aa4c5b11236227466f561b10b0e8de986d", "type": "zip", "shasum": "", "reference": "f5b2e3aa4c5b11236227466f561b10b0e8de986d"}, "time": "2013-06-23T02:49:17+00:00", "require-dev": {"phpunit/phpunit": "3.7.*", "mikey179/vfsstream": "1.3.*@dev", "mockery/mockery": "dev-master@dev"}, "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/v0.1.3"}}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "117b9a3de2e98675ab4c207361314c82cd66da97"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/117b9a3de2e98675ab4c207361314c82cd66da97", "type": "zip", "shasum": "", "reference": "117b9a3de2e98675ab4c207361314c82cd66da97"}, "time": "2013-06-23T02:28:04+00:00", "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/master"}}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "f2de738686eb94fdf216b10c4e14a7c58e2f7da7"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/f2de738686eb94fdf216b10c4e14a7c58e2f7da7", "type": "zip", "shasum": "", "reference": "f2de738686eb94fdf216b10c4e14a7c58e2f7da7"}, "time": "2013-06-16T03:13:42+00:00", "autoload": {"psr-0": {"Athletic": "src/"}}, "require-dev": "__unset"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/polyfractal/athletic.git", "type": "git", "reference": "c37250f29a34d18950f263b432c4f6fddbf33257"}, "dist": {"url": "https://api.github.com/repos/polyfractal/athletic/zipball/c37250f29a34d18950f263b432c4f6fddbf33257", "type": "zip", "shasum": "", "reference": "c37250f29a34d18950f263b432c4f6fddbf33257"}, "time": "2013-06-14T18:26:06+00:00", "require": {"php": ">=5.3.0", "zeptech/annotations": "1.1.0", "nategood/commando": "0.2.1"}, "support": {"issues": "https://github.com/polyfractal/athletic/issues", "source": "https://github.com/polyfractal/athletic/tree/v0.1.0"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:13:11 GMT"}