{"minified": "composer/2.0", "packages": {"phpseclib/phpseclib": [{"name": "phpseclib/phpseclib", "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "keywords": ["security", "cryptography", "ssh", "encryption", "blowfish", "crypto", "signature", "signing", "rsa", "aes", "sftp", "x509", "x.509", "asn1", "asn.1", "BigInteger", "twofish"], "homepage": "http://phpseclib.sourceforge.net", "version": "3.0.46", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "56483a7de62a6c2a6635e42e93b8a9e25d4f0ec6"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/56483a7de62a6c2a6635e42e93b8a9e25d4f0ec6", "type": "zip", "shasum": "", "reference": "56483a7de62a6c2a6635e42e93b8a9e25d4f0ec6"}, "type": "library", "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.46"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2025-06-26T16:29:55+00:00", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "require": {"php": ">=5.6.1", "paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-dom": "Install the DOM extension to load XML formatted public keys."}}, {"version": "3.0.45", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "bd81b90d5963c6b9d87de50357585375223f4dd8"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/bd81b90d5963c6b9d87de50357585375223f4dd8", "type": "zip", "shasum": "", "reference": "bd81b90d5963c6b9d87de50357585375223f4dd8"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.45"}, "time": "2025-06-22T22:54:43+00:00"}, {"version": "3.0.44", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "1d0b5e7e1434678411787c5a0535e68907cf82d9"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/1d0b5e7e1434678411787c5a0535e68907cf82d9", "type": "zip", "shasum": "", "reference": "1d0b5e7e1434678411787c5a0535e68907cf82d9"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.44"}, "time": "2025-06-15T09:59:26+00:00"}, {"version": "3.0.43", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "type": "zip", "shasum": "", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"}, "time": "2024-12-14T21:12:59+00:00"}, {"version": "3.0.42", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "db92f1b1987b12b13f248fe76c3a52cadb67bb98"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/db92f1b1987b12b13f248fe76c3a52cadb67bb98", "type": "zip", "shasum": "", "reference": "db92f1b1987b12b13f248fe76c3a52cadb67bb98"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.42"}, "time": "2024-09-16T03:06:04+00:00"}, {"version": "3.0.41", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "621c73f7dcb310b61de34d1da4c4204e8ace6ceb"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/621c73f7dcb310b61de34d1da4c4204e8ace6ceb", "type": "zip", "shasum": "", "reference": "621c73f7dcb310b61de34d1da4c4204e8ace6ceb"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.41"}, "time": "2024-08-12T00:13:54+00:00"}, {"version": "3.0.40", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "3dd2561d14ed48c03fc30322df1ad46dbac85ea6"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/3dd2561d14ed48c03fc30322df1ad46dbac85ea6", "type": "zip", "shasum": "", "reference": "3dd2561d14ed48c03fc30322df1ad46dbac85ea6"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.40"}, "time": "2024-08-11T16:34:00+00:00"}, {"version": "3.0.39", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "211ebc399c6e73c225a018435fe5ae209d1d1485"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/211ebc399c6e73c225a018435fe5ae209d1d1485", "type": "zip", "shasum": "", "reference": "211ebc399c6e73c225a018435fe5ae209d1d1485"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.39"}, "time": "2024-06-24T06:27:33+00:00"}, {"version": "3.0.38", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "b18b8788e51156c4dd97b7f220a31149a0052067"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b18b8788e51156c4dd97b7f220a31149a0052067", "type": "zip", "shasum": "", "reference": "b18b8788e51156c4dd97b7f220a31149a0052067"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.38"}, "time": "2024-06-17T10:11:32+00:00"}, {"version": "3.0.37", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "cfa2013d0f68c062055180dd4328cc8b9d1f30b8"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/cfa2013d0f68c062055180dd4328cc8b9d1f30b8", "type": "zip", "shasum": "", "reference": "cfa2013d0f68c062055180dd4328cc8b9d1f30b8"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.37"}, "time": "2024-03-03T02:14:58+00:00", "require": {"php": ">=5.6.1", "paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99"}}, {"version": "3.0.36", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c2fb5136162d4be18fdd4da9980696f3aee96d7b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c2fb5136162d4be18fdd4da9980696f3aee96d7b", "type": "zip", "shasum": "", "reference": "c2fb5136162d4be18fdd4da9980696f3aee96d7b"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.36"}, "time": "2024-02-26T05:13:14+00:00"}, {"version": "3.0.35", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "4b1827beabce71953ca479485c0ae9c51287f2fe"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/4b1827beabce71953ca479485c0ae9c51287f2fe", "type": "zip", "shasum": "", "reference": "4b1827beabce71953ca479485c0ae9c51287f2fe"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.35"}, "time": "2023-12-29T01:59:53+00:00"}, {"version": "3.0.34", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "56c79f16a6ae17e42089c06a2144467acc35348a"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/56c79f16a6ae17e42089c06a2144467acc35348a", "type": "zip", "shasum": "", "reference": "56c79f16a6ae17e42089c06a2144467acc35348a"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.34"}, "time": "2023-11-27T11:13:31+00:00"}, {"version": "3.0.33", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "33fa69b2514a61138dd48e7a49f99445711e0ad0"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/33fa69b2514a61138dd48e7a49f99445711e0ad0", "type": "zip", "shasum": "", "reference": "33fa69b2514a61138dd48e7a49f99445711e0ad0"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.33"}, "time": "2023-10-21T14:00:39+00:00"}, {"version": "3.0.23", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "866cc78fbd82462ffd880e3f65692afe928bed50"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/866cc78fbd82462ffd880e3f65692afe928bed50", "type": "zip", "shasum": "", "reference": "866cc78fbd82462ffd880e3f65692afe928bed50"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.23"}, "time": "2023-09-18T17:22:01+00:00"}, {"version": "3.0.22", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "b6bd1c5f79b2c39e144770eb6d9180fbdb00d09b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b6bd1c5f79b2c39e144770eb6d9180fbdb00d09b", "type": "zip", "shasum": "", "reference": "b6bd1c5f79b2c39e144770eb6d9180fbdb00d09b"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.22"}, "time": "2023-09-16T11:49:37+00:00"}, {"version": "3.0.21", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "4580645d3fc05c189024eb3b834c6c1e4f0f30a1"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/4580645d3fc05c189024eb3b834c6c1e4f0f30a1", "type": "zip", "shasum": "", "reference": "4580645d3fc05c189024eb3b834c6c1e4f0f30a1"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.21"}, "time": "2023-07-09T15:24:48+00:00"}, {"version": "3.0.20", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "543a1da81111a0bfd6ae7bbc2865c5e89ed3fc67"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/543a1da81111a0bfd6ae7bbc2865c5e89ed3fc67", "type": "zip", "shasum": "", "reference": "543a1da81111a0bfd6ae7bbc2865c5e89ed3fc67"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.20"}, "time": "2023-06-13T06:30:34+00:00"}, {"version": "3.0.19", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "cc181005cf548bfd8a4896383bb825d859259f95"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/cc181005cf548bfd8a4896383bb825d859259f95", "type": "zip", "shasum": "", "reference": "cc181005cf548bfd8a4896383bb825d859259f95"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.19"}, "time": "2023-03-05T17:13:09+00:00"}, {"version": "3.0.18", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "f28693d38ba21bb0d9f0c411ee5dae2b178201da"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/f28693d38ba21bb0d9f0c411ee5dae2b178201da", "type": "zip", "shasum": "", "reference": "f28693d38ba21bb0d9f0c411ee5dae2b178201da"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.18"}, "time": "2022-12-17T18:26:50+00:00"}, {"version": "3.0.17", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "dbc2307d5c69aeb22db136c52e91130d7f2ca761"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/dbc2307d5c69aeb22db136c52e91130d7f2ca761", "type": "zip", "shasum": "", "reference": "dbc2307d5c69aeb22db136c52e91130d7f2ca761"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.17"}, "time": "2022-10-24T10:51:50+00:00"}, {"version": "3.0.16", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7181378909ed8890be4db53d289faac5b77f8b05"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7181378909ed8890be4db53d289faac5b77f8b05", "type": "zip", "shasum": "", "reference": "7181378909ed8890be4db53d289faac5b77f8b05"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.16"}, "time": "2022-09-05T18:03:08+00:00"}, {"version": "3.0.15", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c96e250238e88bf1040e9f7715efab1d6bc7f622"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c96e250238e88bf1040e9f7715efab1d6bc7f622", "type": "zip", "shasum": "", "reference": "c96e250238e88bf1040e9f7715efab1d6bc7f622"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.15"}, "time": "2022-09-02T17:05:08+00:00"}, {"version": "3.0.14", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "2f0b7af658cbea265cbb4a791d6c29a6613f98ef"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/2f0b7af658cbea265cbb4a791d6c29a6613f98ef", "type": "zip", "shasum": "", "reference": "2f0b7af658cbea265cbb4a791d6c29a6613f98ef"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.14"}, "time": "2022-04-04T05:15:45+00:00", "suggest": {"ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations."}}, {"version": "3.0.13", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "1443ab79364eea48665fa8c09ac67f37d1025f7e"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/1443ab79364eea48665fa8c09ac67f37d1025f7e", "type": "zip", "shasum": "", "reference": "1443ab79364eea48665fa8c09ac67f37d1025f7e"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.13"}, "time": "2022-01-30T08:50:05+00:00", "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "3.0.12", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "89bfb45bd8b1abc3b37e910d57f5dbd3174f40fb"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/89bfb45bd8b1abc3b37e910d57f5dbd3174f40fb", "type": "zip", "shasum": "", "reference": "89bfb45bd8b1abc3b37e910d57f5dbd3174f40fb"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.12"}, "time": "2021-11-28T23:46:03+00:00"}, {"version": "3.0.11", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "6e794226a35159eb06f355efe59a0075a16551dd"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/6e794226a35159eb06f355efe59a0075a16551dd", "type": "zip", "shasum": "", "reference": "6e794226a35159eb06f355efe59a0075a16551dd"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.11"}, "time": "2021-10-27T03:01:46+00:00"}, {"version": "3.0.10", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "62fcc5a94ac83b1506f52d7558d828617fac9187"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/62fcc5a94ac83b1506f52d7558d828617fac9187", "type": "zip", "shasum": "", "reference": "62fcc5a94ac83b1506f52d7558d828617fac9187"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.10"}, "time": "2021-08-16T04:24:45+00:00"}, {"version": "3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "a127a5133804ff2f47ae629dd529b129da616ad7"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/a127a5133804ff2f47ae629dd529b129da616ad7", "type": "zip", "shasum": "", "reference": "a127a5133804ff2f47ae629dd529b129da616ad7"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.9"}, "time": "2021-06-14T06:54:45+00:00"}, {"version": "3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d9615a6fb970d9933866ca8b4036ec3407b020b6"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d9615a6fb970d9933866ca8b4036ec3407b020b6", "type": "zip", "shasum": "", "reference": "d9615a6fb970d9933866ca8b4036ec3407b020b6"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.8"}, "time": "2021-04-19T03:20:48+00:00"}, {"version": "3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d369510df0ebd5e1a5d0fe3d4d23c55fa87a403d"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d369510df0ebd5e1a5d0fe3d4d23c55fa87a403d", "type": "zip", "shasum": "", "reference": "d369510df0ebd5e1a5d0fe3d4d23c55fa87a403d"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.7"}, "time": "2021-04-06T14:00:11+00:00"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "906a5fafabe5e6ba51ef3dc65b2722a677908837"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/906a5fafabe5e6ba51ef3dc65b2722a677908837", "type": "zip", "shasum": "", "reference": "906a5fafabe5e6ba51ef3dc65b2722a677908837"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.6"}, "time": "2021-03-10T13:58:31+00:00"}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7c751ea006577e4c2e83326d90c8b1e8c11b8ede"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7c751ea006577e4c2e83326d90c8b1e8c11b8ede", "type": "zip", "shasum": "", "reference": "7c751ea006577e4c2e83326d90c8b1e8c11b8ede"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.5"}, "time": "2021-02-12T16:18:16+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "845a2275e886ba9fb386c8f59cb383dd9c8963e9"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/845a2275e886ba9fb386c8f59cb383dd9c8963e9", "type": "zip", "shasum": "", "reference": "845a2275e886ba9fb386c8f59cb383dd9c8963e9"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.4"}, "time": "2021-01-25T19:02:05+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "97a5a270e4a9ebfc1a7e2f462e917cbce1a8e6d9"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/97a5a270e4a9ebfc1a7e2f462e917cbce1a8e6d9", "type": "zip", "shasum": "", "reference": "97a5a270e4a9ebfc1a7e2f462e917cbce1a8e6d9"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.3"}, "time": "2021-01-16T17:35:19+00:00", "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0", "php": ">=5.6.1"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7a9418e4e02a3da7950b6b85c30e694d68daf995"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7a9418e4e02a3da7950b6b85c30e694d68daf995", "type": "zip", "shasum": "", "reference": "7a9418e4e02a3da7950b6b85c30e694d68daf995"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.2"}, "time": "2020-12-23T16:39:00+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "094bcbdd97e04fe4032b7962434d76f588e15f66"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/094bcbdd97e04fe4032b7962434d76f588e15f66", "type": "zip", "shasum": "", "reference": "094bcbdd97e04fe4032b7962434d76f588e15f66"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.1"}, "time": "2020-12-19T07:52:34+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "fe62c85e0203503231d489af95d0ac053b7d3575"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/fe62c85e0203503231d489af95d0ac053b7d3575", "type": "zip", "shasum": "", "reference": "fe62c85e0203503231d489af95d0ac053b7d3575"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.0"}, "time": "2020-12-17T06:10:28+00:00"}, {"version": "2.0.48", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "eaa7be704b8b93a6913b69eb7f645a59d7731b61"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/eaa7be704b8b93a6913b69eb7f645a59d7731b61", "type": "zip", "shasum": "", "reference": "eaa7be704b8b93a6913b69eb7f645a59d7731b61"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.48"}, "time": "2024-12-14T21:03:54+00:00", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys."}}, {"version": "2.0.47", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "b7d7d90ee7df7f33a664b4aea32d50a305d35adb"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b7d7d90ee7df7f33a664b4aea32d50a305d35adb", "type": "zip", "shasum": "", "reference": "b7d7d90ee7df7f33a664b4aea32d50a305d35adb"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.47"}, "time": "2024-02-26T04:55:38+00:00"}, {"version": "2.0.46", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "498e67a0c82bd5791fda9b0dd0f4ec8e8aebb02d"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/498e67a0c82bd5791fda9b0dd0f4ec8e8aebb02d", "type": "zip", "shasum": "", "reference": "498e67a0c82bd5791fda9b0dd0f4ec8e8aebb02d"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.46"}, "time": "2023-12-29T01:52:43+00:00"}, {"version": "2.0.45", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "28d8f438a0064c9de80857e3270d071495544640"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/28d8f438a0064c9de80857e3270d071495544640", "type": "zip", "shasum": "", "reference": "28d8f438a0064c9de80857e3270d071495544640"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.45"}, "time": "2023-09-15T20:55:47+00:00"}, {"version": "2.0.44", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "149f608243f8133c61926aae26ce67d2b22b37e5"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/149f608243f8133c61926aae26ce67d2b22b37e5", "type": "zip", "shasum": "", "reference": "149f608243f8133c61926aae26ce67d2b22b37e5"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.44"}, "time": "2023-06-13T08:41:47+00:00"}, {"version": "2.0.43", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "87b6bb4b481c7cfed8526f3911127a270b42e5b6"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/87b6bb4b481c7cfed8526f3911127a270b42e5b6", "type": "zip", "shasum": "", "reference": "87b6bb4b481c7cfed8526f3911127a270b42e5b6"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.43"}, "time": "2023-06-13T06:30:01+00:00"}, {"version": "2.0.42", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "665d289f59e646a259ebf13f29be7f6f54cab24b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/665d289f59e646a259ebf13f29be7f6f54cab24b", "type": "zip", "shasum": "", "reference": "665d289f59e646a259ebf13f29be7f6f54cab24b"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.42"}, "time": "2023-03-06T12:45:53+00:00"}, {"version": "2.0.41", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7e763c6f97ec1fcb37c46aa8ecfc20a2c71d9c1b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7e763c6f97ec1fcb37c46aa8ecfc20a2c71d9c1b", "type": "zip", "shasum": "", "reference": "7e763c6f97ec1fcb37c46aa8ecfc20a2c71d9c1b"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.41"}, "time": "2022-12-23T16:44:18+00:00"}, {"version": "2.0.40", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "5ef6f8376ddad21f3ce1da429950f7e00ec2292c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/5ef6f8376ddad21f3ce1da429950f7e00ec2292c", "type": "zip", "shasum": "", "reference": "5ef6f8376ddad21f3ce1da429950f7e00ec2292c"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.40"}, "time": "2022-12-17T17:22:59+00:00"}, {"version": "2.0.39", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "f3a0e2b715c40cf1fd270d444901b63311725d63"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/f3a0e2b715c40cf1fd270d444901b63311725d63", "type": "zip", "shasum": "", "reference": "f3a0e2b715c40cf1fd270d444901b63311725d63"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.39"}, "time": "2022-10-24T10:49:03+00:00"}, {"version": "2.0.38", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "b03536539f43a4f9aa33c4f0b2f3a1c752088fcd"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b03536539f43a4f9aa33c4f0b2f3a1c752088fcd", "type": "zip", "shasum": "", "reference": "b03536539f43a4f9aa33c4f0b2f3a1c752088fcd"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.38"}, "time": "2022-09-02T17:04:26+00:00"}, {"version": "2.0.37", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c812fbb4d6b4d7f30235ab7298a12f09ba13b37c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c812fbb4d6b4d7f30235ab7298a12f09ba13b37c", "type": "zip", "shasum": "", "reference": "c812fbb4d6b4d7f30235ab7298a12f09ba13b37c"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.37"}, "time": "2022-04-04T04:57:45+00:00", "suggest": {"ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations."}}, {"version": "2.0.36", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "a97547126396548c224703a267a30af1592be146"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/a97547126396548c224703a267a30af1592be146", "type": "zip", "shasum": "", "reference": "a97547126396548c224703a267a30af1592be146"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.36"}, "time": "2022-01-30T08:48:36+00:00"}, {"version": "2.0.35", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "4e16cf3f5f927a7d3f5317820af795c0366c0420"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/4e16cf3f5f927a7d3f5317820af795c0366c0420", "type": "zip", "shasum": "", "reference": "4e16cf3f5f927a7d3f5317820af795c0366c0420"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.35"}, "time": "2021-11-28T23:30:39+00:00"}, {"version": "2.0.34", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "98a6fe587f3481aea319eef7e656d02cfe1675ec"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/98a6fe587f3481aea319eef7e656d02cfe1675ec", "type": "zip", "shasum": "", "reference": "98a6fe587f3481aea319eef7e656d02cfe1675ec"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.34"}, "time": "2021-10-27T02:46:30+00:00"}, {"version": "2.0.33", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "fb53b7889497ec7c1362c94e61d8127ac67ea094"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/fb53b7889497ec7c1362c94e61d8127ac67ea094", "type": "zip", "shasum": "", "reference": "fb53b7889497ec7c1362c94e61d8127ac67ea094"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.33"}, "time": "2021-08-16T04:20:12+00:00"}, {"version": "2.0.32", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "f5c4c19880d45d0be3e7d24ae8ac434844a898cd"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/f5c4c19880d45d0be3e7d24ae8ac434844a898cd", "type": "zip", "shasum": "", "reference": "f5c4c19880d45d0be3e7d24ae8ac434844a898cd"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.32"}, "time": "2021-06-12T12:12:59+00:00"}, {"version": "2.0.31", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "233a920cb38636a43b18d428f9a8db1f0a1a08f4"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/233a920cb38636a43b18d428f9a8db1f0a1a08f4", "type": "zip", "shasum": "", "reference": "233a920cb38636a43b18d428f9a8db1f0a1a08f4"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.31"}, "time": "2021-04-06T13:56:45+00:00"}, {"version": "2.0.30", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "136b9ca7eebef78be14abf90d65c5e57b6bc5d36"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/136b9ca7eebef78be14abf90d65c5e57b6bc5d36", "type": "zip", "shasum": "", "reference": "136b9ca7eebef78be14abf90d65c5e57b6bc5d36"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.30"}, "time": "2020-12-17T05:42:04+00:00"}, {"version": "2.0.29", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "497856a8d997f640b4a516062f84228a772a48a8"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/497856a8d997f640b4a516062f84228a772a48a8", "type": "zip", "shasum": "", "reference": "497856a8d997f640b4a516062f84228a772a48a8"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0"}, "time": "2020-09-08T04:24:43+00:00", "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "2.0.28", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d1ca58cf33cb21046d702ae3a7b14fdacd9f3260"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d1ca58cf33cb21046d702ae3a7b14fdacd9f3260", "type": "zip", "shasum": "", "reference": "d1ca58cf33cb21046d702ae3a7b14fdacd9f3260"}, "time": "2020-07-08T09:08:33+00:00", "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "2.0.27", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "34620af4df7d1988d8f0d7e91f6c8a3bf931d8dc"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/34620af4df7d1988d8f0d7e91f6c8a3bf931d8dc", "type": "zip", "shasum": "", "reference": "34620af4df7d1988d8f0d7e91f6c8a3bf931d8dc"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.27"}, "time": "2020-04-04T23:17:33+00:00"}, {"version": "2.0.26", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "09655fcc1f8bab65727be036b28f6f20311c126c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/09655fcc1f8bab65727be036b28f6f20311c126c", "type": "zip", "shasum": "", "reference": "09655fcc1f8bab65727be036b28f6f20311c126c"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0"}, "time": "2020-03-13T04:15:39+00:00"}, {"version": "2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c18159618ed7cd7ff721ac1a8fec7860a475d2f0"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c18159618ed7cd7ff721ac1a8fec7860a475d2f0", "type": "zip", "shasum": "", "reference": "c18159618ed7cd7ff721ac1a8fec7860a475d2f0"}, "funding": [], "time": "2020-02-25T04:16:50+00:00"}, {"version": "2.0.24", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "40998159a0907cc523e1f2d1904d45765613a617"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/40998159a0907cc523e1f2d1904d45765613a617", "type": "zip", "shasum": "", "reference": "40998159a0907cc523e1f2d1904d45765613a617"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.24"}, "time": "2020-02-04T12:15:04+00:00"}, {"version": "2.0.23", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c78eb5058d5bb1a183133c36d4ba5b6675dfa099"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c78eb5058d5bb1a183133c36d4ba5b6675dfa099", "type": "zip", "shasum": "", "reference": "c78eb5058d5bb1a183133c36d4ba5b6675dfa099"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0"}, "time": "2019-09-17T03:41:22+00:00", "funding": "__unset"}, {"version": "2.0.22", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "cbddb5244edff6d2599977f1a1e4c8657f292e7c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/cbddb5244edff6d2599977f1a1e4c8657f292e7c", "type": "zip", "shasum": "", "reference": "cbddb5244edff6d2599977f1a1e4c8657f292e7c"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.22"}, "time": "2019-09-15T22:48:44+00:00"}, {"version": "2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "9f1287e68b3f283339a9f98f67515dd619e5bf9d"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/9f1287e68b3f283339a9f98f67515dd619e5bf9d", "type": "zip", "shasum": "", "reference": "9f1287e68b3f283339a9f98f67515dd619e5bf9d"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.21"}, "time": "2019-07-12T12:53:49+00:00"}, {"version": "2.0.20", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d6819a55b05e123db1e881d8b230d57f912126be"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d6819a55b05e123db1e881d8b230d57f912126be", "type": "zip", "shasum": "", "reference": "d6819a55b05e123db1e881d8b230d57f912126be"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0"}, "time": "2019-06-23T16:33:11+00:00"}, {"version": "2.0.19", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d2085db7b7394baa071a69c8f9159723c250f2ba"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d2085db7b7394baa071a69c8f9159723c250f2ba", "type": "zip", "shasum": "", "reference": "d2085db7b7394baa071a69c8f9159723c250f2ba"}, "time": "2019-06-20T03:34:11+00:00"}, {"version": "2.0.18", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "60519001db8d791215a822efd366d24cafee9e63"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/60519001db8d791215a822efd366d24cafee9e63", "type": "zip", "shasum": "", "reference": "60519001db8d791215a822efd366d24cafee9e63"}, "time": "2019-06-13T06:15:54+00:00"}, {"version": "2.0.17", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "3ca5b88d582c69178c8d01fd9d02b94e15042bb8"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/3ca5b88d582c69178c8d01fd9d02b94e15042bb8", "type": "zip", "shasum": "", "reference": "3ca5b88d582c69178c8d01fd9d02b94e15042bb8"}, "time": "2019-05-26T20:38:34+00:00"}, {"version": "2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "fae6542efcd33cf77fb32cd554678cc2466d0d8b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/fae6542efcd33cf77fb32cd554678cc2466d0d8b", "type": "zip", "shasum": "", "reference": "fae6542efcd33cf77fb32cd554678cc2466d0d8b"}, "time": "2019-05-26T17:17:27+00:00"}, {"version": "2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "11cf67cf78dc4acb18dc9149a57be4aee5036ce0"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/11cf67cf78dc4acb18dc9149a57be4aee5036ce0", "type": "zip", "shasum": "", "reference": "11cf67cf78dc4acb18dc9149a57be4aee5036ce0"}, "time": "2019-03-10T16:53:45+00:00"}, {"version": "2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "8ebfcadbf30524aeb75b2c446bc2519d5b321478"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/8ebfcadbf30524aeb75b2c446bc2519d5b321478", "type": "zip", "shasum": "", "reference": "8ebfcadbf30524aeb75b2c446bc2519d5b321478"}, "time": "2019-01-27T19:37:29+00:00"}, {"version": "2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "42603ce3f42a27f7e14e54feab95db7b680ad473"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/42603ce3f42a27f7e14e54feab95db7b680ad473", "type": "zip", "shasum": "", "reference": "42603ce3f42a27f7e14e54feab95db7b680ad473"}, "time": "2018-12-16T17:45:25+00:00"}, {"version": "2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "8814dc7841db159daed0b32c2b08fb7e03c6afe7"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/8814dc7841db159daed0b32c2b08fb7e03c6afe7", "type": "zip", "shasum": "", "reference": "8814dc7841db159daed0b32c2b08fb7e03c6afe7"}, "time": "2018-11-04T05:45:48+00:00"}, {"version": "2.0.11", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7053f06f91b3de78e143d430e55a8f7889efc08b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7053f06f91b3de78e143d430e55a8f7889efc08b", "type": "zip", "shasum": "", "reference": "7053f06f91b3de78e143d430e55a8f7889efc08b"}, "time": "2018-04-15T16:55:05+00:00"}, {"version": "2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d305b780829ea4252ed9400b3f5937c2c99b51d4"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d305b780829ea4252ed9400b3f5937c2c99b51d4", "type": "zip", "shasum": "", "reference": "d305b780829ea4252ed9400b3f5937c2c99b51d4"}, "time": "2018-02-19T04:29:13+00:00"}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c9a3fe35e20eb6eeaca716d6a23cde03f52d1558"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c9a3fe35e20eb6eeaca716d6a23cde03f52d1558", "type": "zip", "shasum": "", "reference": "c9a3fe35e20eb6eeaca716d6a23cde03f52d1558"}, "time": "2017-11-29T06:38:08+00:00", "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "~4.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "0a58b73037b4cbcbd66bb9aaed910740eb8624e5"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/0a58b73037b4cbcbd66bb9aaed910740eb8624e5", "type": "zip", "shasum": "", "reference": "0a58b73037b4cbcbd66bb9aaed910740eb8624e5"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/master"}, "time": "2017-11-29T06:38:20+00:00", "require": {"paragonie/constant_time_encoding": "^1", "paragonie/random_compat": "^1.4|^2.0", "php": ">=5.6.1"}}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "f4b6a522dfa1fd1e477c9cfe5909d5b31f098c0b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/f4b6a522dfa1fd1e477c9cfe5909d5b31f098c0b", "type": "zip", "shasum": "", "reference": "f4b6a522dfa1fd1e477c9cfe5909d5b31f098c0b"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0"}, "time": "2017-10-23T05:04:54+00:00", "require": {"php": ">=5.3.3"}}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "34a7699e6f31b1ef4035ee36444407cecf9f56aa"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/34a7699e6f31b1ef4035ee36444407cecf9f56aa", "type": "zip", "shasum": "", "reference": "34a7699e6f31b1ef4035ee36444407cecf9f56aa"}, "time": "2017-06-05T06:31:10+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "f8dd0e18d2328c447dd4190fecd11ef52680d968"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/f8dd0e18d2328c447dd4190fecd11ef52680d968", "type": "zip", "shasum": "", "reference": "f8dd0e18d2328c447dd4190fecd11ef52680d968"}, "time": "2017-05-08T05:58:35+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "ab8028c93c03cc8d9c824efa75dc94f1db2369bf"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/ab8028c93c03cc8d9c824efa75dc94f1db2369bf", "type": "zip", "shasum": "", "reference": "ab8028c93c03cc8d9c824efa75dc94f1db2369bf"}, "time": "2016-10-04T00:57:04+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "41f85e9c2582b3f6d1b7d20395fb40c687ad5370"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/41f85e9c2582b3f6d1b7d20395fb40c687ad5370", "type": "zip", "shasum": "", "reference": "41f85e9c2582b3f6d1b7d20395fb40c687ad5370"}, "time": "2016-08-18T18:49:14+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "3d265f7c079f5b37d33475f996d7a383c5fc8aeb"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/3d265f7c079f5b37d33475f996d7a383c5fc8aeb", "type": "zip", "shasum": "", "reference": "3d265f7c079f5b37d33475f996d7a383c5fc8aeb"}, "time": "2016-05-13T01:15:21+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "ba6fb78f727cd09f2a649113b95468019e490585"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/ba6fb78f727cd09f2a649113b95468019e490585", "type": "zip", "shasum": "", "reference": "ba6fb78f727cd09f2a649113b95468019e490585"}, "time": "2016-01-18T17:07:21+00:00", "autoload": {"psr-4": {"phpseclib\\": "phpseclib/"}}}, {"version": "2.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "a74aa9efbe61430fcb60157c8e025a48ec8ff604"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/a74aa9efbe61430fcb60157c8e025a48ec8ff604", "type": "zip", "shasum": "", "reference": "a74aa9efbe61430fcb60157c8e025a48ec8ff604"}, "time": "2015-08-04T04:48:03+00:00", "include-path": ["phpseclib/"], "suggest": {"ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 5.0.0."}}, {"version": "1.0.23", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "86990d518f4434719271fadd112d34b20208f3a9"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/86990d518f4434719271fadd112d34b20208f3a9", "type": "zip", "shasum": "", "reference": "86990d518f4434719271fadd112d34b20208f3a9"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.23"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-02-26T04:44:00+00:00", "autoload": {"files": ["phpseclib/bootstrap.php", "phpseclib/Crypt/Random.php"], "psr-0": {"Net": "phpseclib/", "File": "phpseclib/", "Math": "phpseclib/", "Crypt": "phpseclib/", "System": "phpseclib/"}}, "require": {"php": ">=5.0.0"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a wide variety of cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 5.0.0."}}, {"version": "1.0.22", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "db278731472afb1621353b659609b0f9054a1879"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/db278731472afb1621353b659609b0f9054a1879", "type": "zip", "shasum": "", "reference": "db278731472afb1621353b659609b0f9054a1879"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.22"}, "time": "2023-12-29T01:44:43+00:00"}, {"version": "1.0.21", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "de7ec75b54d6a1f3d549d03a2bf3e3e392c3bc36"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/de7ec75b54d6a1f3d549d03a2bf3e3e392c3bc36", "type": "zip", "shasum": "", "reference": "de7ec75b54d6a1f3d549d03a2bf3e3e392c3bc36"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.21"}, "time": "2023-07-09T15:16:21+00:00"}, {"version": "1.0.20", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "b3a606b90c47fb526cfb3a1664771cb8314a3434"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b3a606b90c47fb526cfb3a1664771cb8314a3434", "type": "zip", "shasum": "", "reference": "b3a606b90c47fb526cfb3a1664771cb8314a3434"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.20"}, "time": "2021-12-28T06:26:23+00:00", "suggest": {"ext-mcrypt": "Install the Mcrypt extension in order to speed up a wide variety of cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 5.0.0."}}, {"version": "1.0.19", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "6537e74746c09cde3bb94317ae039bd89e1decf9"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/6537e74746c09cde3bb94317ae039bd89e1decf9", "type": "zip", "shasum": "", "reference": "6537e74746c09cde3bb94317ae039bd89e1decf9"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0"}, "time": "2020-07-08T04:31:58+00:00", "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "1.0.18", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "ec1fcfe648e5696efa2dd26ffa54436e3478ae5c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/ec1fcfe648e5696efa2dd26ffa54436e3478ae5c", "type": "zip", "shasum": "", "reference": "ec1fcfe648e5696efa2dd26ffa54436e3478ae5c"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.18"}, "time": "2019-09-17T04:11:10+00:00", "funding": "__unset"}, {"version": "1.0.17", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "db324e5d019f99de15b5a5b6b9214d6b720f1c4f"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/db324e5d019f99de15b5a5b6b9214d6b720f1c4f", "type": "zip", "shasum": "", "reference": "db324e5d019f99de15b5a5b6b9214d6b720f1c4f"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0"}, "time": "2019-09-15T23:22:55+00:00"}, {"version": "1.0.16", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d77bb6e42b9ea9cd883ea0297c1ffec8f96a5dd1"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d77bb6e42b9ea9cd883ea0297c1ffec8f96a5dd1", "type": "zip", "shasum": "", "reference": "d77bb6e42b9ea9cd883ea0297c1ffec8f96a5dd1"}, "time": "2019-06-13T06:15:36+00:00"}, {"version": "1.0.15", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "849f9976330e70cb7884fe44b9dbc962d2b4bd0c"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/849f9976330e70cb7884fe44b9dbc962d2b4bd0c", "type": "zip", "shasum": "", "reference": "849f9976330e70cb7884fe44b9dbc962d2b4bd0c"}, "time": "2019-03-10T16:51:38+00:00"}, {"version": "1.0.14", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7432a6959afe98b9e93153d0034b0f62ad890d31"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7432a6959afe98b9e93153d0034b0f62ad890d31", "type": "zip", "shasum": "", "reference": "7432a6959afe98b9e93153d0034b0f62ad890d31"}, "time": "2019-01-27T19:35:10+00:00"}, {"version": "1.0.13", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "a9c0e2d4300533de468503587ad4021d42393ccd"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/a9c0e2d4300533de468503587ad4021d42393ccd", "type": "zip", "shasum": "", "reference": "a9c0e2d4300533de468503587ad4021d42393ccd"}, "time": "2018-12-16T17:44:58+00:00"}, {"version": "1.0.12", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "20c35d9c7592dfef140b6d8dc299cb2647605e0a"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/20c35d9c7592dfef140b6d8dc299cb2647605e0a", "type": "zip", "shasum": "", "reference": "20c35d9c7592dfef140b6d8dc299cb2647605e0a"}, "time": "2018-11-04T05:38:52+00:00"}, {"version": "1.0.11", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "fd33675e483b887fc59834ab69e33ef7f2706425"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/fd33675e483b887fc59834ab69e33ef7f2706425", "type": "zip", "shasum": "", "reference": "fd33675e483b887fc59834ab69e33ef7f2706425"}, "time": "2018-04-15T16:51:35+00:00"}, {"version": "1.0.10", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "1468ef1a23f66b09b8e906f05ddfa6c3f5236202"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/1468ef1a23f66b09b8e906f05ddfa6c3f5236202", "type": "zip", "shasum": "", "reference": "1468ef1a23f66b09b8e906f05ddfa6c3f5236202"}, "time": "2018-02-08T03:22:54+00:00"}, {"version": "1.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "2bab4e4bd61efaecb8879d158da04f408d312746"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/2bab4e4bd61efaecb8879d158da04f408d312746", "type": "zip", "shasum": "", "reference": "2bab4e4bd61efaecb8879d158da04f408d312746"}, "time": "2017-11-29T06:37:04+00:00", "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "~4.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}}, {"version": "1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "817bebef472ac34260564097a7a69209e9a4f017"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/817bebef472ac34260564097a7a69209e9a4f017", "type": "zip", "shasum": "", "reference": "817bebef472ac34260564097a7a69209e9a4f017"}, "time": "2017-10-23T05:03:25+00:00"}, {"version": "1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "0bb6c9b974cada100cad40f72ef186a199274f9b"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/0bb6c9b974cada100cad40f72ef186a199274f9b", "type": "zip", "shasum": "", "reference": "0bb6c9b974cada100cad40f72ef186a199274f9b"}, "time": "2017-06-05T06:30:30+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "aa8368ed12ef389f20d3932d051a7207471ecbab"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/aa8368ed12ef389f20d3932d051a7207471ecbab", "type": "zip", "shasum": "", "reference": "aa8368ed12ef389f20d3932d051a7207471ecbab"}, "time": "2017-05-08T05:58:24+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "38fa7332e96bcc546ce6eb5cbaa1168c96839393"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/38fa7332e96bcc546ce6eb5cbaa1168c96839393", "type": "zip", "shasum": "", "reference": "38fa7332e96bcc546ce6eb5cbaa1168c96839393"}, "time": "2016-10-22T17:53:16+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "2dfe9c5de7e7d1a4c924197db73826c303f91d7f"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/2dfe9c5de7e7d1a4c924197db73826c303f91d7f", "type": "zip", "shasum": "", "reference": "2dfe9c5de7e7d1a4c924197db73826c303f91d7f"}, "time": "2016-10-04T00:56:50+00:00", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c27fd2e66fa905cd7a535b31a7508f39dcd8c1aa"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c27fd2e66fa905cd7a535b31a7508f39dcd8c1aa", "type": "zip", "shasum": "", "reference": "c27fd2e66fa905cd7a535b31a7508f39dcd8c1aa"}, "time": "2016-08-18T18:43:04+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "b8f3a76632636d52ceb8a4eb6ab4398d4346ca38"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b8f3a76632636d52ceb8a4eb6ab4398d4346ca38", "type": "zip", "shasum": "", "reference": "b8f3a76632636d52ceb8a4eb6ab4398d4346ca38"}, "time": "2016-05-08T00:01:00+00:00", "autoload": {"files": ["phpseclib/Crypt/Random.php"], "psr-0": {"Net": "phpseclib/", "File": "phpseclib/", "Math": "phpseclib/", "Crypt": "phpseclib/", "System": "phpseclib/"}}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "f60e365be28d9218b4bcdc0ab96ac7e338668f11"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/f60e365be28d9218b4bcdc0ab96ac7e338668f11", "type": "zip", "shasum": "", "reference": "f60e365be28d9218b4bcdc0ab96ac7e338668f11"}, "time": "2016-01-18T17:07:12+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "844134df95e42b93ed5506a2d11ca5accda99412"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/844134df95e42b93ed5506a2d11ca5accda99412", "type": "zip", "shasum": "", "reference": "844134df95e42b93ed5506a2d11ca5accda99412"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.0"}, "time": "2015-08-04T02:22:12+00:00", "suggest": {"ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 5.0.0."}}, {"version": "0.3.10", "version_normalized": "********", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "d15bba1edcc7c89e09cc74c5d961317a8b947bf4"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d15bba1edcc7c89e09cc74c5d961317a8b947bf4", "type": "zip", "shasum": "", "reference": "d15bba1edcc7c89e09cc74c5d961317a8b947bf4"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.10"}, "time": "2015-01-28T21:50:33+00:00", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "~4.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~1.5"}, "suggest": {"ext-mcrypt": "Install the Mcrypt extension in order to speed up a wide variety of cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 4.3.3."}}, {"version": "0.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "c6e88ca6e81bc5a2d7161658e16a95b7ef8d6ad1"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/c6e88ca6e81bc5a2d7161658e16a95b7ef8d6ad1", "type": "zip", "shasum": "", "reference": "c6e88ca6e81bc5a2d7161658e16a95b7ef8d6ad1"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.9"}, "time": "2014-11-10T03:08:59+00:00", "require-dev": {"phing/phing": "2.7.*", "phpunit/phpunit": "4.0.*", "sami/sami": "1.*", "squizlabs/php_codesniffer": "1.*"}}, {"version": "0.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "5085202f1f37769aae59f9711c423f28159c9b29"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/5085202f1f37769aae59f9711c423f28159c9b29", "type": "zip", "shasum": "", "reference": "5085202f1f37769aae59f9711c423f28159c9b29"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.8"}, "time": "2014-09-13T02:42:45+00:00"}, {"version": "0.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "8b8c62f278e363b75ddcacaf5803710232fbd3e4"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/8b8c62f278e363b75ddcacaf5803710232fbd3e4", "type": "zip", "shasum": "", "reference": "8b8c62f278e363b75ddcacaf5803710232fbd3e4"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.7"}, "time": "2014-07-05T16:36:21+00:00", "require-dev": {"phing/phing": "2.7.*", "phpunit/phpunit": "4.0.*", "squizlabs/php_codesniffer": "1.*"}}, {"version": "0.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "0ea31d9b65d49a8661e93bec19f44e989bd34c69"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/0ea31d9b65d49a8661e93bec19f44e989bd34c69", "type": "zip", "shasum": "", "reference": "0ea31d9b65d49a8661e93bec19f44e989bd34c69"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.6"}, "time": "2014-02-28T16:05:05+00:00", "require-dev": {"squizlabs/php_codesniffer": "1.*"}}, {"keywords": ["security", "cryptography", "ssh", "encryption", "crypto", "signature", "signing", "rsa", "aes", "sftp", "x509", "x.509", "asn1", "asn.1", "BigInteger"], "version": "0.3.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "fe3765fe1773c9d62985d05d5b5da24c921c4bd0"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/fe3765fe1773c9d62985d05d5b5da24c921c4bd0", "type": "zip", "shasum": "", "reference": "fe3765fe1773c9d62985d05d5b5da24c921c4bd0"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.5"}, "time": "2013-06-08T17:40:39+00:00", "autoload": {"files": ["phpseclib/Crypt/Random.php"], "psr-0": {"Net": "phpseclib/", "File": "phpseclib/", "Math": "phpseclib/", "Crypt": "phpseclib/"}}, "suggest": {"ext-mcrypt": "Install the Mcrypt extension in order to speed up a wide variety of cryptographic operations.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP >= 4.3.3."}, "require-dev": "__unset"}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "7e120a0f3073025d69ed9bcbe3374c54b802f897"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7e120a0f3073025d69ed9bcbe3374c54b802f897", "type": "zip", "shasum": "", "reference": "7e120a0f3073025d69ed9bcbe3374c54b802f897"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.1"}, "time": "2012-11-20T03:10:22+00:00", "autoload": {"psr-0": {"Net": "phpseclib/", "File": "phpseclib/", "Math": "phpseclib/", "Crypt": "phpseclib/"}}, "extra": "__unset"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpseclib/phpseclib.git", "type": "git", "reference": "4eaf6db3ab40093ce8e97b37ba2f6f77fe0db6ca"}, "dist": {"url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/4eaf6db3ab40093ce8e97b37ba2f6f77fe0db6ca", "type": "zip", "shasum": "", "reference": "4eaf6db3ab40093ce8e97b37ba2f6f77fe0db6ca"}, "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/0.3.0"}, "time": "2012-07-07T22:24:45+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-4p7m-np8m-fq35", "affectedVersions": ">=3.0.0,<3.0.33|>=2.0.0,<2.0.46|<1.0.22"}, {"advisoryId": "PKSA-t5xz-td8w-f35v", "affectedVersions": ">=3.0.0,<3.0.36|>=2.0.0,<2.0.47|>=1.0.0,<1.0.23"}, {"advisoryId": "PKSA-jsh4-f6tg-bwyq", "affectedVersions": ">=3.0.0,<3.0.36|>=2.0.0,<2.0.47|>=1.0.0,<1.0.23"}, {"advisoryId": "PKSA-vpz8-6fv7-t3fd", "affectedVersions": ">=3.0.0,<3.0.34"}, {"advisoryId": "PKSA-qrgb-4pgm-cz41", "affectedVersions": ">=3.0.0,<3.0.19"}, {"advisoryId": "PKSA-mnsd-qtjt-pgcq", "affectedVersions": "<2.0.31|>=3.0.0,<3.0.7"}], "last-modified": "Mon, 30 Jun 2025 01:57:47 GMT"}