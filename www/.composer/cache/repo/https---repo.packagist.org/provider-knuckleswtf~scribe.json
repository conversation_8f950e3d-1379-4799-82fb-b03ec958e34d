{"minified": "composer/2.0", "packages": {"knuckleswtf/scribe": [{"name": "knuckleswtf/scribe", "description": "Generate API documentation for humans from your Laravel codebase.✍", "keywords": ["documentation", "api", "laravel"], "homepage": "http://github.com/knuckleswtf/scribe", "version": "5.2.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7d1866bfccc96559b753466afdc1f70ed6c6125e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7d1866bfccc96559b753466afdc1f70ed6c6125e", "type": "zip", "shasum": "", "reference": "7d1866bfccc96559b753466afdc1f70ed6c6125e"}, "type": "library", "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/5.2.1"}, "funding": [{"url": "https://patreon.com/shalvah", "type": "patreon"}], "time": "2025-05-01T01:14:54+00:00", "autoload": {"files": ["src/Config/helpers.php"], "psr-4": {"Knuckles\\Camel\\": "camel/", "Knuckles\\Scribe\\": "src/"}}, "extra": {"laravel": {"providers": ["Knuckles\\Scribe\\ScribeServiceProvider"]}}, "require": {"php": ">=8.1", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.23.1", "laravel/framework": "^9.0|^10.0|^11.0|^12.0", "league/flysystem": "^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^5.0", "nunomaduro/collision": "^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": ">=0.6.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^6.0|^7.0", "symfony/yaml": "^6.0|^7.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^v0.5.0", "laravel/legacy-factories": "^1.3.0", "league/fractal": "^0.20", "nikic/fast-route": "^1.3", "orchestra/testbench": "^7.0|^8.0|^v9.10.0|^10.0", "pestphp/pest": "^1.21|^2.0|^3.0", "phpstan/phpstan": "^2.1.5", "phpunit/phpunit": "^9.0|^10.0|^11.0", "symfony/css-selector": "^6.0|^7.0", "symfony/dom-crawler": "^6.0|^7.0", "spatie/ray": "^1.41"}, "replace": {"mpociot/laravel-apidoc-generator": "*"}}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "618908cac156fc90b0387cb9e14dfbd5a93c76cc"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/618908cac156fc90b0387cb9e14dfbd5a93c76cc", "type": "zip", "shasum": "", "reference": "618908cac156fc90b0387cb9e14dfbd5a93c76cc"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/5.2.0"}, "time": "2025-04-17T21:36:45+00:00"}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "1e846244476a0312b8a7731a779a4fea85a5bfa0"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/1e846244476a0312b8a7731a779a4fea85a5bfa0", "type": "zip", "shasum": "", "reference": "1e846244476a0312b8a7731a779a4fea85a5bfa0"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/5.1.0"}, "time": "2025-02-25T09:09:08+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "f96342bf9b780952edf216c132564420c47540f4"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/f96342bf9b780952edf216c132564420c47540f4", "type": "zip", "shasum": "", "reference": "f96342bf9b780952edf216c132564420c47540f4"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/5.0.1"}, "time": "2025-02-20T10:16:09+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c5e567728ef85ea4af28f4c264dc1ef2293d7e32"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c5e567728ef85ea4af28f4c264dc1ef2293d7e32", "type": "zip", "shasum": "", "reference": "c5e567728ef85ea4af28f4c264dc1ef2293d7e32"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/5.0.0"}, "time": "2025-02-19T10:32:40+00:00", "require-dev": {"dms/phpunit-arraysubset-asserts": "^v0.5.0", "laravel/legacy-factories": "^1.3.0", "league/fractal": "^0.20", "nikic/fast-route": "^1.3", "orchestra/testbench": "^7.0|^8.0|^v9.10.0", "pestphp/pest": "^1.21|^2.0|^3.0", "phpstan/phpstan": "^2.1.5", "phpunit/phpunit": "^9.0|^10.0", "symfony/css-selector": "^6.0", "symfony/dom-crawler": "^6.0", "spatie/ray": "^1.41"}}, {"keywords": ["documentation", "api", "laravel", "dingo"], "version": "4.40.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "1ad707f229185bb2413ea9ed2fb01747abe78ff6"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/1ad707f229185bb2413ea9ed2fb01747abe78ff6", "type": "zip", "shasum": "", "reference": "1ad707f229185bb2413ea9ed2fb01747abe78ff6"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.40.0"}, "time": "2025-02-03T20:29:15+00:00", "autoload": {"psr-4": {"Knuckles\\Camel\\": "camel/", "Knuckles\\Scribe\\": "src/"}}, "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0|^11.0", "illuminate/routing": "^8.0|^9.0|^10.0|^11.0", "illuminate/support": "^8.0|^9.0|^10.0|^11.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^5.0", "nunomaduro/collision": "^5.10|^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": ">=0.6.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.4", "laravel/legacy-factories": "^1.3.0", "laravel/lumen-framework": "^8.0|^9.0|^10.0", "league/fractal": "^0.20", "nikic/fast-route": "^1.3", "orchestra/testbench": "^6.0|^7.0|^8.0", "pestphp/pest": "^1.21", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.0|^10.0", "symfony/css-selector": "^5.4|^6.0", "symfony/dom-crawler": "^5.4|^6.0"}}, {"version": "4.39.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "cb4c2e552fdae4fd2306e12bba60d5de38a9e4ad"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/cb4c2e552fdae4fd2306e12bba60d5de38a9e4ad", "type": "zip", "shasum": "", "reference": "cb4c2e552fdae4fd2306e12bba60d5de38a9e4ad"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.39.0"}, "time": "2024-12-31T14:13:50+00:00"}, {"version": "4.38.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "da667aafb6804b4b599ac6506bb178bfa5434802"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/da667aafb6804b4b599ac6506bb178bfa5434802", "type": "zip", "shasum": "", "reference": "da667aafb6804b4b599ac6506bb178bfa5434802"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.38.0"}, "time": "2024-10-18T20:16:40+00:00"}, {"version": "4.37.2", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6318f3f68cbf09328e5cb6843ce1739e529ef1ac"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6318f3f68cbf09328e5cb6843ce1739e529ef1ac", "type": "zip", "shasum": "", "reference": "6318f3f68cbf09328e5cb6843ce1739e529ef1ac"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.37.2"}, "time": "2024-08-30T12:15:51+00:00"}, {"version": "4.37.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "5eb0f65973db9df5ba455f8bbcc8a1cd6573564b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/5eb0f65973db9df5ba455f8bbcc8a1cd6573564b", "type": "zip", "shasum": "", "reference": "5eb0f65973db9df5ba455f8bbcc8a1cd6573564b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.37.1"}, "time": "2024-07-11T13:57:05+00:00"}, {"version": "4.37.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "85ad49144a10a69c5dbaaf1627feae48e6f2ec17"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/85ad49144a10a69c5dbaaf1627feae48e6f2ec17", "type": "zip", "shasum": "", "reference": "85ad49144a10a69c5dbaaf1627feae48e6f2ec17"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.37.0"}, "time": "2024-06-17T20:34:33+00:00"}, {"version": "4.36.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "1304e1503600b1fe8f6f546445de0ebd927386fa"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/1304e1503600b1fe8f6f546445de0ebd927386fa", "type": "zip", "shasum": "", "reference": "1304e1503600b1fe8f6f546445de0ebd927386fa"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.36.0"}, "time": "2024-05-27T21:51:57+00:00"}, {"version": "4.35.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "dd6696676981139de814bb0eeda7d5e5891a5244"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/dd6696676981139de814bb0eeda7d5e5891a5244", "type": "zip", "shasum": "", "reference": "dd6696676981139de814bb0eeda7d5e5891a5244"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.35.0"}, "time": "2024-03-26T20:58:51+00:00"}, {"version": "4.34.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "30624433a37a8d2ee277c86297bab62d4a2b8338"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/30624433a37a8d2ee277c86297bab62d4a2b8338", "type": "zip", "shasum": "", "reference": "30624433a37a8d2ee277c86297bab62d4a2b8338"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.34.0"}, "time": "2024-03-15T19:25:29+00:00"}, {"version": "4.33.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c0afe840081f8c9872b346694a0ab95a0e378842"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c0afe840081f8c9872b346694a0ab95a0e378842", "type": "zip", "shasum": "", "reference": "c0afe840081f8c9872b346694a0ab95a0e378842"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.33.0"}, "time": "2024-02-29T19:10:57+00:00"}, {"version": "4.32.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "d1d96fb1805a8e600a3c2a33abdb391510a4c22a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/d1d96fb1805a8e600a3c2a33abdb391510a4c22a", "type": "zip", "shasum": "", "reference": "d1d96fb1805a8e600a3c2a33abdb391510a4c22a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.32.0"}, "time": "2024-02-20T12:11:29+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^5.0", "nunomaduro/collision": "^5.10|^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": ">=0.6.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}}, {"version": "4.31.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "528134062eebf4c2908e16544b8fb78d140036b4"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/528134062eebf4c2908e16544b8fb78d140036b4", "type": "zip", "shasum": "", "reference": "528134062eebf4c2908e16544b8fb78d140036b4"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.31.0"}, "time": "2024-02-20T12:00:08+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^5.10|^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.3.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}}, {"version": "4.30.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0900c0d44965b0a13c480a8ea6dbc27e4698bcd9"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0900c0d44965b0a13c480a8ea6dbc27e4698bcd9", "type": "zip", "shasum": "", "reference": "0900c0d44965b0a13c480a8ea6dbc27e4698bcd9"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.30.1"}, "time": "2024-02-18T14:19:49+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10|^5.0", "nunomaduro/collision": "^5.10|^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.4.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}}, {"version": "4.30.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "a92f7fac2ed957e1cd660e477c7a0f650d507ad6"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/a92f7fac2ed957e1cd660e477c7a0f650d507ad6", "type": "zip", "shasum": "", "reference": "a92f7fac2ed957e1cd660e477c7a0f650d507ad6"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.30.0"}, "time": "2024-02-13T05:31:24+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10|^5.0", "nunomaduro/collision": "^5.10|^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.3.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}}, {"version": "4.29.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "695c2a99a70f8c6c1b0dd3bf34d0d70af5ca44fc"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/695c2a99a70f8c6c1b0dd3bf34d0d70af5ca44fc", "type": "zip", "shasum": "", "reference": "695c2a99a70f8c6c1b0dd3bf34d0d70af5ca44fc"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.29.0"}, "time": "2023-12-29T14:26:08+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^5.10|^6.0|^7.0|^8.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.3.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}}, {"version": "4.28.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "22ef3f2d837a85bdf8cc4c1eae63f6eeeae2c8f3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/22ef3f2d837a85bdf8cc4c1eae63f6eeeae2c8f3", "type": "zip", "shasum": "", "reference": "22ef3f2d837a85bdf8cc4c1eae63f6eeeae2c8f3"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.28.0"}, "time": "2023-12-25T20:55:20+00:00"}, {"version": "4.27.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "ab123ea94408c84ee53aaa584926323a3161c238"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/ab123ea94408c84ee53aaa584926323a3161c238", "type": "zip", "shasum": "", "reference": "ab123ea94408c84ee53aaa584926323a3161c238"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.27.0"}, "time": "2023-12-21T17:45:45+00:00"}, {"version": "4.26.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b4606404e8931fb882dd001163168e6314c0d356"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b4606404e8931fb882dd001163168e6314c0d356", "type": "zip", "shasum": "", "reference": "b4606404e8931fb882dd001163168e6314c0d356"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.26.0"}, "time": "2023-11-21T10:33:59+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^5.10|^6.0|^7.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.3.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}}, {"version": "4.25.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e45e3afc5f97008294b9c18edd16666223e486a2"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e45e3afc5f97008294b9c18edd16666223e486a2", "type": "zip", "shasum": "", "reference": "e45e3afc5f97008294b9c18edd16666223e486a2"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.25.0"}, "time": "2023-09-29T22:23:01+00:00"}, {"version": "4.24.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6b4715b8cc46233f3dcaef1c9cc3fd37b8fbb1d1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6b4715b8cc46233f3dcaef1c9cc3fd37b8fbb1d1", "type": "zip", "shasum": "", "reference": "6b4715b8cc46233f3dcaef1c9cc3fd37b8fbb1d1"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.24.0"}, "time": "2023-09-16T15:18:08+00:00"}, {"version": "4.23.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4960403638439ef3fc83a0843e795fb92a5fc4f1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4960403638439ef3fc83a0843e795fb92a5fc4f1", "type": "zip", "shasum": "", "reference": "4960403638439ef3fc83a0843e795fb92a5fc4f1"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.23.1"}, "time": "2023-08-25T16:33:56+00:00"}, {"version": "4.23.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "8daa74c96c2b99c326293f209e392eceb823888f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/8daa74c96c2b99c326293f209e392eceb823888f", "type": "zip", "shasum": "", "reference": "8daa74c96c2b99c326293f209e392eceb823888f"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.23.0"}, "time": "2023-08-24T21:07:05+00:00"}, {"version": "4.22.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "dc63b62e5b470ef874535ed8ac0c42a8eb37cce5"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/dc63b62e5b470ef874535ed8ac0c42a8eb37cce5", "type": "zip", "shasum": "", "reference": "dc63b62e5b470ef874535ed8ac0c42a8eb37cce5"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.22.0"}, "time": "2023-06-30T22:22:25+00:00"}, {"version": "4.21.2", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b4b190cd1f8a0c234061cb8d6cbe019eaa13c495"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b4b190cd1f8a0c234061cb8d6cbe019eaa13c495", "type": "zip", "shasum": "", "reference": "b4b190cd1f8a0c234061cb8d6cbe019eaa13c495"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.21.2"}, "time": "2023-06-05T17:37:20+00:00"}, {"version": "4.21.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "86a9e24b7155837cfe9d9b4e1ec24cc4d31cee3a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/86a9e24b7155837cfe9d9b4e1ec24cc4d31cee3a", "type": "zip", "shasum": "", "reference": "86a9e24b7155837cfe9d9b4e1ec24cc4d31cee3a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.21.1"}, "time": "2023-06-03T17:47:40+00:00"}, {"version": "4.21.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4cf022bd8834158dfb97def0e4b22f7f1b2cd779"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4cf022bd8834158dfb97def0e4b22f7f1b2cd779", "type": "zip", "shasum": "", "reference": "4cf022bd8834158dfb97def0e4b22f7f1b2cd779"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.21.0"}, "time": "2023-05-29T15:50:25+00:00"}, {"version": "4.20.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0fe280ed8f40640b4ca89ba8e6cc3f99bc835168"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0fe280ed8f40640b4ca89ba8e6cc3f99bc835168", "type": "zip", "shasum": "", "reference": "0fe280ed8f40640b4ca89ba8e6cc3f99bc835168"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.20.0"}, "time": "2023-05-28T11:59:34+00:00"}, {"version": "4.19.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "f8c413cfd88d9175b68799ab5b1814e73f391385"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/f8c413cfd88d9175b68799ab5b1814e73f391385", "type": "zip", "shasum": "", "reference": "f8c413cfd88d9175b68799ab5b1814e73f391385"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.19.1"}, "time": "2023-05-05T19:52:38+00:00"}, {"version": "4.19.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4b806df88437bb4d5ace75d1d76ae7b23ece168d"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4b806df88437bb4d5ace75d1d76ae7b23ece168d", "type": "zip", "shasum": "", "reference": "4b806df88437bb4d5ace75d1d76ae7b23ece168d"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.19.0"}, "time": "2023-04-22T18:33:33+00:00"}, {"version": "4.18.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "53faab8433cd099328965d90b79d05eb176cff58"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/53faab8433cd099328965d90b79d05eb176cff58", "type": "zip", "shasum": "", "reference": "53faab8433cd099328965d90b79d05eb176cff58"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.18.0"}, "time": "2023-04-08T13:04:47+00:00"}, {"version": "4.17.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "edc14a9800f2690b9ac1cbc9305ea6ba360e0d68"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/edc14a9800f2690b9ac1cbc9305ea6ba360e0d68", "type": "zip", "shasum": "", "reference": "edc14a9800f2690b9ac1cbc9305ea6ba360e0d68"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.17.0"}, "time": "2023-03-19T14:53:52+00:00"}, {"version": "4.16.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "08897fcb588c78741550fecaa4e263f879268801"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/08897fcb588c78741550fecaa4e263f879268801", "type": "zip", "shasum": "", "reference": "08897fcb588c78741550fecaa4e263f879268801"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.16.1"}, "time": "2023-03-19T13:41:24+00:00", "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.4", "laravel/legacy-factories": "^1.3.0", "laravel/lumen-framework": "^8.0|^9.0", "league/fractal": "^0.20", "nikic/fast-route": "^1.3", "orchestra/testbench": "^6.0|^7.0|^8.0", "pestphp/pest": "^1.21", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.0|^10.0", "symfony/css-selector": "^5.4|^6.0", "symfony/dom-crawler": "^5.4|^6.0"}}, {"version": "4.16.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "1df972a3f73dd3b98e1dcf038597606ba9bdb01f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/1df972a3f73dd3b98e1dcf038597606ba9bdb01f", "type": "zip", "shasum": "", "reference": "1df972a3f73dd3b98e1dcf038597606ba9bdb01f"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.16.0"}, "time": "2023-02-16T18:51:12+00:00"}, {"version": "4.15.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6799496e3fa0e79cdcf48f68785c16a942dbc756"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6799496e3fa0e79cdcf48f68785c16a942dbc756", "type": "zip", "shasum": "", "reference": "6799496e3fa0e79cdcf48f68785c16a942dbc756"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.15.0"}, "time": "2023-02-14T20:18:36+00:00"}, {"version": "4.14.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "750e949202167a4f25c736ba48a3a9965fb1751a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/750e949202167a4f25c736ba48a3a9965fb1751a", "type": "zip", "shasum": "", "reference": "750e949202167a4f25c736ba48a3a9965fb1751a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.14.0"}, "time": "2023-02-07T19:29:29+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0|^10.0", "illuminate/routing": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^5.10|^6.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.3.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}}, {"version": "4.13.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "ecc6d28e9db10b2b0154b7a081a262e643523e02"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/ecc6d28e9db10b2b0154b7a081a262e643523e02", "type": "zip", "shasum": "", "reference": "ecc6d28e9db10b2b0154b7a081a262e643523e02"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.13.0"}, "time": "2023-01-22T21:06:43+00:00", "require": {"php": ">=8.0", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^8.0|^9.0", "illuminate/routing": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^5.10|^6.0", "ramsey/uuid": "^4.2.2", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "^0.3.0", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}, "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.3.0", "laravel/lumen-framework": "^8.0|^9.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^6.0|^7.0", "pestphp/pest": "^1.21", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.0|^10.0", "symfony/css-selector": "^5.4|^6.0", "symfony/dom-crawler": "^5.4|^6.0"}}, {"version": "4.12.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "52578b80091b24e174e607afaf0e0c962014b56d"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/52578b80091b24e174e607afaf0e0c962014b56d", "type": "zip", "shasum": "", "reference": "52578b80091b24e174e607afaf0e0c962014b56d"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.12.0"}, "time": "2023-01-15T19:26:34+00:00"}, {"version": "4.11.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9a02fdb91b5cc50e315d1f3f3430243a93ffafa3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9a02fdb91b5cc50e315d1f3f3430243a93ffafa3", "type": "zip", "shasum": "", "reference": "9a02fdb91b5cc50e315d1f3f3430243a93ffafa3"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.11.0"}, "time": "2023-01-08T13:08:54+00:00"}, {"version": "4.10.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7cdf3903647b79d396d945c2062bef4f907a6fe7"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7cdf3903647b79d396d945c2062bef4f907a6fe7", "type": "zip", "shasum": "", "reference": "7cdf3903647b79d396d945c2062bef4f907a6fe7"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.10.1"}, "time": "2022-12-14T18:47:15+00:00"}, {"version": "4.10.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b1f9cd41c4c67b3db4af53374a515eaf2e200d7a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b1f9cd41c4c67b3db4af53374a515eaf2e200d7a", "type": "zip", "shasum": "", "reference": "b1f9cd41c4c67b3db4af53374a515eaf2e200d7a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.10.0"}, "time": "2022-12-14T17:54:44+00:00"}, {"version": "4.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7d2f163bb81b4886ee5cb9ba8645091ae0b915e3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7d2f163bb81b4886ee5cb9ba8645091ae0b915e3", "type": "zip", "shasum": "", "reference": "7d2f163bb81b4886ee5cb9ba8645091ae0b915e3"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.9.0"}, "time": "2022-12-05T21:47:56+00:00"}, {"version": "4.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "00e7c2716b7e1186f23b44f43ab97d855972eb0e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/00e7c2716b7e1186f23b44f43ab97d855972eb0e", "type": "zip", "shasum": "", "reference": "00e7c2716b7e1186f23b44f43ab97d855972eb0e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.8.0"}, "time": "2022-12-02T18:13:36+00:00"}, {"version": "4.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "13c6f68842e727c77274038199f5d235174bb2ba"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/13c6f68842e727c77274038199f5d235174bb2ba", "type": "zip", "shasum": "", "reference": "13c6f68842e727c77274038199f5d235174bb2ba"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.7.1"}, "time": "2022-11-28T21:32:18+00:00"}, {"version": "4.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "3434bb16407590b361a7ce9ce412e13605998362"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/3434bb16407590b361a7ce9ce412e13605998362", "type": "zip", "shasum": "", "reference": "3434bb16407590b361a7ce9ce412e13605998362"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.7.0"}, "time": "2022-11-28T21:25:37+00:00"}, {"version": "4.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "cf072ab019e05fc70e00a743b699cdafcadfa567"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/cf072ab019e05fc70e00a743b699cdafcadfa567", "type": "zip", "shasum": "", "reference": "cf072ab019e05fc70e00a743b699cdafcadfa567"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.6.1"}, "time": "2022-11-25T19:00:16+00:00"}, {"version": "4.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0c6ed2dba65d981306869c8e8f46fef478568d31"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0c6ed2dba65d981306869c8e8f46fef478568d31", "type": "zip", "shasum": "", "reference": "0c6ed2dba65d981306869c8e8f46fef478568d31"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.6.0"}, "time": "2022-11-18T22:45:30+00:00"}, {"version": "4.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "bd2a3cdb79750e976b024945fb97f5d743b1dceb"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/bd2a3cdb79750e976b024945fb97f5d743b1dceb", "type": "zip", "shasum": "", "reference": "bd2a3cdb79750e976b024945fb97f5d743b1dceb"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.5.0"}, "time": "2022-11-16T22:11:16+00:00"}, {"version": "4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "46da8d6ceb69596797c1f92cf39373db7a52072e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/46da8d6ceb69596797c1f92cf39373db7a52072e", "type": "zip", "shasum": "", "reference": "46da8d6ceb69596797c1f92cf39373db7a52072e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.4.0"}, "time": "2022-11-16T07:51:58+00:00"}, {"version": "4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "19318972a6b688e5e81a9cc89b6d955de2237b77"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/19318972a6b688e5e81a9cc89b6d955de2237b77", "type": "zip", "shasum": "", "reference": "19318972a6b688e5e81a9cc89b6d955de2237b77"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.3.0"}, "time": "2022-11-15T21:07:01+00:00"}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "da89aed0e395663df6da628c5579d89ea4dfa226"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/da89aed0e395663df6da628c5579d89ea4dfa226", "type": "zip", "shasum": "", "reference": "da89aed0e395663df6da628c5579d89ea4dfa226"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.2.2"}, "time": "2022-11-10T21:17:20+00:00"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "2f5e6d34b4464f44c138f21e9242010f5d466c94"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/2f5e6d34b4464f44c138f21e9242010f5d466c94", "type": "zip", "shasum": "", "reference": "2f5e6d34b4464f44c138f21e9242010f5d466c94"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.2.1"}, "time": "2022-11-09T18:20:18+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "61268db3e7585c36cabf6512a39516f7c192621f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/61268db3e7585c36cabf6512a39516f7c192621f", "type": "zip", "shasum": "", "reference": "61268db3e7585c36cabf6512a39516f7c192621f"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.2.0"}, "time": "2022-11-08T23:04:48+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "fca55860d8cab9306dd9c23ebf20f4bf4b644e84"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/fca55860d8cab9306dd9c23ebf20f4bf4b644e84", "type": "zip", "shasum": "", "reference": "fca55860d8cab9306dd9c23ebf20f4bf4b644e84"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.1.0"}, "time": "2022-10-15T19:09:27+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e34780346397a1359f2e64e3bb483524d7ba1727"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e34780346397a1359f2e64e3bb483524d7ba1727", "type": "zip", "shasum": "", "reference": "e34780346397a1359f2e64e3bb483524d7ba1727"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/4.0.0"}, "time": "2022-09-10T02:42:26+00:00"}, {"version": "3.37.2", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "74c483d8f14a57c36fad6a1063cab5437c312c6b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/74c483d8f14a57c36fad6a1063cab5437c312c6b", "type": "zip", "shasum": "", "reference": "74c483d8f14a57c36fad6a1063cab5437c312c6b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.37.2"}, "time": "2022-09-07T23:13:06+00:00", "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0|^9.0", "illuminate/routing": "^6.0|^7.0|^8.0|^9.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0", "league/flysystem": "^1.1.4|^2.1.1|^3.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0|^6.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^3.1.0", "shalvah/upgrader": "0.*", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^4.0|^5.0|^6.0", "symfony/yaml": "^4.0|^5.0|^6.0"}, "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.3.0", "laravel/lumen-framework": "^6.0|^7.0|^8.0|^9.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0", "pestphp/pest": "^1.21", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.0|^10.0", "symfony/css-selector": "^5.3|^6.0", "symfony/dom-crawler": "^5.3|^6.0"}}, {"version": "3.37.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4b3e48d80fd5a16afdb5ebd99bf14d8f81125040"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4b3e48d80fd5a16afdb5ebd99bf14d8f81125040", "type": "zip", "shasum": "", "reference": "4b3e48d80fd5a16afdb5ebd99bf14d8f81125040"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.37.1"}, "time": "2022-09-05T20:06:33+00:00"}, {"version": "3.37.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "722ca129ded459eef8a9f9b969d0736c1d08cb75"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/722ca129ded459eef8a9f9b969d0736c1d08cb75", "type": "zip", "shasum": "", "reference": "722ca129ded459eef8a9f9b969d0736c1d08cb75"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.37.0"}, "time": "2022-08-27T20:57:13+00:00"}, {"version": "3.36.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "a739d2441dd60e97398d2dfee5373ecc09074ef3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/a739d2441dd60e97398d2dfee5373ecc09074ef3", "type": "zip", "shasum": "", "reference": "a739d2441dd60e97398d2dfee5373ecc09074ef3"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.36.0"}, "time": "2022-08-12T20:05:34+00:00"}, {"version": "3.35.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "23314038867ee468682209ebd58f632b383d0f11"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/23314038867ee468682209ebd58f632b383d0f11", "type": "zip", "shasum": "", "reference": "23314038867ee468682209ebd58f632b383d0f11"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.35.0"}, "time": "2022-07-27T21:54:59+00:00"}, {"version": "3.34.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "185b327dd81d4c73dec7fc74fc473bfb05738c90"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/185b327dd81d4c73dec7fc74fc473bfb05738c90", "type": "zip", "shasum": "", "reference": "185b327dd81d4c73dec7fc74fc473bfb05738c90"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.34.0"}, "time": "2022-07-16T14:14:18+00:00"}, {"version": "3.33.2", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e39ce83fd7dfa72329912d885c5c061cb9b0045e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e39ce83fd7dfa72329912d885c5c061cb9b0045e", "type": "zip", "shasum": "", "reference": "e39ce83fd7dfa72329912d885c5c061cb9b0045e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.33.2"}, "time": "2022-07-09T21:42:18+00:00"}, {"version": "3.33.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "a8fbe1c9b989546534d7c41ca49d19bd323d52d8"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/a8fbe1c9b989546534d7c41ca49d19bd323d52d8", "type": "zip", "shasum": "", "reference": "a8fbe1c9b989546534d7c41ca49d19bd323d52d8"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.33.1"}, "time": "2022-07-08T20:51:52+00:00"}, {"version": "3.33.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "df677461933d26b521e3dd71c5d7bf3b9b0f30c7"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/df677461933d26b521e3dd71c5d7bf3b9b0f30c7", "type": "zip", "shasum": "", "reference": "df677461933d26b521e3dd71c5d7bf3b9b0f30c7"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.33.0"}, "time": "2022-06-27T20:24:09+00:00"}, {"version": "3.32.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9e3d35d83d9558375c28dec6011a04c87fd13e56"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9e3d35d83d9558375c28dec6011a04c87fd13e56", "type": "zip", "shasum": "", "reference": "9e3d35d83d9558375c28dec6011a04c87fd13e56"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.32.0"}, "time": "2022-06-23T19:11:08+00:00"}, {"version": "3.31.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b2052640cd815ce68471aefc3ac181145703a858"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b2052640cd815ce68471aefc3ac181145703a858", "type": "zip", "shasum": "", "reference": "b2052640cd815ce68471aefc3ac181145703a858"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.31.0"}, "time": "2022-06-16T10:55:14+00:00"}, {"version": "3.30.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "63c7e18b423048970af1e9b443ccaedd8e4d1119"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/63c7e18b423048970af1e9b443ccaedd8e4d1119", "type": "zip", "shasum": "", "reference": "63c7e18b423048970af1e9b443ccaedd8e4d1119"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.30.0"}, "time": "2022-06-11T00:00:29+00:00"}, {"version": "3.29.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "52b175a40150feb9bd8cd70b876090875780e192"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/52b175a40150feb9bd8cd70b876090875780e192", "type": "zip", "shasum": "", "reference": "52b175a40150feb9bd8cd70b876090875780e192"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.29.1"}, "time": "2022-05-22T12:56:09+00:00"}, {"version": "3.29.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4ff0d7f75ca44c2c79273c035437e706be01df81"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4ff0d7f75ca44c2c79273c035437e706be01df81", "type": "zip", "shasum": "", "reference": "4ff0d7f75ca44c2c79273c035437e706be01df81"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.29.0"}, "time": "2022-05-22T12:19:27+00:00"}, {"version": "3.28.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "376fbeb09bf20d6df7ec031889e5075827293e09"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/376fbeb09bf20d6df7ec031889e5075827293e09", "type": "zip", "shasum": "", "reference": "376fbeb09bf20d6df7ec031889e5075827293e09"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.28.0"}, "time": "2022-05-14T11:44:40+00:00"}, {"version": "3.27.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "31f534a88ef5c70f9f0c232335126d4ff9e0d71c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/31f534a88ef5c70f9f0c232335126d4ff9e0d71c", "type": "zip", "shasum": "", "reference": "31f534a88ef5c70f9f0c232335126d4ff9e0d71c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.27.0"}, "time": "2022-04-29T22:31:47+00:00"}, {"version": "3.26.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c82acf526c3bb9ce046e109ea2c1d50a0c7008ba"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c82acf526c3bb9ce046e109ea2c1d50a0c7008ba", "type": "zip", "shasum": "", "reference": "c82acf526c3bb9ce046e109ea2c1d50a0c7008ba"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.26.0"}, "time": "2022-04-03T17:34:12+00:00"}, {"version": "3.25.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4e3d72fdce5fa898e629903f1b8ce4674e341c7c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4e3d72fdce5fa898e629903f1b8ce4674e341c7c", "type": "zip", "shasum": "", "reference": "4e3d72fdce5fa898e629903f1b8ce4674e341c7c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.25.0"}, "time": "2022-03-21T11:03:16+00:00"}, {"version": "3.24.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "37165616dc373023b9b57bdffab037d1b9e1c41a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/37165616dc373023b9b57bdffab037d1b9e1c41a", "type": "zip", "shasum": "", "reference": "37165616dc373023b9b57bdffab037d1b9e1c41a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.24.1"}, "time": "2022-03-07T08:57:49+00:00"}, {"version": "3.24.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c5609a0e6d93e678b1049695b921698cb9ece0f2"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c5609a0e6d93e678b1049695b921698cb9ece0f2", "type": "zip", "shasum": "", "reference": "c5609a0e6d93e678b1049695b921698cb9ece0f2"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.24.0"}, "time": "2022-02-21T12:25:39+00:00"}, {"version": "3.23.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7a3d9feec5f0c1cdd68564e6237fffac2b36bba0"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7a3d9feec5f0c1cdd68564e6237fffac2b36bba0", "type": "zip", "shasum": "", "reference": "7a3d9feec5f0c1cdd68564e6237fffac2b36bba0"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.23.0"}, "time": "2022-01-31T09:09:39+00:00"}, {"version": "3.22.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "432abf9f68e74589c829470ef3465b4032711b85"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/432abf9f68e74589c829470ef3465b4032711b85", "type": "zip", "shasum": "", "reference": "432abf9f68e74589c829470ef3465b4032711b85"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.22.0"}, "time": "2022-01-26T13:07:23+00:00"}, {"version": "3.21.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "615754c9a99898af7bb5c5bcd2c3f6604bb20f1e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/615754c9a99898af7bb5c5bcd2c3f6604bb20f1e", "type": "zip", "shasum": "", "reference": "615754c9a99898af7bb5c5bcd2c3f6604bb20f1e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.21.0"}, "time": "2022-01-02T12:43:42+00:00", "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^3.0.2", "shalvah/upgrader": "0.*", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.90", "phpunit/phpunit": "^9.0", "symfony/css-selector": "^5.3", "symfony/dom-crawler": "^5.3"}, "suggest": {"league/fractal": "Required for transformers support"}}, {"version": "3.20.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7377a613a5534095d74a403dd953a72eaeeb1623"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7377a613a5534095d74a403dd953a72eaeeb1623", "type": "zip", "shasum": "", "reference": "7377a613a5534095d74a403dd953a72eaeeb1623"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.20.0"}, "time": "2021-12-21T15:10:24+00:00"}, {"version": "3.19.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "30ea2dc97571b4ae675cb7f4ead6c0588d97a0ee"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/30ea2dc97571b4ae675cb7f4ead6c0588d97a0ee", "type": "zip", "shasum": "", "reference": "30ea2dc97571b4ae675cb7f4ead6c0588d97a0ee"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.19.1"}, "time": "2021-12-09T08:34:17+00:00"}, {"version": "3.19.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "2ad0a96248dad9a500d252a8e00b414fd14a8b89"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/2ad0a96248dad9a500d252a8e00b414fd14a8b89", "type": "zip", "shasum": "", "reference": "2ad0a96248dad9a500d252a8e00b414fd14a8b89"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.19.0"}, "time": "2021-12-08T07:43:37+00:00"}, {"version": "3.18.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6a001e403b65567be4089e26ab18bd4f23dd2216"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6a001e403b65567be4089e26ab18bd4f23dd2216", "type": "zip", "shasum": "", "reference": "6a001e403b65567be4089e26ab18bd4f23dd2216"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.18.0"}, "time": "2021-11-16T09:59:19+00:00"}, {"version": "3.17.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "d61afc38764834eb15e108fca1ec7713b0957905"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/d61afc38764834eb15e108fca1ec7713b0957905", "type": "zip", "shasum": "", "reference": "d61afc38764834eb15e108fca1ec7713b0957905"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.17.0"}, "time": "2021-11-28T08:27:29+00:00"}, {"version": "3.16.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6a001e403b65567be4089e26ab18bd4f23dd2216"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6a001e403b65567be4089e26ab18bd4f23dd2216", "type": "zip", "shasum": "", "reference": "6a001e403b65567be4089e26ab18bd4f23dd2216"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.16.0"}, "time": "2021-11-16T09:59:19+00:00"}, {"version": "3.15.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "fff9c29b80e98f6ea1a3fef04bf5f672ea3b4142"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/fff9c29b80e98f6ea1a3fef04bf5f672ea3b4142", "type": "zip", "shasum": "", "reference": "fff9c29b80e98f6ea1a3fef04bf5f672ea3b4142"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.15.0"}, "time": "2021-11-08T15:54:41+00:00"}, {"version": "3.14.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "20f45942ef4bdf5cf5243de382cb863226a2d654"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/20f45942ef4bdf5cf5243de382cb863226a2d654", "type": "zip", "shasum": "", "reference": "20f45942ef4bdf5cf5243de382cb863226a2d654"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.14.1"}, "time": "2021-11-02T10:51:50+00:00"}, {"version": "3.14.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "553530f91a34f0f73131b1fa55125842a23a9f7a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/553530f91a34f0f73131b1fa55125842a23a9f7a", "type": "zip", "shasum": "", "reference": "553530f91a34f0f73131b1fa55125842a23a9f7a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.14.0"}, "time": "2021-10-29T20:04:08+00:00"}, {"version": "3.13.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "fb8b28f3511ae1d6f7ef3d5f2d7c37446944c75f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/fb8b28f3511ae1d6f7ef3d5f2d7c37446944c75f", "type": "zip", "shasum": "", "reference": "fb8b28f3511ae1d6f7ef3d5f2d7c37446944c75f"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.13.0"}, "time": "2021-10-26T18:23:53+00:00"}, {"version": "3.12.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "90808bd948e11e93a5a21faf1aa29f5efb8de577"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/90808bd948e11e93a5a21faf1aa29f5efb8de577", "type": "zip", "shasum": "", "reference": "90808bd948e11e93a5a21faf1aa29f5efb8de577"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.12.1"}, "time": "2021-10-25T08:37:30+00:00"}, {"version": "3.12.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "2819fa98855d8a8b17024a3feeb56e9891f1e6ad"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/2819fa98855d8a8b17024a3feeb56e9891f1e6ad", "type": "zip", "shasum": "", "reference": "2819fa98855d8a8b17024a3feeb56e9891f1e6ad"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.12.0"}, "time": "2021-10-24T12:51:03+00:00"}, {"version": "3.11.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "17bd5a9f0449642a85ccbd46011c6f9afe4ff572"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/17bd5a9f0449642a85ccbd46011c6f9afe4ff572", "type": "zip", "shasum": "", "reference": "17bd5a9f0449642a85ccbd46011c6f9afe4ff572"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.11.1"}, "time": "2021-09-23T13:00:08+00:00"}, {"version": "3.11.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "093a1f24435d44e82aac33e9e2d8fee3a54fdcce"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/093a1f24435d44e82aac33e9e2d8fee3a54fdcce", "type": "zip", "shasum": "", "reference": "093a1f24435d44e82aac33e9e2d8fee3a54fdcce"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.11.0"}, "time": "2021-09-22T01:25:15+00:00"}, {"version": "3.10.3", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "d4e7d2f89cda217f9bb3b65fdb3091f8fe857ad9"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/d4e7d2f89cda217f9bb3b65fdb3091f8fe857ad9", "type": "zip", "shasum": "", "reference": "d4e7d2f89cda217f9bb3b65fdb3091f8fe857ad9"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.10.3"}, "time": "2021-09-20T13:49:02+00:00"}, {"version": "3.10.2", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b4f78bfb949382491c12f1857c52cea433065f2d"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b4f78bfb949382491c12f1857c52cea433065f2d", "type": "zip", "shasum": "", "reference": "b4f78bfb949382491c12f1857c52cea433065f2d"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.10.2"}, "time": "2021-09-10T21:21:19+00:00"}, {"version": "3.10.1", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0d50bb27c9153444d9b6bbb8220f116103d0f852"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0d50bb27c9153444d9b6bbb8220f116103d0f852", "type": "zip", "shasum": "", "reference": "0d50bb27c9153444d9b6bbb8220f116103d0f852"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.10.1"}, "time": "2021-09-09T19:57:34+00:00"}, {"version": "3.10.0", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "a8694082369dcb9a8651a0f39e39b9e2d3e82357"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/a8694082369dcb9a8651a0f39e39b9e2d3e82357", "type": "zip", "shasum": "", "reference": "a8694082369dcb9a8651a0f39e39b9e2d3e82357"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.10.0"}, "time": "2021-09-09T19:23:28+00:00"}, {"version": "3.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "a9cfc732fa5e31b4a16947842b2278d253abb419"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/a9cfc732fa5e31b4a16947842b2278d253abb419", "type": "zip", "shasum": "", "reference": "a9cfc732fa5e31b4a16947842b2278d253abb419"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.9.1"}, "time": "2021-08-26T12:53:10+00:00"}, {"version": "3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4395bf56952c8a6dabe961829dac5cdacb45f1d7"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4395bf56952c8a6dabe961829dac5cdacb45f1d7", "type": "zip", "shasum": "", "reference": "4395bf56952c8a6dabe961829dac5cdacb45f1d7"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.9.0"}, "time": "2021-08-21T09:15:23+00:00"}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "058559e9b26695aa9b9f3284ba64407dc480e01a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/058559e9b26695aa9b9f3284ba64407dc480e01a", "type": "zip", "shasum": "", "reference": "058559e9b26695aa9b9f3284ba64407dc480e01a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.8.0"}, "time": "2021-07-28T20:30:22+00:00"}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0538797bf90749bb7e915f193334e0391a54ca46"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0538797bf90749bb7e915f193334e0391a54ca46", "type": "zip", "shasum": "", "reference": "0538797bf90749bb7e915f193334e0391a54ca46"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.7.0"}, "time": "2021-07-22T12:30:18+00:00", "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^3.0.2", "spatie/data-transfer-object": "^2.6|^3.0", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "3.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b9cb1888edfbc30d4b82b395dd8b74eecba87d3a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b9cb1888edfbc30d4b82b395dd8b74eecba87d3a", "type": "zip", "shasum": "", "reference": "b9cb1888edfbc30d4b82b395dd8b74eecba87d3a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.6.3"}, "time": "2021-07-20T13:28:50+00:00", "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^3.0.2", "spatie/data-transfer-object": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "3.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9794ed3bde26caa4cbfa3211369ad4d369a0d6bd"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9794ed3bde26caa4cbfa3211369ad4d369a0d6bd", "type": "zip", "shasum": "", "reference": "9794ed3bde26caa4cbfa3211369ad4d369a0d6bd"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.6.2"}, "time": "2021-07-17T09:30:48+00:00"}, {"version": "3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "518be53254280707e305f27f33556533282af5e1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/518be53254280707e305f27f33556533282af5e1", "type": "zip", "shasum": "", "reference": "518be53254280707e305f27f33556533282af5e1"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.6.1"}, "time": "2021-07-16T19:36:59+00:00"}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "3e4ef31095f6b8a509c1ab5f1af0fa4dc39c2112"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/3e4ef31095f6b8a509c1ab5f1af0fa4dc39c2112", "type": "zip", "shasum": "", "reference": "3e4ef31095f6b8a509c1ab5f1af0fa4dc39c2112"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.6.0"}, "time": "2021-07-13T14:51:20+00:00"}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c91eed5499525b53d0b9c1d8ff943bca298e2ef1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c91eed5499525b53d0b9c1d8ff943bca298e2ef1", "type": "zip", "shasum": "", "reference": "c91eed5499525b53d0b9c1d8ff943bca298e2ef1"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.5.2"}, "time": "2021-07-12T10:25:11+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4fad273447b548c13f899948b4a44e50f5d28590"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4fad273447b548c13f899948b4a44e50f5d28590", "type": "zip", "shasum": "", "reference": "4fad273447b548c13f899948b4a44e50f5d28590"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.5.1"}, "time": "2021-07-06T11:44:11+00:00"}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "258b973525365da04d1ca7977f5a2d2958e2e772"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/258b973525365da04d1ca7977f5a2d2958e2e772", "type": "zip", "shasum": "", "reference": "258b973525365da04d1ca7977f5a2d2958e2e772"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.5.0"}, "time": "2021-07-05T22:12:16+00:00"}, {"version": "3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "77c27beb3cdb5acc2cb686a001c02b6489ac8946"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/77c27beb3cdb5acc2cb686a001c02b6489ac8946", "type": "zip", "shasum": "", "reference": "77c27beb3cdb5acc2cb686a001c02b6489ac8946"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.4.3"}, "time": "2021-07-05T20:49:34+00:00"}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "d0a69809f2f1c7270ef6300a87807ad026ae793f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/d0a69809f2f1c7270ef6300a87807ad026ae793f", "type": "zip", "shasum": "", "reference": "d0a69809f2f1c7270ef6300a87807ad026ae793f"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.4.2"}, "time": "2021-07-05T17:43:23+00:00"}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "5f0a9c15e71361719da6513e509f2cf1efe510db"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/5f0a9c15e71361719da6513e509f2cf1efe510db", "type": "zip", "shasum": "", "reference": "5f0a9c15e71361719da6513e509f2cf1efe510db"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.4.1"}, "time": "2021-07-02T13:26:11+00:00", "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^9.0"}}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4b59fb99a770a479c1a16ddbaaab932f1f4b5912"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4b59fb99a770a479c1a16ddbaaab932f1f4b5912", "type": "zip", "shasum": "", "reference": "4b59fb99a770a479c1a16ddbaaab932f1f4b5912"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.4.0"}, "time": "2021-07-01T12:58:57+00:00", "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^3.0", "spatie/data-transfer-object": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "f79f5bad307bd830353348e12245c1120c2b562e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/f79f5bad307bd830353348e12245c1120c2b562e", "type": "zip", "shasum": "", "reference": "f79f5bad307bd830353348e12245c1120c2b562e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.3.2"}, "time": "2021-06-30T17:34:12+00:00"}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c156352cdf18013b3211c6e246c9e40944dc6019"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c156352cdf18013b3211c6e246c9e40944dc6019", "type": "zip", "shasum": "", "reference": "c156352cdf18013b3211c6e246c9e40944dc6019"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.3.1"}, "time": "2021-06-29T16:44:43+00:00", "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "spatie/data-transfer-object": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "8b9325270e728858973ac49ec6393e6be970129b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/8b9325270e728858973ac49ec6393e6be970129b", "type": "zip", "shasum": "", "reference": "8b9325270e728858973ac49ec6393e6be970129b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.3.0"}, "time": "2021-06-25T12:31:13+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "d1a9a815098858b4485f3a061cef33a8cc5d2285"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/d1a9a815098858b4485f3a061cef33a8cc5d2285", "type": "zip", "shasum": "", "reference": "d1a9a815098858b4485f3a061cef33a8cc5d2285"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.2.0"}, "time": "2021-06-24T22:40:27+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "cda557e030a2f6f3e3b3648b546d3d71878d0f71"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/cda557e030a2f6f3e3b3648b546d3d71878d0f71", "type": "zip", "shasum": "", "reference": "cda557e030a2f6f3e3b3648b546d3d71878d0f71"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.1.0"}, "time": "2021-06-18T11:46:34+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "38c72413de1a4a4cce7a1793b341eef80b5a55e5"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/38c72413de1a4a4cce7a1793b341eef80b5a55e5", "type": "zip", "shasum": "", "reference": "38c72413de1a4a4cce7a1793b341eef80b5a55e5"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.0.3"}, "time": "2021-06-18T10:02:34+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "2e4b0b4485eba540e1c0743cbfa6de7300c01d9e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/2e4b0b4485eba540e1c0743cbfa6de7300c01d9e", "type": "zip", "shasum": "", "reference": "2e4b0b4485eba540e1c0743cbfa6de7300c01d9e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.0.2"}, "time": "2021-06-11T21:50:20+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "277c56e553e74b396ed7fb6dd55f9f14b529a116"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/277c56e553e74b396ed7fb6dd55f9f14b529a116", "type": "zip", "shasum": "", "reference": "277c56e553e74b396ed7fb6dd55f9f14b529a116"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.0.1"}, "time": "2021-06-08T09:18:29+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "456d9204ef326366d0285d30d8db66197ab253fa"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/456d9204ef326366d0285d30d8db66197ab253fa", "type": "zip", "shasum": "", "reference": "456d9204ef326366d0285d30d8db66197ab253fa"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.0.0"}, "time": "2021-06-07T14:16:28+00:00"}, {"version": "2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0f0364685ed27cd9227b291da6b06039f1ae286c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0f0364685ed27cd9227b291da6b06039f1ae286c", "type": "zip", "shasum": "", "reference": "0f0364685ed27cd9227b291da6b06039f1ae286c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.10"}, "time": "2021-06-30T10:28:43+00:00", "autoload": {"psr-4": {"Knuckles\\Scribe\\": "src/"}}, "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^5.0|^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^9.0"}}, {"version": "2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6689944d232dbc22fe68f78f99d27e5620cbff7b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6689944d232dbc22fe68f78f99d27e5620cbff7b", "type": "zip", "shasum": "", "reference": "6689944d232dbc22fe68f78f99d27e5620cbff7b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.9"}, "time": "2021-06-09T11:29:52+00:00"}, {"version": "2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "69a5211bf1087f286823caafa98b2c32055d9001"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/69a5211bf1087f286823caafa98b2c32055d9001", "type": "zip", "shasum": "", "reference": "69a5211bf1087f286823caafa98b2c32055d9001"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.8"}, "time": "2021-06-08T09:25:10+00:00"}, {"version": "2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "277c56e553e74b396ed7fb6dd55f9f14b529a116"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/277c56e553e74b396ed7fb6dd55f9f14b529a116", "type": "zip", "shasum": "", "reference": "277c56e553e74b396ed7fb6dd55f9f14b529a116"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/3.0.1"}, "time": "2021-06-08T09:18:29+00:00", "autoload": {"psr-4": {"Knuckles\\Camel\\": "camel/", "Knuckles\\Scribe\\": "src/"}}, "require": {"php": ">=7.4", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nikic/php-parser": "^4.10", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "spatie/data-transfer-object": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^9.0"}}, {"version": "2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "2fa414b656594f3b9d0b6c6b9704d55cde892c28"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/2fa414b656594f3b9d0b6c6b9704d55cde892c28", "type": "zip", "shasum": "", "reference": "2fa414b656594f3b9d0b6c6b9704d55cde892c28"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.6"}, "time": "2021-05-27T18:30:12+00:00", "autoload": {"psr-4": {"Knuckles\\Scribe\\": "src/"}}, "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "ext-pdo": "*", "erusev/parsedown": "^1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^5.0|^6.0", "dms/phpunit-arraysubset-asserts": "^0.2.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^9.0"}}, {"version": "2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "546185cf41efa501cd0715851dfeadd4e28c9a83"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/546185cf41efa501cd0715851dfeadd4e28c9a83", "type": "zip", "shasum": "", "reference": "546185cf41efa501cd0715851dfeadd4e28c9a83"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.5"}, "time": "2021-05-27T16:11:45+00:00"}, {"version": "2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "19b5b0aabef0dc2eed3e4d2d72b7c2fda6ac574b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/19b5b0aabef0dc2eed3e4d2d72b7c2fda6ac574b", "type": "zip", "shasum": "", "reference": "19b5b0aabef0dc2eed3e4d2d72b7c2fda6ac574b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.4"}, "time": "2021-05-27T13:36:39+00:00"}, {"version": "2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "0482f018c318fcf85137426a0f4fa35561996921"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/0482f018c318fcf85137426a0f4fa35561996921", "type": "zip", "shasum": "", "reference": "0482f018c318fcf85137426a0f4fa35561996921"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.3"}, "time": "2021-05-25T11:03:37+00:00"}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9a067a1dbe8293a9b162eb0fd2edcb70cf44b0a4"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9a067a1dbe8293a9b162eb0fd2edcb70cf44b0a4", "type": "zip", "shasum": "", "reference": "9a067a1dbe8293a9b162eb0fd2edcb70cf44b0a4"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.2"}, "time": "2021-05-22T07:46:51+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b405cdc0166484f68c6efc1117b741a470029413"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b405cdc0166484f68c6efc1117b741a470029413", "type": "zip", "shasum": "", "reference": "b405cdc0166484f68c6efc1117b741a470029413"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.1"}, "time": "2021-05-21T15:39:45+00:00"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "cf9d9765ac4fd48e4bc02d18df22411ca2cee1de"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/cf9d9765ac4fd48e4bc02d18df22411ca2cee1de", "type": "zip", "shasum": "", "reference": "cf9d9765ac4fd48e4bc02d18df22411ca2cee1de"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.7.0"}, "time": "2021-05-21T12:29:27+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "ed82fb27e5b1dbaa67459e1db9d59bcb8eb2aa65"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/ed82fb27e5b1dbaa67459e1db9d59bcb8eb2aa65", "type": "zip", "shasum": "", "reference": "ed82fb27e5b1dbaa67459e1db9d59bcb8eb2aa65"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.6.0"}, "time": "2021-04-08T08:03:24+00:00"}, {"version": "2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9458992e24dcb65d6e2562a6a24185f6ba7413ec"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9458992e24dcb65d6e2562a6a24185f6ba7413ec", "type": "zip", "shasum": "", "reference": "9458992e24dcb65d6e2562a6a24185f6ba7413ec"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.5.3"}, "time": "2021-02-11T12:10:52+00:00"}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "ecb96c34ee520bc695e35ec63d7e16cedf3a407c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/ecb96c34ee520bc695e35ec63d7e16cedf3a407c", "type": "zip", "shasum": "", "reference": "ecb96c34ee520bc695e35ec63d7e16cedf3a407c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.5.2"}, "time": "2021-01-25T22:24:31+00:00"}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "5b571f4971ad0cf3b68561508924b90de450cf6a"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/5b571f4971ad0cf3b68561508924b90de450cf6a", "type": "zip", "shasum": "", "reference": "5b571f4971ad0cf3b68561508924b90de450cf6a"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.5.1"}, "time": "2020-12-16T14:51:13+00:00"}, {"version": "2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "49b7fbf6fe7d865cf8181a9d16e5ecadbaec5fad"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/49b7fbf6fe7d865cf8181a9d16e5ecadbaec5fad", "type": "zip", "shasum": "", "reference": "49b7fbf6fe7d865cf8181a9d16e5ecadbaec5fad"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.4.2"}, "time": "2020-12-01T10:52:38+00:00", "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e7b8408dce19eb00f79c49db6d6644c5b017a2be"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e7b8408dce19eb00f79c49db6d6644c5b017a2be", "type": "zip", "shasum": "", "reference": "e7b8408dce19eb00f79c49db6d6644c5b017a2be"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.4.1"}, "time": "2020-11-30T12:30:12+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "290a770e4cac1a381c69d693f5d459de40379855"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/290a770e4cac1a381c69d693f5d459de40379855", "type": "zip", "shasum": "", "reference": "290a770e4cac1a381c69d693f5d459de40379855"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.4.0"}, "time": "2020-11-30T11:43:31+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e58849abb4bd5371480fd32cc3fbc3718b7fc477"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e58849abb4bd5371480fd32cc3fbc3718b7fc477", "type": "zip", "shasum": "", "reference": "e58849abb4bd5371480fd32cc3fbc3718b7fc477"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.3.0"}, "time": "2020-11-15T14:48:25+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "amphp/parallel-functions": "^1.0", "erusev/parsedown": "^1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b2d926435b80342d7a19b005bdf14b1ef5dfea8f"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b2d926435b80342d7a19b005bdf14b1ef5dfea8f", "type": "zip", "shasum": "", "reference": "b2d926435b80342d7a19b005bdf14b1ef5dfea8f"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.2.1"}, "time": "2020-11-14T13:39:04+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "8168c2007b468a7099bb6eddf8e1fcbf7d9a2253"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/8168c2007b468a7099bb6eddf8e1fcbf7d9a2253", "type": "zip", "shasum": "", "reference": "8168c2007b468a7099bb6eddf8e1fcbf7d9a2253"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.2.0"}, "time": "2020-11-11T16:41:31+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "657ead8956c8156932fe3726060e4ea267fc6bd1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/657ead8956c8156932fe3726060e4ea267fc6bd1", "type": "zip", "shasum": "", "reference": "657ead8956c8156932fe3726060e4ea267fc6bd1"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.1.0"}, "time": "2020-11-10T16:45:20+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fakerphp/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e6934dc6e45d285b15b98aa47a87e1cf7c495ab8"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e6934dc6e45d285b15b98aa47a87e1cf7c495ab8", "type": "zip", "shasum": "", "reference": "e6934dc6e45d285b15b98aa47a87e1cf7c495ab8"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.3"}, "time": "2020-11-02T12:00:26+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "a2d6f5e5c071ccbf1d349d094fe102580175a677"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/a2d6f5e5c071ccbf1d349d094fe102580175a677", "type": "zip", "shasum": "", "reference": "a2d6f5e5c071ccbf1d349d094fe102580175a677"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.2"}, "time": "2020-10-30T14:45:29+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7fde3587620664eb6436a20040b34eeb79a3ca5c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7fde3587620664eb6436a20040b34eeb79a3ca5c", "type": "zip", "shasum": "", "reference": "7fde3587620664eb6436a20040b34eeb79a3ca5c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.1"}, "time": "2020-10-26T21:40:41+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4564805f3c3e4a02daf53e7c7669e2ad0f1de419"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4564805f3c3e4a02daf53e7c7669e2ad0f1de419", "type": "zip", "shasum": "", "reference": "4564805f3c3e4a02daf53e7c7669e2ad0f1de419"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.0"}, "time": "2020-10-24T14:35:37+00:00"}, {"version": "2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "be69987aa4bba7aeca5373466de297099dc30b5e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/be69987aa4bba7aeca5373466de297099dc30b5e", "type": "zip", "shasum": "", "reference": "be69987aa4bba7aeca5373466de297099dc30b5e"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.0-beta.1"}, "time": "2020-10-16T11:07:58+00:00"}, {"version": "2.0.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "472b042ff31d1ec3b20ece01b7549b35dba2742c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/472b042ff31d1ec3b20ece01b7549b35dba2742c", "type": "zip", "shasum": "", "reference": "472b042ff31d1ec3b20ece01b7549b35dba2742c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.0-beta"}, "time": "2020-10-08T14:24:06+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/legacy-factories": "^1.0.4", "league/fractal": "^0.19.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0"}}, {"version": "2.0.0-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "8a1e845c2c37ad198404a574fdf346eadd03c394"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/8a1e845c2c37ad198404a574fdf346eadd03c394", "type": "zip", "shasum": "", "reference": "8a1e845c2c37ad198404a574fdf346eadd03c394"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/2.0.0-alpha"}, "time": "2020-10-01T07:31:17+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.6", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "nikic/fast-route": "^1.3", "orchestra/testbench": "^4.0|^5.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "f0de9961af1c505127b76c74f02a07259e722d45"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/f0de9961af1c505127b76c74f02a07259e722d45", "type": "zip", "shasum": "", "reference": "f0de9961af1c505127b76c74f02a07259e722d45"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.9.1"}, "time": "2020-10-19T12:37:29+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "5.8.*|^6.0|^7.0|^8.0", "illuminate/routing": "5.8.*|^6.0|^7.0|^8.0", "illuminate/support": "5.8.*|^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.5", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/legacy-factories": "^1.0.4", "laravel/lumen-framework": "5.8.*|^6.0|^7.0|^8.0", "league/fractal": "^0.19.0", "orchestra/testbench": "^3.7|^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "640af34d029a6a3a6498e1af613a3b5818bfb8b3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/640af34d029a6a3a6498e1af613a3b5818bfb8b3", "type": "zip", "shasum": "", "reference": "640af34d029a6a3a6498e1af613a3b5818bfb8b3"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.9.0"}, "time": "2020-10-01T07:22:31+00:00"}, {"version": "1.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "4054bc4b750a1f4a946a73ca4925cd8d829076d9"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/4054bc4b750a1f4a946a73ca4925cd8d829076d9", "type": "zip", "shasum": "", "reference": "4054bc4b750a1f4a946a73ca4925cd8d829076d9"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.8.3"}, "time": "2020-09-17T21:59:56+00:00"}, {"version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "3dc2c8d93fcfd8e650b0234ddda162e58fd73e3d"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/3dc2c8d93fcfd8e650b0234ddda162e58fd73e3d", "type": "zip", "shasum": "", "reference": "3dc2c8d93fcfd8e650b0234ddda162e58fd73e3d"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.8.2"}, "time": "2020-09-17T21:33:34+00:00"}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "5c9cef302fedb0b9c308fec42ac40ca2bc2b33ca"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/5c9cef302fedb0b9c308fec42ac40ca2bc2b33ca", "type": "zip", "shasum": "", "reference": "5c9cef302fedb0b9c308fec42ac40ca2bc2b33ca"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.8.1"}, "time": "2020-09-17T20:41:32+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e5f61aba6effeccc97d60851a773bb7f81e33ff1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e5f61aba6effeccc97d60851a773bb7f81e33ff1", "type": "zip", "shasum": "", "reference": "e5f61aba6effeccc97d60851a773bb7f81e33ff1"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.8.0"}, "time": "2020-09-15T20:20:58+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "fef8aedb08402aee831f7c83169a8de15259b2ff"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/fef8aedb08402aee831f7c83169a8de15259b2ff", "type": "zip", "shasum": "", "reference": "fef8aedb08402aee831f7c83169a8de15259b2ff"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.7.0"}, "time": "2020-09-12T14:14:21+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0|^8.0", "illuminate/routing": "^5.8|^6.0|^7.0|^8.0", "illuminate/support": "^5.8|^6.0|^7.0|^8.0", "knuckleswtf/pastel": "^1.3.5", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/legacy-factories": "^1.0.4", "league/fractal": "^0.19.0", "orchestra/testbench": "^3.7|^4.0|^5.0|^6.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9c46db3e2b1b91836c3556b342871609d22f189b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9c46db3e2b1b91836c3556b342871609d22f189b", "type": "zip", "shasum": "", "reference": "9c46db3e2b1b91836c3556b342871609d22f189b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.6.0"}, "time": "2020-09-08T12:09:33+00:00", "require": {"php": ">=7.2.5", "ext-fileinfo": "*", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0", "illuminate/routing": "^5.8|^6.0|^7.0", "illuminate/support": "^5.8|^6.0|^7.0", "knuckleswtf/pastel": "^1.3.5", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/lumen-framework": "^5.8|^6.0|^7.0", "league/fractal": "^0.19.0", "orchestra/testbench": "^3.7|^4.0|^5.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "76a8051d6df1f9cff80f675213303e69ebc000af"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/76a8051d6df1f9cff80f675213303e69ebc000af", "type": "zip", "shasum": "", "reference": "76a8051d6df1f9cff80f675213303e69ebc000af"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.5.0"}, "time": "2020-09-03T21:01:26+00:00", "require": {"php": ">=7.2.5", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0", "illuminate/routing": "^5.8|^6.0|^7.0", "illuminate/support": "^5.8|^6.0|^7.0", "knuckleswtf/pastel": "^1.3.3", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/lumen-framework": "^5.7|^6.0|^7.0", "league/fractal": "^0.19.0", "orchestra/testbench": "^3.7|^4.0|^5.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0|^9.0"}}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "f6f01ac9bd44e83bc1bc335cce441d678e7d7ff0"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/f6f01ac9bd44e83bc1bc335cce441d678e7d7ff0", "type": "zip", "shasum": "", "reference": "f6f01ac9bd44e83bc1bc335cce441d678e7d7ff0"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.4.1"}, "time": "2020-08-24T17:36:10+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "812282084092cef62b14b4237b8e6b3c8566f303"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/812282084092cef62b14b4237b8e6b3c8566f303", "type": "zip", "shasum": "", "reference": "812282084092cef62b14b4237b8e6b3c8566f303"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.4.0"}, "time": "2020-08-23T20:11:04+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "7e588d52cfd09588ab15e58a051f67a8d35d9bda"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/7e588d52cfd09588ab15e58a051f67a8d35d9bda", "type": "zip", "shasum": "", "reference": "7e588d52cfd09588ab15e58a051f67a8d35d9bda"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/master"}, "time": "2020-07-17T01:23:59+00:00", "require": {"php": ">=7.2.5", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0", "illuminate/routing": "^5.8|^6.0|^7.0", "illuminate/support": "^5.8|^6.0|^7.0", "knuckleswtf/pastel": "^1.3.3", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0|^5.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "5de8a5d05c0288fe83a6cb16fcb28e2d171722ce"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/5de8a5d05c0288fe83a6cb16fcb28e2d171722ce", "type": "zip", "shasum": "", "reference": "5de8a5d05c0288fe83a6cb16fcb28e2d171722ce"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.2.0"}, "time": "2020-07-05T14:38:54+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "b2117ea66120cfd6eaf3266a98ee453d75aede26"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/b2117ea66120cfd6eaf3266a98ee453d75aede26", "type": "zip", "shasum": "", "reference": "b2117ea66120cfd6eaf3266a98ee453d75aede26"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/master"}, "time": "2020-07-03T19:32:00+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "42d034f6267518c05050ccd18da2989bdfce35d3"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/42d034f6267518c05050ccd18da2989bdfce35d3", "type": "zip", "shasum": "", "reference": "42d034f6267518c05050ccd18da2989bdfce35d3"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.1.0"}, "time": "2020-06-01T05:07:12+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "6721694c27da4ebfb0098101c0a7a164badda3c6"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/6721694c27da4ebfb0098101c0a7a164badda3c6", "type": "zip", "shasum": "", "reference": "6721694c27da4ebfb0098101c0a7a164badda3c6"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.0.3"}, "time": "2020-05-25T10:55:22+00:00"}, {"keywords": ["documentation", "api", "laravel"], "version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e96fdd428c0ff6b13bdfa86ffb28194a5225ca44"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e96fdd428c0ff6b13bdfa86ffb28194a5225ca44", "type": "zip", "shasum": "", "reference": "e96fdd428c0ff6b13bdfa86ffb28194a5225ca44"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.0.2"}, "time": "2020-05-24T20:47:18+00:00", "require": {"php": ">=7.2.5", "ext-json": "*", "erusev/parsedown": "^1.7.4", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0", "illuminate/routing": "^5.8|^6.0|^7.0", "illuminate/support": "^5.8|^6.0|^7.0", "knuckleswtf/pastel": "^1.3.3", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0"}, "require-dev": {"brianium/paratest": "^4.0", "dms/phpunit-arraysubset-asserts": "^0.1.0", "laravel/lumen-framework": "^5.7|^6.0|^7.0", "league/fractal": "^0.19.0", "orchestra/testbench": "^3.7|^4.0|^5.0", "phpstan/phpstan": "^0.12.19", "phpunit/phpunit": "^8.0"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "c8f1881b4e25d8d3777341e0b472c760a636b03b"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/c8f1881b4e25d8d3777341e0b472c760a636b03b", "type": "zip", "shasum": "", "reference": "c8f1881b4e25d8d3777341e0b472c760a636b03b"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/master"}, "time": "2020-05-23T12:40:26+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "627ba652e99cb916c70e5fa3d74e9b37855ed82e"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/627ba652e99cb916c70e5fa3d74e9b37855ed82e", "type": "zip", "shasum": "", "reference": "627ba652e99cb916c70e5fa3d74e9b37855ed82e"}, "time": "2020-05-23T11:38:49+00:00", "require": {"php": ">=7.2.5", "ext-json": "*", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0", "illuminate/routing": "^5.8|^6.0|^7.0", "illuminate/support": "^5.8|^6.0|^7.0", "knuckleswtf/pastel": "^1.3.3", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0"}}, {"version": "1.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "e22c9f65287ea4e5f4aa2cfe4f72d93f58a30fe1"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/e22c9f65287ea4e5f4aa2cfe4f72d93f58a30fe1", "type": "zip", "shasum": "", "reference": "e22c9f65287ea4e5f4aa2cfe4f72d93f58a30fe1"}, "time": "2020-05-21T11:47:12+00:00"}, {"version": "1.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "8c72064e3dec31a792faad3731edabe0061960db"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/8c72064e3dec31a792faad3731edabe0061960db", "type": "zip", "shasum": "", "reference": "8c72064e3dec31a792faad3731edabe0061960db"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.0.0-beta3"}, "time": "2020-05-19T22:03:05+00:00", "require": {"php": ">=7.2.5", "ext-json": "*", "fzaninotto/faker": "^1.9.1", "illuminate/console": "^5.8|^6.0|^7.0", "illuminate/routing": "^5.8|^6.0|^7.0", "illuminate/support": "^5.8|^6.0|^7.0", "knuckleswtf/pastel": "^1.3.1", "league/flysystem": "^1.0", "mpociot/reflection-docblock": "^1.0.1", "nunomaduro/collision": "^3.0|^4.0", "ramsey/uuid": "^3.8|^4.0", "shalvah/clara": "^2.6", "symfony/var-exporter": "^4.0|^5.0"}}, {"version": "1.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "9453be292a0163f903a83cfce455fc50b764a58c"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/9453be292a0163f903a83cfce455fc50b764a58c", "type": "zip", "shasum": "", "reference": "9453be292a0163f903a83cfce455fc50b764a58c"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.0.0-beta2"}, "time": "2020-05-19T21:32:31+00:00"}, {"version": "1.0.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/knuckleswtf/scribe.git", "type": "git", "reference": "ce5b8b8fa9fc443f743119aadf4d73b6053d3aac"}, "dist": {"url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/ce5b8b8fa9fc443f743119aadf4d73b6053d3aac", "type": "zip", "shasum": "", "reference": "ce5b8b8fa9fc443f743119aadf4d73b6053d3aac"}, "support": {"issues": "https://github.com/knuckleswtf/scribe/issues", "source": "https://github.com/knuckleswtf/scribe/tree/1.0.0-beta"}, "time": "2020-05-15T16:54:30+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 01 May 2025 01:17:15 GMT"}