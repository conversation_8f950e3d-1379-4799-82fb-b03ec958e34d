{"minified": "composer/2.0", "packages": {"php-di/phpdoc-reader": [{"name": "php-di/phpdoc-reader", "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "homepage": "", "version": "2.2.1", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/66daff34cbd2627740ffec9469ffbac9f8c8185c", "type": "zip", "shasum": "", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c"}, "type": "library", "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/2.2.1"}, "funding": [], "time": "2020-10-12T12:39:22+00:00", "autoload": {"psr-4": {"PhpDocReader\\": "src/PhpDocReader"}}, "require": {"php": ">=7.2.0"}, "require-dev": {"phpunit/phpunit": "^8.5|^9.0", "mnapoli/hard-mode": "~0.3.0"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "d6e59b509b65cf721eb15d7ab43ea687f467cd46"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/d6e59b509b65cf721eb15d7ab43ea687f467cd46", "type": "zip", "shasum": "", "reference": "d6e59b509b65cf721eb15d7ab43ea687f467cd46"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/2.2.0"}, "time": "2020-10-12T11:33:00+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "15678f7451c020226807f520efb867ad26fbbfcf"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/15678f7451c020226807f520efb867ad26fbbfcf", "type": "zip", "shasum": "", "reference": "15678f7451c020226807f520efb867ad26fbbfcf"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/master"}, "time": "2019-09-26T11:24:58+00:00", "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.6"}, "funding": "__unset"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "7d0de60b9341933c8afd172a6255cd7557601e0e"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/7d0de60b9341933c8afd172a6255cd7557601e0e", "type": "zip", "shasum": "", "reference": "7d0de60b9341933c8afd172a6255cd7557601e0e"}, "time": "2018-02-18T17:39:01+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "83f5ead159defccfa8e7092e5b6c1c533b326d68"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/83f5ead159defccfa8e7092e5b6c1c533b326d68", "type": "zip", "shasum": "", "reference": "83f5ead159defccfa8e7092e5b6c1c533b326d68"}, "time": "2015-11-29T10:34:25+00:00", "require": {"php": ">=5.3.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "21dce5e29f640d655e7b4583ecfb7d166127a5da"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/21dce5e29f640d655e7b4583ecfb7d166127a5da", "type": "zip", "shasum": "", "reference": "21dce5e29f640d655e7b4583ecfb7d166127a5da"}, "time": "2015-06-01T14:23:20+00:00"}, {"description": "", "version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "8a6e123fd1ce54f7fcbd71747b3bf04e465da229"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/8a6e123fd1ce54f7fcbd71747b3bf04e465da229", "type": "zip", "shasum": "", "reference": "8a6e123fd1ce54f7fcbd71747b3bf04e465da229"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/1.3.0"}, "time": "2014-08-21T08:20:45+00:00", "autoload": {"psr-0": {"UnitTest": "tests/", "PhpDocReader": "src/"}}, "require": {"php": ">=5.3.0", "doctrine/annotations": "1.*"}, "require-dev": "__unset"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "0da1a14d38df6233d7783d9917cdccba9bcfe536"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/0da1a14d38df6233d7783d9917cdccba9bcfe536", "type": "zip", "shasum": "", "reference": "0da1a14d38df6233d7783d9917cdccba9bcfe536"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/1.2.1"}, "time": "2014-01-28T14:49:44+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "5d5c72b6ea0fcfe61b74f6e04a2c8f47ca9f9499"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/5d5c72b6ea0fcfe61b74f6e04a2c8f47ca9f9499", "type": "zip", "shasum": "", "reference": "5d5c72b6ea0fcfe61b74f6e04a2c8f47ca9f9499"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/1.2.0"}, "time": "2014-01-28T14:31:57+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "94eed8fb141f989fe044ae3b6743cc1d87b05a7f"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/94eed8fb141f989fe044ae3b6743cc1d87b05a7f", "type": "zip", "shasum": "", "reference": "94eed8fb141f989fe044ae3b6743cc1d87b05a7f"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/1.1.0"}, "time": "2013-12-15T22:24:08+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PhpDocReader.git", "type": "git", "reference": "46bcd87182a3858d2b4f69d4314cb17144d60501"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/46bcd87182a3858d2b4f69d4314cb17144d60501", "type": "zip", "shasum": "", "reference": "46bcd87182a3858d2b4f69d4314cb17144d60501"}, "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/1.0.0"}, "time": "2013-10-15T16:17:07+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 29 Mar 2024 03:36:26 GMT"}