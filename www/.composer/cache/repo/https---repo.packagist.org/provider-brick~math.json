{"minified": "composer/2.0", "packages": {"brick/math": [{"name": "brick/math", "description": "Arbitrary-precision arithmetic library", "keywords": ["decimal", "math", "BigInteger", "bignum", "arithmetic", "mathematics", "brick", "integer", "bigdecimal", "Arbitrary-precision", "bignumber", "BigRational", "rational"], "homepage": "", "version": "0.13.1", "version_normalized": "********", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/fc7ed316430118cc7836bf45faff18d5dfc8de04", "type": "zip", "shasum": "", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04"}, "type": "library", "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.13.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-03-29T13:50:30+00:00", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "require": {"php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^10.1", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "6.8.8"}}, {"version": "0.13.0", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "efe64d2cd94d410ba85831a6d4c16ac48542751b"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/efe64d2cd94d410ba85831a6d4c16ac48542751b", "type": "zip", "shasum": "", "reference": "efe64d2cd94d410ba85831a6d4c16ac48542751b"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.13.0"}, "time": "2025-03-03T13:19:29+00:00"}, {"version": "0.12.3", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba", "type": "zip", "shasum": "", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "time": "2025-02-28T13:11:00+00:00"}, {"version": "0.12.2", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "901eddb1e45a8e0f689302e40af871c181ecbe40"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/901eddb1e45a8e0f689302e40af871c181ecbe40", "type": "zip", "shasum": "", "reference": "901eddb1e45a8e0f689302e40af871c181ecbe40"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.2"}, "time": "2025-02-26T10:21:45+00:00"}, {"version": "0.12.1", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "f510c0a40911935b77b86859eb5223d58d660df1"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/f510c0a40911935b77b86859eb5223d58d660df1", "type": "zip", "shasum": "", "reference": "f510c0a40911935b77b86859eb5223d58d660df1"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.1"}, "time": "2023-11-29T23:19:16+00:00", "require-dev": {"phpunit/phpunit": "^10.1", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "5.16.0"}}, {"version": "0.12.0", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "21e0a1e6bc7aa5b640ce8d82b2ff5f251c41a952"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/21e0a1e6bc7aa5b640ce8d82b2ff5f251c41a952", "type": "zip", "shasum": "", "reference": "21e0a1e6bc7aa5b640ce8d82b2ff5f251c41a952"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.0"}, "time": "2023-11-26T14:36:23+00:00"}, {"keywords": ["math", "BigInteger", "bignum", "arithmetic", "brick", "bigdecimal", "Arbitrary-precision", "BigRational"], "version": "0.11.0", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "type": "zip", "shasum": "", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "time": "2023-01-15T23:15:59+00:00", "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "5.0.0"}}, {"version": "0.10.2", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/459f2781e1a08d52ee56b0b1444086e038561e3f", "type": "zip", "shasum": "", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.10.2"}, "time": "2022-08-10T22:54:19+00:00", "require": {"php": "^7.4 || ^8.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "4.25.0"}}, {"version": "0.10.1", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "de846578401f4e58f911b3afeb62ced56365ed87"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/de846578401f4e58f911b3afeb62ced56365ed87", "type": "zip", "shasum": "", "reference": "de846578401f4e58f911b3afeb62ced56365ed87"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.10.1"}, "time": "2022-08-01T22:54:31+00:00"}, {"version": "0.10.0", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "4da12356b27546867630ce14cc6b961827bff64e"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/4da12356b27546867630ce14cc6b961827bff64e", "type": "zip", "shasum": "", "reference": "4da12356b27546867630ce14cc6b961827bff64e"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.10.0"}, "time": "2022-06-18T12:52:18+00:00", "require-dev": {"phpunit/phpunit": "^9.0", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "4.23.0"}}, {"version": "0.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/ca57d18f028f84f777b2168cd1911b0dee2343ae", "type": "zip", "shasum": "", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.9.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/brick/math", "type": "tidelift"}], "time": "2021-08-15T20:50:18+00:00", "require": {"php": "^7.1 || ^8.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.0", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "4.9.2"}}, {"version": "0.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "dff976c2f3487d42c1db75a3b180e2b9f0e72ce0"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/dff976c2f3487d42c1db75a3b180e2b9f0e72ce0", "type": "zip", "shasum": "", "reference": "dff976c2f3487d42c1db75a3b180e2b9f0e72ce0"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.9.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/brick/math", "type": "tidelift"}], "time": "2021-01-20T22:51:39+00:00", "require-dev": {"phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.0", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "4.3.2"}}, {"version": "0.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "283a40c901101e66de7061bd359252c013dcc43c"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/283a40c901101e66de7061bd359252c013dcc43c", "type": "zip", "shasum": "", "reference": "283a40c901101e66de7061bd359252c013dcc43c"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/master"}, "time": "2020-08-18T23:57:15+00:00", "require": {"php": "^7.1|^8.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^7.5.15|^8.5", "php-coveralls/php-coveralls": "^2.2", "vimeo/psalm": "^3.5"}}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "b69d0daabbb830b1dac93d757cdc5dab253574c2"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/b69d0daabbb830b1dac93d757cdc5dab253574c2", "type": "zip", "shasum": "", "reference": "b69d0daabbb830b1dac93d757cdc5dab253574c2"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.9.0"}, "time": "2020-08-18T09:40:56+00:00"}, {"version": "0.8.17", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "e6f8e7d04346a95be89580f8c2c22d6c3fa65556"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/e6f8e7d04346a95be89580f8c2c22d6c3fa65556", "type": "zip", "shasum": "", "reference": "e6f8e7d04346a95be89580f8c2c22d6c3fa65556"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/v0.8"}, "time": "2020-08-18T23:41:20+00:00"}, {"version": "0.8.16", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "edffaf98c06459b11dc1843e499bfea2aaf78754"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/edffaf98c06459b11dc1843e499bfea2aaf78754", "type": "zip", "shasum": "", "reference": "edffaf98c06459b11dc1843e499bfea2aaf78754"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.8.16"}, "time": "2020-08-18T08:59:30+00:00"}, {"version": "0.8.15", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "9b08d412b9da9455b210459ff71414de7e6241cd"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/9b08d412b9da9455b210459ff71414de7e6241cd", "type": "zip", "shasum": "", "reference": "9b08d412b9da9455b210459ff71414de7e6241cd"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.8.15"}, "time": "2020-04-15T15:59:35+00:00"}, {"version": "0.8.14", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "6f7a46b5c3d487b277f38fbd17df57d348cace8a"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/6f7a46b5c3d487b277f38fbd17df57d348cace8a", "type": "zip", "shasum": "", "reference": "6f7a46b5c3d487b277f38fbd17df57d348cace8a"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.8.14"}, "funding": [], "time": "2020-02-17T13:57:43+00:00", "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "7.*", "php-coveralls/php-coveralls": "2.*", "vimeo/psalm": "3.*"}}, {"version": "0.8.13", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "d6c1421d82992198835613b0d099050b66f2edc7"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/d6c1421d82992198835613b0d099050b66f2edc7", "type": "zip", "shasum": "", "reference": "d6c1421d82992198835613b0d099050b66f2edc7"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/master"}, "time": "2020-02-16T17:16:11+00:00"}, {"version": "0.8.12", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "ea9a095b902f5d42126aba4455a64476f5528241"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/ea9a095b902f5d42126aba4455a64476f5528241", "type": "zip", "shasum": "", "reference": "ea9a095b902f5d42126aba4455a64476f5528241"}, "time": "2020-01-27T13:00:05+00:00", "funding": "__unset"}, {"version": "0.8.11", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "bf4894d6373d396666d280a42ecdf1ad75f8222f"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/bf4894d6373d396666d280a42ecdf1ad75f8222f", "type": "zip", "shasum": "", "reference": "bf4894d6373d396666d280a42ecdf1ad75f8222f"}, "time": "2020-01-23T00:19:01+00:00", "require-dev": {"phpunit/phpunit": "7.*", "php-coveralls/php-coveralls": "2.*"}}, {"version": "0.8.10", "version_normalized": "********", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "73f3ca76d700a46ab8df45899afa8c610b0d47da"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/73f3ca76d700a46ab8df45899afa8c610b0d47da", "type": "zip", "shasum": "", "reference": "73f3ca76d700a46ab8df45899afa8c610b0d47da"}, "time": "2020-01-21T21:06:40+00:00"}, {"version": "0.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "2667c1659c1dcc681b4207f722180024d12a3626"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/2667c1659c1dcc681b4207f722180024d12a3626", "type": "zip", "shasum": "", "reference": "2667c1659c1dcc681b4207f722180024d12a3626"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.8.9"}, "time": "2020-01-08T09:40:28+00:00"}, {"keywords": ["math", "BigInteger", "arithmetic", "brick", "bigdecimal", "Arbitrary-precision", "BigRational"], "version": "0.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "b77bf303449ac66e46d521e5022cedd1663ab6bd"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/b77bf303449ac66e46d521e5022cedd1663ab6bd", "type": "zip", "shasum": "", "reference": "b77bf303449ac66e46d521e5022cedd1663ab6bd"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/master"}, "time": "2019-04-25T14:55:49+00:00"}, {"version": "0.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "0f8728f266ae4b2c09945a9ca40fceb28a1a0f61"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/0f8728f266ae4b2c09945a9ca40fceb28a1a0f61", "type": "zip", "shasum": "", "reference": "0f8728f266ae4b2c09945a9ca40fceb28a1a0f61"}, "time": "2019-04-19T23:49:09+00:00"}, {"version": "0.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "44d14bb2a1976b6a165d00eb08823eaa6e8f366e"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/44d14bb2a1976b6a165d00eb08823eaa6e8f366e", "type": "zip", "shasum": "", "reference": "44d14bb2a1976b6a165d00eb08823eaa6e8f366e"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.8.6"}, "time": "2019-04-11T12:03:33+00:00"}, {"version": "0.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "510f506185d4c988f97180426ec80fb3dbf51e04"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/510f506185d4c988f97180426ec80fb3dbf51e04", "type": "zip", "shasum": "", "reference": "510f506185d4c988f97180426ec80fb3dbf51e04"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/master"}, "time": "2019-02-12T10:40:15+00:00"}, {"version": "0.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "41eeaed92906856968a3d4cc1020a70647c9e97f"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/41eeaed92906856968a3d4cc1020a70647c9e97f", "type": "zip", "shasum": "", "reference": "41eeaed92906856968a3d4cc1020a70647c9e97f"}, "time": "2018-12-07T14:38:55+00:00"}, {"version": "0.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "f5b7885432e878456cb04f957d6e170edb05b288"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/f5b7885432e878456cb04f957d6e170edb05b288", "type": "zip", "shasum": "", "reference": "f5b7885432e878456cb04f957d6e170edb05b288"}, "time": "2018-12-06T12:18:45+00:00"}, {"version": "0.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "e79436250990d168fe7f53d6654827e201f9f35f"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/e79436250990d168fe7f53d6654827e201f9f35f", "type": "zip", "shasum": "", "reference": "e79436250990d168fe7f53d6654827e201f9f35f"}, "time": "2018-11-07T23:16:33+00:00"}, {"version": "0.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "96936b264ca51f2249a2df6642eb99b76ae03df5"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/96936b264ca51f2249a2df6642eb99b76ae03df5", "type": "zip", "shasum": "", "reference": "96936b264ca51f2249a2df6642eb99b76ae03df5"}, "time": "2018-11-07T21:54:33+00:00"}, {"version": "0.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "3a0ac33048bcec1d826def08ae2c7a23e0e49ca1"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/3a0ac33048bcec1d826def08ae2c7a23e0e49ca1", "type": "zip", "shasum": "", "reference": "3a0ac33048bcec1d826def08ae2c7a23e0e49ca1"}, "time": "2018-10-13T16:54:09+00:00"}, {"version": "0.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "e599ac0d639d8c77baab78a893d5287e65703e4d"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/e599ac0d639d8c77baab78a893d5287e65703e4d", "type": "zip", "shasum": "", "reference": "e599ac0d639d8c77baab78a893d5287e65703e4d"}, "time": "2018-08-20T17:13:41+00:00"}, {"version": "0.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "726a8f271b440d6788a81edf486dd756fb23b6fc"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/726a8f271b440d6788a81edf486dd756fb23b6fc", "type": "zip", "shasum": "", "reference": "726a8f271b440d6788a81edf486dd756fb23b6fc"}, "time": "2018-07-21T23:35:12+00:00"}, {"version": "0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "4f39b97e367bf607f00bc61de9aa70c74b916da3"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/4f39b97e367bf607f00bc61de9aa70c74b916da3", "type": "zip", "shasum": "", "reference": "4f39b97e367bf607f00bc61de9aa70c74b916da3"}, "time": "2018-02-27T13:51:17+00:00", "require-dev": {"phpunit/phpunit": "7.*", "satooshi/php-coveralls": "2.*"}}, {"version": "0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "b55f41cb55dced8955063e1f0f0b923a0d7da529"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/b55f41cb55dced8955063e1f0f0b923a0d7da529", "type": "zip", "shasum": "", "reference": "b55f41cb55dced8955063e1f0f0b923a0d7da529"}, "time": "2017-10-02T17:56:40+00:00", "autoload": {"psr-4": {"Brick\\Math\\": "src/", "Brick\\Math\\Tests\\": "tests/"}}, "require-dev": {"phpunit/phpunit": "6.*", "satooshi/php-coveralls": "1.*"}}, {"version": "0.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "9338fdd6ec14716ac02b2b37ca485fbddc501ea6"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/9338fdd6ec14716ac02b2b37ca485fbddc501ea6", "type": "zip", "shasum": "", "reference": "9338fdd6ec14716ac02b2b37ca485fbddc501ea6"}, "time": "2017-10-02T17:47:56+00:00"}, {"version": "0.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "55431c7b8acbe1c10f62fa0f306fa2a473f5818a"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/55431c7b8acbe1c10f62fa0f306fa2a473f5818a", "type": "zip", "shasum": "", "reference": "55431c7b8acbe1c10f62fa0f306fa2a473f5818a"}, "time": "2017-10-02T17:32:42+00:00"}, {"version": "0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "4da1620896fe329053c0ae556a3ab4629e4b0f47"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/4da1620896fe329053c0ae556a3ab4629e4b0f47", "type": "zip", "shasum": "", "reference": "4da1620896fe329053c0ae556a3ab4629e4b0f47"}, "time": "2017-08-25T10:17:31+00:00"}, {"version": "0.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "b44a78b01cb79aa4bf17de46a45db968b6958bda"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/b44a78b01cb79aa4bf17de46a45db968b6958bda", "type": "zip", "shasum": "", "reference": "b44a78b01cb79aa4bf17de46a45db968b6958bda"}, "time": "2016-10-17T22:08:04+00:00", "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "dev-master"}}, {"version": "0.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "859ea904393a7feb60ccf8bb38a860b8bbbcdcd9"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/859ea904393a7feb60ccf8bb38a860b8bbbcdcd9", "type": "zip", "shasum": "", "reference": "859ea904393a7feb60ccf8bb38a860b8bbbcdcd9"}, "time": "2016-03-31T16:54:32+00:00"}, {"keywords": ["math", "BigInteger", "arithmetic", "brick", "bigdecimal", "Arbitrary-precision"], "version": "0.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "08c6979c62c56e942b0153ee2ed857f9a256b19f"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/08c6979c62c56e942b0153ee2ed857f9a256b19f", "type": "zip", "shasum": "", "reference": "08c6979c62c56e942b0153ee2ed857f9a256b19f"}, "time": "2015-08-06T11:04:27+00:00"}, {"version": "0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "70f66fc7c918a5e2114cad0d7df8bde8235d8202"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/70f66fc7c918a5e2114cad0d7df8bde8235d8202", "type": "zip", "shasum": "", "reference": "70f66fc7c918a5e2114cad0d7df8bde8235d8202"}, "time": "2015-07-05T11:13:10+00:00"}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "786810b8918a1855ea47f521413fb3afbf626137"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/786810b8918a1855ea47f521413fb3afbf626137", "type": "zip", "shasum": "", "reference": "786810b8918a1855ea47f521413fb3afbf626137"}, "time": "2015-07-04T20:40:13+00:00"}, {"version": "0.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "d87853119aa6f11e3251f3eacd541f517af2c2b9"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/d87853119aa6f11e3251f3eacd541f517af2c2b9", "type": "zip", "shasum": "", "reference": "d87853119aa6f11e3251f3eacd541f517af2c2b9"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/v0.4"}, "time": "2016-03-31T17:24:40+00:00"}, {"version": "0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "ac691e01e56db625f838d3d548f3c28612614601"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/ac691e01e56db625f838d3d548f3c28612614601", "type": "zip", "shasum": "", "reference": "ac691e01e56db625f838d3d548f3c28612614601"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/master"}, "time": "2015-06-16T22:40:31+00:00"}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "0a8b269952cc787c694956846a45c1fd0d78464b"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/0a8b269952cc787c694956846a45c1fd0d78464b", "type": "zip", "shasum": "", "reference": "0a8b269952cc787c694956846a45c1fd0d78464b"}, "time": "2015-06-12T22:14:37+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "5533b2aa36ae6691f5b81c1e1ec357d5c8b35d9c"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/5533b2aa36ae6691f5b81c1e1ec357d5c8b35d9c", "type": "zip", "shasum": "", "reference": "5533b2aa36ae6691f5b81c1e1ec357d5c8b35d9c"}, "time": "2015-06-12T10:25:09+00:00"}, {"version": "0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "69706f8384583dbff0f617b52a9e927378628928"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/69706f8384583dbff0f617b52a9e927378628928", "type": "zip", "shasum": "", "reference": "69706f8384583dbff0f617b52a9e927378628928"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/v0.3"}, "time": "2016-03-31T17:35:41+00:00"}, {"version": "0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "4f4b9531edba0e8a033ff23e0bf54b0e129bc3fa"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/4f4b9531edba0e8a033ff23e0bf54b0e129bc3fa", "type": "zip", "shasum": "", "reference": "4f4b9531edba0e8a033ff23e0bf54b0e129bc3fa"}, "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/master"}, "time": "2015-06-11T21:35:47+00:00"}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "2c35d6fc5bd29aa3af06b16e5f7793c056657c5f"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/2c35d6fc5bd29aa3af06b16e5f7793c056657c5f", "type": "zip", "shasum": "", "reference": "2c35d6fc5bd29aa3af06b16e5f7793c056657c5f"}, "time": "2015-06-07T22:12:50+00:00"}, {"version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "e9da17c78dce4fdb35b705c0b42b74dd30363b9d"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/e9da17c78dce4fdb35b705c0b42b74dd30363b9d", "type": "zip", "shasum": "", "reference": "e9da17c78dce4fdb35b705c0b42b74dd30363b9d"}, "time": "2015-06-07T12:48:49+00:00"}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "2aabbe1a53e63670a5af5b88b9de1ba483427d34"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/2aabbe1a53e63670a5af5b88b9de1ba483427d34", "type": "zip", "shasum": "", "reference": "2aabbe1a53e63670a5af5b88b9de1ba483427d34"}, "time": "2015-06-05T20:19:20+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "c642d0d0c9e671a1d417d3c0f3b5bc7433b8ad40"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/c642d0d0c9e671a1d417d3c0f3b5bc7433b8ad40", "type": "zip", "shasum": "", "reference": "c642d0d0c9e671a1d417d3c0f3b5bc7433b8ad40"}, "time": "2015-06-04T19:45:13+00:00"}, {"version": "0.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "4f3095cca11592d82874ae3dddb69cd798d64495"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/4f3095cca11592d82874ae3dddb69cd798d64495", "type": "zip", "shasum": "", "reference": "4f3095cca11592d82874ae3dddb69cd798d64495"}, "time": "2015-06-04T10:18:26+00:00"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "53a61b46a714f04aa648f0c6fa67899bc51b5888"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/53a61b46a714f04aa648f0c6fa67899bc51b5888", "type": "zip", "shasum": "", "reference": "53a61b46a714f04aa648f0c6fa67899bc51b5888"}, "time": "2015-06-02T20:08:56+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "49ea5a085e7132cf665cb724e30998400cbb68d0"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/49ea5a085e7132cf665cb724e30998400cbb68d0", "type": "zip", "shasum": "", "reference": "49ea5a085e7132cf665cb724e30998400cbb68d0"}, "time": "2015-05-22T11:39:59+00:00"}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "61968fcee920dfbc45963ac5a45711a9d16303f5"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/61968fcee920dfbc45963ac5a45711a9d16303f5", "type": "zip", "shasum": "", "reference": "61968fcee920dfbc45963ac5a45711a9d16303f5"}, "time": "2014-09-01T17:10:04+00:00", "require": {"php": ">=5.5"}}, {"description": "Arbitrary-precision arithmetic", "version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/brick/math.git", "type": "git", "reference": "016b1388f5d873919528c621bc779b3f62a396d7"}, "dist": {"url": "https://api.github.com/repos/brick/math/zipball/016b1388f5d873919528c621bc779b3f62a396d7", "type": "zip", "shasum": "", "reference": "016b1388f5d873919528c621bc779b3f62a396d7"}, "time": "2014-08-31T14:50:06+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 29 Mar 2025 13:51:31 GMT"}