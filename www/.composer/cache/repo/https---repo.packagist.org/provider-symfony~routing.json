{"minified": "composer/2.0", "packages": {"symfony/routing": [{"name": "symfony/routing", "description": "Maps an HTTP request to a set of configuration variables", "keywords": ["routing", "url", "router", "uri"], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8e213820c5fea844ecea29203d2a308019007c15"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8e213820c5fea844ecea29203d2a308019007c15", "type": "zip", "shasum": "", "reference": "8e213820c5fea844ecea29203d2a308019007c15"}, "type": "library", "support": {"source": "https://github.com/symfony/routing/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-24T20:43:28+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5d4973c3f6078c3b7de999bbe4b7b71b1bacad7b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5d4973c3f6078c3b7de999bbe4b7b71b1bacad7b", "type": "zip", "shasum": "", "reference": "5d4973c3f6078c3b7de999bbe4b7b71b1bacad7b"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.3.0-BETA1"}, "time": "2025-03-24T08:06:17+00:00"}, {"version": "v7.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ee9a67edc6baa33e5fae662f94f91fd262930996", "type": "zip", "shasum": "", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.2.3"}, "time": "2025-01-17T10:56:55+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e10a2450fa957af6c448b9b93c9010a4e4c0725e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e10a2450fa957af6c448b9b93c9010a4e4c0725e", "type": "zip", "shasum": "", "reference": "e10a2450fa957af6c448b9b93c9010a4e4c0725e"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.2.0"}, "time": "2024-11-25T11:08:51+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0782e32f411cf1c4a1ab3f5c074a9b61f3df8e5a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0782e32f411cf1c4a1ab3f5c074a9b61f3df8e5a", "type": "zip", "shasum": "", "reference": "0782e32f411cf1c4a1ab3f5c074a9b61f3df8e5a"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.2.0-RC1"}, "time": "2024-11-06T08:41:34+00:00"}, {"version": "v7.2.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/routing/tree/v7.2.0-BETA2"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b9f47730638e96d6ff26f84bd4a7e89073d15634"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b9f47730638e96d6ff26f84bd4a7e89073d15634", "type": "zip", "shasum": "", "reference": "b9f47730638e96d6ff26f84bd4a7e89073d15634"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.2.0-BETA1"}, "time": "2024-10-03T12:20:01+00:00"}, {"version": "v7.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "07f6463a8ff4377944222b69b126bd5495e9d44e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/07f6463a8ff4377944222b69b126bd5495e9d44e", "type": "zip", "shasum": "", "reference": "07f6463a8ff4377944222b69b126bd5495e9d44e"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.11"}, "time": "2025-01-17T10:33:21+00:00"}, {"version": "v7.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a27bb8e0cc3ca4baf17159d053910c9736c3aa4c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a27bb8e0cc3ca4baf17159d053910c9736c3aa4c", "type": "zip", "shasum": "", "reference": "a27bb8e0cc3ca4baf17159d053910c9736c3aa4c"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.9"}, "time": "2024-11-13T16:12:35+00:00"}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "66a2c469f6c22d08603235c46a20007c0701ea0a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/66a2c469f6c22d08603235c46a20007c0701ea0a", "type": "zip", "shasum": "", "reference": "66a2c469f6c22d08603235c46a20007c0701ea0a"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.6"}, "time": "2024-10-01T08:31:23+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1500aee0094a3ce1c92626ed8cf3c2037e86f5a7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1500aee0094a3ce1c92626ed8cf3c2037e86f5a7", "type": "zip", "shasum": "", "reference": "1500aee0094a3ce1c92626ed8cf3c2037e86f5a7"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.4"}, "time": "2024-08-29T08:16:25+00:00"}, {"version": "v7.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8a908a3f22d5a1b5d297578c2ceb41b02fa916d0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8a908a3f22d5a1b5d297578c2ceb41b02fa916d0", "type": "zip", "shasum": "", "reference": "8a908a3f22d5a1b5d297578c2ceb41b02fa916d0"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.3"}, "time": "2024-07-17T06:10:24+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "60c31bab5c45af7f13091b87deb708830f3c96c0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/60c31bab5c45af7f13091b87deb708830f3c96c0", "type": "zip", "shasum": "", "reference": "60c31bab5c45af7f13091b87deb708830f3c96c0"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0ec2f36fbd769467f98c9c02cea1b76ed117115d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0ec2f36fbd769467f98c9c02cea1b76ed117115d", "type": "zip", "shasum": "", "reference": "0ec2f36fbd769467f98c9c02cea1b76ed117115d"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.0"}, "time": "2024-05-28T06:54:05+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ec6e34d7e972c21d1e3aa5531dfdf23979c024b6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ec6e34d7e972c21d1e3aa5531dfdf23979c024b6", "type": "zip", "shasum": "", "reference": "ec6e34d7e972c21d1e3aa5531dfdf23979c024b6"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.0-RC1"}, "time": "2024-05-17T10:55:18+00:00"}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "91479c48d276d907cc68614b46409fa92ec4a1c8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/91479c48d276d907cc68614b46409fa92ec4a1c8", "type": "zip", "shasum": "", "reference": "91479c48d276d907cc68614b46409fa92ec4a1c8"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.1.0-BETA1"}, "time": "2024-05-02T09:03:15+00:00"}, {"version": "v7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ed9fe56db2eb080b4fc1ecea9d66277ef6d1fb8a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ed9fe56db2eb080b4fc1ecea9d66277ef6d1fb8a", "type": "zip", "shasum": "", "reference": "ed9fe56db2eb080b4fc1ecea9d66277ef6d1fb8a"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.10"}, "time": "2024-07-17T06:06:58+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ceb71bb86a8c4e65eb3b9dd33819df390c11ec67"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ceb71bb86a8c4e65eb3b9dd33819df390c11ec67", "type": "zip", "shasum": "", "reference": "ceb71bb86a8c4e65eb3b9dd33819df390c11ec67"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9f82bf7766ccc9c22ab7aeb9bebb98351483fa5b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9f82bf7766ccc9c22ab7aeb9bebb98351483fa5b", "type": "zip", "shasum": "", "reference": "9f82bf7766ccc9c22ab7aeb9bebb98351483fa5b"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cded64e5bbf9f31786f1055fcc76718fdd77519c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cded64e5bbf9f31786f1055fcc76718fdd77519c", "type": "zip", "shasum": "", "reference": "cded64e5bbf9f31786f1055fcc76718fdd77519c"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.6"}, "time": "2024-03-28T21:02:11+00:00"}, {"version": "v7.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ba6bf07d43289c6a4b4591ddb75bc3bc5f069c19"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ba6bf07d43289c6a4b4591ddb75bc3bc5f069c19", "type": "zip", "shasum": "", "reference": "ba6bf07d43289c6a4b4591ddb75bc3bc5f069c19"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.5"}, "time": "2024-02-27T12:34:35+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "858b26756ffc35a11238b269b484ee3a393a74d3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/858b26756ffc35a11238b269b484ee3a393a74d3", "type": "zip", "shasum": "", "reference": "858b26756ffc35a11238b269b484ee3a393a74d3"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.3"}, "time": "2024-01-30T13:55:15+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "78866be67255f42716271e33d1d8b64eb6e47bd9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/78866be67255f42716271e33d1d8b64eb6e47bd9", "type": "zip", "shasum": "", "reference": "78866be67255f42716271e33d1d8b64eb6e47bd9"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.2"}, "time": "2023-12-29T15:37:40+00:00"}, {"version": "v7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fc55062907669835af6408558ae4d1dafef74b1e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fc55062907669835af6408558ae4d1dafef74b1e", "type": "zip", "shasum": "", "reference": "fc55062907669835af6408558ae4d1dafef74b1e"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.1"}, "time": "2023-12-01T15:10:06+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7426e03beb76f3637cc5a68303972666b9a91170"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7426e03beb76f3637cc5a68303972666b9a91170", "type": "zip", "shasum": "", "reference": "7426e03beb76f3637cc5a68303972666b9a91170"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.0"}, "time": "2023-11-29T08:40:23+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "88a97f5b78a14b061a10a96214b8418fd7915c1c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/88a97f5b78a14b061a10a96214b8418fd7915c1c", "type": "zip", "shasum": "", "reference": "88a97f5b78a14b061a10a96214b8418fd7915c1c"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.0-RC1"}, "time": "2023-11-15T15:44:10+00:00"}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "16f1379106f6dd9e6e5f75b51cab876fce1294c7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/16f1379106f6dd9e6e5f75b51cab876fce1294c7", "type": "zip", "shasum": "", "reference": "16f1379106f6dd9e6e5f75b51cab876fce1294c7"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.0-BETA3"}, "time": "2023-10-30T14:12:42+00:00"}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ed3e3dff27f33d904782df63351a0df3be86a138"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ed3e3dff27f33d904782df63351a0df3be86a138", "type": "zip", "shasum": "", "reference": "ed3e3dff27f33d904782df63351a0df3be86a138"}, "support": {"source": "https://github.com/symfony/routing/tree/v7.0.0-BETA1"}, "time": "2023-10-20T16:35:23+00:00"}, {"version": "v6.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1f5234e8457164a3a0038a4c0a4ba27876a9c670", "type": "zip", "shasum": "", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.22"}, "time": "2025-04-27T16:08:38+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/config": "^6.2|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}}, {"version": "v6.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e9bfc94953019089acdfb9be51c1b9142c4afa68"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e9bfc94953019089acdfb9be51c1b9142c4afa68", "type": "zip", "shasum": "", "reference": "e9bfc94953019089acdfb9be51c1b9142c4afa68"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.18"}, "time": "2025-01-09T08:51:02+00:00"}, {"version": "v6.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "91e02e606b4b705c2f4fb42f7e7708b7923a3220"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/91e02e606b4b705c2f4fb42f7e7708b7923a3220", "type": "zip", "shasum": "", "reference": "91e02e606b4b705c2f4fb42f7e7708b7923a3220"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.16"}, "time": "2024-11-13T15:31:34+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "640a74250d13f9c30d5ca045b6aaaabcc8215278"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/640a74250d13f9c30d5ca045b6aaaabcc8215278", "type": "zip", "shasum": "", "reference": "640a74250d13f9c30d5ca045b6aaaabcc8215278"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.13"}, "time": "2024-10-01T08:30:56+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a7c8036bd159486228dc9be3e846a00a0dda9f9f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a7c8036bd159486228dc9be3e846a00a0dda9f9f", "type": "zip", "shasum": "", "reference": "a7c8036bd159486228dc9be3e846a00a0dda9f9f"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.12"}, "time": "2024-09-20T08:32:26+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8ee0c24c1bf61c263a26f1b9b6d19e83b1121f2a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8ee0c24c1bf61c263a26f1b9b6d19e83b1121f2a", "type": "zip", "shasum": "", "reference": "8ee0c24c1bf61c263a26f1b9b6d19e83b1121f2a"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.11"}, "time": "2024-08-29T08:15:38+00:00"}, {"version": "v6.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "aad19fe10753ba842f0d653a8db819c4b3affa87"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/aad19fe10753ba842f0d653a8db819c4b3affa87", "type": "zip", "shasum": "", "reference": "aad19fe10753ba842f0d653a8db819c4b3affa87"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.10"}, "time": "2024-07-15T09:26:24+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8a40d0f9b01f0fbb80885d3ce0ad6714fb603a58"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8a40d0f9b01f0fbb80885d3ce0ad6714fb603a58", "type": "zip", "shasum": "", "reference": "8a40d0f9b01f0fbb80885d3ce0ad6714fb603a58"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "276e06398f71fa2a973264d94f28150f93cfb907"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/276e06398f71fa2a973264d94f28150f93cfb907", "type": "zip", "shasum": "", "reference": "276e06398f71fa2a973264d94f28150f93cfb907"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f2591fd1f8c6e3734656b5d6b3829e8bf81f507c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f2591fd1f8c6e3734656b5d6b3829e8bf81f507c", "type": "zip", "shasum": "", "reference": "f2591fd1f8c6e3734656b5d6b3829e8bf81f507c"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.6"}, "time": "2024-03-28T13:28:49+00:00"}, {"version": "v6.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7fe30068e207d9c31c0138501ab40358eb2d49a4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7fe30068e207d9c31c0138501ab40358eb2d49a4", "type": "zip", "shasum": "", "reference": "7fe30068e207d9c31c0138501ab40358eb2d49a4"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.5"}, "time": "2024-02-27T12:33:30+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3b2957ad54902f0f544df83e3d58b38d7e8e5842"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3b2957ad54902f0f544df83e3d58b38d7e8e5842", "type": "zip", "shasum": "", "reference": "3b2957ad54902f0f544df83e3d58b38d7e8e5842"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.3"}, "time": "2024-01-30T13:55:02+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "98eab13a07fddc85766f1756129c69f207ffbc21"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/98eab13a07fddc85766f1756129c69f207ffbc21", "type": "zip", "shasum": "", "reference": "98eab13a07fddc85766f1756129c69f207ffbc21"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.2"}, "time": "2023-12-29T15:34:34+00:00"}, {"version": "v6.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0c95c164fdba18b12523b75e64199ca3503e6d40"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0c95c164fdba18b12523b75e64199ca3503e6d40", "type": "zip", "shasum": "", "reference": "0c95c164fdba18b12523b75e64199ca3503e6d40"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.1"}, "time": "2023-12-01T14:54:37+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ae014d60d7c8e80be5c3b644a286e91249a3e8f4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ae014d60d7c8e80be5c3b644a286e91249a3e8f4", "type": "zip", "shasum": "", "reference": "ae014d60d7c8e80be5c3b644a286e91249a3e8f4"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.0"}, "time": "2023-11-29T08:04:54+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "18bba718a449b582ba4d118c3d04598a4bd8764e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/18bba718a449b582ba4d118c3d04598a4bd8764e", "type": "zip", "shasum": "", "reference": "18bba718a449b582ba4d118c3d04598a4bd8764e"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.0-RC1"}, "time": "2023-11-12T20:48:31+00:00"}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ba1b0dc73d526787bd64929269927c7f9ae33769"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ba1b0dc73d526787bd64929269927c7f9ae33769", "type": "zip", "shasum": "", "reference": "ba1b0dc73d526787bd64929269927c7f9ae33769"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.0-BETA3"}, "time": "2023-11-06T09:15:24+00:00"}, {"version": "v6.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cda2bc5271b5780194dd3ae6c3ab6a3a71e8744b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cda2bc5271b5780194dd3ae6c3ab6a3a71e8744b", "type": "zip", "shasum": "", "reference": "cda2bc5271b5780194dd3ae6c3ab6a3a71e8744b"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.0-BETA2"}, "time": "2023-10-29T12:45:54+00:00"}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ea594c977fca54a7725865ee37ac509601c44598"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ea594c977fca54a7725865ee37ac509601c44598", "type": "zip", "shasum": "", "reference": "ea594c977fca54a7725865ee37ac509601c44598"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.4.0-BETA1"}, "time": "2023-10-20T12:00:10+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c7a3dcdd44d14022bf0d9d27f14a7b238f7e3e85"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c7a3dcdd44d14022bf0d9d27f14a7b238f7e3e85", "type": "zip", "shasum": "", "reference": "c7a3dcdd44d14022bf0d9d27f14a7b238f7e3e85"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.12"}, "time": "2024-01-30T13:17:59+00:00", "require-dev": {"symfony/config": "^6.2", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3"}}, {"version": "v6.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5f1b4eb8e7b7d8487389bd774fb76f51dba57452"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5f1b4eb8e7b7d8487389bd774fb76f51dba57452", "type": "zip", "shasum": "", "reference": "5f1b4eb8e7b7d8487389bd774fb76f51dba57452"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.11"}, "time": "2023-12-29T15:20:22+00:00"}, {"version": "v6.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cb7404232d49dd11cc971b832fcbd49e7c22b049"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cb7404232d49dd11cc971b832fcbd49e7c22b049", "type": "zip", "shasum": "", "reference": "cb7404232d49dd11cc971b832fcbd49e7c22b049"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.10"}, "time": "2023-12-01T14:25:58+00:00"}, {"version": "v6.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "82616e59acd3e3d9c916bba798326cb7796d7d31"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/82616e59acd3e3d9c916bba798326cb7796d7d31", "type": "zip", "shasum": "", "reference": "82616e59acd3e3d9c916bba798326cb7796d7d31"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.5"}, "time": "2023-09-20T16:05:51+00:00"}, {"version": "v6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e7243039ab663822ff134fbc46099b5fdfa16f6a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e7243039ab663822ff134fbc46099b5fdfa16f6a", "type": "zip", "shasum": "", "reference": "e7243039ab663822ff134fbc46099b5fdfa16f6a"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.3"}, "time": "2023-07-31T07:08:24+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9874c77e1746c7be68ae67e79433cbb202648a8d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9874c77e1746c7be68ae67e79433cbb202648a8d", "type": "zip", "shasum": "", "reference": "9874c77e1746c7be68ae67e79433cbb202648a8d"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.2"}, "time": "2023-07-24T13:52:02+00:00", "require": {"php": ">=8.1"}}, {"version": "v6.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d37ad1779c38b8eb71996d17dc13030dcb7f9cf5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d37ad1779c38b8eb71996d17dc13030dcb7f9cf5", "type": "zip", "shasum": "", "reference": "d37ad1779c38b8eb71996d17dc13030dcb7f9cf5"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.1"}, "time": "2023-06-05T15:30:22+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "827f59fdc67eecfc4dfff81f9c93bf4d98f0c89b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/827f59fdc67eecfc4dfff81f9c93bf4d98f0c89b", "type": "zip", "shasum": "", "reference": "827f59fdc67eecfc4dfff81f9c93bf4d98f0c89b"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.3.0"}, "time": "2023-04-28T15:57:00+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/routing/tree/v6.3.0-BETA1"}}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1e54cc8e769d9aba461f0848bcbd17c81696bec9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1e54cc8e769d9aba461f0848bcbd17c81696bec9", "type": "zip", "shasum": "", "reference": "1e54cc8e769d9aba461f0848bcbd17c81696bec9"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.13"}, "time": "2023-07-24T13:51:53+00:00", "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}}, {"version": "v6.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d59dc6a43191985bc9c27c746ce7986f01540e94"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d59dc6a43191985bc9c27c746ce7986f01540e94", "type": "zip", "shasum": "", "reference": "d59dc6a43191985bc9c27c746ce7986f01540e94"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.12"}, "time": "2023-06-05T15:29:05+00:00"}, {"version": "v6.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "69062e2823f03b82265d73a966999660f0e1e404"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/69062e2823f03b82265d73a966999660f0e1e404", "type": "zip", "shasum": "", "reference": "69062e2823f03b82265d73a966999660f0e1e404"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.8"}, "time": "2023-03-14T15:00:05+00:00"}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fa643fa4c56de161f8bc8c0492a76a60140b50e4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fa643fa4c56de161f8bc8c0492a76a60140b50e4", "type": "zip", "shasum": "", "reference": "fa643fa4c56de161f8bc8c0492a76a60140b50e4"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.7"}, "time": "2023-02-14T08:53:37+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "589bd742d5d03c192c8521911680fe88f61712fe"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/589bd742d5d03c192c8521911680fe88f61712fe", "type": "zip", "shasum": "", "reference": "589bd742d5d03c192c8521911680fe88f61712fe"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.5"}, "time": "2023-01-01T08:38:09+00:00"}, {"version": "v6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "35fec764f3e2c8c08fb340d275c84bc78ca7e0c9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/35fec764f3e2c8c08fb340d275c84bc78ca7e0c9", "type": "zip", "shasum": "", "reference": "35fec764f3e2c8c08fb340d275c84bc78ca7e0c9"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.3"}, "time": "2022-12-20T16:41:15+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "857ac6f4df371470fbdefa2f5967a2618dbf1852"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/857ac6f4df371470fbdefa2f5967a2618dbf1852", "type": "zip", "shasum": "", "reference": "857ac6f4df371470fbdefa2f5967a2618dbf1852"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.0"}, "time": "2022-11-09T13:28:29+00:00", "require-dev": {"symfony/config": "^6.2", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3"}}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/routing/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0cfffe1fb6f8419c75f21332b438ee5f1a142853"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0cfffe1fb6f8419c75f21332b438ee5f1a142853", "type": "zip", "shasum": "", "reference": "0cfffe1fb6f8419c75f21332b438ee5f1a142853"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.0-BETA2"}, "time": "2022-10-26T22:15:37+00:00"}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "340e6133f5abc105858564a129f9ebd92eeb894e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/340e6133f5abc105858564a129f9ebd92eeb894e", "type": "zip", "shasum": "", "reference": "340e6133f5abc105858564a129f9ebd92eeb894e"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.2.0-BETA1"}, "time": "2022-10-23T17:07:54+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "dd8556e52717bd8559fdac8e9090388be1e2eba7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/dd8556e52717bd8559fdac8e9090388be1e2eba7", "type": "zip", "shasum": "", "reference": "dd8556e52717bd8559fdac8e9090388be1e2eba7"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.11"}, "time": "2023-01-01T08:36:55+00:00", "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "96e68e4a1cd81ac63c6302c42e3f9aa8b4defb04"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/96e68e4a1cd81ac63c6302c42e3f9aa8b4defb04", "type": "zip", "shasum": "", "reference": "96e68e4a1cd81ac63c6302c42e3f9aa8b4defb04"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.9"}, "time": "2022-12-20T16:40:36+00:00"}, {"version": "v6.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "95effeb9d6e2cec861cee06bf5bbf82d09aea7f5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/95effeb9d6e2cec861cee06bf5bbf82d09aea7f5", "type": "zip", "shasum": "", "reference": "95effeb9d6e2cec861cee06bf5bbf82d09aea7f5"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.7"}, "time": "2022-10-18T13:12:43+00:00", "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3"}}, {"version": "v6.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f8c1ebb43d0f39e5ecd12a732ba1952a3dd8455c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f8c1ebb43d0f39e5ecd12a732ba1952a3dd8455c", "type": "zip", "shasum": "", "reference": "f8c1ebb43d0f39e5ecd12a732ba1952a3dd8455c"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.5"}, "time": "2022-09-09T09:26:14+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ef9108b3a88045b7546e808fb404ddb073dd35ea"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ef9108b3a88045b7546e808fb404ddb073dd35ea", "type": "zip", "shasum": "", "reference": "ef9108b3a88045b7546e808fb404ddb073dd35ea"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.3"}, "time": "2022-07-20T15:00:40+00:00"}, {"version": "v6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8f068b792e515b25e26855ac8dc7fe800399f3e5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8f068b792e515b25e26855ac8dc7fe800399f3e5", "type": "zip", "shasum": "", "reference": "8f068b792e515b25e26855ac8dc7fe800399f3e5"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.1"}, "time": "2022-06-08T12:21:15+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "03c13cbaefdec9fbb7a62ed18235d487686540dd"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/03c13cbaefdec9fbb7a62ed18235d487686540dd", "type": "zip", "shasum": "", "reference": "03c13cbaefdec9fbb7a62ed18235d487686540dd"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.0"}, "time": "2022-04-27T17:06:58+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/routing/tree/v6.1.0-BETA2"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d7ab2999702baba21bde764a6eb831b53b83d2df"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d7ab2999702baba21bde764a6eb831b53b83d2df", "type": "zip", "shasum": "", "reference": "d7ab2999702baba21bde764a6eb831b53b83d2df"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.1.0-BETA1"}, "time": "2022-04-14T08:23:11+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e56ca9b41c1ec447193474cd86ad7c0b547755ac"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e56ca9b41c1ec447193474cd86ad7c0b547755ac", "type": "zip", "shasum": "", "reference": "e56ca9b41c1ec447193474cd86ad7c0b547755ac"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.19"}, "time": "2023-01-01T08:36:10+00:00", "require": {"php": ">=8.0.2"}, "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "61687a0aa80f6807c52e116ee64072f6ec53780c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/61687a0aa80f6807c52e116ee64072f6ec53780c", "type": "zip", "shasum": "", "reference": "61687a0aa80f6807c52e116ee64072f6ec53780c"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.17"}, "time": "2022-12-20T16:40:04+00:00"}, {"version": "v6.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3b7384fad32c6d0e1919b5bd18a69fbcfc383508"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3b7384fad32c6d0e1919b5bd18a69fbcfc383508", "type": "zip", "shasum": "", "reference": "3b7384fad32c6d0e1919b5bd18a69fbcfc383508"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.15"}, "time": "2022-10-18T13:11:57+00:00", "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3"}}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "434b64f7d3a582ec33fcf69baaf085473e67d639"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/434b64f7d3a582ec33fcf69baaf085473e67d639", "type": "zip", "shasum": "", "reference": "434b64f7d3a582ec33fcf69baaf085473e67d639"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.11"}, "time": "2022-07-20T13:45:53+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "74c40c9fc334acc601a32fcf4274e74fb3bac11e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/74c40c9fc334acc601a32fcf4274e74fb3bac11e", "type": "zip", "shasum": "", "reference": "74c40c9fc334acc601a32fcf4274e74fb3bac11e"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.8"}, "time": "2022-04-22T08:18:02+00:00"}, {"version": "v6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a738b152426ac7fcb94bdab8188264652238bef1"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a738b152426ac7fcb94bdab8188264652238bef1", "type": "zip", "shasum": "", "reference": "a738b152426ac7fcb94bdab8188264652238bef1"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.5"}, "time": "2022-01-31T19:46:53+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b1debdf7a40e6bc7eee0f363ab9dd667fe04f099"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b1debdf7a40e6bc7eee0f363ab9dd667fe04f099", "type": "zip", "shasum": "", "reference": "b1debdf7a40e6bc7eee0f363ab9dd667fe04f099"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.3"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "362bc14e1187deaef12d1ca0e04bd919580e8e49"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/362bc14e1187deaef12d1ca0e04bd919580e8e49", "type": "zip", "shasum": "", "reference": "362bc14e1187deaef12d1ca0e04bd919580e8e49"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8d9fd70b701a9a9c4c52ad99040e96b49d084a06"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8d9fd70b701a9a9c4c52ad99040e96b49d084a06", "type": "zip", "shasum": "", "reference": "8d9fd70b701a9a9c4c52ad99040e96b49d084a06"}, "support": {"source": "https://github.com/symfony/routing/tree/v6.0.0"}, "time": "2021-11-04T18:17:10+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/routing/tree/v6.0.0-BETA1"}}, {"version": "v5.4.48", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "dd08c19879a9b37ff14fd30dcbdf99a4cf045db1"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/dd08c19879a9b37ff14fd30dcbdf99a4cf045db1", "type": "zip", "shasum": "", "reference": "dd08c19879a9b37ff14fd30dcbdf99a4cf045db1"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.48"}, "time": "2024-11-12T18:20:21+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/config": "^5.3|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.3", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "986597b3d1c86ecefe094c0c236a9e9ad22756f2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/986597b3d1c86ecefe094c0c236a9e9ad22756f2", "type": "zip", "shasum": "", "reference": "986597b3d1c86ecefe094c0c236a9e9ad22756f2"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.45"}, "time": "2024-09-30T08:44:06+00:00"}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b6f71780bbdd5e93e1c5638671cf0ba42aa8c6d8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b6f71780bbdd5e93e1c5638671cf0ba42aa8c6d8", "type": "zip", "shasum": "", "reference": "b6f71780bbdd5e93e1c5638671cf0ba42aa8c6d8"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.43"}, "time": "2024-08-27T06:36:52+00:00"}, {"version": "v5.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f8dd6f80c96aeec9b13fc13757842342e05c4878"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f8dd6f80c96aeec9b13fc13757842342e05c4878", "type": "zip", "shasum": "", "reference": "f8dd6f80c96aeec9b13fc13757842342e05c4878"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.42"}, "time": "2024-07-09T20:57:15+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6df1dd8b306649303267a760699cf04cf39b1f7b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6df1dd8b306649303267a760699cf04cf39b1f7b", "type": "zip", "shasum": "", "reference": "6df1dd8b306649303267a760699cf04cf39b1f7b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5485974ef20de1150dd195a81e9da4915d45263f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5485974ef20de1150dd195a81e9da4915d45263f", "type": "zip", "shasum": "", "reference": "5485974ef20de1150dd195a81e9da4915d45263f"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f455f06d4ee7d354d9dcaf7d436532c1f388ee01"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f455f06d4ee7d354d9dcaf7d436532c1f388ee01", "type": "zip", "shasum": "", "reference": "f455f06d4ee7d354d9dcaf7d436532c1f388ee01"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.38"}, "time": "2024-03-18T16:56:51+00:00"}, {"version": "v5.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "48ae43e443693ddb4e574f7c12f0d17ce287694e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/48ae43e443693ddb4e574f7c12f0d17ce287694e", "type": "zip", "shasum": "", "reference": "48ae43e443693ddb4e574f7c12f0d17ce287694e"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.37"}, "time": "2024-02-27T09:52:32+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "86c5a06a61ddaf17efa1403542e3d7146af96203"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/86c5a06a61ddaf17efa1403542e3d7146af96203", "type": "zip", "shasum": "", "reference": "86c5a06a61ddaf17efa1403542e3d7146af96203"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.35"}, "time": "2024-01-30T13:10:15+00:00"}, {"version": "v5.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f1d08ed59d7718845bb70acd7480fa7da8966ec0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f1d08ed59d7718845bb70acd7480fa7da8966ec0", "type": "zip", "shasum": "", "reference": "f1d08ed59d7718845bb70acd7480fa7da8966ec0"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.34"}, "time": "2023-12-27T12:51:02+00:00"}, {"version": "v5.4.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5b5b86670f947db92ab54cdcff585e76064d0b04"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5b5b86670f947db92ab54cdcff585e76064d0b04", "type": "zip", "shasum": "", "reference": "5b5b86670f947db92ab54cdcff585e76064d0b04"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.33"}, "time": "2023-12-01T09:28:00+00:00"}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "853fc7df96befc468692de0a48831b38f04d2cb2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/853fc7df96befc468692de0a48831b38f04d2cb2", "type": "zip", "shasum": "", "reference": "853fc7df96befc468692de0a48831b38f04d2cb2"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.26"}, "time": "2023-07-24T13:28:37+00:00"}, {"version": "v5.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "56bfc1394f7011303eb2e22724f9b422d3f14649"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/56bfc1394f7011303eb2e22724f9b422d3f14649", "type": "zip", "shasum": "", "reference": "56bfc1394f7011303eb2e22724f9b422d3f14649"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.25"}, "time": "2023-06-05T14:18:47+00:00"}, {"version": "v5.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c2ac11eb34947999b7c38fb4c835a57306907e6d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c2ac11eb34947999b7c38fb4c835a57306907e6d", "type": "zip", "shasum": "", "reference": "c2ac11eb34947999b7c38fb4c835a57306907e6d"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.22"}, "time": "2023-03-14T14:59:20+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "2ea0f3049076e8ef96eab203a809d6b2332f0361"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/2ea0f3049076e8ef96eab203a809d6b2332f0361", "type": "zip", "shasum": "", "reference": "2ea0f3049076e8ef96eab203a809d6b2332f0361"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.21"}, "time": "2023-02-16T09:33:00+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "df1b28f37c8e78912213c58ef6ab2f2037bbfdc5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/df1b28f37c8e78912213c58ef6ab2f2037bbfdc5", "type": "zip", "shasum": "", "reference": "df1b28f37c8e78912213c58ef6ab2f2037bbfdc5"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.19"}, "time": "2023-01-01T08:32:19+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4ce2df9a469c19ba45ca6aca04fec1c358a6e791"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4ce2df9a469c19ba45ca6aca04fec1c358a6e791", "type": "zip", "shasum": "", "reference": "4ce2df9a469c19ba45ca6aca04fec1c358a6e791"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.17"}, "time": "2022-12-20T11:10:57+00:00"}, {"version": "v5.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5c9b129efe9abce9470e384bf65d8a7e262eee69"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5c9b129efe9abce9470e384bf65d8a7e262eee69", "type": "zip", "shasum": "", "reference": "5c9b129efe9abce9470e384bf65d8a7e262eee69"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.15"}, "time": "2022-10-13T14:10:41+00:00", "require-dev": {"symfony/config": "^5.3|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3"}}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3e01ccd9b2a3a4167ba2b3c53612762300300226"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3e01ccd9b2a3a4167ba2b3c53612762300300226", "type": "zip", "shasum": "", "reference": "3e01ccd9b2a3a4167ba2b3c53612762300300226"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.11"}, "time": "2022-07-20T13:00:38+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e07817bb6244ea33ef5ad31abc4a9288bef3f2f7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e07817bb6244ea33ef5ad31abc4a9288bef3f2f7", "type": "zip", "shasum": "", "reference": "e07817bb6244ea33ef5ad31abc4a9288bef3f2f7"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.8"}, "time": "2022-04-18T21:45:37+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "44b29c7a94e867ccde1da604792f11a469958981"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/44b29c7a94e867ccde1da604792f11a469958981", "type": "zip", "shasum": "", "reference": "44b29c7a94e867ccde1da604792f11a469958981"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.3"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9eeae93c32ca86746e5d38f3679e9569981038b1"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9eeae93c32ca86746e5d38f3679e9569981038b1", "type": "zip", "shasum": "", "reference": "9eeae93c32ca86746e5d38f3679e9569981038b1"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.0"}, "time": "2021-11-23T10:19:22+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c15fd164525234abeb05bfc296ddc4471029937f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c15fd164525234abeb05bfc296ddc4471029937f", "type": "zip", "shasum": "", "reference": "c15fd164525234abeb05bfc296ddc4471029937f"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.4.0-BETA1"}, "time": "2021-11-04T14:23:59+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "eddd14a04a9f34a9f5fe0b87961aca580d6dfa6c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/eddd14a04a9f34a9f5fe0b87961aca580d6dfa6c", "type": "zip", "shasum": "", "reference": "eddd14a04a9f34a9f5fe0b87961aca580d6dfa6c"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00", "require-dev": {"symfony/config": "^5.3", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3"}}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fcbc2b81d55984f04bb704c2269755fa5aaf5cca"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fcbc2b81d55984f04bb704c2269755fa5aaf5cca", "type": "zip", "shasum": "", "reference": "fcbc2b81d55984f04bb704c2269755fa5aaf5cca"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.11"}, "time": "2021-11-04T16:37:19+00:00"}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "be865017746fe869007d94220ad3f5297951811b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/be865017746fe869007d94220ad3f5297951811b", "type": "zip", "shasum": "", "reference": "be865017746fe869007d94220ad3f5297951811b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.7"}, "time": "2021-08-04T21:42:42+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0a35d2f57d73c46ab6d042ced783b81d09a624c4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0a35d2f57d73c46ab6d042ced783b81d09a624c4", "type": "zip", "shasum": "", "reference": "0a35d2f57d73c46ab6d042ced783b81d09a624c4"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.4"}, "time": "2021-07-23T15:55:36+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "368e81376a8e049c37cb80ae87dbfbf411279199"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/368e81376a8e049c37cb80ae87dbfbf411279199", "type": "zip", "shasum": "", "reference": "368e81376a8e049c37cb80ae87dbfbf411279199"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"symfony/config": "^5.3", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "^1.12", "psr/log": "~1.0"}}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6cbf8e795a2bf7585bb672f68e713c66dd8f99d3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6cbf8e795a2bf7585bb672f68e713c66dd8f99d3", "type": "zip", "shasum": "", "reference": "6cbf8e795a2bf7585bb672f68e713c66dd8f99d3"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.0-RC1"}, "time": "2021-05-16T13:08:56+00:00"}, {"version": "v5.3.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4014fd2fdabf12474509270485c3a249539bf6cc"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4014fd2fdabf12474509270485c3a249539bf6cc", "type": "zip", "shasum": "", "reference": "4014fd2fdabf12474509270485c3a249539bf6cc"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.0-BETA3"}, "time": "2021-05-02T18:54:57+00:00"}, {"version": "v5.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a2f72e1e66a2858aad205387baa4a7150ba8f378"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a2f72e1e66a2858aad205387baa4a7150ba8f378", "type": "zip", "shasum": "", "reference": "a2f72e1e66a2858aad205387baa4a7150ba8f378"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.0-BETA2"}, "time": "2021-05-01T14:20:43+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6cf404cba62f0c0b8a0b2f228e467da37e4b93d4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6cf404cba62f0c0b8a0b2f228e467da37e4b93d4", "type": "zip", "shasum": "", "reference": "6cf404cba62f0c0b8a0b2f228e467da37e4b93d4"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.3.0-BETA1"}, "time": "2021-04-14T15:38:59+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "983a19067308962592755f57d97ca1d1f1edd37b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/983a19067308962592755f57d97ca1d1f1edd37b", "type": "zip", "shasum": "", "reference": "983a19067308962592755f57d97ca1d1f1edd37b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.12"}, "time": "2021-07-23T15:54:19+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/config": "^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<5.0", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8bc1f4ac6a46f63eca345d90443a7e44908142ae"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8bc1f4ac6a46f63eca345d90443a7e44908142ae", "type": "zip", "shasum": "", "reference": "8bc1f4ac6a46f63eca345d90443a7e44908142ae"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.10"}, "time": "2021-05-26T17:40:38+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"symfony/config": "^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "^1.10.4", "psr/log": "~1.0"}}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4a7b2bf5e1221be1902b6853743a9bb317f6925e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4a7b2bf5e1221be1902b6853743a9bb317f6925e", "type": "zip", "shasum": "", "reference": "4a7b2bf5e1221be1902b6853743a9bb317f6925e"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.9"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3f0cab2e95b5e92226f34c2c1aa969d3fc41f48c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3f0cab2e95b5e92226f34c2c1aa969d3fc41f48c", "type": "zip", "shasum": "", "reference": "3f0cab2e95b5e92226f34c2c1aa969d3fc41f48c"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.7"}, "time": "2021-04-11T22:55:21+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "31fba555f178afd04d54fd26953501b2c3f0c6e6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/31fba555f178afd04d54fd26953501b2c3f0c6e6", "type": "zip", "shasum": "", "reference": "31fba555f178afd04d54fd26953501b2c3f0c6e6"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.6"}, "time": "2021-03-14T13:53:33+00:00", "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cafa138128dfd6ab6be1abf6279169957b34f662"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cafa138128dfd6ab6be1abf6279169957b34f662", "type": "zip", "shasum": "", "reference": "cafa138128dfd6ab6be1abf6279169957b34f662"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.4"}, "time": "2021-02-22T15:48:39+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "348b5917e56546c6d96adbf21d7f92c9ef563661"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/348b5917e56546c6d96adbf21d7f92c9ef563661", "type": "zip", "shasum": "", "reference": "348b5917e56546c6d96adbf21d7f92c9ef563661"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.3"}, "time": "2021-01-27T10:15:41+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/routing/tree/v5.2.2"}}, {"description": "Symfony Routing Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "934ac2720dcc878a47a45c986b483a7ee7193620"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/934ac2720dcc878a47a45c986b483a7ee7193620", "type": "zip", "shasum": "", "reference": "934ac2720dcc878a47a45c986b483a7ee7193620"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.1"}, "time": "2020-12-08T17:03:37+00:00", "require-dev": {"symfony/config": "^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "^1.7", "psr/log": "~1.0"}}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "130ac5175ad2fd417978baebd8062e2e6b2bc28b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/130ac5175ad2fd417978baebd8062e2e6b2bc28b", "type": "zip", "shasum": "", "reference": "130ac5175ad2fd417978baebd8062e2e6b2bc28b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.0"}, "time": "2020-11-27T00:39:34+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1e6197621f53ebc807db00892194ca5d816c1f3e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1e6197621f53ebc807db00892194ca5d816c1f3e", "type": "zip", "shasum": "", "reference": "1e6197621f53ebc807db00892194ca5d816c1f3e"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.0-RC2"}, "time": "2020-10-28T21:46:03+00:00"}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/routing/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3bf7d029e39f680dd7087efd6ba186674770ad1b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3bf7d029e39f680dd7087efd6ba186674770ad1b", "type": "zip", "shasum": "", "reference": "3bf7d029e39f680dd7087efd6ba186674770ad1b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:08:07+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "992bf371fa99e2bd964503b463667e12a4535bf9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/992bf371fa99e2bd964503b463667e12a4535bf9", "type": "zip", "shasum": "", "reference": "992bf371fa99e2bd964503b463667e12a4535bf9"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.0-BETA2"}, "time": "2020-10-14T17:08:19+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "72d46a4e3bb862b7df6733fa3132404245fea4de"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/72d46a4e3bb862b7df6733fa3132404245fea4de", "type": "zip", "shasum": "", "reference": "72d46a4e3bb862b7df6733fa3132404245fea4de"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.2.0-BETA1"}, "time": "2020-10-02T13:09:26+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Maps an HTTP request to a set of configuration variables", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e7f71f5da6af8b238f2257670fd6aa4ae6263826"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e7f71f5da6af8b238f2257670fd6aa4ae6263826", "type": "zip", "shasum": "", "reference": "e7f71f5da6af8b238f2257670fd6aa4ae6263826"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "require-dev": {"symfony/config": "^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "^1.10.4", "psr/log": "~1.0"}, "extra": "__unset"}, {"description": "Symfony Routing Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fb0f2aa1199b8d910364462170bfa70b1b4ee37d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fb0f2aa1199b8d910364462170bfa70b1b4ee37d", "type": "zip", "shasum": "", "reference": "fb0f2aa1199b8d910364462170bfa70b1b4ee37d"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.10"}, "time": "2020-12-08T17:02:38+00:00", "require-dev": {"symfony/config": "^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "doctrine/annotations": "~1.2", "psr/log": "~1.0"}}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "461b184cfe5c2e677bbd67761aa377914ab48a16"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/461b184cfe5c2e677bbd67761aa377914ab48a16", "type": "zip", "shasum": "", "reference": "461b184cfe5c2e677bbd67761aa377914ab48a16"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.9"}, "time": "2020-11-26T23:46:31+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d6ceee2a37b61b41079005207bf37746d1bfe71f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d6ceee2a37b61b41079005207bf37746d1bfe71f", "type": "zip", "shasum": "", "reference": "d6ceee2a37b61b41079005207bf37746d1bfe71f"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "720348c2ae011f8c56964c0fc3e992840cb60ccf"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/720348c2ae011f8c56964c0fc3e992840cb60ccf", "type": "zip", "shasum": "", "reference": "720348c2ae011f8c56964c0fc3e992840cb60ccf"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.7"}, "time": "2020-10-02T13:05:43+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d36e06eb02a55522a8eed070c1cbc3dc3c389876"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d36e06eb02a55522a8eed070c1cbc3dc3c389876", "type": "zip", "shasum": "", "reference": "d36e06eb02a55522a8eed070c1cbc3dc3c389876"}, "support": {"source": "https://github.com/symfony/routing/tree/5.1"}, "time": "2020-09-02T16:23:27+00:00"}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "47b0218344cb6af25c93ca8ee1137fafbee5005d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/47b0218344cb6af25c93ca8ee1137fafbee5005d", "type": "zip", "shasum": "", "reference": "47b0218344cb6af25c93ca8ee1137fafbee5005d"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.4"}, "time": "2020-08-10T08:03:57+00:00"}, {"version": "v5.1.4", "version_normalized": "*******"}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "08c9a82f09d12ee048f85e76e0d783f82844eb5d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/08c9a82f09d12ee048f85e76e0d783f82844eb5d", "type": "zip", "shasum": "", "reference": "08c9a82f09d12ee048f85e76e0d783f82844eb5d"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.3"}, "time": "2020-06-18T18:24:02+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "bbd0ba121d623f66d165a55a108008968911f3eb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/bbd0ba121d623f66d165a55a108008968911f3eb", "type": "zip", "shasum": "", "reference": "bbd0ba121d623f66d165a55a108008968911f3eb"}, "support": {"source": "https://github.com/symfony/routing/tree/5.1"}, "time": "2020-06-10T11:49:58+00:00"}, {"version": "v5.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/routing/tree/v5.1.1"}}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "95cf30145b26c758d6d832aa2d0de3128978d556"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/95cf30145b26c758d6d832aa2d0de3128978d556", "type": "zip", "shasum": "", "reference": "95cf30145b26c758d6d832aa2d0de3128978d556"}, "support": {"source": "https://github.com/symfony/routing/tree/5.1"}, "time": "2020-05-30T20:35:19+00:00"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7d0c52b8278d8adc05dc1d880579d9992ad15156"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7d0c52b8278d8adc05dc1d880579d9992ad15156", "type": "zip", "shasum": "", "reference": "7d0c52b8278d8adc05dc1d880579d9992ad15156"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d678a574bdc50629236497776226117b4a55398c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d678a574bdc50629236497776226117b4a55398c", "type": "zip", "shasum": "", "reference": "d678a574bdc50629236497776226117b4a55398c"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.1.0-BETA1"}, "time": "2020-05-05T07:03:13+00:00", "require": {"php": "^7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1369ee6823074c406815b65a40d47fd5ee48e517"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1369ee6823074c406815b65a40d47fd5ee48e517", "type": "zip", "shasum": "", "reference": "1369ee6823074c406815b65a40d47fd5ee48e517"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.11"}, "time": "2020-06-18T18:18:56+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f32f36ee08fd427313f3574546eeb258aa0a752a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f32f36ee08fd427313f3574546eeb258aa0a752a", "type": "zip", "shasum": "", "reference": "f32f36ee08fd427313f3574546eeb258aa0a752a"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.9"}, "time": "2020-05-30T20:12:43+00:00"}, {"version": "v5.0.9", "version_normalized": "*******"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9b18480a6e101f8d9ab7c483ace7c19441be5111"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9b18480a6e101f8d9ab7c483ace7c19441be5111", "type": "zip", "shasum": "", "reference": "9b18480a6e101f8d9ab7c483ace7c19441be5111"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.8"}, "time": "2020-04-21T21:02:50+00:00", "require": {"php": "^7.2.5"}}, {"version": "v5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d98a95d0a684caba47a47c1c50c602a669dc973b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d98a95d0a684caba47a47c1c50c602a669dc973b", "type": "zip", "shasum": "", "reference": "d98a95d0a684caba47a47c1c50c602a669dc973b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.7"}, "time": "2020-03-30T11:42:42+00:00"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c4d3064f355fed672721da90e0153eb7cbe1e574"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c4d3064f355fed672721da90e0153eb7cbe1e574", "type": "zip", "shasum": "", "reference": "c4d3064f355fed672721da90e0153eb7cbe1e574"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.6"}, "time": "2020-03-16T12:10:54+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d6ca39fd05c1902bf34d724ba06fb8044a0b46de"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d6ca39fd05c1902bf34d724ba06fb8044a0b46de", "type": "zip", "shasum": "", "reference": "d6ca39fd05c1902bf34d724ba06fb8044a0b46de"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.5"}, "time": "2020-02-25T14:24:11+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7da33371d8ecfed6c9d93d87c73749661606f803"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7da33371d8ecfed6c9d93d87c73749661606f803", "type": "zip", "shasum": "", "reference": "7da33371d8ecfed6c9d93d87c73749661606f803"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.3"}, "time": "2020-01-04T14:08:26+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******"}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "120c5fa4f4ef5466cbb510ece8126e0007285301"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/120c5fa4f4ef5466cbb510ece8126e0007285301", "type": "zip", "shasum": "", "reference": "120c5fa4f4ef5466cbb510ece8126e0007285301"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.2"}, "time": "2019-12-12T13:03:32+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "145b6f63a0cc781c3300f48d9f250f9aded1e416"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/145b6f63a0cc781c3300f48d9f250f9aded1e416", "type": "zip", "shasum": "", "reference": "145b6f63a0cc781c3300f48d9f250f9aded1e416"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.1"}, "time": "2019-12-01T08:48:26+00:00"}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5d67bc113f3e565f8b3ecbea740f09d32af0a30b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5d67bc113f3e565f8b3ecbea740f09d32af0a30b", "type": "zip", "shasum": "", "reference": "5d67bc113f3e565f8b3ecbea740f09d32af0a30b"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.0"}, "time": "2019-11-20T11:12:35+00:00"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f4e9a16c28b4198ae7de2f417b3304a07dfd6663"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f4e9a16c28b4198ae7de2f417b3304a07dfd6663", "type": "zip", "shasum": "", "reference": "f4e9a16c28b4198ae7de2f417b3304a07dfd6663"}, "support": {"source": "https://github.com/symfony/routing/tree/v5.0.0-BETA1"}, "time": "2019-11-12T14:58:10+00:00", "require": {"php": "^7.2.9"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/routing/tree/master"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/routing/tree/v5.0.0-BETA1"}}, {"description": "Maps an HTTP request to a set of configuration variables", "version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "type": "zip", "shasum": "", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/config": "^4.2|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "extra": "__unset"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c25e38d403c00d5ddcfc514f016f1b534abdf052"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c25e38d403c00d5ddcfc514f016f1b534abdf052", "type": "zip", "shasum": "", "reference": "c25e38d403c00d5ddcfc514f016f1b534abdf052"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.41"}, "time": "2022-04-12T15:19:55+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "324f7f73b89cd30012575119430ccfb1dfbc24be"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/324f7f73b89cd30012575119430ccfb1dfbc24be", "type": "zip", "shasum": "", "reference": "324f7f73b89cd30012575119430ccfb1dfbc24be"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fc9dda0c8496f8ef0a89805c2eabfc43b8cef366"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fc9dda0c8496f8ef0a89805c2eabfc43b8cef366", "type": "zip", "shasum": "", "reference": "fc9dda0c8496f8ef0a89805c2eabfc43b8cef366"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.34"}, "time": "2021-11-04T12:23:33+00:00"}, {"version": "v4.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9ddf033927ad9f30ba2bfd167a7b342cafa13e8e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9ddf033927ad9f30ba2bfd167a7b342cafa13e8e", "type": "zip", "shasum": "", "reference": "9ddf033927ad9f30ba2bfd167a7b342cafa13e8e"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.30"}, "time": "2021-08-04T21:41:01+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "244609821beece97167fa7ba4eef49d2a31862db"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/244609821beece97167fa7ba4eef49d2a31862db", "type": "zip", "shasum": "", "reference": "244609821beece97167fa7ba4eef49d2a31862db"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.27"}, "time": "2021-07-23T15:41:52+00:00"}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3a3c2f197ad0846ac6413225fc78868ba1c61434"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3a3c2f197ad0846ac6413225fc78868ba1c61434", "type": "zip", "shasum": "", "reference": "3a3c2f197ad0846ac6413225fc78868ba1c61434"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.25"}, "time": "2021-05-26T17:39:37+00:00", "require": {"php": ">=7.1.3"}, "require-dev": {"symfony/config": "^4.2|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "doctrine/annotations": "^1.10.4", "psr/log": "~1.0"}}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b42c3631fd9e3511610afb2ba081ea7e38d9fa38"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b42c3631fd9e3511610afb2ba081ea7e38d9fa38", "type": "zip", "shasum": "", "reference": "b42c3631fd9e3511610afb2ba081ea7e38d9fa38"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.24"}, "time": "2021-05-16T09:52:47+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "049e7c5c41f98511959668791b4adc0898a821b3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/049e7c5c41f98511959668791b4adc0898a821b3", "type": "zip", "shasum": "", "reference": "049e7c5c41f98511959668791b4adc0898a821b3"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.22"}, "time": "2021-04-11T12:59:39+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "69919991c845b34626664ddc9b3aef9d09d2a5df"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/69919991c845b34626664ddc9b3aef9d09d2a5df", "type": "zip", "shasum": "", "reference": "69919991c845b34626664ddc9b3aef9d09d2a5df"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.20"}, "time": "2021-02-22T15:37:04+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "87529f6e305c7acb162840d1ea57922038072425"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/87529f6e305c7acb162840d1ea57922038072425", "type": "zip", "shasum": "", "reference": "87529f6e305c7acb162840d1ea57922038072425"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00"}, {"description": "Symfony Routing Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "80b042c20b035818daec844723e23b9825134ba0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/80b042c20b035818daec844723e23b9825134ba0", "type": "zip", "shasum": "", "reference": "80b042c20b035818daec844723e23b9825134ba0"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.18"}, "time": "2020-12-08T16:59:59+00:00", "require-dev": {"symfony/config": "^4.2|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "doctrine/annotations": "~1.2", "psr/log": "~1.0"}}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "08712c5dd5041c03e997e13892f45884faccd868"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/08712c5dd5041c03e997e13892f45884faccd868", "type": "zip", "shasum": "", "reference": "08712c5dd5041c03e997e13892f45884faccd868"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.17"}, "time": "2020-11-24T13:31:32+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "826794f2e9305fe73cba859c60d2a336851bd202"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/826794f2e9305fe73cba859c60d2a336851bd202", "type": "zip", "shasum": "", "reference": "826794f2e9305fe73cba859c60d2a336851bd202"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "006b2d06672b8650998f328fc603eb6f3feb979f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/006b2d06672b8650998f328fc603eb6f3feb979f", "type": "zip", "shasum": "", "reference": "006b2d06672b8650998f328fc603eb6f3feb979f"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.15"}, "time": "2020-10-01T16:25:17+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8db77d97152f55f0df5158cc0a877ad8e16099ac"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8db77d97152f55f0df5158cc0a877ad8e16099ac", "type": "zip", "shasum": "", "reference": "8db77d97152f55f0df5158cc0a877ad8e16099ac"}, "support": {"source": "https://github.com/symfony/routing/tree/4.4"}, "time": "2020-09-02T16:08:58+00:00"}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e3387963565da9bae51d1d3ab8041646cc93bd04"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e3387963565da9bae51d1d3ab8041646cc93bd04", "type": "zip", "shasum": "", "reference": "e3387963565da9bae51d1d3ab8041646cc93bd04"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.12"}, "time": "2020-08-10T07:27:51+00:00"}, {"version": "v4.4.12", "version_normalized": "********"}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e103381a4c2f0731c14589041852bf979e97c7af"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e103381a4c2f0731c14589041852bf979e97c7af", "type": "zip", "shasum": "", "reference": "e103381a4c2f0731c14589041852bf979e97c7af"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.11"}, "time": "2020-07-05T09:39:30+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0f557911dde75c2a9652b8097bd7c9f54507f646"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0f557911dde75c2a9652b8097bd7c9f54507f646", "type": "zip", "shasum": "", "reference": "0f557911dde75c2a9652b8097bd7c9f54507f646"}, "support": {"source": "https://github.com/symfony/routing/tree/4.4"}, "time": "2020-05-30T20:07:26+00:00", "require": {"php": "^7.1.3"}}, {"version": "v4.4.9", "version_normalized": "*******"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "67b4e1f99c050cbc310b8f3d0dbdc4b0212c052c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/67b4e1f99c050cbc310b8f3d0dbdc4b0212c052c", "type": "zip", "shasum": "", "reference": "67b4e1f99c050cbc310b8f3d0dbdc4b0212c052c"}, "time": "2020-04-21T19:59:53+00:00"}, {"version": "v4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0f562fa613e288d7dbae6c63abbc9b33ed75a8f8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0f562fa613e288d7dbae6c63abbc9b33ed75a8f8", "type": "zip", "shasum": "", "reference": "0f562fa613e288d7dbae6c63abbc9b33ed75a8f8"}, "time": "2020-03-30T11:41:10+00:00"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "bd92312650007d29bbabf00795c591b975a0b9a6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/bd92312650007d29bbabf00795c591b975a0b9a6", "type": "zip", "shasum": "", "reference": "bd92312650007d29bbabf00795c591b975a0b9a6"}, "time": "2020-03-16T11:24:17+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4124d621d0e445732520037f888a0456951bde8c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4124d621d0e445732520037f888a0456951bde8c", "type": "zip", "shasum": "", "reference": "4124d621d0e445732520037f888a0456951bde8c"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.5"}, "time": "2020-02-25T12:41:09+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7bf4e38573728e317b926ca4482ad30470d0e86a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7bf4e38573728e317b926ca4482ad30470d0e86a", "type": "zip", "shasum": "", "reference": "7bf4e38573728e317b926ca4482ad30470d0e86a"}, "support": {"source": "https://github.com/symfony/routing/tree/4.4"}, "time": "2020-01-08T17:29:02+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "628bcafae1b2043969378dcfbf9c196539a38722"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/628bcafae1b2043969378dcfbf9c196539a38722", "type": "zip", "shasum": "", "reference": "628bcafae1b2043969378dcfbf9c196539a38722"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.2"}, "time": "2019-12-12T12:53:52+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "51f3f20ad29329a0bdf5c0e2f722d3764b065273"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/51f3f20ad29329a0bdf5c0e2f722d3764b065273", "type": "zip", "shasum": "", "reference": "51f3f20ad29329a0bdf5c0e2f722d3764b065273"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.1"}, "time": "2019-12-01T08:39:58+00:00"}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cf6d72cf0348775f5243b8389169a7096221ea40"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cf6d72cf0348775f5243b8389169a7096221ea40", "type": "zip", "shasum": "", "reference": "cf6d72cf0348775f5243b8389169a7096221ea40"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.4.0"}, "time": "2019-11-20T13:44:34+00:00"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "99876ba2ac4fd8fa07db6b3cf3db2ab3aa0caa6a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/99876ba2ac4fd8fa07db6b3cf3db2ab3aa0caa6a", "type": "zip", "shasum": "", "reference": "99876ba2ac4fd8fa07db6b3cf3db2ab3aa0caa6a"}, "support": {"source": "https://github.com/symfony/routing/tree/4.4"}, "time": "2019-11-12T14:51:11+00:00"}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/routing/tree/v4.4.0-BETA1"}}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6cc4b6a92e3c623b2c7e56180728839b0caf8564"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6cc4b6a92e3c623b2c7e56180728839b0caf8564", "type": "zip", "shasum": "", "reference": "6cc4b6a92e3c623b2c7e56180728839b0caf8564"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.3.11"}, "time": "2020-01-08T14:00:15+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require-dev": {"symfony/config": "~4.2", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "doctrine/annotations": "~1.2", "psr/log": "~1.0"}}, {"version": "v4.3.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/4.3"}}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a252cd9441a00e71b52a28838cbd14115795a725"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a252cd9441a00e71b52a28838cbd14115795a725", "type": "zip", "shasum": "", "reference": "a252cd9441a00e71b52a28838cbd14115795a725"}, "time": "2019-12-01T08:34:52+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "533fd12a41fb9ce8d4e861693365427849487c0e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/533fd12a41fb9ce8d4e861693365427849487c0e", "type": "zip", "shasum": "", "reference": "533fd12a41fb9ce8d4e861693365427849487c0e"}, "time": "2019-11-04T20:23:03+00:00"}, {"version": "v4.3.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/routing/tree/v4.3.7"}}, {"version": "v4.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "63a9920cc86fcc745e5ea254e362f02b615290b9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/63a9920cc86fcc745e5ea254e362f02b615290b9", "type": "zip", "shasum": "", "reference": "63a9920cc86fcc745e5ea254e362f02b615290b9"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.3.6"}, "time": "2019-10-30T12:58:49+00:00"}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3b174ef04fe66696524efad1e5f7a6c663d822ea"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3b174ef04fe66696524efad1e5f7a6c663d822ea", "type": "zip", "shasum": "", "reference": "3b174ef04fe66696524efad1e5f7a6c663d822ea"}, "support": {"source": "https://github.com/symfony/routing/tree/4.3"}, "time": "2019-10-04T20:57:10+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ff1049f6232dc5b6023b1ff1c6de56f82bcd264f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ff1049f6232dc5b6023b1ff1c6de56f82bcd264f", "type": "zip", "shasum": "", "reference": "ff1049f6232dc5b6023b1ff1c6de56f82bcd264f"}, "time": "2019-08-26T08:26:39+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a88c47a5861549f5dc1197660818084c3b67d773"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a88c47a5861549f5dc1197660818084c3b67d773", "type": "zip", "shasum": "", "reference": "a88c47a5861549f5dc1197660818084c3b67d773"}, "time": "2019-07-23T14:43:56+00:00"}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "2ef809021d72071c611b218c47a3bf3b17b7325e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/2ef809021d72071c611b218c47a3bf3b17b7325e", "type": "zip", "shasum": "", "reference": "2ef809021d72071c611b218c47a3bf3b17b7325e"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.3.2"}, "time": "2019-06-26T13:54:39+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9b31cd24f6ad2cebde6845f6daa9c6d69efe2465"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9b31cd24f6ad2cebde6845f6daa9c6d69efe2465", "type": "zip", "shasum": "", "reference": "9b31cd24f6ad2cebde6845f6daa9c6d69efe2465"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.3.1"}, "time": "2019-06-05T09:16:20+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e6cc85f03102ef5e4aedfe636f83e58cf6fd7338"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e6cc85f03102ef5e4aedfe636f83e58cf6fd7338", "type": "zip", "shasum": "", "reference": "e6cc85f03102ef5e4aedfe636f83e58cf6fd7338"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.3.0-RC1"}, "time": "2019-05-20T16:16:12+00:00"}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/routing/tree/4.3"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "82fac7d892e29a858f570eb4bc015c4b9521e821"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/82fac7d892e29a858f570eb4bc015c4b9521e821", "type": "zip", "shasum": "", "reference": "82fac7d892e29a858f570eb4bc015c4b9521e821"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2019-04-27T14:39:34+00:00"}, {"version": "v4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1174ae15f862a0f2d481c29ba172a70b208c9561"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1174ae15f862a0f2d481c29ba172a70b208c9561", "type": "zip", "shasum": "", "reference": "1174ae15f862a0f2d481c29ba172a70b208c9561"}, "support": {"source": "https://github.com/symfony/routing/tree/4.2"}, "time": "2019-06-26T13:53:23+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "require-dev": {"symfony/config": "~4.2", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v4.2.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/v4.2.10"}}, {"version": "v4.2.10", "version_normalized": "********"}, {"version": "v4.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c5ce09ed9db079dded1017a2494dbf6820efd204"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c5ce09ed9db079dded1017a2494dbf6820efd204", "type": "zip", "shasum": "", "reference": "c5ce09ed9db079dded1017a2494dbf6820efd204"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.2.9"}, "time": "2019-05-20T16:15:26+00:00"}, {"version": "v4.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f4e43bb0dff56f0f62fa056c82d7eadcdb391bab"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f4e43bb0dff56f0f62fa056c82d7eadcdb391bab", "type": "zip", "shasum": "", "reference": "f4e43bb0dff56f0f62fa056c82d7eadcdb391bab"}, "support": {"source": "https://github.com/symfony/routing/tree/4.2"}, "time": "2019-04-27T09:38:08+00:00"}, {"version": "v4.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0e5719d216017b1a0342fa48e86467cedca1c954"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0e5719d216017b1a0342fa48e86467cedca1c954", "type": "zip", "shasum": "", "reference": "0e5719d216017b1a0342fa48e86467cedca1c954"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.2.7"}, "time": "2019-04-14T18:04:59+00:00"}, {"version": "v4.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b9f16550d76897ab0a86c198f8008c6578a5068f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b9f16550d76897ab0a86c198f8008c6578a5068f", "type": "zip", "shasum": "", "reference": "b9f16550d76897ab0a86c198f8008c6578a5068f"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.2.6"}, "time": "2019-04-03T13:26:22+00:00"}, {"version": "v4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "319f600c1ea0f981f6bdc2f042cfc1690957c0e0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/319f600c1ea0f981f6bdc2f042cfc1690957c0e0", "type": "zip", "shasum": "", "reference": "319f600c1ea0f981f6bdc2f042cfc1690957c0e0"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.2.5"}, "time": "2019-03-30T15:58:42+00:00"}, {"version": "v4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ff03eae644e6b1e26d4a04b2385fe3a1a7f04e42"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ff03eae644e6b1e26d4a04b2385fe3a1a7f04e42", "type": "zip", "shasum": "", "reference": "ff03eae644e6b1e26d4a04b2385fe3a1a7f04e42"}, "support": {"source": "https://github.com/symfony/routing/tree/4.2"}, "time": "2019-02-23T15:17:42+00:00", "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}}, {"version": "v4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7f8e44fc498972466f0841c3e48dc555f23bdf53"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7f8e44fc498972466f0841c3e48dc555f23bdf53", "type": "zip", "shasum": "", "reference": "7f8e44fc498972466f0841c3e48dc555f23bdf53"}, "time": "2019-01-29T09:49:29+00:00"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e69b7a13a0b58af378a49b49dd7084462de16cee"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e69b7a13a0b58af378a49b49dd7084462de16cee", "type": "zip", "shasum": "", "reference": "e69b7a13a0b58af378a49b49dd7084462de16cee"}, "time": "2019-01-03T09:07:35+00:00"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "649460207e77da6c545326c7f53618d23ad2c866"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/649460207e77da6c545326c7f53618d23ad2c866", "type": "zip", "shasum": "", "reference": "649460207e77da6c545326c7f53618d23ad2c866"}, "time": "2018-12-03T22:08:12+00:00"}, {"version": "v4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "97b9f457df748357eec101df5c8b1c649b543241"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/97b9f457df748357eec101df5c8b1c649b543241", "type": "zip", "shasum": "", "reference": "97b9f457df748357eec101df5c8b1c649b543241"}, "time": "2018-11-29T14:48:32+00:00"}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "858d356b648f4345defbeff4a82b8bc3e137ae14"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/858d356b648f4345defbeff4a82b8bc3e137ae14", "type": "zip", "shasum": "", "reference": "858d356b648f4345defbeff4a82b8bc3e137ae14"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2018-11-26T10:55:26+00:00"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "19be3f528002ddbb0e03f188c5c3285020b151f7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/19be3f528002ddbb0e03f188c5c3285020b151f7", "type": "zip", "shasum": "", "reference": "19be3f528002ddbb0e03f188c5c3285020b151f7"}, "time": "2018-11-15T12:17:10+00:00"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b7138e66721449d2b64134e80894b337b8ea6b1d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b7138e66721449d2b64134e80894b337b8ea6b1d", "type": "zip", "shasum": "", "reference": "b7138e66721449d2b64134e80894b337b8ea6b1d"}, "time": "2018-10-31T10:56:31+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "da122c1ee55cf15bf9784e739b2251dec638f9cc"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/da122c1ee55cf15bf9784e739b2251dec638f9cc", "type": "zip", "shasum": "", "reference": "da122c1ee55cf15bf9784e739b2251dec638f9cc"}, "support": {"source": "https://github.com/symfony/routing/tree/v4.1.12"}, "time": "2019-01-29T09:39:33+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9708dbe372c3dccfe090bf988adf4488c58bb52a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9708dbe372c3dccfe090bf988adf4488c58bb52a", "type": "zip", "shasum": "", "reference": "9708dbe372c3dccfe090bf988adf4488c58bb52a"}, "time": "2019-01-03T09:05:57+00:00"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cda9b423e3457b5915687f8dab5679c1fd03fd80"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cda9b423e3457b5915687f8dab5679c1fd03fd80", "type": "zip", "shasum": "", "reference": "cda9b423e3457b5915687f8dab5679c1fd03fd80"}, "time": "2018-12-03T21:38:57+00:00"}, {"version": "v4.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "eebc037d45c253af2349c5b25a2d0531a732efdc"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/eebc037d45c253af2349c5b25a2d0531a732efdc", "type": "zip", "shasum": "", "reference": "eebc037d45c253af2349c5b25a2d0531a732efdc"}, "time": "2018-11-26T10:24:14+00:00"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d4a3c14cfbe6b9c05a1d6e948654022d4d1ad3fd"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d4a3c14cfbe6b9c05a1d6e948654022d4d1ad3fd", "type": "zip", "shasum": "", "reference": "d4a3c14cfbe6b9c05a1d6e948654022d4d1ad3fd"}, "time": "2018-10-28T18:38:52+00:00"}, {"version": "v4.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "537803f0bdfede36b9acef052d2e4d447d9fa0e9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/537803f0bdfede36b9acef052d2e4d447d9fa0e9", "type": "zip", "shasum": "", "reference": "537803f0bdfede36b9acef052d2e4d447d9fa0e9"}, "time": "2018-10-02T12:40:59+00:00"}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d998113cf6db1e8262fdd8d5db9774c9a7be33b0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d998113cf6db1e8262fdd8d5db9774c9a7be33b0", "type": "zip", "shasum": "", "reference": "d998113cf6db1e8262fdd8d5db9774c9a7be33b0"}, "time": "2018-09-08T13:24:10+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a5784c2ec4168018c87b38f0e4f39d2278499f51"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a5784c2ec4168018c87b38f0e4f39d2278499f51", "type": "zip", "shasum": "", "reference": "a5784c2ec4168018c87b38f0e4f39d2278499f51"}, "time": "2018-08-03T07:58:40+00:00"}, {"version": "v4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6912cfebc0ea4e7a46fdd15c9bd1f427dd39ff1b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6912cfebc0ea4e7a46fdd15c9bd1f427dd39ff1b", "type": "zip", "shasum": "", "reference": "6912cfebc0ea4e7a46fdd15c9bd1f427dd39ff1b"}, "time": "2018-07-26T11:24:31+00:00"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "73770bf3682b4407b017c2bdcb2b11cdcbce5322"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/73770bf3682b4407b017c2bdcb2b11cdcbce5322", "type": "zip", "shasum": "", "reference": "73770bf3682b4407b017c2bdcb2b11cdcbce5322"}, "time": "2018-06-28T06:30:33+00:00"}, {"version": "v4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b38b9797327b26ea2e4146a40e6e2dc9820a6932"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b38b9797327b26ea2e4146a40e6e2dc9820a6932", "type": "zip", "shasum": "", "reference": "b38b9797327b26ea2e4146a40e6e2dc9820a6932"}, "time": "2018-06-19T21:38:16+00:00"}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "180b51c66d10f09e562c9ebc395b39aacb2cf8a2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/180b51c66d10f09e562c9ebc395b39aacb2cf8a2", "type": "zip", "shasum": "", "reference": "180b51c66d10f09e562c9ebc395b39aacb2cf8a2"}, "time": "2018-05-30T07:26:09+00:00", "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "48c3d6e78097c434338a7bff3e53a316f7b8d5a8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/48c3d6e78097c434338a7bff3e53a316f7b8d5a8", "type": "zip", "shasum": "", "reference": "48c3d6e78097c434338a7bff3e53a316f7b8d5a8"}, "time": "2018-05-25T21:14:53+00:00"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "54c81d22ee70981e77763b296b36097a6b669985"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/54c81d22ee70981e77763b296b36097a6b669985", "type": "zip", "shasum": "", "reference": "54c81d22ee70981e77763b296b36097a6b669985"}, "time": "2018-05-17T10:36:03+00:00"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ac9df6928e717e5dc1378951c5750e0a73c0235b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ac9df6928e717e5dc1378951c5750e0a73c0235b", "type": "zip", "shasum": "", "reference": "ac9df6928e717e5dc1378951c5750e0a73c0235b"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2018-04-27T09:21:35+00:00"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "698f48e01196dd99c14fdae980e55bd7c71fae40"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/698f48e01196dd99c14fdae980e55bd7c71fae40", "type": "zip", "shasum": "", "reference": "698f48e01196dd99c14fdae980e55bd7c71fae40"}, "support": {"source": "https://github.com/symfony/routing/tree/4.0"}, "time": "2018-07-26T11:22:46+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c889c79c10aaaa7b6069c6c8988fa03d69affc58"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c889c79c10aaaa7b6069c6c8988fa03d69affc58", "type": "zip", "shasum": "", "reference": "c889c79c10aaaa7b6069c6c8988fa03d69affc58"}, "time": "2018-06-19T20:54:48+00:00"}, {"version": "v4.0.12", "version_normalized": "********"}, {"version": "v4.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e8833b64b139926cbe1610d53722185e55c18e44"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e8833b64b139926cbe1610d53722185e55c18e44", "type": "zip", "shasum": "", "reference": "e8833b64b139926cbe1610d53722185e55c18e44"}, "time": "2018-05-16T14:21:07+00:00", "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/http-foundation": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1dfbfdf060bbc80da8dedc062050052e694cd027"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1dfbfdf060bbc80da8dedc062050052e694cd027", "type": "zip", "shasum": "", "reference": "1dfbfdf060bbc80da8dedc062050052e694cd027"}, "time": "2018-04-20T06:20:23+00:00"}, {"version": "v4.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0663036dd57dbfd4e9ff29f75bbd5dd3253ebe71"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0663036dd57dbfd4e9ff29f75bbd5dd3253ebe71", "type": "zip", "shasum": "", "reference": "0663036dd57dbfd4e9ff29f75bbd5dd3253ebe71"}, "time": "2018-04-04T13:50:32+00:00"}, {"version": "v4.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ca780c838046bfef4a6fd50284ae71a5d1f1a8b2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ca780c838046bfef4a6fd50284ae71a5d1f1a8b2", "type": "zip", "shasum": "", "reference": "ca780c838046bfef4a6fd50284ae71a5d1f1a8b2"}, "time": "2018-04-02T09:52:41+00:00"}, {"version": "v4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9c6268c1970c7e507bedc8946bece32a7db23515"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9c6268c1970c7e507bedc8946bece32a7db23515", "type": "zip", "shasum": "", "reference": "9c6268c1970c7e507bedc8946bece32a7db23515"}, "time": "2018-02-28T21:50:02+00:00"}, {"version": "v4.0.5", "version_normalized": "*******"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a69bd948700b672e036147762f46749bcae33796"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a69bd948700b672e036147762f46749bcae33796", "type": "zip", "shasum": "", "reference": "a69bd948700b672e036147762f46749bcae33796"}, "time": "2018-01-16T18:04:12+00:00"}, {"version": "v4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a34b58ed26cc090f99b2ef833d609a6884581b3c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a34b58ed26cc090f99b2ef833d609a6884581b3c", "type": "zip", "shasum": "", "reference": "a34b58ed26cc090f99b2ef833d609a6884581b3c"}, "time": "2018-01-04T15:52:56+00:00"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "972810def5cae044d19195045f7eb418141bf37b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/972810def5cae044d19195045f7eb418141bf37b", "type": "zip", "shasum": "", "reference": "972810def5cae044d19195045f7eb418141bf37b"}, "time": "2017-12-14T22:39:22+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "82c0de9bfc4b295e11a172d5f98b60b88611e0e7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/82c0de9bfc4b295e11a172d5f98b60b88611e0e7", "type": "zip", "shasum": "", "reference": "82c0de9bfc4b295e11a172d5f98b60b88611e0e7"}, "time": "2017-11-24T14:34:08+00:00"}, {"version": "v4.0.0", "version_normalized": "*******"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "337a9eadc95ce7652ebe279ba3f5d86a0e0c5fad"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/337a9eadc95ce7652ebe279ba3f5d86a0e0c5fad", "type": "zip", "shasum": "", "reference": "337a9eadc95ce7652ebe279ba3f5d86a0e0c5fad"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2017-11-19T20:10:49+00:00"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "faffea9e427c2ab337eba8419de6f5a7ade636ce"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/faffea9e427c2ab337eba8419de6f5a7ade636ce", "type": "zip", "shasum": "", "reference": "faffea9e427c2ab337eba8419de6f5a7ade636ce"}, "time": "2017-11-07T14:45:01+00:00"}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a031adc974a737fe9880b652b551435db2629e98"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a031adc974a737fe9880b652b551435db2629e98", "type": "zip", "shasum": "", "reference": "a031adc974a737fe9880b652b551435db2629e98"}, "time": "2017-10-24T14:16:56+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7686c809b4b6c73b87e691fe67e4f23887420e7b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7686c809b4b6c73b87e691fe67e4f23887420e7b", "type": "zip", "shasum": "", "reference": "7686c809b4b6c73b87e691fe67e4f23887420e7b"}, "time": "2017-10-19T00:12:27+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3e522ac69cadffd8131cc2b22157fa7662331a6c", "type": "zip", "shasum": "", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00", "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/config": "^3.3.1|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/v3.4.46"}}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9c62272ecc68d1a055ded693a5b47683300fa213"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9c62272ecc68d1a055ded693a5b47683300fa213", "type": "zip", "shasum": "", "reference": "9c62272ecc68d1a055ded693a5b47683300fa213"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.45"}, "time": "2020-09-02T16:06:40+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0614c9ef738c99f4d41c2cd1b2d0a44c67fd301a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0614c9ef738c99f4d41c2cd1b2d0a44c67fd301a", "type": "zip", "shasum": "", "reference": "0614c9ef738c99f4d41c2cd1b2d0a44c67fd301a"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.44"}, "time": "2020-08-09T11:28:08+00:00"}, {"version": "v3.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "58381b7b815c1e3afaf60a534fbf769157fe5fe7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/58381b7b815c1e3afaf60a534fbf769157fe5fe7", "type": "zip", "shasum": "", "reference": "58381b7b815c1e3afaf60a534fbf769157fe5fe7"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.43"}, "time": "2020-07-23T09:37:51+00:00"}, {"version": "v3.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e0d43b6f9417ad59ecaa8e2f799b79eef417387f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e0d43b6f9417ad59ecaa8e2f799b79eef417387f", "type": "zip", "shasum": "", "reference": "e0d43b6f9417ad59ecaa8e2f799b79eef417387f"}, "support": {"source": "https://github.com/symfony/routing/tree/3.4"}, "time": "2020-05-30T19:50:06+00:00"}, {"version": "v3.4.41", "version_normalized": "********"}, {"version": "v3.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "53b432fde8eea7dab820e75abda5b97fdaa829b4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/53b432fde8eea7dab820e75abda5b97fdaa829b4", "type": "zip", "shasum": "", "reference": "53b432fde8eea7dab820e75abda5b97fdaa829b4"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.40"}, "time": "2020-04-12T09:58:27+00:00"}, {"version": "v3.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "785e4e6b835e9ab4f9412862855d0e1b7a2b4627"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/785e4e6b835e9ab4f9412862855d0e1b7a2b4627", "type": "zip", "shasum": "", "reference": "785e4e6b835e9ab4f9412862855d0e1b7a2b4627"}, "support": {"source": "https://github.com/symfony/routing/tree/3.4"}, "time": "2020-03-25T12:02:26+00:00"}, {"version": "v3.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c1377905edfa76e6934dd3c73f9a073305b47c00"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c1377905edfa76e6934dd3c73f9a073305b47c00", "type": "zip", "shasum": "", "reference": "c1377905edfa76e6934dd3c73f9a073305b47c00"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.38"}, "time": "2020-02-04T08:04:52+00:00"}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6366fbf86a05abccf77d6e605abbb0ee342f2cfa"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6366fbf86a05abccf77d6e605abbb0ee342f2cfa", "type": "zip", "shasum": "", "reference": "6366fbf86a05abccf77d6e605abbb0ee342f2cfa"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.37"}, "time": "2020-01-04T12:05:51+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b689ccd48e234ea404806d94b07eeb45f9f6f06a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b689ccd48e234ea404806d94b07eeb45f9f6f06a", "type": "zip", "shasum": "", "reference": "b689ccd48e234ea404806d94b07eeb45f9f6f06a"}, "support": {"source": "https://github.com/symfony/routing/tree/3.4"}, "time": "2019-12-01T08:33:36+00:00"}, {"version": "v3.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "afc10b9c6b5196e0fecbc3bd373c7b4482e5b6b5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/afc10b9c6b5196e0fecbc3bd373c7b4482e5b6b5", "type": "zip", "shasum": "", "reference": "afc10b9c6b5196e0fecbc3bd373c7b4482e5b6b5"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.35"}, "time": "2019-11-08T17:25:00+00:00"}, {"version": "v3.4.34", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/3.4"}}, {"version": "v3.4.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ebc745e728f7a9fb571e7c116a14245c00b8f366"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ebc745e728f7a9fb571e7c116a14245c00b8f366", "type": "zip", "shasum": "", "reference": "ebc745e728f7a9fb571e7c116a14245c00b8f366"}, "time": "2019-10-24T15:33:53+00:00"}, {"version": "v3.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8b0faa681c4ee14701e76a7056fef15ac5384163"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8b0faa681c4ee14701e76a7056fef15ac5384163", "type": "zip", "shasum": "", "reference": "8b0faa681c4ee14701e76a7056fef15ac5384163"}, "support": {"source": "https://github.com/symfony/routing/tree/v3.4.32"}, "time": "2019-08-26T07:50:50+00:00"}, {"version": "v3.4.31", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/3.4"}}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8d804d8a65a26dc9de1aaf2ff3a421e581d050e6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8d804d8a65a26dc9de1aaf2ff3a421e581d050e6", "type": "zip", "shasum": "", "reference": "8d804d8a65a26dc9de1aaf2ff3a421e581d050e6"}, "time": "2019-06-26T11:14:13+00:00"}, {"version": "v3.4.29", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/v3.4.29"}}, {"version": "v3.4.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3458f90c2c17dfbb3260dbbfca19a0c415576ce0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3458f90c2c17dfbb3260dbbfca19a0c415576ce0", "type": "zip", "shasum": "", "reference": "3458f90c2c17dfbb3260dbbfca19a0c415576ce0"}, "support": {"source": "https://github.com/symfony/routing/tree/3.4"}, "time": "2019-05-18T16:36:47+00:00"}, {"version": "v3.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ff11aac46d6cb8a65f2855687bb9a1ac9d860eec"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ff11aac46d6cb8a65f2855687bb9a1ac9d860eec", "type": "zip", "shasum": "", "reference": "ff11aac46d6cb8a65f2855687bb9a1ac9d860eec"}, "time": "2019-03-29T21:58:42+00:00"}, {"version": "v3.4.26", "version_normalized": "********"}, {"version": "v3.4.25", "version_normalized": "********"}, {"version": "v3.4.24", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/v3.4.24"}}, {"version": "v3.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6b25a86df5860461ff1990946168c0ef944f83db"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6b25a86df5860461ff1990946168c0ef944f83db", "type": "zip", "shasum": "", "reference": "6b25a86df5860461ff1990946168c0ef944f83db"}, "support": {"source": "https://github.com/symfony/routing/tree/3.4"}, "time": "2019-02-23T15:06:07+00:00", "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}}, {"version": "v3.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "62f0b8d8cd2cd359c3caa5a9f5253a4a6d480646"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/62f0b8d8cd2cd359c3caa5a9f5253a4a6d480646", "type": "zip", "shasum": "", "reference": "62f0b8d8cd2cd359c3caa5a9f5253a4a6d480646"}, "time": "2019-01-29T08:47:12+00:00"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "445d3629a26930158347a50d1a5f2456c49e0ae6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/445d3629a26930158347a50d1a5f2456c49e0ae6", "type": "zip", "shasum": "", "reference": "445d3629a26930158347a50d1a5f2456c49e0ae6"}, "time": "2019-01-01T13:45:19+00:00"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "94a3dd89bda078bef0c3bf79eb024fe136dd58f9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/94a3dd89bda078bef0c3bf79eb024fe136dd58f9", "type": "zip", "shasum": "", "reference": "94a3dd89bda078bef0c3bf79eb024fe136dd58f9"}, "time": "2018-12-03T13:20:34+00:00"}, {"version": "v3.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "86eb1a581279b5e40ca280a4f63a15e37d51d16c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/86eb1a581279b5e40ca280a4f63a15e37d51d16c", "type": "zip", "shasum": "", "reference": "86eb1a581279b5e40ca280a4f63a15e37d51d16c"}, "time": "2018-11-26T08:40:22+00:00"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "585f6e2d740393d546978769dd56e496a6233e0b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/585f6e2d740393d546978769dd56e496a6233e0b", "type": "zip", "shasum": "", "reference": "585f6e2d740393d546978769dd56e496a6233e0b"}, "time": "2018-10-02T12:28:39+00:00"}, {"version": "v3.4.17", "version_normalized": "********"}, {"version": "v3.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "eb22cc1d2a89975ebd92454b8dad8c0940df8284"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/eb22cc1d2a89975ebd92454b8dad8c0940df8284", "type": "zip", "shasum": "", "reference": "eb22cc1d2a89975ebd92454b8dad8c0940df8284"}, "time": "2018-09-08T13:15:14+00:00"}, {"version": "v3.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e20f4bb79502c3c0db86d572f7683a30d4143911"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e20f4bb79502c3c0db86d572f7683a30d4143911", "type": "zip", "shasum": "", "reference": "e20f4bb79502c3c0db86d572f7683a30d4143911"}, "time": "2018-07-26T11:19:56+00:00"}, {"version": "v3.4.14", "version_normalized": "********"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6b9fef5343828e542db17e2519722ef08992f2c1"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6b9fef5343828e542db17e2519722ef08992f2c1", "type": "zip", "shasum": "", "reference": "6b9fef5343828e542db17e2519722ef08992f2c1"}, "time": "2018-06-19T20:52:10+00:00"}, {"version": "v3.4.12", "version_normalized": "********"}, {"version": "v3.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e382da877f5304aabc12ec3073eec430670c8296"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e382da877f5304aabc12ec3073eec430670c8296", "type": "zip", "shasum": "", "reference": "e382da877f5304aabc12ec3073eec430670c8296"}, "time": "2018-05-16T12:49:49+00:00", "require-dev": {"symfony/config": "^3.3.1|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9deb375986f5d1f37283d8386716d26985a0f4b6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9deb375986f5d1f37283d8386716d26985a0f4b6", "type": "zip", "shasum": "", "reference": "9deb375986f5d1f37283d8386716d26985a0f4b6"}, "time": "2018-04-12T09:01:03+00:00"}, {"version": "v3.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "50f333b707bef9f6972ad04e6df3ec8875c9a67c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/50f333b707bef9f6972ad04e6df3ec8875c9a67c", "type": "zip", "shasum": "", "reference": "50f333b707bef9f6972ad04e6df3ec8875c9a67c"}, "time": "2018-04-04T13:22:16+00:00"}, {"version": "v3.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5f90733adbf19ea71468a5761fb8f5a043d424dd"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5f90733adbf19ea71468a5761fb8f5a043d424dd", "type": "zip", "shasum": "", "reference": "5f90733adbf19ea71468a5761fb8f5a043d424dd"}, "time": "2018-03-23T08:14:09+00:00"}, {"version": "v3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8773a9d52715f1a579576ce0e60213de34f5ef3e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8773a9d52715f1a579576ce0e60213de34f5ef3e", "type": "zip", "shasum": "", "reference": "8773a9d52715f1a579576ce0e60213de34f5ef3e"}, "time": "2018-02-28T21:49:22+00:00", "require-dev": {"symfony/config": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}}, {"version": "v3.4.5", "version_normalized": "*******"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "235d01730d553a97732990588407eaf6779bb4b2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/235d01730d553a97732990588407eaf6779bb4b2", "type": "zip", "shasum": "", "reference": "235d01730d553a97732990588407eaf6779bb4b2"}, "time": "2018-01-16T18:03:57+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e2b6d6fe7b090c7af720b75c7722c6dfa7a52658"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e2b6d6fe7b090c7af720b75c7722c6dfa7a52658", "type": "zip", "shasum": "", "reference": "e2b6d6fe7b090c7af720b75c7722c6dfa7a52658"}, "time": "2018-01-04T15:09:34+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5f248dfac5e4660c74982eb3dadc71cf58595570"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5f248dfac5e4660c74982eb3dadc71cf58595570", "type": "zip", "shasum": "", "reference": "5f248dfac5e4660c74982eb3dadc71cf58595570"}, "time": "2017-12-14T22:37:31+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d768aa5b25d98188bae3fe4ce3eb2924c97aafac"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d768aa5b25d98188bae3fe4ce3eb2924c97aafac", "type": "zip", "shasum": "", "reference": "d768aa5b25d98188bae3fe4ce3eb2924c97aafac"}, "time": "2017-11-24T14:13:49+00:00"}, {"version": "v3.4.0", "version_normalized": "*******"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fb3865c894f5dd00e7364f2a1cf30f91bd146628"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fb3865c894f5dd00e7364f2a1cf30f91bd146628", "type": "zip", "shasum": "", "reference": "fb3865c894f5dd00e7364f2a1cf30f91bd146628"}, "time": "2017-11-19T20:09:36+00:00"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "78e46a29ea0a76f8a63b5aecde17270c36542d7b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/78e46a29ea0a76f8a63b5aecde17270c36542d7b", "type": "zip", "shasum": "", "reference": "78e46a29ea0a76f8a63b5aecde17270c36542d7b"}, "time": "2017-11-07T14:20:24+00:00"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "29fff3c44f194f335334875f938ba71ba599a236"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/29fff3c44f194f335334875f938ba71ba599a236", "type": "zip", "shasum": "", "reference": "29fff3c44f194f335334875f938ba71ba599a236"}, "time": "2017-11-05T16:10:10+00:00"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ea20b62d5586a4c83f31dffff7b206eaa65ef32f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ea20b62d5586a4c83f31dffff7b206eaa65ef32f", "type": "zip", "shasum": "", "reference": "ea20b62d5586a4c83f31dffff7b206eaa65ef32f"}, "time": "2017-10-24T14:12:06+00:00"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f37405ce8f22603cb8771f3da023ec9e7edaed83"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f37405ce8f22603cb8771f3da023ec9e7edaed83", "type": "zip", "shasum": "", "reference": "f37405ce8f22603cb8771f3da023ec9e7edaed83"}, "time": "2017-10-16T20:33:34+00:00"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3329b5bf114779dcea6f9d0c04cd55e2d7553067"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3329b5bf114779dcea6f9d0c04cd55e2d7553067", "type": "zip", "shasum": "", "reference": "3329b5bf114779dcea6f9d0c04cd55e2d7553067"}, "support": {"source": "https://github.com/symfony/routing/tree/3.3"}, "time": "2018-01-16T18:03:02+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "require-dev": {"symfony/config": "~2.8|~3.0", "symfony/http-foundation": "~2.8|~3.0", "symfony/yaml": "~3.3", "symfony/expression-language": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.3"}}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c0659f6eaed0924adc9652fb481b5382699a9749"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c0659f6eaed0924adc9652fb481b5382699a9749", "type": "zip", "shasum": "", "reference": "c0659f6eaed0924adc9652fb481b5382699a9749"}, "time": "2018-01-04T15:09:11+00:00"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4788b7e561fe433fb659ffe2a8a2f8af27a48849"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4788b7e561fe433fb659ffe2a8a2f8af27a48849", "type": "zip", "shasum": "", "reference": "4788b7e561fe433fb659ffe2a8a2f8af27a48849"}, "time": "2017-11-24T10:33:04+00:00"}, {"version": "v3.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cf7fa1dfcfee2c96969bfa1c0341e5627ecb1e95"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cf7fa1dfcfee2c96969bfa1c0341e5627ecb1e95", "type": "zip", "shasum": "", "reference": "cf7fa1dfcfee2c96969bfa1c0341e5627ecb1e95"}, "time": "2017-11-07T14:16:22+00:00"}, {"version": "v3.3.12", "version_normalized": "********"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "2e26fa63da029dab49bf9377b3b4f60a8fecb009"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/2e26fa63da029dab49bf9377b3b4f60a8fecb009", "type": "zip", "shasum": "", "reference": "2e26fa63da029dab49bf9377b3b4f60a8fecb009"}, "time": "2017-10-02T07:25:00+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "970326dcd04522e1cd1fe128abaee54c225e27f9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/970326dcd04522e1cd1fe128abaee54c225e27f9", "type": "zip", "shasum": "", "reference": "970326dcd04522e1cd1fe128abaee54c225e27f9"}, "time": "2017-07-29T21:54:42+00:00"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4aee1a917fd4859ff8b51b9fd1dfb790a5ecfa26"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4aee1a917fd4859ff8b51b9fd1dfb790a5ecfa26", "type": "zip", "shasum": "", "reference": "4aee1a917fd4859ff8b51b9fd1dfb790a5ecfa26"}, "time": "2017-07-21T17:43:13+00:00", "require": {"php": ">=5.5.9"}}, {"version": "v3.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "dc70bbd0ca7b19259f63cdacc8af370bc32a4728"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/dc70bbd0ca7b19259f63cdacc8af370bc32a4728", "type": "zip", "shasum": "", "reference": "dc70bbd0ca7b19259f63cdacc8af370bc32a4728"}, "time": "2017-06-24T09:29:48+00:00"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "39804eeafea5cca851946e1eed122eb94459fdb4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/39804eeafea5cca851946e1eed122eb94459fdb4", "type": "zip", "shasum": "", "reference": "39804eeafea5cca851946e1eed122eb94459fdb4"}, "time": "2017-06-02T09:51:43+00:00"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3aa0c7d759a2c88f4dff47c3d6776e7380bb2c9a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3aa0c7d759a2c88f4dff47c3d6776e7380bb2c9a", "type": "zip", "shasum": "", "reference": "3aa0c7d759a2c88f4dff47c3d6776e7380bb2c9a"}, "time": "2017-05-24T11:35:23+00:00"}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fadf93908abafa12bed07680faf52c41c7038317"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fadf93908abafa12bed07680faf52c41c7038317", "type": "zip", "shasum": "", "reference": "fadf93908abafa12bed07680faf52c41c7038317"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2017-04-13T13:45:25+00:00"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b382d7c4f443372c118efcd0cd2bf1028434f2f5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b382d7c4f443372c118efcd0cd2bf1028434f2f5", "type": "zip", "shasum": "", "reference": "b382d7c4f443372c118efcd0cd2bf1028434f2f5"}, "support": {"source": "https://github.com/symfony/routing/tree/3.2"}, "time": "2017-06-23T06:35:45+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require-dev": {"symfony/config": "~2.8|~3.0", "symfony/http-foundation": "~2.8|~3.0", "symfony/yaml": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<2.8"}}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a6d0a35ef83a1f454619afc961f235fed24963f7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a6d0a35ef83a1f454619afc961f235fed24963f7", "type": "zip", "shasum": "", "reference": "a6d0a35ef83a1f454619afc961f235fed24963f7"}, "time": "2017-05-23T22:01:29+00:00"}, {"version": "v3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5029745d6d463585e8b487dbc83d6333f408853a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5029745d6d463585e8b487dbc83d6333f408853a", "type": "zip", "shasum": "", "reference": "5029745d6d463585e8b487dbc83d6333f408853a"}, "time": "2017-04-12T14:13:17+00:00"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d6605f9a5767bc5bc4895e1c762ba93964608aee"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d6605f9a5767bc5bc4895e1c762ba93964608aee", "type": "zip", "shasum": "", "reference": "d6605f9a5767bc5bc4895e1c762ba93964608aee"}, "time": "2017-03-02T15:58:09+00:00"}, {"version": "v3.2.6", "version_normalized": "*******"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "af464432c177dbcdbb32295113b7627500331f2d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/af464432c177dbcdbb32295113b7627500331f2d", "type": "zip", "shasum": "", "reference": "af464432c177dbcdbb32295113b7627500331f2d"}, "time": "2017-01-28T02:37:08+00:00"}, {"version": "v3.2.3", "version_normalized": "*******"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fda2c67d47ec801726ca888c95d701d31b27b444"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fda2c67d47ec801726ca888c95d701d31b27b444", "type": "zip", "shasum": "", "reference": "fda2c67d47ec801726ca888c95d701d31b27b444"}, "time": "2017-01-02T20:32:22+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3f239c0e049d8920928674cd55e21061182b0106"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3f239c0e049d8920928674cd55e21061182b0106", "type": "zip", "shasum": "", "reference": "3f239c0e049d8920928674cd55e21061182b0106"}, "time": "2016-11-25T12:32:42+00:00"}, {"version": "v3.2.0", "version_normalized": "*******"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "75a1dee97199aca3aa4795773a55a2e96a6da498"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/75a1dee97199aca3aa4795773a55a2e96a6da498", "type": "zip", "shasum": "", "reference": "75a1dee97199aca3aa4795773a55a2e96a6da498"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2016-10-21T20:33:10+00:00"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f25581d4eb0a82962c291917f826166f0dcd8a9a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f25581d4eb0a82962c291917f826166f0dcd8a9a", "type": "zip", "shasum": "", "reference": "f25581d4eb0a82962c291917f826166f0dcd8a9a"}, "support": {"source": "https://github.com/symfony/routing/tree/3.1"}, "time": "2017-01-28T00:04:57+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}, {"version": "v3.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5cd8d7b88e9f30a7d830fa15876828da272685d3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5cd8d7b88e9f30a7d830fa15876828da272685d3", "type": "zip", "shasum": "", "reference": "5cd8d7b88e9f30a7d830fa15876828da272685d3"}, "time": "2017-01-02T20:31:54+00:00"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4beb3dceb14cf2dd63dd222d1825ca981a2952cb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4beb3dceb14cf2dd63dd222d1825ca981a2952cb", "type": "zip", "shasum": "", "reference": "4beb3dceb14cf2dd63dd222d1825ca981a2952cb"}, "time": "2016-11-25T12:27:14+00:00"}, {"version": "v3.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8edf62498a1a4c57ba317664a4b698339c10cdf6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8edf62498a1a4c57ba317664a4b698339c10cdf6", "type": "zip", "shasum": "", "reference": "8edf62498a1a4c57ba317664a4b698339c10cdf6"}, "time": "2016-08-16T14:58:24+00:00"}, {"version": "v3.1.6", "version_normalized": "*******"}, {"version": "v3.1.5", "version_normalized": "*******"}, {"version": "v3.1.4", "version_normalized": "*******"}, {"version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "22c7adc204057a0ff0b12eea2889782a5deb70a3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/22c7adc204057a0ff0b12eea2889782a5deb70a3", "type": "zip", "shasum": "", "reference": "22c7adc204057a0ff0b12eea2889782a5deb70a3"}, "time": "2016-06-29T05:41:56+00:00"}, {"version": "v3.1.2", "version_normalized": "*******"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e52818e421659dd58a4eb3d439064fb3c2480999"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e52818e421659dd58a4eb3d439064fb3c2480999", "type": "zip", "shasum": "", "reference": "e52818e421659dd58a4eb3d439064fb3c2480999"}, "time": "2016-05-30T06:58:39+00:00"}, {"version": "v3.1.0", "version_normalized": "*******"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7220655bd709c7bd1c83ccdb8b8a89e46cb740b3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7220655bd709c7bd1c83ccdb8b8a89e46cb740b3", "type": "zip", "shasum": "", "reference": "7220655bd709c7bd1c83ccdb8b8a89e46cb740b3"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2016-05-03T12:24:23+00:00"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9038984bd9c05ab07280121e9e10f61a7231457b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9038984bd9c05ab07280121e9e10f61a7231457b", "type": "zip", "shasum": "", "reference": "9038984bd9c05ab07280121e9e10f61a7231457b"}, "support": {"source": "https://github.com/symfony/routing/tree/3.0"}, "time": "2016-06-29T05:40:00+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c780454838a1131adc756d737a4b4cc1d18f8c64"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c780454838a1131adc756d737a4b4cc1d18f8c64", "type": "zip", "shasum": "", "reference": "c780454838a1131adc756d737a4b4cc1d18f8c64"}, "time": "2016-05-30T06:58:27+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a6cd168310066176599442aa21f5da86c3f8e0b3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a6cd168310066176599442aa21f5da86c3f8e0b3", "type": "zip", "shasum": "", "reference": "a6cd168310066176599442aa21f5da86c3f8e0b3"}, "time": "2016-05-03T12:23:49+00:00"}, {"version": "v3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6ab6fd5ee754fb53a303a5621ae35f3afd5970ac"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6ab6fd5ee754fb53a303a5621ae35f3afd5970ac", "type": "zip", "shasum": "", "reference": "6ab6fd5ee754fb53a303a5621ae35f3afd5970ac"}, "time": "2016-04-28T09:48:42+00:00"}, {"version": "v3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d061b609f2d0769494c381ec92f5c5cc5e4a20aa"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d061b609f2d0769494c381ec92f5c5cc5e4a20aa", "type": "zip", "shasum": "", "reference": "d061b609f2d0769494c381ec92f5c5cc5e4a20aa"}, "time": "2016-03-23T13:23:25+00:00"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fa1e9a8173cf0077dd995205da453eacd758fdf6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fa1e9a8173cf0077dd995205da453eacd758fdf6", "type": "zip", "shasum": "", "reference": "fa1e9a8173cf0077dd995205da453eacd758fdf6"}, "time": "2016-02-04T13:53:13+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4686baa55a835e1c1ede9b86ba02415c8c8d6166"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4686baa55a835e1c1ede9b86ba02415c8c8d6166", "type": "zip", "shasum": "", "reference": "4686baa55a835e1c1ede9b86ba02415c8c8d6166"}, "time": "2016-01-27T05:14:46+00:00", "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/yaml": "For using the YAML loader"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3b1bac52f42cb0f54df1a2dbabd55a1d214e2a59"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3b1bac52f42cb0f54df1a2dbabd55a1d214e2a59", "type": "zip", "shasum": "", "reference": "3b1bac52f42cb0f54df1a2dbabd55a1d214e2a59"}, "time": "2015-12-23T08:00:11+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "252014dfa4685e80f9216ae4b7d78b1a50dd55c2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/252014dfa4685e80f9216ae4b7d78b1a50dd55c2", "type": "zip", "shasum": "", "reference": "252014dfa4685e80f9216ae4b7d78b1a50dd55c2"}, "support": {"source": "https://github.com/symfony/routing/tree/master"}, "time": "2015-11-26T07:02:09+00:00"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "26c3f4784cd99c67fc8693a748f45c847c8c23ab"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/26c3f4784cd99c67fc8693a748f45c847c8c23ab", "type": "zip", "shasum": "", "reference": "26c3f4784cd99c67fc8693a748f45c847c8c23ab"}, "time": "2015-11-10T10:42:06+00:00"}, {"version": "v2.8.52", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8b0df6869d1997baafff6a1541826eac5a03d067"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8b0df6869d1997baafff6a1541826eac5a03d067", "type": "zip", "shasum": "", "reference": "8b0df6869d1997baafff6a1541826eac5a03d067"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.8.50"}, "time": "2018-11-20T15:55:20+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9"}, "require-dev": {"symfony/config": "~2.7|~3.0.0", "symfony/http-foundation": "~2.3|~3.0.0", "symfony/yaml": "^2.0.5|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "conflict": {"symfony/config": "<2.7"}}, {"version": "v2.8.50", "version_normalized": "********"}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "066acfcc71bb8274dc4fcd6d438a2e368053f3a4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/066acfcc71bb8274dc4fcd6d438a2e368053f3a4", "type": "zip", "shasum": "", "reference": "066acfcc71bb8274dc4fcd6d438a2e368053f3a4"}, "time": "2018-10-02T03:12:00+00:00"}, {"version": "v2.8.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fed18962c40095adc36c2ad05bf0d957cc346f61"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fed18962c40095adc36c2ad05bf0d957cc346f61", "type": "zip", "shasum": "", "reference": "fed18962c40095adc36c2ad05bf0d957cc346f61"}, "time": "2018-09-08T12:44:02+00:00"}, {"version": "v2.8.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e26f791e8669603e9dc0a601e75a50f914eaa144"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e26f791e8669603e9dc0a601e75a50f914eaa144", "type": "zip", "shasum": "", "reference": "e26f791e8669603e9dc0a601e75a50f914eaa144"}, "time": "2018-07-26T11:13:39+00:00"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "39064bae264907e48fe4e8ad5141f6dc6a3932fb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/39064bae264907e48fe4e8ad5141f6dc6a3932fb", "type": "zip", "shasum": "", "reference": "39064bae264907e48fe4e8ad5141f6dc6a3932fb"}, "time": "2018-06-19T09:52:17+00:00"}, {"version": "v2.8.42", "version_normalized": "********"}, {"version": "v2.8.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a12693530e2b9e81017fdfb806cd915b9d1cbbb9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a12693530e2b9e81017fdfb806cd915b9d1cbbb9", "type": "zip", "shasum": "", "reference": "a12693530e2b9e81017fdfb806cd915b9d1cbbb9"}, "time": "2018-02-28T21:47:46+00:00", "require-dev": {"symfony/config": "~2.7|~3.0.0", "symfony/http-foundation": "~2.3|~3.0.0", "symfony/yaml": "^2.0.5|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********"}, {"version": "v2.8.38", "version_normalized": "********"}, {"version": "v2.8.37", "version_normalized": "********"}, {"version": "v2.8.36", "version_normalized": "********"}, {"version": "v2.8.35", "version_normalized": "********"}, {"version": "v2.8.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "627ea100720dac15d8165648caac57456dda84aa"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/627ea100720dac15d8165648caac57456dda84aa", "type": "zip", "shasum": "", "reference": "627ea100720dac15d8165648caac57456dda84aa"}, "time": "2018-01-16T18:00:04+00:00"}, {"version": "v2.8.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e0aada430641d074571dbd4b103a07b523494a89"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e0aada430641d074571dbd4b103a07b523494a89", "type": "zip", "shasum": "", "reference": "e0aada430641d074571dbd4b103a07b523494a89"}, "time": "2018-01-04T14:58:28+00:00"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fa8f982682a3b65d87858be7dbf84f7f03feb616"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fa8f982682a3b65d87858be7dbf84f7f03feb616", "type": "zip", "shasum": "", "reference": "fa8f982682a3b65d87858be7dbf84f7f03feb616"}, "time": "2017-11-19T19:05:05+00:00"}, {"version": "v2.8.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "67bcd31a2f2fb1bf25cd8f4d4e0e2e2c91cccd5f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/67bcd31a2f2fb1bf25cd8f4d4e0e2e2c91cccd5f", "type": "zip", "shasum": "", "reference": "67bcd31a2f2fb1bf25cd8f4d4e0e2e2c91cccd5f"}, "time": "2017-11-07T14:08:47+00:00"}, {"version": "v2.8.30", "version_normalized": "********"}, {"version": "v2.8.29", "version_normalized": "********"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "74808bc927c173935edc31e331d742698b055d0a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/74808bc927c173935edc31e331d742698b055d0a", "type": "zip", "shasum": "", "reference": "74808bc927c173935edc31e331d742698b055d0a"}, "time": "2017-10-01T21:00:16+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5cdaffed631e88c972f283bb4f9ca699f3c8bffa"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5cdaffed631e88c972f283bb4f9ca699f3c8bffa", "type": "zip", "shasum": "", "reference": "5cdaffed631e88c972f283bb4f9ca699f3c8bffa"}, "time": "2017-06-20T23:27:56+00:00"}, {"version": "v2.8.26", "version_normalized": "********"}, {"version": "v2.8.25", "version_normalized": "********"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d428588038f13a0e5771a2a8ccbc9de46bba9a19"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d428588038f13a0e5771a2a8ccbc9de46bba9a19", "type": "zip", "shasum": "", "reference": "d428588038f13a0e5771a2a8ccbc9de46bba9a19"}, "time": "2017-06-01T20:52:29+00:00"}, {"version": "v2.8.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a8f328c7e701b7bb05a93fca62a5ab2b6b3e500e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a8f328c7e701b7bb05a93fca62a5ab2b6b3e500e", "type": "zip", "shasum": "", "reference": "a8f328c7e701b7bb05a93fca62a5ab2b6b3e500e"}, "time": "2017-04-12T14:07:15+00:00"}, {"version": "v2.8.20", "version_normalized": "********"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d145cd396f702c497cb24b21785ddac90a23fe71"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d145cd396f702c497cb24b21785ddac90a23fe71", "type": "zip", "shasum": "", "reference": "d145cd396f702c497cb24b21785ddac90a23fe71"}, "time": "2017-03-02T15:56:34+00:00"}, {"version": "v2.8.18", "version_normalized": "********"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e2d142ef10f712dfe8d983c1883270e02c783aa7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e2d142ef10f712dfe8d983c1883270e02c783aa7", "type": "zip", "shasum": "", "reference": "e2d142ef10f712dfe8d983c1883270e02c783aa7"}, "time": "2017-01-27T23:54:58+00:00", "require-dev": {"symfony/config": "~2.7|~3.0.0", "symfony/http-foundation": "~2.3|~3.0.0", "symfony/yaml": "~2.0,>=2.0.5|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.8.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "2a7e3e02bbfb0a4f722e6a3154489e4ac8b3a97f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/2a7e3e02bbfb0a4f722e6a3154489e4ac8b3a97f", "type": "zip", "shasum": "", "reference": "2a7e3e02bbfb0a4f722e6a3154489e4ac8b3a97f"}, "time": "2017-01-02T20:30:24+00:00"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ef15c9f105f43a0714fa96b9e718fa4f23bc27a6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ef15c9f105f43a0714fa96b9e718fa4f23bc27a6", "type": "zip", "shasum": "", "reference": "ef15c9f105f43a0714fa96b9e718fa4f23bc27a6"}, "time": "2016-11-25T12:26:42+00:00"}, {"version": "v2.8.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6cee3ed22c778a7410119a3d51a20f60252a156c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6cee3ed22c778a7410119a3d51a20f60252a156c", "type": "zip", "shasum": "", "reference": "6cee3ed22c778a7410119a3d51a20f60252a156c"}, "time": "2016-08-16T14:56:08+00:00"}, {"version": "v2.8.13", "version_normalized": "********"}, {"version": "v2.8.12", "version_normalized": "********"}, {"version": "v2.8.11", "version_normalized": "********"}, {"version": "v2.8.10", "version_normalized": "********"}, {"version": "v2.8.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cdd298b1d45b9882de0905856e89171bf487c6d5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cdd298b1d45b9882de0905856e89171bf487c6d5", "type": "zip", "shasum": "", "reference": "cdd298b1d45b9882de0905856e89171bf487c6d5"}, "time": "2016-06-29T05:29:29+00:00"}, {"version": "v2.8.8", "version_normalized": "*******"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4cbc81aa378869445fbd2d18c8c8b4a056c632a0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4cbc81aa378869445fbd2d18c8c8b4a056c632a0", "type": "zip", "shasum": "", "reference": "4cbc81aa378869445fbd2d18c8c8b4a056c632a0"}, "time": "2016-05-30T06:57:11+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0d814e0c1dc07cb688ca2298bbf7b32ace80cee1"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0d814e0c1dc07cb688ca2298bbf7b32ace80cee1", "type": "zip", "shasum": "", "reference": "0d814e0c1dc07cb688ca2298bbf7b32ace80cee1"}, "time": "2016-05-03T12:21:46+00:00"}, {"version": "v2.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0a1222c49f8f5a015c6b351f5a35306ad9537285"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0a1222c49f8f5a015c6b351f5a35306ad9537285", "type": "zip", "shasum": "", "reference": "0a1222c49f8f5a015c6b351f5a35306ad9537285"}, "time": "2016-04-26T08:02:35+00:00"}, {"version": "v2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d7d4a20cb55a90a06c0070d1a360e5ac606306ef"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d7d4a20cb55a90a06c0070d1a360e5ac606306ef", "type": "zip", "shasum": "", "reference": "d7d4a20cb55a90a06c0070d1a360e5ac606306ef"}, "time": "2016-03-23T13:11:46+00:00"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ae38e64bae52753c0f4a1a6583f5ba9bb2b742ab"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ae38e64bae52753c0f4a1a6583f5ba9bb2b742ab", "type": "zip", "shasum": "", "reference": "ae38e64bae52753c0f4a1a6583f5ba9bb2b742ab"}, "time": "2016-02-04T13:53:00+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5451a8a1874fd4e6a4dd347ea611d86cd8441735"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5451a8a1874fd4e6a4dd347ea611d86cd8441735", "type": "zip", "shasum": "", "reference": "5451a8a1874fd4e6a4dd347ea611d86cd8441735"}, "time": "2016-01-11T16:43:36+00:00", "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/yaml": "For using the YAML loader"}}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b3261d88bad77de60e01f05706810413cc11367d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b3261d88bad77de60e01f05706810413cc11367d", "type": "zip", "shasum": "", "reference": "b3261d88bad77de60e01f05706810413cc11367d"}, "time": "2015-12-23T07:56:26+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f76830dc0f8068df36226dc822e7fc1f5f73be46"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f76830dc0f8068df36226dc822e7fc1f5f73be46", "type": "zip", "shasum": "", "reference": "f76830dc0f8068df36226dc822e7fc1f5f73be46"}, "time": "2015-11-26T07:00:59+00:00"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b72a761c1525f20c01cf007d6fb5f3a11291c117"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b72a761c1525f20c01cf007d6fb5f3a11291c117", "type": "zip", "shasum": "", "reference": "b72a761c1525f20c01cf007d6fb5f3a11291c117"}, "time": "2015-11-04T16:00:43+00:00"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "33bd5882f201f9a3b7dd9640b95710b71304c4fb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/33bd5882f201f9a3b7dd9640b95710b71304c4fb", "type": "zip", "shasum": "", "reference": "33bd5882f201f9a3b7dd9640b95710b71304c4fb"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.51"}, "time": "2018-02-28T09:36:59+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require-dev": {"symfony/config": "~2.7", "symfony/http-foundation": "~2.3", "symfony/yaml": "^2.0.5", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/routing/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "2.7.48.0"}, {"version": "v2.7.47", "version_normalized": "2.7.47.0"}, {"version": "v2.7.46", "version_normalized": "********"}, {"version": "v2.7.45", "version_normalized": "********"}, {"version": "v2.7.44", "version_normalized": "********"}, {"version": "v2.7.43", "version_normalized": "********"}, {"version": "v2.7.42", "version_normalized": "********"}, {"version": "v2.7.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f9e62983a64343f9845ab4a15656cf1e1719809a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f9e62983a64343f9845ab4a15656cf1e1719809a", "type": "zip", "shasum": "", "reference": "f9e62983a64343f9845ab4a15656cf1e1719809a"}, "time": "2018-01-16T16:51:05+00:00"}, {"version": "v2.7.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "21d76302177151ed8463166663da4d45e63a5612"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/21d76302177151ed8463166663da4d45e63a5612", "type": "zip", "shasum": "", "reference": "21d76302177151ed8463166663da4d45e63a5612"}, "time": "2018-01-03T18:00:19+00:00"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "52739044d32e390fc233e38ad504c590f454d067"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/52739044d32e390fc233e38ad504c590f454d067", "type": "zip", "shasum": "", "reference": "52739044d32e390fc233e38ad504c590f454d067"}, "time": "2017-11-19T18:49:57+00:00"}, {"version": "v2.7.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ed743c1e355515fd77e922f1150a8d41101cec95"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ed743c1e355515fd77e922f1150a8d41101cec95", "type": "zip", "shasum": "", "reference": "ed743c1e355515fd77e922f1150a8d41101cec95"}, "time": "2017-11-07T14:04:08+00:00"}, {"version": "v2.7.37", "version_normalized": "********"}, {"version": "v2.7.36", "version_normalized": "********"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0bf7ca0dda1ad9c3be15423661edb90abe781ce4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0bf7ca0dda1ad9c3be15423661edb90abe781ce4", "type": "zip", "shasum": "", "reference": "0bf7ca0dda1ad9c3be15423661edb90abe781ce4"}, "time": "2017-09-30T14:00:25+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ebb0b3eb0be5a9f418e12e9234d1644f65d4c791"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ebb0b3eb0be5a9f418e12e9234d1644f65d4c791", "type": "zip", "shasum": "", "reference": "ebb0b3eb0be5a9f418e12e9234d1644f65d4c791"}, "time": "2017-06-19T14:02:36+00:00"}, {"version": "v2.7.33", "version_normalized": "********"}, {"version": "v2.7.32", "version_normalized": "********"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6de57ec3161f62ed497b10eaf3954bc7a1443447"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6de57ec3161f62ed497b10eaf3954bc7a1443447", "type": "zip", "shasum": "", "reference": "6de57ec3161f62ed497b10eaf3954bc7a1443447"}, "time": "2017-06-01T20:44:56+00:00"}, {"version": "v2.7.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "04e8fa0e37e96be8b373c98c24d200ff039b07a0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/04e8fa0e37e96be8b373c98c24d200ff039b07a0", "type": "zip", "shasum": "", "reference": "04e8fa0e37e96be8b373c98c24d200ff039b07a0"}, "time": "2017-04-12T07:39:27+00:00"}, {"version": "v2.7.27", "version_normalized": "********"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9a339bb5e7d2e41fd88d49e24a37b1265b1aa1f0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9a339bb5e7d2e41fd88d49e24a37b1265b1aa1f0", "type": "zip", "shasum": "", "reference": "9a339bb5e7d2e41fd88d49e24a37b1265b1aa1f0"}, "time": "2017-03-02T14:51:26+00:00"}, {"version": "v2.7.25", "version_normalized": "********"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0333f0c6ab51285b0054dda170f1153b7b067ff0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0333f0c6ab51285b0054dda170f1153b7b067ff0", "type": "zip", "shasum": "", "reference": "0333f0c6ab51285b0054dda170f1153b7b067ff0"}, "time": "2017-01-23T20:38:04+00:00", "require-dev": {"symfony/config": "~2.7", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.7.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4b9b87788956961b3f9fa5e5de4ddee825379637"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4b9b87788956961b3f9fa5e5de4ddee825379637", "type": "zip", "shasum": "", "reference": "4b9b87788956961b3f9fa5e5de4ddee825379637"}, "time": "2017-01-02T20:30:00+00:00"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "aa5c9a9005560937bf6c504124616ad5b823732b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/aa5c9a9005560937bf6c504124616ad5b823732b", "type": "zip", "shasum": "", "reference": "aa5c9a9005560937bf6c504124616ad5b823732b"}, "time": "2016-11-25T12:07:11+00:00"}, {"version": "v2.7.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c4509a70fdb18d63decd3c24c44734703ed5c7eb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c4509a70fdb18d63decd3c24c44734703ed5c7eb", "type": "zip", "shasum": "", "reference": "c4509a70fdb18d63decd3c24c44734703ed5c7eb"}, "time": "2016-08-16T10:55:04+00:00"}, {"version": "v2.7.20", "version_normalized": "********"}, {"version": "v2.7.19", "version_normalized": "********"}, {"version": "v2.7.18", "version_normalized": "********"}, {"version": "v2.7.17", "version_normalized": "********"}, {"version": "v2.7.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8b64559584e9b580a777b8706863b8815f891bc9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8b64559584e9b580a777b8706863b8815f891bc9", "type": "zip", "shasum": "", "reference": "8b64559584e9b580a777b8706863b8815f891bc9"}, "time": "2016-06-28T06:24:06+00:00"}, {"version": "v2.7.15", "version_normalized": "********"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ad60d48d322ad87a1dcce40b38a611b0f6709909"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ad60d48d322ad87a1dcce40b38a611b0f6709909", "type": "zip", "shasum": "", "reference": "ad60d48d322ad87a1dcce40b38a611b0f6709909"}, "time": "2016-05-30T06:56:59+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "07c44ac73b42f7c6123fc024ccab03a36e87a2cc"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/07c44ac73b42f7c6123fc024ccab03a36e87a2cc", "type": "zip", "shasum": "", "reference": "07c44ac73b42f7c6123fc024ccab03a36e87a2cc"}, "time": "2016-04-28T10:50:58+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f3fad0ea232dcce8cd20388db5a4f492f9a24ed5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f3fad0ea232dcce8cd20388db5a4f492f9a24ed5", "type": "zip", "shasum": "", "reference": "f3fad0ea232dcce8cd20388db5a4f492f9a24ed5"}, "time": "2016-03-18T18:55:39+00:00"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c63128f6dd5095351a87cd7c8801963001e22aff"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c63128f6dd5095351a87cd7c8801963001e22aff", "type": "zip", "shasum": "", "reference": "c63128f6dd5095351a87cd7c8801963001e22aff"}, "time": "2016-02-04T13:52:46+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6fec77993acfe19aecf60544b9c7d32f3d5b2506"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6fec77993acfe19aecf60544b9c7d32f3d5b2506", "type": "zip", "shasum": "", "reference": "6fec77993acfe19aecf60544b9c7d32f3d5b2506"}, "time": "2016-01-03T15:32:00+00:00", "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/yaml": "For using the YAML loader"}}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "2c100bd94be50e2a1fce7fe1ac534e28776c24ff"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/2c100bd94be50e2a1fce7fe1ac534e28776c24ff", "type": "zip", "shasum": "", "reference": "2c100bd94be50e2a1fce7fe1ac534e28776c24ff"}, "time": "2015-12-23T06:54:35+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7450f6196711b124fb8b04a12286d01a0401ddfe"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7450f6196711b124fb8b04a12286d01a0401ddfe", "type": "zip", "shasum": "", "reference": "7450f6196711b124fb8b04a12286d01a0401ddfe"}, "time": "2015-11-18T13:41:01+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f353e1f588679c3ec987624e6c617646bd01ba38"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f353e1f588679c3ec987624e6c617646bd01ba38", "type": "zip", "shasum": "", "reference": "f353e1f588679c3ec987624e6c617646bd01ba38"}, "time": "2015-10-27T15:38:06+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6c5fae83efa20baf166fcf4582f57094e9f60f16"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6c5fae83efa20baf166fcf4582f57094e9f60f16", "type": "zip", "shasum": "", "reference": "6c5fae83efa20baf166fcf4582f57094e9f60f16"}, "time": "2015-09-14T14:14:09+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.7", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "20b1378cb6efffb77ea0608232f18c8f0dd25109"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/20b1378cb6efffb77ea0608232f18c8f0dd25109", "type": "zip", "shasum": "", "reference": "20b1378cb6efffb77ea0608232f18c8f0dd25109"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.4"}, "time": "2015-08-24T07:13:45+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ea9134f277162b02e5f80ac058b75a77637b0d26"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ea9134f277162b02e5f80ac058b75a77637b0d26", "type": "zip", "shasum": "", "reference": "ea9134f277162b02e5f80ac058b75a77637b0d26"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.3"}, "time": "2015-07-09T16:07:40+00:00"}, {"version": "v2.7.2", "version_normalized": "*******"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5581be29185b8fb802398904555f70da62f6d50d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5581be29185b8fb802398904555f70da62f6d50d", "type": "zip", "shasum": "", "reference": "5581be29185b8fb802398904555f70da62f6d50d"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.1"}, "time": "2015-06-11T17:20:40+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6f0333fb8b89cf6f8fd9d6740c5e83b73d9c95b9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6f0333fb8b89cf6f8fd9d6740c5e83b73d9c95b9", "type": "zip", "shasum": "", "reference": "6f0333fb8b89cf6f8fd9d6740c5e83b73d9c95b9"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.0"}, "time": "2015-05-19T06:58:17+00:00"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "15bf04e8b754ee90e31f47d4c9b2d15946ed53b2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/15bf04e8b754ee90e31f47d4c9b2d15946ed53b2", "type": "zip", "shasum": "", "reference": "15bf04e8b754ee90e31f47d4c9b2d15946ed53b2"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.0-BETA2"}, "time": "2015-05-11T17:33:08+00:00"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7f0622bda4c8520b543bf794fdbe61e01d22ee98"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7f0622bda4c8520b543bf794fdbe61e01d22ee98", "type": "zip", "shasum": "", "reference": "7f0622bda4c8520b543bf794fdbe61e01d22ee98"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.7.0-BETA1"}, "time": "2015-04-10T07:23:38+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Routing\\": ""}}, "target-dir": "Symfony/Component/Routing"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0a1764d41bbb54f3864808c50569ac382b44d128"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0a1764d41bbb54f3864808c50569ac382b44d128", "type": "zip", "shasum": "", "reference": "0a1764d41bbb54f3864808c50569ac382b44d128"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.11"}, "time": "2015-07-09T16:02:48+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "conflict": "__unset"}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********"}, {"version": "v2.6.10", "version_normalized": "********"}, {"version": "v2.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "dc9df18a1cfe87de65e270e8f01407ca6d7c39cb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/dc9df18a1cfe87de65e270e8f01407ca6d7c39cb", "type": "zip", "shasum": "", "reference": "dc9df18a1cfe87de65e270e8f01407ca6d7c39cb"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.9"}, "time": "2015-05-15T13:32:45+00:00"}, {"version": "v2.6.8", "version_normalized": "*******"}, {"version": "v2.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1455ec537940f7428ea6aa9411f3c4bca69413a0"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1455ec537940f7428ea6aa9411f3c4bca69413a0", "type": "zip", "shasum": "", "reference": "1455ec537940f7428ea6aa9411f3c4bca69413a0"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.7"}, "time": "2015-05-02T15:18:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4e173a645b63ff60a124f3741b4f15feebd908fa"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4e173a645b63ff60a124f3741b4f15feebd908fa", "type": "zip", "shasum": "", "reference": "4e173a645b63ff60a124f3741b4f15feebd908fa"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.6"}, "time": "2015-03-30T15:54:10+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a7f3eb540e5c553c3c95993c6fc2e7edb2f3b9d2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a7f3eb540e5c553c3c95993c6fc2e7edb2f3b9d2", "type": "zip", "shasum": "", "reference": "a7f3eb540e5c553c3c95993c6fc2e7edb2f3b9d2"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.5"}, "time": "2015-03-13T17:37:22+00:00"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "bda1c3c67f2a33bbeabb1d321feaf626a0ca5698"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/bda1c3c67f2a33bbeabb1d321feaf626a0ca5698", "type": "zip", "shasum": "", "reference": "bda1c3c67f2a33bbeabb1d321feaf626a0ca5698"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.4"}, "time": "2015-01-15T12:15:12+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "28382c6806780ddc657c136a5ca4415dd3252f41"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/28382c6806780ddc657c136a5ca4415dd3252f41", "type": "zip", "shasum": "", "reference": "28382c6806780ddc657c136a5ca4415dd3252f41"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.3"}, "time": "2015-01-05T14:28:40+00:00"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b50c10839e1639fb3a89710f9510b63cc8be54c5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b50c10839e1639fb3a89710f9510b63cc8be54c5", "type": "zip", "shasum": "", "reference": "b50c10839e1639fb3a89710f9510b63cc8be54c5"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.1"}, "time": "2014-12-02T20:19:20+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5e2c6aba6cbb00a6b7e3760db99f72c85c52b6e5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5e2c6aba6cbb00a6b7e3760db99f72c85c52b6e5", "type": "zip", "shasum": "", "reference": "5e2c6aba6cbb00a6b7e3760db99f72c85c52b6e5"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.0-BETA2"}, "time": "2014-11-16T17:28:09+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f0bb6f818f9a7ece41c7dfe14e08b13c2de55b0c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f0bb6f818f9a7ece41c7dfe14e08b13c2de55b0c", "type": "zip", "shasum": "", "reference": "f0bb6f818f9a7ece41c7dfe14e08b13c2de55b0c"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.6.0-BETA1"}, "time": "2014-11-03T19:16:49+00:00"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "46142c34ea830f47429df6e15faec3a33292d618"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/46142c34ea830f47429df6e15faec3a33292d618", "type": "zip", "shasum": "", "reference": "46142c34ea830f47429df6e15faec3a33292d618"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.12"}, "time": "2015-02-08T07:07:45+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.5.11", "version_normalized": "********"}, {"version": "v2.5.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3a9c35ca312d8cb6a308b751f9a1cd92cd5d9277"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3a9c35ca312d8cb6a308b751f9a1cd92cd5d9277", "type": "zip", "shasum": "", "reference": "3a9c35ca312d8cb6a308b751f9a1cd92cd5d9277"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.10"}, "time": "2015-01-13T14:58:43+00:00"}, {"version": "v2.5.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "47e350dadadabdf64c8dbab499a1132c567f9411"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/47e350dadadabdf64c8dbab499a1132c567f9411", "type": "zip", "shasum": "", "reference": "47e350dadadabdf64c8dbab499a1132c567f9411"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.9"}, "time": "2015-01-05T08:51:41+00:00"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9b7d9f6121e45243cc18e4ed682d8faa418d04bc"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9b7d9f6121e45243cc18e4ed682d8faa418d04bc", "type": "zip", "shasum": "", "reference": "9b7d9f6121e45243cc18e4ed682d8faa418d04bc"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.8"}, "time": "2014-12-02T20:15:53+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f3f5f31d5e227c51eed7ba8b1ed809a97ebaa341"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f3f5f31d5e227c51eed7ba8b1ed809a97ebaa341", "type": "zip", "shasum": "", "reference": "f3f5f31d5e227c51eed7ba8b1ed809a97ebaa341"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.7"}, "time": "2014-11-03T20:24:10+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "c7e381f571aac1de9e06f2bac2097984c543096a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/c7e381f571aac1de9e06f2bac2097984c543096a", "type": "zip", "shasum": "", "reference": "c7e381f571aac1de9e06f2bac2097984c543096a"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.6"}, "time": "2014-10-24T05:49:22+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9bc38fe72e0eff61611e7cd4df3accbce20b1d36"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9bc38fe72e0eff61611e7cd4df3accbce20b1d36", "type": "zip", "shasum": "", "reference": "9bc38fe72e0eff61611e7cd4df3accbce20b1d36"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.5"}, "time": "2014-09-22T15:28:36+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "de58587ae3fb17fdc802893cf934bbb03e6b8eb5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/de58587ae3fb17fdc802893cf934bbb03e6b8eb5", "type": "zip", "shasum": "", "reference": "de58587ae3fb17fdc802893cf934bbb03e6b8eb5"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.4"}, "time": "2014-08-26T14:16:33+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/yaml": "~2.0", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1c285e6fffaa026c8073a387f403b1052d61ed95"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1c285e6fffaa026c8073a387f403b1052d61ed95", "type": "zip", "shasum": "", "reference": "1c285e6fffaa026c8073a387f403b1052d61ed95"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.3"}, "time": "2014-07-28T13:20:46+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "24d5f003a794894513d67c888181ba8c3b8e0693"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/24d5f003a794894513d67c888181ba8c3b8e0693", "type": "zip", "shasum": "", "reference": "24d5f003a794894513d67c888181ba8c3b8e0693"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.2"}, "time": "2014-07-09T09:05:48+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5afcfa7344a8048fa1b09e51572fd48805d79a0e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5afcfa7344a8048fa1b09e51572fd48805d79a0e", "type": "zip", "shasum": "", "reference": "5afcfa7344a8048fa1b09e51572fd48805d79a0e"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.1"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "99b4e8e2978e6a08c086fd771cc0aa66c9e80a56"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/99b4e8e2978e6a08c086fd771cc0aa66c9e80a56", "type": "zip", "shasum": "", "reference": "99b4e8e2978e6a08c086fd771cc0aa66c9e80a56"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.0-RC1"}, "time": "2014-04-23T14:08:54+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "dd45a1af38d20ebe9b6d4015885d861a4577bc29"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/dd45a1af38d20ebe9b6d4015885d861a4577bc29", "type": "zip", "shasum": "", "reference": "dd45a1af38d20ebe9b6d4015885d861a4577bc29"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.5.0-BETA1"}, "time": "2014-04-03T05:23:50+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0bd97a27cfd1ad2699ad045de2a744888318a228"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0bd97a27cfd1ad2699ad045de2a744888318a228", "type": "zip", "shasum": "", "reference": "0bd97a27cfd1ad2699ad045de2a744888318a228"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.10"}, "time": "2014-09-22T15:28:09+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f72741bcca494b7d3073e266548cf72d014e1413"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f72741bcca494b7d3073e266548cf72d014e1413", "type": "zip", "shasum": "", "reference": "f72741bcca494b7d3073e266548cf72d014e1413"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.9"}, "time": "2014-08-26T14:14:58+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/yaml": "~2.0", "symfony/expression-language": "~2.4", "doctrine/annotations": "~1.0", "psr/log": "~1.0"}}, {"version": "v2.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d290f15b7083cee926e17299ab6889210058b30e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d290f15b7083cee926e17299ab6889210058b30e", "type": "zip", "shasum": "", "reference": "d290f15b7083cee926e17299ab6889210058b30e"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.8"}, "time": "2014-07-09T09:04:55+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "378ac634c6089571b5e75d776fe61baa0579b075"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/378ac634c6089571b5e75d776fe61baa0579b075", "type": "zip", "shasum": "", "reference": "378ac634c6089571b5e75d776fe61baa0579b075"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.7"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "74229f66e16bce6d2415ca44d4756f8e7ea880f8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/74229f66e16bce6d2415ca44d4756f8e7ea880f8", "type": "zip", "shasum": "", "reference": "74229f66e16bce6d2415ca44d4756f8e7ea880f8"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.6"}, "time": "2014-04-23T14:04:21+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b2fdea8b60400bb84e4931d71101ebbb3a08c1eb"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b2fdea8b60400bb84e4931d71101ebbb3a08c1eb", "type": "zip", "shasum": "", "reference": "b2fdea8b60400bb84e4931d71101ebbb3a08c1eb"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.3"}, "time": "2014-02-11T13:52:09+00:00"}, {"version": "v2.4.2", "version_normalized": "*******"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4abfb500aab8be458c9e3a227ea56b190584f78a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4abfb500aab8be458c9e3a227ea56b190584f78a", "type": "zip", "shasum": "", "reference": "4abfb500aab8be458c9e3a227ea56b190584f78a"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.1"}, "time": "2014-01-05T02:10:50+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a7a665e2a0e3a203ca9a690c021066e76295bf6a"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a7a665e2a0e3a203ca9a690c021066e76295bf6a", "type": "zip", "shasum": "", "reference": "a7a665e2a0e3a203ca9a690c021066e76295bf6a"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.0-RC1"}, "time": "2013-11-22T17:42:00+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1"}, {"keywords": [], "version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5b59329198eb40039d9dbfd85aefefc35f442ea2"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5b59329198eb40039d9dbfd85aefefc35f442ea2", "type": "zip", "shasum": "", "reference": "5b59329198eb40039d9dbfd85aefefc35f442ea2"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.0-BETA2"}, "time": "2013-10-16T14:40:12+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/yaml": "~2.0", "symfony/expression-language": "~2.4", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "suggest": {"doctrine/common": "", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "1065935ca7ad083d562a58e31a87a88c706db59f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/1065935ca7ad083d562a58e31a87a88c706db59f", "type": "zip", "shasum": "", "reference": "1065935ca7ad083d562a58e31a87a88c706db59f"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.4.0-BETA1"}, "time": "2013-09-29T19:54:28+00:00"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5b8a2bb7569df81401171829498809e90d6e446c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5b8a2bb7569df81401171829498809e90d6e446c", "type": "zip", "shasum": "", "reference": "5b8a2bb7569df81401171829498809e90d6e446c"}, "support": {"source": "https://github.com/symfony/routing/tree/2.3"}, "time": "2016-05-29T10:13:06+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.3.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5a34ea3a76581e80f3bf2f76d933c0086759d149"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5a34ea3a76581e80f3bf2f76d933c0086759d149", "type": "zip", "shasum": "", "reference": "5a34ea3a76581e80f3bf2f76d933c0086759d149"}, "time": "2016-03-23T17:41:29+00:00"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9f464f27cb127232f5c32d5a736fe769e5249442"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9f464f27cb127232f5c32d5a736fe769e5249442", "type": "zip", "shasum": "", "reference": "9f464f27cb127232f5c32d5a736fe769e5249442"}, "time": "2016-03-04T07:12:06+00:00"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "32ada20e2a1beac1eb244e9b27017649e7179a57"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/32ada20e2a1beac1eb244e9b27017649e7179a57", "type": "zip", "shasum": "", "reference": "32ada20e2a1beac1eb244e9b27017649e7179a57"}, "time": "2016-02-03T18:52:07+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7b172205e307497230b3285680e19c3902a6b7c8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7b172205e307497230b3285680e19c3902a6b7c8", "type": "zip", "shasum": "", "reference": "7b172205e307497230b3285680e19c3902a6b7c8"}, "time": "2016-01-02T02:53:47+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "104c77ad83b4551d9d184660d0dd1ad8226b447c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/104c77ad83b4551d9d184660d0dd1ad8226b447c", "type": "zip", "shasum": "", "reference": "104c77ad83b4551d9d184660d0dd1ad8226b447c"}, "time": "2015-12-22T13:55:19+00:00"}, {"version": "v2.3.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "08a4065c47b45a1785101aabb29082fe415cec6c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/08a4065c47b45a1785101aabb29082fe415cec6c", "type": "zip", "shasum": "", "reference": "08a4065c47b45a1785101aabb29082fe415cec6c"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f241daca4c032793aaaef7dae75cf9e115d9b01e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f241daca4c032793aaaef7dae75cf9e115d9b01e", "type": "zip", "shasum": "", "reference": "f241daca4c032793aaaef7dae75cf9e115d9b01e"}, "time": "2015-10-24T12:07:54+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Routing\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "9ed0492ba2dbfc0d3c329ac742c08d849a0f000c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/9ed0492ba2dbfc0d3c329ac742c08d849a0f000c", "type": "zip", "shasum": "", "reference": "9ed0492ba2dbfc0d3c329ac742c08d849a0f000c"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.33"}, "time": "2015-09-11T07:57:18+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fbadda8d2a35bdec8187bd54f698b8b704649721"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fbadda8d2a35bdec8187bd54f698b8b704649721", "type": "zip", "shasum": "", "reference": "fbadda8d2a35bdec8187bd54f698b8b704649721"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.32"}, "time": "2015-08-01T19:33:42+00:00"}, {"version": "v2.3.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4ced3b75d7629c90527279e37d3fb572c35ecb99"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4ced3b75d7629c90527279e37d3fb572c35ecb99", "type": "zip", "shasum": "", "reference": "4ced3b75d7629c90527279e37d3fb572c35ecb99"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.31"}, "time": "2015-07-05T14:01:47+00:00"}, {"version": "v2.3.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "00a6c79757f34aabbdf9afa2094df1b28639a3c9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/00a6c79757f34aabbdf9afa2094df1b28639a3c9", "type": "zip", "shasum": "", "reference": "00a6c79757f34aabbdf9afa2094df1b28639a3c9"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.30"}, "time": "2015-05-15T13:28:34+00:00"}, {"version": "v2.3.29", "version_normalized": "********"}, {"version": "v2.3.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "d4181ab9f26e3bb7430f49aa2956e8ea6568630f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/d4181ab9f26e3bb7430f49aa2956e8ea6568630f", "type": "zip", "shasum": "", "reference": "d4181ab9f26e3bb7430f49aa2956e8ea6568630f"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.28"}, "time": "2015-05-01T14:06:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a6f4dc55fe3d2ba3a0efa145c3efb373992e92e7"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a6f4dc55fe3d2ba3a0efa145c3efb373992e92e7", "type": "zip", "shasum": "", "reference": "a6f4dc55fe3d2ba3a0efa145c3efb373992e92e7"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.27"}, "time": "2015-03-27T22:05:05+00:00"}, {"version": "v2.3.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "db8985eb39e1169e17f4b857cb6f060fca2be28b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/db8985eb39e1169e17f4b857cb6f060fca2be28b", "type": "zip", "shasum": "", "reference": "db8985eb39e1169e17f4b857cb6f060fca2be28b"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.26"}, "time": "2015-03-13T17:09:38+00:00"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0408b663166195fbd2a6bb6cc14b71da9716231d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0408b663166195fbd2a6bb6cc14b71da9716231d", "type": "zip", "shasum": "", "reference": "0408b663166195fbd2a6bb6cc14b71da9716231d"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.25"}, "time": "2015-01-03T14:49:25+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0,>=2.0.5", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.3.24", "version_normalized": "********"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7aa060d6fc47af0417628b0c39116a8f2599aba1"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7aa060d6fc47af0417628b0c39116a8f2599aba1", "type": "zip", "shasum": "", "reference": "7aa060d6fc47af0417628b0c39116a8f2599aba1"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.23"}, "time": "2014-12-02T19:42:47+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/http-foundation": "~2.3", "symfony/yaml": "~2.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "be0828af76ecc686634a9b1405c8554baf133e7d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/be0828af76ecc686634a9b1405c8554baf133e7d", "type": "zip", "shasum": "", "reference": "be0828af76ecc686634a9b1405c8554baf133e7d"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.22"}, "time": "2014-10-27T14:27:32+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f7f8ebf9c99e5ebfdb908c3558a818c2883eab1f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f7f8ebf9c99e5ebfdb908c3558a818c2883eab1f", "type": "zip", "shasum": "", "reference": "f7f8ebf9c99e5ebfdb908c3558a818c2883eab1f"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.21"}, "time": "2014-10-13T12:38:27+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "ed46950560039d7bac3b8829075a15735fe2f368"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/ed46950560039d7bac3b8829075a15735fe2f368", "type": "zip", "shasum": "", "reference": "ed46950560039d7bac3b8829075a15735fe2f368"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.20"}, "time": "2014-09-22T15:12:11+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "aa2beef5d5f1d84250b5ca73a4056c0504e5969e"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/aa2beef5d5f1d84250b5ca73a4056c0504e5969e", "type": "zip", "shasum": "", "reference": "aa2beef5d5f1d84250b5ca73a4056c0504e5969e"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.19"}, "time": "2014-08-15T09:05:55+00:00", "require-dev": {"symfony/config": "~2.2", "symfony/yaml": "~2.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "0ee3c12de95c76f50a71613f329ae0f568ca13c6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/0ee3c12de95c76f50a71613f329ae0f568ca13c6", "type": "zip", "shasum": "", "reference": "0ee3c12de95c76f50a71613f329ae0f568ca13c6"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.18"}, "time": "2014-07-07T10:13:42+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f7334b35c74a28dabd897df66a3a9e776f352708"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f7334b35c74a28dabd897df66a3a9e776f352708", "type": "zip", "shasum": "", "reference": "f7334b35c74a28dabd897df66a3a9e776f352708"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6e4c9024a04340b83e456a1a24597dba066dcdc9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6e4c9024a04340b83e456a1a24597dba066dcdc9", "type": "zip", "shasum": "", "reference": "6e4c9024a04340b83e456a1a24597dba066dcdc9"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.16"}, "time": "2014-04-23T13:35:47+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "08afcafd9af22a24a8055669f85d63b863c4711b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/08afcafd9af22a24a8055669f85d63b863c4711b", "type": "zip", "shasum": "", "reference": "08afcafd9af22a24a8055669f85d63b863c4711b"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.12"}, "time": "2014-03-28T10:34:27+00:00"}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b7c5289d628936505417c9382000384992e3d856"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b7c5289d628936505417c9382000384992e3d856", "type": "zip", "shasum": "", "reference": "b7c5289d628936505417c9382000384992e3d856"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.11"}, "time": "2014-02-11T10:29:24+00:00"}, {"version": "v2.3.10", "version_normalized": "********"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f8e308cc2914fa9fcb99ba6a0e9a8768a5a6b30c"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f8e308cc2914fa9fcb99ba6a0e9a8768a5a6b30c", "type": "zip", "shasum": "", "reference": "f8e308cc2914fa9fcb99ba6a0e9a8768a5a6b30c"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.9"}, "time": "2014-01-02T12:27:24+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f3622da652fc6a73ce9d85dbee4ec7ef425c8349"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f3622da652fc6a73ce9d85dbee4ec7ef425c8349", "type": "zip", "shasum": "", "reference": "f3622da652fc6a73ce9d85dbee4ec7ef425c8349"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.8"}, "time": "2013-12-14T11:06:34+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "7d41463094752e87a0fae60316d236abecb8a034"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/7d41463094752e87a0fae60316d236abecb8a034", "type": "zip", "shasum": "", "reference": "7d41463094752e87a0fae60316d236abecb8a034"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.7"}, "time": "2013-09-29T19:41:41+00:00"}, {"version": "v2.3.6", "version_normalized": "*******"}, {"version": "v2.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6d1f7b101337594fe790c47166068583b60b6460"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6d1f7b101337594fe790c47166068583b60b6460", "type": "zip", "shasum": "", "reference": "6d1f7b101337594fe790c47166068583b60b6460"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.5"}, "time": "2013-09-19T09:45:20+00:00"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "69af3f07dbf3ae93dd513dbc373f561cb2e7f143"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/69af3f07dbf3ae93dd513dbc373f561cb2e7f143", "type": "zip", "shasum": "", "reference": "69af3f07dbf3ae93dd513dbc373f561cb2e7f143"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.4"}, "time": "2013-08-23T15:14:07+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "25eb4f846382877a3547d70dae9ea35439d81305"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/25eb4f846382877a3547d70dae9ea35439d81305", "type": "zip", "shasum": "", "reference": "25eb4f846382877a3547d70dae9ea35439d81305"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.3"}, "time": "2013-08-02T20:53:38+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3deda53b19ee9d361b03de5268bc91533eb76235"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3deda53b19ee9d361b03de5268bc91533eb76235", "type": "zip", "shasum": "", "reference": "3deda53b19ee9d361b03de5268bc91533eb76235"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.2"}, "time": "2013-06-23T08:16:02+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "255e1163b74a22cd83d3fd51d63c0588afe0bc56"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/255e1163b74a22cd83d3fd51d63c0588afe0bc56", "type": "zip", "shasum": "", "reference": "255e1163b74a22cd83d3fd51d63c0588afe0bc56"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.3.1"}, "time": "2013-05-20T08:57:26+00:00"}, {"version": "v2.3.0", "version_normalized": "*******"}, {"version": "v2.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8d9821794f7192d43b76737edb94a817949a5579"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8d9821794f7192d43b76737edb94a817949a5579", "type": "zip", "shasum": "", "reference": "8d9821794f7192d43b76737edb94a817949a5579"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.11"}, "time": "2013-09-26T19:07:06+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require-dev": {"symfony/config": ">=2.2,<2.3-dev", "symfony/yaml": "~2.0", "doctrine/common": "~2.2", "psr/log": "~1.0"}, "suggest": {"doctrine/common": "~2.2", "symfony/config": "2.2.*", "symfony/yaml": "2.2.*"}}, {"version": "v2.2.10", "version_normalized": "********"}, {"version": "v2.2.9", "version_normalized": "*******"}, {"version": "v2.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "412ee2c4c77c5b2353618f44de4e845c8f0db00f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/412ee2c4c77c5b2353618f44de4e845c8f0db00f", "type": "zip", "shasum": "", "reference": "412ee2c4c77c5b2353618f44de4e845c8f0db00f"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.8"}, "time": "2013-09-19T09:36:05+00:00"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "2704242137edc19cc61e71027a7a04eef28f42c6"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/2704242137edc19cc61e71027a7a04eef28f42c6", "type": "zip", "shasum": "", "reference": "2704242137edc19cc61e71027a7a04eef28f42c6"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.6"}, "time": "2013-08-23T14:06:02+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f2265e3500f121ac69409138eebd70f0467f5a30"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f2265e3500f121ac69409138eebd70f0467f5a30", "type": "zip", "shasum": "", "reference": "f2265e3500f121ac69409138eebd70f0467f5a30"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.5"}, "time": "2013-07-30T11:22:46+00:00"}, {"version": "v2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "844f21ea5370dcb58abd65dbc66b4849fb090c1d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/844f21ea5370dcb58abd65dbc66b4849fb090c1d", "type": "zip", "shasum": "", "reference": "844f21ea5370dcb58abd65dbc66b4849fb090c1d"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.4"}, "time": "2013-06-23T06:56:04+00:00"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "351917218674d07847cb51ba22f6934aaea745df"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/351917218674d07847cb51ba22f6934aaea745df", "type": "zip", "shasum": "", "reference": "351917218674d07847cb51ba22f6934aaea745df"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.3"}, "time": "2013-05-10T16:49:00+00:00"}, {"version": "v2.2.2", "version_normalized": "*******"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6fea997908607f18a970a3848ec89c8e775255f3"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6fea997908607f18a970a3848ec89c8e775255f3", "type": "zip", "shasum": "", "reference": "6fea997908607f18a970a3848ec89c8e775255f3"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.1"}, "time": "2013-03-23T12:03:22+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6b4baba22f70bfe870b29b149456e89d644d63f4"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6b4baba22f70bfe870b29b149456e89d644d63f4", "type": "zip", "shasum": "", "reference": "6b4baba22f70bfe870b29b149456e89d644d63f4"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.2.0"}, "time": "2013-02-11T11:24:47+00:00"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "6d3732663789b6e1bd79197f9bd0c582cb72da81"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/6d3732663789b6e1bd79197f9bd0c582cb72da81", "type": "zip", "shasum": "", "reference": "6d3732663789b6e1bd79197f9bd0c582cb72da81"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.13"}, "time": "2013-05-06T10:48:41+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Routing": ""}}, "require-dev": {"symfony/config": "2.1.*", "symfony/yaml": "2.1.*", "symfony/http-kernel": "2.1.*", "doctrine/common": "~2.2"}, "suggest": {"doctrine/common": "~2.2", "symfony/config": "2.1.*", "symfony/yaml": "2.1.*"}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********"}, {"version": "v2.1.10", "version_normalized": "********"}, {"version": "v2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3813ea3ed47d5ae4fc5cfa7aba84191713a45d1f"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3813ea3ed47d5ae4fc5cfa7aba84191713a45d1f", "type": "zip", "shasum": "", "reference": "3813ea3ed47d5ae4fc5cfa7aba84191713a45d1f"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.9"}, "time": "2013-03-23T07:47:35+00:00"}, {"version": "v2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "4c44279e55ec0943dd9e51a0a6afd8b9d35e9761"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/4c44279e55ec0943dd9e51a0a6afd8b9d35e9761", "type": "zip", "shasum": "", "reference": "4c44279e55ec0943dd9e51a0a6afd8b9d35e9761"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.8"}, "time": "2013-02-22T18:53:49+00:00"}, {"version": "v2.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "fd28ee5bb7a9dd817dbe18605c0c4c7d7f82cfa9"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/fd28ee5bb7a9dd817dbe18605c0c4c7d7f82cfa9", "type": "zip", "shasum": "", "reference": "fd28ee5bb7a9dd817dbe18605c0c4c7d7f82cfa9"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.7"}, "time": "2013-01-09T08:51:07+00:00", "require-dev": {"symfony/config": "2.1.*", "symfony/yaml": "2.1.*", "symfony/http-kernel": "2.1.*", "doctrine/common": ">=2.2,<2.4-dev"}, "suggest": {"doctrine/common": ">=2.2,<2.4-dev", "symfony/config": "2.1.*", "symfony/yaml": "2.1.*"}}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "af87ed34dccc2c11886743410075407b66108edf"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/af87ed34dccc2c11886743410075407b66108edf", "type": "zip", "shasum": "", "reference": "af87ed34dccc2c11886743410075407b66108edf"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.6"}, "time": "2012-12-11T10:40:22+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "3fbbb421ba70623da9be8f42f12458c03a162480"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/3fbbb421ba70623da9be8f42f12458c03a162480", "type": "zip", "shasum": "", "reference": "3fbbb421ba70623da9be8f42f12458c03a162480"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.4"}, "time": "2012-11-19T10:35:29+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b91de18aa18864d51904ba1ce6e247c42867a1f5"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b91de18aa18864d51904ba1ce6e247c42867a1f5", "type": "zip", "shasum": "", "reference": "b91de18aa18864d51904ba1ce6e247c42867a1f5"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.3"}, "time": "2012-10-26T09:26:42+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "5069cbd8bf02ed33cfd640b227bb43e816d55f14"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/5069cbd8bf02ed33cfd640b227bb43e816d55f14", "type": "zip", "shasum": "", "reference": "5069cbd8bf02ed33cfd640b227bb43e816d55f14"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.2"}, "time": "2012-09-10T10:53:42+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "79ea5628ea4395fe61ea711808a338cca1b222e8"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/79ea5628ea4395fe61ea711808a338cca1b222e8", "type": "zip", "shasum": "", "reference": "79ea5628ea4395fe61ea711808a338cca1b222e8"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.1.0"}, "time": "2012-09-05T15:44:14+00:00", "suggest": {"doctrine/common": ">=2.2,<2.4-dev", "symfony/config": "v2.1.0", "symfony/yaml": "v2.1.0"}}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "cfae6e9ec75c37a45e622eb40835290950c12d0d"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/cfae6e9ec75c37a45e622eb40835290950c12d0d", "type": "zip", "shasum": "", "reference": "cfae6e9ec75c37a45e622eb40835290950c12d0d"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.25"}, "time": "2013-01-04T16:59:43+00:00", "require": {"php": ">=5.3.2"}, "suggest": {"symfony/config": "v2.0.25", "symfony/yaml": "v2.0.25"}, "require-dev": "__unset", "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.24", "symfony/yaml": "v2.0.24"}}, {"version": "v2.0.23", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.23", "symfony/yaml": "v2.0.23"}}, {"version": "v2.0.22", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.22", "symfony/yaml": "v2.0.22"}}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "17e798c2fcd9993eb82698042cfb24d24fb38338"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/17e798c2fcd9993eb82698042cfb24d24fb38338", "type": "zip", "shasum": "", "reference": "17e798c2fcd9993eb82698042cfb24d24fb38338"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.21"}, "time": "2012-12-03T12:35:11+00:00", "suggest": {"symfony/config": "v2.0.21", "symfony/yaml": "v2.0.21"}}, {"version": "v2.0.20", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.20", "symfony/yaml": "v2.0.20"}}, {"version": "v2.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "769e8e6584c5d13e7c6f51aa77a9d5f977fec463"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/769e8e6584c5d13e7c6f51aa77a9d5f977fec463", "type": "zip", "shasum": "", "reference": "769e8e6584c5d13e7c6f51aa77a9d5f977fec463"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.19"}, "time": "2012-08-28T06:43:14+00:00", "suggest": {"symfony/config": "v2.0.19", "symfony/yaml": "v2.0.19"}}, {"version": "v2.0.18", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.18", "symfony/yaml": "v2.0.18"}}, {"version": "v2.0.17", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.17", "symfony/yaml": "v2.0.17"}}, {"version": "v2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "95d29ed05b8a1842556c55c0d433bf729f68637b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/95d29ed05b8a1842556c55c0d433bf729f68637b", "type": "zip", "shasum": "", "reference": "95d29ed05b8a1842556c55c0d433bf729f68637b"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.16"}, "time": "2012-07-09T12:43:50+00:00", "suggest": {"symfony/config": "v2.0.16", "symfony/yaml": "v2.0.16"}}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "8271955c872e02ebd2280f08447a8f35deb40b96"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/8271955c872e02ebd2280f08447a8f35deb40b96", "type": "zip", "shasum": "", "reference": "8271955c872e02ebd2280f08447a8f35deb40b96"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.15"}, "time": "2012-05-21T20:25:19+00:00", "suggest": {"symfony/config": "v2.0.15", "symfony/yaml": "v2.0.15"}}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a375499220b2b1729e90c0a8a1b91bb4f5368933"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a375499220b2b1729e90c0a8a1b91bb4f5368933", "type": "zip", "shasum": "", "reference": "a375499220b2b1729e90c0a8a1b91bb4f5368933"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.14"}, "time": "2012-05-15T16:56:32+00:00", "suggest": {"symfony/config": "v2.0.14", "symfony/yaml": "v2.0.14"}}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "b6875f5dd6b6334dad781b6a0b724b2033b77b84"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/b6875f5dd6b6334dad781b6a0b724b2033b77b84", "type": "zip", "shasum": "", "reference": "b6875f5dd6b6334dad781b6a0b724b2033b77b84"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.13"}, "time": "2012-04-23T07:55:54+00:00", "suggest": {"symfony/config": "v2.0.13", "symfony/yaml": "v2.0.13"}}, {"version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "f4772b55c427fe869d699264597a55c01a9c0973"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/f4772b55c427fe869d699264597a55c01a9c0973", "type": "zip", "shasum": "", "reference": "f4772b55c427fe869d699264597a55c01a9c0973"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.12"}, "time": "2012-02-22T09:10:37+00:00", "suggest": {"symfony/config": "v2.0.12", "symfony/yaml": "v2.0.12"}}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "75ccb5566c3f8af4edb165ec95d9be8dffe3a426"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/75ccb5566c3f8af4edb165ec95d9be8dffe3a426", "type": "zip", "shasum": "", "reference": "75ccb5566c3f8af4edb165ec95d9be8dffe3a426"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.10"}, "time": "2012-01-11T14:46:50+00:00", "suggest": {"symfony/config": "v2.0.10", "symfony/yaml": "v2.0.10"}}, {"version": "v2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "bb334b1d0a84d9e5e22bcc7b98f6d6c5e8833cae"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/bb334b1d0a84d9e5e22bcc7b98f6d6c5e8833cae", "type": "zip", "shasum": "", "reference": "bb334b1d0a84d9e5e22bcc7b98f6d6c5e8833cae"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.9"}, "time": "2012-01-05T13:51:20+00:00", "suggest": {"symfony/config": "v2.0.9", "symfony/yaml": "v2.0.9"}}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "a16d5a86342d034918fbd4c12e2ccf05310b9c53"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/a16d5a86342d034918fbd4c12e2ccf05310b9c53", "type": "zip", "shasum": "", "reference": "a16d5a86342d034918fbd4c12e2ccf05310b9c53"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.7"}, "time": "2011-11-24T06:16:14+00:00", "suggest": {"symfony/config": "2.0.7", "symfony/yaml": "2.0.7"}}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "e6bd267cb9af6ea4c2e5ccd33b3f0922d9fab059"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/e6bd267cb9af6ea4c2e5ccd33b3f0922d9fab059", "type": "zip", "shasum": "", "reference": "e6bd267cb9af6ea4c2e5ccd33b3f0922d9fab059"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.6"}, "time": "2011-11-02T13:18:45+00:00", "suggest": {"symfony/config": ">=2.0", "symfony/yaml": ">=2.0"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "76c305cab0dd459132f449b678d32591dde0aa26"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/76c305cab0dd459132f449b678d32591dde0aa26", "type": "zip", "shasum": "", "reference": "76c305cab0dd459132f449b678d32591dde0aa26"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/routing.git", "type": "git", "reference": "167153c08e6b4e44ac8789d59352fcb55633267b"}, "dist": {"url": "https://api.github.com/repos/symfony/routing/zipball/167153c08e6b4e44ac8789d59352fcb55633267b", "type": "zip", "shasum": "", "reference": "167153c08e6b4e44ac8789d59352fcb55633267b"}, "support": {"source": "https://github.com/symfony/routing/tree/v2.0.4"}, "time": "2011-09-30T10:01:46+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [{"advisoryId": "PKSA-g1jq-pppn-wyhk", "affectedVersions": ">=2.0.0,<2.0.17"}, {"advisoryId": "PKSA-p71j-52x5-sfs1", "affectedVersions": ">=2.0.0,<2.0.19"}], "last-modified": "Thu, 29 May 2025 07:50:49 GMT"}