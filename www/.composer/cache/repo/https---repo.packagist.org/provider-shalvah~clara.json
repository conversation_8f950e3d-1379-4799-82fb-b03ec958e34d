{"minified": "composer/2.0", "packages": {"shalvah/clara": [{"name": "shalvah/clara", "description": "🔊 Simple, pretty, testable console output for CLI apps.", "keywords": ["log", "logging", "cli"], "homepage": "", "version": "3.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "cdbb5737cbdd101756d97dd2279a979a1af7710b"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/cdbb5737cbdd101756d97dd2279a979a1af7710b", "type": "zip", "shasum": "", "reference": "cdbb5737cbdd101756d97dd2279a979a1af7710b"}, "type": "library", "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/3.2.0"}, "funding": [], "time": "2024-02-27T20:30:59+00:00", "autoload": {"files": ["helpers.php"], "psr-4": {"Shalvah\\Clara\\": "src/"}}, "require": {"php": ">=7.4", "symfony/console": "^4.0|^5.0|^6.0|^7.0"}, "require-dev": {"phpunit/phpunit": "^9.1", "eloquent/phony-phpunit": "^7.0"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "d3ae9b277393d438edbfcf9ddb7ca42e958fa054"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/d3ae9b277393d438edbfcf9ddb7ca42e958fa054", "type": "zip", "shasum": "", "reference": "d3ae9b277393d438edbfcf9ddb7ca42e958fa054"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/3.1.0"}, "time": "2022-01-14T13:54:30+00:00", "require": {"php": ">=7.4", "symfony/console": "^4.0|^5.0|^6.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "9b4e5b2681fb79e7214f66106d11917d9c3faa40"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/9b4e5b2681fb79e7214f66106d11917d9c3faa40", "type": "zip", "shasum": "", "reference": "9b4e5b2681fb79e7214f66106d11917d9c3faa40"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/3.0.2"}, "time": "2021-07-01T14:39:22+00:00", "require": {"php": ">=7.4", "symfony/console": "^4.0|^5.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "5e3d5218a773465c4eed14f173c2b4cb47fe84fc"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/5e3d5218a773465c4eed14f173c2b4cb47fe84fc", "type": "zip", "shasum": "", "reference": "5e3d5218a773465c4eed14f173c2b4cb47fe84fc"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/3.0.1"}, "time": "2021-06-30T12:34:41+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "ae8ad771988770df369e710f37310b3512372cd6"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/ae8ad771988770df369e710f37310b3512372cd6", "type": "zip", "shasum": "", "reference": "ae8ad771988770df369e710f37310b3512372cd6"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/3.0.0"}, "time": "2021-06-30T12:27:53+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "f1d8a36da149b605769ef86286110e435a68d9ac"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/f1d8a36da149b605769ef86286110e435a68d9ac", "type": "zip", "shasum": "", "reference": "f1d8a36da149b605769ef86286110e435a68d9ac"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/2.6.0"}, "time": "2020-04-12T11:08:11+00:00", "require": {"php": ">=7.2.5", "symfony/console": "^4.0|^5.0"}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "66f0115cc2a334cbdbfcb33508f72b4dad785787"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/66f0115cc2a334cbdbfcb33508f72b4dad785787", "type": "zip", "shasum": "", "reference": "66f0115cc2a334cbdbfcb33508f72b4dad785787"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/master"}, "time": "2020-04-10T19:00:13+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "6a702363e278798fbe9a345ade66f682bbf67140"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/6a702363e278798fbe9a345ade66f682bbf67140", "type": "zip", "shasum": "", "reference": "6a702363e278798fbe9a345ade66f682bbf67140"}, "time": "2020-04-10T18:53:18+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "09c076c10cec6aea692c74ec0bf22ef45ee4cbd6"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/09c076c10cec6aea692c74ec0bf22ef45ee4cbd6", "type": "zip", "shasum": "", "reference": "09c076c10cec6aea692c74ec0bf22ef45ee4cbd6"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/2.3.0"}, "time": "2020-04-10T13:46:03+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "7613e7b2c1d499486314193e6c097b268b6f31f9"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/7613e7b2c1d499486314193e6c097b268b6f31f9", "type": "zip", "shasum": "", "reference": "7613e7b2c1d499486314193e6c097b268b6f31f9"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/2.2.0"}, "time": "2020-04-10T03:19:31+00:00", "autoload": {"psr-4": {"Shalvah\\Clara\\": "src/"}}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "c4112de1fee3ed1017552011bdf1b41786083c48"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/c4112de1fee3ed1017552011bdf1b41786083c48", "type": "zip", "shasum": "", "reference": "c4112de1fee3ed1017552011bdf1b41786083c48"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/master"}, "time": "2020-04-10T02:21:05+00:00", "require": {"php": ">=7.2.5", "symfony/console": "^5.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "7c77409c79c0cde09096c1cd8c1e84171cdca084"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/7c77409c79c0cde09096c1cd8c1e84171cdca084", "type": "zip", "shasum": "", "reference": "7c77409c79c0cde09096c1cd8c1e84171cdca084"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/2.0.0"}, "time": "2020-04-10T01:43:12+00:00"}, {"description": "Simple console logging for CLI apps.", "version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "35aeae86b9f5b7fc8633d62522169f68e6519875"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/35aeae86b9f5b7fc8633d62522169f68e6519875", "type": "zip", "shasum": "", "reference": "35aeae86b9f5b7fc8633d62522169f68e6519875"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/1.1.0"}, "time": "2020-04-09T08:23:08+00:00", "autoload": {"psr-4": {"Shalvah\\Clara\\": "src"}}, "require-dev": {"phpunit/phpunit": "^8.5"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/clara.git", "type": "git", "reference": "c5fd2fcc7eeaa6cc5bb8cfc087e027e6a7e03088"}, "dist": {"url": "https://api.github.com/repos/shalvah/clara/zipball/c5fd2fcc7eeaa6cc5bb8cfc087e027e6a7e03088", "type": "zip", "shasum": "", "reference": "c5fd2fcc7eeaa6cc5bb8cfc087e027e6a7e03088"}, "support": {"issues": "https://github.com/shalvah/clara/issues", "source": "https://github.com/shalvah/clara/tree/1.0.0"}, "time": "2020-04-09T07:31:32+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 27 Mar 2024 20:39:02 GMT"}