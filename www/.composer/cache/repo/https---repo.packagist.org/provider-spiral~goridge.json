{"minified": "composer/2.0", "packages": {"spiral/goridge": [{"name": "spiral/goridge", "description": "High-performance PHP-to-Golang RPC bridge", "keywords": [], "homepage": "https://spiral.dev/", "version": "4.2.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "2a372118dac1f0c0511e2862f963ce649fefd9fa"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/2a372118dac1f0c0511e2862f963ce649fefd9fa", "type": "zip", "shasum": "", "reference": "2a372118dac1f0c0511e2862f963ce649fefd9fa"}, "type": "go<PERSON>", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/goridge/tree/4.2.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-05T13:55:33+00:00", "autoload": {"psr-4": {"Spiral\\Goridge\\": "src"}}, "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "spiral/roadrunner": "^2023 || ^2024.1 || ^2025.1"}, "require-dev": {"google/protobuf": "^3.22 || ^4.0", "infection/infection": "^0.29.0", "jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.5.45", "rybakit/msgpack": "^0.7", "spiral/code-style": "*", "vimeo/psalm": "^6.0"}, "suggest": {"ext-msgpack": "MessagePack codec support", "ext-protobuf": "Protobuf codec support", "rybakit/msgpack": "(^0.7) MessagePack codec support", "google/protobuf": "(^3.0) Protobuf codec support"}}, {"version": "v4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "c6696bd1834f5e88d1252a953a1336c041795411"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/c6696bd1834f5e88d1252a953a1336c041795411", "type": "zip", "shasum": "", "reference": "c6696bd1834f5e88d1252a953a1336c041795411"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v4.2.0"}, "time": "2024-04-11T17:26:14+00:00", "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "spiral/roadrunner": "^2023 || ^2024.1"}, "require-dev": {"vimeo/psalm": "^5.9", "google/protobuf": "^3.22", "rybakit/msgpack": "^0.7", "phpunit/phpunit": "^10.0", "jetbrains/phpstorm-attributes": "^1.0", "infection/infection": "^0.26.1"}}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "a5d67441dc862aa32647622975c9e6f6042a593d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/a5d67441dc862aa32647622975c9e6f6042a593d", "type": "zip", "shasum": "", "reference": "a5d67441dc862aa32647622975c9e6f6042a593d"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/goridge/tree/4.1.1"}, "time": "2023-12-21T10:47:02+00:00", "require": {"php": ">=8.1", "ext-json": "*", "ext-sockets": "*", "spiral/roadrunner": "^2023"}}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "d955f58be1c51daa1eb94a5ddaf4c2daf64ee14e"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/d955f58be1c51daa1eb94a5ddaf4c2daf64ee14e", "type": "zip", "shasum": "", "reference": "d955f58be1c51daa1eb94a5ddaf4c2daf64ee14e"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/goridge/tree/4.1.0"}, "time": "2023-10-03T18:40:43+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "56302fc0b677e7874a64ad5d76177e802f963a2a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/56302fc0b677e7874a64ad5d76177e802f963a2a", "type": "zip", "shasum": "", "reference": "56302fc0b677e7874a64ad5d76177e802f963a2a"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/goridge/tree/4.0.0"}, "time": "2023-04-13T11:38:18+00:00"}, {"homepage": "", "version": "v3.2.1", "version_normalized": "*******", "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "af388883ffc08f362d6f99e1eb30a063e3fd7ad7"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/af388883ffc08f362d6f99e1eb30a063e3fd7ad7", "type": "zip", "shasum": "", "reference": "af388883ffc08f362d6f99e1eb30a063e3fd7ad7"}, "support": {"source": "https://github.com/roadrunner-php/goridge/tree/v3.2.1"}, "funding": [{"url": "https://github.com/roadrunner-server", "type": "github"}], "time": "2024-06-27T08:01:49+00:00", "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*", "symfony/polyfill-php80": "^1.22"}, "require-dev": {"vimeo/psalm": "^4.18.1", "google/protobuf": "^3.17", "rybakit/msgpack": "^0.7", "phpunit/phpunit": "^9.5", "jetbrains/phpstorm-attributes": "^1.0", "infection/infection": "^0.26.1"}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "3d8e97d7d1cc26b6130d233177b23ecb3c7d4efb"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/3d8e97d7d1cc26b6130d233177b23ecb3c7d4efb", "type": "zip", "shasum": "", "reference": "3d8e97d7d1cc26b6130d233177b23ecb3c7d4efb"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.2.0"}, "time": "2022-03-21T20:32:19+00:00", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "2c50b649b4296a3733f2ff5de339f41b9db57b04"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/2c50b649b4296a3733f2ff5de339f41b9db57b04", "type": "zip", "shasum": "", "reference": "2c50b649b4296a3733f2ff5de339f41b9db57b04"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.1.2"}, "time": "2022-01-13T08:13:33+00:00", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "85ad54ae5f3a194330a459b8bd30324764536c2a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/85ad54ae5f3a194330a459b8bd30324764536c2a", "type": "zip", "shasum": "", "reference": "85ad54ae5f3a194330a459b8bd30324764536c2a"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.1.1"}, "time": "2021-06-10T12:31:44+00:00", "require-dev": {"vimeo/psalm": "^4.6", "spiral/code-style": "^1.0", "google/protobuf": "^3.17", "rybakit/msgpack": "^0.7", "phpunit/phpunit": "^8.0", "jetbrains/phpstorm-attributes": "^1.0"}}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "2238be146a3c0171edda22dee88ecca9151775c5"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/2238be146a3c0171edda22dee88ecca9151775c5", "type": "zip", "shasum": "", "reference": "2238be146a3c0171edda22dee88ecca9151775c5"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.1.0"}, "time": "2021-06-10T09:14:01+00:00", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "3258892afd1d0a9cb889eb21ddd353dd06ba774d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/3258892afd1d0a9cb889eb21ddd353dd06ba774d", "type": "zip", "shasum": "", "reference": "3258892afd1d0a9cb889eb21ddd353dd06ba774d"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.0.1"}, "time": "2021-03-11T14:28:29+00:00", "autoload": {"psr-4": {"Spiral\\Goridge\\": "src/"}}, "require": {"php": ">=7.4", "ext-json": "*", "ext-sockets": "*"}, "require-dev": {"spiral/code-style": "^1.0", "rybakit/msgpack": "^0.7.1", "phpunit/phpunit": "~8.0", "phpstan/phpstan": "~0.12.34"}, "suggest": "__unset", "extra": "__unset"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "2ddd4d0257c28268111e042b7b8c60ca6d6606cf"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/2ddd4d0257c28268111e042b7b8c60ca6d6606cf", "type": "zip", "shasum": "", "reference": "2ddd4d0257c28268111e042b7b8c60ca6d6606cf"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.0.0"}, "time": "2020-12-29T13:03:32+00:00"}, {"version": "v3.0.0-beta4", "version_normalized": "*******-beta4"}, {"version": "v3.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "be9906595b6e4f1efb57312f6df2753abfd4f235"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/be9906595b6e4f1efb57312f6df2753abfd4f235", "type": "zip", "shasum": "", "reference": "be9906595b6e4f1efb57312f6df2753abfd4f235"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.0.0-beta3"}, "time": "2020-12-11T11:01:07+00:00"}, {"version": "v3.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "973f6bb9014812b0ed92943e31a5a8fb8d938d37"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/973f6bb9014812b0ed92943e31a5a8fb8d938d37", "type": "zip", "shasum": "", "reference": "973f6bb9014812b0ed92943e31a5a8fb8d938d37"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.0.0-beta2"}, "time": "2020-12-10T18:44:30+00:00"}, {"version": "v3.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "4f464f4c92b3d24f1fe30d9834963508f0a71335"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/4f464f4c92b3d24f1fe30d9834963508f0a71335", "type": "zip", "shasum": "", "reference": "4f464f4c92b3d24f1fe30d9834963508f0a71335"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v3.0.0-beta1"}, "time": "2020-12-10T18:28:59+00:00", "require": {"php": ">=7.4|>=8.0", "ext-json": "*", "ext-sockets": "*"}}, {"version": "v2.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "a7373de7f86a5452f8ad61bd1340dc158626f7f8"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/a7373de7f86a5452f8ad61bd1340dc158626f7f8", "type": "zip", "shasum": "", "reference": "a7373de7f86a5452f8ad61bd1340dc158626f7f8"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.4.5"}, "time": "2020-08-14T14:28:30+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Spiral\\Goridge\\": "src/"}}, "require": {"php": ">=7.2", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "~8.0", "spiral/code-style": "^1.0", "phpstan/phpstan": "^0.12.23"}}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "c522eec9d8215c6059eeb4647d37003a2726e346"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/c522eec9d8215c6059eeb4647d37003a2726e346", "type": "zip", "shasum": "", "reference": "c522eec9d8215c6059eeb4647d37003a2726e346"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.4.4"}, "time": "2020-05-25T06:11:58+00:00"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "a9ad6369617f857e2899e1bb5b2348611db4da79"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/a9ad6369617f857e2899e1bb5b2348611db4da79", "type": "zip", "shasum": "", "reference": "a9ad6369617f857e2899e1bb5b2348611db4da79"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.4.3"}, "time": "2020-05-22T11:58:26+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "4f06669674b982ed4925a8b67ee2f771522137f2"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/4f06669674b982ed4925a8b67ee2f771522137f2", "type": "zip", "shasum": "", "reference": "4f06669674b982ed4925a8b67ee2f771522137f2"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.4.2"}, "time": "2020-05-19T14:17:03+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "f1be906877b0bbe203c76bb313ed7c05b664b937"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/f1be906877b0bbe203c76bb313ed7c05b664b937", "type": "zip", "shasum": "", "reference": "f1be906877b0bbe203c76bb313ed7c05b664b937"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/2.4.1"}, "time": "2020-05-06T08:14:24+00:00", "require-dev": {"phpunit/phpunit": "~8.0", "spiral/code-style": "^1.0"}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "99fd70b70056f5ee8911683be6831b70b401a441"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/99fd70b70056f5ee8911683be6831b70b401a441", "type": "zip", "shasum": "", "reference": "99fd70b70056f5ee8911683be6831b70b401a441"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/2.4.0"}, "time": "2020-05-05T12:00:50+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "ea465372d867fd181ad5fabeab883e807a761646"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/ea465372d867fd181ad5fabeab883e807a761646", "type": "zip", "shasum": "", "reference": "ea465372d867fd181ad5fabeab883e807a761646"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.3.3"}, "time": "2020-04-22T14:16:35+00:00", "autoload": {"files": ["php-src/functions.php"], "psr-4": {"Spiral\\Goridge\\": "php-src/"}}, "require": {"php": "^7.1", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "~6.0"}}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "45765932c537de3429fd96375afe1031b9a9a2e9"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/45765932c537de3429fd96375afe1031b9a9a2e9", "type": "zip", "shasum": "", "reference": "45765932c537de3429fd96375afe1031b9a9a2e9"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.3.2"}, "time": "2020-04-21T14:58:22+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "22c9d3e001630c4fbd2a463773d9afe12e014327"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/22c9d3e001630c4fbd2a463773d9afe12e014327", "type": "zip", "shasum": "", "reference": "22c9d3e001630c4fbd2a463773d9afe12e014327"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.3.0"}, "time": "2020-03-23T14:23:40+00:00", "autoload": {"psr-4": {"Spiral\\Goridge\\": "php-src/"}}, "require": {"php": ">=7.0"}}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "676b03eabec8821e50e4af551e331d5e3eaac799"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/676b03eabec8821e50e4af551e331d5e3eaac799", "type": "zip", "shasum": "", "reference": "676b03eabec8821e50e4af551e331d5e3eaac799"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.2.1"}, "time": "2019-11-30T09:37:08+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "d339ea3ef9f81715d03034998ebf871f2b263d56"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/d339ea3ef9f81715d03034998ebf871f2b263d56", "type": "zip", "shasum": "", "reference": "d339ea3ef9f81715d03034998ebf871f2b263d56"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.2.0"}, "time": "2019-11-29T13:50:11+00:00"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "4d5c763886dd0a1e45b1a7d1f99d180cf1a7a7c4"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/4d5c763886dd0a1e45b1a7d1f99d180cf1a7a7c4", "type": "zip", "shasum": "", "reference": "4d5c763886dd0a1e45b1a7d1f99d180cf1a7a7c4"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.1.4"}, "time": "2019-04-01T14:30:26+00:00"}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "1d9d4cc79ddfc73a0e92d9c77ebdf54abcade694"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/1d9d4cc79ddfc73a0e92d9c77ebdf54abcade694", "type": "zip", "shasum": "", "reference": "1d9d4cc79ddfc73a0e92d9c77ebdf54abcade694"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.1.3"}, "time": "2018-12-20T12:06:05+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "0351012be508e881db1276451f5bdd49eda5978c"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/0351012be508e881db1276451f5bdd49eda5978c", "type": "zip", "shasum": "", "reference": "0351012be508e881db1276451f5bdd49eda5978c"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.1.2"}, "time": "2018-06-07T13:08:32+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "e541345238a2ced3c43eb5e40d535b85bf22e437"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/e541345238a2ced3c43eb5e40d535b85bf22e437", "type": "zip", "shasum": "", "reference": "e541345238a2ced3c43eb5e40d535b85bf22e437"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.1.1"}, "time": "2018-06-07T12:19:14+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "27197f427ee2924496803b393a3c8a9b12f512a7"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/27197f427ee2924496803b393a3c8a9b12f512a7", "type": "zip", "shasum": "", "reference": "27197f427ee2924496803b393a3c8a9b12f512a7"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.1.0"}, "time": "2018-06-03T14:54:44+00:00"}, {"version": "v2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "4bff3a2651cb89f64106afc6a0ae15898718aba3"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/4bff3a2651cb89f64106afc6a0ae15898718aba3", "type": "zip", "shasum": "", "reference": "4bff3a2651cb89f64106afc6a0ae15898718aba3"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.0.5"}, "time": "2018-04-03T14:02:20+00:00", "autoload": {"psr-4": {"Spiral\\Goridge\\": "source/"}}}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "f0a8108aae0644682cff46536e9effabf18390d9"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/f0a8108aae0644682cff46536e9effabf18390d9", "type": "zip", "shasum": "", "reference": "f0a8108aae0644682cff46536e9effabf18390d9"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.0.4"}, "time": "2018-03-21T21:06:41+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "5e2bc5baf71f7a089a57acd739e85b6e0b01bb66"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/5e2bc5baf71f7a089a57acd739e85b6e0b01bb66", "type": "zip", "shasum": "", "reference": "5e2bc5baf71f7a089a57acd739e85b6e0b01bb66"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.0.3"}, "time": "2018-02-20T11:53:19+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "c08dcbc3d971f40a03a1bc9d53a0d229b97cd47a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/c08dcbc3d971f40a03a1bc9d53a0d229b97cd47a", "type": "zip", "shasum": "", "reference": "c08dcbc3d971f40a03a1bc9d53a0d229b97cd47a"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.0.2"}, "time": "2018-01-29T08:25:04+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "24a36898426359ec2a4f645ff8ee06f153ac2f3f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/24a36898426359ec2a4f645ff8ee06f153ac2f3f", "type": "zip", "shasum": "", "reference": "24a36898426359ec2a4f645ff8ee06f153ac2f3f"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.0.1"}, "time": "2018-01-23T22:15:20+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "f60182bef09f1e45a47908e1f0fb080affdcab81"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/f60182bef09f1e45a47908e1f0fb080affdcab81", "type": "zip", "shasum": "", "reference": "f60182bef09f1e45a47908e1f0fb080affdcab81"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v2.0.0"}, "time": "2017-11-17T11:07:26+00:00"}, {"description": "Goridge, high performance PHP-to-GO net/rpc Codec", "version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "b7737900ce50314963c9e6a53902ac9d1ace2c83"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/b7737900ce50314963c9e6a53902ac9d1ace2c83", "type": "zip", "shasum": "", "reference": "b7737900ce50314963c9e6a53902ac9d1ace2c83"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v1.0.4"}, "time": "2017-08-21T09:59:39+00:00", "autoload": {"psr-4": {"Spiral\\": "source/"}}, "require": {"php": ">=7.0", "ext-sockets": "*"}, "require-dev": {"phpunit/phpunit": "~6.0", "symfony/process": "^3.3"}}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "1e29dc2eb518502130e25bed8a606cd946b253c4"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/1e29dc2eb518502130e25bed8a606cd946b253c4", "type": "zip", "shasum": "", "reference": "1e29dc2eb518502130e25bed8a606cd946b253c4"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v1.0.3"}, "time": "2017-08-14T14:27:31+00:00", "require": {"php": ">=7.1", "ext-sockets": "*"}}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "670075c1e6075ae42327a32841dcaf80774af592"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/670075c1e6075ae42327a32841dcaf80774af592", "type": "zip", "shasum": "", "reference": "670075c1e6075ae42327a32841dcaf80774af592"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v1.0.2"}, "time": "2017-08-14T12:26:32+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "b41cf8b79622cfff0a5b3d8674e65dd57bca4085"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/b41cf8b79622cfff0a5b3d8674e65dd57bca4085", "type": "zip", "shasum": "", "reference": "b41cf8b79622cfff0a5b3d8674e65dd57bca4085"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v1.0.1"}, "time": "2017-08-14T08:16:20+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/goridge.git", "type": "git", "reference": "56633c33d4f8d4b2c3eac5fdda3008a3bd6521b7"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/56633c33d4f8d4b2c3eac5fdda3008a3bd6521b7", "type": "zip", "shasum": "", "reference": "56633c33d4f8d4b2c3eac5fdda3008a3bd6521b7"}, "support": {"issues": "https://github.com/roadrunner-php/goridge/issues", "source": "https://github.com/roadrunner-php/goridge/tree/v1.0.0"}, "time": "2017-08-13T21:05:00+00:00", "require-dev": {"phpunit/phpunit": "~6.0", "mockery/mockery": "^0.9.9", "symfony/process": "^3.3"}}]}, "security-advisories": [], "last-modified": "Mon, 05 May 2025 14:16:54 GMT"}