{"minified": "composer/2.0", "packages": {"symfony/polyfill-php73": [{"name": "symfony/polyfill-php73", "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "keywords": ["compatibility", "portable", "polyfill", "shim"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "type": "zip", "shasum": "", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}}, {"version": "v1.31.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.31.0"}}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/ec444d3f3f6505bb28d11afa41e75faadebc10a1", "type": "zip", "shasum": "", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "21bd091060673a1177ae842c0ef8fe30893114d2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/21bd091060673a1177ae842c0ef8fe30893114d2", "type": "zip", "shasum": "", "reference": "21bd091060673a1177ae842c0ef8fe30893114d2"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "fe2f306d1d9d346a7fee353d0d5012e401e984b5"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fe2f306d1d9d346a7fee353d0d5012e401e984b5", "type": "zip", "shasum": "", "reference": "fe2f306d1d9d346a7fee353d0d5012e401e984b5"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.28.0"}, "time": "2023-01-26T09:26:14+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "9e8ecb5f92152187c4799efd3c96b78ccab18ff9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/9e8ecb5f92152187c4799efd3c96b78ccab18ff9", "type": "zip", "shasum": "", "reference": "9e8ecb5f92152187c4799efd3c96b78ccab18ff9"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/e440d35fa0286f77fb45b79a03fedbeda9307e85", "type": "zip", "shasum": "", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/cc5db0e22b3cb4111010e48785a97f670b350ca5", "type": "zip", "shasum": "", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.25.0"}, "time": "2021-06-05T21:20:04+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.24.0"}}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "fba8933c384d6476ab14fb7b8526e5287ca7e010"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fba8933c384d6476ab14fb7b8526e5287ca7e010", "type": "zip", "shasum": "", "reference": "fba8933c384d6476ab14fb7b8526e5287ca7e010"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.23.0"}, "time": "2021-02-19T12:13:01+00:00"}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "type": "zip", "shasum": "", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.22.1"}, "time": "2021-01-07T16:49:33+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.22.0"}}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "8ff431c517be11c78c48a39a66d37431e26a6bed"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/8ff431c517be11c78c48a39a66d37431e26a6bed", "type": "zip", "shasum": "", "reference": "8ff431c517be11c78c48a39a66d37431e26a6bed"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "9d920e3218205554171b2503bb3e4a1366824a16"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/9d920e3218205554171b2503bb3e4a1366824a16", "type": "zip", "shasum": "", "reference": "9d920e3218205554171b2503bb3e4a1366824a16"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.19.0"}, "time": "2020-10-23T09:01:57+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "fffa1a52a023e782cdcc221d781fe1ec8f87fcca"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fffa1a52a023e782cdcc221d781fe1ec8f87fcca", "type": "zip", "shasum": "", "reference": "fffa1a52a023e782cdcc221d781fe1ec8f87fcca"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/master"}, "time": "2020-07-14T12:35:20+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.18.0"}}, {"version": "v1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "fa0837fe02d617d31fbb25f990655861bb27bd1a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fa0837fe02d617d31fbb25f990655861bb27bd1a", "type": "zip", "shasum": "", "reference": "fa0837fe02d617d31fbb25f990655861bb27bd1a"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.17.1"}, "time": "2020-06-06T08:46:27+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a760d8964ff79ab9bf057613a5808284ec852ccc", "type": "zip", "shasum": "", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/master"}, "time": "2020-05-12T16:47:27+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "7e95fe59d12169fcf4041487e4bf34fca37ee0ed"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/7e95fe59d12169fcf4041487e4bf34fca37ee0ed", "type": "zip", "shasum": "", "reference": "7e95fe59d12169fcf4041487e4bf34fca37ee0ed"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.16.0"}, "time": "2020-05-02T14:56:09+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "0f27e9f464ea3da33cbe7ca3bdf4eb66def9d0f7"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f27e9f464ea3da33cbe7ca3bdf4eb66def9d0f7", "type": "zip", "shasum": "", "reference": "0f27e9f464ea3da33cbe7ca3bdf4eb66def9d0f7"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.15.0"}, "time": "2020-02-27T09:26:54+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "5e66a0fa1070bf46bec4bea7962d285108edd675"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/5e66a0fa1070bf46bec4bea7962d285108edd675", "type": "zip", "shasum": "", "reference": "5e66a0fa1070bf46bec4bea7962d285108edd675"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.14.0"}, "time": "2020-01-13T11:15:53+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}, {"version": "v1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "4b0e2222c55a25b4541305a053013d5647d3a25f"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/4b0e2222c55a25b4541305a053013d5647d3a25f", "type": "zip", "shasum": "", "reference": "4b0e2222c55a25b4541305a053013d5647d3a25f"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/master"}, "time": "2019-11-27T16:25:15+00:00", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}}, {"version": "v1.13.1", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.13.0"}}, {"version": "v1.13.0", "version_normalized": "********"}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "2ceb49eaccb9352bff54d22570276bb75ba4a188"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/2ceb49eaccb9352bff54d22570276bb75ba4a188", "type": "zip", "shasum": "", "reference": "2ceb49eaccb9352bff54d22570276bb75ba4a188"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.12.0"}, "time": "2019-08-06T08:03:45+00:00", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}}, {"version": "v1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "d1fb4abcc0c47be136208ad9d68bf59f1ee17abd"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/d1fb4abcc0c47be136208ad9d68bf59f1ee17abd", "type": "zip", "shasum": "", "reference": "d1fb4abcc0c47be136208ad9d68bf59f1ee17abd"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/master"}, "time": "2019-02-06T07:57:58+00:00", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "47ad352296d61aae366f075b8609f4dcc28853ef"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/47ad352296d61aae366f075b8609f4dcc28853ef", "type": "zip", "shasum": "", "reference": "47ad352296d61aae366f075b8609f4dcc28853ef"}, "time": "2018-09-25T06:33:47+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}}, "extra": {"branch-alias": {"dev-master": "1.9-dev"}}}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "990ca8fa94736211d2b305178c3fb2527e1fbce1"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/990ca8fa94736211d2b305178c3fb2527e1fbce1", "type": "zip", "shasum": "", "reference": "990ca8fa94736211d2b305178c3fb2527e1fbce1"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.9.0"}, "time": "2018-08-06T14:22:27+00:00"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-php73.git", "type": "git", "reference": "9841f6fc047725a8286ea986018355bbc9200383"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/9841f6fc047725a8286ea986018355bbc9200383", "type": "zip", "shasum": "", "reference": "9841f6fc047725a8286ea986018355bbc9200383"}, "support": {"source": "https://github.com/symfony/polyfill-php73/tree/master"}, "time": "2018-04-26T06:22:38+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 09:41:35 GMT"}