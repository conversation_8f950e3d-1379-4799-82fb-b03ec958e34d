{"minified": "composer/2.0", "packages": {"spiral/roadrunner-cli": [{"name": "spiral/roadrunner-cli", "description": "RoadRunner: Command Line Interface", "keywords": [], "homepage": "https://roadrunner.dev", "version": "v2.7.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-php/cli.git", "type": "git", "reference": "a51ff873654744821437e76406df7b6a0d4dbfe1"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/cli/zipball/a51ff873654744821437e76406df7b6a0d4dbfe1", "type": "zip", "shasum": "", "reference": "a51ff873654744821437e76406df7b6a0d4dbfe1"}, "type": "library", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/cli/tree/v2.7.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-02-18T12:24:20+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Console\\": "src"}}, "bin": ["bin/rr"], "require": {"php": ">=8.1", "ext-json": "*", "composer/semver": "^3.4", "spiral/roadrunner-worker": "^2 || ^3", "spiral/tokenizer": "^2.13 || ^3.15", "symfony/console": "^5.3 || ^6.0 || ^7.0", "symfony/http-client": "^4.4.51 || ^5.4.49 || ^6.4.17 || ^7.2", "symfony/yaml": "^5.4.49 || ^6.4.17 || ^7.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.2", "spiral/code-style": "^2.2.2", "spiral/dumper": "^3.3", "vimeo/psalm": "^6.0"}}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/cli.git", "type": "git", "reference": "61eec29e2df6ccd2a7243e2bcc21453ba2fa3f4d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/cli/zipball/61eec29e2df6ccd2a7243e2bcc21453ba2fa3f4d", "type": "zip", "shasum": "", "reference": "61eec29e2df6ccd2a7243e2bcc21453ba2fa3f4d"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/cli/tree/v2.7.0"}, "time": "2025-01-28T11:07:47+00:00"}, {"homepage": "", "version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/cli.git", "type": "git", "reference": "a896e1d05a1fc4ebaf14e68b8b901c851c3b38b2"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/cli/zipball/a896e1d05a1fc4ebaf14e68b8b901c851c3b38b2", "type": "zip", "shasum": "", "reference": "a896e1d05a1fc4ebaf14e68b8b901c851c3b38b2"}, "support": {"source": "https://github.com/roadrunner-php/cli/tree/v2.6.0"}, "funding": [], "time": "2023-12-05T20:46:56+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "require": {"php": ">=7.4", "ext-json": "*", "composer/semver": "^3.2", "spiral/roadrunner-worker": ">=2.0.2", "spiral/tokenizer": "^2.13 || ^3.0", "symfony/console": "^5.3 || ^6.0 || ^7.0", "symfony/http-client": "^4.4.11 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.22", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "vimeo/psalm": "^5.17"}}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-php/cli.git", "type": "git", "reference": "468c4a646d10a38b1475ec7b71f5880aa354febf"}, "dist": {"url": "https://api.github.com/repos/roadrunner-php/cli/zipball/468c4a646d10a38b1475ec7b71f5880aa354febf", "type": "zip", "shasum": "", "reference": "468c4a646d10a38b1475ec7b71f5880aa354febf"}, "support": {"issues": "https://github.com/roadrunner-php/cli/issues", "source": "https://github.com/roadrunner-php/cli/tree/v2.5.0"}, "time": "2023-04-18T14:19:26+00:00", "require": {"php": ">=7.4", "ext-json": "*", "composer/semver": "^3.2", "spiral/roadrunner-worker": ">=2.0.2", "spiral/tokenizer": "^2.13 || ^3.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.22", "symfony/yaml": "^5.4 || ^6.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "vimeo/psalm": "^4.4", "symfony/var-dumper": "^4.4|^5.0"}}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "8676fcc57823b164cac216f08e8590fe7d0f2016"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/8676fcc57823b164cac216f08e8590fe7d0f2016", "type": "zip", "shasum": "", "reference": "8676fcc57823b164cac216f08e8590fe7d0f2016"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.4.0"}, "time": "2022-12-16T07:35:51+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "2b42333e1ee772a950039e8aebb5d819377b2bb4"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/2b42333e1ee772a950039e8aebb5d819377b2bb4", "type": "zip", "shasum": "", "reference": "2b42333e1ee772a950039e8aebb5d819377b2bb4"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.3.0"}, "time": "2022-07-28T08:53:10+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "d8a224137b1d1ace0aac2308df396403393d63a8"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/d8a224137b1d1ace0aac2308df396403393d63a8", "type": "zip", "shasum": "", "reference": "d8a224137b1d1ace0aac2308df396403393d63a8"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.2.0"}, "time": "2022-05-17T06:44:24+00:00", "require": {"php": ">=7.4", "ext-json": "*", "spiral/roadrunner-worker": ">=2.0.2", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.22", "composer/semver": "^3.2"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "8a42aeed24939c64bccbaa179d473f9c57393dc1"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/8a42aeed24939c64bccbaa179d473f9c57393dc1", "type": "zip", "shasum": "", "reference": "8a42aeed24939c64bccbaa179d473f9c57393dc1"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.1.0"}, "time": "2022-01-20T07:51:22+00:00"}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "211a9c4a5d52f762c6698c02f0a7f5af67c826bf"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/211a9c4a5d52f762c6698c02f0a7f5af67c826bf", "type": "zip", "shasum": "", "reference": "211a9c4a5d52f762c6698c02f0a7f5af67c826bf"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.14"}, "time": "2021-12-23T07:40:46+00:00"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "af7be95378b277901bb4fe73d3f99a52e69dc5fe"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/af7be95378b277901bb4fe73d3f99a52e69dc5fe", "type": "zip", "shasum": "", "reference": "af7be95378b277901bb4fe73d3f99a52e69dc5fe"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.13"}, "time": "2021-12-01T11:38:41+00:00", "require": {"php": ">=7.4", "ext-json": "*", "spiral/roadrunner-worker": ">=2.0.2", "symfony/console": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/polyfill-php80": "^1.22", "composer/semver": "^3.2"}}, {"version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "1364e26627e71c3204161ab22f7dc7405e325d48"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/1364e26627e71c3204161ab22f7dc7405e325d48", "type": "zip", "shasum": "", "reference": "1364e26627e71c3204161ab22f7dc7405e325d48"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.12"}, "time": "2021-11-02T09:03:48+00:00"}, {"version": "v2.0.11", "version_normalized": "********", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "4e805b544cb6e30d513222a3624f11dbb843582e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/4e805b544cb6e30d513222a3624f11dbb843582e", "type": "zip", "shasum": "", "reference": "4e805b544cb6e30d513222a3624f11dbb843582e"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.11"}, "time": "2021-10-05T08:50:03+00:00"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "2a637918d4d5803b75f5bfd65004391477ab9e4a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/2a637918d4d5803b75f5bfd65004391477ab9e4a", "type": "zip", "shasum": "", "reference": "2a637918d4d5803b75f5bfd65004391477ab9e4a"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.10"}, "time": "2021-10-04T16:16:32+00:00"}, {"version": "v2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "4bf2bdb3664f58dac2f197ad42dbef35a0cb3c94"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/4bf2bdb3664f58dac2f197ad42dbef35a0cb3c94", "type": "zip", "shasum": "", "reference": "4bf2bdb3664f58dac2f197ad42dbef35a0cb3c94"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.9"}, "time": "2021-09-28T12:09:23+00:00"}, {"version": "v2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "2bba5435b70ff9d443f1cda41cc581df1d4b5f09"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/2bba5435b70ff9d443f1cda41cc581df1d4b5f09", "type": "zip", "shasum": "", "reference": "2bba5435b70ff9d443f1cda41cc581df1d4b5f09"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.8"}, "time": "2021-09-16T15:52:59+00:00"}, {"version": "v2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "8504eade9dd2c820c96fbf1f94c7afdad13b4063"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/8504eade9dd2c820c96fbf1f94c7afdad13b4063", "type": "zip", "shasum": "", "reference": "8504eade9dd2c820c96fbf1f94c7afdad13b4063"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.7"}, "time": "2021-06-29T09:19:57+00:00"}, {"version": "v2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "42c03687f8c183d36e5bc73f43782a7d2b3ca155"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/42c03687f8c183d36e5bc73f43782a7d2b3ca155", "type": "zip", "shasum": "", "reference": "42c03687f8c183d36e5bc73f43782a7d2b3ca155"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.6"}, "time": "2021-06-01T10:04:14+00:00"}, {"version": "v2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "d75a3257ad474dbf4c421813ee3201d771c3e3e5"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/d75a3257ad474dbf4c421813ee3201d771c3e3e5", "type": "zip", "shasum": "", "reference": "d75a3257ad474dbf4c421813ee3201d771c3e3e5"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.5"}, "time": "2021-05-25T19:57:07+00:00"}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "81dcec5c2d94481dedace488c27d433414726654"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/81dcec5c2d94481dedace488c27d433414726654", "type": "zip", "shasum": "", "reference": "81dcec5c2d94481dedace488c27d433414726654"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.4"}, "time": "2021-05-12T15:49:01+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "df9a69b25ae1dfb361e971e2cdc12c56ba128f18"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/df9a69b25ae1dfb361e971e2cdc12c56ba128f18", "type": "zip", "shasum": "", "reference": "df9a69b25ae1dfb361e971e2cdc12c56ba128f18"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.3"}, "time": "2021-04-10T14:21:48+00:00", "require": {"php": ">=7.4", "ext-json": "*", "spiral/roadrunner-worker": ">=2.0", "symfony/console": "^5.1", "symfony/http-client": "^5.1", "symfony/polyfill-php80": "^1.22", "composer/semver": "^3.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "vimeo/psalm": "^4.4", "symfony/var-dumper": "^5.1"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "612627f9f188bc5511a18123db592ff83fefd04d"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/612627f9f188bc5511a18123db592ff83fefd04d", "type": "zip", "shasum": "", "reference": "612627f9f188bc5511a18123db592ff83fefd04d"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.2"}, "time": "2021-03-10T05:56:52+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "14d962186a9dcc242c972e8e619fac227a54a61e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/14d962186a9dcc242c972e8e619fac227a54a61e", "type": "zip", "shasum": "", "reference": "14d962186a9dcc242c972e8e619fac227a54a61e"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.1"}, "time": "2021-02-24T16:35:02+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner-cli.git", "type": "git", "reference": "3569aa278fb6089b3e6a6856aca598d4e014df1b"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner-cli/zipball/3569aa278fb6089b3e6a6856aca598d4e014df1b", "type": "zip", "shasum": "", "reference": "3569aa278fb6089b3e6a6856aca598d4e014df1b"}, "support": {"issues": "https://github.com/spiral/roadrunner-cli/issues", "source": "https://github.com/spiral/roadrunner-cli/tree/v2.0.0"}, "time": "2021-02-11T21:26:40+00:00"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 18 Feb 2025 12:25:15 GMT"}