{"minified": "composer/2.0", "packages": {"tgalopin/html-sanitizer": [{"name": "tgalopin/html-sanitizer", "description": "Sanitize untrustworthy HTML user input", "keywords": [], "homepage": "", "version": "1.5.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "5d02dcb6f2ea4f505731eac440798caa1b3b0913"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/5d02dcb6f2ea4f505731eac440798caa1b3b0913", "type": "zip", "shasum": "", "reference": "5d02dcb6f2ea4f505731eac440798caa1b3b0913"}, "type": "library", "time": "2021-09-14T08:27:50+00:00", "autoload": {"psr-4": {"HtmlSanitizer\\": "src"}}, "require": {"php": ">=7.1", "ext-dom": "*", "league/uri-parser": "^1.4.1", "masterminds/html5": "^2.4", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "^7.4", "symfony/var-dumper": "^4.1"}, "abandoned": "symfony/html-sanitizer", "support": {"issues": "https://github.com/tgalopin/html-sanitizer/issues", "source": "https://github.com/tgalopin/html-sanitizer/tree/1.5.0"}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "56cca6b48de4e50d16a4f549e3e677ae0d561e91"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/56cca6b48de4e50d16a4f549e3e677ae0d561e91", "type": "zip", "shasum": "", "reference": "56cca6b48de4e50d16a4f549e3e677ae0d561e91"}, "time": "2020-02-03T16:51:08+00:00", "require": {"php": ">=7.1", "ext-dom": "*", "league/uri-parser": "^1.4.1", "masterminds/html5": "^2.4", "psr/log": "^1.0"}, "support": {"issues": "https://github.com/tgalopin/html-sanitizer/issues", "source": "https://github.com/tgalopin/html-sanitizer/tree/master"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "8ca76a6ce8c7d66f38a9101769af63bfb5626439"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/8ca76a6ce8c7d66f38a9101769af63bfb5626439", "type": "zip", "shasum": "", "reference": "8ca76a6ce8c7d66f38a9101769af63bfb5626439"}, "time": "2019-10-24T14:52:22+00:00", "require": {"php": ">=7.1", "ext-dom": "*", "ext-intl": "*", "masterminds/html5": "^2.4", "psr/log": "^1.0", "league/uri-parser": "^1.4.1"}, "support": {"issues": "https://github.com/tgalopin/html-sanitizer/issues", "source": "https://github.com/tgalopin/html-sanitizer/tree/1.3.0"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "858f83aff56a6cdd36747b010116be12da6a4cfb"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/858f83aff56a6cdd36747b010116be12da6a4cfb", "type": "zip", "shasum": "", "reference": "858f83aff56a6cdd36747b010116be12da6a4cfb"}, "time": "2019-04-05T08:47:19+00:00", "require": {"php": ">=7.1", "ext-dom": "*", "masterminds/html5": "^2.4", "psr/log": "^1.0", "league/uri-parser": "^1.4.1"}, "support": {"issues": "https://github.com/tgalopin/html-sanitizer/issues", "source": "https://github.com/tgalopin/html-sanitizer/tree/master"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "286e4f3d13cf0294d968a6022647e5d6bc708b60"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/286e4f3d13cf0294d968a6022647e5d6bc708b60", "type": "zip", "shasum": "", "reference": "286e4f3d13cf0294d968a6022647e5d6bc708b60"}, "time": "2018-12-01T15:16:40+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "2d17c01854845670038477c3c4a61aa49d1e83fc"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/2d17c01854845670038477c3c4a61aa49d1e83fc", "type": "zip", "shasum": "", "reference": "2d17c01854845670038477c3c4a61aa49d1e83fc"}, "time": "2018-11-28T18:09:33+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "45ec054e236de121108e8d1cd0200f9c6d7573d3"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/45ec054e236de121108e8d1cd0200f9c6d7573d3", "type": "zip", "shasum": "", "reference": "45ec054e236de121108e8d1cd0200f9c6d7573d3"}, "time": "2018-11-26T21:21:05+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "11c444f49a02df5bf79f311a593c93487c672b74"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/11c444f49a02df5bf79f311a593c93487c672b74", "type": "zip", "shasum": "", "reference": "11c444f49a02df5bf79f311a593c93487c672b74"}, "time": "2018-11-18T21:39:29+00:00", "require": {"php": ">=7.1", "ext-dom": "*", "masterminds/html5": "^2.4", "psr/log": "^1.0", "league/uri-parser": "^1.4"}}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "a3c5a77567bcd90edbc839d5dfceca0849a7bbfa"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/a3c5a77567bcd90edbc839d5dfceca0849a7bbfa", "type": "zip", "shasum": "", "reference": "a3c5a77567bcd90edbc839d5dfceca0849a7bbfa"}, "time": "2018-11-17T17:22:55+00:00", "require": {"php": ">=7.1", "ext-dom": "*", "masterminds/html5": "^2.3", "psr/log": "^1.0", "league/uri-parser": "^1.4"}}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "0bf7eea130503082daf2d93b4730c8eb3cbff498"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/0bf7eea130503082daf2d93b4730c8eb3cbff498", "type": "zip", "shasum": "", "reference": "0bf7eea130503082daf2d93b4730c8eb3cbff498"}, "time": "2018-11-04T17:18:05+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "7ffb7535b590562198821ca19b305163591d30c8"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/7ffb7535b590562198821ca19b305163591d30c8", "type": "zip", "shasum": "", "reference": "7ffb7535b590562198821ca19b305163591d30c8"}, "time": "2018-10-19T23:08:20+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "00d0dc7708a5e8f718a6478424a3e418aac8023d"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/00d0dc7708a5e8f718a6478424a3e418aac8023d", "type": "zip", "shasum": "", "reference": "00d0dc7708a5e8f718a6478424a3e418aac8023d"}, "time": "2018-10-19T13:54:28+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/tgalopin/html-sanitizer.git", "type": "git", "reference": "6e4d4fd1deaf916036554c6e91589995155c2a04"}, "dist": {"url": "https://api.github.com/repos/tgalopin/html-sanitizer/zipball/6e4d4fd1deaf916036554c6e91589995155c2a04", "type": "zip", "shasum": "", "reference": "6e4d4fd1deaf916036554c6e91589995155c2a04"}, "time": "2018-10-16T17:59:08+00:00", "require": {"php": ">=7.1", "ext-dom": "*", "masterminds/html5": "^2.3", "psr/log": "^1.0"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:43:15 GMT"}