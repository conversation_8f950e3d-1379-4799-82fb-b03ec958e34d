{"minified": "composer/2.0", "packages": {"zendframework/zend-stdlib": [{"name": "zendframework/zend-stdlib", "description": "SPL extensions, array utilities, error handlers, and more", "keywords": ["ZendFramework", "stdlib", "zf"], "homepage": "", "version": "3.2.1", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [], "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "66536006722aff9e62d1b331025089b7ec71c065"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/66536006722aff9e62d1b331025089b7ec71c065", "type": "zip", "shasum": "", "reference": "66536006722aff9e62d1b331025089b7ec71c065"}, "type": "library", "time": "2018-08-28T21:34:05+00:00", "autoload": {"psr-4": {"Zend\\Stdlib\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "3.2.x-dev", "dev-develop": "3.3.x-dev"}}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpbench/phpbench": "^0.13", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0"}, "abandoned": "laminas/laminas-stdlib", "support": {"docs": "https://docs.zendframework.com/zend-stdlib/", "forum": "https://discourse.zendframework.com/c/questions/components", "issues": "https://github.com/zendframework/zend-stdlib/issues", "rss": "https://github.com/zendframework/zend-stdlib/releases.atom", "slack": "https://zendframework-slack.herokuapp.com", "source": "https://github.com/zendframework/zend-stdlib"}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "cd164b4a18b5d1aeb69be2c26db035b5ed6925ae"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/cd164b4a18b5d1aeb69be2c26db035b5ed6925ae", "type": "zip", "shasum": "", "reference": "cd164b4a18b5d1aeb69be2c26db035b5ed6925ae"}, "time": "2018-04-30T13:50:40+00:00"}, {"description": "", "keywords": ["zf2", "stdlib"], "homepage": "https://github.com/zendframework/zend-stdlib", "version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "10ef03144902d1955f935fff5346ed52f7d99bcc"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/10ef03144902d1955f935fff5346ed52f7d99bcc", "type": "zip", "shasum": "", "reference": "10ef03144902d1955f935fff5346ed52f7d99bcc"}, "time": "2018-04-12T16:05:42+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev", "dev-develop": "3.2-dev"}}, "require-dev": {"athletic/athletic": "~0.1", "phpunit/phpunit": "~4.0", "zendframework/zend-coding-standard": "~1.0.0"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-3.1.1"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "debedcfc373a293f9250cc9aa03cf121428c8e78"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/debedcfc373a293f9250cc9aa03cf121428c8e78", "type": "zip", "shasum": "", "reference": "debedcfc373a293f9250cc9aa03cf121428c8e78"}, "time": "2016-09-13T14:38:50+00:00", "require-dev": {"athletic/athletic": "~0.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.6.2"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-3.1.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "8bafa58574204bdff03c275d1d618aaa601588ae"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/8bafa58574204bdff03c275d1d618aaa601588ae", "type": "zip", "shasum": "", "reference": "8bafa58574204bdff03c275d1d618aaa601588ae"}, "time": "2016-04-12T21:19:36+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev", "dev-develop": "3.1-dev"}}, "require": {"php": "^5.5 || ^7.0"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "athletic/athletic": "~0.1"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-3.0.1"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "22eb098958980fbbe6b9a06f209f5a4b496cc0c1"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/22eb098958980fbbe6b9a06f209f5a4b496cc0c1", "type": "zip", "shasum": "", "reference": "22eb098958980fbbe6b9a06f209f5a4b496cc0c1"}, "time": "2016-02-03T16:53:37+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-3.0.0"}}, {"version": "2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "0e44eb46788f65e09e077eb7f44d2659143bcc1f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/0e44eb46788f65e09e077eb7f44d2659143bcc1f", "type": "zip", "shasum": "", "reference": "0e44eb46788f65e09e077eb7f44d2659143bcc1f"}, "time": "2016-04-12T21:17:31+00:00", "extra": {"branch-alias": {"dev-release-2.7": "2.7-dev", "dev-master": "3.0-dev", "dev-develop": "3.1-dev"}}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-hydrator": "~1.1"}, "require-dev": {"zendframework/zend-config": "~2.5", "zendframework/zend-eventmanager": "~2.5", "zendframework/zend-inputfilter": "~2.5", "zendframework/zend-serializer": "~2.5", "zendframework/zend-servicemanager": "~2.5", "zendframework/zend-filter": "~2.5", "fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "athletic/athletic": "~0.1"}, "suggest": {"zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-servicemanager": "To support hydrator plugin manager usage", "zendframework/zend-filter": "To support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.7"}}, {"version": "2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "4499760badfada27ee600f5c2cfd47d5263ccf1b"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/4499760badfada27ee600f5c2cfd47d5263ccf1b", "type": "zip", "shasum": "", "reference": "4499760badfada27ee600f5c2cfd47d5263ccf1b"}, "time": "2016-02-19T16:02:11+00:00"}, {"version": "2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "b894a85f3ef8a52e7aa62c3c5aa245e383c70cca"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/b894a85f3ef8a52e7aa62c3c5aa245e383c70cca", "type": "zip", "shasum": "", "reference": "b894a85f3ef8a52e7aa62c3c5aa245e383c70cca"}, "time": "2016-02-16T18:25:48+00:00", "require": {"php": "^5.5 || ^7.0", "zendframework/zend-hydrator": "~1.0"}}, {"version": "2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "cae029346a33663b998507f94962eb27de060683"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/cae029346a33663b998507f94962eb27de060683", "type": "zip", "shasum": "", "reference": "cae029346a33663b998507f94962eb27de060683"}, "time": "2015-10-15T15:57:32+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev", "dev-develop": "2.8-dev"}}, "require": {"php": ">=5.5", "zendframework/zend-hydrator": "~1.0"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.7.4"}}, {"version": "2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "8ac0c77ff567fcf49b58689ee3bfa7595be102bc"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/8ac0c77ff567fcf49b58689ee3bfa7595be102bc", "type": "zip", "shasum": "", "reference": "8ac0c77ff567fcf49b58689ee3bfa7595be102bc"}, "time": "2015-09-25T04:06:33+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/master"}}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "130eaa89e24acbc6386af9061aabc78e1948b1f9"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/130eaa89e24acbc6386af9061aabc78e1948b1f9", "type": "zip", "shasum": "", "reference": "130eaa89e24acbc6386af9061aabc78e1948b1f9"}, "time": "2015-09-23T13:11:47+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "e310dc83e756fdf0f61281540c4bee29f9e72e36"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/e310dc83e756fdf0f61281540c4bee29f9e72e36", "type": "zip", "shasum": "", "reference": "e310dc83e756fdf0f61281540c4bee29f9e72e36"}, "time": "2015-09-22T16:25:52+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.7.1"}}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "b317f647b7fc59db1917b8bda3d4ae8eb0fee1ec"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/b317f647b7fc59db1917b8bda3d4ae8eb0fee1ec", "type": "zip", "shasum": "", "reference": "b317f647b7fc59db1917b8bda3d4ae8eb0fee1ec"}, "time": "2015-09-22T13:07:16+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.7.0"}}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "a35758803fc9051ec1aff43989e679b6b451b1b4"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/a35758803fc9051ec1aff43989e679b6b451b1b4", "type": "zip", "shasum": "", "reference": "a35758803fc9051ec1aff43989e679b6b451b1b4"}, "time": "2015-07-21T17:08:05+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev", "dev-develop": "2.7-dev"}}, "require": {"php": ">=5.5"}, "require-dev": {"zendframework/zend-config": "~2.5", "zendframework/zend-eventmanager": "~2.5", "zendframework/zend-inputfilter": "~2.5", "zendframework/zend-serializer": "~2.5", "zendframework/zend-servicemanager": "~2.5", "zendframework/zend-filter": "~2.5", "fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.6.0"}}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "2e50c4e8f8d0ab928aa5beeb21644f30bf2cc8bf"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/2e50c4e8f8d0ab928aa5beeb21644f30bf2cc8bf", "type": "zip", "shasum": "", "reference": "2e50c4e8f8d0ab928aa5beeb21644f30bf2cc8bf"}, "time": "2015-07-21T14:59:49+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/master"}}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "cc8e90a60dd5d44b9730b77d07b97550091da1ae"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/cc8e90a60dd5d44b9730b77d07b97550091da1ae", "type": "zip", "shasum": "", "reference": "cc8e90a60dd5d44b9730b77d07b97550091da1ae"}, "time": "2015-06-03T15:32:03+00:00", "require": {"php": ">=5.3.23"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.5.1"}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "06054e30e60f2907a8963fbd090f338ada0c7263"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/06054e30e60f2907a8963fbd090f338ada0c7263", "type": "zip", "shasum": "", "reference": "06054e30e60f2907a8963fbd090f338ada0c7263"}, "time": "2015-06-03T14:05:58+00:00", "require-dev": {"zendframework/zend-config": "~2.5.0", "zendframework/zend-eventmanager": "~2.5.0", "zendframework/zend-inputfilter": "~2.5.0", "zendframework/zend-serializer": "~2.5.0", "zendframework/zend-servicemanager": "~2.5.0", "zendframework/zend-filter": "~2.5.0", "fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.5.0"}}, {"version": "2.4.13", "version_normalized": "********", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "d8ecb629a72da9f91bd95c5af006384823560b42"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/d8ecb629a72da9f91bd95c5af006384823560b42", "type": "zip", "shasum": "", "reference": "d8ecb629a72da9f91bd95c5af006384823560b42"}, "time": "2015-07-21T13:55:46+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev", "dev-develop": "2.5-dev"}}, "require-dev": {"zendframework/zend-eventmanager": "self.version", "zendframework/zend-serializer": "self.version", "zendframework/zend-servicemanager": "self.version", "zendframework/zend-filter": "self.version", "fabpot/php-cs-fixer": "1.7.*", "satooshi/php-coveralls": "dev-master", "phpunit/phpunit": "~4.0"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.4.4"}}, {"version": "2.4.12", "version_normalized": "********"}, {"version": "2.4.11", "version_normalized": "********"}, {"version": "2.4.10", "version_normalized": "********"}, {"version": "2.4.9", "version_normalized": "*******"}, {"version": "2.4.8", "version_normalized": "*******"}, {"version": "2.4.7", "version_normalized": "*******"}, {"version": "2.4.6", "version_normalized": "*******"}, {"version": "2.4.5", "version_normalized": "*******"}, {"version": "2.4.4", "version_normalized": "*******"}, {"version": "2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "a5dd7fd2ba6e8f6c6ea5a12db0605d3aa48af1e7"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/a5dd7fd2ba6e8f6c6ea5a12db0605d3aa48af1e7", "type": "zip", "shasum": "", "reference": "a5dd7fd2ba6e8f6c6ea5a12db0605d3aa48af1e7"}, "time": "2015-05-07T14:55:31+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.4.2"}}, {"version": "2.4.2", "version_normalized": "*******"}, {"version": "2.4.1", "version_normalized": "*******"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "cf05c5ba75606e47ffee91cedc72778da46f74c3"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/cf05c5ba75606e47ffee91cedc72778da46f74c3", "type": "zip", "shasum": "", "reference": "cf05c5ba75606e47ffee91cedc72778da46f74c3"}, "time": "2015-03-25T20:55:48+00:00", "extra": {"branch-alias": {"dev-master": "2.3-dev", "dev-develop": "2.4-dev"}}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.4.0rc7"}}, {"version": "2.4.0rc7", "version_normalized": "*******-RC7"}, {"version": "2.4.0rc6", "version_normalized": "*******-RC6"}, {"version": "2.4.0rc5", "version_normalized": "*******-RC5"}, {"version": "2.4.0rc4", "version_normalized": "*******-RC4"}, {"version": "2.4.0rc3", "version_normalized": "*******-RC3"}, {"version": "2.4.0rc2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "30948ddd6a30ceb43018fd7e531d8f7837e17a0b"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/30948ddd6a30ceb43018fd7e531d8f7837e17a0b", "type": "zip", "shasum": "", "reference": "30948ddd6a30ceb43018fd7e531d8f7837e17a0b"}, "time": "2015-03-23T18:29:14+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.4.0rc2"}}, {"version": "2.4.0rc1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "d13901fd634ba06cc5bdca9b39a7766d215ed687"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/d13901fd634ba06cc5bdca9b39a7766d215ed687", "type": "zip", "shasum": "", "reference": "d13901fd634ba06cc5bdca9b39a7766d215ed687"}, "time": "2015-03-19T20:27:11+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.4.0rc1"}}, {"version": "2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "2acb22aaf926f5a1934c6622d596f0b14872c3cd"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/2acb22aaf926f5a1934c6622d596f0b14872c3cd", "type": "zip", "shasum": "", "reference": "2acb22aaf926f5a1934c6622d596f0b14872c3cd"}, "time": "2015-02-10T14:55:30+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.3.9"}}, {"version": "2.3.8", "version_normalized": "*******"}, {"version": "2.3.7", "version_normalized": "*******"}, {"version": "2.3.6", "version_normalized": "*******"}, {"version": "2.3.5", "version_normalized": "*******"}, {"version": "2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "e79f79ab541fbe965e734268ab9c198c66750e71"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/e79f79ab541fbe965e734268ab9c198c66750e71", "type": "zip", "shasum": "", "reference": "e79f79ab541fbe965e734268ab9c198c66750e71"}, "time": "2015-01-13T22:22:54+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.3.4"}}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "f78c86701b33caddae091623708860dea0c2715f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/f78c86701b33caddae091623708860dea0c2715f", "type": "zip", "shasum": "", "reference": "f78c86701b33caddae091623708860dea0c2715f"}, "time": "2014-08-11T15:50:41+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.3.3"}}, {"version": "2.3.2", "version_normalized": "*******"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "6079302963d57f36a9ba92ed3f38b992997aa78d"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/6079302963d57f36a9ba92ed3f38b992997aa78d", "type": "zip", "shasum": "", "reference": "6079302963d57f36a9ba92ed3f38b992997aa78d"}, "time": "2014-04-15T13:59:53+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.3.1"}}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "426b5396e89e7da2db9678bc9a0b57865f84fe0f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/426b5396e89e7da2db9678bc9a0b57865f84fe0f", "type": "zip", "shasum": "", "reference": "426b5396e89e7da2db9678bc9a0b57865f84fe0f"}, "time": "2014-03-12T16:10:15+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev", "dev-develop": "2.3-dev"}}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.3.0"}}, {"version": "2.2.10", "version_normalized": "********", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "b42d784bd142e916058a9d68eeafcc10ff63c12e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/b42d784bd142e916058a9d68eeafcc10ff63c12e", "type": "zip", "shasum": "", "reference": "b42d784bd142e916058a9d68eeafcc10ff63c12e"}, "time": "2014-03-05T17:29:35+00:00", "require": {"php": ">=5.3.3"}, "suggest": {"zendframework/zend-servicemanager": "To support hydrator plugin manager usage", "zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-filter": "To support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.10"}}, {"version": "2.2.9", "version_normalized": "*******"}, {"version": "2.2.8", "version_normalized": "*******"}, {"version": "2.2.7", "version_normalized": "*******"}, {"version": "2.2.6", "version_normalized": "*******"}, {"version": "2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "fb3a072fdedc0608dbb07888dd5ac9ea5e4a78a1"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/fb3a072fdedc0608dbb07888dd5ac9ea5e4a78a1", "type": "zip", "shasum": "", "reference": "fb3a072fdedc0608dbb07888dd5ac9ea5e4a78a1"}, "time": "2013-10-31T15:35:17+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.5"}}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "505c38597ed08d04a6dee1d90bd49601aeedcf81"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/505c38597ed08d04a6dee1d90bd49601aeedcf81", "type": "zip", "shasum": "", "reference": "505c38597ed08d04a6dee1d90bd49601aeedcf81"}, "time": "2013-08-21T14:30:36+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.4"}}, {"version": "2.2.3", "version_normalized": "*******"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "e9553336c0903801cb25eb007fc1a64f571ac48b"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/e9553336c0903801cb25eb007fc1a64f571ac48b", "type": "zip", "shasum": "", "reference": "e9553336c0903801cb25eb007fc1a64f571ac48b"}, "time": "2013-07-22T16:34:19+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.2"}}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "0ec42df0169e3e6e57d8507b98125f7be17fe499"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/0ec42df0169e3e6e57d8507b98125f7be17fe499", "type": "zip", "shasum": "", "reference": "0ec42df0169e3e6e57d8507b98125f7be17fe499"}, "time": "2013-06-12T19:40:47+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.1"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "2c511692d0556fb9de178b39c08ddfe108488092"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/2c511692d0556fb9de178b39c08ddfe108488092", "type": "zip", "shasum": "", "reference": "2c511692d0556fb9de178b39c08ddfe108488092"}, "time": "2013-05-10T10:37:10+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.0rc3"}}, {"version": "2.2.0rc3", "version_normalized": "*******-RC3"}, {"version": "2.2.0rc2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "4f8ad50b9ed545879b727ed7ceadc3612da19de1"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/4f8ad50b9ed545879b727ed7ceadc3612da19de1", "type": "zip", "shasum": "", "reference": "4f8ad50b9ed545879b727ed7ceadc3612da19de1"}, "time": "2013-05-02T14:25:41+00:00", "suggest": {"pecl-weakref": "Implementation of weak references for Stdlib\\CallbackHandler", "zendframework/zend-servicemanager": "To support hydrator plugin manager usage", "zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-filter": "To support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.0rc2"}}, {"version": "2.2.0rc1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "c1ca981ddc871e7c3d84b21f084150d60d882681"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/c1ca981ddc871e7c3d84b21f084150d60d882681", "type": "zip", "shasum": "", "reference": "c1ca981ddc871e7c3d84b21f084150d60d882681"}, "time": "2013-05-01T21:44:23+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.2.0rc1"}}, {"version": "2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "0027339961ad3d49f91ee092e23f7269c18cb470"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/0027339961ad3d49f91ee092e23f7269c18cb470", "type": "zip", "shasum": "", "reference": "0027339961ad3d49f91ee092e23f7269c18cb470"}, "time": "2013-04-17T13:32:54+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev", "dev-develop": "2.2-dev"}}, "suggest": {"zendframework/zend-servicemanager": "To support hydrator plugin manager usage", "zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-filter": "To support naming strategy hydrator usage", "pecl-weakref": "Implementation of weak references for Stdlib\\CallbackHandler"}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.1.6"}}, {"version": "2.1.5", "version_normalized": "*******", "suggest": {"pecl-weakref": "Implementation of weak references for Stdlib\\CallbackHandler", "zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-servicemanager": "To support hydrator plugin manager usage", "zendframework/zend-filter": "To support naming strategy hydrator usage"}}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "573ff50552961782372c8f5b7be1eb83989d33c4"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/573ff50552961782372c8f5b7be1eb83989d33c4", "type": "zip", "shasum": "", "reference": "573ff50552961782372c8f5b7be1eb83989d33c4"}, "time": "2013-03-13T22:26:48+00:00", "autoload": {"files": ["Zend/Stdlib/compatibility/autoload.php"], "psr-4": {"Zend\\Stdlib\\": "src/"}}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.1.4"}}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "41f53b0833e35e8eb59d2ebf4fd5590cfb2e65c5"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/41f53b0833e35e8eb59d2ebf4fd5590cfb2e65c5", "type": "zip", "shasum": "", "reference": "41f53b0833e35e8eb59d2ebf4fd5590cfb2e65c5"}, "time": "2013-02-21T21:00:16+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.1.3"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "abd48b1336f80f08da52218f7157d32d5446d4f2"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/abd48b1336f80f08da52218f7157d32d5446d4f2", "type": "zip", "shasum": "", "reference": "abd48b1336f80f08da52218f7157d32d5446d4f2"}, "time": "2013-02-20T18:00:40+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.1.2"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "deac91b84675238a2eef5381380d642004e3351e"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/deac91b84675238a2eef5381380d642004e3351e", "type": "zip", "shasum": "", "reference": "deac91b84675238a2eef5381380d642004e3351e"}, "time": "2013-02-06T20:36:49+00:00", "autoload": {"files": ["compatibility/autoload.php"], "psr-4": {"Zend\\Stdlib\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "2.4-dev", "dev-develop": "2.5-dev"}}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.1.1"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "f094b731e7c106eb3ae4346a75eb6cc18c2b1617"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/f094b731e7c106eb3ae4346a75eb6cc18c2b1617", "type": "zip", "shasum": "", "reference": "f094b731e7c106eb3ae4346a75eb6cc18c2b1617"}, "time": "2013-01-29T22:45:48+00:00", "autoload": {"psr-4": {"Zend\\Stdlib\\": "src/"}}, "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.1.0"}}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "53f0837c7b97497d88130132d447531b1f881f6c"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/53f0837c7b97497d88130132d447531b1f881f6c", "type": "zip", "shasum": "", "reference": "53f0837c7b97497d88130132d447531b1f881f6c"}, "time": "2013-01-26T11:18:56+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.0.8"}}, {"version": "2.0.7", "version_normalized": "*******"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "fa0e3707d8f4cd567403fb141341b0bd1b0ad554"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/fa0e3707d8f4cd567403fb141341b0bd1b0ad554", "type": "zip", "shasum": "", "reference": "fa0e3707d8f4cd567403fb141341b0bd1b0ad554"}, "time": "2012-12-19T20:42:18+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.0.6"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "2b9635bdaa3b34c570dc12d0dec85554635b3917"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/2b9635bdaa3b34c570dc12d0dec85554635b3917", "type": "zip", "shasum": "", "reference": "2b9635bdaa3b34c570dc12d0dec85554635b3917"}, "time": "2012-11-20T18:49:53+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.0.5"}}, {"version": "2.0.4", "version_normalized": "*******"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-stdlib.git", "type": "git", "reference": "88ccceab9d3a1e218a83b2e7ae4b5787c82858ff"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/88ccceab9d3a1e218a83b2e7ae4b5787c82858ff", "type": "zip", "shasum": "", "reference": "88ccceab9d3a1e218a83b2e7ae4b5787c82858ff"}, "time": "2012-10-10T15:36:09+00:00", "support": {"issues": "https://github.com/zendframework/zend-stdlib/issues", "source": "https://github.com/zendframework/zend-stdlib/tree/release-2.0.3"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:12:29 GMT"}