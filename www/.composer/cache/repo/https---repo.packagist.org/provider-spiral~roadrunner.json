{"minified": "composer/2.0", "packages": {"spiral/roadrunner": [{"name": "spiral/roadrunner", "description": "RoadRunner: High-performance PHP application server and process manager written in Go and powered with plugins", "keywords": [], "homepage": "https://roadrunner.dev/", "version": "v2025.1.2", "version_normalized": "2025.1.2.0", "license": ["MIT"], "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "885c7087efa77380d5109901cf0a4888f611294b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/885c7087efa77380d5109901cf0a4888f611294b", "type": "zip", "shasum": "", "reference": "885c7087efa77380d5109901cf0a4888f611294b"}, "type": "metapackage", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2025.1.2"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-06-14T22:21:13+00:00"}, {"version": "v2025.1.1", "version_normalized": "2025.1.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "10289dc36b062bc7778992a80d7409b35194a159"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/10289dc36b062bc7778992a80d7409b35194a159", "type": "zip", "shasum": "", "reference": "10289dc36b062bc7778992a80d7409b35194a159"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2025.1.1"}, "time": "2025-05-01T14:08:49+00:00"}, {"version": "v2025.1.0", "version_normalized": "2025.1.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "7c9252915b0d62837e1735076d9785ae549d316a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/7c9252915b0d62837e1735076d9785ae549d316a", "type": "zip", "shasum": "", "reference": "7c9252915b0d62837e1735076d9785ae549d316a"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2025.1.0"}, "time": "2025-05-01T09:27:51+00:00"}, {"version": "v2024.3.5", "version_normalized": "2024.3.5.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6c147b5d5bf93e8cb7ab77e3d2e371c119043212"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6c147b5d5bf93e8cb7ab77e3d2e371c119043212", "type": "zip", "shasum": "", "reference": "6c147b5d5bf93e8cb7ab77e3d2e371c119043212"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.3.5"}, "time": "2025-02-27T17:17:08+00:00"}, {"version": "v2024.3.4", "version_normalized": "2024.3.4.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "513d9b7451bdea8099a96b9135b403f6c71e7141"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/513d9b7451bdea8099a96b9135b403f6c71e7141", "type": "zip", "shasum": "", "reference": "513d9b7451bdea8099a96b9135b403f6c71e7141"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.3.4"}, "time": "2025-02-13T19:26:52+00:00"}, {"version": "v2024.3.3", "version_normalized": "2024.3.3.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "e2d86914579217e717c1a8bfa7afe85ab6aeac6c"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/e2d86914579217e717c1a8bfa7afe85ab6aeac6c", "type": "zip", "shasum": "", "reference": "e2d86914579217e717c1a8bfa7afe85ab6aeac6c"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.3.3"}, "time": "2025-02-10T20:25:19+00:00"}, {"version": "v2024.3.2", "version_normalized": "2024.3.2.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "69f3cd4ca3d45589d7b0bdaa04fc176a4381a697"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/69f3cd4ca3d45589d7b0bdaa04fc176a4381a697", "type": "zip", "shasum": "", "reference": "69f3cd4ca3d45589d7b0bdaa04fc176a4381a697"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.3.2"}, "time": "2025-01-16T19:53:21+00:00"}, {"version": "v2024.3.1", "version_normalized": "2024.3.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "762d627ae06ffc7dd46fbbf3de8b13d7672a5cb3"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/762d627ae06ffc7dd46fbbf3de8b13d7672a5cb3", "type": "zip", "shasum": "", "reference": "762d627ae06ffc7dd46fbbf3de8b13d7672a5cb3"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.3.1"}, "time": "2024-12-20T02:12:40+00:00"}, {"version": "v2024.3.0", "version_normalized": "2024.3.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "9335c69e9e302e0063cff2b9739f140c9474864e"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/9335c69e9e302e0063cff2b9739f140c9474864e", "type": "zip", "shasum": "", "reference": "9335c69e9e302e0063cff2b9739f140c9474864e"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.3.0"}, "time": "2024-12-05T18:15:06+00:00"}, {"version": "v2024.2.1", "version_normalized": "2024.2.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "651cb40d300e5594b0d69648c20233795c99c9c9"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/651cb40d300e5594b0d69648c20233795c99c9c9", "type": "zip", "shasum": "", "reference": "651cb40d300e5594b0d69648c20233795c99c9c9"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.2.1"}, "time": "2024-09-12T16:07:07+00:00"}, {"version": "v2024.2.0", "version_normalized": "2024.2.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "9f770b1d89c50f332c197139b6fb279b0d47d1e3"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/9f770b1d89c50f332c197139b6fb279b0d47d1e3", "type": "zip", "shasum": "", "reference": "9f770b1d89c50f332c197139b6fb279b0d47d1e3"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.2.0"}, "time": "2024-07-25T13:11:40+00:00"}, {"version": "v2024.1.5", "version_normalized": "2024.1.5.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "2b72bebf7e8119a9d160cc10ec182eae022136ff"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/2b72bebf7e8119a9d160cc10ec182eae022136ff", "type": "zip", "shasum": "", "reference": "2b72bebf7e8119a9d160cc10ec182eae022136ff"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.1.5"}, "time": "2024-06-20T19:03:36+00:00"}, {"version": "v2024.1.4", "version_normalized": "2024.1.4.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "116621a11169e26bafe56e6ec4113c6f17bf73de"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/116621a11169e26bafe56e6ec4113c6f17bf73de", "type": "zip", "shasum": "", "reference": "116621a11169e26bafe56e6ec4113c6f17bf73de"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.1.4"}, "time": "2024-06-13T16:43:56+00:00"}, {"version": "v2024.1.3", "version_normalized": "2024.1.3.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "daea3729c0459503f5165c38e145a9fe0420e53c"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/daea3729c0459503f5165c38e145a9fe0420e53c", "type": "zip", "shasum": "", "reference": "daea3729c0459503f5165c38e145a9fe0420e53c"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.1.3"}, "time": "2024-06-06T14:36:51+00:00"}, {"version": "v2024.1.2", "version_normalized": "2024.1.2.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "8e80f7ce055a7c56dedbb6af281ec2531e0c403f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/8e80f7ce055a7c56dedbb6af281ec2531e0c403f", "type": "zip", "shasum": "", "reference": "8e80f7ce055a7c56dedbb6af281ec2531e0c403f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.1.2"}, "time": "2024-05-16T19:38:52+00:00"}, {"version": "v2024.1.1", "version_normalized": "2024.1.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "9d73edf285ec84beb6673d59823aa654b12aefff"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/9d73edf285ec84beb6673d59823aa654b12aefff", "type": "zip", "shasum": "", "reference": "9d73edf285ec84beb6673d59823aa654b12aefff"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.1.1"}, "time": "2024-05-02T16:33:48+00:00"}, {"version": "v2024.1.0", "version_normalized": "2024.1.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "c0d3df2b33bc2a9e9726dbbd73d7fe125ab9d045"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/c0d3df2b33bc2a9e9726dbbd73d7fe125ab9d045", "type": "zip", "shasum": "", "reference": "c0d3df2b33bc2a9e9726dbbd73d7fe125ab9d045"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2024.1.0"}, "time": "2024-04-11T16:32:14+00:00"}, {"version": "v2023.3.12", "version_normalized": "2023.3.12.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "9b5b65e00c6de7327142a8d689646f950fe00788"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/9b5b65e00c6de7327142a8d689646f950fe00788", "type": "zip", "shasum": "", "reference": "9b5b65e00c6de7327142a8d689646f950fe00788"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.12"}, "time": "2024-02-29T18:16:45+00:00"}, {"version": "v2023.3.11", "version_normalized": "2023.3.11.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "a39c549a196451fec13fde1266ea7b3dc3722f3a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/a39c549a196451fec13fde1266ea7b3dc3722f3a", "type": "zip", "shasum": "", "reference": "a39c549a196451fec13fde1266ea7b3dc3722f3a"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.11"}, "time": "2024-02-15T19:41:47+00:00"}, {"version": "v2023.3.10", "version_normalized": "2023.3.10.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "8cb2c6f4c5a52a867ccfb1768a4dbcd28998f04c"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/8cb2c6f4c5a52a867ccfb1768a4dbcd28998f04c", "type": "zip", "shasum": "", "reference": "8cb2c6f4c5a52a867ccfb1768a4dbcd28998f04c"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.10"}, "time": "2024-02-01T22:19:39+00:00"}, {"version": "v2023.3.9", "version_normalized": "2023.3.9.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "362f643b79eeac05e27e6dd3b42aaddf405f49f6"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/362f643b79eeac05e27e6dd3b42aaddf405f49f6", "type": "zip", "shasum": "", "reference": "362f643b79eeac05e27e6dd3b42aaddf405f49f6"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.9"}, "time": "2024-01-11T13:35:47+00:00"}, {"version": "v2023.3.8", "version_normalized": "2023.3.8.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "80eff8aa42c59bc2d2cc9a2fab4e02398fd94994"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/80eff8aa42c59bc2d2cc9a2fab4e02398fd94994", "type": "zip", "shasum": "", "reference": "80eff8aa42c59bc2d2cc9a2fab4e02398fd94994"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.8"}, "time": "2023-12-14T15:48:20+00:00"}, {"version": "v2023.3.7", "version_normalized": "2023.3.7.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6c15487972f1d070025341472829471f3a604871"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6c15487972f1d070025341472829471f3a604871", "type": "zip", "shasum": "", "reference": "6c15487972f1d070025341472829471f3a604871"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.7"}, "time": "2023-11-30T19:08:45+00:00"}, {"version": "v2023.3.6", "version_normalized": "2023.3.6.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "fa7db309156d5090ac92a041416b387547df982e"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/fa7db309156d5090ac92a041416b387547df982e", "type": "zip", "shasum": "", "reference": "fa7db309156d5090ac92a041416b387547df982e"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.6"}, "time": "2023-11-23T16:23:27+00:00"}, {"version": "v2023.3.5", "version_normalized": "2023.3.5.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "2c69fed9020a336c89ab2c25f897f5738d4f59d3"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/2c69fed9020a336c89ab2c25f897f5738d4f59d3", "type": "zip", "shasum": "", "reference": "2c69fed9020a336c89ab2c25f897f5738d4f59d3"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.5"}, "time": "2023-11-16T13:58:20+00:00"}, {"version": "v2023.3.4", "version_normalized": "2023.3.4.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "2f2aeb95ed35600bd43d400f0d3c5414f79d78b2"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/2f2aeb95ed35600bd43d400f0d3c5414f79d78b2", "type": "zip", "shasum": "", "reference": "2f2aeb95ed35600bd43d400f0d3c5414f79d78b2"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.4"}, "time": "2023-11-09T16:13:48+00:00"}, {"version": "v2023.3.3", "version_normalized": "2023.3.3.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "b5acfd6212252c1977990e88f416b2d813939a03"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/b5acfd6212252c1977990e88f416b2d813939a03", "type": "zip", "shasum": "", "reference": "b5acfd6212252c1977990e88f416b2d813939a03"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.3"}, "time": "2023-10-26T17:36:10+00:00"}, {"version": "v2023.3.2", "version_normalized": "2023.3.2.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "093f7c83951a7d72cb595a93a535f449c67de17f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/093f7c83951a7d72cb595a93a535f449c67de17f", "type": "zip", "shasum": "", "reference": "093f7c83951a7d72cb595a93a535f449c67de17f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.2"}, "time": "2023-10-19T16:21:00+00:00"}, {"version": "v2023.3.1", "version_normalized": "2023.3.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "99cbb8923e07c285d8540d68c72f4a04dfa09d32"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/99cbb8923e07c285d8540d68c72f4a04dfa09d32", "type": "zip", "shasum": "", "reference": "99cbb8923e07c285d8540d68c72f4a04dfa09d32"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.1"}, "time": "2023-10-12T13:43:50+00:00"}, {"version": "v2023.3.0", "version_normalized": "2023.3.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "90544d7075bee888c7cc08acb608b62e646e1842"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/90544d7075bee888c7cc08acb608b62e646e1842", "type": "zip", "shasum": "", "reference": "90544d7075bee888c7cc08acb608b62e646e1842"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.0"}, "time": "2023-10-05T21:53:45+00:00"}, {"version": "v2023.3.0-rc.1", "version_normalized": "2023.3.0.0-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "4cd88bb591f277a212acc127165cce95df07c944"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/4cd88bb591f277a212acc127165cce95df07c944", "type": "zip", "shasum": "", "reference": "4cd88bb591f277a212acc127165cce95df07c944"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.0-rc.1"}, "time": "2023-09-14T17:14:37+00:00"}, {"version": "v2023.3.0-beta.2", "version_normalized": "2023.3.0.0-beta2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "2041c0d6d1043dc5ec7ef8c1bbd8b45c017231a3"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/2041c0d6d1043dc5ec7ef8c1bbd8b45c017231a3", "type": "zip", "shasum": "", "reference": "2041c0d6d1043dc5ec7ef8c1bbd8b45c017231a3"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.0-beta.2"}, "time": "2023-08-31T20:19:49+00:00"}, {"version": "v2023.3.0-beta.1", "version_normalized": "2023.3.0.0-beta1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "9f15de1036fd084eb5ec74e22cfabf82ab07985b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/9f15de1036fd084eb5ec74e22cfabf82ab07985b", "type": "zip", "shasum": "", "reference": "9f15de1036fd084eb5ec74e22cfabf82ab07985b"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.3.0-beta.1"}, "time": "2023-08-18T17:27:24+00:00"}, {"version": "v2023.2.2", "version_normalized": "2023.2.2.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "ed31539895f2c02cb0186eb8eca9ead8eb2c382d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/ed31539895f2c02cb0186eb8eca9ead8eb2c382d", "type": "zip", "shasum": "", "reference": "ed31539895f2c02cb0186eb8eca9ead8eb2c382d"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.2.2"}, "time": "2023-08-10T16:28:24+00:00"}, {"version": "v2023.2.1", "version_normalized": "2023.2.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "923fec7ebd3019fd5cb5fc6f00ac8e47cc7a18f6"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/923fec7ebd3019fd5cb5fc6f00ac8e47cc7a18f6", "type": "zip", "shasum": "", "reference": "923fec7ebd3019fd5cb5fc6f00ac8e47cc7a18f6"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.2.1"}, "time": "2023-07-27T16:59:14+00:00"}, {"version": "v2023.2.0", "version_normalized": "2023.2.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f90d1f0770de7be7bd3835fa9309914c3f85a2b0"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f90d1f0770de7be7bd3835fa9309914c3f85a2b0", "type": "zip", "shasum": "", "reference": "f90d1f0770de7be7bd3835fa9309914c3f85a2b0"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.2.0"}, "time": "2023-07-06T18:48:26+00:00"}, {"version": "v2023.2.0-beta.3", "version_normalized": "2023.2.0.0-beta3", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f156c45a3003a31288fbc6528022fe8257fd478f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f156c45a3003a31288fbc6528022fe8257fd478f", "type": "zip", "shasum": "", "reference": "f156c45a3003a31288fbc6528022fe8257fd478f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.2.0-beta.3"}, "time": "2023-06-22T20:04:16+00:00"}, {"version": "v2023.2.0-beta.2", "version_normalized": "2023.2.0.0-beta2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "0017447bccb46664d6bbc28aee2c60ecf2ef71e8"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/0017447bccb46664d6bbc28aee2c60ecf2ef71e8", "type": "zip", "shasum": "", "reference": "0017447bccb46664d6bbc28aee2c60ecf2ef71e8"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.2.0-beta.2"}, "time": "2023-06-22T19:57:37+00:00"}, {"version": "v2023.2.0-beta.1", "version_normalized": "2023.2.0.0-beta1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "41a9ba1dbac00b6102a417873491f62fb357a759"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/41a9ba1dbac00b6102a417873491f62fb357a759", "type": "zip", "shasum": "", "reference": "41a9ba1dbac00b6102a417873491f62fb357a759"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.2.0-beta.1"}, "time": "2023-06-22T16:57:14+00:00"}, {"version": "v2023.1.5", "version_normalized": "2023.1.5.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "8683c0b03e9e9e52b30f7e7ad354d13ff17841b4"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/8683c0b03e9e9e52b30f7e7ad354d13ff17841b4", "type": "zip", "shasum": "", "reference": "8683c0b03e9e9e52b30f7e7ad354d13ff17841b4"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.5"}, "time": "2023-06-08T14:35:25+00:00"}, {"version": "v2023.1.4", "version_normalized": "2023.1.4.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "d1a39a542cecf3ecedb4acf5978892e38bf15137"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/d1a39a542cecf3ecedb4acf5978892e38bf15137", "type": "zip", "shasum": "", "reference": "d1a39a542cecf3ecedb4acf5978892e38bf15137"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.4"}, "time": "2023-05-25T10:57:00+00:00"}, {"version": "v2023.1.3", "version_normalized": "2023.1.3.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "cce43ad827dcc3316d38965cd802fbb40bd7ef2a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/cce43ad827dcc3316d38965cd802fbb40bd7ef2a", "type": "zip", "shasum": "", "reference": "cce43ad827dcc3316d38965cd802fbb40bd7ef2a"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.3"}, "time": "2023-05-11T12:23:02+00:00"}, {"version": "v2023.1.2", "version_normalized": "2023.1.2.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "0f7076a620a09e2a99a670c879a862d9f19080dd"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/0f7076a620a09e2a99a670c879a862d9f19080dd", "type": "zip", "shasum": "", "reference": "0f7076a620a09e2a99a670c879a862d9f19080dd"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.2"}, "time": "2023-05-04T13:10:14+00:00"}, {"version": "v2023.1.1", "version_normalized": "2023.1.1.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "aa1a7b82dedd9b768b0b29907db53f6e0821af20"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/aa1a7b82dedd9b768b0b29907db53f6e0821af20", "type": "zip", "shasum": "", "reference": "aa1a7b82dedd9b768b0b29907db53f6e0821af20"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.1"}, "time": "2023-04-20T12:13:21+00:00"}, {"version": "v2023.1.0", "version_normalized": "2023.1.0.0", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "2d616033fe4df1c297864ac7bddfafa354843a9f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/2d616033fe4df1c297864ac7bddfafa354843a9f", "type": "zip", "shasum": "", "reference": "2d616033fe4df1c297864ac7bddfafa354843a9f"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.0"}, "time": "2023-04-13T15:05:54+00:00"}, {"version": "v2023.1.0-rc.2", "version_normalized": "2023.1.0.0-RC2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "af0845a33b1483a2afdb6e30cd2aa26c9a16017e"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/af0845a33b1483a2afdb6e30cd2aa26c9a16017e", "type": "zip", "shasum": "", "reference": "af0845a33b1483a2afdb6e30cd2aa26c9a16017e"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.0-rc.2"}, "time": "2023-04-06T09:26:11+00:00"}, {"version": "v2023.1.0-rc.1", "version_normalized": "2023.1.0.0-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "1c718eb7f002aa09ee3556b3df2c03043e31642d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/1c718eb7f002aa09ee3556b3df2c03043e31642d", "type": "zip", "shasum": "", "reference": "1c718eb7f002aa09ee3556b3df2c03043e31642d"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.0-rc.1"}, "time": "2023-03-24T13:43:49+00:00"}, {"version": "v2023.1.0-beta.1", "version_normalized": "2023.1.0.0-beta1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "1bf2711022af56bb6c4a205a55165519e52902fc"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/1bf2711022af56bb6c4a205a55165519e52902fc", "type": "zip", "shasum": "", "reference": "1bf2711022af56bb6c4a205a55165519e52902fc"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.0-beta.1"}, "time": "2023-03-16T12:57:21+00:00"}, {"version": "v2023.1.0-alpha.2", "version_normalized": "2023.1.0.0-alpha2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "92fc954a0288e9f1ff3948a169edb270867f5d70"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/92fc954a0288e9f1ff3948a169edb270867f5d70", "type": "zip", "shasum": "", "reference": "92fc954a0288e9f1ff3948a169edb270867f5d70"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.0-alpha.2"}, "time": "2023-02-16T19:50:52+00:00"}, {"version": "v2023.1.0-alpha.1", "version_normalized": "2023.1.0.0-alpha1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f8fb555f8ee18d61868c8fa1f794191276372337"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f8fb555f8ee18d61868c8fa1f794191276372337", "type": "zip", "shasum": "", "reference": "f8fb555f8ee18d61868c8fa1f794191276372337"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2023.1.0-alpha.1"}, "time": "2023-01-26T13:19:09+00:00"}, {"version": "v2.12.3", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "18f526ad9eef19b57bdf1ad4b0252683f514c718"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/18f526ad9eef19b57bdf1ad4b0252683f514c718", "type": "zip", "shasum": "", "reference": "18f526ad9eef19b57bdf1ad4b0252683f514c718"}, "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.3"}, "time": "2023-02-16T13:00:26+00:00", "require": {"spiral/roadrunner-worker": "^2.0", "spiral/roadrunner-cli": "^2.0", "spiral/roadrunner-http": "^2.0"}}, {"homepage": "", "version": "v2.12.2", "version_normalized": "********", "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "c794a7ab2ae828af8068776b9dfcdcce5299d855"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/c794a7ab2ae828af8068776b9dfcdcce5299d855", "type": "zip", "shasum": "", "reference": "c794a7ab2ae828af8068776b9dfcdcce5299d855"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.2"}, "funding": [], "time": "2023-01-12T11:55:21+00:00"}, {"version": "v2.12.2-alpha.2", "version_normalized": "********-alpha2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f6bc8835dc4d996992b6d4a2751ac10dc6138e4f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f6bc8835dc4d996992b6d4a2751ac10dc6138e4f", "type": "zip", "shasum": "", "reference": "f6bc8835dc4d996992b6d4a2751ac10dc6138e4f"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.2-alpha.2"}, "time": "2022-12-30T00:31:58+00:00"}, {"version": "v2.12.2-alpha.1", "version_normalized": "********-alpha1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "33cec44ae130462433cee9062d06797ae8ce537f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/33cec44ae130462433cee9062d06797ae8ce537f", "type": "zip", "shasum": "", "reference": "33cec44ae130462433cee9062d06797ae8ce537f"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.2-alpha.1"}, "time": "2022-12-22T18:03:31+00:00"}, {"version": "v2.12.1", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "ef1f3159ac2a16ddf0119afcd61df07a51cc8b33"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/ef1f3159ac2a16ddf0119afcd61df07a51cc8b33", "type": "zip", "shasum": "", "reference": "ef1f3159ac2a16ddf0119afcd61df07a51cc8b33"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.1"}, "time": "2022-12-01T11:49:48+00:00"}, {"version": "v2.12.1-rc.1", "version_normalized": "********-RC1", "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.1-rc.1"}}, {"version": "v2.12.0", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "33bebe596b6c920634ec9386d818b912939de94b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/33bebe596b6c920634ec9386d818b912939de94b", "type": "zip", "shasum": "", "reference": "33bebe596b6c920634ec9386d818b912939de94b"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.0"}, "time": "2022-11-24T14:32:20+00:00"}, {"version": "v2.12.0-rc.1", "version_normalized": "********-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f1a9fcae8d5b2ecb6533d16956321672dc55af27"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f1a9fcae8d5b2ecb6533d16956321672dc55af27", "type": "zip", "shasum": "", "reference": "f1a9fcae8d5b2ecb6533d16956321672dc55af27"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.0-rc.1"}, "time": "2022-11-12T17:36:25+00:00"}, {"version": "v2.12.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "e0f445279760d0ec1d230e6cc179664246642501"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/e0f445279760d0ec1d230e6cc179664246642501", "type": "zip", "shasum": "", "reference": "e0f445279760d0ec1d230e6cc179664246642501"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.0-beta.1"}, "time": "2022-11-03T13:48:05+00:00"}, {"version": "v2.12.0-alpha.1", "version_normalized": "********-alpha1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "4abde012b9ecacfe40339c2633fa792e6215199a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/4abde012b9ecacfe40339c2633fa792e6215199a", "type": "zip", "shasum": "", "reference": "4abde012b9ecacfe40339c2633fa792e6215199a"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.12.0-alpha.1"}, "time": "2022-10-14T09:34:35+00:00"}, {"version": "v2.11.4", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "bee07c00a8954945156ca3e3de9c032b530abd01"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/bee07c00a8954945156ca3e3de9c032b530abd01", "type": "zip", "shasum": "", "reference": "bee07c00a8954945156ca3e3de9c032b530abd01"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.4"}, "time": "2022-10-06T14:25:16+00:00"}, {"version": "v2.11.4-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "d847167c75b3e2cb197f4c2d0a32126dfe7fa0ae"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/d847167c75b3e2cb197f4c2d0a32126dfe7fa0ae", "type": "zip", "shasum": "", "reference": "d847167c75b3e2cb197f4c2d0a32126dfe7fa0ae"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.4-beta.1"}, "time": "2022-10-03T10:20:14+00:00"}, {"version": "v2.11.3", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f851d4b364456a5da960bbb0370cf6e24cb9f449"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f851d4b364456a5da960bbb0370cf6e24cb9f449", "type": "zip", "shasum": "", "reference": "f851d4b364456a5da960bbb0370cf6e24cb9f449"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.3"}, "time": "2022-09-29T21:37:40+00:00"}, {"version": "v2.11.3-rc.1", "version_normalized": "********-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "07610e3545c6c4f8dff2b7a936cec5c637c10954"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/07610e3545c6c4f8dff2b7a936cec5c637c10954", "type": "zip", "shasum": "", "reference": "07610e3545c6c4f8dff2b7a936cec5c637c10954"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.3-rc.1"}, "time": "2022-09-29T18:18:57+00:00"}, {"version": "v2.11.2", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "28b14c2c390e899c4ac4db9c8b28d0c7d444da3d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/28b14c2c390e899c4ac4db9c8b28d0c7d444da3d", "type": "zip", "shasum": "", "reference": "28b14c2c390e899c4ac4db9c8b28d0c7d444da3d"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.2"}, "time": "2022-09-15T08:17:58+00:00"}, {"version": "v2.11.1", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "ff29b8b2228f77c8a89d10b22eb6c2c8776f92fa"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/ff29b8b2228f77c8a89d10b22eb6c2c8776f92fa", "type": "zip", "shasum": "", "reference": "ff29b8b2228f77c8a89d10b22eb6c2c8776f92fa"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.1"}, "time": "2022-08-25T13:12:59+00:00"}, {"version": "v2.11.0", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6a57268eafbc1407e4b242d0458a6df659c523d1"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6a57268eafbc1407e4b242d0458a6df659c523d1", "type": "zip", "shasum": "", "reference": "6a57268eafbc1407e4b242d0458a6df659c523d1"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.0"}, "time": "2022-08-18T13:27:03+00:00"}, {"description": "RoadRunner: High-performance PHP application server, load-balancer and process manager written in Golang", "version": "v2.11.0-rc.1", "version_normalized": "********-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6ff9a526083d4c542bebf710249ff3e922582856"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6ff9a526083d4c542bebf710249ff3e922582856", "type": "zip", "shasum": "", "reference": "6ff9a526083d4c542bebf710249ff3e922582856"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.0-rc.1"}, "time": "2022-08-11T13:35:28+00:00"}, {"version": "v2.11.0-beta.3", "version_normalized": "********-beta3", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "311b3faa3f3bc4d247ab4753b30d7677b4e32ce9"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/311b3faa3f3bc4d247ab4753b30d7677b4e32ce9", "type": "zip", "shasum": "", "reference": "311b3faa3f3bc4d247ab4753b30d7677b4e32ce9"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.0-beta.3"}, "time": "2022-08-07T12:46:02+00:00"}, {"version": "v2.11.0-beta.2", "version_normalized": "********-beta2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f85dd414a6425f390445f817eb40671d51ff2645"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f85dd414a6425f390445f817eb40671d51ff2645", "type": "zip", "shasum": "", "reference": "f85dd414a6425f390445f817eb40671d51ff2645"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.0-beta.2"}, "time": "2022-07-26T00:26:04+00:00"}, {"version": "v2.11.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "757b3b6d32a629b56df58948e708b21fb5bed24f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/757b3b6d32a629b56df58948e708b21fb5bed24f", "type": "zip", "shasum": "", "reference": "757b3b6d32a629b56df58948e708b21fb5bed24f"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.11.0-beta.1"}, "time": "2022-07-18T14:42:32+00:00"}, {"version": "v2.10.7", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "18a7a98bcb483a680b6ebe7da8bb61e95329daf4"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/18a7a98bcb483a680b6ebe7da8bb61e95329daf4", "type": "zip", "shasum": "", "reference": "18a7a98bcb483a680b6ebe7da8bb61e95329daf4"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.7"}, "time": "2022-07-14T09:00:44+00:00"}, {"version": "v2.10.6", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "4e79622cf871e1ee4303875e470e455d430c9513"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/4e79622cf871e1ee4303875e470e455d430c9513", "type": "zip", "shasum": "", "reference": "4e79622cf871e1ee4303875e470e455d430c9513"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.6"}, "time": "2022-07-07T10:18:07+00:00"}, {"version": "v2.10.5", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "3996ba6d12f953f808408e276f69dfcf0950e906"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/3996ba6d12f953f808408e276f69dfcf0950e906", "type": "zip", "shasum": "", "reference": "3996ba6d12f953f808408e276f69dfcf0950e906"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.5"}, "time": "2022-06-23T20:54:36+00:00"}, {"version": "v2.10.4", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "eb6f264ff8741914a784eb259a07c961cb4e56bc"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/eb6f264ff8741914a784eb259a07c961cb4e56bc", "type": "zip", "shasum": "", "reference": "eb6f264ff8741914a784eb259a07c961cb4e56bc"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.4"}, "time": "2022-06-10T23:18:13+00:00"}, {"version": "v2.10.4-rc.1", "version_normalized": "********-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "a7383864697e7fdc6b824c0ccdcbbcd6491a5b8a"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/a7383864697e7fdc6b824c0ccdcbbcd6491a5b8a", "type": "zip", "shasum": "", "reference": "a7383864697e7fdc6b824c0ccdcbbcd6491a5b8a"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.4-rc.1"}, "time": "2022-06-09T08:59:52+00:00"}, {"version": "v2.10.3", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "8cb55ccd67478947a9420ba5293de120bae6418f"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/8cb55ccd67478947a9420ba5293de120bae6418f", "type": "zip", "shasum": "", "reference": "8cb55ccd67478947a9420ba5293de120bae6418f"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.3"}, "time": "2022-06-02T10:40:06+00:00"}, {"version": "v2.10.2", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "0e1ec33a2fb06c3b982740e20aa0e93373a9a708"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/0e1ec33a2fb06c3b982740e20aa0e93373a9a708", "type": "zip", "shasum": "", "reference": "0e1ec33a2fb06c3b982740e20aa0e93373a9a708"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.2"}, "time": "2022-05-26T12:02:20+00:00"}, {"version": "v2.10.1", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "aab5755b9e7729b7262d2daa359f45c05e6be834"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/aab5755b9e7729b7262d2daa359f45c05e6be834", "type": "zip", "shasum": "", "reference": "aab5755b9e7729b7262d2daa359f45c05e6be834"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.1"}, "time": "2022-05-19T10:19:25+00:00"}, {"version": "v2.10.0", "version_normalized": "********", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "8c32984d7c30fe913ba5f0f6eb727775e8540e0b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/8c32984d7c30fe913ba5f0f6eb727775e8540e0b", "type": "zip", "shasum": "", "reference": "8c32984d7c30fe913ba5f0f6eb727775e8540e0b"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0"}, "time": "2022-05-16T15:40:39+00:00"}, {"version": "v2.10.0-rc.7", "version_normalized": "********-RC7", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "96fdef5d03187ffe24cadd01e5cde026e509470d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/96fdef5d03187ffe24cadd01e5cde026e509470d", "type": "zip", "shasum": "", "reference": "96fdef5d03187ffe24cadd01e5cde026e509470d"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.7"}, "time": "2022-05-14T15:43:16+00:00"}, {"version": "v2.10.0-rc.6", "version_normalized": "********-RC6", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f06c2453460963b41e2ebf14b656b72112bde710"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f06c2453460963b41e2ebf14b656b72112bde710", "type": "zip", "shasum": "", "reference": "f06c2453460963b41e2ebf14b656b72112bde710"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.6"}, "time": "2022-05-13T08:32:01+00:00"}, {"version": "v2.10.0-rc.5", "version_normalized": "********-RC5", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "c8f821ff0e3f9c33be2181645b2551d2d4ebc60b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/c8f821ff0e3f9c33be2181645b2551d2d4ebc60b", "type": "zip", "shasum": "", "reference": "c8f821ff0e3f9c33be2181645b2551d2d4ebc60b"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.5"}, "time": "2022-05-12T19:30:33+00:00"}, {"version": "v2.10.0-rc.4", "version_normalized": "********-RC4", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "0b823ea7271645bb214c8a05ee02cb3d4b994872"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/0b823ea7271645bb214c8a05ee02cb3d4b994872", "type": "zip", "shasum": "", "reference": "0b823ea7271645bb214c8a05ee02cb3d4b994872"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.4"}, "time": "2022-05-12T10:16:40+00:00"}, {"version": "v2.10.0-rc.3", "version_normalized": "********-RC3", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "0f3024a0900681502626cfb16b351376e9a40c92"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/0f3024a0900681502626cfb16b351376e9a40c92", "type": "zip", "shasum": "", "reference": "0f3024a0900681502626cfb16b351376e9a40c92"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.3"}, "time": "2022-05-11T10:55:31+00:00"}, {"version": "v2.10.0-rc.2", "version_normalized": "********-RC2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "da42b6dbde7acddf276ed868ba9f36a860ef22d5"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/da42b6dbde7acddf276ed868ba9f36a860ef22d5", "type": "zip", "shasum": "", "reference": "da42b6dbde7acddf276ed868ba9f36a860ef22d5"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.2"}, "time": "2022-05-10T12:55:01+00:00"}, {"version": "v2.10.0-rc.1", "version_normalized": "********-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "689cfbfb5b044e0bb64872943ced75afaf74a808"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/689cfbfb5b044e0bb64872943ced75afaf74a808", "type": "zip", "shasum": "", "reference": "689cfbfb5b044e0bb64872943ced75afaf74a808"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-rc.1"}, "time": "2022-05-10T12:52:56+00:00"}, {"version": "v2.10.0-alpha.1", "version_normalized": "********-alpha1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "fdc584f219773138f2b1afdea7a9f861afafca3d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/fdc584f219773138f2b1afdea7a9f861afafca3d", "type": "zip", "shasum": "", "reference": "fdc584f219773138f2b1afdea7a9f861afafca3d"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.10.0-alpha.1"}, "time": "2022-04-13T17:14:21+00:00"}, {"version": "v2.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "c88ed1ea6a9f50e748b2e82a4c4c27b42cb3f7fb"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/c88ed1ea6a9f50e748b2e82a4c4c27b42cb3f7fb", "type": "zip", "shasum": "", "reference": "c88ed1ea6a9f50e748b2e82a4c4c27b42cb3f7fb"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.9.4"}, "time": "2022-05-06T16:40:14+00:00"}, {"version": "v2.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "53d0d0eff309436b50f9b6fb24d08e8258ee61b8"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/53d0d0eff309436b50f9b6fb24d08e8258ee61b8", "type": "zip", "shasum": "", "reference": "53d0d0eff309436b50f9b6fb24d08e8258ee61b8"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.9.3"}, "time": "2022-05-06T09:38:32+00:00"}, {"version": "v2.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6ae4b643add41ef93262898892fc617d1d862898"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6ae4b643add41ef93262898892fc617d1d862898", "type": "zip", "shasum": "", "reference": "6ae4b643add41ef93262898892fc617d1d862898"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.9.2"}, "time": "2022-04-28T10:25:47+00:00"}, {"version": "v2.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "71de185b717ec51dac9b6034f456a9fac6412d14"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/71de185b717ec51dac9b6034f456a9fac6412d14", "type": "zip", "shasum": "", "reference": "71de185b717ec51dac9b6034f456a9fac6412d14"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.9.1"}, "time": "2022-04-11T10:16:29+00:00"}, {"version": "v2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "a75413573099387bd03f91caefb0072f3fb9504d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/a75413573099387bd03f91caefb0072f3fb9504d", "type": "zip", "shasum": "", "reference": "a75413573099387bd03f91caefb0072f3fb9504d"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.9.0"}, "time": "2022-04-07T17:17:01+00:00"}, {"version": "v2.9.0-alpha.1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "c9fddf54ab3d02eb02be3013a22af0ac2547f57e"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/c9fddf54ab3d02eb02be3013a22af0ac2547f57e", "type": "zip", "shasum": "", "reference": "c9fddf54ab3d02eb02be3013a22af0ac2547f57e"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.9.0-alpha.1"}, "time": "2022-03-23T19:38:36+00:00"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "133e19f6952b1fe76ada4b903cb8ec13816827d5"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/133e19f6952b1fe76ada4b903cb8ec13816827d5", "type": "zip", "shasum": "", "reference": "133e19f6952b1fe76ada4b903cb8ec13816827d5"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.8"}, "time": "2022-03-31T10:39:11+00:00"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "de7b748278b9b626aa1dcf608b916a2b4e342f9b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/de7b748278b9b626aa1dcf608b916a2b4e342f9b", "type": "zip", "shasum": "", "reference": "de7b748278b9b626aa1dcf608b916a2b4e342f9b"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.7"}, "time": "2022-03-24T15:28:39+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "5c20f28797a2141013d9424c95c330387395c52d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/5c20f28797a2141013d9424c95c330387395c52d", "type": "zip", "shasum": "", "reference": "5c20f28797a2141013d9424c95c330387395c52d"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.6"}, "time": "2022-03-24T13:37:04+00:00"}, {"version": "v2.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "f1c4803f1d1e20516e3b6e55cf9497ad4b774c3c"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/f1c4803f1d1e20516e3b6e55cf9497ad4b774c3c", "type": "zip", "shasum": "", "reference": "f1c4803f1d1e20516e3b6e55cf9497ad4b774c3c"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.5"}, "time": "2022-03-23T11:48:51+00:00"}, {"version": "v2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "12c8c65b04a3afec5fa6c97d6a950046f22bb4bf"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/12c8c65b04a3afec5fa6c97d6a950046f22bb4bf", "type": "zip", "shasum": "", "reference": "12c8c65b04a3afec5fa6c97d6a950046f22bb4bf"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.4"}, "time": "2022-03-17T11:19:26+00:00"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "934517ef3cf53ffd444b54dde123d6a69d1bbfd9"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/934517ef3cf53ffd444b54dde123d6a69d1bbfd9", "type": "zip", "shasum": "", "reference": "934517ef3cf53ffd444b54dde123d6a69d1bbfd9"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.3"}, "time": "2022-03-13T12:09:04+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6c7f2cec8a2913d6152d3a44927353cd64f9d3c3"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6c7f2cec8a2913d6152d3a44927353cd64f9d3c3", "type": "zip", "shasum": "", "reference": "6c7f2cec8a2913d6152d3a44927353cd64f9d3c3"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.2"}, "time": "2022-02-21T16:54:07+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "6f690001175859149910eadc33f4306ba2e3015d"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/6f690001175859149910eadc33f4306ba2e3015d", "type": "zip", "shasum": "", "reference": "6f690001175859149910eadc33f4306ba2e3015d"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.1"}, "time": "2022-02-21T07:46:44+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "180876e189b4e5a78ee8efee803c58a3dfad3814"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/180876e189b4e5a78ee8efee803c58a3dfad3814", "type": "zip", "shasum": "", "reference": "180876e189b4e5a78ee8efee803c58a3dfad3814"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.0"}, "time": "2022-02-17T07:37:29+00:00"}, {"version": "v2.8.0-rc.2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "c73c9d8af4425d939132f24a310f28653335eb65"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/c73c9d8af4425d939132f24a310f28653335eb65", "type": "zip", "shasum": "", "reference": "c73c9d8af4425d939132f24a310f28653335eb65"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.0-rc.2"}, "time": "2022-02-16T18:19:42+00:00"}, {"version": "v2.8.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "fe1cb1759dc269a26363c2d4a275101674ae3e82"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/fe1cb1759dc269a26363c2d4a275101674ae3e82", "type": "zip", "shasum": "", "reference": "fe1cb1759dc269a26363c2d4a275101674ae3e82"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.8.0-rc.1"}, "time": "2022-02-16T18:01:19+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "879b519ba7c0bd92ae531b957f9fd34ee074c454"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/879b519ba7c0bd92ae531b957f9fd34ee074c454", "type": "zip", "shasum": "", "reference": "879b519ba7c0bd92ae531b957f9fd34ee074c454"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.9"}, "time": "2022-02-14T18:34:37+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "58b80d16560228d818d0a9e22c195f1ea81ebc27"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/58b80d16560228d818d0a9e22c195f1ea81ebc27", "type": "zip", "shasum": "", "reference": "58b80d16560228d818d0a9e22c195f1ea81ebc27"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.8"}, "time": "2022-02-12T09:39:24+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "7a4338b218e0cc0c417497145ee74cbed1377a8b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/7a4338b218e0cc0c417497145ee74cbed1377a8b", "type": "zip", "shasum": "", "reference": "7a4338b218e0cc0c417497145ee74cbed1377a8b"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.7"}, "time": "2022-02-10T17:47:29+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "340c47126a75228fbe38105b8fb7894430495963"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/340c47126a75228fbe38105b8fb7894430495963", "type": "zip", "shasum": "", "reference": "340c47126a75228fbe38105b8fb7894430495963"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.6"}, "time": "2022-02-06T10:01:07+00:00"}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "b3a4beed318afbf8bac6baaf17c055037476e767"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/b3a4beed318afbf8bac6baaf17c055037476e767", "type": "zip", "shasum": "", "reference": "b3a4beed318afbf8bac6baaf17c055037476e767"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.5"}, "time": "2022-02-04T22:35:35+00:00"}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "def609ffe7ec1cf510564f610f56a5e570d43168"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/def609ffe7ec1cf510564f610f56a5e570d43168", "type": "zip", "shasum": "", "reference": "def609ffe7ec1cf510564f610f56a5e570d43168"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.4"}, "time": "2022-01-21T22:49:04+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "b49a0b183b234efcbd5e58bea5433439ab037a84"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/b49a0b183b234efcbd5e58bea5433439ab037a84", "type": "zip", "shasum": "", "reference": "b49a0b183b234efcbd5e58bea5433439ab037a84"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.3"}, "time": "2022-01-19T07:21:55+00:00"}, {"version": "v2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "bd934bb4dcdcb65e183ad642de3071a712fd618b"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/bd934bb4dcdcb65e183ad642de3071a712fd618b", "type": "zip", "shasum": "", "reference": "bd934bb4dcdcb65e183ad642de3071a712fd618b"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.2"}, "time": "2022-01-17T17:18:39+00:00"}, {"version": "v2.7.2-rc.4", "version_normalized": "*******-RC4", "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.2-rc.4"}}, {"version": "v2.7.2-rc.3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "548a86bd77c81bb0602baa6cd00718893a495896"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/548a86bd77c81bb0602baa6cd00718893a495896", "type": "zip", "shasum": "", "reference": "548a86bd77c81bb0602baa6cd00718893a495896"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.2-rc.3"}, "time": "2022-01-17T17:09:45+00:00"}, {"version": "v2.7.2-rc.2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "7bccd274d6cbbf76ba2f512e907ee40c028cbb50"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/7bccd274d6cbbf76ba2f512e907ee40c028cbb50", "type": "zip", "shasum": "", "reference": "7bccd274d6cbbf76ba2f512e907ee40c028cbb50"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.2-rc.2"}, "time": "2022-01-17T17:06:38+00:00"}, {"version": "v2.7.2-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/roadrunner-server/roadrunner.git", "type": "git", "reference": "e03fec021b7b547622cfd31d967a1adf4eeae966"}, "dist": {"url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/e03fec021b7b547622cfd31d967a1adf4eeae966", "type": "zip", "shasum": "", "reference": "e03fec021b7b547622cfd31d967a1adf4eeae966"}, "support": {"issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2.7.2-rc.1"}, "time": "2022-01-17T16:59:26+00:00"}, {"version": "v2.7.1", "version_normalized": "*******", "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "13609dd03dd0d2fa85b9fb850be787bf4e2ea67f"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/13609dd03dd0d2fa85b9fb850be787bf4e2ea67f", "type": "zip", "shasum": "", "reference": "13609dd03dd0d2fa85b9fb850be787bf4e2ea67f"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.7.1"}, "time": "2022-01-12T22:16:43+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "7627cb3ba0fd42592ed36bdf4de5c5af1ffa9f18"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/7627cb3ba0fd42592ed36bdf4de5c5af1ffa9f18", "type": "zip", "shasum": "", "reference": "7627cb3ba0fd42592ed36bdf4de5c5af1ffa9f18"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.7.0"}, "time": "2022-01-12T14:16:33+00:00"}, {"version": "v2.7.0-rc.2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a191eac78092dda89dbcd19c7a3a171f6aafb71a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a191eac78092dda89dbcd19c7a3a171f6aafb71a", "type": "zip", "shasum": "", "reference": "a191eac78092dda89dbcd19c7a3a171f6aafb71a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.7.0-rc.2"}, "time": "2022-01-11T21:28:16+00:00"}, {"version": "v2.7.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e4ee005938a388de4e4bbb9fad097b563989e158"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e4ee005938a388de4e4bbb9fad097b563989e158", "type": "zip", "shasum": "", "reference": "e4ee005938a388de4e4bbb9fad097b563989e158"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.7.0-rc.1"}, "time": "2022-01-05T01:19:54+00:00"}, {"version": "v2.7.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "7b5d220f0f1be155d83d887cd4996bdf4394c570"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/7b5d220f0f1be155d83d887cd4996bdf4394c570", "type": "zip", "shasum": "", "reference": "7b5d220f0f1be155d83d887cd4996bdf4394c570"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.7.0-beta.1"}, "time": "2021-12-25T22:02:46+00:00"}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9cbb6be27ca0bd56eaa6db9a875830a8ce6110e8"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9cbb6be27ca0bd56eaa6db9a875830a8ce6110e8", "type": "zip", "shasum": "", "reference": "9cbb6be27ca0bd56eaa6db9a875830a8ce6110e8"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.3"}, "time": "2021-12-23T18:07:45+00:00"}, {"version": "v2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "31112495808ae37f38f7b514de1f40b8b8a75238"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/31112495808ae37f38f7b514de1f40b8b8a75238", "type": "zip", "shasum": "", "reference": "31112495808ae37f38f7b514de1f40b8b8a75238"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.2"}, "time": "2021-12-14T22:13:32+00:00"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "634c3535856fb58eee5416f425a10e1c0bf46178"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/634c3535856fb58eee5416f425a10e1c0bf46178", "type": "zip", "shasum": "", "reference": "634c3535856fb58eee5416f425a10e1c0bf46178"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.1"}, "time": "2021-12-14T11:15:45+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "c14e4fa638300ccd96bdedfb0200ed8378eebdf2"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/c14e4fa638300ccd96bdedfb0200ed8378eebdf2", "type": "zip", "shasum": "", "reference": "c14e4fa638300ccd96bdedfb0200ed8378eebdf2"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0"}, "time": "2021-11-30T12:12:28+00:00"}, {"version": "v2.6.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "06f1293149fad14e3a316d5f8f9264df7b938eb3"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/06f1293149fad14e3a316d5f8f9264df7b938eb3", "type": "zip", "shasum": "", "reference": "06f1293149fad14e3a316d5f8f9264df7b938eb3"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-rc.1"}, "time": "2021-11-25T12:55:34+00:00"}, {"version": "v2.6.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6c5e3ab6c01d31caa2d14930c188bae697c5cd48"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6c5e3ab6c01d31caa2d14930c188bae697c5cd48", "type": "zip", "shasum": "", "reference": "6c5e3ab6c01d31caa2d14930c188bae697c5cd48"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-beta.1"}, "time": "2021-11-23T16:02:45+00:00"}, {"version": "v2.6.0-alpha.5", "version_normalized": "*******-alpha5", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "ecdcee628744e7371eda50ab6f1d3c7c13e8d7c9"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/ecdcee628744e7371eda50ab6f1d3c7c13e8d7c9", "type": "zip", "shasum": "", "reference": "ecdcee628744e7371eda50ab6f1d3c7c13e8d7c9"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-alpha.5"}, "time": "2021-11-07T19:21:18+00:00"}, {"version": "v2.6.0-alpha.4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "c1c16ddb8717af5b19f45118e615241ac14a54d6"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/c1c16ddb8717af5b19f45118e615241ac14a54d6", "type": "zip", "shasum": "", "reference": "c1c16ddb8717af5b19f45118e615241ac14a54d6"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-alpha.4"}, "time": "2021-11-02T12:53:48+00:00"}, {"version": "v2.6.0-alpha.3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e4f4c7ac0c47231c220a190eef5db5baf956fbe1"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e4f4c7ac0c47231c220a190eef5db5baf956fbe1", "type": "zip", "shasum": "", "reference": "e4f4c7ac0c47231c220a190eef5db5baf956fbe1"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-alpha.3"}, "time": "2021-11-01T11:53:24+00:00"}, {"version": "v2.6.0-alpha.2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b78558d0af7f813f81de0338400f99291932347f"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b78558d0af7f813f81de0338400f99291932347f", "type": "zip", "shasum": "", "reference": "b78558d0af7f813f81de0338400f99291932347f"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-alpha.2"}, "time": "2021-10-30T20:33:06+00:00"}, {"version": "v2.6.0-alpha.1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "c8c3f9f113eae13aa37cf92043b288bb0c68a622"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/c8c3f9f113eae13aa37cf92043b288bb0c68a622", "type": "zip", "shasum": "", "reference": "c8c3f9f113eae13aa37cf92043b288bb0c68a622"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.6.0-alpha.1"}, "time": "2021-10-27T19:50:03+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "536c6300b9627138ef957c31a08bcd5339c21b30"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/536c6300b9627138ef957c31a08bcd5339c21b30", "type": "zip", "shasum": "", "reference": "536c6300b9627138ef957c31a08bcd5339c21b30"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.3"}, "time": "2021-11-07T19:22:37+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "2f727a7b3f1587c65eb5ab3a32287ee628cbbc99"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/2f727a7b3f1587c65eb5ab3a32287ee628cbbc99", "type": "zip", "shasum": "", "reference": "2f727a7b3f1587c65eb5ab3a32287ee628cbbc99"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.2"}, "time": "2021-11-07T17:43:33+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "160d0427e6dbc3065147f4b2a39950e2f6958408"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/160d0427e6dbc3065147f4b2a39950e2f6958408", "type": "zip", "shasum": "", "reference": "160d0427e6dbc3065147f4b2a39950e2f6958408"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.1"}, "time": "2021-11-07T13:27:10+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "160055c16d4c1ca1e0e19853cbb89ef3509c7556"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/160055c16d4c1ca1e0e19853cbb89ef3509c7556", "type": "zip", "shasum": "", "reference": "160055c16d4c1ca1e0e19853cbb89ef3509c7556"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0"}, "time": "2021-10-19T12:34:36+00:00"}, {"version": "v2.5.0-rc.2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "c1c4136e86bf71837c7995ff75d859b4b68f0616"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/c1c4136e86bf71837c7995ff75d859b4b68f0616", "type": "zip", "shasum": "", "reference": "c1c4136e86bf71837c7995ff75d859b4b68f0616"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-rc.2"}, "time": "2021-10-18T13:19:28+00:00"}, {"version": "v2.5.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "d5474764f095fc2d829654272d5b5bf3662d0241"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/d5474764f095fc2d829654272d5b5bf3662d0241", "type": "zip", "shasum": "", "reference": "d5474764f095fc2d829654272d5b5bf3662d0241"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-rc.1"}, "time": "2021-10-16T17:13:44+00:00"}, {"version": "v2.5.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0ffbcea1aea3cc63af3168c278c193792f01f656"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0ffbcea1aea3cc63af3168c278c193792f01f656", "type": "zip", "shasum": "", "reference": "0ffbcea1aea3cc63af3168c278c193792f01f656"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-beta.4"}, "time": "2021-10-14T14:12:47+00:00"}, {"version": "v2.5.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "85b9bbe25b4c382707f111f07b0990152cf36339"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/85b9bbe25b4c382707f111f07b0990152cf36339", "type": "zip", "shasum": "", "reference": "85b9bbe25b4c382707f111f07b0990152cf36339"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-beta.3"}, "time": "2021-10-11T16:00:25+00:00"}, {"version": "v2.5.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "7e650eb122ce74df43aa77ad1cb877858bfa416c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/7e650eb122ce74df43aa77ad1cb877858bfa416c", "type": "zip", "shasum": "", "reference": "7e650eb122ce74df43aa77ad1cb877858bfa416c"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-beta.2"}, "time": "2021-10-07T05:47:46+00:00"}, {"version": "v2.5.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "ccf08c9470453f498cc20bc2245004662690320e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/ccf08c9470453f498cc20bc2245004662690320e", "type": "zip", "shasum": "", "reference": "ccf08c9470453f498cc20bc2245004662690320e"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-beta.1"}, "time": "2021-10-01T12:03:37+00:00"}, {"version": "v2.5.0-alpha.2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "3581b45f237a3f7aa29591ceb2bf6f4a4642a2f5"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/3581b45f237a3f7aa29591ceb2bf6f4a4642a2f5", "type": "zip", "shasum": "", "reference": "3581b45f237a3f7aa29591ceb2bf6f4a4642a2f5"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-alpha.2"}, "time": "2021-09-16T18:46:50+00:00"}, {"version": "v2.5.0-alpha.1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "44d0b505031e8f042783792883021f21fcba5f72"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/44d0b505031e8f042783792883021f21fcba5f72", "type": "zip", "shasum": "", "reference": "44d0b505031e8f042783792883021f21fcba5f72"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.5.0-alpha.1"}, "time": "2021-09-16T14:35:58+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "54a1f22f2f297d4277b9bcba27b7fb821ffe7632"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/54a1f22f2f297d4277b9bcba27b7fb821ffe7632", "type": "zip", "shasum": "", "reference": "54a1f22f2f297d4277b9bcba27b7fb821ffe7632"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.4.1"}, "time": "2021-09-12T19:13:32+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6749db4a2d39fa70b426bcf50edf66a176c07f57"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6749db4a2d39fa70b426bcf50edf66a176c07f57", "type": "zip", "shasum": "", "reference": "6749db4a2d39fa70b426bcf50edf66a176c07f57"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.4.0"}, "time": "2021-09-02T17:09:01+00:00"}, {"version": "v2.4.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0437d1f58514f694ea86e8176e621c009cd510f9"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0437d1f58514f694ea86e8176e621c009cd510f9", "type": "zip", "shasum": "", "reference": "0437d1f58514f694ea86e8176e621c009cd510f9"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.4.0-rc.1"}, "time": "2021-08-31T14:20:33+00:00"}, {"version": "v2.4.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6992fcede46cd2f52566291e7936483376a9a454"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6992fcede46cd2f52566291e7936483376a9a454", "type": "zip", "shasum": "", "reference": "6992fcede46cd2f52566291e7936483376a9a454"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.4.0-beta.1"}, "time": "2021-08-20T11:38:40+00:00"}, {"version": "v2.4.0-alpha.1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a2d9f341ae1b49d50cb47e54a627581415823ab0"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a2d9f341ae1b49d50cb47e54a627581415823ab0", "type": "zip", "shasum": "", "reference": "a2d9f341ae1b49d50cb47e54a627581415823ab0"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.4.0-alpha.1"}, "time": "2021-08-12T12:42:37+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "de7271b734e2ab6958aef8c7e8917537fc0c654a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/de7271b734e2ab6958aef8c7e8917537fc0c654a", "type": "zip", "shasum": "", "reference": "de7271b734e2ab6958aef8c7e8917537fc0c654a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.2"}, "time": "2021-07-14T14:24:46+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "7681180b381cc718afe1462c9d7521ff2b83efd7"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/7681180b381cc718afe1462c9d7521ff2b83efd7", "type": "zip", "shasum": "", "reference": "7681180b381cc718afe1462c9d7521ff2b83efd7"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.1"}, "time": "2021-06-30T08:40:54+00:00"}, {"version": "v2.3.1-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "fc540f6029772ff51913b8ee3c082f8197010c52"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/fc540f6029772ff51913b8ee3c082f8197010c52", "type": "zip", "shasum": "", "reference": "fc540f6029772ff51913b8ee3c082f8197010c52"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.1-rc.1"}, "time": "2021-06-26T06:37:27+00:00"}, {"version": "v2.3.1-beta.6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e9249c7896331bab97a18a7ee0db17803fdd31fb"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e9249c7896331bab97a18a7ee0db17803fdd31fb", "type": "zip", "shasum": "", "reference": "e9249c7896331bab97a18a7ee0db17803fdd31fb"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.1-beta.6"}, "time": "2021-06-24T14:40:49+00:00"}, {"version": "v2.3.1-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "87d023d32feef5fe28c9bb65a796deb77d536b15"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/87d023d32feef5fe28c9bb65a796deb77d536b15", "type": "zip", "shasum": "", "reference": "87d023d32feef5fe28c9bb65a796deb77d536b15"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.1-beta.4"}, "time": "2021-06-21T06:54:52+00:00"}, {"version": "v2.3.1-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "25e0841c6aa5e2686da5b9f74e3d77d3814ff592"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/25e0841c6aa5e2686da5b9f74e3d77d3814ff592", "type": "zip", "shasum": "", "reference": "25e0841c6aa5e2686da5b9f74e3d77d3814ff592"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.1-beta.3"}, "time": "2021-06-16T12:53:40+00:00"}, {"version": "v2.3.1-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "aeaf67900cbd9dff5bd7318eb4d79e302331e457"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/aeaf67900cbd9dff5bd7318eb4d79e302331e457", "type": "zip", "shasum": "", "reference": "aeaf67900cbd9dff5bd7318eb4d79e302331e457"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.1-beta.1"}, "time": "2021-06-14T14:56:32+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "54c3553cc39df4eae92d1f2c8c428e625f32f41a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/54c3553cc39df4eae92d1f2c8c428e625f32f41a", "type": "zip", "shasum": "", "reference": "54c3553cc39df4eae92d1f2c8c428e625f32f41a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.0"}, "time": "2021-06-11T13:52:35+00:00"}, {"version": "v2.3.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b99bfbe21a0f44b1a16b9110d779719fc637127c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b99bfbe21a0f44b1a16b9110d779719fc637127c", "type": "zip", "shasum": "", "reference": "b99bfbe21a0f44b1a16b9110d779719fc637127c"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.0-beta.3"}, "time": "2021-06-09T17:26:18+00:00"}, {"version": "v2.3.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "4f3e16892479db4bd8280a46987f3105e46e5c96"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/4f3e16892479db4bd8280a46987f3105e46e5c96", "type": "zip", "shasum": "", "reference": "4f3e16892479db4bd8280a46987f3105e46e5c96"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.0-beta.2"}, "time": "2021-06-08T19:36:40+00:00"}, {"version": "v2.3.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9c01e7ab1548e1416598b702d63866fa6dc5707b"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9c01e7ab1548e1416598b702d63866fa6dc5707b", "type": "zip", "shasum": "", "reference": "9c01e7ab1548e1416598b702d63866fa6dc5707b"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.3.0-beta.1"}, "time": "2021-06-02T18:23:42+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e1ff9daead5033b537296ffb071e551b95af91ab"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e1ff9daead5033b537296ffb071e551b95af91ab", "type": "zip", "shasum": "", "reference": "e1ff9daead5033b537296ffb071e551b95af91ab"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.2.1"}, "time": "2021-05-13T17:15:00+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "bf28e244848b21709e142cd5e2fe07953e878f21"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/bf28e244848b21709e142cd5e2fe07953e878f21", "type": "zip", "shasum": "", "reference": "bf28e244848b21709e142cd5e2fe07953e878f21"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.2.0"}, "time": "2021-05-12T17:23:21+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "d4cd17271f3e825b594a28c3d8b6fbc6848f93cb"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/d4cd17271f3e825b594a28c3d8b6fbc6848f93cb", "type": "zip", "shasum": "", "reference": "d4cd17271f3e825b594a28c3d8b6fbc6848f93cb"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.1.1"}, "time": "2021-04-29T19:47:14+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "92d089e0ecedaa1ccdf9e2700b9cb2b61f307b1d"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/92d089e0ecedaa1ccdf9e2700b9cb2b61f307b1d", "type": "zip", "shasum": "", "reference": "92d089e0ecedaa1ccdf9e2700b9cb2b61f307b1d"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.1.0"}, "time": "2021-04-23T09:28:49+00:00"}, {"version": "v2.1.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "02cb030cec51a81c6b2f6d8351e66112eb2c9b7f"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/02cb030cec51a81c6b2f6d8351e66112eb2c9b7f", "type": "zip", "shasum": "", "reference": "02cb030cec51a81c6b2f6d8351e66112eb2c9b7f"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.1.0-beta.3"}, "time": "2021-04-20T12:52:27+00:00"}, {"version": "v2.1.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "3362f211f6358d60bea47ac2de4cc29a47373973"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/3362f211f6358d60bea47ac2de4cc29a47373973", "type": "zip", "shasum": "", "reference": "3362f211f6358d60bea47ac2de4cc29a47373973"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.1.0-beta.2"}, "time": "2021-04-20T07:06:45+00:00"}, {"version": "v2.1.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0d5ddfc3952b03c37fc728017fc33d2c2db3fbff"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0d5ddfc3952b03c37fc728017fc33d2c2db3fbff", "type": "zip", "shasum": "", "reference": "0d5ddfc3952b03c37fc728017fc33d2c2db3fbff"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.1.0-beta.1"}, "time": "2021-03-31T16:33:27+00:00"}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "5d6ccf85ce65944c5876abc97c3e1d2acf470828"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/5d6ccf85ce65944c5876abc97c3e1d2acf470828", "type": "zip", "shasum": "", "reference": "5d6ccf85ce65944c5876abc97c3e1d2acf470828"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.4"}, "time": "2021-04-06T13:07:33+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "5a6c8b22148d7e97d20cf94d4978557cf0faec37"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/5a6c8b22148d7e97d20cf94d4978557cf0faec37", "type": "zip", "shasum": "", "reference": "5a6c8b22148d7e97d20cf94d4978557cf0faec37"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.3"}, "time": "2021-03-29T10:33:06+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9daf91214875d4ee9ad52877e0293596a075fb51"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9daf91214875d4ee9ad52877e0293596a075fb51", "type": "zip", "shasum": "", "reference": "9daf91214875d4ee9ad52877e0293596a075fb51"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.2"}, "time": "2021-03-23T08:38:09+00:00"}, {"version": "v2.0.2-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a893ee1875d0e36f24198dc046275aaf5edbb418"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a893ee1875d0e36f24198dc046275aaf5edbb418", "type": "zip", "shasum": "", "reference": "a893ee1875d0e36f24198dc046275aaf5edbb418"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.2-beta.2"}, "time": "2021-03-20T08:02:39+00:00"}, {"version": "v2.0.2-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "584f9ad08cfe162f1c9a9e42802e0bd0a2284ea8"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/584f9ad08cfe162f1c9a9e42802e0bd0a2284ea8", "type": "zip", "shasum": "", "reference": "584f9ad08cfe162f1c9a9e42802e0bd0a2284ea8"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.2-beta.1"}, "time": "2021-03-16T15:23:50+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "f47028a810ffcbf0f30e3c0151649e7670cdc8ed"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/f47028a810ffcbf0f30e3c0151649e7670cdc8ed", "type": "zip", "shasum": "", "reference": "f47028a810ffcbf0f30e3c0151649e7670cdc8ed"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.1"}, "time": "2021-03-09T17:40:25+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a93b4c429129037988e74de25b94829b142f213e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a93b4c429129037988e74de25b94829b142f213e", "type": "zip", "shasum": "", "reference": "a93b4c429129037988e74de25b94829b142f213e"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0"}, "time": "2021-03-02T15:59:43+00:00"}, {"version": "v2.0.0-RC.4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b3cb0cd2428f1bfdd959ea8f3461e2992b6acdb2"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b3cb0cd2428f1bfdd959ea8f3461e2992b6acdb2", "type": "zip", "shasum": "", "reference": "b3cb0cd2428f1bfdd959ea8f3461e2992b6acdb2"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-RC.4"}, "time": "2021-02-24T11:16:59+00:00"}, {"version": "v2.0.0-RC.3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "51d0f10096614bb300c439d5f973a805987fe4be"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/51d0f10096614bb300c439d5f973a805987fe4be", "type": "zip", "shasum": "", "reference": "51d0f10096614bb300c439d5f973a805987fe4be"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-RC.3"}, "time": "2021-02-17T06:51:30+00:00"}, {"version": "v2.0.0-RC.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "8f6cafdc0948a5ea13bf9a811b576aa4b3ef7e4a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/8f6cafdc0948a5ea13bf9a811b576aa4b3ef7e4a", "type": "zip", "shasum": "", "reference": "8f6cafdc0948a5ea13bf9a811b576aa4b3ef7e4a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-RC.1"}, "time": "2021-02-11T11:06:10+00:00", "require": {"spiral/roadrunner-worker": ">=2.0"}}, {"version": "v2.0.0-beta.24", "version_normalized": "*******-beta24", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b9c9909b98c1b3e15421a4bcad9e8fcc01332d37"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b9c9909b98c1b3e15421a4bcad9e8fcc01332d37", "type": "zip", "shasum": "", "reference": "b9c9909b98c1b3e15421a4bcad9e8fcc01332d37"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta.24"}, "time": "2021-02-04T09:26:16+00:00"}, {"version": "v2.0.0-beta.22", "version_normalized": "*******-beta22", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "20a1a5d2eb26090e0eef0e6772330ee2a52526fa"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/20a1a5d2eb26090e0eef0e6772330ee2a52526fa", "type": "zip", "shasum": "", "reference": "20a1a5d2eb26090e0eef0e6772330ee2a52526fa"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta.22"}, "time": "2021-02-02T16:30:00+00:00"}, {"version": "v2.0.0-beta.21", "version_normalized": "*******-beta21", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0ee8c6ce423886c6a7776e9e3bfbed342178f94d"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0ee8c6ce423886c6a7776e9e3bfbed342178f94d", "type": "zip", "shasum": "", "reference": "0ee8c6ce423886c6a7776e9e3bfbed342178f94d"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta.21"}, "time": "2021-02-01T11:43:29+00:00"}, {"version": "v2.0.0-beta19", "version_normalized": "*******-beta19", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "408e5c38571e9426dd31c75deddfa6407276a9c5"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/408e5c38571e9426dd31c75deddfa6407276a9c5", "type": "zip", "shasum": "", "reference": "408e5c38571e9426dd31c75deddfa6407276a9c5"}, "type": "server", "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta19"}, "time": "2021-01-27T23:31:01+00:00", "bin": ["bin/rr"], "require": {"spiral/roadrunner-worker": ">=2.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "composer/package-versions-deprecated": "^1.8"}}, {"version": "v2.0.0-beta13", "version_normalized": "*******-beta13", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e9d6ed13fef70486b4edf2b59fdb592046cd25bc"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e9d6ed13fef70486b4edf2b59fdb592046cd25bc", "type": "zip", "shasum": "", "reference": "e9d6ed13fef70486b4edf2b59fdb592046cd25bc"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta13"}, "time": "2021-01-27T11:36:10+00:00"}, {"version": "v2.0.0-beta12", "version_normalized": "*******-beta12", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e2266b80db47444ba5858c736833a8a81b1361ad"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e2266b80db47444ba5858c736833a8a81b1361ad", "type": "zip", "shasum": "", "reference": "e2266b80db47444ba5858c736833a8a81b1361ad"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta12"}, "time": "2021-01-26T08:52:03+00:00"}, {"version": "v2.0.0-beta11", "version_normalized": "*******-beta11", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "29d6020a9e8a3713b22269ed946547c96c24d3da"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/29d6020a9e8a3713b22269ed946547c96c24d3da", "type": "zip", "shasum": "", "reference": "29d6020a9e8a3713b22269ed946547c96c24d3da"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-beta11"}, "time": "2021-01-22T08:04:09+00:00"}, {"description": "High-performance PHP application server, load-balancer and process manager written in Golang", "version": "v2.0.0-alpha22", "version_normalized": "*******-alpha22", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b5020bfce6b5362400cb9b578fe32c1a6ed5d61a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b5020bfce6b5362400cb9b578fe32c1a6ed5d61a", "type": "zip", "shasum": "", "reference": "b5020bfce6b5362400cb9b578fe32c1a6ed5d61a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha22"}, "time": "2020-11-27T08:19:27+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\": "src/"}}, "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}, "require-dev": {"phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha21", "version_normalized": "*******-alpha21", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "46ae5dcc22d971b0f909bce23ec8fdef26811ed6"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/46ae5dcc22d971b0f909bce23ec8fdef26811ed6", "type": "zip", "shasum": "", "reference": "46ae5dcc22d971b0f909bce23ec8fdef26811ed6"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha21"}, "time": "2020-11-26T13:21:50+00:00"}, {"version": "v2.0.0-alpha20", "version_normalized": "*******-alpha20", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0a48a027642a34c560717526c55f70b7260d678c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0a48a027642a34c560717526c55f70b7260d678c", "type": "zip", "shasum": "", "reference": "0a48a027642a34c560717526c55f70b7260d678c"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha20"}, "time": "2020-11-18T08:18:48+00:00", "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}, "require-dev": {"psr/http-factory": "^1.0", "psr/http-message": "^1.0", "phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha19", "version_normalized": "*******-alpha19", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "561003b0b66a4f767782552a9282d824b08831be"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/561003b0b66a4f767782552a9282d824b08831be", "type": "zip", "shasum": "", "reference": "561003b0b66a4f767782552a9282d824b08831be"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha19"}, "time": "2020-10-21T11:03:49+00:00", "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}, "require-dev": {"phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha18", "version_normalized": "*******-alpha18", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6236aac37bd1661b20400689f66d1e92283c5111"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6236aac37bd1661b20400689f66d1e92283c5111", "type": "zip", "shasum": "", "reference": "6236aac37bd1661b20400689f66d1e92283c5111"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha18"}, "time": "2020-11-16T12:46:08+00:00", "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}, "require-dev": {"psr/http-factory": "^1.0", "psr/http-message": "^1.0", "phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha17", "version_normalized": "*******-alpha17", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0874bcb2f6b284a940ba4f3507eb8c4619c27868"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0874bcb2f6b284a940ba4f3507eb8c4619c27868", "type": "zip", "shasum": "", "reference": "0874bcb2f6b284a940ba4f3507eb8c4619c27868"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha17"}, "time": "2020-11-09T12:11:10+00:00"}, {"version": "v2.0.0-alpha16", "version_normalized": "*******-alpha16", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "561003b0b66a4f767782552a9282d824b08831be"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/561003b0b66a4f767782552a9282d824b08831be", "type": "zip", "shasum": "", "reference": "561003b0b66a4f767782552a9282d824b08831be"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha19"}, "time": "2020-10-21T11:03:49+00:00", "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}, "require-dev": {"phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha15", "version_normalized": "*******-alpha15", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9fbe7726dd55cfedda724b7644e1b6bf7c1a6cb4"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9fbe7726dd55cfedda724b7644e1b6bf7c1a6cb4", "type": "zip", "shasum": "", "reference": "9fbe7726dd55cfedda724b7644e1b6bf7c1a6cb4"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha15"}, "time": "2020-11-05T14:58:23+00:00", "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}, "require-dev": {"psr/http-factory": "^1.0", "psr/http-message": "^1.0", "phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha14", "version_normalized": "*******-alpha14", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "de53c3ad12a8afb379610f87399373c4d0626ef6"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/de53c3ad12a8afb379610f87399373c4d0626ef6", "type": "zip", "shasum": "", "reference": "de53c3ad12a8afb379610f87399373c4d0626ef6"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha14"}, "time": "2020-10-28T13:05:39+00:00"}, {"version": "v2.0.0-alpha13", "version_normalized": "*******-alpha13", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6f39542d75d0da1e0ff09906bdd340f855a409af"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6f39542d75d0da1e0ff09906bdd340f855a409af", "type": "zip", "shasum": "", "reference": "6f39542d75d0da1e0ff09906bdd340f855a409af"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha13"}, "time": "2020-10-19T11:06:26+00:00", "require": {"php": "^7.2", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}, "require-dev": {"phpstan/phpstan": "~0.12"}}, {"version": "v2.0.0-alpha12", "version_normalized": "*******-alpha12", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6f7e126d6ea25c83b4df50048d1073c6e76ff338"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6f7e126d6ea25c83b4df50048d1073c6e76ff338", "type": "zip", "shasum": "", "reference": "6f7e126d6ea25c83b4df50048d1073c6e76ff338"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha12"}, "time": "2020-10-17T14:31:27+00:00"}, {"version": "v2.0.0-alpha11", "version_normalized": "*******-alpha11", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9e572cecbade5673dc93e1240e9573f093f64bd6"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9e572cecbade5673dc93e1240e9573f093f64bd6", "type": "zip", "shasum": "", "reference": "9e572cecbade5673dc93e1240e9573f093f64bd6"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha11"}, "time": "2020-10-17T14:27:58+00:00"}, {"version": "v2.0.0-alpha10", "version_normalized": "*******-alpha10", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "f0ac12cb7870c71342bf0f2cee6883722ddd9ea1"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/f0ac12cb7870c71342bf0f2cee6883722ddd9ea1", "type": "zip", "shasum": "", "reference": "f0ac12cb7870c71342bf0f2cee6883722ddd9ea1"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha10"}, "time": "2020-10-17T14:18:24+00:00"}, {"version": "v2.0.0-alpha7", "version_normalized": "*******-alpha7"}, {"version": "v2.0.0-alpha6", "version_normalized": "*******-alpha6", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "16fbf3104c3c34bd9355593052b686acd26a8efe"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/16fbf3104c3c34bd9355593052b686acd26a8efe", "type": "zip", "shasum": "", "reference": "16fbf3104c3c34bd9355593052b686acd26a8efe"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha6"}, "time": "2020-10-16T09:53:03+00:00"}, {"version": "v2.0.0-alpha5", "version_normalized": "*******-alpha5", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "4fd2e882674e9e8b591ec182155b4ba90223af21"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/4fd2e882674e9e8b591ec182155b4ba90223af21", "type": "zip", "shasum": "", "reference": "4fd2e882674e9e8b591ec182155b4ba90223af21"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha5"}, "time": "2020-10-16T09:43:02+00:00"}, {"version": "v2.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "176097759cd98927b3781bb9aeb304174d7e948e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/176097759cd98927b3781bb9aeb304174d7e948e", "type": "zip", "shasum": "", "reference": "176097759cd98927b3781bb9aeb304174d7e948e"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha3"}, "time": "2020-10-13T11:01:17+00:00"}, {"version": "v2.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "414195987e1ae2b0ea940dcf1a5b8178453c4382"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/414195987e1ae2b0ea940dcf1a5b8178453c4382", "type": "zip", "shasum": "", "reference": "414195987e1ae2b0ea940dcf1a5b8178453c4382"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha2"}, "time": "2020-10-02T11:35:57+00:00"}, {"version": "v2.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0dc44d54cfcc9dd3fa09a41136f35a9a8d26b994"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0dc44d54cfcc9dd3fa09a41136f35a9a8d26b994", "type": "zip", "shasum": "", "reference": "0dc44d54cfcc9dd3fa09a41136f35a9a8d26b994"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha1"}, "time": "2020-10-13T10:55:20+00:00"}, {"version": "v1.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b934cc5e46e8b8ce92cacc4fe0094603992706f2"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b934cc5e46e8b8ce92cacc4fe0094603992706f2", "type": "zip", "shasum": "", "reference": "b934cc5e46e8b8ce92cacc4fe0094603992706f2"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.9.2"}, "time": "2021-01-14T16:04:43+00:00", "require": {"php": "^7.2 || ^8.0", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "laminas/laminas-diactoros": "^1.3.6 || ^2.0", "composer/package-versions-deprecated": "^1.8"}, "require-dev": {"phpstan/phpstan": "~0.12.34"}}, {"version": "v1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "f0c76183d9b1cd1ffd696874720cc82bde54fd14"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/f0c76183d9b1cd1ffd696874720cc82bde54fd14", "type": "zip", "shasum": "", "reference": "f0c76183d9b1cd1ffd696874720cc82bde54fd14"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.9.1"}, "time": "2020-12-22T08:40:29+00:00"}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b22078ce707d21ac17ea1727a4174c44ef57ae69"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b22078ce707d21ac17ea1727a4174c44ef57ae69", "type": "zip", "shasum": "", "reference": "b22078ce707d21ac17ea1727a4174c44ef57ae69"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.9.0"}, "time": "2020-12-02T09:39:42+00:00", "require": {"ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "php": "^7.3 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}, "require-dev": {"phpstan/phpstan": "~0.12"}}, {"version": "v1.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "561003b0b66a4f767782552a9282d824b08831be"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/561003b0b66a4f767782552a9282d824b08831be", "type": "zip", "shasum": "", "reference": "561003b0b66a4f767782552a9282d824b08831be"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v2.0.0-alpha19"}, "time": "2020-10-21T11:03:49+00:00", "require": {"ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.4.2", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "php": "^7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0af502835616774a8b306596e4d1a8c5843b6bc8"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0af502835616774a8b306596e4d1a8c5843b6bc8", "type": "zip", "shasum": "", "reference": "0af502835616774a8b306596e4d1a8c5843b6bc8"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/staging"}, "time": "2020-09-02T08:52:38+00:00"}, {"version": "v1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6f9849754ca13cc0cc7d8bfaee1ed320f3f5d8b4"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6f9849754ca13cc0cc7d8bfaee1ed320f3f5d8b4", "type": "zip", "shasum": "", "reference": "6f9849754ca13cc0cc7d8bfaee1ed320f3f5d8b4"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.8.2"}, "time": "2020-07-06T09:51:26+00:00"}, {"version": "v1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "af2b44192819d88b5beb0a1ca73646475eb25b5a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/af2b44192819d88b5beb0a1ca73646475eb25b5a", "type": "zip", "shasum": "", "reference": "af2b44192819d88b5beb0a1ca73646475eb25b5a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.8.1"}, "time": "2020-05-25T06:14:02+00:00"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "4d7dff992d7aef6775744f91ce2766d164f4a5da"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/4d7dff992d7aef6775744f91ce2766d164f4a5da", "type": "zip", "shasum": "", "reference": "4d7dff992d7aef6775744f91ce2766d164f4a5da"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.8.0"}, "time": "2020-05-05T14:59:13+00:00", "require": {"ext-json": "*", "ext-curl": "*", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "php": "^7.2", "spiral/goridge": "^2.4", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "fee8dbb4a6723068050c3396c61180a0733cf988"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/fee8dbb4a6723068050c3396c61180a0733cf988", "type": "zip", "shasum": "", "reference": "fee8dbb4a6723068050c3396c61180a0733cf988"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.7.1"}, "time": "2020-04-22T07:20:09+00:00", "require": {"php": "^7.1", "ext-json": "*", "ext-curl": "*", "spiral/goridge": "^2.3", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "laminas/laminas-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "7b2e5d9ffcffd539cbff431855b775f1f8901f73"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/7b2e5d9ffcffd539cbff431855b775f1f8901f73", "type": "zip", "shasum": "", "reference": "7b2e5d9ffcffd539cbff431855b775f1f8901f73"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.7.0"}, "time": "2020-03-23T14:39:27+00:00", "require": {"php": "^7.1", "ext-curl": "*", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "zendframework/zend-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "ca497b21bcc33361867d16e1709c55f0313128a7"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/ca497b21bcc33361867d16e1709c55f0313128a7", "type": "zip", "shasum": "", "reference": "ca497b21bcc33361867d16e1709c55f0313128a7"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.6.4"}, "time": "2020-03-14T10:54:25+00:00"}, {"version": "v1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a1a64624daac80fc190fe790b96bbc74563e2fd0"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a1a64624daac80fc190fe790b96bbc74563e2fd0", "type": "zip", "shasum": "", "reference": "a1a64624daac80fc190fe790b96bbc74563e2fd0"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.6.3"}, "time": "2020-03-10T08:06:55+00:00"}, {"version": "v1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a62e379d537aa309fc687a273835f5b4b2541215"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a62e379d537aa309fc687a273835f5b4b2541215", "type": "zip", "shasum": "", "reference": "a62e379d537aa309fc687a273835f5b4b2541215"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.6.2"}, "time": "2020-02-23T13:52:02+00:00", "require": {"php": "^7.1", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "zendframework/zend-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "691e937bcaf089e33e11e7f5528e7e736f500d0a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/691e937bcaf089e33e11e7f5528e7e736f500d0a", "type": "zip", "shasum": "", "reference": "691e937bcaf089e33e11e7f5528e7e736f500d0a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.6.1"}, "time": "2020-02-17T09:18:51+00:00"}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "cb006db6c6fb2b1b1b25445d67faaa2d6cd24801"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/cb006db6c6fb2b1b1b25445d67faaa2d6cd24801", "type": "zip", "shasum": "", "reference": "cb006db6c6fb2b1b1b25445d67faaa2d6cd24801"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.6.0"}, "time": "2020-02-11T19:40:29+00:00"}, {"version": "v1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "75a817edcff22e721dcc3a7fa5590b866f630403"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/75a817edcff22e721dcc3a7fa5590b866f630403", "type": "zip", "shasum": "", "reference": "75a817edcff22e721dcc3a7fa5590b866f630403"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.5.3"}, "time": "2019-12-23T13:58:09+00:00", "require-dev": "__unset"}, {"version": "v1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "fadf373c1fe5e51bfaeb9e5ac3fe4ee748620a44"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/fadf373c1fe5e51bfaeb9e5ac3fe4ee748620a44", "type": "zip", "shasum": "", "reference": "fadf373c1fe5e51bfaeb9e5ac3fe4ee748620a44"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.5.2"}, "time": "2019-12-05T12:16:07+00:00"}, {"version": "v1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "3e335a7c62a0f67b6290fc4982e541eeb93d483c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/3e335a7c62a0f67b6290fc4982e541eeb93d483c", "type": "zip", "shasum": "", "reference": "3e335a7c62a0f67b6290fc4982e541eeb93d483c"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.5.1"}, "time": "2019-10-22T15:48:38+00:00", "require": {"php": "^7.1", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0", "zendframework/zend-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a72ccd1b6247a25c90fad317d98c884f3869b623"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a72ccd1b6247a25c90fad317d98c884f3869b623", "type": "zip", "shasum": "", "reference": "a72ccd1b6247a25c90fad317d98c884f3869b623"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.5.0"}, "time": "2019-10-12T10:07:47+00:00"}, {"version": "v1.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "dcd03789d7f8e1dd31ddf63e6ee66aebf89b1605"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/dcd03789d7f8e1dd31ddf63e6ee66aebf89b1605", "type": "zip", "shasum": "", "reference": "dcd03789d7f8e1dd31ddf63e6ee66aebf89b1605"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.8"}, "time": "2019-09-06T11:02:08+00:00", "require": {"php": "^7.0", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0", "zendframework/zend-diactoros": "^1.3 || ^2.0"}}, {"version": "v1.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "7476936ee5504694dc97f2835d8d12c3a3ae4fc0"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/7476936ee5504694dc97f2835d8d12c3a3ae4fc0", "type": "zip", "shasum": "", "reference": "7476936ee5504694dc97f2835d8d12c3a3ae4fc0"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.7"}, "time": "2019-07-29T13:09:18+00:00"}, {"version": "v1.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9aee5beceffae852240491f042b5133e4f607a54"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9aee5beceffae852240491f042b5133e4f607a54", "type": "zip", "shasum": "", "reference": "9aee5beceffae852240491f042b5133e4f607a54"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.6"}, "time": "2019-07-01T12:19:13+00:00"}, {"version": "v1.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "bf540817226fae3833a81e08b6021789fdca8f2c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/bf540817226fae3833a81e08b6021789fdca8f2c", "type": "zip", "shasum": "", "reference": "bf540817226fae3833a81e08b6021789fdca8f2c"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.5"}, "time": "2019-06-27T11:54:58+00:00"}, {"version": "v1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a7320f346c5163ca3dd13becaf2d3c2f8d241742"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a7320f346c5163ca3dd13becaf2d3c2f8d241742", "type": "zip", "shasum": "", "reference": "a7320f346c5163ca3dd13becaf2d3c2f8d241742"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.4"}, "time": "2019-06-24T12:22:13+00:00", "bin": ["src/bin/rr"]}, {"version": "v1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "4229ef797ddd62f2bed7f2544043800c2def6a92"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/4229ef797ddd62f2bed7f2544043800c2def6a92", "type": "zip", "shasum": "", "reference": "4229ef797ddd62f2bed7f2544043800c2def6a92"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.3"}, "time": "2019-06-03T17:09:35+00:00"}, {"version": "v1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "1b1a43ce1140a7c4c4128d76ed1243d0d20b7c70"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/1b1a43ce1140a7c4c4128d76ed1243d0d20b7c70", "type": "zip", "shasum": "", "reference": "1b1a43ce1140a7c4c4128d76ed1243d0d20b7c70"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.2"}, "time": "2019-05-22T09:11:58+00:00", "require": {"php": "^7.0", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/console": "^2.5.0 || ^3.0.0 || ^4.0.0", "http-interop/http-factory-diactoros": "^1.0"}}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "eaa893ade15b1afab2c91f86bb738da2771cfa77"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/eaa893ade15b1afab2c91f86bb738da2771cfa77", "type": "zip", "shasum": "", "reference": "eaa893ade15b1afab2c91f86bb738da2771cfa77"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.1"}, "time": "2019-05-15T06:38:23+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "564e0769989ccad6c93bb42bca873a6614e8f387"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/564e0769989ccad6c93bb42bca873a6614e8f387", "type": "zip", "shasum": "", "reference": "564e0769989ccad6c93bb42bca873a6614e8f387"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.4.0"}, "time": "2019-05-05T09:48:36+00:00"}, {"description": "High-performance PHP load balancer and process manager library for Golang", "version": "v1.3.7", "version_normalized": "*******", "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "52f67468ccefe2f28dde7da5106784f5ec19ba9c"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/52f67468ccefe2f28dde7da5106784f5ec19ba9c", "type": "zip", "shasum": "", "reference": "52f67468ccefe2f28dde7da5106784f5ec19ba9c"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.7"}, "time": "2019-03-21T16:03:00+00:00", "bin": ["qbuild/rr-build"], "require": {"php": "^7.0", "ext-json": "*", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "zendframework/zend-diactoros": "^1.3|^2.0"}}, {"version": "v1.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "c5f8f9d12de48d93db2c9ef63acc5bfab4f1d08a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/c5f8f9d12de48d93db2c9ef63acc5bfab4f1d08a", "type": "zip", "shasum": "", "reference": "c5f8f9d12de48d93db2c9ef63acc5bfab4f1d08a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.6"}, "time": "2019-03-21T11:50:32+00:00"}, {"version": "v1.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "3c1923606c7b5391678b7a760772c8e55f046025"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/3c1923606c7b5391678b7a760772c8e55f046025", "type": "zip", "shasum": "", "reference": "3c1923606c7b5391678b7a760772c8e55f046025"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.5"}, "time": "2019-02-14T12:17:02+00:00"}, {"version": "v1.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "5d54e66187f1ff3c1a42009498b5e3749df6c334"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/5d54e66187f1ff3c1a42009498b5e3749df6c334", "type": "zip", "shasum": "", "reference": "5d54e66187f1ff3c1a42009498b5e3749df6c334"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.4"}, "time": "2019-02-02T14:27:29+00:00"}, {"version": "v1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "60ba949e5ea17ae02fb795c011db7a71782f96e3"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/60ba949e5ea17ae02fb795c011db7a71782f96e3", "type": "zip", "shasum": "", "reference": "60ba949e5ea17ae02fb795c011db7a71782f96e3"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.3"}, "time": "2019-01-31T12:17:08+00:00"}, {"version": "v1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6f81ce6cd30f86455d6ab0dfb52915a52683a2e9"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6f81ce6cd30f86455d6ab0dfb52915a52683a2e9", "type": "zip", "shasum": "", "reference": "6f81ce6cd30f86455d6ab0dfb52915a52683a2e9"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.2"}, "time": "2019-01-17T10:06:46+00:00"}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "3fa1671b2408dca780de368dcbb2b4ab9df6c379"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/3fa1671b2408dca780de368dcbb2b4ab9df6c379", "type": "zip", "shasum": "", "reference": "3fa1671b2408dca780de368dcbb2b4ab9df6c379"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.1"}, "time": "2019-01-11T13:29:31+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "b0cedeabded6939acf71cb5e28c3094247110d86"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/b0cedeabded6939acf71cb5e28c3094247110d86", "type": "zip", "shasum": "", "reference": "b0cedeabded6939acf71cb5e28c3094247110d86"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.3.0"}, "time": "2019-01-05T13:50:19+00:00", "bin": "__unset"}, {"version": "v1.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "4258f54f5b1ef25a692644d2bd58256092b7c23f"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/4258f54f5b1ef25a692644d2bd58256092b7c23f", "type": "zip", "shasum": "", "reference": "4258f54f5b1ef25a692644d2bd58256092b7c23f"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.8"}, "time": "2018-12-26T12:11:55+00:00", "require": {"php": "^7.0", "spiral/goridge": "^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "http-interop/http-factory-diactoros": "^1.0"}}, {"version": "v1.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "cde25a703534455b1af8156b867ede54b30024aa"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/cde25a703534455b1af8156b867ede54b30024aa", "type": "zip", "shasum": "", "reference": "cde25a703534455b1af8156b867ede54b30024aa"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.7"}, "time": "2018-12-20T12:11:00+00:00"}, {"version": "v1.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0a995cdd5b218e56250fc4758fbaefcd218c9cc6"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0a995cdd5b218e56250fc4758fbaefcd218c9cc6", "type": "zip", "shasum": "", "reference": "0a995cdd5b218e56250fc4758fbaefcd218c9cc6"}, "type": "library", "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.6"}, "time": "2018-10-18T10:37:49+00:00"}, {"version": "v1.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "45531ae427ac4120991d2f0ddc79b8b776a07b37"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/45531ae427ac4120991d2f0ddc79b8b776a07b37", "type": "zip", "shasum": "", "reference": "45531ae427ac4120991d2f0ddc79b8b776a07b37"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.5"}, "time": "2018-10-13T08:02:03+00:00"}, {"version": "v1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "e01f682048dec1328833e3fd0e33ed3bcf482261"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/e01f682048dec1328833e3fd0e33ed3bcf482261", "type": "zip", "shasum": "", "reference": "e01f682048dec1328833e3fd0e33ed3bcf482261"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.4"}, "time": "2018-09-30T16:14:33+00:00", "require": {"php": "^7.0", "spiral/goridge": "^2.0", "psr/http-message": "^1.0", "zendframework/zend-diactoros": "^1.7"}}, {"version": "v1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "6122fca108c20984732c969fb1ba53cce5b3c44a"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/6122fca108c20984732c969fb1ba53cce5b3c44a", "type": "zip", "shasum": "", "reference": "6122fca108c20984732c969fb1ba53cce5b3c44a"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.3"}, "time": "2018-09-29T20:37:16+00:00"}, {"version": "v1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "0b7efbb5825d798c05106b0ec651bb97bb2137ee"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/0b7efbb5825d798c05106b0ec651bb97bb2137ee", "type": "zip", "shasum": "", "reference": "0b7efbb5825d798c05106b0ec651bb97bb2137ee"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.2"}, "time": "2018-09-23T12:58:40+00:00"}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "bdff4b25d2a879357bc0ed53e96c0b551de07f88"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/bdff4b25d2a879357bc0ed53e96c0b551de07f88", "type": "zip", "shasum": "", "reference": "bdff4b25d2a879357bc0ed53e96c0b551de07f88"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.1"}, "time": "2018-09-21T13:29:34+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\": "php-src/"}}}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "a554a98dda0d793da09db17314ef3977a2f3a465"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/a554a98dda0d793da09db17314ef3977a2f3a465", "type": "zip", "shasum": "", "reference": "a554a98dda0d793da09db17314ef3977a2f3a465"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.2.0"}, "time": "2018-09-10T12:42:15+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "2cb472acb99ff900b0b1649d65cb3fea6a0507f2"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/2cb472acb99ff900b0b1649d65cb3fea6a0507f2", "type": "zip", "shasum": "", "reference": "2cb472acb99ff900b0b1649d65cb3fea6a0507f2"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.1.1"}, "time": "2018-07-26T15:27:03+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "d24a45209656b4554eb3fed7c06c51a0f6645061"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/d24a45209656b4554eb3fed7c06c51a0f6645061", "type": "zip", "shasum": "", "reference": "d24a45209656b4554eb3fed7c06c51a0f6645061"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.1.0"}, "time": "2018-07-09T05:05:35+00:00"}, {"version": "v1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "f7e18871e076cebee979748731cf0e99abe42cb2"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/f7e18871e076cebee979748731cf0e99abe42cb2", "type": "zip", "shasum": "", "reference": "f7e18871e076cebee979748731cf0e99abe42cb2"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.0.5"}, "time": "2018-06-30T10:52:13+00:00"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "9e39b07d4c41c8a565c139837aa4c405c4c9f5a8"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/9e39b07d4c41c8a565c139837aa4c405c4c9f5a8", "type": "zip", "shasum": "", "reference": "9e39b07d4c41c8a565c139837aa4c405c4c9f5a8"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.0.3"}, "time": "2018-06-23T17:49:32+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "fe514d88ac711afd64e2d730449b737df15f3c80"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/fe514d88ac711afd64e2d730449b737df15f3c80", "type": "zip", "shasum": "", "reference": "fe514d88ac711afd64e2d730449b737df15f3c80"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.0.2"}, "time": "2018-06-19T20:12:18+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "1504d7ab08f4395ef81bcf7643435dcb5cad544e"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/1504d7ab08f4395ef81bcf7643435dcb5cad544e", "type": "zip", "shasum": "", "reference": "1504d7ab08f4395ef81bcf7643435dcb5cad544e"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.0.1"}, "time": "2018-06-15T12:55:33+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "3bcfe26dad554450dc1742da92874627697fd049"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/3bcfe26dad554450dc1742da92874627697fd049", "type": "zip", "shasum": "", "reference": "3bcfe26dad554450dc1742da92874627697fd049"}, "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v1.0.0"}, "time": "2018-06-13T21:02:58+00:00"}, {"description": "High-performance PHP job balancer and process manager library for Golang", "version": "v0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/spiral/roadrunner.git", "type": "git", "reference": "2f27300fb08d988b87ddfa62ebec615e52fc2f3b"}, "dist": {"url": "https://api.github.com/repos/spiral/roadrunner/zipball/2f27300fb08d988b87ddfa62ebec615e52fc2f3b", "type": "zip", "shasum": "", "reference": "2f27300fb08d988b87ddfa62ebec615e52fc2f3b"}, "type": "go<PERSON>", "support": {"issues": "https://github.com/spiral/roadrunner/issues", "source": "https://github.com/spiral/roadrunner/tree/v0.9.0"}, "time": "2018-01-28T16:13:27+00:00", "autoload": {"psr-4": {"Spiral\\RoadRunner\\": "source/"}}, "require": {"php": "^7.0", "spiral/goridge": "^2.0"}, "require-dev": {"phpunit/phpunit": "~6.0"}}]}, "security-advisories": [], "last-modified": "Sat, 14 Jun 2025 22:23:16 GMT"}