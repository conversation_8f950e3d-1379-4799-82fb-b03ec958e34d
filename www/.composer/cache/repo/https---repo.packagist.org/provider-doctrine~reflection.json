{"minified": "composer/2.0", "packages": {"doctrine/reflection": [{"name": "doctrine/reflection", "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "keywords": ["reflection", "static"], "homepage": "https://www.doctrine-project.org/projects/reflection.html", "version": "1.2.4", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "type": "zip", "shasum": "", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7"}, "type": "library", "time": "2023-07-27T18:11:59+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "require": {"php": "^7.1 || ^8.0", "ext-tokenizer": "*", "doctrine/annotations": "^1.0 || ^2.0"}, "require-dev": {"doctrine/coding-standard": "^9", "doctrine/common": "^3.3", "phpstan/phpstan": "^1.4.10", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "conflict": {"doctrine/common": "<2.9"}, "abandoned": "roave/better-reflection", "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.4"}}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "1034e5e71f89978b80f9c1570e7226f6c3b9b6fb"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/1034e5e71f89978b80f9c1570e7226f6c3b9b6fb", "type": "zip", "shasum": "", "reference": "1034e5e71f89978b80f9c1570e7226f6c3b9b6fb"}, "time": "2022-05-31T18:46:25+00:00", "require": {"php": "^7.1 || ^8.0", "ext-tokenizer": "*", "doctrine/annotations": "^1.0"}, "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.3"}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/fa587178be682efe90d005e3a322590d6ebb59a5", "type": "zip", "shasum": "", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5"}, "time": "2020-10-27T21:46:55+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require-dev": {"doctrine/coding-standard": "^6.0 || ^8.2.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0 || ^0.12.20", "phpstan/phpstan-phpunit": "^0.11.0 || ^0.12.16", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.2"}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "55e71912dfcd824b2fdd16f2d9afe15684cfce79"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/55e71912dfcd824b2fdd16f2d9afe15684cfce79", "type": "zip", "shasum": "", "reference": "55e71912dfcd824b2fdd16f2d9afe15684cfce79"}, "time": "2020-03-27T11:06:43+00:00", "require": {"php": "^7.1", "ext-tokenizer": "*", "doctrine/annotations": "^1.0"}, "require-dev": {"phpstan/phpstan": "^0.11.0", "phpstan/phpstan-phpunit": "^0.11.0", "phpunit/phpunit": "^7.0", "doctrine/coding-standard": "^5.0", "doctrine/common": "^2.10"}, "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.x"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "b699ecc7f2784d1e49924fd9858cf1078db6b0e2"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/b699ecc7f2784d1e49924fd9858cf1078db6b0e2", "type": "zip", "shasum": "", "reference": "b699ecc7f2784d1e49924fd9858cf1078db6b0e2"}, "time": "2020-03-21T11:34:59+00:00", "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/master"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "bc420ead87fdfe08c03ecc3549db603a45b06d4c"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/bc420ead87fdfe08c03ecc3549db603a45b06d4c", "type": "zip", "shasum": "", "reference": "bc420ead87fdfe08c03ecc3549db603a45b06d4c"}, "time": "2020-01-08T19:53:19+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"description": "Doctrine Reflection component", "keywords": ["reflection"], "version": "v1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/doctrine/reflection.git", "type": "git", "reference": "02538d3f95e88eb397a5f86274deb2c6175c2ab6"}, "dist": {"url": "https://api.github.com/repos/doctrine/reflection/zipball/02538d3f95e88eb397a5f86274deb2c6175c2ab6", "type": "zip", "shasum": "", "reference": "02538d3f95e88eb397a5f86274deb2c6175c2ab6"}, "time": "2018-06-14T14:45:07+00:00", "require-dev": {"phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpunit/phpunit": "^7.0", "doctrine/coding-standard": "^4.0", "doctrine/common": "^2.8", "squizlabs/php_codesniffer": "^3.0"}, "conflict": "__unset"}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:40:25 GMT"}