{"minified": "composer/2.0", "packages": {"psr/clock": [{"name": "psr/clock", "description": "Common interface for reading the clock.", "keywords": ["time", "psr", "clock", "now", "psr-20"], "homepage": "https://github.com/php-fig/clock", "version": "1.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/clock.git", "type": "git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "type": "zip", "shasum": "", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "type": "library", "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "funding": [], "time": "2022-11-25T14:36:26+00:00", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "require": {"php": "^7.0 || ^8.0"}}]}, "security-advisories": [], "last-modified": "Thu, 21 Mar 2024 15:30:47 GMT"}