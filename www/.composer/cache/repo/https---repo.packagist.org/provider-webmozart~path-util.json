{"minified": "composer/2.0", "packages": {"webmozart/path-util": [{"name": "webmozart/path-util", "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "keywords": [], "homepage": "", "version": "2.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "type": "zip", "shasum": "", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "type": "library", "time": "2015-12-17T08:42:14+00:00", "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "abandoned": "symfony/filesystem", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.3.0"}}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "e5ac8b8a0ceaf7b36bff257be21780332880284d"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/e5ac8b8a0ceaf7b36bff257be21780332880284d", "type": "zip", "shasum": "", "reference": "e5ac8b8a0ceaf7b36bff257be21780332880284d"}, "time": "2015-10-05T10:49:32+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.2.3"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "f1c8da76b3ed86a43058fa68f59c0e4f90c69f27"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/f1c8da76b3ed86a43058fa68f59c0e4f90c69f27", "type": "zip", "shasum": "", "reference": "f1c8da76b3ed86a43058fa68f59c0e4f90c69f27"}, "time": "2015-08-24T17:27:41+00:00", "require-dev": {"phpunit/phpunit": "^4.6"}, "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.2.2"}}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "1bc1987de4c5a4f1b1fe11a64bc6c44acd08a056"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/1bc1987de4c5a4f1b1fe11a64bc6c44acd08a056", "type": "zip", "shasum": "", "reference": "1bc1987de4c5a4f1b1fe11a64bc6c44acd08a056"}, "time": "2015-08-24T13:25:29+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.2.1"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "7ec6056a604b6d017808d71f50d73155468639e4"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/7ec6056a604b6d017808d71f50d73155468639e4", "type": "zip", "shasum": "", "reference": "7ec6056a604b6d017808d71f50d73155468639e4"}, "time": "2015-08-14T11:36:10+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.2.0"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "2640299f8f0813d65d5a7541f674f09b925b4da0"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/2640299f8f0813d65d5a7541f674f09b925b4da0", "type": "zip", "shasum": "", "reference": "2640299f8f0813d65d5a7541f674f09b925b4da0"}, "time": "2015-07-14T16:09:01+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.1.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "ff82d231236bec95020c7d2bfa10780402b0cbeb"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/ff82d231236bec95020c7d2bfa10780402b0cbeb", "type": "zip", "shasum": "", "reference": "ff82d231236bec95020c7d2bfa10780402b0cbeb"}, "time": "2015-05-21T10:43:18+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.0.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "86cd0f94363c17f4d557a9f02ed70f67584e9f84"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/86cd0f94363c17f4d557a9f02ed70f67584e9f84", "type": "zip", "shasum": "", "reference": "86cd0f94363c17f4d557a9f02ed70f67584e9f84"}, "time": "2015-03-19T10:57:25+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=5.3.3"}, "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/1.1.0"}, "require-dev": "__unset"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "0290376dc1f3ff5cf116c573b0892630e05ee659"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/0290376dc1f3ff5cf116c573b0892630e05ee659", "type": "zip", "shasum": "", "reference": "0290376dc1f3ff5cf116c573b0892630e05ee659"}, "time": "2015-01-12T10:28:01+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/1.0.2"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "e23d4a13e7c627106ee60877e204fda80183916a"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/e23d4a13e7c627106ee60877e204fda80183916a", "type": "zip", "shasum": "", "reference": "e23d4a13e7c627106ee60877e204fda80183916a"}, "time": "2014-12-03T11:03:29+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/1.0.1"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozart/path-util.git", "type": "git", "reference": "8097f070ef1b9417d459fa34cdc0703f5a55f0f4"}, "dist": {"url": "https://api.github.com/repos/webmozart/path-util/zipball/8097f070ef1b9417d459fa34cdc0703f5a55f0f4", "type": "zip", "shasum": "", "reference": "8097f070ef1b9417d459fa34cdc0703f5a55f0f4"}, "time": "2014-11-26T17:03:42+00:00", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/1.0.0"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:17:41 GMT"}