{"minified": "composer/2.0", "packages": {"symfony/mime": [{"name": "symfony/mime", "description": "Allows manipulating MIME messages", "keywords": ["mime", "mime-type"], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "type": "zip", "shasum": "", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9"}, "type": "library", "support": {"source": "https://github.com/symfony/mime/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-19T08:51:26+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mime/tree/v7.3.0-BETA1"}}, {"version": "v7.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "706e65c72d402539a072d0d6ad105fff6c161ef1"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/706e65c72d402539a072d0d6ad105fff6c161ef1", "type": "zip", "shasum": "", "reference": "706e65c72d402539a072d0d6ad105fff6c161ef1"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.6"}, "time": "2025-04-27T13:34:41+00:00"}, {"version": "v7.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "87ca22046b78c3feaff04b337f33b38510fd686b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/87ca22046b78c3feaff04b337f33b38510fd686b", "type": "zip", "shasum": "", "reference": "87ca22046b78c3feaff04b337f33b38510fd686b"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.4"}, "time": "2025-02-19T08:51:20+00:00"}, {"version": "v7.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2fc3b4bd67e4747e45195bc4c98bea4628476204"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2fc3b4bd67e4747e45195bc4c98bea4628476204", "type": "zip", "shasum": "", "reference": "2fc3b4bd67e4747e45195bc4c98bea4628476204"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.3"}, "time": "2025-01-27T11:08:17+00:00"}, {"version": "v7.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7f9617fcf15cb61be30f8b252695ed5e2bfac283"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7f9617fcf15cb61be30f8b252695ed5e2bfac283", "type": "zip", "shasum": "", "reference": "7f9617fcf15cb61be30f8b252695ed5e2bfac283"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.1"}, "time": "2024-12-07T08:50:44+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "cc84a4b81f62158c3846ac7ff10f696aae2b524d"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/cc84a4b81f62158c3846ac7ff10f696aae2b524d", "type": "zip", "shasum": "", "reference": "cc84a4b81f62158c3846ac7ff10f696aae2b524d"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.0"}, "time": "2024-11-23T09:19:39+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "f31946de86ef8fcf48ae76652542f29df2ef4428"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/f31946de86ef8fcf48ae76652542f29df2ef4428", "type": "zip", "shasum": "", "reference": "f31946de86ef8fcf48ae76652542f29df2ef4428"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.0-RC1"}, "time": "2024-11-10T09:50:45+00:00"}, {"version": "v7.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c73ca0f7f11fb609356b84d0d808053bf433880a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c73ca0f7f11fb609356b84d0d808053bf433880a", "type": "zip", "shasum": "", "reference": "c73ca0f7f11fb609356b84d0d808053bf433880a"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.0-BETA2"}, "time": "2024-11-05T09:31:08+00:00"}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e57faea55d255c31b0899f0130c7d9da65fa2ec7"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e57faea55d255c31b0899f0130c7d9da65fa2ec7", "type": "zip", "shasum": "", "reference": "e57faea55d255c31b0899f0130c7d9da65fa2ec7"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.2.0-BETA1"}, "time": "2024-10-25T15:15:23+00:00"}, {"version": "v7.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c252e20d1179dd35a5bfdb4a61a2084387ce97f4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c252e20d1179dd35a5bfdb4a61a2084387ce97f4", "type": "zip", "shasum": "", "reference": "c252e20d1179dd35a5bfdb4a61a2084387ce97f4"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.11"}, "time": "2025-01-27T10:57:12+00:00"}, {"version": "v7.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a42697498633a43da1d390ccf453b597df982a35"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a42697498633a43da1d390ccf453b597df982a35", "type": "zip", "shasum": "", "reference": "a42697498633a43da1d390ccf453b597df982a35"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.10"}, "time": "2024-12-07T08:49:48+00:00"}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "caa1e521edb2650b8470918dfe51708c237f0598"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/caa1e521edb2650b8470918dfe51708c237f0598", "type": "zip", "shasum": "", "reference": "caa1e521edb2650b8470918dfe51708c237f0598"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.6"}, "time": "2024-10-25T15:11:02+00:00"}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "711d2e167e8ce65b05aea6b258c449671cdd38ff"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/711d2e167e8ce65b05aea6b258c449671cdd38ff", "type": "zip", "shasum": "", "reference": "711d2e167e8ce65b05aea6b258c449671cdd38ff"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.5"}, "time": "2024-09-20T08:28:38+00:00"}, {"version": "v7.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ccaa6c2503db867f472a587291e764d6a1e58758"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ccaa6c2503db867f472a587291e764d6a1e58758", "type": "zip", "shasum": "", "reference": "ccaa6c2503db867f472a587291e764d6a1e58758"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.4"}, "time": "2024-08-13T14:28:19+00:00"}, {"version": "v7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "26a00b85477e69a4bab63b66c5dce64f18b0cbfc"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/26a00b85477e69a4bab63b66c5dce64f18b0cbfc", "type": "zip", "shasum": "", "reference": "26a00b85477e69a4bab63b66c5dce64f18b0cbfc"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.2"}, "time": "2024-06-28T10:03:55+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "21027eaacc1a8a20f5e616c25c3580f5dd3a15df"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/21027eaacc1a8a20f5e616c25c3580f5dd3a15df", "type": "zip", "shasum": "", "reference": "21027eaacc1a8a20f5e616c25c3580f5dd3a15df"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.1"}, "time": "2024-06-04T06:40:14+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "92d6b9b1217eebff2035577db505b7e1435ca78c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/92d6b9b1217eebff2035577db505b7e1435ca78c", "type": "zip", "shasum": "", "reference": "92d6b9b1217eebff2035577db505b7e1435ca78c"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.0"}, "time": "2024-05-29T15:16:11+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4"}}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "b79638146f25c878d08c014a290ac0d9d5085e00"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/b79638146f25c878d08c014a290ac0d9d5085e00", "type": "zip", "shasum": "", "reference": "b79638146f25c878d08c014a290ac0d9d5085e00"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.1.0-RC1"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mime/tree/v7.1.0-BETA1"}}, {"version": "v7.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "60757ea7d562ae1756c1f430a6f7872156a15f32"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/60757ea7d562ae1756c1f430a6f7872156a15f32", "type": "zip", "shasum": "", "reference": "60757ea7d562ae1756c1f430a6f7872156a15f32"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.0.9"}, "time": "2024-06-28T09:58:46+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "3426d1e95f432c82ceef57e9943383116800f406"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/3426d1e95f432c82ceef57e9943383116800f406", "type": "zip", "shasum": "", "reference": "3426d1e95f432c82ceef57e9943383116800f406"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.0.8"}, "time": "2024-06-02T15:49:03+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4"}}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "3adbf110c306546f6f00337f421d2edca0e8d3c0"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/3adbf110c306546f6f00337f421d2edca0e8d3c0", "type": "zip", "shasum": "", "reference": "3adbf110c306546f6f00337f421d2edca0e8d3c0"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "99362408c9abdf8c7cadcf0529b6fc8b16f5ace2"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/99362408c9abdf8c7cadcf0529b6fc8b16f5ace2", "type": "zip", "shasum": "", "reference": "99362408c9abdf8c7cadcf0529b6fc8b16f5ace2"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.0.6"}, "time": "2024-03-21T19:37:36+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c1ffe24ba6fdc3e3f0f3fcb93519103b326a3716"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c1ffe24ba6fdc3e3f0f3fcb93519103b326a3716", "type": "zip", "shasum": "", "reference": "c1ffe24ba6fdc3e3f0f3fcb93519103b326a3716"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.0.3"}, "time": "2024-01-30T08:34:29+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0a2fff95c1a10df97f571d67e76c7ae0f0d4f535"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0a2fff95c1a10df97f571d67e76c7ae0f0d4f535", "type": "zip", "shasum": "", "reference": "0a2fff95c1a10df97f571d67e76c7ae0f0d4f535"}, "support": {"source": "https://github.com/symfony/mime/tree/v7.0.0"}, "time": "2023-10-19T14:20:43+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mime/tree/v7.0.0-BETA1"}}, {"version": "v6.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/fec8aa5231f3904754955fad33c2db50594d22d1", "type": "zip", "shasum": "", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.21"}, "time": "2025-04-27T13:27:38+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}}, {"version": "v6.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3", "type": "zip", "shasum": "", "reference": "ac537b6c55ccc2c749f3c979edfa9ec14aaed4f3"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.19"}, "time": "2025-02-17T21:23:52+00:00"}, {"version": "v6.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "917d77981eb1ea963608d5cda4d9c0cf72eaa68e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/917d77981eb1ea963608d5cda4d9c0cf72eaa68e", "type": "zip", "shasum": "", "reference": "917d77981eb1ea963608d5cda4d9c0cf72eaa68e"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.18"}, "time": "2025-01-23T13:10:52+00:00"}, {"version": "v6.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ea87c8850a54ff039d3e0ab4ae5586dd4e6c0232"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ea87c8850a54ff039d3e0ab4ae5586dd4e6c0232", "type": "zip", "shasum": "", "reference": "ea87c8850a54ff039d3e0ab4ae5586dd4e6c0232"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.17"}, "time": "2024-12-02T11:09:41+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "1de1cf14d99b12c7ebbb850491ec6ae3ed468855"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/1de1cf14d99b12c7ebbb850491ec6ae3ed468855", "type": "zip", "shasum": "", "reference": "1de1cf14d99b12c7ebbb850491ec6ae3ed468855"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.13"}, "time": "2024-10-25T15:07:50+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "abe16ee7790b16aa525877419deb0f113953f0e1"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/abe16ee7790b16aa525877419deb0f113953f0e1", "type": "zip", "shasum": "", "reference": "abe16ee7790b16aa525877419deb0f113953f0e1"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.12"}, "time": "2024-09-20T08:18:25+00:00"}, {"version": "v6.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "dba5d5f6073baf7a3576b580cc4a208b4ca00553"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/dba5d5f6073baf7a3576b580cc4a208b4ca00553", "type": "zip", "shasum": "", "reference": "dba5d5f6073baf7a3576b580cc4a208b4ca00553"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.11"}, "time": "2024-08-13T12:15:02+00:00"}, {"version": "v6.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7d048964877324debdcb4e0549becfa064a20d43"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7d048964877324debdcb4e0549becfa064a20d43", "type": "zip", "shasum": "", "reference": "7d048964877324debdcb4e0549becfa064a20d43"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.9"}, "time": "2024-06-28T09:49:33+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "618597ab8b78ac86d1c75a9d0b35540cda074f33"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/618597ab8b78ac86d1c75a9d0b35540cda074f33", "type": "zip", "shasum": "", "reference": "618597ab8b78ac86d1c75a9d0b35540cda074f33"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.8"}, "time": "2024-06-01T07:50:16+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.3.2|^7.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.3.2"}}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "decadcf3865918ecfcbfa90968553994ce935a5e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/decadcf3865918ecfcbfa90968553994ce935a5e", "type": "zip", "shasum": "", "reference": "decadcf3865918ecfcbfa90968553994ce935a5e"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "14762b86918823cb42e3558cdcca62e58b5227fe"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/14762b86918823cb42e3558cdcca62e58b5227fe", "type": "zip", "shasum": "", "reference": "14762b86918823cb42e3558cdcca62e58b5227fe"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.6"}, "time": "2024-03-21T19:36:20+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5017e0a9398c77090b7694be46f20eb796262a34"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5017e0a9398c77090b7694be46f20eb796262a34", "type": "zip", "shasum": "", "reference": "5017e0a9398c77090b7694be46f20eb796262a34"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.3"}, "time": "2024-01-30T08:32:12+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.3.2|^7.0"}}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ca4f58b2ef4baa8f6cecbeca2573f88cd577d205"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ca4f58b2ef4baa8f6cecbeca2573f88cd577d205", "type": "zip", "shasum": "", "reference": "ca4f58b2ef4baa8f6cecbeca2573f88cd577d205"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.4.0"}, "time": "2023-10-17T11:49:05+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mime/tree/v6.4.0-BETA1"}}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "4b24dcaf8dfcd23fb7abb5b9df11e8c8093db68a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/4b24dcaf8dfcd23fb7abb5b9df11e8c8093db68a", "type": "zip", "shasum": "", "reference": "4b24dcaf8dfcd23fb7abb5b9df11e8c8093db68a"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.3.12"}, "time": "2024-01-30T08:17:33+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "~6.3.12|^6.4.3"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.3.12|>=6.4,<6.4.3"}}, {"version": "v6.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d5179eedf1cb2946dbd760475ebf05c251ef6a6e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d5179eedf1cb2946dbd760475ebf05c251ef6a6e", "type": "zip", "shasum": "", "reference": "d5179eedf1cb2946dbd760475ebf05c251ef6a6e"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.3.5"}, "time": "2023-09-29T06:59:36+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "~6.2.13|^6.3.2"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.2.13|>=6.3,<6.3.2"}}, {"version": "v6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "9a0cbd52baa5ba5a5b1f0cacc59466f194730f98"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/9a0cbd52baa5ba5a5b1f0cacc59466f194730f98", "type": "zip", "shasum": "", "reference": "9a0cbd52baa5ba5a5b1f0cacc59466f194730f98"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.3.3"}, "time": "2023-07-31T07:08:24+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "fa1b1c1bbd5dcc0b1a8c88f45bb757f5e11ffa52"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/fa1b1c1bbd5dcc0b1a8c88f45bb757f5e11ffa52", "type": "zip", "shasum": "", "reference": "fa1b1c1bbd5dcc0b1a8c88f45bb757f5e11ffa52"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.3.2"}, "time": "2023-07-27T06:30:42+00:00", "require": {"php": ">=8.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7b5d2121858cd6efbed778abce9cfdd7ab1f62ad"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7b5d2121858cd6efbed778abce9cfdd7ab1f62ad", "type": "zip", "shasum": "", "reference": "7b5d2121858cd6efbed778abce9cfdd7ab1f62ad"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.3.0"}, "time": "2023-04-28T15:57:00+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^6.2"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.2"}}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mime/tree/v6.3.0-BETA1"}}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "32f2c5f8114fd778ed00fc54f684e805d7e48508"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/32f2c5f8114fd778ed00fc54f684e805d7e48508", "type": "zip", "shasum": "", "reference": "32f2c5f8114fd778ed00fc54f684e805d7e48508"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.13"}, "time": "2023-07-27T06:30:34+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "~6.2.13|^6.3.2"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.2.13|>=6.3,<6.3.2"}}, {"version": "v6.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "b6c137fc53a9f7c4c951cd3f362b3734c7a97723"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/b6c137fc53a9f7c4c951cd3f362b3734c7a97723", "type": "zip", "shasum": "", "reference": "b6c137fc53a9f7c4c951cd3f362b3734c7a97723"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.10"}, "time": "2023-04-19T09:54:16+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^6.2"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.2"}}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "62e341f80699badb0ad70b31149c8df89a2d778e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/62e341f80699badb0ad70b31149c8df89a2d778e", "type": "zip", "shasum": "", "reference": "62e341f80699badb0ad70b31149c8df89a2d778e"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.7"}, "time": "2023-02-24T10:42:00+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "4b7b349f67d15cd0639955c8179a76c89f6fd610"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/4b7b349f67d15cd0639955c8179a76c89f6fd610", "type": "zip", "shasum": "", "reference": "4b7b349f67d15cd0639955c8179a76c89f6fd610"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.5"}, "time": "2023-01-10T18:53:53+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "8c98bf40406e791043890a163f6f6599b9cfa1ed"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/8c98bf40406e791043890a163f6f6599b9cfa1ed", "type": "zip", "shasum": "", "reference": "8c98bf40406e791043890a163f6f6599b9cfa1ed"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.2"}, "time": "2022-12-14T16:38:10+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^6.2"}}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "1e8005a7cbd79fb824ad81308ef2a76592a08bc0"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/1e8005a7cbd79fb824ad81308ef2a76592a08bc0", "type": "zip", "shasum": "", "reference": "1e8005a7cbd79fb824ad81308ef2a76592a08bc0"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.0"}, "time": "2022-11-28T12:28:19+00:00"}, {"version": "v6.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/mime/tree/v6.2.0-RC2"}}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "061d442ff6fc6a02d707ea8d5715740f5a6e68c4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/061d442ff6fc6a02d707ea8d5715740f5a6e68c4", "type": "zip", "shasum": "", "reference": "061d442ff6fc6a02d707ea8d5715740f5a6e68c4"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.0-RC1"}, "time": "2022-11-08T14:53:55+00:00"}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/mime/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "877a042802953e4be7d2b9e171ffde791f3e2ce8"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/877a042802953e4be7d2b9e171ffde791f3e2ce8", "type": "zip", "shasum": "", "reference": "877a042802953e4be7d2b9e171ffde791f3e2ce8"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.2.0-BETA1"}, "time": "2022-10-19T08:20:14+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2bff58573e81a1df51bf99ad01725428beda1cbc"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2bff58573e81a1df51bf99ad01725428beda1cbc", "type": "zip", "shasum": "", "reference": "2bff58573e81a1df51bf99ad01725428beda1cbc"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.11"}, "time": "2023-01-10T18:53:01+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.2|^6.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4"}}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "07497fe44ff80480eac2a120b3cef39173dda936"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/07497fe44ff80480eac2a120b3cef39173dda936", "type": "zip", "shasum": "", "reference": "07497fe44ff80480eac2a120b3cef39173dda936"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.9"}, "time": "2022-12-14T16:32:43+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.2|^6.0"}}, {"version": "v6.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d02c3938a50fbc95cf8f364be1c011758270f30e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d02c3938a50fbc95cf8f364be1c011758270f30e", "type": "zip", "shasum": "", "reference": "d02c3938a50fbc95cf8f364be1c011758270f30e"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.8"}, "time": "2022-11-28T12:27:40+00:00"}, {"version": "v6.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "f440f066d57691088d998d6e437ce98771144618"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/f440f066d57691088d998d6e437ce98771144618", "type": "zip", "shasum": "", "reference": "f440f066d57691088d998d6e437ce98771144618"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.7"}, "time": "2022-10-19T08:10:53+00:00"}, {"version": "v6.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5ae192b9a39730435cfec025a499f79d05ac68a3"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5ae192b9a39730435cfec025a499f79d05ac68a3", "type": "zip", "shasum": "", "reference": "5ae192b9a39730435cfec025a499f79d05ac68a3"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.6"}, "time": "2022-10-07T08:04:03+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4.14|~6.0.14|^6.1.6"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<5.4.14|>=6.0,<6.0.14|>=6.1,<6.1.6"}}, {"version": "v6.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d521b2204f7dcebe81c1b5fb99ed70dfb6f34b4b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d521b2204f7dcebe81c1b5fb99ed70dfb6f34b4b", "type": "zip", "shasum": "", "reference": "d521b2204f7dcebe81c1b5fb99ed70dfb6f34b4b"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.5"}, "time": "2022-09-02T08:05:20+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4"}}, {"version": "v6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5d1de2d3c52f8ca469c488f4b9e007e9e9cee0b3"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5d1de2d3c52f8ca469c488f4b9e007e9e9cee0b3", "type": "zip", "shasum": "", "reference": "5d1de2d3c52f8ca469c488f4b9e007e9e9cee0b3"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.4"}, "time": "2022-08-19T14:27:04+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "9c0247994fc6584da8591ba64b2bffaace9df87d"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/9c0247994fc6584da8591ba64b2bffaace9df87d", "type": "zip", "shasum": "", "reference": "9c0247994fc6584da8591ba64b2bffaace9df87d"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.3"}, "time": "2022-07-20T13:46:29+00:00"}, {"version": "v6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "56508865dd883dce3c863af11b3e8053adab30d7"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/56508865dd883dce3c863af11b3e8053adab30d7", "type": "zip", "shasum": "", "reference": "56508865dd883dce3c863af11b3e8053adab30d7"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.1"}, "time": "2022-06-09T12:51:38+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "032e796edbf842bc4b4c81c42598b144b95ce56a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/032e796edbf842bc4b4c81c42598b144b95ce56a", "type": "zip", "shasum": "", "reference": "032e796edbf842bc4b4c81c42598b144b95ce56a"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.0"}, "time": "2022-05-21T13:34:40+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ccc0b0b3a702eae0160f3e93b384b754eeb8702c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ccc0b0b3a702eae0160f3e93b384b754eeb8702c", "type": "zip", "shasum": "", "reference": "ccc0b0b3a702eae0160f3e93b384b754eeb8702c"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.1.0-RC1"}, "time": "2022-04-12T16:22:53+00:00"}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/mime/tree/v6.1.0-BETA1"}}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d7052547a0070cbeadd474e172b527a00d657301"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d7052547a0070cbeadd474e172b527a00d657301", "type": "zip", "shasum": "", "reference": "d7052547a0070cbeadd474e172b527a00d657301"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.19"}, "time": "2023-01-11T11:50:03+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4.14|~6.0.14|^6.1.6"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<5.4.14|>=6.0,<6.0.14|>=6.1,<6.1.6"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "3e6a7ba15997020778312ed576ad01ab60dc2336"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/3e6a7ba15997020778312ed576ad01ab60dc2336", "type": "zip", "shasum": "", "reference": "3e6a7ba15997020778312ed576ad01ab60dc2336"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.17"}, "time": "2022-12-14T16:19:02+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4.14|~6.0.14|^6.1.6"}}, {"version": "v6.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ad9878bede5707cdf5ff7f5c86d82a921bbbfe1c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ad9878bede5707cdf5ff7f5c86d82a921bbbfe1c", "type": "zip", "shasum": "", "reference": "ad9878bede5707cdf5ff7f5c86d82a921bbbfe1c"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.16"}, "time": "2022-11-28T12:25:56+00:00"}, {"version": "v6.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c01b88b63418131daf2edd0bdc17fc8a6d1c939a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c01b88b63418131daf2edd0bdc17fc8a6d1c939a", "type": "zip", "shasum": "", "reference": "c01b88b63418131daf2edd0bdc17fc8a6d1c939a"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.14"}, "time": "2022-10-07T08:02:12+00:00"}, {"version": "v6.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c1d6eba531d956c23b3127dc6ae6f5ac4a90db6c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c1d6eba531d956c23b3127dc6ae6f5ac4a90db6c", "type": "zip", "shasum": "", "reference": "c1d6eba531d956c23b3127dc6ae6f5ac4a90db6c"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.13"}, "time": "2022-09-02T08:05:03+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4"}}, {"version": "v6.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "02a11577f2f9522c783179712bdf6d2d3cb9fc1d"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/02a11577f2f9522c783179712bdf6d2d3cb9fc1d", "type": "zip", "shasum": "", "reference": "02a11577f2f9522c783179712bdf6d2d3cb9fc1d"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.12"}, "time": "2022-08-19T14:25:15+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c6f16f6789587348f6518b193d3499c0e1f5e5c5"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c6f16f6789587348f6518b193d3499c0e1f5e5c5", "type": "zip", "shasum": "", "reference": "c6f16f6789587348f6518b193d3499c0e1f5e5c5"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.11"}, "time": "2022-07-20T13:45:53+00:00"}, {"version": "v6.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "4de7886c66e0953f5d6edab3e49ceb751d01621c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/4de7886c66e0953f5d6edab3e49ceb751d01621c", "type": "zip", "shasum": "", "reference": "4de7886c66e0953f5d6edab3e49ceb751d01621c"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.10"}, "time": "2022-06-09T12:50:38+00:00"}, {"version": "v6.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e17bae63d437b3e21942dcc47ccca802d3573dd8"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e17bae63d437b3e21942dcc47ccca802d3573dd8", "type": "zip", "shasum": "", "reference": "e17bae63d437b3e21942dcc47ccca802d3573dd8"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.9"}, "time": "2022-05-21T13:33:31+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c1701e88ad0ca49fc6ad6cdf360bc0e1209fb5e1"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c1701e88ad0ca49fc6ad6cdf360bc0e1209fb5e1", "type": "zip", "shasum": "", "reference": "c1701e88ad0ca49fc6ad6cdf360bc0e1209fb5e1"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.8"}, "time": "2022-04-12T16:11:42+00:00"}, {"version": "v6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "74266e396f812a2301536397a6360b6e6913c0d8"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/74266e396f812a2301536397a6360b6e6913c0d8", "type": "zip", "shasum": "", "reference": "74266e396f812a2301536397a6360b6e6913c0d8"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.7"}, "time": "2022-03-13T20:10:05+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2cd9601efd040e56f43360daa68f3c6b0534923a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2cd9601efd040e56f43360daa68f3c6b0534923a", "type": "zip", "shasum": "", "reference": "2cd9601efd040e56f43360daa68f3c6b0534923a"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.3"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5586487ae45a89355d5515059de48869207112e1"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5586487ae45a89355d5515059de48869207112e1", "type": "zip", "shasum": "", "reference": "5586487ae45a89355d5515059de48869207112e1"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.2"}, "time": "2021-12-28T17:22:37+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "3506356e2368c5623f52cf5ff643e0dee8d5eef7"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/3506356e2368c5623f52cf5ff643e0dee8d5eef7", "type": "zip", "shasum": "", "reference": "3506356e2368c5623f52cf5ff643e0dee8d5eef7"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e83af78cd32fb01e5ae7d1cb202fef3aef1e840a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e83af78cd32fb01e5ae7d1cb202fef3aef1e840a", "type": "zip", "shasum": "", "reference": "e83af78cd32fb01e5ae7d1cb202fef3aef1e840a"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.0"}, "time": "2021-11-20T17:00:10+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a0ad0b96c4ebef769ba5cff4cad73e2965d828d5"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a0ad0b96c4ebef769ba5cff4cad73e2965d828d5", "type": "zip", "shasum": "", "reference": "a0ad0b96c4ebef769ba5cff4cad73e2965d828d5"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.0-BETA3"}, "time": "2021-11-17T12:23:22+00:00"}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "f600177450965f25bd95ec099f26bcb17a2a8a14"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/f600177450965f25bd95ec099f26bcb17a2a8a14", "type": "zip", "shasum": "", "reference": "f600177450965f25bd95ec099f26bcb17a2a8a14"}, "support": {"source": "https://github.com/symfony/mime/tree/v6.0.0-BETA1"}, "time": "2021-11-04T17:14:40+00:00"}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/8c1b9b3e5b52981551fc6044539af1d974e39064", "type": "zip", "shasum": "", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.45"}, "time": "2024-10-23T20:18:32+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}}, {"version": "v5.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a02711d6ce461edada8c0f8641aa536709b99b47"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a02711d6ce461edada8c0f8641aa536709b99b47", "type": "zip", "shasum": "", "reference": "a02711d6ce461edada8c0f8641aa536709b99b47"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.43"}, "time": "2024-08-13T10:38:38+00:00"}, {"version": "v5.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c71c7a1aeed60b22d05e738197e31daf2120bd42"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c71c7a1aeed60b22d05e738197e31daf2120bd42", "type": "zip", "shasum": "", "reference": "c71c7a1aeed60b22d05e738197e31daf2120bd42"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.41"}, "time": "2024-06-28T09:36:24+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "8c6dc1fb0b1f990aa15086abcde66dbde3a9bdad"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/8c6dc1fb0b1f990aa15086abcde66dbde3a9bdad", "type": "zip", "shasum": "", "reference": "8c6dc1fb0b1f990aa15086abcde66dbde3a9bdad"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a5364f016fd9e090f7b4f250a97ea6925a5ca985"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a5364f016fd9e090f7b4f250a97ea6925a5ca985", "type": "zip", "shasum": "", "reference": "a5364f016fd9e090f7b4f250a97ea6925a5ca985"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "82fa6be8a0295a3932df871e88fc8c8d77aa71d4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/82fa6be8a0295a3932df871e88fc8c8d77aa71d4", "type": "zip", "shasum": "", "reference": "82fa6be8a0295a3932df871e88fc8c8d77aa71d4"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.38"}, "time": "2024-03-21T07:25:32+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ee94d9b538f93abbbc1ee4ccff374593117b04a9"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ee94d9b538f93abbbc1ee4ccff374593117b04a9", "type": "zip", "shasum": "", "reference": "ee94d9b538f93abbbc1ee4ccff374593117b04a9"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.35"}, "time": "2024-01-30T08:00:51+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2ea06dfeee20000a319d8407cea1d47533d5a9d2"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2ea06dfeee20000a319d8407cea1d47533d5a9d2", "type": "zip", "shasum": "", "reference": "2ea06dfeee20000a319d8407cea1d47533d5a9d2"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.26"}, "time": "2023-07-27T06:29:31+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.26|~6.2.13|^6.3.2"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.26|>=6,<6.2.13|>=6.3,<6.3.2"}}, {"version": "v5.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ae0a1032a450a3abf305ee44fc55ed423fbf16e3"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ae0a1032a450a3abf305ee44fc55ed423fbf16e3", "type": "zip", "shasum": "", "reference": "ae0a1032a450a3abf305ee44fc55ed423fbf16e3"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.23"}, "time": "2023-04-19T09:49:13+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.14|~6.0.14|^6.1.6"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.14|>=6.0,<6.0.14|>=6.1,<6.1.6"}}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ef57d9fb9cdd5e6b2ffc567d109865d10b6920cd"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ef57d9fb9cdd5e6b2ffc567d109865d10b6920cd", "type": "zip", "shasum": "", "reference": "ef57d9fb9cdd5e6b2ffc567d109865d10b6920cd"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.21"}, "time": "2023-02-21T19:46:44+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a858429a9c704edc53fe057228cf9ca282ba48eb"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a858429a9c704edc53fe057228cf9ca282ba48eb", "type": "zip", "shasum": "", "reference": "a858429a9c704edc53fe057228cf9ca282ba48eb"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.19"}, "time": "2023-01-09T05:43:46+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2a83d82efc91c3f03a23c8b47a896df168aa5c63"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2a83d82efc91c3f03a23c8b47a896df168aa5c63", "type": "zip", "shasum": "", "reference": "2a83d82efc91c3f03a23c8b47a896df168aa5c63"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.17"}, "time": "2022-12-13T09:59:55+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.14|~6.0.14|^6.1.6"}}, {"version": "v5.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "46eeedb08f0832b1b61a84c612d945fc85ee4734"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/46eeedb08f0832b1b61a84c612d945fc85ee4734", "type": "zip", "shasum": "", "reference": "46eeedb08f0832b1b61a84c612d945fc85ee4734"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.16"}, "time": "2022-11-26T16:45:22+00:00"}, {"version": "v5.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "1c118b253bb3495d81e95a6e3ec6c2766a98a0c4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/1c118b253bb3495d81e95a6e3ec6c2766a98a0c4", "type": "zip", "shasum": "", "reference": "1c118b253bb3495d81e95a6e3ec6c2766a98a0c4"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.14"}, "time": "2022-10-07T08:01:20+00:00"}, {"version": "v5.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "bb2ccf759e2b967dcd11bdee5bdf30dddd2290bd"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/bb2ccf759e2b967dcd11bdee5bdf30dddd2290bd", "type": "zip", "shasum": "", "reference": "bb2ccf759e2b967dcd11bdee5bdf30dddd2290bd"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.13"}, "time": "2022-09-01T18:18:29+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.2|^6.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}}, {"version": "v5.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "03876e9c5a36f5b45e7d9a381edda5421eff8a90"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/03876e9c5a36f5b45e7d9a381edda5421eff8a90", "type": "zip", "shasum": "", "reference": "03876e9c5a36f5b45e7d9a381edda5421eff8a90"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.12"}, "time": "2022-08-19T14:24:03+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "3cd175cdcdb6db2e589e837dd46aff41027d9830"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/3cd175cdcdb6db2e589e837dd46aff41027d9830", "type": "zip", "shasum": "", "reference": "3cd175cdcdb6db2e589e837dd46aff41027d9830"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.11"}, "time": "2022-07-20T11:34:24+00:00"}, {"version": "v5.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "02265e1e5111c3cd7480387af25e82378b7ab9cc"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/02265e1e5111c3cd7480387af25e82378b7ab9cc", "type": "zip", "shasum": "", "reference": "02265e1e5111c3cd7480387af25e82378b7ab9cc"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.10"}, "time": "2022-06-09T12:22:40+00:00"}, {"version": "v5.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2b3802a24e48d0cfccf885173d2aac91e73df92e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2b3802a24e48d0cfccf885173d2aac91e73df92e", "type": "zip", "shasum": "", "reference": "2b3802a24e48d0cfccf885173d2aac91e73df92e"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.9"}, "time": "2022-05-21T10:24:18+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "af49bc163ec3272f677bde3bc44c0d766c1fd662"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/af49bc163ec3272f677bde3bc44c0d766c1fd662", "type": "zip", "shasum": "", "reference": "af49bc163ec3272f677bde3bc44c0d766c1fd662"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.8"}, "time": "2022-04-12T15:48:08+00:00"}, {"version": "v5.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "92d27a34dea2e199fa9b687e3fff3a7d169b7b1c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/92d27a34dea2e199fa9b687e3fff3a7d169b7b1c", "type": "zip", "shasum": "", "reference": "92d27a34dea2e199fa9b687e3fff3a7d169b7b1c"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.7"}, "time": "2022-03-11T16:08:05+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e1503cfb5c9a225350f549d3bb99296f4abfb80f"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e1503cfb5c9a225350f549d3bb99296f4abfb80f", "type": "zip", "shasum": "", "reference": "e1503cfb5c9a225350f549d3bb99296f4abfb80f"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.3"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "1bfd938cf9562822c05c4d00e8f92134d3c8e42d"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/1bfd938cf9562822c05c4d00e8f92134d3c8e42d", "type": "zip", "shasum": "", "reference": "1bfd938cf9562822c05c4d00e8f92134d3c8e42d"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.2"}, "time": "2021-12-28T17:15:56+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d4365000217b67c01acff407573906ff91bcfb34"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d4365000217b67c01acff407573906ff91bcfb34", "type": "zip", "shasum": "", "reference": "d4365000217b67c01acff407573906ff91bcfb34"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.0"}, "time": "2021-11-23T10:19:22+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d2ca59b995bd714031c0bc8528415ee54be430e8"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d2ca59b995bd714031c0bc8528415ee54be430e8", "type": "zip", "shasum": "", "reference": "d2ca59b995bd714031c0bc8528415ee54be430e8"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.0-BETA3"}, "time": "2021-11-17T12:18:18+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d75d8cd9aa5ad257ed1b958f042c7a2d7cf505d5"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d75d8cd9aa5ad257ed1b958f042c7a2d7cf505d5", "type": "zip", "shasum": "", "reference": "d75d8cd9aa5ad257ed1b958f042c7a2d7cf505d5"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.4.0-BETA1"}, "time": "2021-11-04T16:48:04+00:00"}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2769b338f999a7c53a88e3c124a3d69d7d3feb49"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2769b338f999a7c53a88e3c124a3d69d7d3feb49", "type": "zip", "shasum": "", "reference": "2769b338f999a7c53a88e3c124a3d69d7d3feb49"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00", "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d6b8a06d0d344977141c3f929e335dc4e440b0ae"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d6b8a06d0d344977141c3f929e335dc4e440b0ae", "type": "zip", "shasum": "", "reference": "d6b8a06d0d344977141c3f929e335dc4e440b0ae"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.13"}, "time": "2021-12-28T14:16:49+00:00"}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "dffc0684f10526db12c52fcd6238c64695426d61"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/dffc0684f10526db12c52fcd6238c64695426d61", "type": "zip", "shasum": "", "reference": "dffc0684f10526db12c52fcd6238c64695426d61"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.11"}, "time": "2021-11-20T16:42:42+00:00"}, {"version": "v5.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a756033d0a7e53db389618653ae991eba5a19a11"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a756033d0a7e53db389618653ae991eba5a19a11", "type": "zip", "shasum": "", "reference": "a756033d0a7e53db389618653ae991eba5a19a11"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.8"}, "time": "2021-09-10T12:30:38+00:00"}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ae887cb3b044658676129f5e97aeb7e9eb69c2d8"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ae887cb3b044658676129f5e97aeb7e9eb69c2d8", "type": "zip", "shasum": "", "reference": "ae887cb3b044658676129f5e97aeb7e9eb69c2d8"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.7"}, "time": "2021-08-20T11:40:01+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "633e4e8afe9e529e5599d71238849a4218dd497b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/633e4e8afe9e529e5599d71238849a4218dd497b", "type": "zip", "shasum": "", "reference": "633e4e8afe9e529e5599d71238849a4218dd497b"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.4"}, "time": "2021-07-21T12:40:44+00:00"}, {"version": "v5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "47dd7912152b82d0d4c8d9040dbc93d6232d472a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/47dd7912152b82d0d4c8d9040dbc93d6232d472a", "type": "zip", "shasum": "", "reference": "47dd7912152b82d0d4c8d9040dbc93d6232d472a"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.2"}, "time": "2021-06-09T10:58:01+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ed710d297b181f6a7194d8172c9c2423d58e4852"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ed710d297b181f6a7194d8172c9c2423d58e4852", "type": "zip", "shasum": "", "reference": "ed710d297b181f6a7194d8172c9c2423d58e4852"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.0"}, "time": "2021-05-26T17:43:10+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0c3daded809edda4adee7179122a2a98069d8df7"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0c3daded809edda4adee7179122a2a98069d8df7", "type": "zip", "shasum": "", "reference": "0c3daded809edda4adee7179122a2a98069d8df7"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.0-RC1"}, "time": "2021-05-16T13:08:56+00:00"}, {"version": "v5.3.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "45c850ddfd10504399c1048a3b26be89335938a9"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/45c850ddfd10504399c1048a3b26be89335938a9", "type": "zip", "shasum": "", "reference": "45c850ddfd10504399c1048a3b26be89335938a9"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.0-BETA3"}, "time": "2021-05-07T13:12:11+00:00"}, {"version": "v5.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "6b300fc7cabc779d5e2448faa1ea0397bf69a64a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/6b300fc7cabc779d5e2448faa1ea0397bf69a64a", "type": "zip", "shasum": "", "reference": "6b300fc7cabc779d5e2448faa1ea0397bf69a64a"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.0-BETA2"}, "time": "2021-04-29T20:52:27+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "80690182cbcd32ddbd3e2f1f4d1f3628f3ac1326"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/80690182cbcd32ddbd3e2f1f4d1f3628f3ac1326", "type": "zip", "shasum": "", "reference": "80690182cbcd32ddbd3e2f1f4d1f3628f3ac1326"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.3.0-BETA1"}, "time": "2021-04-08T10:31:48+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "6ad63acd694b30e75fb2426b037e92859df73d6b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/6ad63acd694b30e75fb2426b037e92859df73d6b", "type": "zip", "shasum": "", "reference": "6ad63acd694b30e75fb2426b037e92859df73d6b"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.12"}, "time": "2021-07-21T12:38:00+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c1299e8a2b3f9b22b621fa5f58bea72cd51d9a27"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c1299e8a2b3f9b22b621fa5f58bea72cd51d9a27", "type": "zip", "shasum": "", "reference": "c1299e8a2b3f9b22b621fa5f58bea72cd51d9a27"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.11"}, "time": "2021-06-09T10:57:10+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0cb96ba3149255f5e5e257db9ea38ef86e154111"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0cb96ba3149255f5e5e257db9ea38ef86e154111", "type": "zip", "shasum": "", "reference": "0cb96ba3149255f5e5e257db9ea38ef86e154111"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.10"}, "time": "2021-05-26T12:52:38+00:00"}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "64258e870f8cc75c3dae986201ea2df58c210b52"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/64258e870f8cc75c3dae986201ea2df58c210b52", "type": "zip", "shasum": "", "reference": "64258e870f8cc75c3dae986201ea2df58c210b52"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.9"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7af452bf51c46f18da00feb32e1ad36db9426515"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7af452bf51c46f18da00feb32e1ad36db9426515", "type": "zip", "shasum": "", "reference": "7af452bf51c46f18da00feb32e1ad36db9426515"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.7"}, "time": "2021-04-29T20:47:09+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "1b2092244374cbe48ae733673f2ca0818b37197b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/1b2092244374cbe48ae733673f2ca0818b37197b", "type": "zip", "shasum": "", "reference": "1b2092244374cbe48ae733673f2ca0818b37197b"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.6"}, "time": "2021-03-12T13:18:39+00:00"}, {"version": "v5.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "554ba128f1955038b45db5e1fa7e93bfc683b139"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/554ba128f1955038b45db5e1fa7e93bfc683b139", "type": "zip", "shasum": "", "reference": "554ba128f1955038b45db5e1fa7e93bfc683b139"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.5"}, "time": "2021-03-07T16:08:20+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5155d2fe14ef1eb150e3bdbbc1ec1455df95e9cd"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5155d2fe14ef1eb150e3bdbbc1ec1455df95e9cd", "type": "zip", "shasum": "", "reference": "5155d2fe14ef1eb150e3bdbbc1ec1455df95e9cd"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.4"}, "time": "2021-02-15T18:55:04+00:00", "require-dev": {"egulias/email-validator": "^2.1.10", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7dee6a43493f39b51ff6c5bb2bd576fe40a76c86"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7dee6a43493f39b51ff6c5bb2bd576fe40a76c86", "type": "zip", "shasum": "", "reference": "7dee6a43493f39b51ff6c5bb2bd576fe40a76c86"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.3"}, "time": "2021-02-02T06:10:15+00:00"}, {"version": "v5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "37bade585ea100d235c031b258eff93b5b6bb9a9"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/37bade585ea100d235c031b258eff93b5b6bb9a9", "type": "zip", "shasum": "", "reference": "37bade585ea100d235c031b258eff93b5b6bb9a9"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.2"}, "time": "2021-01-25T14:08:25+00:00"}, {"description": "A library to manipulate MIME messages", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "de97005aef7426ba008c46ba840fc301df577ada"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/de97005aef7426ba008c46ba840fc301df577ada", "type": "zip", "shasum": "", "reference": "de97005aef7426ba008c46ba840fc301df577ada"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.1"}, "time": "2020-12-09T18:54:12+00:00", "conflict": {"symfony/mailer": "<4.4"}}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "05f667e8fa029568964fd3bec6bc17765b853cc5"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/05f667e8fa029568964fd3bec6bc17765b853cc5", "type": "zip", "shasum": "", "reference": "05f667e8fa029568964fd3bec6bc17765b853cc5"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.0"}, "time": "2020-10-30T14:55:39+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/mime/tree/v5.2.0-RC2"}}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/mime/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "be1339811a39c6fb35fcf4616d5f1d00ff50ad33"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/be1339811a39c6fb35fcf4616d5f1d00ff50ad33", "type": "zip", "shasum": "", "reference": "be1339811a39c6fb35fcf4616d5f1d00ff50ad33"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:08:07+00:00", "require": {"php": ">=7.2.5", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^4.4|^5.0"}}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "9d96f799a2fb72d68b176d589eb7bfdb05367b77"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/9d96f799a2fb72d68b176d589eb7bfdb05367b77", "type": "zip", "shasum": "", "reference": "9d96f799a2fb72d68b176d589eb7bfdb05367b77"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "aa1d922a7c32aedc10f6647ded250535ee3c6346"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/aa1d922a7c32aedc10f6647ded250535ee3c6346", "type": "zip", "shasum": "", "reference": "aa1d922a7c32aedc10f6647ded250535ee3c6346"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.2.0-BETA1"}, "time": "2020-10-04T08:36:21+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Allows manipulating MIME messages", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "d7d899822da1fa89bcf658e8e8d836f5578e6f7a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/d7d899822da1fa89bcf658e8e8d836f5578e6f7a", "type": "zip", "shasum": "", "reference": "d7d899822da1fa89bcf658e8e8d836f5578e6f7a"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}, "extra": "__unset"}, {"description": "A library to manipulate MIME messages", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5d98ca27d3cf0242ef43efac051980abeb45aaff"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5d98ca27d3cf0242ef43efac051980abeb45aaff", "type": "zip", "shasum": "", "reference": "5d98ca27d3cf0242ef43efac051980abeb45aaff"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.1.10"}, "time": "2020-12-09T18:49:55+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "698cab643bbb517a0d9d317e2ec9fc1636e97f1f"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/698cab643bbb517a0d9d317e2ec9fc1636e97f1f", "type": "zip", "shasum": "", "reference": "698cab643bbb517a0d9d317e2ec9fc1636e97f1f"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.1.9"}, "time": "2020-10-28T21:31:18+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "f5485a92c24d4bcfc2f3fc648744fb398482ff1b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/f5485a92c24d4bcfc2f3fc648744fb398482ff1b", "type": "zip", "shasum": "", "reference": "f5485a92c24d4bcfc2f3fc648744fb398482ff1b"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "4404d6545125863561721514ad9388db2661eec5"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/4404d6545125863561721514ad9388db2661eec5", "type": "zip", "shasum": "", "reference": "4404d6545125863561721514ad9388db2661eec5"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.1.7"}, "time": "2020-09-02T16:23:27+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/5.1"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "89a2c9b4cb7b5aa516cf55f5194c384f444c81dc"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/89a2c9b4cb7b5aa516cf55f5194c384f444c81dc", "type": "zip", "shasum": "", "reference": "89a2c9b4cb7b5aa516cf55f5194c384f444c81dc"}, "time": "2020-08-17T10:01:29+00:00"}, {"version": "v5.1.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/v5.1.4"}}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "149fb0ad35aae3c7637b496b38478797fa6a7ea6"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/149fb0ad35aae3c7637b496b38478797fa6a7ea6", "type": "zip", "shasum": "", "reference": "149fb0ad35aae3c7637b496b38478797fa6a7ea6"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.1.3"}, "time": "2020-07-23T10:04:31+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c0c418f05e727606e85b482a8591519c4712cf45"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c0c418f05e727606e85b482a8591519c4712cf45", "type": "zip", "shasum": "", "reference": "c0c418f05e727606e85b482a8591519c4712cf45"}, "support": {"source": "https://github.com/symfony/mime/tree/5.1"}, "time": "2020-06-09T15:07:35+00:00"}, {"version": "v5.1.1", "version_normalized": "*******"}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "56261f89385f9d13cf843a5101ac72131190bc91"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/56261f89385f9d13cf843a5101ac72131190bc91", "type": "zip", "shasum": "", "reference": "56261f89385f9d13cf843a5101ac72131190bc91"}, "time": "2020-05-25T12:33:44+00:00"}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/mime/tree/v5.1.0-RC2"}}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "b439f550b001f1321fa4e2ca282a53c7ad514a45"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/b439f550b001f1321fa4e2ca282a53c7ad514a45", "type": "zip", "shasum": "", "reference": "b439f550b001f1321fa4e2ca282a53c7ad514a45"}, "support": {"source": "https://github.com/symfony/mime/tree/5.1"}, "time": "2020-05-09T15:57:56+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e2c903bce82b6840609a910cad7dd0fa148dad7c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e2c903bce82b6840609a910cad7dd0fa148dad7c", "type": "zip", "shasum": "", "reference": "e2c903bce82b6840609a910cad7dd0fa148dad7c"}, "support": {"source": "https://github.com/symfony/mime/tree/master"}, "time": "2020-04-18T20:50:29+00:00"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "aa2b2013a8d380e3980a29a79cc0fbcfb02fb920"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/aa2b2013a8d380e3980a29a79cc0fbcfb02fb920", "type": "zip", "shasum": "", "reference": "aa2b2013a8d380e3980a29a79cc0fbcfb02fb920"}, "support": {"source": "https://github.com/symfony/mime/tree/5.0"}, "time": "2020-07-23T10:04:24+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "19e98b0a9f8a13fe056cd69243a68df519f3b907"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/19e98b0a9f8a13fe056cd69243a68df519f3b907", "type": "zip", "shasum": "", "reference": "19e98b0a9f8a13fe056cd69243a68df519f3b907"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.10"}, "time": "2020-06-09T15:07:20+00:00"}, {"version": "v5.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c7fb653965541595e89847fddf60860be42513ba"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c7fb653965541595e89847fddf60860be42513ba", "type": "zip", "shasum": "", "reference": "c7fb653965541595e89847fddf60860be42513ba"}, "support": {"source": "https://github.com/symfony/mime/tree/5.0"}, "time": "2020-05-25T12:33:25+00:00"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5d6c81c39225a750f3f43bee15f03093fb9aaa0b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5d6c81c39225a750f3f43bee15f03093fb9aaa0b", "type": "zip", "shasum": "", "reference": "5d6c81c39225a750f3f43bee15f03093fb9aaa0b"}, "time": "2020-04-17T03:29:44+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}}, {"version": "v5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "481b7d6da88922fb1e0d86a943987722b08f3955"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/481b7d6da88922fb1e0d86a943987722b08f3955", "type": "zip", "shasum": "", "reference": "481b7d6da88922fb1e0d86a943987722b08f3955"}, "time": "2020-03-27T16:56:45+00:00"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e9927cabc1519d2498d02b743f2cab6e4722ad3d"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e9927cabc1519d2498d02b743f2cab6e4722ad3d", "type": "zip", "shasum": "", "reference": "e9927cabc1519d2498d02b743f2cab6e4722ad3d"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.6"}, "time": "2020-03-16T12:10:54+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "9b3e5b5e58c56bbd76628c952d2b78556d305f3c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/9b3e5b5e58c56bbd76628c952d2b78556d305f3c", "type": "zip", "shasum": "", "reference": "9b3e5b5e58c56bbd76628c952d2b78556d305f3c"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.5"}, "time": "2020-02-04T09:41:09+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2a3c7fee1f1a0961fa9cf360d5da553d05095e59"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2a3c7fee1f1a0961fa9cf360d5da553d05095e59", "type": "zip", "shasum": "", "reference": "2a3c7fee1f1a0961fa9cf360d5da553d05095e59"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.4"}, "time": "2020-01-04T14:08:26+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/5.0"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0e6a4ced216e49d457eddcefb61132173a876d79"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0e6a4ced216e49d457eddcefb61132173a876d79", "type": "zip", "shasum": "", "reference": "0e6a4ced216e49d457eddcefb61132173a876d79"}, "time": "2019-11-30T14:12:50+00:00"}, {"version": "v5.0.1", "version_normalized": "*******"}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "76f3c09b7382bf979af7bcd8e6f8033f1324285e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/76f3c09b7382bf979af7bcd8e6f8033f1324285e", "type": "zip", "shasum": "", "reference": "76f3c09b7382bf979af7bcd8e6f8033f1324285e"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.0"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "fb9a9728ad1b2da6c4440885e8c879ab3ff8cb35"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/fb9a9728ad1b2da6c4440885e8c879ab3ff8cb35", "type": "zip", "shasum": "", "reference": "fb9a9728ad1b2da6c4440885e8c879ab3ff8cb35"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.0-RC1"}, "time": "2019-11-13T07:42:48+00:00", "require": {"php": "^7.2.9", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/mime/tree/master"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c4457ae2453d8d2a10b15fc4adefea517131047a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c4457ae2453d8d2a10b15fc4adefea517131047a", "type": "zip", "shasum": "", "reference": "c4457ae2453d8d2a10b15fc4adefea517131047a"}, "support": {"source": "https://github.com/symfony/mime/tree/v5.0.0-BETA1"}, "time": "2019-10-30T13:25:47+00:00"}, {"description": "Allows manipulating MIME messages", "version": "v4.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0eaf33cd6d1b3eaa50e7bc48b17f6e45789df35d"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0eaf33cd6d1b3eaa50e7bc48b17f6e45789df35d", "type": "zip", "shasum": "", "reference": "0eaf33cd6d1b3eaa50e7bc48b17f6e45789df35d"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "symfony/mailer": "<4.4"}, "extra": "__unset"}, {"version": "v4.4.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "e90a24bd2ae19739bf9cdb1facca3120e8b4b699"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/e90a24bd2ae19739bf9cdb1facca3120e8b4b699", "type": "zip", "shasum": "", "reference": "e90a24bd2ae19739bf9cdb1facca3120e8b4b699"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.46"}, "time": "2022-08-30T19:52:09+00:00"}, {"version": "v4.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "44d515bb6add9828efe6a9e176483f6b13b17367"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/44d515bb6add9828efe6a9e176483f6b13b17367", "type": "zip", "shasum": "", "reference": "44d515bb6add9828efe6a9e176483f6b13b17367"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.45"}, "time": "2022-08-18T21:14:46+00:00"}, {"version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "72748976b0cc82b44fcae1d66c9df7b598b543c4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/72748976b0cc82b44fcae1d66c9df7b598b543c4", "type": "zip", "shasum": "", "reference": "72748976b0cc82b44fcae1d66c9df7b598b543c4"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"version": "v4.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "de46889e8844d8327677582950bd227273d8f2f3"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/de46889e8844d8327677582950bd227273d8f2f3", "type": "zip", "shasum": "", "reference": "de46889e8844d8327677582950bd227273d8f2f3"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.43"}, "time": "2022-06-01T19:35:40+00:00"}, {"version": "v4.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "f4f3e994024f16c1d6ca8338c62a10e0767314fc"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/f4f3e994024f16c1d6ca8338c62a10e0767314fc", "type": "zip", "shasum": "", "reference": "f4f3e994024f16c1d6ca8338c62a10e0767314fc"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.42"}, "time": "2022-05-21T10:10:45+00:00"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "5b05a62a714bb7e8ba1ce7412f7442debe67c291"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/5b05a62a714bb7e8ba1ce7412f7442debe67c291", "type": "zip", "shasum": "", "reference": "5b05a62a714bb7e8ba1ce7412f7442debe67c291"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.41"}, "time": "2022-04-12T15:19:55+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "a4fb074827e59a80bc3c5df4657fa782bac7cdab"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/a4fb074827e59a80bc3c5df4657fa782bac7cdab", "type": "zip", "shasum": "", "reference": "a4fb074827e59a80bc3c5df4657fa782bac7cdab"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "fee42d10c8920b2308f466269cbf924ddc4fce94"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/fee42d10c8920b2308f466269cbf924ddc4fce94", "type": "zip", "shasum": "", "reference": "fee42d10c8920b2308f466269cbf924ddc4fce94"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.36"}, "time": "2021-12-25T19:39:39+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "dd269a4da0b7c227b8fa910940588fd1bae08493"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/dd269a4da0b7c227b8fa910940588fd1bae08493", "type": "zip", "shasum": "", "reference": "dd269a4da0b7c227b8fa910940588fd1bae08493"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.34"}, "time": "2021-11-19T11:24:24+00:00"}, {"version": "v4.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "c4fd68f54f608c639ddebecfc61746a86134bf4a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/c4fd68f54f608c639ddebecfc61746a86134bf4a", "type": "zip", "shasum": "", "reference": "c4fd68f54f608c639ddebecfc61746a86134bf4a"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.31"}, "time": "2021-09-10T10:18:20+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "6ab91e811439360339ebde803630c8d74223fa77"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/6ab91e811439360339ebde803630c8d74223fa77", "type": "zip", "shasum": "", "reference": "6ab91e811439360339ebde803630c8d74223fa77"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.27"}, "time": "2021-07-21T12:19:41+00:00"}, {"version": "v4.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "1a2bdd55e13e295f63a57a5838029bf41b1969bf"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/1a2bdd55e13e295f63a57a5838029bf41b1969bf", "type": "zip", "shasum": "", "reference": "1a2bdd55e13e295f63a57a5838029bf41b1969bf"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.26"}, "time": "2021-06-08T11:22:53+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "264565f4cb0a696bc914f4923214a5527e67e742"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/264565f4cb0a696bc914f4923214a5527e67e742", "type": "zip", "shasum": "", "reference": "264565f4cb0a696bc914f4923214a5527e67e742"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.25"}, "time": "2021-05-26T11:20:16+00:00"}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7e8e9192500d0bae9f6aff60c842befc7d887b68"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7e8e9192500d0bae9f6aff60c842befc7d887b68", "type": "zip", "shasum": "", "reference": "7e8e9192500d0bae9f6aff60c842befc7d887b68"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.24"}, "time": "2021-05-16T09:52:47+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "36f2e59c90762bb09170553130a4dc1934cada58"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/36f2e59c90762bb09170553130a4dc1934cada58", "type": "zip", "shasum": "", "reference": "36f2e59c90762bb09170553130a4dc1934cada58"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.22"}, "time": "2021-04-27T14:58:50+00:00"}, {"version": "v4.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "50d7a1d569edad1f1321c59123c4c322c8daff7c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/50d7a1d569edad1f1321c59123c4c322c8daff7c", "type": "zip", "shasum": "", "reference": "50d7a1d569edad1f1321c59123c4c322c8daff7c"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.21"}, "time": "2021-03-12T08:47:38+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "6db092f97cd6eee8d4b2026e3a8fa3f576b396d4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/6db092f97cd6eee8d4b2026e3a8fa3f576b396d4", "type": "zip", "shasum": "", "reference": "6db092f97cd6eee8d4b2026e3a8fa3f576b396d4"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.20"}, "time": "2021-02-14T12:29:41+00:00", "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "conflict": {"symfony/mailer": "<4.4"}}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7f50c27c7417115ca620962b853a7f4db0479e7c"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7f50c27c7417115ca620962b853a7f4db0479e7c", "type": "zip", "shasum": "", "reference": "7f50c27c7417115ca620962b853a7f4db0479e7c"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00"}, {"description": "A library to manipulate MIME messages", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7a4176a1cbc4cc99268c531de547fccbd0beb370"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7a4176a1cbc4cc99268c531de547fccbd0beb370", "type": "zip", "shasum": "", "reference": "7a4176a1cbc4cc99268c531de547fccbd0beb370"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.18"}, "time": "2020-12-09T11:15:38+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "4148b752f7e961931887410513ce3d9e267d25f2"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/4148b752f7e961931887410513ce3d9e267d25f2", "type": "zip", "shasum": "", "reference": "4148b752f7e961931887410513ce3d9e267d25f2"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.17"}, "time": "2020-10-28T20:42:29+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "360f9963b6d4db6c3454d58548fb2b085f97d3e2"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/360f9963b6d4db6c3454d58548fb2b085f97d3e2", "type": "zip", "shasum": "", "reference": "360f9963b6d4db6c3454d58548fb2b085f97d3e2"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "42df2507eb8e6cd9795f51c99dd52bab543a918f"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/42df2507eb8e6cd9795f51c99dd52bab543a918f", "type": "zip", "shasum": "", "reference": "42df2507eb8e6cd9795f51c99dd52bab543a918f"}, "support": {"source": "https://github.com/symfony/mime/tree/4.4"}, "time": "2020-09-02T16:08:58+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "support": {"source": "https://github.com/symfony/mime/tree/v4.4.14"}}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "50ad671306d3d3ffb888d95b4fb1859496831e3a"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/50ad671306d3d3ffb888d95b4fb1859496831e3a", "type": "zip", "shasum": "", "reference": "50ad671306d3d3ffb888d95b4fb1859496831e3a"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.13"}, "time": "2020-08-17T09:56:45+00:00"}, {"version": "v4.4.12", "version_normalized": "********", "support": {"source": "https://github.com/symfony/mime/tree/4.4"}}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "cb00d7210bc096f997e63189a62b5e35d72babac"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/cb00d7210bc096f997e63189a62b5e35d72babac", "type": "zip", "shasum": "", "reference": "cb00d7210bc096f997e63189a62b5e35d72babac"}, "time": "2020-07-22T12:10:07+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "af8e69e7527f752ab0ef6e1b717bac3f7336b8c6"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/af8e69e7527f752ab0ef6e1b717bac3f7336b8c6", "type": "zip", "shasum": "", "reference": "af8e69e7527f752ab0ef6e1b717bac3f7336b8c6"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.10"}, "time": "2020-06-09T09:16:12+00:00"}, {"version": "v4.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "2adc53069becd0de3ea2748438646610ad0968db"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/2adc53069becd0de3ea2748438646610ad0968db", "type": "zip", "shasum": "", "reference": "2adc53069becd0de3ea2748438646610ad0968db"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.9"}, "time": "2020-05-25T05:42:33+00:00"}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "7a583ffb6c7dd5aabb5db920817a3cc39261c517"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/7a583ffb6c7dd5aabb5db920817a3cc39261c517", "type": "zip", "shasum": "", "reference": "7a583ffb6c7dd5aabb5db920817a3cc39261c517"}, "support": {"source": "https://github.com/symfony/mime/tree/4.4"}, "time": "2020-04-16T14:49:30+00:00", "require": {"php": "^7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}}, {"version": "v4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "6dde9dc70155e91b850b1d009d1f841c54bc4aba"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/6dde9dc70155e91b850b1d009d1f841c54bc4aba", "type": "zip", "shasum": "", "reference": "6dde9dc70155e91b850b1d009d1f841c54bc4aba"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.7"}, "time": "2020-03-27T16:54:36+00:00"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "f6be9d809d805ab5bdb12f2d5843ba2c78533c7e"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/f6be9d809d805ab5bdb12f2d5843ba2c78533c7e", "type": "zip", "shasum": "", "reference": "f6be9d809d805ab5bdb12f2d5843ba2c78533c7e"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.6"}, "time": "2020-03-16T11:24:17+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "304db017bafd71c122cd5223a9ac2d03dc24da32"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/304db017bafd71c122cd5223a9ac2d03dc24da32", "type": "zip", "shasum": "", "reference": "304db017bafd71c122cd5223a9ac2d03dc24da32"}, "support": {"source": "https://github.com/symfony/mime/tree/4.4"}, "funding": [], "time": "2020-02-04T09:32:40+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "225034620ecd4b34fd826e9983d85e2b7a359094"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/225034620ecd4b34fd826e9983d85e2b7a359094", "type": "zip", "shasum": "", "reference": "225034620ecd4b34fd826e9983d85e2b7a359094"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.4"}, "time": "2020-01-04T13:00:46+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/4.4"}}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "010cc488e56cafe5f7494dea70aea93100c234df"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/010cc488e56cafe5f7494dea70aea93100c234df", "type": "zip", "shasum": "", "reference": "010cc488e56cafe5f7494dea70aea93100c234df"}, "time": "2019-11-30T08:27:26+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/v4.4.1"}}, {"version": "v4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "89da7b68b7149aab065c09b97f938753ab52831f"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/89da7b68b7149aab065c09b97f938753ab52831f", "type": "zip", "shasum": "", "reference": "89da7b68b7149aab065c09b97f938753ab52831f"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.4.0-BETA2"}, "time": "2019-11-13T07:39:40+00:00"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "bf6913d054a319f3851a3f840463086f1b145655"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/bf6913d054a319f3851a3f840463086f1b145655", "type": "zip", "shasum": "", "reference": "bf6913d054a319f3851a3f840463086f1b145655"}, "support": {"source": "https://github.com/symfony/mime/tree/4.4"}, "time": "2019-10-30T13:23:44+00:00"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "50f65ca2a6c33702728024d33e4b9461f67623c4"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/50f65ca2a6c33702728024d33e4b9461f67623c4", "type": "zip", "shasum": "", "reference": "50f65ca2a6c33702728024d33e4b9461f67623c4"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.3.11"}, "time": "2020-01-01T11:51:43+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "~3.4|^4.1"}, "conflict": "__unset"}, {"version": "v4.3.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/mime/tree/4.3"}}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "22aecf6b11638ef378fab25d6c5a2da8a31a1448"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/22aecf6b11638ef378fab25d6c5a2da8a31a1448", "type": "zip", "shasum": "", "reference": "22aecf6b11638ef378fab25d6c5a2da8a31a1448"}, "time": "2019-11-12T13:10:02+00:00"}, {"version": "v4.3.8", "version_normalized": "*******"}, {"version": "v4.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "3c0e197529da6e59b217615ba8ee7604df88b551"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/3c0e197529da6e59b217615ba8ee7604df88b551", "type": "zip", "shasum": "", "reference": "3c0e197529da6e59b217615ba8ee7604df88b551"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.3.7"}, "time": "2019-10-30T12:58:49+00:00"}, {"version": "v4.3.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/v4.3.6"}}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "32f71570547b91879fdbd9cf50317d556ae86916"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/32f71570547b91879fdbd9cf50317d556ae86916", "type": "zip", "shasum": "", "reference": "32f71570547b91879fdbd9cf50317d556ae86916"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.3.5"}, "time": "2019-09-19T17:00:15+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "987a05df1c6ac259b34008b932551353f4f408df"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/987a05df1c6ac259b34008b932551353f4f408df", "type": "zip", "shasum": "", "reference": "987a05df1c6ac259b34008b932551353f4f408df"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.3.4"}, "time": "2019-08-22T08:16:11+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "6b7148029b1dd5eda1502064f06d01357b7b2d8b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/6b7148029b1dd5eda1502064f06d01357b7b2d8b", "type": "zip", "shasum": "", "reference": "6b7148029b1dd5eda1502064f06d01357b7b2d8b"}, "support": {"source": "https://github.com/symfony/mime/tree/v4.3.3"}, "time": "2019-07-19T16:21:19+00:00", "require-dev": {"egulias/email-validator": "^2.0", "symfony/dependency-injection": "~3.4|^4.1"}}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "ec2c5565de60e03f33d4296a655e3273f0ad1f8b"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/ec2c5565de60e03f33d4296a655e3273f0ad1f8b", "type": "zip", "shasum": "", "reference": "ec2c5565de60e03f33d4296a655e3273f0ad1f8b"}, "support": {"source": "https://github.com/symfony/mime/tree/4.3"}, "time": "2019-06-04T09:22:54+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/mime/tree/v4.3.1"}}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "0b166aee243364cd9de05755d2e9651876090abb"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/0b166aee243364cd9de05755d2e9651876090abb", "type": "zip", "shasum": "", "reference": "0b166aee243364cd9de05755d2e9651876090abb"}, "support": {"source": "https://github.com/symfony/mime/tree/4.3"}, "time": "2019-05-22T13:16:28+00:00"}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/mime.git", "type": "git", "reference": "32cf67fe03e308904f36609c813b78bc34ebaccb"}, "dist": {"url": "https://api.github.com/repos/symfony/mime/zipball/32cf67fe03e308904f36609c813b78bc34ebaccb", "type": "zip", "shasum": "", "reference": "32cf67fe03e308904f36609c813b78bc34ebaccb"}, "support": {"source": "https://github.com/symfony/mime/tree/master"}, "time": "2019-04-17T15:26:35+00:00"}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1"}]}, "security-advisories": [{"advisoryId": "PKSA-7y15-t1fp-w94f", "affectedVersions": ">=4.3.0,<4.3.8"}], "last-modified": "Thu, 29 May 2025 07:50:05 GMT"}