{"minified": "composer/2.0", "packages": {"amphp/serialization": [{"name": "amphp/serialization", "description": "Serialization tools for IPC and data storage in PHP.", "keywords": ["serialize", "serialization", "async", "asynchronous"], "homepage": "https://github.com/amphp/serialization", "version": "v1.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/serialization.git", "type": "git", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1"}, "dist": {"url": "https://api.github.com/repos/amphp/serialization/zipball/693e77b2fb0b266c3c7d622317f881de44ae94a1", "type": "zip", "shasum": "", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1"}, "type": "library", "support": {"issues": "https://github.com/amphp/serialization/issues", "source": "https://github.com/amphp/serialization/tree/master"}, "funding": [], "time": "2020-03-25T21:39:07+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Serialization\\": "src"}}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^9 || ^8 || ^7", "amphp/php-cs-fixer-config": "dev-master"}}]}, "security-advisories": [], "last-modified": "Fri, 29 Mar 2024 21:26:54 GMT"}