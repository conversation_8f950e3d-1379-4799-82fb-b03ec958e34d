{"minified": "composer/2.0", "packages": {"nette/schema": [{"name": "nette/schema", "description": "📐 Nette Schema: validating data structures against a given Schema.", "keywords": ["config", "nette"], "homepage": "https://nette.org", "version": "v1.3.2", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "da801d52f0354f70a638673c4a0f04e16529431d"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/da801d52f0354f70a638673c4a0f04e16529431d", "type": "zip", "shasum": "", "reference": "da801d52f0354f70a638673c4a0f04e16529431d"}, "type": "library", "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.2"}, "funding": [], "time": "2024-10-06T23:10:23+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "require": {"php": "8.1 - 8.4", "nette/utils": "^4.0"}, "require-dev": {"nette/tester": "^2.5.2", "tracy/tracy": "^2.8", "phpstan/phpstan-nette": "^1.0"}}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "type": "zip", "shasum": "", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.0"}, "time": "2023-12-11T11:54:22+00:00", "require": {"php": "8.1 - 8.3", "nette/utils": "^4.0"}, "require-dev": {"nette/tester": "^2.4", "tracy/tracy": "^2.8", "phpstan/phpstan-nette": "^1.0"}}, {"version": "v1.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/0462f0166e823aad657c9224d0f849ecac1ba10a", "type": "zip", "shasum": "", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.5"}, "time": "2023-10-05T20:37:59+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "require": {"php": "7.1 - 8.3", "nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "tracy/tracy": "^2.7", "phpstan/phpstan-nette": "^1.0"}}, {"version": "v1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "c9ff517a53903b3d4e29ec547fb20feecb05b8ab"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/c9ff517a53903b3d4e29ec547fb20feecb05b8ab", "type": "zip", "shasum": "", "reference": "c9ff517a53903b3d4e29ec547fb20feecb05b8ab"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.4"}, "time": "2023-08-05T18:56:25+00:00"}, {"version": "v1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "abbdbb70e0245d5f3bf77874cea1dfb0c930d06f"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/abbdbb70e0245d5f3bf77874cea1dfb0c930d06f", "type": "zip", "shasum": "", "reference": "abbdbb70e0245d5f3bf77874cea1dfb0c930d06f"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.3"}, "time": "2022-10-13T01:24:26+00:00", "require": {"php": ">=7.1 <8.3", "nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0"}}, {"version": "v1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/9a39cef03a5b34c7de64f551538cbba05c2be5df", "type": "zip", "shasum": "", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.2"}, "time": "2021-10-15T11:40:02+00:00", "require": {"php": ">=7.1 <8.2", "nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "tracy/tracy": "^2.7", "phpstan/phpstan-nette": "^0.12"}}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "f5ed39fc96358f922cedfd1e516f0dadf5d2be0d"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/f5ed39fc96358f922cedfd1e516f0dadf5d2be0d", "type": "zip", "shasum": "", "reference": "f5ed39fc96358f922cedfd1e516f0dadf5d2be0d"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.1"}, "time": "2021-03-04T17:51:11+00:00", "require": {"php": ">=7.1 <8.1", "nette/utils": "^3.1.4 || ^4.0"}, "require-dev": {"phpstan/phpstan-nette": "^0.12", "nette/tester": "^2.3 || ^2.4", "tracy/tracy": "^2.7"}}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "9962564311f4affebd63f9cab014ab69266306ce"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/9962564311f4affebd63f9cab014ab69266306ce", "type": "zip", "shasum": "", "reference": "9962564311f4affebd63f9cab014ab69266306ce"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.0"}, "time": "2021-01-21T14:49:23+00:00", "require": {"php": ">=7.1 <8.1", "nette/utils": "^3.1.4"}, "require-dev": {"nette/tester": "^2.2", "tracy/tracy": "^2.3", "phpstan/phpstan-nette": "^0.12"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "3b6b9702a2a54170c81d157b65570fac871caba0"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/3b6b9702a2a54170c81d157b65570fac871caba0", "type": "zip", "shasum": "", "reference": "3b6b9702a2a54170c81d157b65570fac871caba0"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.1.0"}, "time": "2020-12-20T22:18:49+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "74b4b51ee8d3d44db1b43afc4017c56bfd0b8772"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/74b4b51ee8d3d44db1b43afc4017c56bfd0b8772", "type": "zip", "shasum": "", "reference": "74b4b51ee8d3d44db1b43afc4017c56bfd0b8772"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.0.4"}, "time": "2020-12-17T23:40:10+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "34baf9eca75eccdad3d04306c5d6bec0f6b252ad"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/34baf9eca75eccdad3d04306c5d6bec0f6b252ad", "type": "zip", "shasum": "", "reference": "34baf9eca75eccdad3d04306c5d6bec0f6b252ad"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.0.3"}, "time": "2020-11-25T21:54:59+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "febf71fb4052c824046f5a33f4f769a6e7fa0cb4"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/febf71fb4052c824046f5a33f4f769a6e7fa0cb4", "type": "zip", "shasum": "", "reference": "febf71fb4052c824046f5a33f4f769a6e7fa0cb4"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.0.2"}, "time": "2020-01-06T22:52:48+00:00", "extra": {"branch-alias": []}, "require": {"php": ">=7.1", "nette/utils": "^3.1"}, "funding": "__unset"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "337117df1dade22e2ba1fdc4a4b832c1e9b06b76"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/337117df1dade22e2ba1fdc4a4b832c1e9b06b76", "type": "zip", "shasum": "", "reference": "337117df1dade22e2ba1fdc4a4b832c1e9b06b76"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.0.1"}, "time": "2019-10-31T20:52:19+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=7.1", "nette/utils": "^3.0.1"}, "require-dev": {"nette/tester": "^2.2", "tracy/tracy": "^2.3"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nette/schema.git", "type": "git", "reference": "6241d8d4da39e825dd6cb5bfbe4242912f4d7e4d"}, "dist": {"url": "https://api.github.com/repos/nette/schema/zipball/6241d8d4da39e825dd6cb5bfbe4242912f4d7e4d", "type": "zip", "shasum": "", "reference": "6241d8d4da39e825dd6cb5bfbe4242912f4d7e4d"}, "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.0.0"}, "time": "2019-04-03T15:53:25+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 07 Nov 2024 14:56:37 GMT"}