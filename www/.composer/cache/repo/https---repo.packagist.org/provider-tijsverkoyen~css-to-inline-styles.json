{"minified": "composer/2.0", "packages": {"tijsverkoyen/css-to-inline-styles": [{"name": "tijsverkoyen/css-to-inline-styles", "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "keywords": [], "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "version": "v2.3.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "0d72ac1c00084279c1816675284073c5a337c20d"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0d72ac1c00084279c1816675284073c5a337c20d", "type": "zip", "shasum": "", "reference": "0d72ac1c00084279c1816675284073c5a337c20d"}, "type": "library", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.3.0"}, "funding": [], "time": "2024-12-21T16:25:41+00:00", "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require": {"php": "^7.4 || ^8.0", "ext-dom": "*", "ext-libxml": "*", "symfony/css-selector": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^8.5.21 || ^9.5.10", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0"}}, {"version": "v2.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "type": "zip", "shasum": "", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.2.7"}, "time": "2023-12-08T13:03:43+00:00", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "require": {"php": "^5.5 || ^7.0 || ^8.0", "ext-dom": "*", "ext-libxml": "*", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}}, {"version": "2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "c42125b83a4fa63b187fdf29f9c93cb7733da30c"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/c42125b83a4fa63b187fdf29f9c93cb7733da30c", "type": "zip", "shasum": "", "reference": "c42125b83a4fa63b187fdf29f9c93cb7733da30c"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.6"}, "time": "2023-01-03T09:29:04+00:00", "require": {"php": "^5.5 || ^7.0 || ^8.0", "ext-dom": "*", "ext-libxml": "*", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}}, {"version": "2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "4348a3a06651827a27d989ad1d13efec6bb49b19"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/4348a3a06651827a27d989ad1d13efec6bb49b19", "type": "zip", "shasum": "", "reference": "4348a3a06651827a27d989ad1d13efec6bb49b19"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.5"}, "time": "2022-09-12T13:28:28+00:00"}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/da444caae6aca7a19c0c140f68c6182e337d5b1c", "type": "zip", "shasum": "", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.4"}, "time": "2021-12-08T09:12:39+00:00"}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/b43b05cf43c1b6d849478965062b6ef73e223bb5", "type": "zip", "shasum": "", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.3"}, "time": "2020-07-13T06:12:54+00:00", "require": {"php": "^5.5 || ^7.0 || ^8.0", "ext-dom": "*", "ext-libxml": "*", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "dda2ee426acd6d801d5b7fd1001cde9b5f790e15"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/dda2ee426acd6d801d5b7fd1001cde9b5f790e15", "type": "zip", "shasum": "", "reference": "dda2ee426acd6d801d5b7fd1001cde9b5f790e15"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.2"}, "time": "2019-10-24T08:53:34+00:00", "require": {"php": "^5.5 || ^7.0", "ext-dom": "*", "ext-libxml": "*", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "funding": "__unset"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "0ed4a2ea4e0902dac0489e6436ebcd5bbcae9757"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0ed4a2ea4e0902dac0489e6436ebcd5bbcae9757", "type": "zip", "shasum": "", "reference": "0ed4a2ea4e0902dac0489e6436ebcd5bbcae9757"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/master"}, "time": "2017-11-27T11:13:29+00:00", "require": {"php": "^5.5 || ^7.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "ab03919dfd85a74ae0372f8baf9f3c7d5c03b04b"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/ab03919dfd85a74ae0372f8baf9f3c7d5c03b04b", "type": "zip", "shasum": "", "reference": "ab03919dfd85a74ae0372f8baf9f3c7d5c03b04b"}, "time": "2016-09-20T12:50:39+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": "^5.5 || ^7", "symfony/css-selector": "^2.7|~3.0"}, "require-dev": {"phpunit/phpunit": "~4.8|5.1.*"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "0febd154de815eb5e000eaddb6372454ab4a4736"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0febd154de815eb5e000eaddb6372454ab4a4736", "type": "zip", "shasum": "", "reference": "0febd154de815eb5e000eaddb6372454ab4a4736"}, "time": "2016-09-05T07:09:20+00:00", "require": {"symfony/css-selector": "^2.7|~3.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "bb535b05ba1e23a0400ecc73adaa00d37289baf1"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/bb535b05ba1e23a0400ecc73adaa00d37289baf1", "type": "zip", "shasum": "", "reference": "bb535b05ba1e23a0400ecc73adaa00d37289baf1"}, "time": "2016-02-23T13:27:47+00:00"}, {"version": "1.5.5", "version_normalized": "*******", "license": ["BSD"], "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "9753fc340726e327e4d48b7c0604f85475ae0bc3"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/9753fc340726e327e4d48b7c0604f85475ae0bc3", "type": "zip", "shasum": "", "reference": "9753fc340726e327e4d48b7c0604f85475ae0bc3"}, "time": "2015-12-08T16:14:14+00:00", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "require": {"php": ">=5.3.0", "symfony/css-selector": "~2.1|~3.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "1.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "3065b197f54c83392a4e0ba355678a5080dd9ee2"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/3065b197f54c83392a4e0ba355678a5080dd9ee2", "type": "zip", "shasum": "", "reference": "3065b197f54c83392a4e0ba355678a5080dd9ee2"}, "time": "2015-04-01T14:40:03+00:00", "require": {"php": ">=5.3.0", "symfony/css-selector": "~2.1"}}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "12effc9bdedc933eef0d4d3baff2899c2ad43e5c"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/12effc9bdedc933eef0d4d3baff2899c2ad43e5c", "type": "zip", "shasum": "", "reference": "12effc9bdedc933eef0d4d3baff2899c2ad43e5c"}, "time": "2015-04-01T14:28:56+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "c50e8cd86acb0e528855c59653006e96d094a5b1"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/c50e8cd86acb0e528855c59653006e96d094a5b1", "type": "zip", "shasum": "", "reference": "c50e8cd86acb0e528855c59653006e96d094a5b1"}, "time": "2014-08-01T18:02:04+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "3dcfeae27c26a03b5ab467f70c0de94efa89e8e5"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/3dcfeae27c26a03b5ab467f70c0de94efa89e8e5", "type": "zip", "shasum": "", "reference": "3dcfeae27c26a03b5ab467f70c0de94efa89e8e5"}, "time": "2014-07-25T14:16:33+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "require": {"php": ">=5.3.0", "symfony/css-selector": "~2.0"}, "require-dev": "__unset"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "1af6513d9136fdfbc09b7e693aac688ef6f9101c"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/1af6513d9136fdfbc09b7e693aac688ef6f9101c", "type": "zip", "shasum": "", "reference": "1af6513d9136fdfbc09b7e693aac688ef6f9101c"}, "time": "2014-07-25T14:04:36+00:00"}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "8f3197f893c998490fd7304e6c071f19aa650052"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/8f3197f893c998490fd7304e6c071f19aa650052", "type": "zip", "shasum": "", "reference": "8f3197f893c998490fd7304e6c071f19aa650052"}, "time": "2014-07-25T13:18:36+00:00", "autoload": {"psr-0": {"TijsVerkoyen\\CssToInlineStyles": ""}}, "target-dir": "TijsVerkoyen/CssToInlineStyles"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "f11ff46929264a8177d58ebc73342f1818660c68"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/f11ff46929264a8177d58ebc73342f1818660c68", "type": "zip", "shasum": "", "reference": "f11ff46929264a8177d58ebc73342f1818660c68"}, "time": "2014-07-25T12:55:30+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "8800cfd4af69819db32c0aa9edaf76ad8e5001c3"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/8800cfd4af69819db32c0aa9edaf76ad8e5001c3", "type": "zip", "shasum": "", "reference": "8800cfd4af69819db32c0aa9edaf76ad8e5001c3"}, "time": "2014-07-25T12:50:38+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "de55df9b915a1cc8b76e11ae9bd1e61cbc95e62b"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/de55df9b915a1cc8b76e11ae9bd1e61cbc95e62b", "type": "zip", "shasum": "", "reference": "de55df9b915a1cc8b76e11ae9bd1e61cbc95e62b"}, "time": "2014-07-18T13:53:52+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "c462ea1bde83c9f49c94787ba492858de9bb239d"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/c462ea1bde83c9f49c94787ba492858de9bb239d", "type": "zip", "shasum": "", "reference": "c462ea1bde83c9f49c94787ba492858de9bb239d"}, "time": "2014-07-18T13:40:46+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "3848e86d51b79d484fa60ebb64602967ca0a7a3f"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/3848e86d51b79d484fa60ebb64602967ca0a7a3f", "type": "zip", "shasum": "", "reference": "3848e86d51b79d484fa60ebb64602967ca0a7a3f"}, "time": "2014-05-05T09:15:31+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": ">=5.3.0"}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "e53f25f6685d167f80b9d51b91b09aa70ea6c1c9"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/e53f25f6685d167f80b9d51b91b09aa70ea6c1c9", "type": "zip", "shasum": "", "reference": "e53f25f6685d167f80b9d51b91b09aa70ea6c1c9"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/1.2.1"}, "time": "2013-02-09T17:48:44+00:00", "extra": "__unset"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "70ee412434dd6de4ab3ae06d024c2a9117911b89"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/70ee412434dd6de4ab3ae06d024c2a9117911b89", "type": "zip", "shasum": "", "reference": "70ee412434dd6de4ab3ae06d024c2a9117911b89"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/master"}, "time": "2013-02-09T17:41:35+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "5590a4e1d7ff3acd85465372de8be906d711c5d5"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/5590a4e1d7ff3acd85465372de8be906d711c5d5", "type": "zip", "shasum": "", "reference": "5590a4e1d7ff3acd85465372de8be906d711c5d5"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/1.1.0"}, "time": "2012-10-13T18:39:35+00:00", "autoload": {"classmap": [""]}, "require": {"php": ">=5.2.0", "ext-curl": "*"}, "target-dir": "__unset"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "type": "git", "reference": "d66f0b3cfb63e6ca39e95c3a3586bd38197e5ebf"}, "dist": {"url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/d66f0b3cfb63e6ca39e95c3a3586bd38197e5ebf", "type": "zip", "shasum": "", "reference": "d66f0b3cfb63e6ca39e95c3a3586bd38197e5ebf"}, "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/1.0.6"}, "time": "2012-10-13T17:49:09+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 21 Dec 2024 16:28:44 GMT"}