{"minified": "composer/2.0", "packages": {"ralouphie/getallheaders": [{"name": "ralouphie/getallheaders", "description": "A polyfill for getallheaders.", "keywords": [], "homepage": "", "version": "3.0.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "type": "zip", "shasum": "", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "type": "library", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00", "autoload": {"files": ["src/getallheaders.php"]}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5 || ^6.5", "php-coveralls/php-coveralls": "^2.1"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "beb49b96960f0cbf17e7fbcaaddc8eb434e126eb"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/beb49b96960f0cbf17e7fbcaaddc8eb434e126eb", "type": "zip", "shasum": "", "reference": "beb49b96960f0cbf17e7fbcaaddc8eb434e126eb"}, "time": "2018-12-11T21:53:24+00:00", "require-dev": {"phpunit/phpunit": "^5", "satooshi/php-coveralls": ">=1.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "cd148e0cf8789def229b1bc24be0cb0eeb24301c"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/cd148e0cf8789def229b1bc24be0cb0eeb24301c", "type": "zip", "shasum": "", "reference": "cd148e0cf8789def229b1bc24be0cb0eeb24301c"}, "time": "2018-12-10T04:22:02+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "e47eb688e84643a6c5e7c0333535e07c3fb75ab8"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/e47eb688e84643a6c5e7c0333535e07c3fb75ab8", "type": "zip", "shasum": "", "reference": "e47eb688e84643a6c5e7c0333535e07c3fb75ab8"}, "time": "2018-12-06T19:23:00+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "type": "zip", "shasum": "", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.5"}, "time": "2016-02-11T07:05:27+00:00", "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "b92568691d6368308e6abde4577cb008f28ba07e"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/b92568691d6368308e6abde4577cb008f28ba07e", "type": "zip", "shasum": "", "reference": "b92568691d6368308e6abde4577cb008f28ba07e"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.4"}, "time": "2016-02-11T06:52:58+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "861626df689dd37fba77fc8ee2013aaeaa2fd489"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/861626df689dd37fba77fc8ee2013aaeaa2fd489", "type": "zip", "shasum": "", "reference": "861626df689dd37fba77fc8ee2013aaeaa2fd489"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.3"}, "time": "2016-02-11T06:29:25+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "b8516215b07185afc0294264623f0acc5af46692"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/b8516215b07185afc0294264623f0acc5af46692", "type": "zip", "shasum": "", "reference": "b8516215b07185afc0294264623f0acc5af46692"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.2"}, "time": "2016-02-11T06:25:37+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "5486a597eae9527c36cb82065f7909ab5a7a5b4f"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5486a597eae9527c36cb82065f7909ab5a7a5b4f", "type": "zip", "shasum": "", "reference": "5486a597eae9527c36cb82065f7909ab5a7a5b4f"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.1"}, "time": "2016-02-11T06:23:27+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "05c6a7ebbd9cffcf788b781260c9be0cd2b45f83"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/05c6a7ebbd9cffcf788b781260c9be0cd2b45f83", "type": "zip", "shasum": "", "reference": "05c6a7ebbd9cffcf788b781260c9be0cd2b45f83"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/2.0.0"}, "time": "2016-02-11T06:16:22+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "086cda5dd8fc4988ec98cd466fbb26b23573fb64"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/086cda5dd8fc4988ec98cd466fbb26b23573fb64", "type": "zip", "shasum": "", "reference": "086cda5dd8fc4988ec98cd466fbb26b23573fb64"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/1.0.0"}, "time": "2016-01-29T20:38:56+00:00"}, {"version": "0.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/ralouphie/getallheaders.git", "type": "git", "reference": "7348bf32ea7bfe70674dcf527aa0fcf598fea201"}, "dist": {"url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/7348bf32ea7bfe70674dcf527aa0fcf598fea201", "type": "zip", "shasum": "", "reference": "7348bf32ea7bfe70674dcf527aa0fcf598fea201"}, "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/0.1.7"}, "time": "2014-06-03T14:12:33+00:00", "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": "dev-master"}}]}, "security-advisories": [], "last-modified": "Mon, 08 Apr 2024 20:55:57 GMT"}