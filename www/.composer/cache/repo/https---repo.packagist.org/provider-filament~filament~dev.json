{"minified": "composer/2.0", "packages": {"filament/filament": [{"name": "filament/filament", "description": "A collection of full-stack components for accelerated Laravel app development.", "keywords": [], "homepage": "https://github.com/filamentphp/filament", "version": "4.x-dev", "version_normalized": "4.9999999.9999999.9999999-dev", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/filamentphp/panels.git", "type": "git", "reference": "14e17cc6a88c1062ed6088700c5087f99dd4a4c4"}, "dist": {"url": "https://api.github.com/repos/filamentphp/panels/zipball/14e17cc6a88c1062ed6088700c5087f99dd4a4c4", "type": "zip", "shasum": "", "reference": "14e17cc6a88c1062ed6088700c5087f99dd4a4c4"}, "type": "library", "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "funding": [], "time": "2025-07-08T20:59:46+00:00", "autoload": {"files": ["src/global_helpers.php", "src/helpers.php"], "psr-4": {"Filament\\": "src"}}, "extra": {"laravel": {"providers": ["Filament\\FilamentServiceProvider"]}}, "require": {"php": "^8.2", "chillerlan/php-qrcode": "^5.0", "filament/actions": "self.version", "filament/forms": "self.version", "filament/infolists": "self.version", "filament/notifications": "self.version", "filament/schemas": "self.version", "filament/support": "self.version", "filament/tables": "self.version", "filament/widgets": "self.version", "pragmarx/google2fa": "^8.0", "pragmarx/google2fa-qrcode": "^3.0"}}, {"version": "3.x-dev", "version_normalized": "3.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/filamentphp/panels.git", "type": "git", "reference": "b432b938c35467b9626978fb8b72578ec4d162ae"}, "dist": {"url": "https://api.github.com/repos/filamentphp/panels/zipball/b432b938c35467b9626978fb8b72578ec4d162ae", "type": "zip", "shasum": "", "reference": "b432b938c35467b9626978fb8b72578ec4d162ae"}, "time": "2025-07-01T09:34:40+00:00", "default-branch": true, "require": {"filament/actions": "self.version", "filament/forms": "self.version", "filament/infolists": "self.version", "filament/notifications": "self.version", "filament/support": "self.version", "filament/tables": "self.version", "filament/widgets": "self.version", "php": "^8.1", "danharrin/livewire-rate-limiting": "^0.3|^1.0|^2.0", "illuminate/auth": "^10.45|^11.0|^12.0", "illuminate/console": "^10.45|^11.0|^12.0", "illuminate/contracts": "^10.45|^11.0|^12.0", "illuminate/cookie": "^10.45|^11.0|^12.0", "illuminate/database": "^10.45|^11.0|^12.0", "illuminate/http": "^10.45|^11.0|^12.0", "illuminate/routing": "^10.45|^11.0|^12.0", "illuminate/session": "^10.45|^11.0|^12.0", "illuminate/support": "^10.45|^11.0|^12.0", "illuminate/view": "^10.45|^11.0|^12.0", "spatie/laravel-package-tools": "^1.9"}}, {"description": "Effortlessly build TALL-powered admin panels.", "version": "2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/filamentphp/panels.git", "type": "git", "reference": "8226efb5030b431d68d2c8ad88b1cd11dca08ee8"}, "dist": {"url": "https://api.github.com/repos/filamentphp/panels/zipball/8226efb5030b431d68d2c8ad88b1cd11dca08ee8", "type": "zip", "shasum": "", "reference": "8226efb5030b431d68d2c8ad88b1cd11dca08ee8"}, "time": "2025-03-25T10:29:11+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Filament\\": "src"}}, "require": {"php": "^8.0", "spatie/laravel-package-tools": "^1.9", "filament/forms": "self.version", "filament/tables": "self.version", "danharrin/livewire-rate-limiting": "^0.3|^1.0", "filament/support": "self.version", "filament/notifications": "self.version", "livewire/livewire": "^2.10.7", "illuminate/auth": "^8.6|^9.0|^10.0", "illuminate/console": "^8.6|^9.0|^10.0", "illuminate/contracts": "^8.6|^9.0|^10.0", "illuminate/cookie": "^8.6|^9.0|^10.0", "illuminate/database": "^8.6|^9.0|^10.0", "illuminate/http": "^8.6|^9.0|^10.0", "illuminate/routing": "^8.6|^9.0|^10.0", "illuminate/session": "^8.6|^9.0|^10.0", "illuminate/support": "^8.6|^9.0|^10.0", "illuminate/view": "^8.6|^9.0|^10.0"}, "default-branch": "__unset"}, {"description": "The elegant TALL stack admin panel for Laravel artisans.", "homepage": "", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/filamentphp/panels.git", "type": "git", "reference": "3af91ef3692edbf96a0fdd033a4b1a488cd42e0e"}, "dist": {"url": "https://api.github.com/repos/filamentphp/panels/zipball/3af91ef3692edbf96a0fdd033a4b1a488cd42e0e", "type": "zip", "shasum": "", "reference": "3af91ef3692edbf96a0fdd033a4b1a488cd42e0e"}, "support": {"source": "https://github.com/filamentphp/panels/tree/v1.13.4"}, "time": "2021-12-17T20:46:45+00:00", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Filament\\": "src", "Filament\\Forms\\": "packages/forms/src", "Filament\\Tables\\": "packages/tables/src", "Filament\\Database\\Factories\\": "database/factories"}}, "extra": {"laravel": {"aliases": {"FilamentManager": "Filament\\Filament"}, "providers": ["Filament\\FilamentServiceProvider", "Filament\\Forms\\FormsServiceProvider", "Filament\\Tables\\TablesServiceProvider"]}}, "require": {"php": "^7.4|^8.0", "blade-ui-kit/blade-heroicons": "^1.0", "danharrin/date-format-converter": "^0.2", "danharrin/livewire-rate-limiting": "^0.3", "illuminate/auth": "^8.0", "illuminate/console": "^8.0", "illuminate/cookie": "^8.0", "illuminate/database": "^8.0", "illuminate/filesystem": "^8.0", "illuminate/http": "^8.0", "illuminate/notifications": "^8.0", "illuminate/routing": "^8.0", "illuminate/session": "^8.0", "illuminate/support": "^8.0", "illuminate/view": "^8.0", "league/glide-laravel": "^1.0", "thomaswelton/laravel-gravatar": "^1.0", "livewire/livewire": "^2.6"}, "require-dev": {"orchestra/testbench": "^6.0", "phpunit/phpunit": "^9.5", "symplify/monorepo-builder": "^9.0"}, "replace": {"filament/forms": "self.version", "filament/tables": "self.version"}}, {"description": "Effortlessly build TALL-stack apps and admin panels.", "homepage": "https://github.com/filamentphp/filament", "version": "dev-3.x-alpha", "version_normalized": "dev-3.x-alpha", "source": {"url": "https://github.com/filamentphp/panels.git", "type": "git", "reference": "3bff654d46fc39867d48245fa2a012d000e85757"}, "dist": {"url": "https://api.github.com/repos/filamentphp/panels/zipball/3bff654d46fc39867d48245fa2a012d000e85757", "type": "zip", "shasum": "", "reference": "3bff654d46fc39867d48245fa2a012d000e85757"}, "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "time": "2023-10-28T09:34:14+00:00", "autoload": {"files": ["src/global_helpers.php", "src/helpers.php"], "psr-4": {"Filament\\": "src"}}, "extra": {"laravel": {"providers": ["Filament\\FilamentServiceProvider"]}}, "require": {"php": "^8.1", "danharrin/livewire-rate-limiting": "^0.3|^1.0", "filament/actions": "self.version", "filament/forms": "self.version", "filament/infolists": "self.version", "filament/notifications": "self.version", "filament/support": "self.version", "filament/tables": "self.version", "filament/widgets": "self.version", "illuminate/auth": "^9.0|^10.0", "illuminate/console": "^9.0|^10.0", "illuminate/contracts": "^9.0|^10.0", "illuminate/cookie": "^9.0|^10.0", "illuminate/database": "^9.0|^10.0", "illuminate/http": "^9.0|^10.0", "illuminate/routing": "^9.0|^10.0", "illuminate/session": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "illuminate/view": "^9.0|^10.0", "livewire/livewire": "^2.10.7", "spatie/laravel-package-tools": "^1.9"}, "require-dev": "__unset", "replace": "__unset"}]}, "last-modified": "<PERSON><PERSON>, 08 Jul 2025 20:59:56 GMT"}