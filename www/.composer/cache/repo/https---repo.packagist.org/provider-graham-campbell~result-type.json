{"minified": "composer/2.0", "packages": {"graham-campbell/result-type": [{"name": "graham-campbell/result-type", "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "result", "Result-Type", "Result Type"], "homepage": "", "version": "v1.1.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "type": "zip", "shasum": "", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "type": "library", "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/fbd48bce38f73f8a4ec8583362e732e4095e5862", "type": "zip", "shasum": "", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.2"}, "time": "2023-11-12T22:16:48+00:00", "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2"}, "require-dev": {"phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831", "type": "zip", "shasum": "", "reference": "672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.1"}, "time": "2023-02-25T20:23:15+00:00", "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.1"}, "require-dev": {"phpunit/phpunit": "^8.5.32 || ^9.6.3 || ^10.0.12"}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "a878d45c1914464426dc94da61c9e1d36ae262a8"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/a878d45c1914464426dc94da61c9e1d36ae262a8", "type": "zip", "shasum": "", "reference": "a878d45c1914464426dc94da61c9e1d36ae262a8"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.0"}, "time": "2022-07-30T15:56:11+00:00", "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9"}, "require-dev": {"phpunit/phpunit": "^8.5.28 || ^9.5.21"}}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "0690bde05318336c7221785f2a932467f98b64ca"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/0690bde05318336c7221785f2a932467f98b64ca", "type": "zip", "shasum": "", "reference": "0690bde05318336c7221785f2a932467f98b64ca"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.4"}, "time": "2021-11-21T21:41:47+00:00", "require": {"php": "^7.0 || ^8.0", "phpoption/phpoption": "^1.8"}, "require-dev": {"phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}}, {"version": "v1.0.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "296c015dc30ec4322168c5ad3ee5cc11dae827ac"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/296c015dc30ec4322168c5ad3ee5cc11dae827ac", "type": "zip", "shasum": "", "reference": "296c015dc30ec4322168c5ad3ee5cc11dae827ac"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.3"}, "time": "2021-10-17T19:48:54+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "84afea85c6841deeea872f36249a206e878a5de0"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/84afea85c6841deeea872f36249a206e878a5de0", "type": "zip", "shasum": "", "reference": "84afea85c6841deeea872f36249a206e878a5de0"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.2"}, "time": "2021-08-28T21:34:50+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "7e279d2cd5d7fbb156ce46daada972355cea27bb"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/7e279d2cd5d7fbb156ce46daada972355cea27bb", "type": "zip", "shasum": "", "reference": "7e279d2cd5d7fbb156ce46daada972355cea27bb"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.1"}, "time": "2020-04-13T13:17:36+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": "^7.0|^8.0", "phpoption/phpoption": "^1.7.3"}, "require-dev": {"phpunit/phpunit": "^6.5|^7.5|^8.5|^9.0"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/GrahamCampbell/Result-Type.git", "type": "git", "reference": "593031be901e26a45f5ca21edf625a547badf1f3"}, "dist": {"url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/593031be901e26a45f5ca21edf625a547badf1f3", "type": "zip", "shasum": "", "reference": "593031be901e26a45f5ca21edf625a547badf1f3"}, "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://www.patreon.com/GrahamJCampbell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2020-03-21T18:17:09+00:00", "require": {"php": "^7.0|^8.0", "phpoption/phpoption": "^1.7.2"}}]}, "security-advisories": [], "last-modified": "Sat, 20 Jul 2024 21:46:26 GMT"}