{"minified": "composer/2.0", "packages": {"egulias/email-validator": [{"name": "egulias/email-validator", "description": "A library for validating emails against several RFCs", "keywords": ["email", "validator", "validation", "emailvalidation", "emailvalidator"], "homepage": "https://github.com/egulias/EmailValidator", "version": "4.0.4", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "type": "zip", "shasum": "", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "type": "library", "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "require": {"php": ">=8.1", "doctrine/lexer": "^2.0 || ^3.0", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "b115554301161fa21467629f1e1391c1936de517"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/b115554301161fa21467629f1e1391c1936de517", "type": "zip", "shasum": "", "reference": "b115554301161fa21467629f1e1391c1936de517"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.3"}, "time": "2024-12-27T00:36:43+00:00"}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ebaaf5be6c0286928352e054f2d5125608e5405e", "type": "zip", "shasum": "", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.2"}, "time": "2023-10-06T06:47:41+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "3a85486b709bc384dae8eb78fb2eec649bdb64ff"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/3a85486b709bc384dae8eb78fb2eec649bdb64ff", "type": "zip", "shasum": "", "reference": "3a85486b709bc384dae8eb78fb2eec649bdb64ff"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.1"}, "time": "2023-01-14T14:17:03+00:00", "require-dev": {"phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^4.30"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "db4c31490b9fbc02eeb4e6d39c61c74ee1d14f69"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/db4c31490b9fbc02eeb4e6d39c61c74ee1d14f69", "type": "zip", "shasum": "", "reference": "db4c31490b9fbc02eeb4e6d39c61c74ee1d14f69"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.0"}, "time": "2023-01-07T11:14:40+00:00"}, {"version": "3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "type": "zip", "shasum": "", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "time": "2023-06-01T07:04:22+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require": {"php": ">=7.2", "doctrine/lexer": "^1.2|^2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}}, {"version": "3.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "b531a2311709443320c786feb4519cfaf94af796"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/b531a2311709443320c786feb4519cfaf94af796", "type": "zip", "shasum": "", "reference": "b531a2311709443320c786feb4519cfaf94af796"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.5"}, "time": "2023-01-02T17:26:14+00:00"}, {"version": "3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "5f35e41eba05fdfbabd95d72f83795c835fb7ed2"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/5f35e41eba05fdfbabd95d72f83795c835fb7ed2", "type": "zip", "shasum": "", "reference": "5f35e41eba05fdfbabd95d72f83795c835fb7ed2"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.4"}, "time": "2022-12-30T14:09:25+00:00", "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "0170967656f55a87054fde439ef35a22df91468b"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0170967656f55a87054fde439ef35a22df91468b", "type": "zip", "shasum": "", "reference": "0170967656f55a87054fde439ef35a22df91468b"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.3"}, "time": "2022-09-03T09:54:08+00:00", "require": {"php": ">=7.2", "doctrine/lexer": "^1.2", "symfony/polyfill-intl-idn": "^1.15"}}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "1ac0be80b32ea829009257934ecfd5ab20f24ba5"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/1ac0be80b32ea829009257934ecfd5ab20f24ba5", "type": "zip", "shasum": "", "reference": "1ac0be80b32ea829009257934ecfd5ab20f24ba5"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.2"}, "time": "2022-12-29T21:07:24+00:00"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f88dcf4b14af14a98ad96b14b2b317969eab6715", "type": "zip", "shasum": "", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.1"}, "time": "2022-06-18T20:57:19+00:00"}, {"version": "3.2", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "a5ed8d58ed0c340a7c2109f587951b1c84cf6286"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/a5ed8d58ed0c340a7c2109f587951b1c84cf6286", "type": "zip", "shasum": "", "reference": "a5ed8d58ed0c340a7c2109f587951b1c84cf6286"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2"}, "time": "2022-05-28T22:19:18+00:00"}, {"version": "3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "ee0db30118f661fb166bcffbf5d82032df484697"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ee0db30118f661fb166bcffbf5d82032df484697", "type": "zip", "shasum": "", "reference": "ee0db30118f661fb166bcffbf5d82032df484697"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.2"}, "time": "2021-10-11T09:18:27+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "c81f18a3efb941d8c4d2e025f6183b5c6d697307"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/c81f18a3efb941d8c4d2e025f6183b5c6d697307", "type": "zip", "shasum": "", "reference": "c81f18a3efb941d8c4d2e025f6183b5c6d697307"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.1"}, "time": "2021-04-01T18:37:14+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "62c3b73c581c834885acf6e120b412b76acc495a"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/62c3b73c581c834885acf6e120b412b76acc495a", "type": "zip", "shasum": "", "reference": "62c3b73c581c834885acf6e120b412b76acc495a"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.0"}, "time": "2021-03-07T14:33:28+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "451b4389027aa5c74abf43fb331daf3dec7f146e"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/451b4389027aa5c74abf43fb331daf3dec7f146e", "type": "zip", "shasum": "", "reference": "451b4389027aa5c74abf43fb331daf3dec7f146e"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.0.1"}, "time": "2021-02-28T14:27:57+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "e5df72f45f0ba585a278ce02607b8b542aabfec7"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5df72f45f0ba585a278ce02607b8b542aabfec7", "type": "zip", "shasum": "", "reference": "e5df72f45f0ba585a278ce02607b8b542aabfec7"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.0.0"}, "time": "2020-12-29T15:04:19+00:00"}, {"version": "2.1.25", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "type": "zip", "shasum": "", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "time": "2020-12-29T14:50:06+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "require": {"php": ">=5.5", "doctrine/lexer": "^1.0.1", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}}, {"version": "2.1.24", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "ca90a3291eee1538cd48ff25163240695bd95448"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ca90a3291eee1538cd48ff25163240695bd95448", "type": "zip", "shasum": "", "reference": "ca90a3291eee1538cd48ff25163240695bd95448"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.24"}, "time": "2020-11-14T15:56:27+00:00"}, {"version": "2.1.23", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "5fa792ad1853ae2bc60528dd3e5cbf4542d3c1df"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/5fa792ad1853ae2bc60528dd3e5cbf4542d3c1df", "type": "zip", "shasum": "", "reference": "5fa792ad1853ae2bc60528dd3e5cbf4542d3c1df"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.23"}, "funding": [], "time": "2020-10-31T20:37:35+00:00"}, {"version": "2.1.22", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "68e418ec08fbfc6f58f6fd2eea70ca8efc8cc7d5"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/68e418ec08fbfc6f58f6fd2eea70ca8efc8cc7d5", "type": "zip", "shasum": "", "reference": "68e418ec08fbfc6f58f6fd2eea70ca8efc8cc7d5"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.22"}, "time": "2020-09-26T15:48:38+00:00"}, {"version": "2.1.21", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "563d0cdde5d862235ffe24a158497f4d490191b5"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/563d0cdde5d862235ffe24a158497f4d490191b5", "type": "zip", "shasum": "", "reference": "563d0cdde5d862235ffe24a158497f4d490191b5"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.21"}, "time": "2020-09-19T14:37:56+00:00"}, {"version": "2.1.20", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "f46887bc48db66c7f38f668eb7d6ae54583617ff"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f46887bc48db66c7f38f668eb7d6ae54583617ff", "type": "zip", "shasum": "", "reference": "f46887bc48db66c7f38f668eb7d6ae54583617ff"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.20"}, "time": "2020-09-06T13:44:32+00:00"}, {"version": "2.1.19", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "840d5603eb84cc81a6a0382adac3293e57c1c64c"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/840d5603eb84cc81a6a0382adac3293e57c1c64c", "type": "zip", "shasum": "", "reference": "840d5603eb84cc81a6a0382adac3293e57c1c64c"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2020-08-08T21:28:19+00:00"}, {"version": "2.1.18", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "cfa3d44471c7f5bfb684ac2b0da7114283d78441"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/cfa3d44471c7f5bfb684ac2b0da7114283d78441", "type": "zip", "shasum": "", "reference": "cfa3d44471c7f5bfb684ac2b0da7114283d78441"}, "time": "2020-06-16T20:11:17+00:00"}, {"version": "2.1.17", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ade6887fd9bd74177769645ab5c474824f8a418a", "type": "zip", "shasum": "", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.17"}, "time": "2020-02-13T22:36:52+00:00", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "require-dev": {"satooshi/php-coveralls": "^1.0.1", "phpunit/phpunit": "^4.8.36|^7.5.15", "dominicsayers/isemail": "^3.0.7"}, "funding": "__unset"}, {"version": "2.1.16", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "5065fafc8c29d229ff207f2a89b02175f479a909"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/5065fafc8c29d229ff207f2a89b02175f479a909", "type": "zip", "shasum": "", "reference": "5065fafc8c29d229ff207f2a89b02175f479a909"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2020-02-12T22:28:28+00:00"}, {"version": "2.1.15", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "e834eea5306d85d67de5a05db5882911d5b29357"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e834eea5306d85d67de5a05db5882911d5b29357", "type": "zip", "shasum": "", "reference": "e834eea5306d85d67de5a05db5882911d5b29357"}, "time": "2020-01-20T21:40:59+00:00"}, {"version": "2.1.14", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "c4b8d12921999d8a561004371701dbc2e05b5ece"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/c4b8d12921999d8a561004371701dbc2e05b5ece", "type": "zip", "shasum": "", "reference": "c4b8d12921999d8a561004371701dbc2e05b5ece"}, "time": "2020-01-05T14:11:20+00:00", "require": {"php": ">=5.5", "doctrine/lexer": "^1.0.1"}}, {"version": "2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "834593d5900615639208417760ba6a17299e2497"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/834593d5900615639208417760ba6a17299e2497", "type": "zip", "shasum": "", "reference": "834593d5900615639208417760ba6a17299e2497"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.13"}, "time": "2019-12-30T08:14:25+00:00"}, {"version": "2.1.12", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "a6255605af39f2db7f5cb62e672bd8a7bad8d208"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/a6255605af39f2db7f5cb62e672bd8a7bad8d208", "type": "zip", "shasum": "", "reference": "a6255605af39f2db7f5cb62e672bd8a7bad8d208"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.12"}, "time": "2019-12-20T12:49:39+00:00", "require": {"php": ">= 5.5", "doctrine/lexer": "^1.0.1"}, "require-dev": {"satooshi/php-coveralls": "^1.0.1", "phpunit/phpunit": "^4.8.35||^5.7||^6.0", "symfony/phpunit-bridge": "^4.4@dev", "dominicsayers/isemail": "dev-master"}}, {"version": "2.1.11", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "92dd169c32f6f55ba570c309d83f5209cefb5e23"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/92dd169c32f6f55ba570c309d83f5209cefb5e23", "type": "zip", "shasum": "", "reference": "92dd169c32f6f55ba570c309d83f5209cefb5e23"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2019-08-13T17:33:27+00:00"}, {"version": "2.1.10", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "a6c8d7101b19a451c1707b1b79bbbc56e4bdb7ec"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/a6c8d7101b19a451c1707b1b79bbbc56e4bdb7ec", "type": "zip", "shasum": "", "reference": "a6c8d7101b19a451c1707b1b79bbbc56e4bdb7ec"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.10"}, "time": "2019-07-19T20:52:08+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}, {"version": "2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "128cc721d771ec2c46ce59698f4ca42b73f71b25"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/128cc721d771ec2c46ce59698f4ca42b73f71b25", "type": "zip", "shasum": "", "reference": "128cc721d771ec2c46ce59698f4ca42b73f71b25"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2019-06-23T10:14:27+00:00", "require-dev": {"satooshi/php-coveralls": "^1.0.1", "phpunit/phpunit": "^4.8.35||^5.7||^6.0", "dominicsayers/isemail": "dev-master"}}, {"version": "2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "c26463ff9241f27907112fbcd0c86fa670cfef98"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/c26463ff9241f27907112fbcd0c86fa670cfef98", "type": "zip", "shasum": "", "reference": "c26463ff9241f27907112fbcd0c86fa670cfef98"}, "time": "2019-05-16T22:02:54+00:00"}, {"version": "2.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "709f21f92707308cdf8f9bcfa1af4cb26586521e"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/709f21f92707308cdf8f9bcfa1af4cb26586521e", "type": "zip", "shasum": "", "reference": "709f21f92707308cdf8f9bcfa1af4cb26586521e"}, "time": "2018-12-04T22:38:24+00:00"}, {"version": "2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "0578b32b30b22de3e8664f797cf846fc9246f786"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0578b32b30b22de3e8664f797cf846fc9246f786", "type": "zip", "shasum": "", "reference": "0578b32b30b22de3e8664f797cf846fc9246f786"}, "time": "2018-09-25T20:47:26+00:00"}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "54859fabea8b3beecbb1a282888d5c990036b9e3"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/54859fabea8b3beecbb1a282888d5c990036b9e3", "type": "zip", "shasum": "", "reference": "54859fabea8b3beecbb1a282888d5c990036b9e3"}, "time": "2018-08-16T20:49:45+00:00"}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "8790f594151ca6a2010c6218e09d96df67173ad3"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/8790f594151ca6a2010c6218e09d96df67173ad3", "type": "zip", "shasum": "", "reference": "8790f594151ca6a2010c6218e09d96df67173ad3"}, "time": "2018-04-10T10:11:19+00:00"}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "1bec00a10039b823cc94eef4eddd47dcd3b2ca04"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/1bec00a10039b823cc94eef4eddd47dcd3b2ca04", "type": "zip", "shasum": "", "reference": "1bec00a10039b823cc94eef4eddd47dcd3b2ca04"}, "time": "2017-11-15T23:40:40+00:00", "require-dev": {"satooshi/php-coveralls": "^1.0.1", "phpunit/phpunit": "^4.8.35", "dominicsayers/isemail": "dev-master"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "bc31baa11ea2883e017f0a10d9722ef9d50eac1c"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/bc31baa11ea2883e017f0a10d9722ef9d50eac1c", "type": "zip", "shasum": "", "reference": "bc31baa11ea2883e017f0a10d9722ef9d50eac1c"}, "time": "2017-01-30T22:07:36+00:00", "require-dev": {"satooshi/php-coveralls": "dev-master", "phpunit/phpunit": "^4.8.0", "dominicsayers/isemail": "dev-master"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "30562b69fc6c8e9740645877a98063b3842204b2"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/30562b69fc6c8e9740645877a98063b3842204b2", "type": "zip", "shasum": "", "reference": "30562b69fc6c8e9740645877a98063b3842204b2"}, "time": "2016-08-07T21:18:59+00:00", "suggest": {"ext/php-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "19811e00829958f06d9bfbfeb0d0e6b7aaff3767"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/19811e00829958f06d9bfbfeb0d0e6b7aaff3767", "type": "zip", "shasum": "", "reference": "19811e00829958f06d9bfbfeb0d0e6b7aaff3767"}, "time": "2016-07-12T13:22:10+00:00", "suggest": "__unset"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "b366d54b0a1e60b5275f6306a3fd1e7af2ec765a"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/b366d54b0a1e60b5275f6306a3fd1e7af2ec765a", "type": "zip", "shasum": "", "reference": "b366d54b0a1e60b5275f6306a3fd1e7af2ec765a"}, "time": "2016-07-03T21:53:17+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "f5ea1fbb9c462f009994d6fcd3c0dd5eda719432"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f5ea1fbb9c462f009994d6fcd3c0dd5eda719432", "type": "zip", "shasum": "", "reference": "f5ea1fbb9c462f009994d6fcd3c0dd5eda719432"}, "time": "2016-05-16T17:21:35+00:00"}, {"description": "A library for validating emails", "version": "1.2.17", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "19674b35a0a3456be1b96e137098d31ed386fb61"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/19674b35a0a3456be1b96e137098d31ed386fb61", "type": "zip", "shasum": "", "reference": "19674b35a0a3456be1b96e137098d31ed386fb61"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/1.2"}, "funding": [], "time": "2020-04-11T12:59:45+00:00", "autoload": {"psr-0": {"Egulias\\": "src/"}}, "require": {"php": ">=5.3.3", "doctrine/lexer": "^1.0.1"}, "require-dev": {"satooshi/php-coveralls": "^1.0.1", "phpunit/phpunit": "^4.8.36|^7.5.15"}, "extra": "__unset"}, {"version": "1.2.16", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "92fd7ea285f1edd1503d78f75b8d8992b0fb3941"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/92fd7ea285f1edd1503d78f75b8d8992b0fb3941", "type": "zip", "shasum": "", "reference": "92fd7ea285f1edd1503d78f75b8d8992b0fb3941"}, "time": "2019-12-30T08:17:06+00:00", "funding": "__unset"}, {"version": "1.2.15", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "758a77525bdaabd6c0f5669176bd4361cb2dda9e"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/758a77525bdaabd6c0f5669176bd4361cb2dda9e", "type": "zip", "shasum": "", "reference": "758a77525bdaabd6c0f5669176bd4361cb2dda9e"}, "time": "2018-09-25T20:59:41+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">= 5.3.3", "doctrine/lexer": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^4.8.24"}}, {"version": "1.2.14", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "5642614492f0ca2064c01d60cc33284cc2f731a9"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/5642614492f0ca2064c01d60cc33284cc2f731a9", "type": "zip", "shasum": "", "reference": "5642614492f0ca2064c01d60cc33284cc2f731a9"}, "time": "2017-02-03T22:48:59+00:00"}, {"version": "1.2.13", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "b8bb147f46cc9790326ce2440a13be06cc5a63bb"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/b8bb147f46cc9790326ce2440a13be06cc5a63bb", "type": "zip", "shasum": "", "reference": "b8bb147f46cc9790326ce2440a13be06cc5a63bb"}, "time": "2016-07-03T21:52:18+00:00"}, {"version": "1.2.12", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "cbfe3ee6f758225559b1111048e05a3e4c2e26cf"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/cbfe3ee6f758225559b1111048e05a3e4c2e26cf", "type": "zip", "shasum": "", "reference": "cbfe3ee6f758225559b1111048e05a3e4c2e26cf"}, "time": "2016-05-15T10:06:34+00:00"}, {"version": "1.2.11", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "04c6cdf9871140b80947c87db7770c0dd9c75c7c"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/04c6cdf9871140b80947c87db7770c0dd9c75c7c", "type": "zip", "shasum": "", "reference": "04c6cdf9871140b80947c87db7770c0dd9c75c7c"}, "time": "2015-11-11T03:28:32+00:00", "require": {"php": ">= 5.3.3", "doctrine/lexer": "~1.0,>=1.0.1"}, "require-dev": {"satooshi/php-coveralls": "dev-master", "phpunit/phpunit": "~4.4"}}, {"version": "1.2.10", "version_normalized": "********", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "de448a30fa78f2dc93889be529e875a13c6034ac"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/de448a30fa78f2dc93889be529e875a13c6034ac", "type": "zip", "shasum": "", "reference": "de448a30fa78f2dc93889be529e875a13c6034ac"}, "time": "2015-10-11T22:34:23+00:00"}, {"version": "1.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "af864423f50ea59f96c87bb1eae147a70bcf67a1"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/af864423f50ea59f96c87bb1eae147a70bcf67a1", "type": "zip", "shasum": "", "reference": "af864423f50ea59f96c87bb1eae147a70bcf67a1"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2015-06-22T21:07:51+00:00"}, {"keywords": ["email", "validator", "validation"], "version": "1.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "5631c3fe2e56cd5a3f79b20fae970d884a5ff951"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/5631c3fe2e56cd5a3f79b20fae970d884a5ff951", "type": "zip", "shasum": "", "reference": "5631c3fe2e56cd5a3f79b20fae970d884a5ff951"}, "time": "2015-04-26T15:49:00+00:00", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}}, {"version": "1.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "af9417f765623429c9d2a200f0159537e08d1ef3"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/af9417f765623429c9d2a200f0159537e08d1ef3", "type": "zip", "shasum": "", "reference": "af9417f765623429c9d2a200f0159537e08d1ef3"}, "time": "2015-01-04T22:42:32+00:00", "require": {"php": ">= 5.3.3", "doctrine/lexer": "~1.0"}, "require-dev": {"satooshi/php-coveralls": "dev-master"}}, {"version": "1.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "7a64ea18af01a114e8241a397097b266af5411ee"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/7a64ea18af01a114e8241a397097b266af5411ee", "type": "zip", "shasum": "", "reference": "7a64ea18af01a114e8241a397097b266af5411ee"}, "time": "2014-11-29T09:09:14+00:00", "require": {"php": ">= 5.3.3", "doctrine/lexer": "dev-master"}}, {"version": "1.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "518f80a0ff7c1a35780e7702f4262c8c6f2b807f"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/518f80a0ff7c1a35780e7702f4262c8c6f2b807f", "type": "zip", "shasum": "", "reference": "518f80a0ff7c1a35780e7702f4262c8c6f2b807f"}, "time": "2014-11-06T08:59:44+00:00", "require": {"php": ">= 5.3.3", "doctrine/lexer": "~1.0"}}, {"version": "1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "5c3a79217cbb98c975d7d23f12749e6f0be5cace"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/5c3a79217cbb98c975d7d23f12749e6f0be5cace", "type": "zip", "shasum": "", "reference": "5c3a79217cbb98c975d7d23f12749e6f0be5cace"}, "time": "2014-11-02T23:13:57+00:00"}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "49494c3189b7f07d7262c53057de3569b57605b4"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/49494c3189b7f07d7262c53057de3569b57605b4", "type": "zip", "shasum": "", "reference": "49494c3189b7f07d7262c53057de3569b57605b4"}, "time": "2014-10-26T22:13:52+00:00"}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "39b451bb2bb0655d83d82a38a0bba7189298cfc5"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/39b451bb2bb0655d83d82a38a0bba7189298cfc5", "type": "zip", "shasum": "", "reference": "39b451bb2bb0655d83d82a38a0bba7189298cfc5"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/1.2.2"}, "time": "2014-09-01T22:35:48+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "65afaf3f2bb3dc1ec635a0bb26e160df42b6848e"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/65afaf3f2bb3dc1ec635a0bb26e160df42b6848e", "type": "zip", "shasum": "", "reference": "65afaf3f2bb3dc1ec635a0bb26e160df42b6848e"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2014-08-21T23:08:16+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "bb530a193056992702df9f722e8814f57bdb13c2"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/bb530a193056992702df9f722e8814f57bdb13c2", "type": "zip", "shasum": "", "reference": "bb530a193056992702df9f722e8814f57bdb13c2"}, "time": "2014-07-06T17:21:29+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "f064a586114c9864da45508b1b09911643635e94"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f064a586114c9864da45508b1b09911643635e94", "type": "zip", "shasum": "", "reference": "f064a586114c9864da45508b1b09911643635e94"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/1.1.1"}, "time": "2014-01-12T23:12:24+00:00", "require-dev": "__unset"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "f61c17d6fe190aaca95d0de20a09a979ebd4d6a4"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f61c17d6fe190aaca95d0de20a09a979ebd4d6a4", "type": "zip", "shasum": "", "reference": "f61c17d6fe190aaca95d0de20a09a979ebd4d6a4"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/1.1.0"}, "time": "2013-12-08T17:42:31+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/egulias/EmailValidator.git", "type": "git", "reference": "f8e9c2552431d5455ae6becda74c7e134ad5145c"}, "dist": {"url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f8e9c2552431d5455ae6becda74c7e134ad5145c", "type": "zip", "shasum": "", "reference": "f8e9c2552431d5455ae6becda74c7e134ad5145c"}, "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/master"}, "time": "2013-05-19T17:34:36+00:00", "autoload": {"psr-0": {"egulias\\": "src/"}}, "require": {"jms/parser-lib": "dev-master", "php": ">= 5.3.3"}, "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 20 Mar 2025 21:39:05 GMT"}