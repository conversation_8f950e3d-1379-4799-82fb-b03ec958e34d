{"minified": "composer/2.0", "packages": {"staabm/side-effects-detector": [{"name": "staabm/side-effects-detector", "description": "A static analysis tool to detect side effects in PHP code", "keywords": ["static analysis"], "homepage": "", "version": "1.0.5", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/staabm/side-effects-detector.git", "type": "git", "reference": "d8334211a140ce329c13726d4a715adbddd0a163"}, "dist": {"url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/d8334211a140ce329c13726d4a715adbddd0a163", "type": "zip", "shasum": "", "reference": "d8334211a140ce329c13726d4a715adbddd0a163"}, "type": "library", "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.5"}, "funding": [{"url": "https://github.com/staabm", "type": "github"}], "time": "2024-10-20T05:08:20+00:00", "autoload": {"classmap": ["lib/"]}, "require": {"php": "^7.4 || ^8.0", "ext-tokenizer": "*"}, "require-dev": {"phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^1.12.6", "phpunit/phpunit": "^9.6.21", "symfony/var-dumper": "^5.4.43", "tomasvotruba/type-coverage": "1.0.0", "tomasvotruba/unused-public": "1.0.0"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/staabm/side-effects-detector.git", "type": "git", "reference": "f449ed69364e4b6ed5a07debeb281d990e764977"}, "dist": {"url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/f449ed69364e4b6ed5a07debeb281d990e764977", "type": "zip", "shasum": "", "reference": "f449ed69364e4b6ed5a07debeb281d990e764977"}, "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.4"}, "time": "2024-10-19T10:34:23+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/staabm/side-effects-detector.git", "type": "git", "reference": "57d11ad026abbaccf205bd53d5fb42fad0a44127"}, "dist": {"url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/57d11ad026abbaccf205bd53d5fb42fad0a44127", "type": "zip", "shasum": "", "reference": "57d11ad026abbaccf205bd53d5fb42fad0a44127"}, "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.3"}, "time": "2024-10-19T09:22:09+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/staabm/side-effects-detector.git", "type": "git", "reference": "416f6ae8a7394801bdb2df907bbe732397b571b3"}, "dist": {"url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/416f6ae8a7394801bdb2df907bbe732397b571b3", "type": "zip", "shasum": "", "reference": "416f6ae8a7394801bdb2df907bbe732397b571b3"}, "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.2"}, "time": "2024-10-19T07:43:59+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/staabm/side-effects-detector.git", "type": "git", "reference": "863968bf57936b3de18be192545c0ca2c7d2b8ba"}, "dist": {"url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/863968bf57936b3de18be192545c0ca2c7d2b8ba", "type": "zip", "shasum": "", "reference": "863968bf57936b3de18be192545c0ca2c7d2b8ba"}, "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.1"}, "time": "2024-10-18T15:09:59+00:00"}, {"description": "", "version": "1.0", "version_normalized": "*******", "source": {"url": "https://github.com/staabm/side-effects-detector.git", "type": "git", "reference": "ac16d5adbd387142efa2fb5014d8b2201cc2a8b2"}, "dist": {"url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/ac16d5adbd387142efa2fb5014d8b2201cc2a8b2", "type": "zip", "shasum": "", "reference": "ac16d5adbd387142efa2fb5014d8b2201cc2a8b2"}, "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0"}, "time": "2024-10-17T13:48:18+00:00"}, {"version": "0.1", "version_normalized": "*******", "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/0.1"}}]}, "security-advisories": [], "last-modified": "Sun, 20 Oct 2024 05:09:40 GMT"}