{"minified": "composer/2.0", "packages": {"dnoegel/php-xdg-base-dir": [{"name": "dnoegel/php-xdg-base-dir", "description": "implementation of xdg base directory specification for php", "keywords": [], "homepage": "", "version": "v0.1.1", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/dnoegel/php-xdg-base-dir.git", "type": "git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "type": "zip", "shasum": "", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "type": "library", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}}, {"version": "0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dnoegel/php-xdg-base-dir.git", "type": "git", "reference": "265b8593498b997dc2d31e75b89f053b5cc9621a"}, "dist": {"url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/265b8593498b997dc2d31e75b89f053b5cc9621a", "type": "zip", "shasum": "", "reference": "265b8593498b997dc2d31e75b89f053b5cc9621a"}, "type": "project", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/master"}, "time": "2014-10-24T07:27:01+00:00", "require-dev": {"phpunit/phpunit": "@stable"}}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 16 Apr 2024 14:04:26 GMT"}