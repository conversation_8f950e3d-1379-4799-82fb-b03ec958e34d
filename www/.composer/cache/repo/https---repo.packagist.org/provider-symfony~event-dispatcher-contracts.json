{"minified": "composer/2.0", "packages": {"symfony/event-dispatcher-contracts": [{"name": "symfony/event-dispatcher-contracts", "description": "Generic abstractions related to dispatching event", "keywords": ["interfaces", "standards", "interoperability", "contracts", "abstractions", "decoupling"], "homepage": "https://symfony.com", "version": "v3.6.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "type": "zip", "shasum": "", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "type": "library", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}}, {"version": "v3.6.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0-BETA1"}}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "type": "zip", "shasum": "", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/8f93aec25d41b72493c6ddff14e916177c9efc50", "type": "zip", "shasum": "", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "7c6c9a5473dbbb25338f1f7cb62c980af9dfa98e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7c6c9a5473dbbb25338f1f7cb62c980af9dfa98e", "type": "zip", "shasum": "", "reference": "7c6c9a5473dbbb25338f1f7cb62c980af9dfa98e"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.4.3"}, "time": "2024-09-25T14:18:03+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.4-dev"}}}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "4e64b49bf370ade88e567de29465762e316e4224"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/4e64b49bf370ade88e567de29465762e316e4224", "type": "zip", "shasum": "", "reference": "4e64b49bf370ade88e567de29465762e316e4224"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.4.2"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "a76aed96a42d2b521153fb382d418e30d18b59df"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/a76aed96a42d2b521153fb382d418e30d18b59df", "type": "zip", "shasum": "", "reference": "a76aed96a42d2b521153fb382d418e30d18b59df"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.4.0"}, "time": "2023-05-23T14:45:45+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.3.0"}}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "0ad3b6f1e4e2da5690fefe075cd53a238646d8dd"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/0ad3b6f1e4e2da5690fefe075cd53a238646d8dd", "type": "zip", "shasum": "", "reference": "0ad3b6f1e4e2da5690fefe075cd53a238646d8dd"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.2.1"}, "time": "2023-03-01T10:32:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.3-dev"}}, "suggest": {"symfony/event-dispatcher-implementation": ""}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "0782b0b52a737a05b4383d0df35a474303cabdae"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/0782b0b52a737a05b4383d0df35a474303cabdae", "type": "zip", "shasum": "", "reference": "0782b0b52a737a05b4383d0df35a474303cabdae"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.2.0"}, "time": "2022-11-25T10:21:52+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "02ff5eea2f453731cfbc6bc215e456b781480448"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/02ff5eea2f453731cfbc6bc215e456b781480448", "type": "zip", "shasum": "", "reference": "02ff5eea2f453731cfbc6bc215e456b781480448"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.1.1"}, "time": "2022-02-25T11:15:52+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.1-dev"}}}, {"version": "v3.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.1.0"}}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "type": "zip", "shasum": "", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.2"}, "time": "2022-01-02T09:55:41+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}}, {"version": "v3.0.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.1"}}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "aa5422287b75594b90ee9cd807caf8f0df491385"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/aa5422287b75594b90ee9cd807caf8f0df491385", "type": "zip", "shasum": "", "reference": "aa5422287b75594b90ee9cd807caf8f0df491385"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.0"}, "time": "2021-07-15T12:33:35+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f", "type": "zip", "shasum": "", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.4"}, "time": "2024-09-25T14:11:13+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "540f4c73e87fd0c71ca44a6aa305d024ac68cb73"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/540f4c73e87fd0c71ca44a6aa305d024ac68cb73", "type": "zip", "shasum": "", "reference": "540f4c73e87fd0c71ca44a6aa305d024ac68cb73"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.3"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f98b54df6ad059855739db6fcbc2d36995283fe1", "type": "zip", "shasum": "", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.2"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.1"}}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "66bea3b09be61613cd3b4043a65a8ec48cfa6d2a"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/66bea3b09be61613cd3b4043a65a8ec48cfa6d2a", "type": "zip", "shasum": "", "reference": "66bea3b09be61613cd3b4043a65a8ec48cfa6d2a"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.0"}, "time": "2021-07-12T14:48:14+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "69fee1ad2332a7cbab3aca13591953da9cdb7a11"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/69fee1ad2332a7cbab3aca13591953da9cdb7a11", "type": "zip", "shasum": "", "reference": "69fee1ad2332a7cbab3aca13591953da9cdb7a11"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.4.1"}, "time": "2021-03-23T23:28:01+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.4-dev"}}}, {"version": "v2.4.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.4.0"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/0ba7d54483095a198fa51781bc608d17e84dffa2", "type": "zip", "shasum": "", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.2.0"}, "time": "2020-09-07T11:33:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "f6f613d74cfc5a623fc36294d3451eb7fa5a042b"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f6f613d74cfc5a623fc36294d3451eb7fa5a042b", "type": "zip", "shasum": "", "reference": "f6f613d74cfc5a623fc36294d3451eb7fa5a042b"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/master"}, "time": "2020-07-06T13:23:11+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "405952c4e90941a17e52ef7489a2bd94870bb290"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/405952c4e90941a17e52ef7489a2bd94870bb290", "type": "zip", "shasum": "", "reference": "405952c4e90941a17e52ef7489a2bd94870bb290"}, "time": "2020-05-20T17:43:50+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "a66baaea4423031259070211f5123aa9200e7c42"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/a66baaea4423031259070211f5123aa9200e7c42", "type": "zip", "shasum": "", "reference": "a66baaea4423031259070211f5123aa9200e7c42"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.1.1"}, "time": "2020-05-08T21:41:03+00:00", "require": {"php": "^7.2.5", "psr/event-dispatcher": "^1"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "5a749bda3958260864228e520465150cfd954c8e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/5a749bda3958260864228e520465150cfd954c8e", "type": "zip", "shasum": "", "reference": "5a749bda3958260864228e520465150cfd954c8e"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.1.0"}, "time": "2020-02-14T07:31:56+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "af23c2584d4577d54661c434446fb8fbed6025dd"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/af23c2584d4577d54661c434446fb8fbed6025dd", "type": "zip", "shasum": "", "reference": "af23c2584d4577d54661c434446fb8fbed6025dd"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.0.1"}, "time": "2019-11-18T17:27:11+00:00", "funding": "__unset"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "bb09b1d8f8a35243195189ded0d41bdc798ac2fb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/bb09b1d8f8a35243195189ded0d41bdc798ac2fb", "type": "zip", "shasum": "", "reference": "bb09b1d8f8a35243195189ded0d41bdc798ac2fb"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.0.0"}, "time": "2019-11-09T09:18:34+00:00", "require": {"php": "^7.2.9", "psr/event-dispatcher": "^1"}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "type": "zip", "shasum": "", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "1.1-dev"}}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}}, {"version": "v1.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "1d5cd762abaa6b2a4169d3e77610193a7157129e"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/1d5cd762abaa6b2a4169d3e77610193a7157129e", "type": "zip", "shasum": "", "reference": "1d5cd762abaa6b2a4169d3e77610193a7157129e"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.13"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v1.1.12", "version_normalized": "********", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.12"}}, {"version": "v1.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "01e9a4efac0ee33a05dfdf93b346f62e7d0e998c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/01e9a4efac0ee33a05dfdf93b346f62e7d0e998c", "type": "zip", "shasum": "", "reference": "01e9a4efac0ee33a05dfdf93b346f62e7d0e998c"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.11"}, "time": "2021-03-23T15:25:38+00:00"}, {"version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/84e23fdcd2517bf37aecbd16967e83f0caee25a7", "type": "zip", "shasum": "", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.9"}, "time": "2020-07-06T13:19:58+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "type": "zip", "shasum": "", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.8"}, "time": "2019-09-17T09:54:03+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": "^7.1.3"}}, {"version": "v1.1.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/master"}, "funding": "__unset"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "6dd7d743cb4141e634a5ec6082ef8f59ad90e4bb"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/6dd7d743cb4141e634a5ec6082ef8f59ad90e4bb", "type": "zip", "shasum": "", "reference": "6dd7d743cb4141e634a5ec6082ef8f59ad90e4bb"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2019-05-30T22:41:44+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "c61766f4440ca687de1084a5c00b08e167a2575c"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/c61766f4440ca687de1084a5c00b08e167a2575c", "type": "zip", "shasum": "", "reference": "c61766f4440ca687de1084a5c00b08e167a2575c"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.5"}, "time": "2019-06-20T06:46:26+00:00", "funding": "__unset"}, {"version": "v1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "04e22670b5827c9608d0e2b14290edc41842fab6"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/04e22670b5827c9608d0e2b14290edc41842fab6", "type": "zip", "shasum": "", "reference": "04e22670b5827c9608d0e2b14290edc41842fab6"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/master"}, "time": "2019-06-17T11:53:04+00:00"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/event-dispatcher-contracts.git", "type": "git", "reference": "8fa2cf2177083dd59cf8e44ea4b6541764fbda69"}, "dist": {"url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/8fa2cf2177083dd59cf8e44ea4b6541764fbda69", "type": "zip", "shasum": "", "reference": "8fa2cf2177083dd59cf8e44ea4b6541764fbda69"}, "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2019-05-22T12:23:29+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.1"}, "funding": "__unset"}]}, "security-advisories": [], "last-modified": "Sun, 25 May 2025 20:28:22 GMT"}