{"minified": "composer/2.0", "packages": {"phpdocumentor/reflection-docblock": [{"name": "phpdocumentor/reflection-docblock", "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "keywords": [], "homepage": "", "version": "5.6.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "type": "zip", "shasum": "", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "type": "library", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "funding": [], "time": "2025-04-13T19:20:35+00:00", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "require": {"php": "^7.4 || ^8.0", "phpdocumentor/type-resolver": "^1.7", "webmozart/assert": "^1.9.1", "phpdocumentor/reflection-common": "^2.2", "ext-filter": "*", "phpstan/phpdoc-parser": "^1.7|^2.0", "doctrine/deprecations": "^1.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "psalm/phar": "^5.26"}}, {"version": "5.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8", "type": "zip", "shasum": "", "reference": "e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.1"}, "time": "2024-12-07T09:39:29+00:00"}, {"version": "5.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "f3558a4c23426d12bffeaab463f8a8d8b681193c"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/f3558a4c23426d12bffeaab463f8a8d8b681193c", "type": "zip", "shasum": "", "reference": "f3558a4c23426d12bffeaab463f8a8d8b681193c"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.0"}, "time": "2024-11-12T11:25:25+00:00"}, {"version": "5.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "0c70d2c566e899666f367ab7b80986beb3581e6f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/0c70d2c566e899666f367ab7b80986beb3581e6f", "type": "zip", "shasum": "", "reference": "0c70d2c566e899666f367ab7b80986beb3581e6f"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.5.1"}, "time": "2024-11-06T11:58:54+00:00", "require": {"php": "^7.4 || ^8.0", "phpdocumentor/type-resolver": "^1.7", "webmozart/assert": "^1.9.1", "phpdocumentor/reflection-common": "^2.2", "ext-filter": "*", "phpstan/phpdoc-parser": "^1.7", "doctrine/deprecations": "^1.1"}}, {"version": "5.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "54e10d44fc1a84e2598d26f70d4f6f1f233e228a"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/54e10d44fc1a84e2598d26f70d4f6f1f233e228a", "type": "zip", "shasum": "", "reference": "54e10d44fc1a84e2598d26f70d4f6f1f233e228a"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.5.0"}, "time": "2024-11-04T21:26:31+00:00"}, {"version": "5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "type": "zip", "shasum": "", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.4.1"}, "time": "2024-05-21T05:55:05+00:00", "require-dev": {"mockery/mockery": "~1.3.5", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "vimeo/psalm": "^5.13"}}, {"version": "5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "298d2febfe79d03fe714eb871d5538da55205b1a"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/298d2febfe79d03fe714eb871d5538da55205b1a", "type": "zip", "shasum": "", "reference": "298d2febfe79d03fe714eb871d5538da55205b1a"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.4.0"}, "time": "2024-04-09T21:13:58+00:00"}, {"version": "5.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "type": "zip", "shasum": "", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00", "require": {"php": "^7.2 || ^8.0", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1", "phpdocumentor/reflection-common": "^2.2", "ext-filter": "*"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}}, {"version": "5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/069a785b2141f5bcf49f3e353548dc1cce6df556", "type": "zip", "shasum": "", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2020-09-03T19:13:55+00:00", "require-dev": {"mockery/mockery": "~1.3.2"}}, {"version": "5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "d870572532cd70bc3fab58f2e23ad423c8404c44"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/d870572532cd70bc3fab58f2e23ad423c8404c44", "type": "zip", "shasum": "", "reference": "d870572532cd70bc3fab58f2e23ad423c8404c44"}, "time": "2020-08-15T11:14:08+00:00"}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "3170448f5769fe19f456173d833734e0ff1b84df"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/3170448f5769fe19f456173d833734e0ff1b84df", "type": "zip", "shasum": "", "reference": "3170448f5769fe19f456173d833734e0ff1b84df"}, "time": "2020-07-20T20:05:34+00:00"}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "type": "zip", "shasum": "", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.1.0"}, "time": "2020-02-22T12:28:44+00:00", "require": {"php": "^7.2", "phpdocumentor/type-resolver": "^1.0", "webmozart/assert": "^1", "phpdocumentor/reflection-common": "^2.0", "ext-filter": "^7.1"}, "require-dev": {"mockery/mockery": "^1", "doctrine/instantiator": "^1"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "a48807183a4b819072f26e347bbd0b5199a9d15f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/a48807183a4b819072f26e347bbd0b5199a9d15f", "type": "zip", "shasum": "", "reference": "a48807183a4b819072f26e347bbd0b5199a9d15f"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2020-02-09T09:16:15+00:00", "funding": "__unset"}, {"version": "5.0.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "93919e334b0cb304fb04429b4b3a3b1e1787c873"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/93919e334b0cb304fb04429b4b3a3b1e1787c873", "type": "zip", "shasum": "", "reference": "93919e334b0cb304fb04429b4b3a3b1e1787c873"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.0.0-beta"}, "time": "2020-01-27T20:01:09+00:00"}, {"version": "5.0.0-alpha9", "version_normalized": "*******-alpha9", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "cf16f630f2211d388b8d254bf2ea936c232b3cb4"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cf16f630f2211d388b8d254bf2ea936c232b3cb4", "type": "zip", "shasum": "", "reference": "cf16f630f2211d388b8d254bf2ea936c232b3cb4"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2020-01-16T08:49:07+00:00"}, {"version": "5.0.0-alpha8", "version_normalized": "*******-alpha8", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "f256f0b49e236c3b4705804d9998379282e0f75d"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/f256f0b49e236c3b4705804d9998379282e0f75d", "type": "zip", "shasum": "", "reference": "f256f0b49e236c3b4705804d9998379282e0f75d"}, "time": "2020-01-12T12:26:24+00:00"}, {"version": "5.0.0-alpha7", "version_normalized": "*******-alpha7", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "c5a39003d5c0ef8e605d123d23355b1cb4112735"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/c5a39003d5c0ef8e605d123d23355b1cb4112735", "type": "zip", "shasum": "", "reference": "c5a39003d5c0ef8e605d123d23355b1cb4112735"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.0.0-alpha7"}, "time": "2019-12-27T20:42:04+00:00"}, {"version": "5.0.0-alpha6", "version_normalized": "*******-alpha6", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "c19ab7ef57e75b5790aa912fd1cd14708e811970"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/c19ab7ef57e75b5790aa912fd1cd14708e811970", "type": "zip", "shasum": "", "reference": "c19ab7ef57e75b5790aa912fd1cd14708e811970"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.0.0-alpha6"}, "time": "2019-12-20T13:36:14+00:00"}, {"version": "5.0.0-alpha5", "version_normalized": "*******-alpha5", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "8fcadfe5f85c38705151c9ab23b4781f23e6a70e"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/8fcadfe5f85c38705151c9ab23b4781f23e6a70e", "type": "zip", "shasum": "", "reference": "8fcadfe5f85c38705151c9ab23b4781f23e6a70e"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2019-06-15T20:45:01+00:00", "require": {"php": ">=7.1", "phpdocumentor/type-resolver": "^0", "webmozart/assert": "^1"}}, {"version": "5.0.0-alpha4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "48351665a881883231add24fbb7ef869adca2316"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/48351665a881883231add24fbb7ef869adca2316", "type": "zip", "shasum": "", "reference": "48351665a881883231add24fbb7ef869adca2316"}, "time": "2019-04-30T18:01:57+00:00"}, {"version": "5.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "a29cd5c528ececde017f66054656703f6338b4c3"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/a29cd5c528ececde017f66054656703f6338b4c3", "type": "zip", "shasum": "", "reference": "a29cd5c528ececde017f66054656703f6338b4c3"}, "time": "2018-06-20T14:34:23+00:00"}, {"version": "5.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "ef73f5cfdb7a28dc89ad52e68b695fdb8f67115b"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/ef73f5cfdb7a28dc89ad52e68b695fdb8f67115b", "type": "zip", "shasum": "", "reference": "ef73f5cfdb7a28dc89ad52e68b695fdb8f67115b"}, "time": "2018-06-14T12:47:29+00:00"}, {"version": "5.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "6946f85e19ea3342353e22b83b4e2e59414c1a2f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/6946f85e19ea3342353e22b83b4e2e59414c1a2f", "type": "zip", "shasum": "", "reference": "6946f85e19ea3342353e22b83b4e2e59414c1a2f"}, "time": "2018-01-31T20:23:19+00:00", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}}, {"version": "4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "type": "zip", "shasum": "", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/4.x"}, "time": "2019-12-28T18:55:12+00:00", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4", "doctrine/instantiator": "^1.0.5"}}, {"version": "4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "2ecaa9fef01634c83bfa8dc1fe35fb5cef223a62"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/2ecaa9fef01634c83bfa8dc1fe35fb5cef223a62", "type": "zip", "shasum": "", "reference": "2ecaa9fef01634c83bfa8dc1fe35fb5cef223a62"}, "time": "2019-12-20T13:40:23+00:00", "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^6.4", "doctrine/instantiator": "^1.0.5"}}, {"version": "4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "b83ff7cfcfee7827e1e78b637a5904fe6a96698e"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/b83ff7cfcfee7827e1e78b637a5904fe6a96698e", "type": "zip", "shasum": "", "reference": "b83ff7cfcfee7827e1e78b637a5904fe6a96698e"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/4.3.2"}, "time": "2019-09-12T14:27:41+00:00"}, {"version": "4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "bdd9f737ebc2a01c06ea7ff4308ec6697db9b53c"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/bdd9f737ebc2a01c06ea7ff4308ec6697db9b53c", "type": "zip", "shasum": "", "reference": "bdd9f737ebc2a01c06ea7ff4308ec6697db9b53c"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/4.3.1"}, "time": "2019-04-30T17:48:53+00:00", "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^6.4", "doctrine/instantiator": "~1.0.5"}}, {"version": "4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "94fd0001232e47129dd3504189fa1c7225010d08"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/94fd0001232e47129dd3504189fa1c7225010d08", "type": "zip", "shasum": "", "reference": "94fd0001232e47129dd3504189fa1c7225010d08"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2017-11-30T07:14:17+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "66465776cfc249844bde6d117abff1d22e06c2da"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/66465776cfc249844bde6d117abff1d22e06c2da", "type": "zip", "shasum": "", "reference": "66465776cfc249844bde6d117abff1d22e06c2da"}, "time": "2017-11-27T17:38:31+00:00"}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "2d3d238c433cf69caeb4842e97a3223a116f94b2"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/2d3d238c433cf69caeb4842e97a3223a116f94b2", "type": "zip", "shasum": "", "reference": "2d3d238c433cf69caeb4842e97a3223a116f94b2"}, "time": "2017-08-30T18:51:59+00:00", "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "extra": "__unset"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "125c8b15c80827dbcd32facc9f85c8a603617d87"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/125c8b15c80827dbcd32facc9f85c8a603617d87", "type": "zip", "shasum": "", "reference": "125c8b15c80827dbcd32facc9f85c8a603617d87"}, "time": "2017-08-18T21:01:43+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "a8d791f8c91ea5fb91580d48f2c7903849271ba7"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/a8d791f8c91ea5fb91580d48f2c7903849271ba7", "type": "zip", "shasum": "", "reference": "a8d791f8c91ea5fb91580d48f2c7903849271ba7"}, "time": "2017-08-08T06:43:50+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "3c8d2425b563012ec50ffc8c59fe76247ac6e03e"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/3c8d2425b563012ec50ffc8c59fe76247ac6e03e", "type": "zip", "shasum": "", "reference": "3c8d2425b563012ec50ffc8c59fe76247ac6e03e"}, "time": "2017-08-04T21:25:06+00:00"}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/bf329f6c1aadea3299f08ee804682b7c45b326a2", "type": "zip", "shasum": "", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/3.x"}, "time": "2017-11-10T14:09:06+00:00", "require": {"php": "^5.6 || ^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "48eb1f629d55621f03ca72195fe04d05d463b807"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/48eb1f629d55621f03ca72195fe04d05d463b807", "type": "zip", "shasum": "", "reference": "48eb1f629d55621f03ca72195fe04d05d463b807"}, "time": "2017-09-11T18:06:56+00:00", "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "86e24012a3139b42a7b71155cfaa325389f00f1f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/86e24012a3139b42a7b71155cfaa325389f00f1f", "type": "zip", "shasum": "", "reference": "86e24012a3139b42a7b71155cfaa325389f00f1f"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2017-08-29T19:37:41+00:00", "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "4aada1f93c72c35e22fb1383b47fee43b8f1d157"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/4aada1f93c72c35e22fb1383b47fee43b8f1d157", "type": "zip", "shasum": "", "reference": "4aada1f93c72c35e22fb1383b47fee43b8f1d157"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/3.x"}, "time": "2017-08-08T06:39:58+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.3.0", "webmozart/assert": "^1.0"}}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "183824db76118b9dddffc7e522b91fa175f75119"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/183824db76118b9dddffc7e522b91fa175f75119", "type": "zip", "shasum": "", "reference": "183824db76118b9dddffc7e522b91fa175f75119"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2017-08-04T20:55:59+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "46f7e8bb075036c92695b15a1ddb6971c751e585"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/46f7e8bb075036c92695b15a1ddb6971c751e585", "type": "zip", "shasum": "", "reference": "46f7e8bb075036c92695b15a1ddb6971c751e585"}, "time": "2017-07-15T11:38:20+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "8331b5efe816ae05461b7ca1e721c01b46bafb3e"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/8331b5efe816ae05461b7ca1e721c01b46bafb3e", "type": "zip", "shasum": "", "reference": "8331b5efe816ae05461b7ca1e721c01b46bafb3e"}, "time": "2016-09-30T07:12:33+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.2.0", "webmozart/assert": "^1.0"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "9270140b940ff02e58ec577c237274e92cd40cdd"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/9270140b940ff02e58ec577c237274e92cd40cdd", "type": "zip", "shasum": "", "reference": "9270140b940ff02e58ec577c237274e92cd40cdd"}, "time": "2016-06-10T09:48:41+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "ea48fd2c79b68fba8693ae67a21cae5b5ee4cfb8"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/ea48fd2c79b68fba8693ae67a21cae5b5ee4cfb8", "type": "zip", "shasum": "", "reference": "ea48fd2c79b68fba8693ae67a21cae5b5ee4cfb8"}, "time": "2016-06-10T07:04:47+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.1.5", "webmozart/assert": "^1.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "45ada3e3fd09789fbfbd6d65b3f0901f0030dc61"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/45ada3e3fd09789fbfbd6d65b3f0901f0030dc61", "type": "zip", "shasum": "", "reference": "45ada3e3fd09789fbfbd6d65b3f0901f0030dc61"}, "time": "2016-06-06T06:44:13+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "52723f0fb83d053be05c3f6b58ec45e5851c0e99"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/52723f0fb83d053be05c3f6b58ec45e5851c0e99", "type": "zip", "shasum": "", "reference": "52723f0fb83d053be05c3f6b58ec45e5851c0e99"}, "time": "2016-02-26T20:44:16+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "332c96dab8309293384505593ed7259c35a143e2"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/332c96dab8309293384505593ed7259c35a143e2", "type": "zip", "shasum": "", "reference": "332c96dab8309293384505593ed7259c35a143e2"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/3.0.0"}, "time": "2016-01-28T12:45:51+00:00"}, {"description": "", "version": "2.0.5", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "e6a969a640b00d8daa3c66518b0405fb41ae0c4b"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/e6a969a640b00d8daa3c66518b0405fb41ae0c4b", "type": "zip", "shasum": "", "reference": "e6a969a640b00d8daa3c66518b0405fb41ae0c4b"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/2.x"}, "time": "2016-01-25T08:17:30+00:00", "autoload": {"psr-0": {"phpDocumentor": ["src/"]}}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "d68dbdc53dc358a816f00b300704702b2eaff7b8"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/d68dbdc53dc358a816f00b300704702b2eaff7b8", "type": "zip", "shasum": "", "reference": "d68dbdc53dc358a816f00b300704702b2eaff7b8"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/2.0.4"}, "time": "2015-02-03T12:10:50+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "38743b677965c48a637097b2746a281264ae2347"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/38743b677965c48a637097b2746a281264ae2347", "type": "zip", "shasum": "", "reference": "38743b677965c48a637097b2746a281264ae2347"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2014-08-09T10:27:07+00:00", "require-dev": {"phpunit/phpunit": "3.7.*@stable"}, "suggest": {"dflydev/markdown": "1.0.*", "erusev/parsedown": "~0.7"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "0bca477a34baea39add016af90046f002a175619"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/0bca477a34baea39add016af90046f002a175619", "type": "zip", "shasum": "", "reference": "0bca477a34baea39add016af90046f002a175619"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/2.0.2"}, "time": "2014-03-28T09:21:30+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "cfb3ebea556b24df8f8f3745d61478293cb5a166"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cfb3ebea556b24df8f8f3745d61478293cb5a166", "type": "zip", "shasum": "", "reference": "cfb3ebea556b24df8f8f3745d61478293cb5a166"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/2.0.1"}, "time": "2013-12-05T08:16:55+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "66ae84e9d7c8ea85c979cb65977bd8e608baf0c5"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/66ae84e9d7c8ea85c979cb65977bd8e608baf0c5", "type": "zip", "shasum": "", "reference": "66ae84e9d7c8ea85c979cb65977bd8e608baf0c5"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2013-08-07T11:04:22+00:00", "require": {"php": ">=5.3.3", "dflydev/markdown": "1.0.*"}, "suggest": "__unset"}, {"version": "2.0.0a3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "47d3f86c53985b16d80f9a211d639505ccc3824a"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/47d3f86c53985b16d80f9a211d639505ccc3824a", "type": "zip", "shasum": "", "reference": "47d3f86c53985b16d80f9a211d639505ccc3824a"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/2.0.0a3"}, "time": "2013-04-16T14:22:15+00:00", "extra": "__unset"}, {"version": "2.0.0a2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "5d93f42598d6a8332433856fb5656606a4c1e1e3"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/5d93f42598d6a8332433856fb5656606a4c1e1e3", "type": "zip", "shasum": "", "reference": "5d93f42598d6a8332433856fb5656606a4c1e1e3"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/2.0.0a2"}, "time": "2012-12-02T20:26:45+00:00"}, {"version": "2.0.0a1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "841897062442e2a0e8b3def1518caa25f6498dfd"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/841897062442e2a0e8b3def1518caa25f6498dfd", "type": "zip", "shasum": "", "reference": "841897062442e2a0e8b3def1518caa25f6498dfd"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/2.0.0a1"}, "time": "2012-11-27T17:52:28+00:00", "require": {"php": ">=5.3.2", "dflydev/markdown": "1.0.*"}, "require-dev": {"phpunit/phpunit": "*@stable"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "6b707f71668d15d14564fc3766e108551037f10e"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/6b707f71668d15d14564fc3766e108551037f10e", "type": "zip", "shasum": "", "reference": "6b707f71668d15d14564fc3766e108551037f10e"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.3"}, "time": "2012-11-03T21:14:43+00:00", "require-dev": "__unset"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "60ba10fad1cf05066872b953a8b8d0cd204de9ee"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/60ba10fad1cf05066872b953a8b8d0cd204de9ee", "type": "zip", "shasum": "", "reference": "60ba10fad1cf05066872b953a8b8d0cd204de9ee"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.2"}, "time": "2012-09-10T14:50:44+00:00", "autoload": {"psr-0": {"phpDocumentor\\Reflection": "src/"}}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "c999e7d32a64ad69b91d16f7904a1c2793323feb"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/c999e7d32a64ad69b91d16f7904a1c2793323feb", "type": "zip", "shasum": "", "reference": "c999e7d32a64ad69b91d16f7904a1c2793323feb"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.1"}, "time": "2012-07-26T15:09:50+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "2b05ea32215fffe542e11795ea33ad82def10c58"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/2b05ea32215fffe542e11795ea33ad82def10c58", "type": "zip", "shasum": "", "reference": "2b05ea32215fffe542e11795ea33ad82def10c58"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0"}, "time": "2012-07-13T18:07:51+00:00"}, {"version": "1.0.0-beta7", "version_normalized": "*******-beta7", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "cee3c0bc21d102762a81432d23f4bff9d5ef8658"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cee3c0bc21d102762a81432d23f4bff9d5ef8658", "type": "zip", "shasum": "", "reference": "cee3c0bc21d102762a81432d23f4bff9d5ef8658"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta7"}, "time": "2012-06-30T11:38:18+00:00"}, {"version": "1.0.0-beta6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "838fbfa43f3f4bb9081613514eb55535e9854cfd"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/838fbfa43f3f4bb9081613514eb55535e9854cfd", "type": "zip", "shasum": "", "reference": "838fbfa43f3f4bb9081613514eb55535e9854cfd"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta6"}, "time": "2012-06-21T21:03:48+00:00"}, {"version": "1.0.0-beta5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "8f8cbd9ab6028e823c44444258788c386a457b0d"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/8f8cbd9ab6028e823c44444258788c386a457b0d", "type": "zip", "shasum": "", "reference": "8f8cbd9ab6028e823c44444258788c386a457b0d"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta5"}, "time": "2012-06-04T20:34:54+00:00"}, {"version": "1.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "f0a2901e56721169a14915afb7afd52f7cc31f48"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/f0a2901e56721169a14915afb7afd52f7cc31f48", "type": "zip", "shasum": "", "reference": "f0a2901e56721169a14915afb7afd52f7cc31f48"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta4"}, "time": "2012-06-02T20:07:17+00:00"}, {"version": "1.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "3f8a4fca6dad3de99f76515c9a14222129f62b89"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/3f8a4fca6dad3de99f76515c9a14222129f62b89", "type": "zip", "shasum": "", "reference": "3f8a4fca6dad3de99f76515c9a14222129f62b89"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta3"}, "time": "2012-04-07T09:49:54+00:00", "require": {"php": ">=5.3.2"}}, {"version": "1.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "6a34cf123c10f9585d1d2453e7602c97db438979"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/6a34cf123c10f9585d1d2453e7602c97db438979", "type": "zip", "shasum": "", "reference": "6a34cf123c10f9585d1d2453e7602c97db438979"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta2"}, "time": "2012-04-06T18:39:59+00:00", "autoload": {"psr-0": {"phpDocumentor\\Reflection": "src/phpDocumentor"}}}, {"version": "1.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "type": "git", "reference": "fea7abe168dfd01d96f61daa4bc27891814b94f3"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/fea7abe168dfd01d96f61daa4bc27891814b94f3", "type": "zip", "shasum": "", "reference": "fea7abe168dfd01d96f61daa4bc27891814b94f3"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/1.0.0-beta1"}, "time": "2012-04-06T18:15:46+00:00", "autoload": {"psr-0": {"phpDocumentor/Reflection": "src/phpDocumentor"}}}]}, "security-advisories": [], "last-modified": "Sun, 13 Apr 2025 19:21:54 GMT"}