{"minified": "composer/2.0", "packages": {"symfony/translation": [{"name": "symfony/translation", "description": "Provides tools to internationalize your application", "keywords": [], "homepage": "https://symfony.com", "version": "v7.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "241d5ac4910d256660238a7ecf250deba4c73063"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/241d5ac4910d256660238a7ecf250deba4c73063", "type": "zip", "shasum": "", "reference": "241d5ac4910d256660238a7ecf250deba4c73063"}, "type": "library", "support": {"source": "https://github.com/symfony/translation/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"nikic/php-parser": "^5.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"nikic/php-parser": "<5.0", "symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4", "symfony/console": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4aba29076a29a3aa667e09b791e5f868973a8667"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4aba29076a29a3aa667e09b791e5f868973a8667", "type": "zip", "shasum": "", "reference": "4aba29076a29a3aa667e09b791e5f868973a8667"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.3.0"}, "time": "2025-05-29T07:19:49+00:00"}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0760342c70d8352ac27ce75fc62bcaebc3ef5c3e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0760342c70d8352ac27ce75fc62bcaebc3ef5c3e", "type": "zip", "shasum": "", "reference": "0760342c70d8352ac27ce75fc62bcaebc3ef5c3e"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.3.0-RC1"}, "time": "2025-04-24T11:26:44+00:00"}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/translation/tree/v7.3.0-BETA1"}}, {"version": "v7.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3d0a549438da132d6c8aca723939a4a49fa48030"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3d0a549438da132d6c8aca723939a4a49fa48030", "type": "zip", "shasum": "", "reference": "3d0a549438da132d6c8aca723939a4a49fa48030"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.8"}, "time": "2025-06-27T19:53:16+00:00", "require-dev": {"nikic/php-parser": "^4.18|^5.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4", "symfony/console": "<6.4"}}, {"version": "v7.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5f8502e4c2d8e8ac50d51ac6e84b2d8b3dadcbc7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5f8502e4c2d8e8ac50d51ac6e84b2d8b3dadcbc7", "type": "zip", "shasum": "", "reference": "5f8502e4c2d8e8ac50d51ac6e84b2d8b3dadcbc7"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.7"}, "time": "2025-05-29T07:19:28+00:00"}, {"version": "v7.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6", "type": "zip", "shasum": "", "reference": "e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.6"}, "time": "2025-04-07T19:09:28+00:00"}, {"version": "v7.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "283856e6981286cc0d800b53bd5703e8e363f05a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/283856e6981286cc0d800b53bd5703e8e363f05a", "type": "zip", "shasum": "", "reference": "283856e6981286cc0d800b53bd5703e8e363f05a"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.4"}, "time": "2025-02-13T10:27:23+00:00"}, {"version": "v7.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e2674a30132b7cc4d74540d6c2573aa363f05923"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e2674a30132b7cc4d74540d6c2573aa363f05923", "type": "zip", "shasum": "", "reference": "e2674a30132b7cc4d74540d6c2573aa363f05923"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.2"}, "time": "2024-12-07T08:18:10+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dc89e16b44048ceecc879054e5b7f38326ab6cc5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dc89e16b44048ceecc879054e5b7f38326ab6cc5", "type": "zip", "shasum": "", "reference": "dc89e16b44048ceecc879054e5b7f38326ab6cc5"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.0"}, "time": "2024-11-12T20:47:56+00:00"}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f1faf9a381d39d0bf8ca1c10cab7dcf41fba50dc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f1faf9a381d39d0bf8ca1c10cab7dcf41fba50dc", "type": "zip", "shasum": "", "reference": "f1faf9a381d39d0bf8ca1c10cab7dcf41fba50dc"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.2.0-RC1"}, "time": "2024-10-23T06:56:12+00:00"}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/translation/tree/v7.2.0-BETA1"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b9f72ab14efdb6b772f85041fa12f820dee8d55f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b9f72ab14efdb6b772f85041fa12f820dee8d55f", "type": "zip", "shasum": "", "reference": "b9f72ab14efdb6b772f85041fa12f820dee8d55f"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.1.6"}, "time": "2024-09-28T12:35:13+00:00", "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}}, {"version": "v7.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "235535e3f84f3dfbdbde0208ede6ca75c3a489ea"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/235535e3f84f3dfbdbde0208ede6ca75c3a489ea", "type": "zip", "shasum": "", "reference": "235535e3f84f3dfbdbde0208ede6ca75c3a489ea"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.1.5"}, "time": "2024-09-16T06:30:38+00:00"}, {"version": "v7.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8d5e50c813ba2859a6dfc99a0765c550507934a1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8d5e50c813ba2859a6dfc99a0765c550507934a1", "type": "zip", "shasum": "", "reference": "8d5e50c813ba2859a6dfc99a0765c550507934a1"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.1.3"}, "time": "2024-07-26T12:41:01+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "cf5ae136e124fc7681b34ce9fac9d5b9ae8ceee3"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/cf5ae136e124fc7681b34ce9fac9d5b9ae8ceee3", "type": "zip", "shasum": "", "reference": "cf5ae136e124fc7681b34ce9fac9d5b9ae8ceee3"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "583d18e461eada8270ca44b7d99f07abf1ab048e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/583d18e461eada8270ca44b7d99f07abf1ab048e", "type": "zip", "shasum": "", "reference": "583d18e461eada8270ca44b7d99f07abf1ab048e"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.1.0"}, "time": "2024-05-02T11:50:05+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/translation/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/translation/tree/v7.1.0-BETA1"}}, {"version": "v7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "480fc1b9f44d9104239eb36de1e1151814c8a8d8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/480fc1b9f44d9104239eb36de1e1151814c8a8d8", "type": "zip", "shasum": "", "reference": "480fc1b9f44d9104239eb36de1e1151814c8a8d8"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.10"}, "time": "2024-07-26T12:31:22+00:00"}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "649a646a5aae6e86da1ea119643c53d4080624c2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/649a646a5aae6e86da1ea119643c53d4080624c2", "type": "zip", "shasum": "", "reference": "649a646a5aae6e86da1ea119643c53d4080624c2"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1515e03afaa93e6419aba5d5c9d209159317100b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1515e03afaa93e6419aba5d5c9d209159317100b", "type": "zip", "shasum": "", "reference": "1515e03afaa93e6419aba5d5c9d209159317100b"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5b75e872f7d135d7abb4613809fadc8d9f3d30a0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5b75e872f7d135d7abb4613809fadc8d9f3d30a0", "type": "zip", "shasum": "", "reference": "5b75e872f7d135d7abb4613809fadc8d9f3d30a0"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.4"}, "time": "2024-02-22T20:27:20+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7285f25c7dcc74d9ec1232473114274604e50f00"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7285f25c7dcc74d9ec1232473114274604e50f00", "type": "zip", "shasum": "", "reference": "7285f25c7dcc74d9ec1232473114274604e50f00"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.3"}, "time": "2024-01-29T15:41:16+00:00"}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a7566ad85f4328c4a8cdaf441fcfab18ece995e1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a7566ad85f4328c4a8cdaf441fcfab18ece995e1", "type": "zip", "shasum": "", "reference": "a7566ad85f4328c4a8cdaf441fcfab18ece995e1"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.2"}, "time": "2023-12-19T11:23:03+00:00", "require-dev": {"nikic/php-parser": "^4.13", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "psr/log": "^1|^2|^3"}}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ab5a14723f23159854bf91b41255cad23b929fab"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ab5a14723f23159854bf91b41255cad23b929fab", "type": "zip", "shasum": "", "reference": "ab5a14723f23159854bf91b41255cad23b929fab"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.0"}, "time": "2023-11-29T08:40:23+00:00"}, {"version": "v7.0.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dfe7a6a16a06ec3147941830e54e732f6991625e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dfe7a6a16a06ec3147941830e54e732f6991625e", "type": "zip", "shasum": "", "reference": "dfe7a6a16a06ec3147941830e54e732f6991625e"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.0-RC2"}, "time": "2023-11-25T08:38:27+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "50ec3a4a7788bc1a1ca68939ffb2974687ee6af8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/50ec3a4a7788bc1a1ca68939ffb2974687ee6af8", "type": "zip", "shasum": "", "reference": "50ec3a4a7788bc1a1ca68939ffb2974687ee6af8"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.0-RC1"}, "time": "2023-10-28T23:12:22+00:00"}, {"version": "v7.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/translation/tree/v7.0.0-BETA2"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "babf310da2def2c769d27834f81457a8c14e3e10"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/babf310da2def2c769d27834f81457a8c14e3e10", "type": "zip", "shasum": "", "reference": "babf310da2def2c769d27834f81457a8c14e3e10"}, "support": {"source": "https://github.com/symfony/translation/tree/v7.0.0-BETA1"}, "time": "2023-10-20T16:35:23+00:00"}, {"version": "v6.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "de8afa521e04a5220e9e58a1dc99971ab7cac643"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/de8afa521e04a5220e9e58a1dc99971ab7cac643", "type": "zip", "shasum": "", "reference": "de8afa521e04a5220e9e58a1dc99971ab7cac643"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.23"}, "time": "2025-06-26T21:24:02+00:00", "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4", "symfony/console": "<5.4"}}, {"version": "v6.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7e3b3b7146c6fab36ddff304a8041174bf6e17ad"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7e3b3b7146c6fab36ddff304a8041174bf6e17ad", "type": "zip", "shasum": "", "reference": "7e3b3b7146c6fab36ddff304a8041174bf6e17ad"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.22"}, "time": "2025-05-29T07:06:44+00:00"}, {"version": "v6.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bb92ea5588396b319ba43283a5a3087a034cb29c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bb92ea5588396b319ba43283a5a3087a034cb29c", "type": "zip", "shasum": "", "reference": "bb92ea5588396b319ba43283a5a3087a034cb29c"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.21"}, "time": "2025-04-07T19:02:30+00:00"}, {"version": "v6.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3b9bf9f33997c064885a7bfc126c14b9daa0e00e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3b9bf9f33997c064885a7bfc126c14b9daa0e00e", "type": "zip", "shasum": "", "reference": "3b9bf9f33997c064885a7bfc126c14b9daa0e00e"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.19"}, "time": "2025-02-13T10:18:43+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bee9bfabfa8b4045a66bf82520e492cddbaffa66"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bee9bfabfa8b4045a66bf82520e492cddbaffa66", "type": "zip", "shasum": "", "reference": "bee9bfabfa8b4045a66bf82520e492cddbaffa66"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.13"}, "time": "2024-09-27T18:14:25+00:00"}, {"version": "v6.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "cf8360b8352b086be620fae8342c4d96e391a489"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/cf8360b8352b086be620fae8342c4d96e391a489", "type": "zip", "shasum": "", "reference": "cf8360b8352b086be620fae8342c4d96e391a489"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.12"}, "time": "2024-09-16T06:02:54+00:00"}, {"version": "v6.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "94041203f8ac200ae9e7c6a18fa6137814ccecc9"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/94041203f8ac200ae9e7c6a18fa6137814ccecc9", "type": "zip", "shasum": "", "reference": "94041203f8ac200ae9e7c6a18fa6137814ccecc9"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.10"}, "time": "2024-07-26T12:30:32+00:00"}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a002933b13989fc4bd0b58e04bf7eec5210e438a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a002933b13989fc4bd0b58e04bf7eec5210e438a", "type": "zip", "shasum": "", "reference": "a002933b13989fc4bd0b58e04bf7eec5210e438a"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7495687c58bfd88b7883823747b0656d90679123"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7495687c58bfd88b7883823747b0656d90679123", "type": "zip", "shasum": "", "reference": "7495687c58bfd88b7883823747b0656d90679123"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bce6a5a78e94566641b2594d17e48b0da3184a8e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bce6a5a78e94566641b2594d17e48b0da3184a8e", "type": "zip", "shasum": "", "reference": "bce6a5a78e94566641b2594d17e48b0da3184a8e"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.4"}, "time": "2024-02-20T13:16:58+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "637c51191b6b184184bbf98937702bcf554f7d04"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/637c51191b6b184184bbf98937702bcf554f7d04", "type": "zip", "shasum": "", "reference": "637c51191b6b184184bbf98937702bcf554f7d04"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.3"}, "time": "2024-01-29T13:11:52+00:00"}, {"version": "v6.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a2ab2ec1a462e53016de8e8d5e8912bfd62ea681"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a2ab2ec1a462e53016de8e8d5e8912bfd62ea681", "type": "zip", "shasum": "", "reference": "a2ab2ec1a462e53016de8e8d5e8912bfd62ea681"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.2"}, "time": "2023-12-18T09:25:29+00:00", "require-dev": {"nikic/php-parser": "^4.13", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b1035dbc2a344b21f8fa8ac451c7ecec4ea45f37"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b1035dbc2a344b21f8fa8ac451c7ecec4ea45f37", "type": "zip", "shasum": "", "reference": "b1035dbc2a344b21f8fa8ac451c7ecec4ea45f37"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.0"}, "time": "2023-11-29T08:14:36+00:00"}, {"version": "v6.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "25816e0b4526e682db5e1101194327f39956a3ea"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/25816e0b4526e682db5e1101194327f39956a3ea", "type": "zip", "shasum": "", "reference": "25816e0b4526e682db5e1101194327f39956a3ea"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.0-RC2"}, "time": "2023-11-21T15:24:03+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8057a62933f5609340a4f8c36feeec88c09ea024"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8057a62933f5609340a4f8c36feeec88c09ea024", "type": "zip", "shasum": "", "reference": "8057a62933f5609340a4f8c36feeec88c09ea024"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.0-RC1"}, "time": "2023-10-28T23:12:08+00:00"}, {"version": "v6.4.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.0-BETA2"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1df3b64ada88d18efb1c662a476c8f3a648710d5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1df3b64ada88d18efb1c662a476c8f3a648710d5", "type": "zip", "shasum": "", "reference": "1df3b64ada88d18efb1c662a476c8f3a648710d5"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.4.0-BETA1"}, "time": "2023-10-20T12:00:10+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5c67cd1b1635be525f4dbe89042cdc3749a89ff5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5c67cd1b1635be525f4dbe89042cdc3749a89ff5", "type": "zip", "shasum": "", "reference": "5c67cd1b1635be525f4dbe89042cdc3749a89ff5"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.3.12"}, "time": "2024-01-23T14:35:58+00:00", "require-dev": {"nikic/php-parser": "^4.18|^5.0", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "30212e7c87dcb79c83f6362b00bde0e0b1213499"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/30212e7c87dcb79c83f6362b00bde0e0b1213499", "type": "zip", "shasum": "", "reference": "30212e7c87dcb79c83f6362b00bde0e0b1213499"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.3.7"}, "time": "2023-10-28T23:11:45+00:00", "require-dev": {"nikic/php-parser": "^4.13", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "869b26c7a9d4b8a48afdd77ab36031909c87e3a2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/869b26c7a9d4b8a48afdd77ab36031909c87e3a2", "type": "zip", "shasum": "", "reference": "869b26c7a9d4b8a48afdd77ab36031909c87e3a2"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.3.6"}, "time": "2023-10-17T11:32:53+00:00"}, {"version": "v6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3ed078c54bc98bbe4414e1e9b2d5e85ed5a5c8bd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3ed078c54bc98bbe4414e1e9b2d5e85ed5a5c8bd", "type": "zip", "shasum": "", "reference": "3ed078c54bc98bbe4414e1e9b2d5e85ed5a5c8bd"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.3.3"}, "time": "2023-07-31T07:08:24+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f72b2cba8f79dd9d536f534f76874b58ad37876f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f72b2cba8f79dd9d536f534f76874b58ad37876f", "type": "zip", "shasum": "", "reference": "f72b2cba8f79dd9d536f534f76874b58ad37876f"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.3.0"}, "time": "2023-05-19T12:46:45+00:00", "require": {"php": ">=8.1", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/translation/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8bf7f410f7ff9cfb43f927fb4ec978505beb11d8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8bf7f410f7ff9cfb43f927fb4ec978505beb11d8", "type": "zip", "shasum": "", "reference": "8bf7f410f7ff9cfb43f927fb4ec978505beb11d8"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.3.0-BETA1"}, "time": "2023-04-28T15:57:00+00:00"}, {"version": "v6.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "64113df3e8b009f92fad63014f4ec647e65bc927"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/64113df3e8b009f92fad63014f4ec647e65bc927", "type": "zip", "shasum": "", "reference": "64113df3e8b009f92fad63014f4ec647e65bc927"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.11"}, "time": "2023-05-19T12:37:14+00:00", "require": {"php": ">=8.1", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "require-dev": {"nikic/php-parser": "^4.13", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "psr/log": "^1|^2|^3"}, "suggest": {"nikic/php-parser": "To use PhpAstExtractor", "psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "conflict": {"symfony/config": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4", "symfony/console": "<5.4"}}, {"version": "v6.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "817535dbb1721df8b3a8f2489dc7e50bcd6209b5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/817535dbb1721df8b3a8f2489dc7e50bcd6209b5", "type": "zip", "shasum": "", "reference": "817535dbb1721df8b3a8f2489dc7e50bcd6209b5"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.8"}, "time": "2023-03-31T09:14:44+00:00"}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "90db1c6138c90527917671cd9ffa9e8b359e3a73"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/90db1c6138c90527917671cd9ffa9e8b359e3a73", "type": "zip", "shasum": "", "reference": "90db1c6138c90527917671cd9ffa9e8b359e3a73"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.7"}, "time": "2023-02-24T10:42:00+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "60556925a703cfbc1581cde3b3f35b0bb0ea904c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/60556925a703cfbc1581cde3b3f35b0bb0ea904c", "type": "zip", "shasum": "", "reference": "60556925a703cfbc1581cde3b3f35b0bb0ea904c"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.5"}, "time": "2023-01-05T07:00:27+00:00"}, {"version": "v6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a2a15404ef4c15d92c205718eb828b225a144379"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a2a15404ef4c15d92c205718eb828b225a144379", "type": "zip", "shasum": "", "reference": "a2a15404ef4c15d92c205718eb828b225a144379"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.3"}, "time": "2022-12-23T14:11:11+00:00"}, {"version": "v6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3294288c335b6267eab14964bf2c46015663d93f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3294288c335b6267eab14964bf2c46015663d93f", "type": "zip", "shasum": "", "reference": "3294288c335b6267eab14964bf2c46015663d93f"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.2"}, "time": "2022-12-13T18:04:17+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c08de62caead8357244efcb809d0b1a2584f2198"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c08de62caead8357244efcb809d0b1a2584f2198", "type": "zip", "shasum": "", "reference": "c08de62caead8357244efcb809d0b1a2584f2198"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.0"}, "time": "2022-11-02T09:08:04+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/translation/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/translation/tree/v6.2.0-BETA3"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2a32ad93ad16ffcd673f67fdd26ad04876d40222"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2a32ad93ad16ffcd673f67fdd26ad04876d40222", "type": "zip", "shasum": "", "reference": "2a32ad93ad16ffcd673f67fdd26ad04876d40222"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.2.0-BETA1"}, "time": "2022-10-20T10:15:36+00:00"}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e3d6773b18a5e32e80442cadc851bee8b1df72f4"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e3d6773b18a5e32e80442cadc851bee8b1df72f4", "type": "zip", "shasum": "", "reference": "e3d6773b18a5e32e80442cadc851bee8b1df72f4"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.11"}, "time": "2023-01-01T08:36:55+00:00", "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v6.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e6cd330e5a072518f88d65148f3f165541807494"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e6cd330e5a072518f88d65148f3f165541807494", "type": "zip", "shasum": "", "reference": "e6cd330e5a072518f88d65148f3f165541807494"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.6"}, "time": "2022-10-07T08:04:03+00:00"}, {"version": "v6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "45d0f5bb8df7255651ca91c122fab604e776af03"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/45d0f5bb8df7255651ca91c122fab604e776af03", "type": "zip", "shasum": "", "reference": "45d0f5bb8df7255651ca91c122fab604e776af03"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.4"}, "time": "2022-08-02T16:17:38+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b042e16087d298d08c1f013ff505d16c12a3b1be"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b042e16087d298d08c1f013ff505d16c12a3b1be", "type": "zip", "shasum": "", "reference": "b042e16087d298d08c1f013ff505d16c12a3b1be"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.3"}, "time": "2022-07-20T13:46:29+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b254416631615bc6fe49b0a67f18658827288147"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b254416631615bc6fe49b0a67f18658827288147", "type": "zip", "shasum": "", "reference": "b254416631615bc6fe49b0a67f18658827288147"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.0"}, "time": "2022-05-11T12:12:29+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/translation/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8f0598515cd7324fe1b3193f5a0ee4956538eb16"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8f0598515cd7324fe1b3193f5a0ee4956538eb16", "type": "zip", "shasum": "", "reference": "8f0598515cd7324fe1b3193f5a0ee4956538eb16"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.0-BETA2"}, "time": "2022-04-22T08:18:23+00:00"}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "94e782ec30c26f4ed777357b76069791e63df472"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/94e782ec30c26f4ed777357b76069791e63df472", "type": "zip", "shasum": "", "reference": "94e782ec30c26f4ed777357b76069791e63df472"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.1.0-BETA1"}, "time": "2022-04-14T13:14:59+00:00"}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "type": "zip", "shasum": "", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.19"}, "time": "2023-01-01T08:36:10+00:00", "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6f99eb179aee4652c0a7cd7c11f2a870d904330c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6f99eb179aee4652c0a7cd7c11f2a870d904330c", "type": "zip", "shasum": "", "reference": "6f99eb179aee4652c0a7cd7c11f2a870d904330c"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.14"}, "time": "2022-10-07T08:02:12+00:00"}, {"version": "v6.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5e71973b4991e141271465dacf4bf9e719941424"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5e71973b4991e141271465dacf4bf9e719941424", "type": "zip", "shasum": "", "reference": "5e71973b4991e141271465dacf4bf9e719941424"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.12"}, "time": "2022-08-02T16:01:06+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "55ffbe4b690156100af1ae42e1f94c5873085bca"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/55ffbe4b690156100af1ae42e1f94c5873085bca", "type": "zip", "shasum": "", "reference": "55ffbe4b690156100af1ae42e1f94c5873085bca"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.11"}, "time": "2022-07-20T13:45:53+00:00"}, {"version": "v6.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9ba011309943955a3807b8236c17cff3b88f67b6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9ba011309943955a3807b8236c17cff3b88f67b6", "type": "zip", "shasum": "", "reference": "9ba011309943955a3807b8236c17cff3b88f67b6"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.9"}, "time": "2022-05-06T14:27:17+00:00"}, {"version": "v6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3d38cf8f8834148c4457681d539bc204de701501"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3d38cf8f8834148c4457681d539bc204de701501", "type": "zip", "shasum": "", "reference": "3d38cf8f8834148c4457681d539bc204de701501"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.8"}, "time": "2022-04-22T08:18:02+00:00"}, {"version": "v6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b2792b39d74cf41ea3065f27fd2ddf0b556ac7a1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b2792b39d74cf41ea3065f27fd2ddf0b556ac7a1", "type": "zip", "shasum": "", "reference": "b2792b39d74cf41ea3065f27fd2ddf0b556ac7a1"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.7"}, "time": "2022-03-31T17:18:25+00:00"}, {"version": "v6.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f6639cb9b5e0c57fe31e3263b900a77eedb0c908"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f6639cb9b5e0c57fe31e3263b900a77eedb0c908", "type": "zip", "shasum": "", "reference": "f6639cb9b5e0c57fe31e3263b900a77eedb0c908"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.6"}, "time": "2022-03-02T12:58:14+00:00"}, {"version": "v6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e69501c71107cc3146b32aaa45f4edd0c3427875"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e69501c71107cc3146b32aaa45f4edd0c3427875", "type": "zip", "shasum": "", "reference": "e69501c71107cc3146b32aaa45f4edd0c3427875"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.5"}, "time": "2022-02-09T15:52:48+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "71bb15335798f8c4da110911bcf2d2fead7a430d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/71bb15335798f8c4da110911bcf2d2fead7a430d", "type": "zip", "shasum": "", "reference": "71bb15335798f8c4da110911bcf2d2fead7a430d"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.3"}, "time": "2022-01-07T00:29:03+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a16c33f93e2fd62d259222aebf792158e9a28a77"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a16c33f93e2fd62d259222aebf792158e9a28a77", "type": "zip", "shasum": "", "reference": "a16c33f93e2fd62d259222aebf792158e9a28a77"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.2"}, "time": "2021-12-25T20:10:03+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b7956e00c6e03546f2ba489fc50f7c47933e76b8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b7956e00c6e03546f2ba489fc50f7c47933e76b8", "type": "zip", "shasum": "", "reference": "b7956e00c6e03546f2ba489fc50f7c47933e76b8"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5e3848083ef1abc4814be154095946b8193f41d6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5e3848083ef1abc4814be154095946b8193f41d6", "type": "zip", "shasum": "", "reference": "5e3848083ef1abc4814be154095946b8193f41d6"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.0"}, "time": "2021-11-29T15:32:57+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1278da9345f4137a6c7f0af3c6d21563d6bad510"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1278da9345f4137a6c7f0af3c6d21563d6bad510", "type": "zip", "shasum": "", "reference": "1278da9345f4137a6c7f0af3c6d21563d6bad510"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.0-RC1"}, "time": "2021-11-23T19:05:29+00:00"}, {"version": "v6.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dcc3b6e7db2c91b633f0addb038f7dd11933d943"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dcc3b6e7db2c91b633f0addb038f7dd11933d943", "type": "zip", "shasum": "", "reference": "dcc3b6e7db2c91b633f0addb038f7dd11933d943"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.0-BETA2"}, "time": "2021-11-12T11:44:21+00:00", "require-dev": {"symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2.0|^3.0", "symfony/yaml": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e9044a64e680d0f5282c2a50264abc42555004c7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e9044a64e680d0f5282c2a50264abc42555004c7", "type": "zip", "shasum": "", "reference": "e9044a64e680d0f5282c2a50264abc42555004c7"}, "support": {"source": "https://github.com/symfony/translation/tree/v6.0.0-BETA1"}, "time": "2021-11-04T17:14:40+00:00"}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/98f26acc99341ca4bab345fb14d7b1d7cb825bed", "type": "zip", "shasum": "", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "require-dev": {"symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<4.4", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4", "symfony/console": "<5.3"}, "provide": {"symfony/translation-implementation": "2.3"}}, {"version": "v5.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6fed3a20b5b87ee9cdd9dacf545922b8fd475921"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6fed3a20b5b87ee9cdd9dacf545922b8fd475921", "type": "zip", "shasum": "", "reference": "6fed3a20b5b87ee9cdd9dacf545922b8fd475921"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.44"}, "time": "2024-09-15T08:12:35+00:00"}, {"version": "v5.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1d702caccb9f091b738696185f778b1bfef7b5b2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1d702caccb9f091b738696185f778b1bfef7b5b2", "type": "zip", "shasum": "", "reference": "1d702caccb9f091b738696185f778b1bfef7b5b2"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.42"}, "time": "2024-07-26T12:14:19+00:00"}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bb51d7f183756d1ac03f50ea47dc5726518cc7e8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bb51d7f183756d1ac03f50ea47dc5726518cc7e8", "type": "zip", "shasum": "", "reference": "bb51d7f183756d1ac03f50ea47dc5726518cc7e8"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0fabede35e3985c4f96089edeeefe8313e15ca3a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0fabede35e3985c4f96089edeeefe8313e15ca3a", "type": "zip", "shasum": "", "reference": "0fabede35e3985c4f96089edeeefe8313e15ca3a"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "77d7d1e46f52827585e65e6cd6f52a2542e59c72"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/77d7d1e46f52827585e65e6cd6f52a2542e59c72", "type": "zip", "shasum": "", "reference": "77d7d1e46f52827585e65e6cd6f52a2542e59c72"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.35"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v5.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ba72f72fceddf36f00bd495966b5873f2d17ad8f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ba72f72fceddf36f00bd495966b5873f2d17ad8f", "type": "zip", "shasum": "", "reference": "ba72f72fceddf36f00bd495966b5873f2d17ad8f"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.31"}, "time": "2023-11-03T16:16:43+00:00"}, {"version": "v5.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8560dc532e4e48d331937532a7cbfd2a9f9f53ce"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8560dc532e4e48d331937532a7cbfd2a9f9f53ce", "type": "zip", "shasum": "", "reference": "8560dc532e4e48d331937532a7cbfd2a9f9f53ce"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.30"}, "time": "2023-10-28T09:19:54+00:00"}, {"version": "v5.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "de237e59c5833422342be67402d487fbf50334ff"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/de237e59c5833422342be67402d487fbf50334ff", "type": "zip", "shasum": "", "reference": "de237e59c5833422342be67402d487fbf50334ff"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.24"}, "time": "2023-05-19T12:34:17+00:00"}, {"version": "v5.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9a401392f01bc385aa42760eff481d213a0cc2ba"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9a401392f01bc385aa42760eff481d213a0cc2ba", "type": "zip", "shasum": "", "reference": "9a401392f01bc385aa42760eff481d213a0cc2ba"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.22"}, "time": "2023-03-27T16:07:23+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6996affeea65705086939894b77110e9a7f80874"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6996affeea65705086939894b77110e9a7f80874", "type": "zip", "shasum": "", "reference": "6996affeea65705086939894b77110e9a7f80874"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.21"}, "time": "2023-02-21T19:46:44+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "83d487b13b7fb4c0a6ad079f4e4c9b4525e1b695"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/83d487b13b7fb4c0a6ad079f4e4c9b4525e1b695", "type": "zip", "shasum": "", "reference": "83d487b13b7fb4c0a6ad079f4e4c9b4525e1b695"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.19"}, "time": "2023-01-01T08:32:19+00:00"}, {"version": "v5.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f0ed07675863aa6e3939df8b1bc879450b585cab"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f0ed07675863aa6e3939df8b1bc879450b585cab", "type": "zip", "shasum": "", "reference": "f0ed07675863aa6e3939df8b1bc879450b585cab"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.14"}, "time": "2022-10-07T08:01:20+00:00"}, {"version": "v5.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "42ecc77eb4f229ce2df702a648ec93b8478d76ae"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/42ecc77eb4f229ce2df702a648ec93b8478d76ae", "type": "zip", "shasum": "", "reference": "42ecc77eb4f229ce2df702a648ec93b8478d76ae"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.12"}, "time": "2022-08-02T15:52:22+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7a1a8f6bbff269f434a83343a0a5d36a4f8cfa21"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7a1a8f6bbff269f434a83343a0a5d36a4f8cfa21", "type": "zip", "shasum": "", "reference": "7a1a8f6bbff269f434a83343a0a5d36a4f8cfa21"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.11"}, "time": "2022-07-20T13:00:38+00:00"}, {"version": "v5.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1639abc1177d26bcd4320e535e664cef067ab0ca"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1639abc1177d26bcd4320e535e664cef067ab0ca", "type": "zip", "shasum": "", "reference": "1639abc1177d26bcd4320e535e664cef067ab0ca"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.9"}, "time": "2022-05-06T12:33:37+00:00"}, {"version": "v5.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f5c0f6d1f20993b2606f3a5f36b1dc8c1899170b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f5c0f6d1f20993b2606f3a5f36b1dc8c1899170b", "type": "zip", "shasum": "", "reference": "f5c0f6d1f20993b2606f3a5f36b1dc8c1899170b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.8"}, "time": "2022-04-22T08:14:12+00:00"}, {"version": "v5.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e1eb790575202ee3ac2659f55b93b05853726f8e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e1eb790575202ee3ac2659f55b93b05853726f8e", "type": "zip", "shasum": "", "reference": "e1eb790575202ee3ac2659f55b93b05853726f8e"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.7"}, "time": "2022-03-24T17:09:09+00:00"}, {"version": "v5.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a7ca9fdfffb0174209440c2ffa1dee228e15d95b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a7ca9fdfffb0174209440c2ffa1dee228e15d95b", "type": "zip", "shasum": "", "reference": "a7ca9fdfffb0174209440c2ffa1dee228e15d95b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.6"}, "time": "2022-03-02T12:56:28+00:00"}, {"version": "v5.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7e4d52d39e5d86f3f04bef46fa29a1091786bc73"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7e4d52d39e5d86f3f04bef46fa29a1091786bc73", "type": "zip", "shasum": "", "reference": "7e4d52d39e5d86f3f04bef46fa29a1091786bc73"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.5"}, "time": "2022-02-09T15:49:12+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a9dd7403232c61e87e27fb306bbcd1627f245d70"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a9dd7403232c61e87e27fb306bbcd1627f245d70", "type": "zip", "shasum": "", "reference": "a9dd7403232c61e87e27fb306bbcd1627f245d70"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.3"}, "time": "2022-01-07T00:28:17+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ff8bb2107b6a549dc3c5dd9c498dcc82c9c098ca"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ff8bb2107b6a549dc3c5dd9c498dcc82c9c098ca", "type": "zip", "shasum": "", "reference": "ff8bb2107b6a549dc3c5dd9c498dcc82c9c098ca"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.2"}, "time": "2021-12-25T19:45:36+00:00"}, {"version": "v5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8c82cd35ed861236138d5ae1c78c0c7ebcd62107"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8c82cd35ed861236138d5ae1c78c0c7ebcd62107", "type": "zip", "shasum": "", "reference": "8c82cd35ed861236138d5ae1c78c0c7ebcd62107"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.1"}, "time": "2021-12-05T20:33:52+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6fe32b10e912a518805bc9eafc2a87145773cf13"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6fe32b10e912a518805bc9eafc2a87145773cf13", "type": "zip", "shasum": "", "reference": "6fe32b10e912a518805bc9eafc2a87145773cf13"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.0"}, "time": "2021-11-29T15:30:56+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a8d3ff260f433bd4ad5c13d909cbcffa4d59d64f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a8d3ff260f433bd4ad5c13d909cbcffa4d59d64f", "type": "zip", "shasum": "", "reference": "a8d3ff260f433bd4ad5c13d909cbcffa4d59d64f"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.0-RC1"}, "time": "2021-11-23T15:25:16+00:00"}, {"version": "v5.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "70f9cd07dceda1bcd30fa1065e4b394435d5d5c1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/70f9cd07dceda1bcd30fa1065e4b394435d5d5c1", "type": "zip", "shasum": "", "reference": "70f9cd07dceda1bcd30fa1065e4b394435d5d5c1"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.0-BETA2"}, "time": "2021-11-08T17:30:16+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "require-dev": {"symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "psr/log": "^1|^2|^3"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "10eef043e1243762ec3661e71aa5cee6a682c1f6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/10eef043e1243762ec3661e71aa5cee6a682c1f6", "type": "zip", "shasum": "", "reference": "10eef043e1243762ec3661e71aa5cee6a682c1f6"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.4.0-BETA1"}, "time": "2021-11-04T16:48:04+00:00"}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "945066809dc18f6e26123098e1b6e1d7a948660b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/945066809dc18f6e26123098e1b6e1d7a948660b", "type": "zip", "shasum": "", "reference": "945066809dc18f6e26123098e1b6e1d7a948660b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.14"}, "time": "2022-01-03T19:49:08+00:00", "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<4.4", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}}, {"version": "v5.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6ef0582a7a30141346ecc4541c9f61347b9f944b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6ef0582a7a30141346ecc4541c9f61347b9f944b", "type": "zip", "shasum": "", "reference": "6ef0582a7a30141346ecc4541c9f61347b9f944b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.13"}, "time": "2021-12-25T19:24:19+00:00"}, {"version": "v5.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "17a965c8f3b1b348cf15d903ac53942984561f8a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/17a965c8f3b1b348cf15d903ac53942984561f8a", "type": "zip", "shasum": "", "reference": "17a965c8f3b1b348cf15d903ac53942984561f8a"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.11"}, "time": "2021-11-04T16:37:19+00:00"}, {"version": "v5.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6ef197aea2ac8b9cd63e0da7522b3771714035aa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6ef197aea2ac8b9cd63e0da7522b3771714035aa", "type": "zip", "shasum": "", "reference": "6ef197aea2ac8b9cd63e0da7522b3771714035aa"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.10"}, "time": "2021-10-10T06:43:24+00:00"}, {"version": "v5.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6e69f3551c1a3356cf6ea8d019bf039a0f8b6886"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6e69f3551c1a3356cf6ea8d019bf039a0f8b6886", "type": "zip", "shasum": "", "reference": "6e69f3551c1a3356cf6ea8d019bf039a0f8b6886"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.9"}, "time": "2021-08-26T08:22:53+00:00"}, {"version": "v5.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4d595a6d15fd3a2c67f6f31d14d15d3b7356d7a6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4d595a6d15fd3a2c67f6f31d14d15d3b7356d7a6", "type": "zip", "shasum": "", "reference": "4d595a6d15fd3a2c67f6f31d14d15d3b7356d7a6"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.7"}}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d89ad7292932c2699cbe4af98d72c5c6bbc504c1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d89ad7292932c2699cbe4af98d72c5c6bbc504c1", "type": "zip", "shasum": "", "reference": "d89ad7292932c2699cbe4af98d72c5c6bbc504c1"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.4"}, "time": "2021-07-25T09:39:16+00:00"}, {"version": "v5.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "380b8c9e944d0e364b25f28e8e555241eb49c01c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/380b8c9e944d0e364b25f28e8e555241eb49c01c", "type": "zip", "shasum": "", "reference": "380b8c9e944d0e364b25f28e8e555241eb49c01c"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.3"}, "time": "2021-06-27T12:22:47+00:00", "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^2.3"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "psr/log": "~1.0"}}, {"version": "v5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7e2603bcc598e14804c4d2359d8dc4ee3c40391b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7e2603bcc598e14804c4d2359d8dc4ee3c40391b", "type": "zip", "shasum": "", "reference": "7e2603bcc598e14804c4d2359d8dc4ee3c40391b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.2"}, "time": "2021-06-06T09:51:56+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "251de0d921c42ef0a81494d8f37405421deefdf6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/251de0d921c42ef0a81494d8f37405421deefdf6", "type": "zip", "shasum": "", "reference": "251de0d921c42ef0a81494d8f37405421deefdf6"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.0"}, "time": "2021-05-29T22:28:28+00:00"}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f504ce568b6b950783ec08c56ea7ac510a548a47"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f504ce568b6b950783ec08c56ea7ac510a548a47", "type": "zip", "shasum": "", "reference": "f504ce568b6b950783ec08c56ea7ac510a548a47"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.0-RC1"}, "time": "2021-05-16T13:08:56+00:00"}, {"version": "v5.3.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e01b39785d7b14370b9b041e35d7d21560632be0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e01b39785d7b14370b9b041e35d7d21560632be0", "type": "zip", "shasum": "", "reference": "e01b39785d7b14370b9b041e35d7d21560632be0"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.0-BETA4"}, "time": "2021-05-12T09:33:38+00:00"}, {"version": "v5.3.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "97f0d2fc34e53a739ac4f9b887c1683cfba9985b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/97f0d2fc34e53a739ac4f9b887c1683cfba9985b", "type": "zip", "shasum": "", "reference": "97f0d2fc34e53a739ac4f9b887c1683cfba9985b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.0-BETA3"}, "time": "2021-05-09T15:52:59+00:00"}, {"version": "v5.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ec6ee6f48267cb3c84f4b1d9fd4f55c965e44873"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ec6ee6f48267cb3c84f4b1d9fd4f55c965e44873", "type": "zip", "shasum": "", "reference": "ec6ee6f48267cb3c84f4b1d9fd4f55c965e44873"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.0-BETA2"}, "time": "2021-04-28T06:48:47+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7b9e56fbc6bf7051fc5735a364de0c9968e26143"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7b9e56fbc6bf7051fc5735a364de0c9968e26143", "type": "zip", "shasum": "", "reference": "7b9e56fbc6bf7051fc5735a364de0c9968e26143"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.3.0-BETA1"}, "time": "2021-04-13T19:38:10+00:00", "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "psr/log": "~1.0"}}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7b4ef368b25bbb758a7bd5fe20349433e84220ed"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7b4ef368b25bbb758a7bd5fe20349433e84220ed", "type": "zip", "shasum": "", "reference": "7b4ef368b25bbb758a7bd5fe20349433e84220ed"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.12"}, "time": "2021-07-21T13:17:02+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "psr/log": "^1|^2|^3"}}, {"version": "v5.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ecf9ee05f5f099523cf60196c523de52aa8d45a0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ecf9ee05f5f099523cf60196c523de52aa8d45a0", "type": "zip", "shasum": "", "reference": "ecf9ee05f5f099523cf60196c523de52aa8d45a0"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.11"}, "time": "2021-06-06T09:50:27+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^2.3"}, "require-dev": {"symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "psr/log": "~1.0"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dc49cfb0d1d1bf6a9aaccccee570ef62e7b095c4"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dc49cfb0d1d1bf6a9aaccccee570ef62e7b095c4", "type": "zip", "shasum": "", "reference": "dc49cfb0d1d1bf6a9aaccccee570ef62e7b095c4"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.10"}, "time": "2021-05-26T17:40:38+00:00"}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "61af68dba333e2d376a325a29c2a3f2a605b4876"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/61af68dba333e2d376a325a29c2a3f2a605b4876", "type": "zip", "shasum": "", "reference": "61af68dba333e2d376a325a29c2a3f2a605b4876"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.9"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "445caa74a5986f1cc9dd91a2975ef68fa7cb2068"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/445caa74a5986f1cc9dd91a2975ef68fa7cb2068", "type": "zip", "shasum": "", "reference": "445caa74a5986f1cc9dd91a2975ef68fa7cb2068"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.8"}, "time": "2021-05-07T13:41:16+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e37ece5242564bceea54d709eafc948377ec9749"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e37ece5242564bceea54d709eafc948377ec9749", "type": "zip", "shasum": "", "reference": "e37ece5242564bceea54d709eafc948377ec9749"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.7"}, "time": "2021-04-01T08:15:21+00:00"}, {"version": "v5.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2cc7f45d96db9adfcf89adf4401d9dfed509f4e1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2cc7f45d96db9adfcf89adf4401d9dfed509f4e1", "type": "zip", "shasum": "", "reference": "2cc7f45d96db9adfcf89adf4401d9dfed509f4e1"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.6"}, "time": "2021-03-23T19:33:48+00:00"}, {"version": "v5.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0947ab1e3aabd22a6bef393874b2555d2bb976da"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0947ab1e3aabd22a6bef393874b2555d2bb976da", "type": "zip", "shasum": "", "reference": "0947ab1e3aabd22a6bef393874b2555d2bb976da"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.5"}, "time": "2021-03-06T07:59:01+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "74b0353ab34ff4cca827a2cf909e325d96815e60"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/74b0353ab34ff4cca827a2cf909e325d96815e60", "type": "zip", "shasum": "", "reference": "74b0353ab34ff4cca827a2cf909e325d96815e60"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.4"}, "time": "2021-03-04T15:41:09+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c021864d4354ee55160ddcfd31dc477a1bc77949"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c021864d4354ee55160ddcfd31dc477a1bc77949", "type": "zip", "shasum": "", "reference": "c021864d4354ee55160ddcfd31dc477a1bc77949"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.3"}, "time": "2021-01-27T10:15:41+00:00", "provide": {"symfony/translation-implementation": "2.0"}}, {"version": "v5.2.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/v5.2.2"}}, {"description": "Symfony Translation Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a04209ba0d1391c828e5b2373181dac63c52ee70"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a04209ba0d1391c828e5b2373181dac63c52ee70", "type": "zip", "shasum": "", "reference": "a04209ba0d1391c828e5b2373181dac63c52ee70"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.1"}, "time": "2020-12-08T17:03:37+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "52f486a707510884450df461b5a6429dd7a67379"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/52f486a707510884450df461b5a6429dd7a67379", "type": "zip", "shasum": "", "reference": "52f486a707510884450df461b5a6429dd7a67379"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.0"}, "time": "2020-11-28T11:24:18+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "331974aae89a1337fdde974339e822fb734a650e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/331974aae89a1337fdde974339e822fb734a650e", "type": "zip", "shasum": "", "reference": "331974aae89a1337fdde974339e822fb734a650e"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.0-RC2"}, "time": "2020-10-28T21:46:03+00:00"}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/translation/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bce604a985014f50f18322d147f1db3e2572ab4d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bce604a985014f50f18322d147f1db3e2572ab4d", "type": "zip", "shasum": "", "reference": "bce604a985014f50f18322d147f1db3e2572ab4d"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:08:07+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e96fb12fc77d8c868bca0bef364b863c115ce55b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e96fb12fc77d8c868bca0bef364b863c115ce55b", "type": "zip", "shasum": "", "reference": "e96fb12fc77d8c868bca0bef364b863c115ce55b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.0-BETA2"}, "time": "2020-10-14T17:08:19+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1ee329949a65f76c880a3c9c91eb9069b527a44d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1ee329949a65f76c880a3c9c91eb9069b527a44d", "type": "zip", "shasum": "", "reference": "1ee329949a65f76c880a3c9c91eb9069b527a44d"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.2.0-BETA1"}, "time": "2020-09-28T13:05:58+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Provides tools to internationalize your application", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b16d3e4b2d3f78fb2444aa8d19019f033e55ec56"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b16d3e4b2d3f78fb2444aa8d19019f033e55ec56", "type": "zip", "shasum": "", "reference": "b16d3e4b2d3f78fb2444aa8d19019f033e55ec56"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^2"}, "extra": "__unset"}, {"description": "Symfony Translation Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "38c6ca559703dd677db000181ef1839d7c7147de"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/38c6ca559703dd677db000181ef1839d7c7147de", "type": "zip", "shasum": "", "reference": "38c6ca559703dd677db000181ef1839d7c7147de"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.10"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b52e4184a38b69148a2b129c77cf47b8ce61d23f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b52e4184a38b69148a2b129c77cf47b8ce61d23f", "type": "zip", "shasum": "", "reference": "b52e4184a38b69148a2b129c77cf47b8ce61d23f"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.9"}, "time": "2020-11-28T10:57:20+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "27980838fd261e04379fa91e94e81e662fe5a1b6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/27980838fd261e04379fa91e94e81e662fe5a1b6", "type": "zip", "shasum": "", "reference": "27980838fd261e04379fa91e94e81e662fe5a1b6"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e3cdd5119b1b5bf0698c351b8ee20fb5a4ea248b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e3cdd5119b1b5bf0698c351b8ee20fb5a4ea248b", "type": "zip", "shasum": "", "reference": "e3cdd5119b1b5bf0698c351b8ee20fb5a4ea248b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.7"}, "time": "2020-09-27T03:44:28+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/v5.1.6"}}, {"version": "v5.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "917b02cdc5f33e0309b8e9d33ee1480b20687413"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/917b02cdc5f33e0309b8e9d33ee1480b20687413", "type": "zip", "shasum": "", "reference": "917b02cdc5f33e0309b8e9d33ee1480b20687413"}, "support": {"source": "https://github.com/symfony/translation/tree/5.1"}, "time": "2020-08-17T10:01:29+00:00"}, {"version": "v5.1.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/v5.1.4"}}, {"version": "v5.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4b9bf719f0fa5b05253c37fc7b335337ec7ec427"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4b9bf719f0fa5b05253c37fc7b335337ec7ec427", "type": "zip", "shasum": "", "reference": "4b9bf719f0fa5b05253c37fc7b335337ec7ec427"}, "support": {"source": "https://github.com/symfony/translation/tree/5.1"}, "time": "2020-06-30T17:42:22+00:00"}, {"version": "v5.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d387f07d4c15f9c09439cf3f13ddbe0b2c5e8be2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d387f07d4c15f9c09439cf3f13ddbe0b2c5e8be2", "type": "zip", "shasum": "", "reference": "d387f07d4c15f9c09439cf3f13ddbe0b2c5e8be2"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.1"}, "time": "2020-05-30T20:35:19+00:00"}, {"version": "v5.1.1", "version_normalized": "*******"}, {"version": "v5.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/5.1"}}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e6b22a37088755e57393a4d5574803184654163c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e6b22a37088755e57393a4d5574803184654163c", "type": "zip", "shasum": "", "reference": "e6b22a37088755e57393a4d5574803184654163c"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.0-RC2"}, "time": "2020-05-20T17:43:50+00:00"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "48808651825c3d2a2610a0f440718c692fbddde7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/48808651825c3d2a2610a0f440718c692fbddde7", "type": "zip", "shasum": "", "reference": "48808651825c3d2a2610a0f440718c692fbddde7"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.0-RC1"}, "time": "2020-05-16T09:12:54+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^2"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3a5c9dee4145da04b240da2df3d4a759619bc2be"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3a5c9dee4145da04b240da2df3d4a759619bc2be", "type": "zip", "shasum": "", "reference": "3a5c9dee4145da04b240da2df3d4a759619bc2be"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.1.0-BETA1"}, "time": "2020-05-04T15:54:21+00:00"}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "762090e92d8df2b91cace8930ce0329674600225"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/762090e92d8df2b91cace8930ce0329674600225", "type": "zip", "shasum": "", "reference": "762090e92d8df2b91cace8930ce0329674600225"}, "support": {"source": "https://github.com/symfony/translation/tree/5.0"}, "time": "2020-06-30T17:40:59+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2"}}, {"version": "v5.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d3262422559eef735b12ed4c7a2fbe8be3ba8898"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d3262422559eef735b12ed4c7a2fbe8be3ba8898", "type": "zip", "shasum": "", "reference": "d3262422559eef735b12ed4c7a2fbe8be3ba8898"}, "time": "2020-05-30T20:12:43+00:00"}, {"version": "v5.0.9", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/v5.0.9"}}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c3879db7a68fe3e12b41263b05879412c87b27fd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c3879db7a68fe3e12b41263b05879412c87b27fd", "type": "zip", "shasum": "", "reference": "c3879db7a68fe3e12b41263b05879412c87b27fd"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.0.8"}, "time": "2020-04-12T16:45:47+00:00", "require": {"php": "^7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2"}}, {"version": "v5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "99b831770e10807dca0979518e2c89edffef5978"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/99b831770e10807dca0979518e2c89edffef5978", "type": "zip", "shasum": "", "reference": "99b831770e10807dca0979518e2c89edffef5978"}, "support": {"source": "https://github.com/symfony/translation/tree/5.0"}, "time": "2020-03-27T16:56:45+00:00"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "91855689b557481ff2d3d97c535d74284e7b814a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/91855689b557481ff2d3d97c535d74284e7b814a", "type": "zip", "shasum": "", "reference": "91855689b557481ff2d3d97c535d74284e7b814a"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.0.6"}, "time": "2020-03-18T08:00:37+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e9b93f42a1fd6aec6a0872d59ee5c8219a7d584b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e9b93f42a1fd6aec6a0872d59ee5c8219a7d584b", "type": "zip", "shasum": "", "reference": "e9b93f42a1fd6aec6a0872d59ee5c8219a7d584b"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.0.5"}, "time": "2020-02-04T07:41:34+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "28e1054f1ea26c63762d9260c37cb1056ea62dbb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/28e1054f1ea26c63762d9260c37cb1056ea62dbb", "type": "zip", "shasum": "", "reference": "28e1054f1ea26c63762d9260c37cb1056ea62dbb"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.0.4"}, "time": "2020-01-21T08:40:24+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/5.0"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3ae6fad7a3dc2d99a023f9360184628fc44acbb3"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3ae6fad7a3dc2d99a023f9360184628fc44acbb3", "type": "zip", "shasum": "", "reference": "3ae6fad7a3dc2d99a023f9360184628fc44acbb3"}, "time": "2019-12-12T13:03:32+00:00"}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e86df1b0f1672362ecf96023faf2c42241c41330"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e86df1b0f1672362ecf96023faf2c42241c41330", "type": "zip", "shasum": "", "reference": "e86df1b0f1672362ecf96023faf2c42241c41330"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.0", "version_normalized": "*******"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "00a8ff72088be07a53bc2c136384d1b186392cd1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/00a8ff72088be07a53bc2c136384d1b186392cd1", "type": "zip", "shasum": "", "reference": "00a8ff72088be07a53bc2c136384d1b186392cd1"}, "support": {"source": "https://github.com/symfony/translation/tree/v5.0.0-RC1"}, "time": "2019-11-12T17:26:48+00:00", "require": {"php": "^7.2.9", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/translation/tree/master"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1"}, {"description": "Provides tools to internationalize your application", "version": "v4.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/45036b1d53accc48fe9bab71ccd86d57eba0dd94", "type": "zip", "shasum": "", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "require-dev": {"symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "extra": "__unset"}, {"version": "v4.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4e6b4c0dbeb04d6f004ed7f43eb0905ce8396def"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4e6b4c0dbeb04d6f004ed7f43eb0905ce8396def", "type": "zip", "shasum": "", "reference": "4e6b4c0dbeb04d6f004ed7f43eb0905ce8396def"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.45"}, "time": "2022-08-02T12:44:49+00:00"}, {"version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "af947fefc306cec6ea5a1f6160c7e305a71f2493"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/af947fefc306cec6ea5a1f6160c7e305a71f2493", "type": "zip", "shasum": "", "reference": "af947fefc306cec6ea5a1f6160c7e305a71f2493"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"version": "v4.4.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dcb67eae126e74507e0b4f0b9ac6ef35b37c3331"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dcb67eae126e74507e0b4f0b9ac6ef35b37c3331", "type": "zip", "shasum": "", "reference": "dcb67eae126e74507e0b4f0b9ac6ef35b37c3331"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.41"}, "time": "2022-04-21T07:22:34+00:00"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4ce00d6875230b839f5feef82e51971f6c886e00"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4ce00d6875230b839f5feef82e51971f6c886e00", "type": "zip", "shasum": "", "reference": "4ce00d6875230b839f5feef82e51971f6c886e00"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "26d330720627b234803595ecfc0191eeabc65190"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/26d330720627b234803595ecfc0191eeabc65190", "type": "zip", "shasum": "", "reference": "26d330720627b234803595ecfc0191eeabc65190"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.34"}, "time": "2021-11-04T12:23:33+00:00"}, {"version": "v4.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "db0ba1e85280d8ff11e38d53c70f8814d4d740f5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/db0ba1e85280d8ff11e38d53c70f8814d4d740f5", "type": "zip", "shasum": "", "reference": "db0ba1e85280d8ff11e38d53c70f8814d4d740f5"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.32"}, "time": "2021-08-26T05:57:13+00:00"}, {"version": "v4.4.30", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.30"}}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2e3c0f2bf704d635ba862e7198d72331a62d82ba"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2e3c0f2bf704d635ba862e7198d72331a62d82ba", "type": "zip", "shasum": "", "reference": "2e3c0f2bf704d635ba862e7198d72331a62d82ba"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.27"}, "time": "2021-07-21T13:12:00+00:00"}, {"version": "v4.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2f7fa60b8d10ca71c30dc46b0870143183a8f131"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2f7fa60b8d10ca71c30dc46b0870143183a8f131", "type": "zip", "shasum": "", "reference": "2f7fa60b8d10ca71c30dc46b0870143183a8f131"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.26"}, "time": "2021-06-06T08:51:46+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "require-dev": {"symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "psr/log": "~1.0"}}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dfe132c5c6d89f90ce7f961742cc532e9ca16dd4"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dfe132c5c6d89f90ce7f961742cc532e9ca16dd4", "type": "zip", "shasum": "", "reference": "dfe132c5c6d89f90ce7f961742cc532e9ca16dd4"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.25"}, "time": "2021-05-26T17:39:37+00:00"}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "424d29dfcc15575af05196de0100d7b52f650602"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/424d29dfcc15575af05196de0100d7b52f650602", "type": "zip", "shasum": "", "reference": "424d29dfcc15575af05196de0100d7b52f650602"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.24"}, "time": "2021-05-16T09:52:47+00:00"}, {"version": "v4.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ff6e63c7b5de874464642969968f61f8dc649ac3"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ff6e63c7b5de874464642969968f61f8dc649ac3", "type": "zip", "shasum": "", "reference": "ff6e63c7b5de874464642969968f61f8dc649ac3"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.23"}, "time": "2021-04-28T06:59:52+00:00"}, {"version": "v4.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "eb8f5428cc3b40d6dffe303b195b084f1c5fbd14"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/eb8f5428cc3b40d6dffe303b195b084f1c5fbd14", "type": "zip", "shasum": "", "reference": "eb8f5428cc3b40d6dffe303b195b084f1c5fbd14"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.21"}, "time": "2021-03-23T16:25:01+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2271b6d577018a7dea75a9162a08ac84f8234deb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2271b6d577018a7dea75a9162a08ac84f8234deb", "type": "zip", "shasum": "", "reference": "2271b6d577018a7dea75a9162a08ac84f8234deb"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.20"}, "time": "2021-02-26T13:53:48+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e1d0c67167a553556d9f974b5fa79c2448df317a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e1d0c67167a553556d9f974b5fa79c2448df317a", "type": "zip", "shasum": "", "reference": "e1d0c67167a553556d9f974b5fa79c2448df317a"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.19"}, "time": "2021-01-27T09:09:26+00:00", "provide": {"symfony/translation-implementation": "1.0"}}, {"description": "Symfony Translation Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c1001b7d75b3136648f94b245588209d881c6939"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c1001b7d75b3136648f94b245588209d881c6939", "type": "zip", "shasum": "", "reference": "c1001b7d75b3136648f94b245588209d881c6939"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.18"}, "time": "2020-12-08T16:59:59+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "84821e6a14a637e817f25d11147388695b6f790a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/84821e6a14a637e817f25d11147388695b6f790a", "type": "zip", "shasum": "", "reference": "84821e6a14a637e817f25d11147388695b6f790a"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.17"}, "time": "2020-11-27T06:35:49+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "73095716af79f610f3b6338b911357393fdd10ab"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/73095716af79f610f3b6338b911357393fdd10ab", "type": "zip", "shasum": "", "reference": "73095716af79f610f3b6338b911357393fdd10ab"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8494fa1bbf9d77fe1e7d50ac8ccfb80a858a98bd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8494fa1bbf9d77fe1e7d50ac8ccfb80a858a98bd", "type": "zip", "shasum": "", "reference": "8494fa1bbf9d77fe1e7d50ac8ccfb80a858a98bd"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.15"}, "time": "2020-10-02T07:34:48+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0b8c4bb49b05b11d2b9dd1732f26049b08d96884"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0b8c4bb49b05b11d2b9dd1732f26049b08d96884", "type": "zip", "shasum": "", "reference": "0b8c4bb49b05b11d2b9dd1732f26049b08d96884"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.14"}, "time": "2020-09-24T09:40:01+00:00"}, {"version": "v4.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "700e6e50174b0cdcf0fa232773bec5c314680575"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/700e6e50174b0cdcf0fa232773bec5c314680575", "type": "zip", "shasum": "", "reference": "700e6e50174b0cdcf0fa232773bec5c314680575"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.12"}, "time": "2020-08-17T09:56:45+00:00"}, {"version": "v4.4.12", "version_normalized": "********"}, {"version": "v4.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a8ea9d97353294eb6783f2894ef8cee99a045822"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a8ea9d97353294eb6783f2894ef8cee99a045822", "type": "zip", "shasum": "", "reference": "a8ea9d97353294eb6783f2894ef8cee99a045822"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.11"}, "time": "2020-07-23T08:31:43+00:00"}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "79d3ef9096a6a6047dbc69218b68c7b7f63193af"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/79d3ef9096a6a6047dbc69218b68c7b7f63193af", "type": "zip", "shasum": "", "reference": "79d3ef9096a6a6047dbc69218b68c7b7f63193af"}, "support": {"source": "https://github.com/symfony/translation/tree/4.4"}, "time": "2020-05-30T20:06:45+00:00"}, {"version": "v4.4.9", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.9"}}, {"version": "v4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8272bbd2b7e220ef812eba2a2b30068a5c64b191"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8272bbd2b7e220ef812eba2a2b30068a5c64b191", "type": "zip", "shasum": "", "reference": "8272bbd2b7e220ef812eba2a2b30068a5c64b191"}, "support": {"source": "https://github.com/symfony/translation/tree/4.4"}, "time": "2020-04-12T16:45:36+00:00", "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}}, {"version": "v4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4e54d336f2eca5facad449d0b0118bb449375b76"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4e54d336f2eca5facad449d0b0118bb449375b76", "type": "zip", "shasum": "", "reference": "4e54d336f2eca5facad449d0b0118bb449375b76"}, "time": "2020-03-27T16:54:36+00:00"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6617bb1548cec764770b719e317299a0270f4c5f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6617bb1548cec764770b719e317299a0270f4c5f", "type": "zip", "shasum": "", "reference": "6617bb1548cec764770b719e317299a0270f4c5f"}, "time": "2020-03-17T19:51:46+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0a19a77fba20818a969ef03fdaf1602de0546353"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0a19a77fba20818a969ef03fdaf1602de0546353", "type": "zip", "shasum": "", "reference": "0a19a77fba20818a969ef03fdaf1602de0546353"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.5"}, "time": "2020-02-04T09:32:40+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f5d2ac46930238b30a9c2f1b17c905f3697d808c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f5d2ac46930238b30a9c2f1b17c905f3697d808c", "type": "zip", "shasum": "", "reference": "f5d2ac46930238b30a9c2f1b17c905f3697d808c"}, "support": {"source": "https://github.com/symfony/translation/tree/4.4"}, "time": "2020-01-15T13:29:06+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.3"}}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f7669f48a9633bf8139bc026c755e894b7206677"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f7669f48a9633bf8139bc026c755e894b7206677", "type": "zip", "shasum": "", "reference": "f7669f48a9633bf8139bc026c755e894b7206677"}, "support": {"source": "https://github.com/symfony/translation/tree/4.4"}, "time": "2019-12-12T12:53:52+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "897fb68ee7933372517b551d6f08c6d4bb0b8c40"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/897fb68ee7933372517b551d6f08c6d4bb0b8c40", "type": "zip", "shasum": "", "reference": "897fb68ee7933372517b551d6f08c6d4bb0b8c40"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.4.0-BETA1"}, "time": "2019-11-12T17:18:47+00:00"}, {"version": "v4.4.0", "version_normalized": "*******"}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "46e462be71935ae15eab531e4d491d801857f24c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/46e462be71935ae15eab531e4d491d801857f24c", "type": "zip", "shasum": "", "reference": "46e462be71935ae15eab531e4d491d801857f24c"}, "support": {"source": "https://github.com/symfony/translation/tree/4.3"}, "time": "2020-01-04T12:24:57+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6"}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/service-contracts": "^1.1.2", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}}, {"version": "v4.3.10", "version_normalized": "********"}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "73f86a49454d9477864ccbb6c06993e24a052a48"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/73f86a49454d9477864ccbb6c06993e24a052a48", "type": "zip", "shasum": "", "reference": "73f86a49454d9477864ccbb6c06993e24a052a48"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.3.9"}, "time": "2019-11-26T03:44:44+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bbce239b35b0cd47bd75848b23e969f17dd970e7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bbce239b35b0cd47bd75848b23e969f17dd970e7", "type": "zip", "shasum": "", "reference": "bbce239b35b0cd47bd75848b23e969f17dd970e7"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.3.7"}, "time": "2019-11-06T23:21:49+00:00"}, {"version": "v4.3.7", "version_normalized": "*******"}, {"version": "v4.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a3aa590ce944afb3434d7a55f81b00927144d5ec"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a3aa590ce944afb3434d7a55f81b00927144d5ec", "type": "zip", "shasum": "", "reference": "a3aa590ce944afb3434d7a55f81b00927144d5ec"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.3.6"}, "time": "2019-10-30T12:53:54+00:00"}, {"version": "v4.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "fe6193b066c457c144333c06aaa869a2d42a167f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/fe6193b066c457c144333c06aaa869a2d42a167f", "type": "zip", "shasum": "", "reference": "fe6193b066c457c144333c06aaa869a2d42a167f"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.3.5"}, "time": "2019-09-27T14:37:39+00:00"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "28498169dd334095fa981827992f3a24d50fed0f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/28498169dd334095fa981827992f3a24d50fed0f", "type": "zip", "shasum": "", "reference": "28498169dd334095fa981827992f3a24d50fed0f"}, "support": {"source": "https://github.com/symfony/translation/tree/4.3"}, "time": "2019-08-26T08:55:16+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4e3e39cc485304f807622bdc64938e4633396406"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4e3e39cc485304f807622bdc64938e4633396406", "type": "zip", "shasum": "", "reference": "4e3e39cc485304f807622bdc64938e4633396406"}, "time": "2019-07-18T10:34:59+00:00", "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.2"}}, {"version": "v4.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "934ab1d18545149e012aa898cf02e9f23790f7a0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/934ab1d18545149e012aa898cf02e9f23790f7a0", "type": "zip", "shasum": "", "reference": "934ab1d18545149e012aa898cf02e9f23790f7a0"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.3.2"}, "time": "2019-06-13T11:03:18+00:00"}, {"version": "v4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5dda505e5f65d759741dfaf4e54b36010a4b57aa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5dda505e5f65d759741dfaf4e54b36010a4b57aa", "type": "zip", "shasum": "", "reference": "5dda505e5f65d759741dfaf4e54b36010a4b57aa"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.3.1"}, "time": "2019-06-03T20:27:40+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f7e90f0f90e354f99f20aa9ead95a1b201b126f9"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f7e90f0f90e354f99f20aa9ead95a1b201b126f9", "type": "zip", "shasum": "", "reference": "f7e90f0f90e354f99f20aa9ead95a1b201b126f9"}, "support": {"source": "https://github.com/symfony/translation/tree/4.3"}, "time": "2019-05-28T09:09:27+00:00"}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/translation/tree/v4.3.0-RC1"}}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3b1842537242d5856928e9e2a6b797c87f0495a6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3b1842537242d5856928e9e2a6b797c87f0495a6", "type": "zip", "shasum": "", "reference": "3b1842537242d5856928e9e2a6b797c87f0495a6"}, "support": {"source": "https://github.com/symfony/translation/tree/4.3"}, "time": "2019-05-09T09:33:12+00:00", "require": {"php": "^7.1.3", "symfony/contracts": "^1.0.2", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "db5a2d8c6cfec010c9ef58c20d58bea6e3fd5d69"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/db5a2d8c6cfec010c9ef58c20d58bea6e3fd5d69", "type": "zip", "shasum": "", "reference": "db5a2d8c6cfec010c9ef58c20d58bea6e3fd5d69"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2019-05-08T20:09:06+00:00", "provide": {"symfony/translation-contracts-implementation": "1.0"}}, {"version": "v4.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4b84015894d980745b510ba90492722cafe2f90f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4b84015894d980745b510ba90492722cafe2f90f", "type": "zip", "shasum": "", "reference": "4b84015894d980745b510ba90492722cafe2f90f"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.2.12"}, "time": "2019-07-18T10:29:22+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "require": {"php": "^7.1.3", "symfony/contracts": "^1.1.1", "symfony/polyfill-mbstring": "~1.0"}, "provide": {"symfony/translation-implementation": "1.0"}}, {"version": "v4.2.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/4.2"}}, {"version": "v4.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9b7a706c4cb0cd52063374fb63d133166e50aaa6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9b7a706c4cb0cd52063374fb63d133166e50aaa6", "type": "zip", "shasum": "", "reference": "9b7a706c4cb0cd52063374fb63d133166e50aaa6"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.2.10"}, "time": "2019-06-13T10:57:15+00:00"}, {"version": "v4.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5fe4ec5ecc04fa43c34f8df033bf60e3729bfaee"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5fe4ec5ecc04fa43c34f8df033bf60e3729bfaee", "type": "zip", "shasum": "", "reference": "5fe4ec5ecc04fa43c34f8df033bf60e3729bfaee"}, "support": {"source": "https://github.com/symfony/translation/tree/4.2"}, "time": "2019-05-28T09:07:12+00:00"}, {"version": "v4.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "181a426dd129cb496f12d7e7555f6d0b37a7615b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/181a426dd129cb496f12d7e7555f6d0b37a7615b", "type": "zip", "shasum": "", "reference": "181a426dd129cb496f12d7e7555f6d0b37a7615b"}, "time": "2019-05-01T12:55:36+00:00", "require": {"php": "^7.1.3", "symfony/contracts": "^1.0.2", "symfony/polyfill-mbstring": "~1.0"}, "provide": {"symfony/translation-contracts-implementation": "1.0"}}, {"version": "v4.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "46c0dede1f925383d13dc783857be2c41efd0b24"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/46c0dede1f925383d13dc783857be2c41efd0b24", "type": "zip", "shasum": "", "reference": "46c0dede1f925383d13dc783857be2c41efd0b24"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.2.7"}, "time": "2019-04-10T16:20:36+00:00"}, {"version": "v4.2.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/translation/tree/4.2"}}, {"version": "v4.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e46933cc31b68f51f7fc5470fb55550407520f56"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e46933cc31b68f51f7fc5470fb55550407520f56", "type": "zip", "shasum": "", "reference": "e46933cc31b68f51f7fc5470fb55550407520f56"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.2.5"}, "time": "2019-04-01T14:13:08+00:00", "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}}, {"version": "v4.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "748464177a77011f8f4cdd076773862ce4915f8f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/748464177a77011f8f4cdd076773862ce4915f8f", "type": "zip", "shasum": "", "reference": "748464177a77011f8f4cdd076773862ce4915f8f"}, "support": {"source": "https://github.com/symfony/translation/tree/4.2"}, "time": "2019-02-27T03:31:50+00:00"}, {"version": "v4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "23fd7aac70d99a17a8e6473a41fec8fab3331050"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/23fd7aac70d99a17a8e6473a41fec8fab3331050", "type": "zip", "shasum": "", "reference": "23fd7aac70d99a17a8e6473a41fec8fab3331050"}, "time": "2019-01-27T23:11:39+00:00"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "939fb792d73f2ce80e6ae9019d205fc480f1c9a0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/939fb792d73f2ce80e6ae9019d205fc480f1c9a0", "type": "zip", "shasum": "", "reference": "939fb792d73f2ce80e6ae9019d205fc480f1c9a0"}, "time": "2019-01-03T09:07:35+00:00"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c0e2191e9bed845946ab3d99767513b56ca7dcd6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c0e2191e9bed845946ab3d99767513b56ca7dcd6", "type": "zip", "shasum": "", "reference": "c0e2191e9bed845946ab3d99767513b56ca7dcd6"}, "time": "2018-12-06T10:45:32+00:00"}, {"version": "v4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ff9a878c9b8f8bcd4d9138e2d32f508c942773d9"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ff9a878c9b8f8bcd4d9138e2d32f508c942773d9", "type": "zip", "shasum": "", "reference": "ff9a878c9b8f8bcd4d9138e2d32f508c942773d9"}, "time": "2018-11-27T07:20:32+00:00", "require": {"php": "^7.1.3", "symfony/contracts": "^1.0", "symfony/polyfill-mbstring": "~1.0"}}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "cd54ae94dd2d159903485c7894c3426042708e40"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/cd54ae94dd2d159903485c7894c3426042708e40", "type": "zip", "shasum": "", "reference": "cd54ae94dd2d159903485c7894c3426042708e40"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2018-11-26T10:55:26+00:00"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f393c2ed9e42907ea7ccd4d8f2c55b5eb30bd0c2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f393c2ed9e42907ea7ccd4d8f2c55b5eb30bd0c2", "type": "zip", "shasum": "", "reference": "f393c2ed9e42907ea7ccd4d8f2c55b5eb30bd0c2"}, "time": "2018-11-14T16:25:09+00:00"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "35acf59e7dafd3ac919e16babaae0b6378fab564"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/35acf59e7dafd3ac919e16babaae0b6378fab564", "type": "zip", "shasum": "", "reference": "35acf59e7dafd3ac919e16babaae0b6378fab564"}, "time": "2018-10-31T10:56:31+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "347b9f093a2554ce3174ae56cc25a21381352c76"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/347b9f093a2554ce3174ae56cc25a21381352c76", "type": "zip", "shasum": "", "reference": "347b9f093a2554ce3174ae56cc25a21381352c76"}, "support": {"source": "https://github.com/symfony/translation/tree/v4.1.12"}, "time": "2019-01-25T14:34:37+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0"}, "provide": "__unset"}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d012dfeda5c55decc59c0c1d84dc6070dd354223"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d012dfeda5c55decc59c0c1d84dc6070dd354223", "type": "zip", "shasum": "", "reference": "d012dfeda5c55decc59c0c1d84dc6070dd354223"}, "time": "2019-01-03T09:05:57+00:00"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "615e3cf75d00a7d6788316d9631957991ba9c26a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/615e3cf75d00a7d6788316d9631957991ba9c26a", "type": "zip", "shasum": "", "reference": "615e3cf75d00a7d6788316d9631957991ba9c26a"}, "time": "2018-11-26T10:26:29+00:00"}, {"version": "v4.1.8", "version_normalized": "*******"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "aa04dc1c75b7d3da7bd7003104cd0cfc5dff635c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/aa04dc1c75b7d3da7bd7003104cd0cfc5dff635c", "type": "zip", "shasum": "", "reference": "aa04dc1c75b7d3da7bd7003104cd0cfc5dff635c"}, "time": "2018-10-28T18:38:52+00:00"}, {"version": "v4.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9f0b61e339160a466ebcde167a6c5521c810e304"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9f0b61e339160a466ebcde167a6c5521c810e304", "type": "zip", "shasum": "", "reference": "9f0b61e339160a466ebcde167a6c5521c810e304"}, "time": "2018-10-02T16:36:10+00:00"}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6e49130ddf150b7bfe9e34edb2f3f698aa1aa43b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6e49130ddf150b7bfe9e34edb2f3f698aa1aa43b", "type": "zip", "shasum": "", "reference": "6e49130ddf150b7bfe9e34edb2f3f698aa1aa43b"}, "time": "2018-09-21T12:49:42+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "fa2182669f7983b7aa5f1a770d053f79f0ef144f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/fa2182669f7983b7aa5f1a770d053f79f0ef144f", "type": "zip", "shasum": "", "reference": "fa2182669f7983b7aa5f1a770d053f79f0ef144f"}, "time": "2018-08-07T12:45:11+00:00"}, {"version": "v4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6fcd1bd44fd6d7181e6ea57a6f4e08a09b29ef65"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6fcd1bd44fd6d7181e6ea57a6f4e08a09b29ef65", "type": "zip", "shasum": "", "reference": "6fcd1bd44fd6d7181e6ea57a6f4e08a09b29ef65"}, "time": "2018-07-26T11:24:31+00:00"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2dd74d6b2dcbd46a93971e6ce7d245cf3123e957"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2dd74d6b2dcbd46a93971e6ce7d245cf3123e957", "type": "zip", "shasum": "", "reference": "2dd74d6b2dcbd46a93971e6ce7d245cf3123e957"}, "time": "2018-07-23T08:20:20+00:00"}, {"version": "v4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b6d8164085ee0b6debcd1b7a131fd6f63bb04854"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b6d8164085ee0b6debcd1b7a131fd6f63bb04854", "type": "zip", "shasum": "", "reference": "b6d8164085ee0b6debcd1b7a131fd6f63bb04854"}, "time": "2018-06-22T08:59:39+00:00"}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "16328f5b217cebc8dd4adfe4aeeaa8c377581f5a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/16328f5b217cebc8dd4adfe4aeeaa8c377581f5a", "type": "zip", "shasum": "", "reference": "16328f5b217cebc8dd4adfe4aeeaa8c377581f5a"}, "time": "2018-05-30T07:26:09+00:00"}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "66dfa87fdb1fe656c0c62a2f0eb7870a6d420269"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/66dfa87fdb1fe656c0c62a2f0eb7870a6d420269", "type": "zip", "shasum": "", "reference": "66dfa87fdb1fe656c0c62a2f0eb7870a6d420269"}, "time": "2018-05-21T10:10:11+00:00"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "209116aee94057df79751bc878e3cc6e8be37884"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/209116aee94057df79751bc878e3cc6e8be37884", "type": "zip", "shasum": "", "reference": "209116aee94057df79751bc878e3cc6e8be37884"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2018-05-07T07:14:12+00:00"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0ed31cf4b37326ddfaa4a18a8e3d1edcae09aab5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0ed31cf4b37326ddfaa4a18a8e3d1edcae09aab5", "type": "zip", "shasum": "", "reference": "0ed31cf4b37326ddfaa4a18a8e3d1edcae09aab5"}, "support": {"source": "https://github.com/symfony/translation/tree/4.0"}, "time": "2018-07-26T11:22:46+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require-dev": {"symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9280af93b9877cbfb18acbb95be552df4f4714bc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9280af93b9877cbfb18acbb95be552df4f4714bc", "type": "zip", "shasum": "", "reference": "9280af93b9877cbfb18acbb95be552df4f4714bc"}, "time": "2018-07-23T08:19:18+00:00"}, {"version": "v4.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "45020729139b2f5e181a6284af08c6b633f01ffc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/45020729139b2f5e181a6284af08c6b633f01ffc", "type": "zip", "shasum": "", "reference": "45020729139b2f5e181a6284af08c6b633f01ffc"}, "time": "2018-06-22T06:03:15+00:00"}, {"version": "v4.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e1f5863d0a9e79cfec7f031421ced3fe1d403e66"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e1f5863d0a9e79cfec7f031421ced3fe1d403e66", "type": "zip", "shasum": "", "reference": "e1f5863d0a9e79cfec7f031421ced3fe1d403e66"}, "time": "2018-05-21T10:09:47+00:00"}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ad3abf08eb3450491d8d76513100ef58194cd13e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ad3abf08eb3450491d8d76513100ef58194cd13e", "type": "zip", "shasum": "", "reference": "ad3abf08eb3450491d8d76513100ef58194cd13e"}, "time": "2018-04-30T01:23:47+00:00"}, {"version": "v4.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e20a9b7f9f62cb33a11638b345c248e7d510c938"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e20a9b7f9f62cb33a11638b345c248e7d510c938", "type": "zip", "shasum": "", "reference": "e20a9b7f9f62cb33a11638b345c248e7d510c938"}, "time": "2018-02-22T10:50:29+00:00", "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v4.0.7", "version_normalized": "*******"}, {"version": "v4.0.6", "version_normalized": "*******"}, {"version": "v4.0.5", "version_normalized": "*******"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a9c4e4cc56f7eff0960c4f6c157da8f6b13211fc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a9c4e4cc56f7eff0960c4f6c157da8f6b13211fc", "type": "zip", "shasum": "", "reference": "a9c4e4cc56f7eff0960c4f6c157da8f6b13211fc"}, "time": "2018-01-18T22:19:33+00:00"}, {"version": "v4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2bb1b9dac38d32c5afb00edc9371b5db4cf6d000"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2bb1b9dac38d32c5afb00edc9371b5db4cf6d000", "type": "zip", "shasum": "", "reference": "2bb1b9dac38d32c5afb00edc9371b5db4cf6d000"}, "time": "2018-01-03T07:38:00+00:00"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e2678768d2fcb7fe3d8108392626de1ece575634"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e2678768d2fcb7fe3d8108392626de1ece575634", "type": "zip", "shasum": "", "reference": "e2678768d2fcb7fe3d8108392626de1ece575634"}, "time": "2017-12-12T08:41:51+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "317c2002180f64292ed127bbe31dcf47594c6ed7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/317c2002180f64292ed127bbe31dcf47594c6ed7", "type": "zip", "shasum": "", "reference": "317c2002180f64292ed127bbe31dcf47594c6ed7"}, "time": "2017-11-28T22:05:27+00:00"}, {"version": "v4.0.0", "version_normalized": "*******"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "62c2022e74fafdb60b28c48435ad9c1b6f95e679"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/62c2022e74fafdb60b28c48435ad9c1b6f95e679", "type": "zip", "shasum": "", "reference": "62c2022e74fafdb60b28c48435ad9c1b6f95e679"}, "time": "2017-11-24T14:34:08+00:00"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6826748b10976339b62b04e9df00eacad95dc5ee"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6826748b10976339b62b04e9df00eacad95dc5ee", "type": "zip", "shasum": "", "reference": "6826748b10976339b62b04e9df00eacad95dc5ee"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2017-11-07T14:45:01+00:00"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7beac86f46d28608e021764721c47da219a6bd8a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7beac86f46d28608e021764721c47da219a6bd8a", "type": "zip", "shasum": "", "reference": "7beac86f46d28608e021764721c47da219a6bd8a"}, "time": "2017-11-05T16:26:21+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2a07082fb26bec15455cf3e872d9d39f4b5ce2fe"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2a07082fb26bec15455cf3e872d9d39f4b5ce2fe", "type": "zip", "shasum": "", "reference": "2a07082fb26bec15455cf3e872d9d39f4b5ce2fe"}, "time": "2017-10-24T14:16:56+00:00"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8f121e5668ba6b2e955d26064b7949ace06cad84"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8f121e5668ba6b2e955d26064b7949ace06cad84", "type": "zip", "shasum": "", "reference": "8f121e5668ba6b2e955d26064b7949ace06cad84"}, "time": "2017-10-13T13:37:53+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "be83ee6c065cb32becdb306ba61160d598b1ce88"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/be83ee6c065cb32becdb306ba61160d598b1ce88", "type": "zip", "shasum": "", "reference": "be83ee6c065cb32becdb306ba61160d598b1ce88"}, "support": {"source": "https://github.com/symfony/translation/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00", "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/v3.4.46"}}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c826cb2216d1627d1882e212d2ac3ac13d8d5b78"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c826cb2216d1627d1882e212d2ac3ac13d8d5b78", "type": "zip", "shasum": "", "reference": "c826cb2216d1627d1882e212d2ac3ac13d8d5b78"}, "support": {"source": "https://github.com/symfony/translation/tree/3.4"}, "time": "2020-09-02T16:06:40+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0411f10409ca8a23b94613bb2008017f387bacf0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0411f10409ca8a23b94613bb2008017f387bacf0", "type": "zip", "shasum": "", "reference": "0411f10409ca8a23b94613bb2008017f387bacf0"}, "support": {"source": "https://github.com/symfony/translation/tree/v3.4.44"}, "time": "2020-08-10T07:13:15+00:00"}, {"version": "v3.4.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "600b84224bf482441cd4d0026eba78755d2e2b34"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/600b84224bf482441cd4d0026eba78755d2e2b34", "type": "zip", "shasum": "", "reference": "600b84224bf482441cd4d0026eba78755d2e2b34"}, "support": {"source": "https://github.com/symfony/translation/tree/3.4"}, "time": "2020-07-16T09:41:49+00:00"}, {"version": "v3.4.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b0cd62ef0ff7ec31b67d78d7fc818e2bda4e844f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b0cd62ef0ff7ec31b67d78d7fc818e2bda4e844f", "type": "zip", "shasum": "", "reference": "b0cd62ef0ff7ec31b67d78d7fc818e2bda4e844f"}, "time": "2020-05-30T18:58:05+00:00"}, {"version": "v3.4.41", "version_normalized": "********"}, {"version": "v3.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4e844362f573713e6d45949795c95a4cb6cf760d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4e844362f573713e6d45949795c95a4cb6cf760d", "type": "zip", "shasum": "", "reference": "4e844362f573713e6d45949795c95a4cb6cf760d"}, "time": "2020-04-12T16:39:58+00:00"}, {"version": "v3.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e06ca83b2682eba25854b97a8a9af22c1da491f5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e06ca83b2682eba25854b97a8a9af22c1da491f5", "type": "zip", "shasum": "", "reference": "e06ca83b2682eba25854b97a8a9af22c1da491f5"}, "time": "2020-03-16T08:31:04+00:00"}, {"version": "v3.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1eb074e0bd94939a30dd14dbecf7a92b165cea34"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1eb074e0bd94939a30dd14dbecf7a92b165cea34", "type": "zip", "shasum": "", "reference": "1eb074e0bd94939a30dd14dbecf7a92b165cea34"}, "support": {"source": "https://github.com/symfony/translation/tree/v3.4.38"}, "time": "2020-02-04T07:22:30+00:00"}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "577ec9ba1d6443947c48058acc3de298ad25e2bf"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/577ec9ba1d6443947c48058acc3de298ad25e2bf", "type": "zip", "shasum": "", "reference": "577ec9ba1d6443947c48058acc3de298ad25e2bf"}, "support": {"source": "https://github.com/symfony/translation/tree/3.4"}, "time": "2020-01-04T12:05:51+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0be25347c4a8695d9423fe897f4c774f46e97b51"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0be25347c4a8695d9423fe897f4c774f46e97b51", "type": "zip", "shasum": "", "reference": "0be25347c4a8695d9423fe897f4c774f46e97b51"}, "time": "2019-11-23T20:30:33+00:00"}, {"version": "v3.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2031c895bc97ac1787d418d90bd1ed7d299f2772"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2031c895bc97ac1787d418d90bd1ed7d299f2772", "type": "zip", "shasum": "", "reference": "2031c895bc97ac1787d418d90bd1ed7d299f2772"}, "support": {"source": "https://github.com/symfony/translation/tree/v3.4.34"}, "time": "2019-10-30T12:43:22+00:00"}, {"version": "v3.4.34", "version_normalized": "********"}, {"version": "v3.4.33", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/v3.4.33"}}, {"version": "v3.4.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dd313664be0588560acacb252543b585f5408547"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dd313664be0588560acacb252543b585f5408547", "type": "zip", "shasum": "", "reference": "dd313664be0588560acacb252543b585f5408547"}, "support": {"source": "https://github.com/symfony/translation/tree/v3.4.32"}, "time": "2019-09-27T05:57:25+00:00"}, {"version": "v3.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "49a884e9ac297f99c56052bad30b2af89f716ee1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/49a884e9ac297f99c56052bad30b2af89f716ee1", "type": "zip", "shasum": "", "reference": "49a884e9ac297f99c56052bad30b2af89f716ee1"}, "support": {"source": "https://github.com/symfony/translation/tree/3.4"}, "time": "2019-08-26T07:52:58+00:00"}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2c1d800807cfaf5a2c72102f3a7452cd28a12cc0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2c1d800807cfaf5a2c72102f3a7452cd28a12cc0", "type": "zip", "shasum": "", "reference": "2c1d800807cfaf5a2c72102f3a7452cd28a12cc0"}, "time": "2019-07-15T07:11:40+00:00"}, {"version": "v3.4.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5c07632afb8cb14b422051b651213ed17bf7c249"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5c07632afb8cb14b422051b651213ed17bf7c249", "type": "zip", "shasum": "", "reference": "5c07632afb8cb14b422051b651213ed17bf7c249"}, "time": "2019-06-13T10:34:15+00:00"}, {"version": "v3.4.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "301a5d627220a1c4ee522813b0028653af6c4f54"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/301a5d627220a1c4ee522813b0028653af6c4f54", "type": "zip", "shasum": "", "reference": "301a5d627220a1c4ee522813b0028653af6c4f54"}, "time": "2019-05-01T11:10:09+00:00"}, {"version": "v3.4.27", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/v3.4.27"}}, {"version": "v3.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "aae26f143da71adc8707eb489f1dc86aef7d376b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/aae26f143da71adc8707eb489f1dc86aef7d376b", "type": "zip", "shasum": "", "reference": "aae26f143da71adc8707eb489f1dc86aef7d376b"}, "support": {"source": "https://github.com/symfony/translation/tree/v3.4.26"}, "time": "2019-04-10T16:00:48+00:00"}, {"version": "v3.4.25", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/3.4"}}, {"version": "v3.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "629ac01240f6ebc253a621e19b84e329fe19a987"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/629ac01240f6ebc253a621e19b84e329fe19a987", "type": "zip", "shasum": "", "reference": "629ac01240f6ebc253a621e19b84e329fe19a987"}, "time": "2019-03-25T07:48:46+00:00", "require-dev": {"symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/yaml": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "psr/log": "~1.0"}}, {"version": "v3.4.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3e2966209567ffed8825905b53fc8548446130aa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3e2966209567ffed8825905b53fc8548446130aa", "type": "zip", "shasum": "", "reference": "3e2966209567ffed8825905b53fc8548446130aa"}, "time": "2019-02-23T15:06:07+00:00"}, {"version": "v3.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "81cfcd6935cb7505640153576c1f9155b2a179c1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/81cfcd6935cb7505640153576c1f9155b2a179c1", "type": "zip", "shasum": "", "reference": "81cfcd6935cb7505640153576c1f9155b2a179c1"}, "time": "2019-01-25T10:00:44+00:00"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5f357063f4907cef47e5cf82fa3187fbfb700456"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5f357063f4907cef47e5cf82fa3187fbfb700456", "type": "zip", "shasum": "", "reference": "5f357063f4907cef47e5cf82fa3187fbfb700456"}, "time": "2019-01-01T13:45:19+00:00"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bdbe940ed3ef4179f86032086c32d3a858becc0f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bdbe940ed3ef4179f86032086c32d3a858becc0f", "type": "zip", "shasum": "", "reference": "bdbe940ed3ef4179f86032086c32d3a858becc0f"}, "time": "2018-11-26T10:17:44+00:00"}, {"version": "v3.4.19", "version_normalized": "********"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "94bc3a79008e6640defedf5e14eb3b4f20048352"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/94bc3a79008e6640defedf5e14eb3b4f20048352", "type": "zip", "shasum": "", "reference": "94bc3a79008e6640defedf5e14eb3b4f20048352"}, "time": "2018-10-02T16:33:53+00:00"}, {"version": "v3.4.17", "version_normalized": "********"}, {"version": "v3.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "97e07ec91584eca4e9a96d3eb5aadd55844c55a8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/97e07ec91584eca4e9a96d3eb5aadd55844c55a8", "type": "zip", "shasum": "", "reference": "97e07ec91584eca4e9a96d3eb5aadd55844c55a8"}, "time": "2018-09-21T12:47:54+00:00"}, {"version": "v3.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9749930bfc825139aadd2d28461ddbaed6577862"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9749930bfc825139aadd2d28461ddbaed6577862", "type": "zip", "shasum": "", "reference": "9749930bfc825139aadd2d28461ddbaed6577862"}, "time": "2018-07-26T11:19:56+00:00"}, {"version": "v3.4.14", "version_normalized": "********"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0d1c0965c3e82250b9786909cf584fd4a50c9c5b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0d1c0965c3e82250b9786909cf584fd4a50c9c5b", "type": "zip", "shasum": "", "reference": "0d1c0965c3e82250b9786909cf584fd4a50c9c5b"}, "time": "2018-07-23T08:18:36+00:00"}, {"version": "v3.4.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7047f725e35eab768137c677f8c38e4a2a8e38fb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7047f725e35eab768137c677f8c38e4a2a8e38fb", "type": "zip", "shasum": "", "reference": "7047f725e35eab768137c677f8c38e4a2a8e38fb"}, "time": "2018-05-21T10:06:52+00:00"}, {"version": "v3.4.11", "version_normalized": "********"}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d4af50f46cd8171fd5c1cdebdb9a8bbcd8078c6c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d4af50f46cd8171fd5c1cdebdb9a8bbcd8078c6c", "type": "zip", "shasum": "", "reference": "d4af50f46cd8171fd5c1cdebdb9a8bbcd8078c6c"}, "time": "2018-04-30T01:22:56+00:00"}, {"version": "v3.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "80e19eaf12cbb546ac40384e5c55c36306823e57"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/80e19eaf12cbb546ac40384e5c55c36306823e57", "type": "zip", "shasum": "", "reference": "80e19eaf12cbb546ac40384e5c55c36306823e57"}, "time": "2018-02-22T06:28:18+00:00", "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v3.4.7", "version_normalized": "*******"}, {"version": "v3.4.6", "version_normalized": "*******"}, {"version": "v3.4.5", "version_normalized": "*******"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "10b32cf0eae28b9b39fe26c456c42b19854c4b84"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/10b32cf0eae28b9b39fe26c456c42b19854c4b84", "type": "zip", "shasum": "", "reference": "10b32cf0eae28b9b39fe26c456c42b19854c4b84"}, "time": "2018-01-18T22:16:57+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "17b5962d252b2d6d1d37a2485ebb7ddc5b2bef0a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/17b5962d252b2d6d1d37a2485ebb7ddc5b2bef0a", "type": "zip", "shasum": "", "reference": "17b5962d252b2d6d1d37a2485ebb7ddc5b2bef0a"}, "time": "2018-01-03T07:37:34+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4c5d5582baf2829751a5207659329c1f52eedeb6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4c5d5582baf2829751a5207659329c1f52eedeb6", "type": "zip", "shasum": "", "reference": "4c5d5582baf2829751a5207659329c1f52eedeb6"}, "time": "2017-12-12T08:27:14+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e05b0a5996ad7a35ba3a19ffad8b72c9daa64dfa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e05b0a5996ad7a35ba3a19ffad8b72c9daa64dfa", "type": "zip", "shasum": "", "reference": "e05b0a5996ad7a35ba3a19ffad8b72c9daa64dfa"}, "time": "2017-11-27T14:23:00+00:00"}, {"version": "v3.4.0", "version_normalized": "*******"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4bf804fe2477173ce2a1c9d09e716e266518d3f5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4bf804fe2477173ce2a1c9d09e716e266518d3f5", "type": "zip", "shasum": "", "reference": "4bf804fe2477173ce2a1c9d09e716e266518d3f5"}, "time": "2017-11-22T18:43:08+00:00"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "34c2376013f9e151b2e7f935a77e42586b28d8c6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/34c2376013f9e151b2e7f935a77e42586b28d8c6", "type": "zip", "shasum": "", "reference": "34c2376013f9e151b2e7f935a77e42586b28d8c6"}, "time": "2017-11-07T14:20:24+00:00"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7f7a9a29817e938fd397372d6d5a44031789c79c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7f7a9a29817e938fd397372d6d5a44031789c79c", "type": "zip", "shasum": "", "reference": "7f7a9a29817e938fd397372d6d5a44031789c79c"}, "time": "2017-11-05T16:10:10+00:00"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e7bfada027b7c17e973c80f5f789f91f896e42f8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e7bfada027b7c17e973c80f5f789f91f896e42f8", "type": "zip", "shasum": "", "reference": "e7bfada027b7c17e973c80f5f789f91f896e42f8"}, "time": "2017-10-24T14:12:06+00:00"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "24110f6b3f3fe5f9caa2a65bd3b8cba5f25be185"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/24110f6b3f3fe5f9caa2a65bd3b8cba5f25be185", "type": "zip", "shasum": "", "reference": "24110f6b3f3fe5f9caa2a65bd3b8cba5f25be185"}, "time": "2017-10-13T13:33:47+00:00"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "90cb5ca3eb84b3053fef876e11e405fd819487fc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/90cb5ca3eb84b3053fef876e11e405fd819487fc", "type": "zip", "shasum": "", "reference": "90cb5ca3eb84b3053fef876e11e405fd819487fc"}, "support": {"source": "https://github.com/symfony/translation/tree/3.3"}, "time": "2018-01-18T14:19:00+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "require-dev": {"symfony/config": "~2.8|~3.0", "symfony/intl": "^2.8.18|^3.2.5", "symfony/yaml": "~3.3", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/yaml": "<3.3"}}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "96be707e96ba9ac04964d8f556d0fbb33329411c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/96be707e96ba9ac04964d8f556d0fbb33329411c", "type": "zip", "shasum": "", "reference": "96be707e96ba9ac04964d8f556d0fbb33329411c"}, "time": "2018-01-03T07:37:11+00:00"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "373e553477e55cd08f8b86b74db766c75b987fdb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/373e553477e55cd08f8b86b74db766c75b987fdb", "type": "zip", "shasum": "", "reference": "373e553477e55cd08f8b86b74db766c75b987fdb"}, "time": "2017-11-07T14:12:55+00:00"}, {"version": "v3.3.13", "version_normalized": "********"}, {"version": "v3.3.12", "version_normalized": "********"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "409bf229cd552bf7e3faa8ab7e3980b07672073f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/409bf229cd552bf7e3faa8ab7e3980b07672073f", "type": "zip", "shasum": "", "reference": "409bf229cd552bf7e3faa8ab7e3980b07672073f"}, "time": "2017-10-02T06:42:24+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "add53753d978f635492dfe8cd6953f6a7361ef90"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/add53753d978f635492dfe8cd6953f6a7361ef90", "type": "zip", "shasum": "", "reference": "add53753d978f635492dfe8cd6953f6a7361ef90"}, "time": "2017-07-29T21:54:42+00:00"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3", "type": "zip", "shasum": "", "reference": "35dd5fb003c90e8bd4d8cabdf94bf9c96d06fdc3"}, "time": "2017-06-24T16:45:30+00:00", "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0"}}, {"version": "v3.3.5", "version_normalized": "*******"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dc3b2a0c6cfff60327ba1c043a82092735397543"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dc3b2a0c6cfff60327ba1c043a82092735397543", "type": "zip", "shasum": "", "reference": "dc3b2a0c6cfff60327ba1c043a82092735397543"}, "time": "2017-05-22T07:42:36+00:00"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******"}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2a6f7a97421875ea66ace2fc943c2303d4d2289d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2a6f7a97421875ea66ace2fc943c2303d4d2289d", "type": "zip", "shasum": "", "reference": "2a6f7a97421875ea66ace2fc943c2303d4d2289d"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2017-04-12T14:14:56+00:00"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "df36a48672b929bf3995eb62c58d83004b1d0d50"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/df36a48672b929bf3995eb62c58d83004b1d0d50", "type": "zip", "shasum": "", "reference": "df36a48672b929bf3995eb62c58d83004b1d0d50"}, "support": {"source": "https://github.com/symfony/translation/tree/3.2"}, "time": "2017-06-24T16:45:17+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "require-dev": {"symfony/config": "~2.8|~3.0", "symfony/intl": "^2.8.18|^3.2.5", "symfony/yaml": "~2.8|~3.0", "psr/log": "~1.0"}, "conflict": {"symfony/config": "<2.8"}}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f4a04d2df710f81515df576b2de06bdeee518b83"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f4a04d2df710f81515df576b2de06bdeee518b83", "type": "zip", "shasum": "", "reference": "f4a04d2df710f81515df576b2de06bdeee518b83"}, "time": "2017-04-12T14:13:17+00:00"}, {"version": "v3.2.8", "version_normalized": "*******"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c740eee70783d2af4d3d6b70d5146f209e6b4d13"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c740eee70783d2af4d3d6b70d5146f209e6b4d13", "type": "zip", "shasum": "", "reference": "c740eee70783d2af4d3d6b70d5146f209e6b4d13"}, "time": "2017-03-21T21:44:32+00:00"}, {"version": "v3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0e1b15ce8fbf3890f4ccdac430ed5e07fdfe0690"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0e1b15ce8fbf3890f4ccdac430ed5e07fdfe0690", "type": "zip", "shasum": "", "reference": "0e1b15ce8fbf3890f4ccdac430ed5e07fdfe0690"}, "time": "2017-03-04T12:23:14+00:00"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d6825c6bb2f1da13f564678f9f236fe8242c0029"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d6825c6bb2f1da13f564678f9f236fe8242c0029", "type": "zip", "shasum": "", "reference": "d6825c6bb2f1da13f564678f9f236fe8242c0029"}, "time": "2017-02-16T22:46:52+00:00", "require-dev": {"symfony/config": "~2.8|~3.0", "symfony/intl": "~2.8|~3.0", "symfony/yaml": "~2.8|~3.0", "psr/log": "~1.0"}}, {"version": "v3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ca032cc56976d88b85e7386b17020bc6dc95dbc5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ca032cc56976d88b85e7386b17020bc6dc95dbc5", "type": "zip", "shasum": "", "reference": "ca032cc56976d88b85e7386b17020bc6dc95dbc5"}, "time": "2017-01-21T17:06:35+00:00"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6520f3d4cce604d9dd1e86cac7af954984dd9bda"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6520f3d4cce604d9dd1e86cac7af954984dd9bda", "type": "zip", "shasum": "", "reference": "6520f3d4cce604d9dd1e86cac7af954984dd9bda"}, "time": "2017-01-02T20:32:22+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5fd18eca88f4d187807a1eba083bc99feaa8635b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5fd18eca88f4d187807a1eba083bc99feaa8635b", "type": "zip", "shasum": "", "reference": "5fd18eca88f4d187807a1eba083bc99feaa8635b"}, "time": "2016-11-30T14:40:17+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "64ab6fc0b42e5386631f408e6adcaaff8eee5ba1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/64ab6fc0b42e5386631f408e6adcaaff8eee5ba1", "type": "zip", "shasum": "", "reference": "64ab6fc0b42e5386631f408e6adcaaff8eee5ba1"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2016-11-18T21:17:59+00:00"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3c31334b26de2028db66d185705e314feef8e2a0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3c31334b26de2028db66d185705e314feef8e2a0", "type": "zip", "shasum": "", "reference": "3c31334b26de2028db66d185705e314feef8e2a0"}, "time": "2016-11-14T16:20:13+00:00"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7f401e7c2a499dfb6bbfac25c76f614e3ed4904b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7f401e7c2a499dfb6bbfac25c76f614e3ed4904b", "type": "zip", "shasum": "", "reference": "7f401e7c2a499dfb6bbfac25c76f614e3ed4904b"}, "time": "2016-10-18T04:30:21+00:00"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d5a20fab5f63f44c233c69b3041c3cb1d4945e45"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d5a20fab5f63f44c233c69b3041c3cb1d4945e45", "type": "zip", "shasum": "", "reference": "d5a20fab5f63f44c233c69b3041c3cb1d4945e45"}, "support": {"source": "https://github.com/symfony/translation/tree/3.1"}, "time": "2017-01-21T17:01:39+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}, {"version": "v3.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7882149d1e1fd46d960f3e42344c9caf2e535573"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7882149d1e1fd46d960f3e42344c9caf2e535573", "type": "zip", "shasum": "", "reference": "7882149d1e1fd46d960f3e42344c9caf2e535573"}, "time": "2017-01-02T20:31:54+00:00"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2f4b6114b75c506dd1ee7eb485b35facbcb2d873"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2f4b6114b75c506dd1ee7eb485b35facbcb2d873", "type": "zip", "shasum": "", "reference": "2f4b6114b75c506dd1ee7eb485b35facbcb2d873"}, "time": "2016-11-18T21:15:08+00:00"}, {"version": "v3.1.7", "version_normalized": "*******"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ff1285087397d2f64041b35e591f3025881c90cd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ff1285087397d2f64041b35e591f3025881c90cd", "type": "zip", "shasum": "", "reference": "ff1285087397d2f64041b35e591f3025881c90cd"}, "time": "2016-10-18T04:30:12+00:00"}, {"version": "v3.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "93013a18d272e59dab8e67f583155b78c68947eb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/93013a18d272e59dab8e67f583155b78c68947eb", "type": "zip", "shasum": "", "reference": "93013a18d272e59dab8e67f583155b78c68947eb"}, "time": "2016-09-06T11:02:40+00:00"}, {"version": "v3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a35edc277513c9bc0f063ca174c36b346f974528"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a35edc277513c9bc0f063ca174c36b346f974528", "type": "zip", "shasum": "", "reference": "a35edc277513c9bc0f063ca174c36b346f974528"}, "time": "2016-08-05T08:37:39+00:00"}, {"version": "v3.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7713ddf81518d0823b027fe74ec390b80f6b6536"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7713ddf81518d0823b027fe74ec390b80f6b6536", "type": "zip", "shasum": "", "reference": "7713ddf81518d0823b027fe74ec390b80f6b6536"}, "time": "2016-07-26T08:04:17+00:00"}, {"version": "v3.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d63a94528530c3ea5ff46924c8001cec4a398609"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d63a94528530c3ea5ff46924c8001cec4a398609", "type": "zip", "shasum": "", "reference": "d63a94528530c3ea5ff46924c8001cec4a398609"}, "time": "2016-06-29T05:41:56+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b36e79d7bbbfa4a7e9708335082b3eba2263d356"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b36e79d7bbbfa4a7e9708335082b3eba2263d356", "type": "zip", "shasum": "", "reference": "b36e79d7bbbfa4a7e9708335082b3eba2263d356"}, "time": "2016-06-14T11:18:07+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0e67031c9ef0ad681932d2b24f4fd0ec8a6679ff"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0e67031c9ef0ad681932d2b24f4fd0ec8a6679ff", "type": "zip", "shasum": "", "reference": "0e67031c9ef0ad681932d2b24f4fd0ec8a6679ff"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2016-05-12T18:19:29+00:00"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "eee6c664853fd0576f21ae25725cfffeafe83f26"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/eee6c664853fd0576f21ae25725cfffeafe83f26", "type": "zip", "shasum": "", "reference": "eee6c664853fd0576f21ae25725cfffeafe83f26"}, "support": {"source": "https://github.com/symfony/translation/tree/3.0"}, "time": "2016-07-30T07:22:48+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6bf844e1ee3c820c012386c10427a5c67bbefec8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6bf844e1ee3c820c012386c10427a5c67bbefec8", "type": "zip", "shasum": "", "reference": "6bf844e1ee3c820c012386c10427a5c67bbefec8"}, "time": "2016-06-29T05:40:00+00:00"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2b0aacaa613c0ec1ad8046f972d8abdcb19c1db7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2b0aacaa613c0ec1ad8046f972d8abdcb19c1db7", "type": "zip", "shasum": "", "reference": "2b0aacaa613c0ec1ad8046f972d8abdcb19c1db7"}, "time": "2016-06-06T11:33:26+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f7a07af51ea067745a521dab1e3152044a2fb1f2"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f7a07af51ea067745a521dab1e3152044a2fb1f2", "type": "zip", "shasum": "", "reference": "f7a07af51ea067745a521dab1e3152044a2fb1f2"}, "time": "2016-03-25T01:41:20+00:00"}, {"version": "v3.0.5", "version_normalized": "*******"}, {"version": "v3.0.4", "version_normalized": "*******"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2de0b6f7ebe43cffd8a06996ebec6aab79ea9e91"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2de0b6f7ebe43cffd8a06996ebec6aab79ea9e91", "type": "zip", "shasum": "", "reference": "2de0b6f7ebe43cffd8a06996ebec6aab79ea9e91"}, "time": "2016-02-02T13:44:19+00:00"}, {"version": "v3.0.2", "version_normalized": "*******"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dff0867826a7068d673801b7522f8e2634016ef9"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dff0867826a7068d673801b7522f8e2634016ef9", "type": "zip", "shasum": "", "reference": "dff0867826a7068d673801b7522f8e2634016ef9"}, "time": "2015-12-05T17:45:07+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7f14717150a7445f8475864d1235875dd04061fb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7f14717150a7445f8475864d1235875dd04061fb", "type": "zip", "shasum": "", "reference": "7f14717150a7445f8475864d1235875dd04061fb"}, "support": {"source": "https://github.com/symfony/translation/tree/master"}, "time": "2015-11-18T13:48:51+00:00"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6c78cad7c0b39a88931e00737d973175f69680cd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6c78cad7c0b39a88931e00737d973175f69680cd", "type": "zip", "shasum": "", "reference": "6c78cad7c0b39a88931e00737d973175f69680cd"}, "time": "2015-10-30T23:35:59+00:00"}, {"version": "v2.8.52", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "fc58c2a19e56c29f5ba2736ec40d0119a0de2089"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/fc58c2a19e56c29f5ba2736ec40d0119a0de2089", "type": "zip", "shasum": "", "reference": "fc58c2a19e56c29f5ba2736ec40d0119a0de2089"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.8.50"}, "time": "2018-11-24T21:16:41+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/config": "~2.8", "symfony/intl": "~2.7.25|^2.8.18|~3.2.5", "symfony/yaml": "~2.2|~3.0.0", "psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "conflict": {"symfony/config": "<2.7"}}, {"version": "v2.8.50", "version_normalized": "********"}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "cb34ec9549ab65f7f512819441f2d6af4d12c294"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/cb34ec9549ab65f7f512819441f2d6af4d12c294", "type": "zip", "shasum": "", "reference": "cb34ec9549ab65f7f512819441f2d6af4d12c294"}, "time": "2018-10-02T16:27:16+00:00"}, {"version": "v2.8.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "59b4debd89c156fd753343fcc1ca36aa5bc2d0f4"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/59b4debd89c156fd753343fcc1ca36aa5bc2d0f4", "type": "zip", "shasum": "", "reference": "59b4debd89c156fd753343fcc1ca36aa5bc2d0f4"}, "time": "2018-09-21T12:46:38+00:00"}, {"version": "v2.8.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "12ad0a708ec55fb80cac9e809c5b56ddd5417d6a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/12ad0a708ec55fb80cac9e809c5b56ddd5417d6a", "type": "zip", "shasum": "", "reference": "12ad0a708ec55fb80cac9e809c5b56ddd5417d6a"}, "time": "2018-07-26T11:13:39+00:00"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "968379d57b9b9ba96796ef257825bf4621ac6fd8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/968379d57b9b9ba96796ef257825bf4621ac6fd8", "type": "zip", "shasum": "", "reference": "968379d57b9b9ba96796ef257825bf4621ac6fd8"}, "time": "2018-07-19T12:06:28+00:00"}, {"version": "v2.8.42", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c6a27966a92fa361bf2c3a938abc6dee91f7ad67"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c6a27966a92fa361bf2c3a938abc6dee91f7ad67", "type": "zip", "shasum": "", "reference": "c6a27966a92fa361bf2c3a938abc6dee91f7ad67"}, "time": "2018-05-21T09:59:10+00:00"}, {"version": "v2.8.41", "version_normalized": "********"}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6f744108f846097cb250d90c8c463ddd23b43f60"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6f744108f846097cb250d90c8c463ddd23b43f60", "type": "zip", "shasum": "", "reference": "6f744108f846097cb250d90c8c463ddd23b43f60"}, "time": "2018-04-30T01:21:07+00:00"}, {"version": "v2.8.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "72235c1121ae282254e897e342c001f3d4bb7e8d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/72235c1121ae282254e897e342c001f3d4bb7e8d", "type": "zip", "shasum": "", "reference": "72235c1121ae282254e897e342c001f3d4bb7e8d"}, "time": "2018-01-18T13:56:23+00:00", "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v2.8.37", "version_normalized": "********"}, {"version": "v2.8.36", "version_normalized": "********"}, {"version": "v2.8.35", "version_normalized": "********"}, {"version": "v2.8.34", "version_normalized": "********"}, {"version": "v2.8.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a1623f94ec61090fded29ade24e14ce4f4e5878b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a1623f94ec61090fded29ade24e14ce4f4e5878b", "type": "zip", "shasum": "", "reference": "a1623f94ec61090fded29ade24e14ce4f4e5878b"}, "time": "2018-01-03T07:36:31+00:00"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0c63d56516c4c4c323228ca6348eadb7c91b1daf"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0c63d56516c4c4c323228ca6348eadb7c91b1daf", "type": "zip", "shasum": "", "reference": "0c63d56516c4c4c323228ca6348eadb7c91b1daf"}, "time": "2017-11-07T14:08:47+00:00"}, {"version": "v2.8.31", "version_normalized": "********"}, {"version": "v2.8.30", "version_normalized": "********"}, {"version": "v2.8.29", "version_normalized": "********"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1248f84f702c4490adb526856e9356e52aa991be"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1248f84f702c4490adb526856e9356e52aa991be", "type": "zip", "shasum": "", "reference": "1248f84f702c4490adb526856e9356e52aa991be"}, "time": "2017-10-01T21:00:16+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a89af885b8c6d0142c79a02ca9cc059ed25d40d8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a89af885b8c6d0142c79a02ca9cc059ed25d40d8", "type": "zip", "shasum": "", "reference": "a89af885b8c6d0142c79a02ca9cc059ed25d40d8"}, "time": "2017-06-24T16:44:49+00:00"}, {"version": "v2.8.26", "version_normalized": "********"}, {"version": "v2.8.25", "version_normalized": "********"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "14db4cc1172a722aaa3b558bfa8eff593b43cd46"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/14db4cc1172a722aaa3b558bfa8eff593b43cd46", "type": "zip", "shasum": "", "reference": "14db4cc1172a722aaa3b558bfa8eff593b43cd46"}, "time": "2017-06-01T20:52:29+00:00"}, {"version": "v2.8.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "32b7c0bffc07772cf1a902e3464ef77117fa07c7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/32b7c0bffc07772cf1a902e3464ef77117fa07c7", "type": "zip", "shasum": "", "reference": "32b7c0bffc07772cf1a902e3464ef77117fa07c7"}, "time": "2017-04-12T14:07:15+00:00"}, {"version": "v2.8.20", "version_normalized": "********"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "047e97a64d609778cadfc76e3a09793696bb19f1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/047e97a64d609778cadfc76e3a09793696bb19f1", "type": "zip", "shasum": "", "reference": "047e97a64d609778cadfc76e3a09793696bb19f1"}, "time": "2017-03-21T21:39:01+00:00"}, {"version": "v2.8.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b538355bc99db2ec7cc35284ec76d92ae7d1d256"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b538355bc99db2ec7cc35284ec76d92ae7d1d256", "type": "zip", "shasum": "", "reference": "b538355bc99db2ec7cc35284ec76d92ae7d1d256"}, "time": "2017-03-04T12:20:59+00:00"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c281ac2b484210bb95106bdb8ae8356e63277725"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c281ac2b484210bb95106bdb8ae8356e63277725", "type": "zip", "shasum": "", "reference": "c281ac2b484210bb95106bdb8ae8356e63277725"}, "time": "2017-01-21T16:59:38+00:00", "require-dev": {"symfony/config": "~2.8", "symfony/intl": "~2.4|~3.0.0", "symfony/yaml": "~2.2|~3.0.0", "psr/log": "~1.0"}}, {"version": "v2.8.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b4ac4a393f6970cc157fba17be537380de396a86"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b4ac4a393f6970cc157fba17be537380de396a86", "type": "zip", "shasum": "", "reference": "b4ac4a393f6970cc157fba17be537380de396a86"}, "time": "2017-01-02T20:30:24+00:00"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "edbe67e8f729885b55421d5cc58223d48540df07"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/edbe67e8f729885b55421d5cc58223d48540df07", "type": "zip", "shasum": "", "reference": "edbe67e8f729885b55421d5cc58223d48540df07"}, "time": "2016-11-18T21:10:01+00:00"}, {"version": "v2.8.14", "version_normalized": "********"}, {"version": "v2.8.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "cca6ff892355876534b01a927f789bac9601c935"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/cca6ff892355876534b01a927f789bac9601c935", "type": "zip", "shasum": "", "reference": "cca6ff892355876534b01a927f789bac9601c935"}, "time": "2016-10-18T04:28:30+00:00"}, {"version": "v2.8.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bf0ff95faa9b6c0708efc1986255e3608d0ed3c7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bf0ff95faa9b6c0708efc1986255e3608d0ed3c7", "type": "zip", "shasum": "", "reference": "bf0ff95faa9b6c0708efc1986255e3608d0ed3c7"}, "time": "2016-09-06T10:55:00+00:00"}, {"version": "v2.8.11", "version_normalized": "********"}, {"version": "v2.8.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "32b0c824da6df065f43b0c458dc505940e98a7f1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/32b0c824da6df065f43b0c458dc505940e98a7f1", "type": "zip", "shasum": "", "reference": "32b0c824da6df065f43b0c458dc505940e98a7f1"}, "time": "2016-07-30T07:20:35+00:00"}, {"version": "v2.8.9", "version_normalized": "*******"}, {"version": "v2.8.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "00334ef0b9317e5d7c7641a2b56671a1df23b7a0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/00334ef0b9317e5d7c7641a2b56671a1df23b7a0", "type": "zip", "shasum": "", "reference": "00334ef0b9317e5d7c7641a2b56671a1df23b7a0"}, "time": "2016-06-29T05:29:29+00:00"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8a1648d2e165ba87c759ba57d7f4c13d95fdf4a1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8a1648d2e165ba87c759ba57d7f4c13d95fdf4a1", "type": "zip", "shasum": "", "reference": "8a1648d2e165ba87c759ba57d7f4c13d95fdf4a1"}, "time": "2016-06-06T11:11:27+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d60b8e076d22953aabebeebda53bf334438e7aca"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d60b8e076d22953aabebeebda53bf334438e7aca", "type": "zip", "shasum": "", "reference": "d60b8e076d22953aabebeebda53bf334438e7aca"}, "time": "2016-03-25T01:40:30+00:00"}, {"version": "v2.8.5", "version_normalized": "*******"}, {"version": "v2.8.4", "version_normalized": "*******"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b7b4ebadd2b5e614ff7d2d6fc63e0ed0578909c7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b7b4ebadd2b5e614ff7d2d6fc63e0ed0578909c7", "type": "zip", "shasum": "", "reference": "b7b4ebadd2b5e614ff7d2d6fc63e0ed0578909c7"}, "time": "2016-02-02T09:49:18+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bc0b666903944858f4ffec01c4e50c63e5c276c0"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bc0b666903944858f4ffec01c4e50c63e5c276c0", "type": "zip", "shasum": "", "reference": "bc0b666903944858f4ffec01c4e50c63e5c276c0"}, "time": "2016-01-03T15:33:41+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c1db87c51251167dd91198b9d1edf897773adb4f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c1db87c51251167dd91198b9d1edf897773adb4f", "type": "zip", "shasum": "", "reference": "c1db87c51251167dd91198b9d1edf897773adb4f"}, "time": "2015-12-05T17:37:59+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6772657767649fc3b31df12705194fb4af11ef98"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6772657767649fc3b31df12705194fb4af11ef98", "type": "zip", "shasum": "", "reference": "6772657767649fc3b31df12705194fb4af11ef98"}, "time": "2015-11-18T13:45:00+00:00"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "98b89a9091dde2af772d4c74a09447d317d1bdf4"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/98b89a9091dde2af772d4c74a09447d317d1bdf4", "type": "zip", "shasum": "", "reference": "98b89a9091dde2af772d4c74a09447d317d1bdf4"}, "time": "2015-10-30T20:15:42+00:00"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "1959c78c5a32539ef221b3e18a961a96d949118f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/1959c78c5a32539ef221b3e18a961a96d949118f", "type": "zip", "shasum": "", "reference": "1959c78c5a32539ef221b3e18a961a96d949118f"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.7.51"}, "time": "2018-05-17T10:34:06+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require": {"php": ">=5.3.9"}, "require-dev": {"symfony/config": "~2.7", "symfony/intl": "~2.7.25|^2.8.18", "symfony/yaml": "~2.2", "psr/log": "~1.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/translation/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "********"}, {"version": "v2.7.47", "version_normalized": "********"}, {"version": "v2.7.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bf521bfd1ba3f532810a1b3e2a81e8bfa15dce47"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bf521bfd1ba3f532810a1b3e2a81e8bfa15dce47", "type": "zip", "shasum": "", "reference": "bf521bfd1ba3f532810a1b3e2a81e8bfa15dce47"}, "time": "2018-01-17T08:38:26+00:00", "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}}, {"version": "v2.7.45", "version_normalized": "********"}, {"version": "v2.7.44", "version_normalized": "********"}, {"version": "v2.7.43", "version_normalized": "********"}, {"version": "v2.7.42", "version_normalized": "********"}, {"version": "v2.7.41", "version_normalized": "********"}, {"version": "v2.7.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a3c14161a92c275aeabff7c8553dcbc293e64d96"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a3c14161a92c275aeabff7c8553dcbc293e64d96", "type": "zip", "shasum": "", "reference": "a3c14161a92c275aeabff7c8553dcbc293e64d96"}, "time": "2018-01-03T07:23:28+00:00"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2dad34e6f73ad6f5845e5758b8a61cf18d53cfec"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2dad34e6f73ad6f5845e5758b8a61cf18d53cfec", "type": "zip", "shasum": "", "reference": "2dad34e6f73ad6f5845e5758b8a61cf18d53cfec"}, "time": "2017-11-07T14:04:08+00:00"}, {"version": "v2.7.38", "version_normalized": "********"}, {"version": "v2.7.37", "version_normalized": "********"}, {"version": "v2.7.36", "version_normalized": "********"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6909a3cbab97af964dc0b4a5069248c65ae37777"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6909a3cbab97af964dc0b4a5069248c65ae37777", "type": "zip", "shasum": "", "reference": "6909a3cbab97af964dc0b4a5069248c65ae37777"}, "time": "2017-09-30T14:00:25+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d52cb2413f339a9eb19e625c6a0ee9476fc7e2af"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d52cb2413f339a9eb19e625c6a0ee9476fc7e2af", "type": "zip", "shasum": "", "reference": "d52cb2413f339a9eb19e625c6a0ee9476fc7e2af"}, "time": "2017-06-23T10:52:30+00:00"}, {"version": "v2.7.33", "version_normalized": "********"}, {"version": "v2.7.32", "version_normalized": "********"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3232308ee2c8522642df02c343cfa217162485bd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3232308ee2c8522642df02c343cfa217162485bd", "type": "zip", "shasum": "", "reference": "3232308ee2c8522642df02c343cfa217162485bd"}, "time": "2017-06-01T20:44:56+00:00"}, {"version": "v2.7.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "160c2d5c546a1d7ba8ce60514ae177b8e3771829"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/160c2d5c546a1d7ba8ce60514ae177b8e3771829", "type": "zip", "shasum": "", "reference": "160c2d5c546a1d7ba8ce60514ae177b8e3771829"}, "time": "2017-04-12T07:39:27+00:00"}, {"version": "v2.7.27", "version_normalized": "********"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "560c640fabba712734c531a5148e839ef94a49b5"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/560c640fabba712734c531a5148e839ef94a49b5", "type": "zip", "shasum": "", "reference": "560c640fabba712734c531a5148e839ef94a49b5"}, "time": "2017-03-20T15:03:41+00:00"}, {"version": "v2.7.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "07f9bd6f87dcb23c0ed77b5d83a903387a77e039"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/07f9bd6f87dcb23c0ed77b5d83a903387a77e039", "type": "zip", "shasum": "", "reference": "07f9bd6f87dcb23c0ed77b5d83a903387a77e039"}, "time": "2017-03-03T16:35:37+00:00"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f158ed71fe8921de1409a7823221dcfe5070425a"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f158ed71fe8921de1409a7823221dcfe5070425a", "type": "zip", "shasum": "", "reference": "f158ed71fe8921de1409a7823221dcfe5070425a"}, "time": "2017-01-02T20:30:00+00:00", "require-dev": {"symfony/config": "~2.7", "symfony/intl": "~2.4", "symfony/yaml": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.7.23", "version_normalized": "********"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3d7c5418561389ea5c57ba29edbdf7cf6c4ecb40"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3d7c5418561389ea5c57ba29edbdf7cf6c4ecb40", "type": "zip", "shasum": "", "reference": "3d7c5418561389ea5c57ba29edbdf7cf6c4ecb40"}, "time": "2016-11-18T20:26:45+00:00"}, {"version": "v2.7.21", "version_normalized": "********"}, {"version": "v2.7.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f99349cca3041a7397ddc234c12c508cd483d9f7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f99349cca3041a7397ddc234c12c508cd483d9f7", "type": "zip", "shasum": "", "reference": "f99349cca3041a7397ddc234c12c508cd483d9f7"}, "time": "2016-10-16T20:09:53+00:00"}, {"version": "v2.7.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "5b78058d0b4ee0cc0bc0f3bdafe02f18cfff2a35"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/5b78058d0b4ee0cc0bc0f3bdafe02f18cfff2a35", "type": "zip", "shasum": "", "reference": "5b78058d0b4ee0cc0bc0f3bdafe02f18cfff2a35"}, "time": "2016-09-06T07:26:07+00:00"}, {"version": "v2.7.18", "version_normalized": "********"}, {"version": "v2.7.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7dfe708755d2d11c87c49eb82465ae6fa3c331b1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7dfe708755d2d11c87c49eb82465ae6fa3c331b1", "type": "zip", "shasum": "", "reference": "7dfe708755d2d11c87c49eb82465ae6fa3c331b1"}, "time": "2016-07-30T07:17:26+00:00"}, {"version": "v2.7.16", "version_normalized": "********"}, {"version": "v2.7.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "cc56317c4a2c94d5081e09f44038f7c1b3db7662"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/cc56317c4a2c94d5081e09f44038f7c1b3db7662", "type": "zip", "shasum": "", "reference": "cc56317c4a2c94d5081e09f44038f7c1b3db7662"}, "time": "2016-06-28T06:24:06+00:00"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8c8d47680768735fe7297a846cdc5d63c941130c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8c8d47680768735fe7297a846cdc5d63c941130c", "type": "zip", "shasum": "", "reference": "8c8d47680768735fe7297a846cdc5d63c941130c"}, "time": "2016-06-06T11:03:51+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e75d88c9af3c9c06341f4dad7ce776c9bddf94de"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e75d88c9af3c9c06341f4dad7ce776c9bddf94de", "type": "zip", "shasum": "", "reference": "e75d88c9af3c9c06341f4dad7ce776c9bddf94de"}, "time": "2016-03-24T09:06:43+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4c61cf815af17eee4cebf0e4c66f56a2f704cfd8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4c61cf815af17eee4cebf0e4c66f56a2f704cfd8", "type": "zip", "shasum": "", "reference": "4c61cf815af17eee4cebf0e4c66f56a2f704cfd8"}, "time": "2016-02-01T20:45:15+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8cbab8445ad4269427077ba02fff8718cb397e22"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8cbab8445ad4269427077ba02fff8718cb397e22", "type": "zip", "shasum": "", "reference": "8cbab8445ad4269427077ba02fff8718cb397e22"}, "time": "2016-01-03T15:32:00+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e7e95debf0465f7886d2994cd808f9382adb423c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e7e95debf0465f7886d2994cd808f9382adb423c", "type": "zip", "shasum": "", "reference": "e7e95debf0465f7886d2994cd808f9382adb423c"}, "time": "2015-12-05T17:37:09+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e4ecb9c3ba1304eaf24de15c2d7a428101c1982f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e4ecb9c3ba1304eaf24de15c2d7a428101c1982f", "type": "zip", "shasum": "", "reference": "e4ecb9c3ba1304eaf24de15c2d7a428101c1982f"}, "time": "2015-11-18T13:41:01+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6ccd9289ec1c71d01a49d83480de3b5293ce30c8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6ccd9289ec1c71d01a49d83480de3b5293ce30c8", "type": "zip", "shasum": "", "reference": "6ccd9289ec1c71d01a49d83480de3b5293ce30c8"}, "time": "2015-10-27T15:38:06+00:00", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "485877661835e188cd78345c6d4eef1290d17571"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/485877661835e188cd78345c6d4eef1290d17571", "type": "zip", "shasum": "", "reference": "485877661835e188cd78345c6d4eef1290d17571"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.7.4"}, "time": "2015-09-06T08:36:38+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.7", "symfony/intl": "~2.4", "symfony/yaml": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "485877661835e188cd78345c6d4eef1290d17571"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/485877661835e188cd78345c6d4eef1290d17571", "type": "zip", "shasum": "", "reference": "485877661835e188cd78345c6d4eef1290d17571"}, "support": {"source": "https://github.com/symfony/Translation/tree/2.7"}}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "c8dc34cc936152c609cdd722af317e4239d10dd6"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/c8dc34cc936152c609cdd722af317e4239d10dd6", "type": "zip", "shasum": "", "reference": "c8dc34cc936152c609cdd722af317e4239d10dd6"}, "time": "2015-07-09T16:07:40+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.7", "symfony/intl": "~2.3", "symfony/yaml": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.7.2", "version_normalized": "*******"}, {"version": "v2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "8349a2b0d11bd0311df9e8914408080912983a0b"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/8349a2b0d11bd0311df9e8914408080912983a0b", "type": "zip", "shasum": "", "reference": "8349a2b0d11bd0311df9e8914408080912983a0b"}, "time": "2015-06-11T17:26:34+00:00"}, {"version": "v2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "cc1907bbeacfcc703c031b67545400d6e7d1eb79"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/cc1907bbeacfcc703c031b67545400d6e7d1eb79", "type": "zip", "shasum": "", "reference": "cc1907bbeacfcc703c031b67545400d6e7d1eb79"}, "time": "2015-05-29T14:44:44+00:00"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "1be5bbed3938247acd7c5f4e4f29dd32db636b1f"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/1be5bbed3938247acd7c5f4e4f29dd32db636b1f", "type": "zip", "shasum": "", "reference": "1be5bbed3938247acd7c5f4e4f29dd32db636b1f"}, "time": "2015-05-11T02:35:29+00:00"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "7ecdcceff51b57d87ff3e262e4069f902a720a0b"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/7ecdcceff51b57d87ff3e262e4069f902a720a0b", "type": "zip", "shasum": "", "reference": "7ecdcceff51b57d87ff3e262e4069f902a720a0b"}, "time": "2015-04-10T07:23:38+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Translation\\": ""}}, "target-dir": "Symfony/Component/Translation"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d84291215b5892834dd8ca8ee52f9cbdb8274904"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d84291215b5892834dd8ca8ee52f9cbdb8274904", "type": "zip", "shasum": "", "reference": "d84291215b5892834dd8ca8ee52f9cbdb8274904"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.6.11"}, "time": "2015-07-08T05:59:48+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.3,>=2.3.12", "symfony/intl": "~2.3", "symfony/yaml": "~2.2", "psr/log": "~1.0"}, "conflict": "__unset"}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "d84291215b5892834dd8ca8ee52f9cbdb8274904"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/d84291215b5892834dd8ca8ee52f9cbdb8274904", "type": "zip", "shasum": "", "reference": "d84291215b5892834dd8ca8ee52f9cbdb8274904"}, "support": {"source": "https://github.com/symfony/Translation/tree/2.6"}}, {"version": "v2.6.10", "version_normalized": "********"}, {"version": "v2.6.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "89cdf3c43bc24c85dd8173dfcf5a979a95e5bd9c"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/89cdf3c43bc24c85dd8173dfcf5a979a95e5bd9c", "type": "zip", "shasum": "", "reference": "89cdf3c43bc24c85dd8173dfcf5a979a95e5bd9c"}, "time": "2015-05-29T14:42:58+00:00"}, {"version": "v2.6.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "d030b3d8d9699795dbf8c59e915ef879007a4483"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/d030b3d8d9699795dbf8c59e915ef879007a4483", "type": "zip", "shasum": "", "reference": "d030b3d8d9699795dbf8c59e915ef879007a4483"}, "time": "2015-05-22T14:37:51+00:00"}, {"version": "v2.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "398e0eedcb89243ad34a10d079a4b6ea4c0b61ff"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/398e0eedcb89243ad34a10d079a4b6ea4c0b61ff", "type": "zip", "shasum": "", "reference": "398e0eedcb89243ad34a10d079a4b6ea4c0b61ff"}, "time": "2015-05-05T16:51:00+00:00"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "bd939f05cdaca128f4ddbae1b447d6f0203b60af"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/bd939f05cdaca128f4ddbae1b447d6f0203b60af", "type": "zip", "shasum": "", "reference": "bd939f05cdaca128f4ddbae1b447d6f0203b60af"}, "time": "2015-03-30T15:54:10+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "043db5f1eef9598d1bc1d75b93304984c003d7d9"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/043db5f1eef9598d1bc1d75b93304984c003d7d9", "type": "zip", "shasum": "", "reference": "043db5f1eef9598d1bc1d75b93304984c003d7d9"}, "time": "2015-03-14T11:42:25+00:00"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "f289cdf8179d32058c1e1cbac723106a5ff6fa39"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/f289cdf8179d32058c1e1cbac723106a5ff6fa39", "type": "zip", "shasum": "", "reference": "f289cdf8179d32058c1e1cbac723106a5ff6fa39"}, "time": "2015-01-03T15:33:07+00:00", "require-dev": {"symfony/config": "~2.3,>=2.3.12", "symfony/intl": "~2.3", "symfony/yaml": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.6.3", "version_normalized": "*******"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "5b8bf84a43317021849813f556f26dc35968156b"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/5b8bf84a43317021849813f556f26dc35968156b", "type": "zip", "shasum": "", "reference": "5b8bf84a43317021849813f556f26dc35968156b"}, "time": "2014-12-02T20:19:20+00:00", "require-dev": {"symfony/config": "~2.0", "symfony/intl": "~2.3", "symfony/yaml": "~2.2", "psr/log": "~1.0"}}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "0a3711860976f15ee46642b4dd354e9ef9fc9a15"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/0a3711860976f15ee46642b4dd354e9ef9fc9a15", "type": "zip", "shasum": "", "reference": "0a3711860976f15ee46642b4dd354e9ef9fc9a15"}, "time": "2014-11-28T10:00:40+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "09c28736ec20984db38b8cd15f66d7afa70d0b46"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/09c28736ec20984db38b8cd15f66d7afa70d0b46", "type": "zip", "shasum": "", "reference": "09c28736ec20984db38b8cd15f66d7afa70d0b46"}, "support": {"source": "https://github.com/symfony/Translation/tree/master"}, "time": "2014-11-04T14:29:39+00:00"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "a2b07ab1fd04bfa53f4ec081c36cc37b372e78ae"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/a2b07ab1fd04bfa53f4ec081c36cc37b372e78ae", "type": "zip", "shasum": "", "reference": "a2b07ab1fd04bfa53f4ec081c36cc37b372e78ae"}, "time": "2014-10-26T07:46:28+00:00"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "165b5348cd20f8c4b2fcf1097c9c8300d1093b90"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/165b5348cd20f8c4b2fcf1097c9c8300d1093b90", "type": "zip", "shasum": "", "reference": "165b5348cd20f8c4b2fcf1097c9c8300d1093b90"}, "support": {"source": "https://github.com/symfony/Translation/tree/v2.5.11"}, "time": "2015-01-03T15:23:51+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "require-dev": {"symfony/config": "~2.3,>=2.3.12", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}, "suggest": {"symfony/config": "", "symfony/yaml": ""}}, {"version": "v2.5.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/Translation/tree/2.5"}}, {"version": "v2.5.10", "version_normalized": "********"}, {"version": "v2.5.9", "version_normalized": "*******"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "ab6e1580746c248c955a4c70a2a214fe780aa299"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/ab6e1580746c248c955a4c70a2a214fe780aa299", "type": "zip", "shasum": "", "reference": "ab6e1580746c248c955a4c70a2a214fe780aa299"}, "time": "2014-12-02T20:15:53+00:00", "require-dev": {"symfony/config": "~2.0", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "ad3c3248f6ff33856f29c278f575a527eb9621ed"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/ad3c3248f6ff33856f29c278f575a527eb9621ed", "type": "zip", "shasum": "", "reference": "ad3c3248f6ff33856f29c278f575a527eb9621ed"}, "time": "2014-10-26T07:41:27+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "362fe4da2cfe587f72d57aaa2f89e6b61c05dedf"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/362fe4da2cfe587f72d57aaa2f89e6b61c05dedf", "type": "zip", "shasum": "", "reference": "362fe4da2cfe587f72d57aaa2f89e6b61c05dedf"}, "time": "2014-10-01T05:50:18+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "170c0d895616e1a6a35681ffb0b9e339f58ab928"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/170c0d895616e1a6a35681ffb0b9e339f58ab928", "type": "zip", "shasum": "", "reference": "170c0d895616e1a6a35681ffb0b9e339f58ab928"}, "time": "2014-09-23T05:25:11+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "7526ad65f1961b2422ab33e4d3b05f92be16e5e2"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/7526ad65f1961b2422ab33e4d3b05f92be16e5e2", "type": "zip", "shasum": "", "reference": "7526ad65f1961b2422ab33e4d3b05f92be16e5e2"}, "time": "2014-09-03T09:00:14+00:00", "require-dev": {"symfony/config": "~2.0", "symfony/yaml": "~2.2"}}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "ae573e45b099b1e2d332930ac626cd4270e09539"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/ae573e45b099b1e2d332930ac626cd4270e09539", "type": "zip", "shasum": "", "reference": "ae573e45b099b1e2d332930ac626cd4270e09539"}, "time": "2014-07-28T13:20:46+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "059d57c361043f5a06eb348b9b7554213f9ab8d1"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/059d57c361043f5a06eb348b9b7554213f9ab8d1", "type": "zip", "shasum": "", "reference": "059d57c361043f5a06eb348b9b7554213f9ab8d1"}, "time": "2014-07-15T14:22:44+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "d06de937c94cdb339a1f4bbda13c3c8c4406fb92"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/d06de937c94cdb339a1f4bbda13c3c8c4406fb92", "type": "zip", "shasum": "", "reference": "d06de937c94cdb339a1f4bbda13c3c8c4406fb92"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "5f23265dcf8927a84be832608069c9edca3cf5f4"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/5f23265dcf8927a84be832608069c9edca3cf5f4", "type": "zip", "shasum": "", "reference": "5f23265dcf8927a84be832608069c9edca3cf5f4"}, "support": {"source": "https://github.com/symfony/Translation/tree/master"}, "time": "2014-05-22T13:47:45+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "d456c7b7510b6510d27b5addd0bfbf1f83c70d8d"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/d456c7b7510b6510d27b5addd0bfbf1f83c70d8d", "type": "zip", "shasum": "", "reference": "d456c7b7510b6510d27b5addd0bfbf1f83c70d8d"}, "time": "2014-04-18T21:02:40+00:00"}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "e9fd75056bacd2a9f796cb4f3dbd1b2ef36f2a47"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/e9fd75056bacd2a9f796cb4f3dbd1b2ef36f2a47", "type": "zip", "shasum": "", "reference": "e9fd75056bacd2a9f796cb4f3dbd1b2ef36f2a47"}, "support": {"source": "https://github.com/symfony/Translation/tree/2.4"}, "time": "2014-03-26T11:51:10+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "c61327aab6ec192b55e4868c52fdd0ca476100d3"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/c61327aab6ec192b55e4868c52fdd0ca476100d3", "type": "zip", "shasum": "", "reference": "c61327aab6ec192b55e4868c52fdd0ca476100d3"}, "time": "2014-09-23T05:24:59+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "require-dev": {"symfony/config": "~2.0", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "26a20abf9f6f77a2df0a7edbc8104c2991832c65"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/26a20abf9f6f77a2df0a7edbc8104c2991832c65", "type": "zip", "shasum": "", "reference": "26a20abf9f6f77a2df0a7edbc8104c2991832c65"}, "time": "2014-09-03T08:42:07+00:00", "require-dev": {"symfony/config": "~2.0", "symfony/yaml": "~2.2"}}, {"version": "v2.4.8", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "2e9116d2d6d4e673a196f5699465e79a6910359d"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/2e9116d2d6d4e673a196f5699465e79a6910359d", "type": "zip", "shasum": "", "reference": "2e9116d2d6d4e673a196f5699465e79a6910359d"}, "time": "2014-07-15T14:22:28+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "2465aa6c07fee8e742a1872d36ae950ed921033e"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/2465aa6c07fee8e742a1872d36ae950ed921033e", "type": "zip", "shasum": "", "reference": "2465aa6c07fee8e742a1872d36ae950ed921033e"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "9a3941444487ea57acd38f5c61bee1e0c3f69293"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/9a3941444487ea57acd38f5c61bee1e0c3f69293", "type": "zip", "shasum": "", "reference": "9a3941444487ea57acd38f5c61bee1e0c3f69293"}, "time": "2014-05-22T13:46:01+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "d2c73ffa4a5ba1fa0c5d93f43b68331dffe898c5"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/d2c73ffa4a5ba1fa0c5d93f43b68331dffe898c5", "type": "zip", "shasum": "", "reference": "d2c73ffa4a5ba1fa0c5d93f43b68331dffe898c5"}, "time": "2014-04-18T21:02:05+00:00"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "98697a79a0db83d6f9a3a609a0704f02571ab46c"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/98697a79a0db83d6f9a3a609a0704f02571ab46c", "type": "zip", "shasum": "", "reference": "98697a79a0db83d6f9a3a609a0704f02571ab46c"}, "time": "2014-03-26T11:35:33+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "b00fd07417e493e08488e87bcebeb9681fc7323b"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/b00fd07417e493e08488e87bcebeb9681fc7323b", "type": "zip", "shasum": "", "reference": "b00fd07417e493e08488e87bcebeb9681fc7323b"}, "time": "2014-02-03T17:15:33+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "7f76dffd7eaf2c9a3a8f47649404c71440d18c8b"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/7f76dffd7eaf2c9a3a8f47649404c71440d18c8b", "type": "zip", "shasum": "", "reference": "7f76dffd7eaf2c9a3a8f47649404c71440d18c8b"}, "time": "2013-12-31T13:43:26+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "0919e0fc709217f8c9e5049f2603419fdd4a34ff"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/0919e0fc709217f8c9e5049f2603419fdd4a34ff", "type": "zip", "shasum": "", "reference": "0919e0fc709217f8c9e5049f2603419fdd4a34ff"}, "time": "2013-11-28T10:27:26+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "352f175db0165d323d777325ba8ee317d028fd4a"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/352f175db0165d323d777325ba8ee317d028fd4a", "type": "zip", "shasum": "", "reference": "352f175db0165d323d777325ba8ee317d028fd4a"}, "support": {"source": "https://github.com/symfony/Translation/tree/master"}, "time": "2013-11-20T02:31:35+00:00"}, {"version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "8addaff4947de4a9342318de62a078d74b1bd603"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/8addaff4947de4a9342318de62a078d74b1bd603", "type": "zip", "shasum": "", "reference": "8addaff4947de4a9342318de62a078d74b1bd603"}, "time": "2013-10-10T14:19:44+00:00"}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/Translation.git", "type": "git", "reference": "d7e84f71f1856f75025618aca6cdaf074ab84220"}, "dist": {"url": "https://api.github.com/repos/symfony/Translation/zipball/d7e84f71f1856f75025618aca6cdaf074ab84220", "type": "zip", "shasum": "", "reference": "d7e84f71f1856f75025618aca6cdaf074ab84220"}, "time": "2013-09-27T13:28:11+00:00"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "abc5d93921385c01b2f5601b45722cf4bd63199e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/abc5d93921385c01b2f5601b45722cf4bd63199e", "type": "zip", "shasum": "", "reference": "abc5d93921385c01b2f5601b45722cf4bd63199e"}, "support": {"source": "https://github.com/symfony/translation/tree/2.3"}, "time": "2016-03-15T16:57:15+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "require-dev": {"symfony/config": "~2.3,>=2.3.12", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}}, {"version": "v2.3.41", "version_normalized": "********"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3d9c408e1a2650ebe10b1b43e8136d4fda16b202"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3d9c408e1a2650ebe10b1b43e8136d4fda16b202", "type": "zip", "shasum": "", "reference": "3d9c408e1a2650ebe10b1b43e8136d4fda16b202"}, "time": "2016-03-04T07:12:06+00:00"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f96454be6e5a4fa5863d61d0dc77320ad5e4b5a3"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f96454be6e5a4fa5863d61d0dc77320ad5e4b5a3", "type": "zip", "shasum": "", "reference": "f96454be6e5a4fa5863d61d0dc77320ad5e4b5a3"}, "time": "2016-02-01T20:38:54+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d7ee0c4dcaf9201c453d376a097edde7ee6a2e02"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d7ee0c4dcaf9201c453d376a097edde7ee6a2e02", "type": "zip", "shasum": "", "reference": "d7ee0c4dcaf9201c453d376a097edde7ee6a2e02"}, "time": "2016-01-12T11:31:34+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "08cb7fb6f879ec26898e6563d43186155d2a0afc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/08cb7fb6f879ec26898e6563d43186155d2a0afc", "type": "zip", "shasum": "", "reference": "08cb7fb6f879ec26898e6563d43186155d2a0afc"}, "time": "2015-12-01T22:08:33+00:00"}, {"version": "v2.3.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7f01eb66bb0c677f30f5107a8848f8e1fc6ec9a7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7f01eb66bb0c677f30f5107a8848f8e1fc6ec9a7", "type": "zip", "shasum": "", "reference": "7f01eb66bb0c677f30f5107a8848f8e1fc6ec9a7"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f2dbeb87710446df8b8d8c2960d49d15565b96ed"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f2dbeb87710446df8b8d8c2960d49d15565b96ed", "type": "zip", "shasum": "", "reference": "f2dbeb87710446df8b8d8c2960d49d15565b96ed"}, "time": "2015-10-23T09:44:09+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Translation\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7560d045145b5b08f798524e7a31743cf2fccd76"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7560d045145b5b08f798524e7a31743cf2fccd76", "type": "zip", "shasum": "", "reference": "7560d045145b5b08f798524e7a31743cf2fccd76"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.33"}, "time": "2015-09-03T20:55:07+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7", "symfony/config": "~2.3,>=2.3.12", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ef0c28743ddec8ae8bbba1993f7367df3d066f74"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ef0c28743ddec8ae8bbba1993f7367df3d066f74", "type": "zip", "shasum": "", "reference": "ef0c28743ddec8ae8bbba1993f7367df3d066f74"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.32"}, "time": "2015-08-26T09:54:34+00:00"}, {"version": "v2.3.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bb612d220692c2977c5184b83d2bff8d5482fb4d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bb612d220692c2977c5184b83d2bff8d5482fb4d", "type": "zip", "shasum": "", "reference": "bb612d220692c2977c5184b83d2bff8d5482fb4d"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.31"}, "time": "2015-07-01T14:29:20+00:00"}, {"version": "v2.3.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0e1d974b84ccc30fb59aec852dbe6b7562f79e54"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0e1d974b84ccc30fb59aec852dbe6b7562f79e54", "type": "zip", "shasum": "", "reference": "0e1d974b84ccc30fb59aec852dbe6b7562f79e54"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.30"}, "time": "2015-05-28T20:42:23+00:00"}, {"version": "v2.3.29", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "64e3fe3c1fb83a8bc72ef269f0e6406d7fb468bf"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/64e3fe3c1fb83a8bc72ef269f0e6406d7fb468bf", "type": "zip", "shasum": "", "reference": "64e3fe3c1fb83a8bc72ef269f0e6406d7fb468bf"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.29"}, "time": "2015-05-15T13:28:34+00:00"}, {"version": "v2.3.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ab156d547f6b8774ea8760174928a1c775aec4c7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ab156d547f6b8774ea8760174928a1c775aec4c7", "type": "zip", "shasum": "", "reference": "ab156d547f6b8774ea8760174928a1c775aec4c7"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.28"}, "time": "2015-05-01T14:06:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "600ef7febf07aec56f3d2579fd1496cd332d9112"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/600ef7febf07aec56f3d2579fd1496cd332d9112", "type": "zip", "shasum": "", "reference": "600ef7febf07aec56f3d2579fd1496cd332d9112"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.27"}, "time": "2015-03-30T15:33:35+00:00"}, {"version": "v2.3.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "baabd7e9bb79b785f60441be3a200032a8f56d8e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/baabd7e9bb79b785f60441be3a200032a8f56d8e", "type": "zip", "shasum": "", "reference": "baabd7e9bb79b785f60441be3a200032a8f56d8e"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.26"}, "time": "2015-03-07T07:38:01+00:00"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "46131c2b2ca1a5c1db19b89583814822d7a36cbb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/46131c2b2ca1a5c1db19b89583814822d7a36cbb", "type": "zip", "shasum": "", "reference": "46131c2b2ca1a5c1db19b89583814822d7a36cbb"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.25"}, "time": "2015-01-03T13:14:51+00:00", "require-dev": {"symfony/config": "~2.3,>=2.3.12", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}}, {"version": "v2.3.24", "version_normalized": "********"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "dc812ef0f006093b32442a77ff5b46107f5cb121"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/dc812ef0f006093b32442a77ff5b46107f5cb121", "type": "zip", "shasum": "", "reference": "dc812ef0f006093b32442a77ff5b46107f5cb121"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.23"}, "time": "2014-11-30T13:33:44+00:00", "require-dev": {"symfony/config": "~2.0", "symfony/intl": "~2.3", "symfony/yaml": "~2.2"}}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e06d90fdf71a6259bfc4c14e431114937763845f"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e06d90fdf71a6259bfc4c14e431114937763845f", "type": "zip", "shasum": "", "reference": "e06d90fdf71a6259bfc4c14e431114937763845f"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.22"}, "time": "2014-10-26T07:30:58+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "638d6e0ab12934104f6b117867772dd465eb8ef1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/638d6e0ab12934104f6b117867772dd465eb8ef1", "type": "zip", "shasum": "", "reference": "638d6e0ab12934104f6b117867772dd465eb8ef1"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.21"}, "time": "2014-10-01T05:38:33+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2d613edd829cf6feffbe3567c6ac296dfa7026bd"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2d613edd829cf6feffbe3567c6ac296dfa7026bd", "type": "zip", "shasum": "", "reference": "2d613edd829cf6feffbe3567c6ac296dfa7026bd"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.20"}, "time": "2014-09-23T05:15:05+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8ef90498bb0bef56257fd6cce7880c8d7b649745"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8ef90498bb0bef56257fd6cce7880c8d7b649745", "type": "zip", "shasum": "", "reference": "8ef90498bb0bef56257fd6cce7880c8d7b649745"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.19"}, "time": "2014-09-02T07:12:49+00:00", "require-dev": {"symfony/config": "~2.0", "symfony/yaml": "~2.2"}}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "f8a5a625232dea034cbd793cb623aea60e7e18ed"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/f8a5a625232dea034cbd793cb623aea60e7e18ed", "type": "zip", "shasum": "", "reference": "f8a5a625232dea034cbd793cb623aea60e7e18ed"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.18"}, "time": "2014-07-15T13:44:49+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "fb6fb9d681da36ec6ddaf80e7a430e508d4e083b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/fb6fb9d681da36ec6ddaf80e7a430e508d4e083b", "type": "zip", "shasum": "", "reference": "fb6fb9d681da36ec6ddaf80e7a430e508d4e083b"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "62aeb752569787bce4ad10faa96f5f91a9c431aa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/62aeb752569787bce4ad10faa96f5f91a9c431aa", "type": "zip", "shasum": "", "reference": "62aeb752569787bce4ad10faa96f5f91a9c431aa"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.16"}, "time": "2014-05-21T16:08:05+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "7783ff3056af9497c80d7302270c093181e2199b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/7783ff3056af9497c80d7302270c093181e2199b", "type": "zip", "shasum": "", "reference": "7783ff3056af9497c80d7302270c093181e2199b"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.13"}, "time": "2014-04-18T21:01:08+00:00"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4d0e9715effeb79f7b08ca8bf170fc1c5fce902d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4d0e9715effeb79f7b08ca8bf170fc1c5fce902d", "type": "zip", "shasum": "", "reference": "4d0e9715effeb79f7b08ca8bf170fc1c5fce902d"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.12"}, "time": "2014-03-26T07:21:50+00:00"}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "95a82ca5e91dbf3c5d195b9e18f14d57dd4328e8"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/95a82ca5e91dbf3c5d195b9e18f14d57dd4328e8", "type": "zip", "shasum": "", "reference": "95a82ca5e91dbf3c5d195b9e18f14d57dd4328e8"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.11"}, "time": "2014-01-28T15:54:04+00:00"}, {"version": "v2.3.10", "version_normalized": "********"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "b6867ad04b0e230898feb09b6f51d50c7b7ed683"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/b6867ad04b0e230898feb09b6f51d50c7b7ed683", "type": "zip", "shasum": "", "reference": "b6867ad04b0e230898feb09b6f51d50c7b7ed683"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.9"}, "time": "2013-12-30T21:13:42+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6b255d216d206983598d16906dee9dc63a2e8ee4"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6b255d216d206983598d16906dee9dc63a2e8ee4", "type": "zip", "shasum": "", "reference": "6b255d216d206983598d16906dee9dc63a2e8ee4"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.8"}, "time": "2013-11-28T06:43:58+00:00"}, {"version": "v2.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e0012648d7fc96d8f03bb7ca4d5e44f471a15417"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e0012648d7fc96d8f03bb7ca4d5e44f471a15417", "type": "zip", "shasum": "", "reference": "e0012648d7fc96d8f03bb7ca4d5e44f471a15417"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.7"}, "time": "2013-11-13T21:27:40+00:00"}, {"version": "v2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6aedcff5ea623316dbf2112b78f2f63a257aab01"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6aedcff5ea623316dbf2112b78f2f63a257aab01", "type": "zip", "shasum": "", "reference": "6aedcff5ea623316dbf2112b78f2f63a257aab01"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.6"}, "time": "2013-10-10T13:12:30+00:00"}, {"version": "v2.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "609dbde849d6a02be3f4a7ae29841d1d3db64d87"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/609dbde849d6a02be3f4a7ae29841d1d3db64d87", "type": "zip", "shasum": "", "reference": "609dbde849d6a02be3f4a7ae29841d1d3db64d87"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.5"}, "time": "2013-09-19T09:45:20+00:00"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "65f888291f0896ad492f9abc6dc05c998373aded"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/65f888291f0896ad492f9abc6dc05c998373aded", "type": "zip", "shasum": "", "reference": "65f888291f0896ad492f9abc6dc05c998373aded"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.4"}, "time": "2013-08-26T05:49:51+00:00"}, {"version": "v2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6fa9941fbfef37d6113c3bddc687fc507b846feb"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6fa9941fbfef37d6113c3bddc687fc507b846feb", "type": "zip", "shasum": "", "reference": "6fa9941fbfef37d6113c3bddc687fc507b846feb"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.3"}, "time": "2013-08-02T20:53:38+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "70fddb8499919f459322b9857ada7824722db9df"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/70fddb8499919f459322b9857ada7824722db9df", "type": "zip", "shasum": "", "reference": "70fddb8499919f459322b9857ada7824722db9df"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.3.2"}, "time": "2013-05-13T14:36:40+00:00"}, {"version": "v2.3.1", "version_normalized": "*******"}, {"version": "v2.3.0", "version_normalized": "*******"}, {"version": "v2.2.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "e27b46e4f051541b78cd47bab4829056390f19de"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/e27b46e4f051541b78cd47bab4829056390f19de", "type": "zip", "shasum": "", "reference": "e27b46e4f051541b78cd47bab4829056390f19de"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.11"}, "time": "2013-11-25T08:44:14+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require-dev": {"symfony/config": ">=2.0,<2.3-dev", "symfony/yaml": "~2.2"}, "suggest": {"symfony/config": "2.2.*", "symfony/yaml": "2.2.*"}}, {"version": "v2.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0983258f193c8b231b9b0afd54454c5487711daa"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0983258f193c8b231b9b0afd54454c5487711daa", "type": "zip", "shasum": "", "reference": "0983258f193c8b231b9b0afd54454c5487711daa"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.10"}, "time": "2013-11-09T12:40:25+00:00"}, {"version": "v2.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "a829715780bafe9022f1693e4392d0b7463192ff"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/a829715780bafe9022f1693e4392d0b7463192ff", "type": "zip", "shasum": "", "reference": "a829715780bafe9022f1693e4392d0b7463192ff"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.9"}, "time": "2013-10-09T20:59:37+00:00"}, {"version": "v2.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "0ad3e26803586e9865dd7d8312c198a919f8851d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/0ad3e26803586e9865dd7d8312c198a919f8851d", "type": "zip", "shasum": "", "reference": "0ad3e26803586e9865dd7d8312c198a919f8851d"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.8"}, "time": "2013-09-19T09:36:05+00:00"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "37a11fe823c28f9235548d253b215f07cec9a0de"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/37a11fe823c28f9235548d253b215f07cec9a0de", "type": "zip", "shasum": "", "reference": "37a11fe823c28f9235548d253b215f07cec9a0de"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.6"}, "time": "2013-08-24T12:29:44+00:00"}, {"version": "v2.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "32a1531e6298c037041ec82be95600e258938218"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/32a1531e6298c037041ec82be95600e258938218", "type": "zip", "shasum": "", "reference": "32a1531e6298c037041ec82be95600e258938218"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.5"}, "time": "2013-07-28T18:26:16+00:00"}, {"version": "v2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d5afdf0c79d9c7a993c1934a9ad6709dacdd22bc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d5afdf0c79d9c7a993c1934a9ad6709dacdd22bc", "type": "zip", "shasum": "", "reference": "d5afdf0c79d9c7a993c1934a9ad6709dacdd22bc"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.4"}, "time": "2013-05-10T16:49:00+00:00"}, {"version": "v2.2.3", "version_normalized": "*******"}, {"version": "v2.2.2", "version_normalized": "*******"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "8e51b398df129b05cf1cab72aff94ace7442deda"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/8e51b398df129b05cf1cab72aff94ace7442deda", "type": "zip", "shasum": "", "reference": "8e51b398df129b05cf1cab72aff94ace7442deda"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.1"}, "time": "2013-04-01T08:06:05+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9e655f7df0bcdd0257a762c759cc946b812f497e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9e655f7df0bcdd0257a762c759cc946b812f497e", "type": "zip", "shasum": "", "reference": "9e655f7df0bcdd0257a762c759cc946b812f497e"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.2.0"}, "time": "2013-02-08T16:10:57+00:00"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "39ba7a5dc560959667c45c9353b70f43182ca4b3"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/39ba7a5dc560959667c45c9353b70f43182ca4b3", "type": "zip", "shasum": "", "reference": "39ba7a5dc560959667c45c9353b70f43182ca4b3"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.13"}, "time": "2013-04-20T08:25:59+00:00", "autoload": {"psr-0": {"Symfony\\Component\\Translation": ""}}, "require-dev": {"symfony/config": "2.1.*", "symfony/yaml": "2.1.*"}, "suggest": {"symfony/config": "2.1.*", "symfony/yaml": "2.1.*"}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********"}, {"version": "v2.1.10", "version_normalized": "********"}, {"version": "v2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "121e5416841d7c81381538f9862242b1578ed89c"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/121e5416841d7c81381538f9862242b1578ed89c", "type": "zip", "shasum": "", "reference": "121e5416841d7c81381538f9862242b1578ed89c"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.9"}, "time": "2013-03-12T14:23:04+00:00"}, {"version": "v2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "331f25563a58ec768b59137f8ef34cb5fb1ab330"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/331f25563a58ec768b59137f8ef34cb5fb1ab330", "type": "zip", "shasum": "", "reference": "331f25563a58ec768b59137f8ef34cb5fb1ab330"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.8"}, "time": "2013-01-09T08:51:07+00:00"}, {"version": "v2.1.7", "version_normalized": "*******"}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "64ab9706970fdde7d872f9713d023dd47c3136bc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/64ab9706970fdde7d872f9713d023dd47c3136bc", "type": "zip", "shasum": "", "reference": "64ab9706970fdde7d872f9713d023dd47c3136bc"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.6"}, "time": "2012-12-06T10:00:55+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "806431a802d237e15fe38dca0b0460d512854652"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/806431a802d237e15fe38dca0b0460d512854652", "type": "zip", "shasum": "", "reference": "806431a802d237e15fe38dca0b0460d512854652"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.4"}, "time": "2012-11-21T15:37:42+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "deb6d9c83f1247b8a0ce2f66b6fbcbafdeff66ad"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/deb6d9c83f1247b8a0ce2f66b6fbcbafdeff66ad", "type": "zip", "shasum": "", "reference": "deb6d9c83f1247b8a0ce2f66b6fbcbafdeff66ad"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.3"}, "time": "2012-10-20T07:10:30+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "97b97722b00159ba9267f6c4ff940047d58171dc"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/97b97722b00159ba9267f6c4ff940047d58171dc", "type": "zip", "shasum": "", "reference": "97b97722b00159ba9267f6c4ff940047d58171dc"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.2"}, "time": "2012-09-10T10:53:42+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d2730909649ea1fb1789c8927454ff83bc73db20"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d2730909649ea1fb1789c8927454ff83bc73db20", "type": "zip", "shasum": "", "reference": "d2730909649ea1fb1789c8927454ff83bc73db20"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.1.0"}, "time": "2012-08-28T07:54:42+00:00", "suggest": {"symfony/config": "v2.1.0", "symfony/yaml": "v2.1.0"}}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "2c83067acb29667ceb62eedb784a8a4232a18839"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/2c83067acb29667ceb62eedb784a8a4232a18839", "type": "zip", "shasum": "", "reference": "2c83067acb29667ceb62eedb784a8a4232a18839"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.25"}, "time": "2013-01-04T16:59:43+00:00", "require": {"php": ">=5.3.2"}, "suggest": {"symfony/config": "v2.0.25", "symfony/yaml": "v2.0.25"}, "require-dev": "__unset", "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.24", "symfony/yaml": "v2.0.24"}}, {"version": "v2.0.23", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.23", "symfony/yaml": "v2.0.23"}}, {"version": "v2.0.22", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.22", "symfony/yaml": "v2.0.22"}}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "ec538b400330db336ca67a842f2ee37057223cf6"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/ec538b400330db336ca67a842f2ee37057223cf6", "type": "zip", "shasum": "", "reference": "ec538b400330db336ca67a842f2ee37057223cf6"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.21"}, "time": "2012-10-04T15:11:30+00:00", "suggest": {"symfony/config": "v2.0.21", "symfony/yaml": "v2.0.21"}}, {"version": "v2.0.20", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.20", "symfony/yaml": "v2.0.20"}}, {"version": "v2.0.19", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.19", "symfony/yaml": "v2.0.19"}}, {"version": "v2.0.18", "version_normalized": "********", "suggest": {"symfony/config": "v2.0.18", "symfony/yaml": "v2.0.18"}}, {"version": "v2.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "178b3eba474a706f25473d38e23e74b048417c8d"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/178b3eba474a706f25473d38e23e74b048417c8d", "type": "zip", "shasum": "", "reference": "178b3eba474a706f25473d38e23e74b048417c8d"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.17"}, "time": "2012-08-28T06:43:14+00:00", "suggest": {"symfony/config": "v2.0.17", "symfony/yaml": "v2.0.17"}}, {"version": "v2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6c2cea9ba9fb30256d25abe32596c7a20489f792"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6c2cea9ba9fb30256d25abe32596c7a20489f792", "type": "zip", "shasum": "", "reference": "6c2cea9ba9fb30256d25abe32596c7a20489f792"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.16"}, "time": "2012-07-09T12:43:50+00:00", "suggest": {"symfony/config": "v2.0.16", "symfony/yaml": "v2.0.16"}}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "15adc95ca483fe6fa2ee7820a7e184ca1e60a883"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/15adc95ca483fe6fa2ee7820a7e184ca1e60a883", "type": "zip", "shasum": "", "reference": "15adc95ca483fe6fa2ee7820a7e184ca1e60a883"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.15"}, "time": "2012-05-21T20:25:19+00:00", "suggest": {"symfony/config": "v2.0.15", "symfony/yaml": "v2.0.15"}}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "6e6ac2f0db3b7ca803dbe459db76f14f338420c7"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/6e6ac2f0db3b7ca803dbe459db76f14f338420c7", "type": "zip", "shasum": "", "reference": "6e6ac2f0db3b7ca803dbe459db76f14f338420c7"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.14"}, "time": "2012-05-15T16:56:32+00:00", "suggest": {"symfony/config": "v2.0.14", "symfony/yaml": "v2.0.14"}}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "bc1c1fd97e1dd2735ae5b774e9c2427a74a47364"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/bc1c1fd97e1dd2735ae5b774e9c2427a74a47364", "type": "zip", "shasum": "", "reference": "bc1c1fd97e1dd2735ae5b774e9c2427a74a47364"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.13"}, "time": "2012-03-29T12:32:59+00:00", "suggest": {"symfony/config": "v2.0.13", "symfony/yaml": "v2.0.13"}}, {"version": "v2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "3d909f15e46302be46c62a2a7473c0c571ca8230"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/3d909f15e46302be46c62a2a7473c0c571ca8230", "type": "zip", "shasum": "", "reference": "3d909f15e46302be46c62a2a7473c0c571ca8230"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.12"}, "time": "2012-02-22T09:10:37+00:00", "suggest": {"symfony/config": "v2.0.12", "symfony/yaml": "v2.0.12"}}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "9226110cf6802ef9187789d13a0c068c0979b631"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/9226110cf6802ef9187789d13a0c068c0979b631", "type": "zip", "shasum": "", "reference": "9226110cf6802ef9187789d13a0c068c0979b631"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.10"}, "time": "2012-01-05T13:51:20+00:00", "suggest": {"symfony/config": "v2.0.10", "symfony/yaml": "v2.0.10"}}, {"version": "v2.0.9", "version_normalized": "*******", "suggest": {"symfony/config": "v2.0.9", "symfony/yaml": "v2.0.9"}}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "c776b9464c4e111cbdc60b713acd714405ac2829"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/c776b9464c4e111cbdc60b713acd714405ac2829", "type": "zip", "shasum": "", "reference": "c776b9464c4e111cbdc60b713acd714405ac2829"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.7"}, "time": "2011-11-23T16:51:23+00:00", "suggest": {"symfony/config": "2.0.7", "symfony/yaml": "2.0.7"}}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "68d89036e260c727c801bad7b58c88c5c9df1fd1"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/68d89036e260c727c801bad7b58c88c5c9df1fd1", "type": "zip", "shasum": "", "reference": "68d89036e260c727c801bad7b58c88c5c9df1fd1"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.6"}, "time": "2011-11-12T08:45:17+00:00", "suggest": {"symfony/config": ">=2.0", "symfony/yaml": ">=2.0"}}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "4ff75674c34067bd9e5af3000a6bae5b790e0f0e"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/4ff75674c34067bd9e5af3000a6bae5b790e0f0e", "type": "zip", "shasum": "", "reference": "4ff75674c34067bd9e5af3000a6bae5b790e0f0e"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/translation.git", "type": "git", "reference": "d09876fb30e9660fd0e0959ff8258e4c708b6e2b"}, "dist": {"url": "https://api.github.com/repos/symfony/translation/zipball/d09876fb30e9660fd0e0959ff8258e4c708b6e2b", "type": "zip", "shasum": "", "reference": "d09876fb30e9660fd0e0959ff8258e4c708b6e2b"}, "support": {"source": "https://github.com/symfony/translation/tree/v2.0.4"}, "time": "2011-09-29T15:27:38+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [{"advisoryId": "PKSA-9dxk-2dxf-2khq", "affectedVersions": ">=2.0.0,<2.0.17"}], "last-modified": "Sat, 28 Jun 2025 08:28:20 GMT"}