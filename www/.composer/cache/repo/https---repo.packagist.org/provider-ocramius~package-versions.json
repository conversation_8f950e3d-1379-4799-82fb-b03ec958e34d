{"minified": "composer/2.0", "packages": {"ocramius/package-versions": [{"name": "ocramius/package-versions", "description": "Provides efficient querying for installed package versions (no runtime IO)", "keywords": [], "homepage": "", "version": "2.10.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "b2181b8f0e2adeef0db76a209e1a69369d8abe6f"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/b2181b8f0e2adeef0db76a209e1a69369d8abe6f", "type": "zip", "shasum": "", "reference": "b2181b8f0e2adeef0db76a209e1a69369d8abe6f"}, "type": "library", "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.10.0"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/package-versions", "type": "tidelift"}], "time": "2025-02-05T12:31:16+00:00", "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "require": {"php": "~8.2.0 || ~8.3.0 || ~8.4.0", "composer-runtime-api": "^2.2.0"}, "require-dev": {"composer/composer": "^2.8.5", "doctrine/coding-standard": "^12.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.36.0", "phpunit/phpunit": "^11.5.6", "psalm/plugin-phpunit": "^0.19.2", "vimeo/psalm": "^6.3.0"}, "replace": {"composer/package-versions-deprecated": "*"}}, {"version": "2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "b3397b9b4578989929d3bc2602c26fe19f035095"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/b3397b9b4578989929d3bc2602c26fe19f035095", "type": "zip", "shasum": "", "reference": "b3397b9b4578989929d3bc2602c26fe19f035095"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.9.0"}, "time": "2024-08-04T10:04:51+00:00", "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "composer-runtime-api": "^2.2.0"}, "require-dev": {"composer/composer": "^2.7.7", "doctrine/coding-standard": "^12.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.35.0", "phpunit/phpunit": "^9.6.20", "vimeo/psalm": "^5.25.0"}}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "7b5821f854cf1e6753c4ed7ceb3b11ae83bbad4e"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/7b5821f854cf1e6753c4ed7ceb3b11ae83bbad4e", "type": "zip", "shasum": "", "reference": "7b5821f854cf1e6753c4ed7ceb3b11ae83bbad4e"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.8.0"}, "time": "2023-09-15T11:02:59+00:00", "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0", "composer-runtime-api": "^2.2.0"}, "require-dev": {"composer/composer": "^2.6.3", "doctrine/coding-standard": "^12.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.33", "phpunit/phpunit": "^9.6.12", "vimeo/psalm": "^5.15.0"}}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "065921ed7cb2a6861443d91138d0a4378316af8d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/065921ed7cb2a6861443d91138d0a4378316af8d", "type": "zip", "shasum": "", "reference": "065921ed7cb2a6861443d91138d0a4378316af8d"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.7.0"}, "time": "2022-10-31T12:51:46+00:00", "require": {"php": "~8.1.0 || ~8.2.0", "composer-runtime-api": "^2.2.0"}, "require-dev": {"composer/composer": "^2.4.4", "doctrine/coding-standard": "^10.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.25.0", "phpunit/phpunit": "^9.5.26", "vimeo/psalm": "^4.29.0"}}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "9f4dc8c2af37c373b4cbbdce0427ed6c081872f1"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/9f4dc8c2af37c373b4cbbdce0427ed6c081872f1", "type": "zip", "shasum": "", "reference": "9f4dc8c2af37c373b4cbbdce0427ed6c081872f1"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.6.0"}, "time": "2022-10-10T14:46:02+00:00", "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0", "composer-runtime-api": "^2.2.0"}, "require-dev": {"composer/composer": "^2.2.0", "doctrine/coding-standard": "^9.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.13.0", "phpunit/phpunit": "^9.5.10", "vimeo/psalm": "^4.15.0"}}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "6707caff2e84a2368f29fdf2cb28a6b5a9e3ab53"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/6707caff2e84a2368f29fdf2cb28a6b5a9e3ab53", "type": "zip", "shasum": "", "reference": "6707caff2e84a2368f29fdf2cb28a6b5a9e3ab53"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.5.1"}, "time": "2022-03-05T18:04:57+00:00", "require": {"php": "~8.0.0 || ~8.1.0", "composer-runtime-api": "^2.2.0"}, "require-dev": {"composer/composer": "^2.2.0", "doctrine/coding-standard": "^9.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.10.0", "phpunit/phpunit": "^9.5.9", "vimeo/psalm": "^4.10.0"}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "deded4228eed848fc5eae2fa0149ceb43afd012a"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/deded4228eed848fc5eae2fa0149ceb43afd012a", "type": "zip", "shasum": "", "reference": "deded4228eed848fc5eae2fa0149ceb43afd012a"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.5.0"}, "time": "2021-12-22T12:00:55+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "114c30e91981d50fbcb81f1c7344a0f50f651eef"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/114c30e91981d50fbcb81f1c7344a0f50f651eef", "type": "zip", "shasum": "", "reference": "114c30e91981d50fbcb81f1c7344a0f50f651eef"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.4.1"}, "time": "2022-03-05T18:03:10+00:00", "require": {"php": "~8.0.0 || ~8.1.0", "composer-runtime-api": "^2.1.0"}, "require-dev": {"composer/composer": "^2.1.8", "doctrine/coding-standard": "^9.0.0", "ext-zip": "^1.15.0", "roave/infection-static-analysis-plugin": "^1.10.0", "phpunit/phpunit": "^9.5.9", "vimeo/psalm": "^4.10.0"}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "8e96fb9f4671eff86e8e487ff45c49bdc2d2d677"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/8e96fb9f4671eff86e8e487ff45c49bdc2d2d677", "type": "zip", "shasum": "", "reference": "8e96fb9f4671eff86e8e487ff45c49bdc2d2d677"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.4.0"}, "time": "2021-09-19T02:32:19+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "184faed2cf7ecc6b8ff404d1636f9b9ce88a2f50"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/184faed2cf7ecc6b8ff404d1636f9b9ce88a2f50", "type": "zip", "shasum": "", "reference": "184faed2cf7ecc6b8ff404d1636f9b9ce88a2f50"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.3.1"}, "time": "2022-03-05T18:01:06+00:00", "require": {"php": "~8.0.0", "composer-runtime-api": "^2.0.0"}, "require-dev": {"composer/composer": "^2.0.0@dev", "doctrine/coding-standard": "^8.1.0", "ext-zip": "^1.15.0", "infection/infection": "dev-master#8d6c4d6b15ec58d3190a78b7774a5d604ec1075a", "phpunit/phpunit": "~9.3.11", "vimeo/psalm": "^4.0.1"}}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "f64411e9a63a35f8645d5fe04a9f55a2df2895c9"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/f64411e9a63a35f8645d5fe04a9f55a2df2895c9", "type": "zip", "shasum": "", "reference": "f64411e9a63a35f8645d5fe04a9f55a2df2895c9"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.3.0"}, "time": "2020-12-23T03:16:25+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "cec64d82ce050aec4d657d1ca949ac2951327388"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/cec64d82ce050aec4d657d1ca949ac2951327388", "type": "zip", "shasum": "", "reference": "cec64d82ce050aec4d657d1ca949ac2951327388"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.2.1"}, "time": "2022-03-05T17:59:29+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "c90d70009e190b5115058573e0ad96144fe554a4"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/c90d70009e190b5115058573e0ad96144fe554a4", "type": "zip", "shasum": "", "reference": "c90d70009e190b5115058573e0ad96144fe554a4"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.2.0"}, "time": "2020-10-21T13:59:39+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "1478d1a6e167d9250f6c41d75bee806b47bec4c3"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/1478d1a6e167d9250f6c41d75bee806b47bec4c3", "type": "zip", "shasum": "", "reference": "1478d1a6e167d9250f6c41d75bee806b47bec4c3"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.1.1"}, "time": "2022-03-05T17:58:25+00:00", "require": {"php": "^7.4.7 || ~8.0.0", "composer-runtime-api": "^2.0.0"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "a7e35c34bc166a5684a1e2f13da7b1d6a821349d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/a7e35c34bc166a5684a1e2f13da7b1d6a821349d", "type": "zip", "shasum": "", "reference": "a7e35c34bc166a5684a1e2f13da7b1d6a821349d"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.1.0"}, "time": "2020-10-21T13:48:04+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "891d4cc6e9042e7ad22d8080f6d4b0bffdfb950d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/891d4cc6e9042e7ad22d8080f6d4b0bffdfb950d", "type": "zip", "shasum": "", "reference": "891d4cc6e9042e7ad22d8080f6d4b0bffdfb950d"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.0.1"}, "time": "2022-03-05T17:56:08+00:00", "require": {"php": "^7.4.7", "composer-runtime-api": "^2.0.0"}, "require-dev": {"composer/composer": "^2.0.0@dev", "doctrine/coding-standard": "^8.1.0", "ext-zip": "^1.15.0", "infection/infection": "^0.17.2", "phpunit/phpunit": "^9.2.6", "vimeo/psalm": "^3.12.2"}, "replace": "__unset"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "e7f689dc7b2469d855f55cbf01b5db9840d49b80"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/e7f689dc7b2469d855f55cbf01b5db9840d49b80", "type": "zip", "shasum": "", "reference": "e7f689dc7b2469d855f55cbf01b5db9840d49b80"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/2.0.0"}, "time": "2020-08-21T16:28:48+00:00"}, {"description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "f51ff2b2b49baaa302d6bf71880e4d8b5acd7015"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/f51ff2b2b49baaa302d6bf71880e4d8b5acd7015", "type": "zip", "shasum": "", "reference": "f51ff2b2b49baaa302d6bf71880e4d8b5acd7015"}, "type": "composer-plugin", "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.11.x"}, "time": "2020-08-21T12:16:47+00:00", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.99.x-dev"}}, "require": {"php": "^7.4.7", "composer-plugin-api": "^2.0.0", "composer-runtime-api": "^2.0.0"}, "require-dev": {"composer/composer": "^2.0.0@dev", "doctrine/coding-standard": "^8.1.0", "ext-zip": "^1.15.0", "infection/infection": "^0.16.4", "phpunit/phpunit": "^9.1.5", "vimeo/psalm": "^3.12.2"}}, {"version": "1.10.2", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "e19f578f124541b9eb89bd09d53983a7da93e0d7"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/e19f578f124541b9eb89bd09d53983a7da93e0d7", "type": "zip", "shasum": "", "reference": "e19f578f124541b9eb89bd09d53983a7da93e0d7"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.10.2"}, "time": "2020-08-18T10:30:56+00:00"}, {"version": "1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "eac17b0f319021c7c1d4e81e4485bda8959c3ec3"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/eac17b0f319021c7c1d4e81e4485bda8959c3ec3", "type": "zip", "shasum": "", "reference": "eac17b0f319021c7c1d4e81e4485bda8959c3ec3"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.10.1"}, "time": "2020-08-18T10:03:46+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "80f88b35ff45a0fdccf852d4788bb79bb67c3817"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/80f88b35ff45a0fdccf852d4788bb79bb67c3817", "type": "zip", "shasum": "", "reference": "80f88b35ff45a0fdccf852d4788bb79bb67c3817"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.10.0"}, "time": "2020-07-11T19:45:58+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "94c9d42a466c57f91390cdd49c81313264f49d85"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/94c9d42a466c57f91390cdd49c81313264f49d85", "type": "zip", "shasum": "", "reference": "94c9d42a466c57f91390cdd49c81313264f49d85"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.9.0"}, "time": "2020-06-22T14:15:44+00:00", "require": {"php": "^7.4.0", "composer-plugin-api": "^1.1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^9.1.1", "infection/infection": "^0.15.3", "composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.15.0", "doctrine/coding-standard": "^7.0.2", "vimeo/psalm": "^3.9.3"}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "421679846270a5772534828013a93be709fb13df"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/421679846270a5772534828013a93be709fb13df", "type": "zip", "shasum": "", "reference": "421679846270a5772534828013a93be709fb13df"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.8.0"}, "time": "2020-04-06T17:43:35+00:00", "require-dev": {"phpunit/phpunit": "^9.0.1", "infection/infection": "^0.15.3", "composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.15.0", "doctrine/coding-standard": "^7.0.2", "vimeo/psalm": "^3.9.3"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "651c372efc914aea8223e049f85afaf65e09ba23"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/651c372efc914aea8223e049f85afaf65e09ba23", "type": "zip", "shasum": "", "reference": "651c372efc914aea8223e049f85afaf65e09ba23"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/master"}, "time": "2020-03-06T11:34:16+00:00", "require": {"php": "^7.4.0", "composer-plugin-api": "^1.1.0"}, "require-dev": {"phpunit/phpunit": "^9.0.1", "infection/infection": "^0.15.3", "composer/composer": "^1.9.3", "ext-zip": "^1.15.0", "doctrine/coding-standard": "^7.0.2", "vimeo/psalm": "^3.9.3"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "e511a3fd8a8796c92ea78dd7c447423463966298"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/e511a3fd8a8796c92ea78dd7c447423463966298", "type": "zip", "shasum": "", "reference": "e511a3fd8a8796c92ea78dd7c447423463966298"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.6.0"}, "time": "2020-03-05T11:23:05+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "1d32342b8c1eb27353c8887c366147b4c2da673c"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/1d32342b8c1eb27353c8887c366147b4c2da673c", "type": "zip", "shasum": "", "reference": "1d32342b8c1eb27353c8887c366147b4c2da673c"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.5.1"}, "time": "2019-07-17T15:49:50+00:00", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.6.x-dev"}}, "require": {"php": "^7.3.0", "composer-plugin-api": "^1.0.0"}, "require-dev": {"phpunit/phpunit": "^8.2.5", "infection/infection": "^0.13.4", "composer/composer": "^1.8.6", "ext-zip": "*", "doctrine/coding-standard": "^6.0.0", "vimeo/psalm": "^3.4.9"}, "funding": "__unset"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "da0d9d34cd5df6b35756a00827aaeca7d903868d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/da0d9d34cd5df6b35756a00827aaeca7d903868d", "type": "zip", "shasum": "", "reference": "da0d9d34cd5df6b35756a00827aaeca7d903868d"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.5.0"}, "time": "2019-07-17T05:00:30+00:00"}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "type": "zip", "shasum": "", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.4.2"}, "time": "2019-11-15T16:17:10+00:00", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": "^7.1.0", "composer-plugin-api": "^1.0.0"}, "require-dev": {"phpunit/phpunit": "^7.5.17", "infection/infection": "^0.7.1", "composer/composer": "^1.6.3", "ext-zip": "*", "doctrine/coding-standard": "^5.0.1"}}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "7ca61c24dc301cc9d47d6fb459b3d39f2d54b7e5"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/7ca61c24dc301cc9d47d6fb459b3d39f2d54b7e5", "type": "zip", "shasum": "", "reference": "7ca61c24dc301cc9d47d6fb459b3d39f2d54b7e5"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.4.1"}, "time": "2019-11-14T14:22:47+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "a4d4b60d0e60da2487bd21a2c6ac089f85570dbb"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/a4d4b60d0e60da2487bd21a2c6ac089f85570dbb", "type": "zip", "shasum": "", "reference": "a4d4b60d0e60da2487bd21a2c6ac089f85570dbb"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/master"}, "time": "2019-02-21T12:16:21+00:00", "require-dev": {"phpunit/phpunit": "^7.0.0", "infection/infection": "^0.7.1", "composer/composer": "^1.6.3", "ext-zip": "*", "doctrine/coding-standard": "^5.0.1"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "4489d5002c49d55576fa0ba786f42dbb009be46f"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/4489d5002c49d55576fa0ba786f42dbb009be46f", "type": "zip", "shasum": "", "reference": "4489d5002c49d55576fa0ba786f42dbb009be46f"}, "time": "2018-02-05T13:05:30+00:00", "require-dev": {"phpunit/phpunit": "^7.0.0", "infection/infection": "^0.7.1", "composer/composer": "^1.6.3", "ext-zip": "*"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "ad8a245decad4897cc6b432743913dad0d69753c"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/ad8a245decad4897cc6b432743913dad0d69753c", "type": "zip", "shasum": "", "reference": "ad8a245decad4897cc6b432743913dad0d69753c"}, "time": "2017-11-24T11:07:03+00:00", "require": {"php": "~7.0", "composer-plugin-api": "^1.0"}, "require-dev": {"phpunit/phpunit": "^6.4", "humbug/humbug": "dev-master", "composer/composer": "^1.3", "ext-zip": "*"}}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "72b226d2957e9e6a9ed09aeaa29cabd840d1a3b7"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/72b226d2957e9e6a9ed09aeaa29cabd840d1a3b7", "type": "zip", "shasum": "", "reference": "72b226d2957e9e6a9ed09aeaa29cabd840d1a3b7"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.1.3"}, "time": "2017-09-06T15:24:43+00:00", "require-dev": {"phpunit/phpunit": "^5.7.5", "humbug/humbug": "dev-master", "composer/composer": "^1.3", "ext-zip": "*"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "51e867c70f0799790b3e82276875414ce13daaca"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/51e867c70f0799790b3e82276875414ce13daaca", "type": "zip", "shasum": "", "reference": "51e867c70f0799790b3e82276875414ce13daaca"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/master"}, "time": "2016-12-30T09:49:15+00:00", "require-dev": {"phpunit/phpunit": "^5.4.7", "composer/composer": "^1.3", "ext-zip": "*"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "4b2bfc8128db95b533303942b0d5b332bffa07c6"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/4b2bfc8128db95b533303942b0d5b332bffa07c6", "type": "zip", "shasum": "", "reference": "4b2bfc8128db95b533303942b0d5b332bffa07c6"}, "time": "2016-07-25T07:13:56+00:00", "require-dev": {"phpunit/phpunit": "^5.4.7", "composer/composer": "^1.2.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "510c1e5065c45e4318ddf86faafeda9f69e33014"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/510c1e5065c45e4318ddf86faafeda9f69e33014", "type": "zip", "shasum": "", "reference": "510c1e5065c45e4318ddf86faafeda9f69e33014"}, "time": "2016-07-22T14:47:43+00:00", "autoload": {"files": ["src/PackageVersions/Versions.php"], "psr-4": {"PackageVersions\\": "src/PackageVersions"}}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "7e9aa662b8405d6ee09844f66d7f32596902b538"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/7e9aa662b8405d6ee09844f66d7f32596902b538", "type": "zip", "shasum": "", "reference": "7e9aa662b8405d6ee09844f66d7f32596902b538"}, "time": "2016-04-23T17:52:40+00:00", "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "require-dev": {"phpunit/phpunit": "^5.2.8", "composer/composer": "^1.0.0-ALPHA11@ALPHA"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "a108adf44c4663bfb72858ea2fcd174acddd317e"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/a108adf44c4663bfb72858ea2fcd174acddd317e", "type": "zip", "shasum": "", "reference": "a108adf44c4663bfb72858ea2fcd174acddd317e"}, "time": "2016-02-26T14:00:23+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "25f302d5c9a83bb9f62108d9183fc293c0ffe2ca"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/25f302d5c9a83bb9f62108d9183fc293c0ffe2ca", "type": "zip", "shasum": "", "reference": "25f302d5c9a83bb9f62108d9183fc293c0ffe2ca"}, "time": "2016-02-24T15:57:02+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "56253310fb94b3086fa123f5f3e7ab76723594aa"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/56253310fb94b3086fa123f5f3e7ab76723594aa", "type": "zip", "shasum": "", "reference": "56253310fb94b3086fa123f5f3e7ab76723594aa"}, "time": "2016-02-02T17:50:05+00:00", "require-dev": {"phpunit/phpunit": "^5.0", "composer/composer": "^1.0.0-ALPHA8@ALPHA"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/Ocramius/PackageVersions.git", "type": "git", "reference": "df93e9e62fb7eb246f52a810b1710f95f332a53d"}, "dist": {"url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/df93e9e62fb7eb246f52a810b1710f95f332a53d", "type": "zip", "shasum": "", "reference": "df93e9e62fb7eb246f52a810b1710f95f332a53d"}, "support": {"issues": "https://github.com/Ocramius/PackageVersions/issues", "source": "https://github.com/Ocramius/PackageVersions/tree/1.0.0"}, "time": "2016-01-25T23:26:33+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 05 Feb 2025 12:31:21 GMT"}