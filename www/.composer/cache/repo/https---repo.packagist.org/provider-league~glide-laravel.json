{"minified": "composer/2.0", "packages": {"league/glide-laravel": [{"name": "league/glide-laravel", "description": "Glide adapter for <PERSON><PERSON>", "keywords": [], "homepage": "http://glide.thephpleague.com", "version": "1.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}], "source": {"url": "https://github.com/thephpleague/glide-laravel.git", "type": "git", "reference": "b525e33e32940f3b047d6ca357131aba0e973e72"}, "dist": {"url": "https://api.github.com/repos/thephpleague/glide-laravel/zipball/b525e33e32940f3b047d6ca357131aba0e973e72", "type": "zip", "shasum": "", "reference": "b525e33e32940f3b047d6ca357131aba0e973e72"}, "type": "library", "support": {"issues": "https://github.com/thephpleague/glide-laravel/issues", "source": "https://github.com/thephpleague/glide-laravel/tree/master"}, "time": "2015-12-26T15:40:35+00:00", "autoload": {"psr-4": {"League\\Glide\\": "src/"}}, "require": {"league/glide-symfony": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9", "phpunit/phpunit": "^4.0"}}]}, "security-advisories": [], "last-modified": "Sat, 23 Mar 2024 06:22:45 GMT"}