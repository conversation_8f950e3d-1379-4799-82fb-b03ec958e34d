{"minified": "composer/2.0", "packages": {"nunomaduro/termwind": [{"name": "nunomaduro/termwind", "description": "Its like Tailwind CSS, but for the console.", "keywords": ["package", "php", "cli", "css", "console", "style"], "homepage": "", "version": "v2.3.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "dfa08f390e509967a15c22493dc0bac5733d9123"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/dfa08f390e509967a15c22493dc0bac5733d9123", "type": "zip", "shasum": "", "reference": "dfa08f390e509967a15c22493dc0bac5733d9123"}, "type": "library", "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.3.1"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "time": "2025-05-08T08:14:37+00:00", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}, "branch-alias": {"dev-2.x": "2.x-dev"}}, "require": {"php": "^8.2", "ext-mbstring": "*", "symfony/console": "^7.2.6"}, "require-dev": {"illuminate/console": "^11.44.7", "laravel/pint": "^1.22.0", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.36.0 || ^3.8.2", "phpstan/phpstan": "^1.12.25", "phpstan/phpstan-strict-rules": "^1.6.2", "symfony/var-dumper": "^7.2.6", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "52915afe6a1044e8b9cee1bcff836fb63acf9cda"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/52915afe6a1044e8b9cee1bcff836fb63acf9cda", "type": "zip", "shasum": "", "reference": "52915afe6a1044e8b9cee1bcff836fb63acf9cda"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.3.0"}, "time": "2024-11-21T10:39:51+00:00", "require": {"php": "^8.2", "ext-mbstring": "*", "symfony/console": "^7.1.8"}, "require-dev": {"illuminate/console": "^11.33.2", "laravel/pint": "^1.18.2", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.36.0", "phpstan/phpstan": "^1.12.11", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^7.1.8", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "42c84e4e8090766bbd6445d06cd6e57650626ea3"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/42c84e4e8090766bbd6445d06cd6e57650626ea3", "type": "zip", "shasum": "", "reference": "42c84e4e8090766bbd6445d06cd6e57650626ea3"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.2.0"}, "time": "2024-10-15T16:15:16+00:00", "require": {"php": "^8.2", "ext-mbstring": "*", "symfony/console": "^7.1.5"}, "require-dev": {"illuminate/console": "^11.28.0", "laravel/pint": "^1.18.1", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.36.0", "phpstan/phpstan": "^1.12.6", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^7.1.5", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "e5f21eade88689536c0cdad4c3cd75f3ed26e01a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/e5f21eade88689536c0cdad4c3cd75f3ed26e01a", "type": "zip", "shasum": "", "reference": "e5f21eade88689536c0cdad4c3cd75f3ed26e01a"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.1.0"}, "time": "2024-09-05T15:25:50+00:00", "require": {"php": "^8.2", "ext-mbstring": "*", "symfony/console": "^7.0.4"}, "require-dev": {"ergebnis/phpstan-rules": "^2.2.0", "illuminate/console": "^11.1.1", "laravel/pint": "^1.15.0", "mockery/mockery": "^1.6.11", "pestphp/pest": "^2.34.6", "phpstan/phpstan": "^1.10.66", "phpstan/phpstan-strict-rules": "^1.5.2", "symfony/var-dumper": "^7.0.4", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "58c4c58cf23df7f498daeb97092e34f5259feb6a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/58c4c58cf23df7f498daeb97092e34f5259feb6a", "type": "zip", "shasum": "", "reference": "58c4c58cf23df7f498daeb97092e34f5259feb6a"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.0.1"}, "time": "2024-03-06T16:17:14+00:00", "require-dev": {"ergebnis/phpstan-rules": "^2.2.0", "illuminate/console": "^11.0.0", "laravel/pint": "^1.14.0", "mockery/mockery": "^1.6.7", "pestphp/pest": "^2.34.1", "phpstan/phpstan": "^1.10.59", "phpstan/phpstan-strict-rules": "^1.5.2", "symfony/var-dumper": "^7.0.4", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "e534f661e09b712e51971e2cf0f662f83116d5ad"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/e534f661e09b712e51971e2cf0f662f83116d5ad", "type": "zip", "shasum": "", "reference": "e534f661e09b712e51971e2cf0f662f83116d5ad"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.0.0"}, "time": "2023-12-08T16:23:40+00:00", "require": {"php": "^8.2", "ext-mbstring": "*", "symfony/console": "^7.0.1"}, "require-dev": {"ergebnis/phpstan-rules": "^2.1.0", "illuminate/console": "^11.0.0", "laravel/pint": "^1.13.7", "mockery/mockery": "^1.6.6", "pestphp/pest": "^2.28.0", "phpstan/phpstan": "^1.10.48", "phpstan/phpstan-strict-rules": "^1.5.2", "symfony/var-dumper": "^7.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "5369ef84d8142c1d87e4ec278711d4ece3cbf301"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/5369ef84d8142c1d87e4ec278711d4ece3cbf301", "type": "zip", "shasum": "", "reference": "5369ef84d8142c1d87e4ec278711d4ece3cbf301"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.17.0"}, "time": "2024-11-21T10:36:35+00:00", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}, "require": {"php": "^8.1", "ext-mbstring": "*", "symfony/console": "^6.4.15"}, "require-dev": {"illuminate/console": "^10.48.24", "illuminate/support": "^10.48.24", "laravel/pint": "^1.18.2", "pestphp/pest": "^2.36.0", "pestphp/pest-plugin-mock": "2.0.0", "phpstan/phpstan": "^1.12.11", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^6.4.15", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "dcf1ec3dfa36137b7ce41d43866644a7ab8fc257"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/dcf1ec3dfa36137b7ce41d43866644a7ab8fc257", "type": "zip", "shasum": "", "reference": "dcf1ec3dfa36137b7ce41d43866644a7ab8fc257"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.16.0"}, "time": "2024-10-15T15:27:12+00:00", "require": {"php": "^8.1", "ext-mbstring": "*", "symfony/console": "^6.4.12"}, "require-dev": {"illuminate/console": "^10.48.22", "illuminate/support": "^10.48.22", "laravel/pint": "^1.18.1", "pestphp/pest": "^2", "pestphp/pest-plugin-mock": "2.0.0", "phpstan/phpstan": "^1.12.6", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^6.4.11", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v1.15.1", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "type": "zip", "shasum": "", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.15.1"}, "time": "2023-02-08T01:06:31+00:00", "require": {"php": "^8.0", "ext-mbstring": "*", "symfony/console": "^5.3.0|^6.0.0"}, "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^1.0.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "594ab862396c16ead000de0c3c38f4a5cbe1938d"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/594ab862396c16ead000de0c3c38f4a5cbe1938d", "type": "zip", "shasum": "", "reference": "594ab862396c16ead000de0c3c38f4a5cbe1938d"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.15.0"}, "time": "2022-12-20T19:00:15+00:00"}, {"version": "v1.14.2", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "9a8218511eb1a0965629ff820dda25985440aefc"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/9a8218511eb1a0965629ff820dda25985440aefc", "type": "zip", "shasum": "", "reference": "9a8218511eb1a0965629ff820dda25985440aefc"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.14.2"}, "time": "2022-10-28T22:51:32+00:00"}, {"version": "v1.14.1", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "86fc30eace93b9b6d4c844ba6de76db84184e01b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/86fc30eace93b9b6d4c844ba6de76db84184e01b", "type": "zip", "shasum": "", "reference": "86fc30eace93b9b6d4c844ba6de76db84184e01b"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.14.1"}, "time": "2022-10-17T15:20:29+00:00"}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "10065367baccf13b6e30f5e9246fa4f63a79eb1d"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/10065367baccf13b6e30f5e9246fa4f63a79eb1d", "type": "zip", "shasum": "", "reference": "10065367baccf13b6e30f5e9246fa4f63a79eb1d"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.14.0"}, "time": "2022-08-01T11:03:24+00:00"}, {"version": "v1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "132a24bd3e8c559e7f14fa14ba1b83772a0f97f8"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/132a24bd3e8c559e7f14fa14ba1b83772a0f97f8", "type": "zip", "shasum": "", "reference": "132a24bd3e8c559e7f14fa14ba1b83772a0f97f8"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.13.0"}, "time": "2022-07-01T15:06:55+00:00", "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^0.2.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "04b73b2fe447e0aba7545b670186c48fceb091b6"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/04b73b2fe447e0aba7545b670186c48fceb091b6", "type": "zip", "shasum": "", "reference": "04b73b2fe447e0aba7545b670186c48fceb091b6"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.12.0"}, "time": "2022-06-27T17:01:56+00:00"}, {"version": "v1.11.1", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "8ae79be19eddeac25e6bf5b9e1696de87d4f1556"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/8ae79be19eddeac25e6bf5b9e1696de87d4f1556", "type": "zip", "shasum": "", "reference": "8ae79be19eddeac25e6bf5b9e1696de87d4f1556"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.11.1"}, "time": "2022-06-17T12:26:22+00:00", "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "styleci/cli": "^1.2.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}}, {"version": "v1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "ef2907d4fa961b28b22bc5da0c31c3b5802ea130"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/ef2907d4fa961b28b22bc5da0c31c3b5802ea130", "type": "zip", "shasum": "", "reference": "ef2907d4fa961b28b22bc5da0c31c3b5802ea130"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.11.0"}, "time": "2022-06-17T11:55:51+00:00"}, {"version": "v1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "b63e00e448af1d418cbb07a683187685a97ca0e6"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/b63e00e448af1d418cbb07a683187685a97ca0e6", "type": "zip", "shasum": "", "reference": "b63e00e448af1d418cbb07a683187685a97ca0e6"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.10.1"}, "time": "2022-05-12T01:36:04+00:00"}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "73f89d44ab85ae1b8b4951d480e754bf2cb5eaf0"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/73f89d44ab85ae1b8b4951d480e754bf2cb5eaf0", "type": "zip", "shasum": "", "reference": "73f89d44ab85ae1b8b4951d480e754bf2cb5eaf0"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.10.0"}, "time": "2022-05-11T22:12:12+00:00"}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "7d7c5f846158a764bdd0cd943685c381de9be656"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/7d7c5f846158a764bdd0cd943685c381de9be656", "type": "zip", "shasum": "", "reference": "7d7c5f846158a764bdd0cd943685c381de9be656"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.9.0"}, "time": "2022-05-03T00:11:55+00:00"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "84e689ed0daeedb0a20e8aaff6aa2f6ac365256c"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/84e689ed0daeedb0a20e8aaff6aa2f6ac365256c", "type": "zip", "shasum": "", "reference": "84e689ed0daeedb0a20e8aaff6aa2f6ac365256c"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.8.0"}, "time": "2022-04-22T12:02:12+00:00"}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "f962321d81169f0cd9832aa10d083f022f1ada3e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/f962321d81169f0cd9832aa10d083f022f1ada3e", "type": "zip", "shasum": "", "reference": "f962321d81169f0cd9832aa10d083f022f1ada3e"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.7.0"}, "time": "2022-03-30T11:19:29+00:00"}, {"version": "v1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "ef6a5a86c5fad3c7c9bffdc012dc006a0e042548"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/ef6a5a86c5fad3c7c9bffdc012dc006a0e042548", "type": "zip", "shasum": "", "reference": "ef6a5a86c5fad3c7c9bffdc012dc006a0e042548"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.6.2"}, "time": "2022-03-18T13:07:34+00:00"}, {"version": "v1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "778f0802826c1ed841b7778da2796aa836aec000"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/778f0802826c1ed841b7778da2796aa836aec000", "type": "zip", "shasum": "", "reference": "778f0802826c1ed841b7778da2796aa836aec000"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.6.1"}, "time": "2022-03-17T18:45:55+00:00"}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "2c3b308811d1f2a33d5305b18ef16d94359ba40d"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/2c3b308811d1f2a33d5305b18ef16d94359ba40d", "type": "zip", "shasum": "", "reference": "2c3b308811d1f2a33d5305b18ef16d94359ba40d"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.6.0"}, "time": "2022-02-24T17:35:42+00:00"}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "d4fd9630000c86b124020f68fd17190186335dff"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/d4fd9630000c86b124020f68fd17190186335dff", "type": "zip", "shasum": "", "reference": "d4fd9630000c86b124020f68fd17190186335dff"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.5.0"}, "time": "2022-02-14T08:39:10+00:00", "require-dev": {"ergebnis/phpstan-rules": "^0.15.3", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-strict-rules": "^0.12.11", "styleci/cli": "^1.2.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^0.12.1"}}, {"version": "v1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "893a9e5a8fe31b538cd780a43e6b6f8ce0a786ad"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/893a9e5a8fe31b538cd780a43e6b6f8ce0a786ad", "type": "zip", "shasum": "", "reference": "893a9e5a8fe31b538cd780a43e6b6f8ce0a786ad"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.4.3"}, "time": "2022-02-03T17:22:57+00:00", "require-dev": {"ergebnis/phpstan-rules": "^0.15.3", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-strict-rules": "^0.12.11", "styleci/cli": "^1.2.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^0.12.1"}, "extra": "__unset"}, {"version": "v1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "9a26ce3de12e8db688c930d95b324c795a8a48de"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/9a26ce3de12e8db688c930d95b324c795a8a48de", "type": "zip", "shasum": "", "reference": "9a26ce3de12e8db688c930d95b324c795a8a48de"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.4.2"}, "time": "2022-01-29T18:01:44+00:00"}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "9eec47b14b58687566c3fc4b27e98725bb9fe1ab"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/9eec47b14b58687566c3fc4b27e98725bb9fe1ab", "type": "zip", "shasum": "", "reference": "9eec47b14b58687566c3fc4b27e98725bb9fe1ab"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.4.1"}, "time": "2022-01-29T15:19:22+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "0f074a91ee26083234082205f1be8f7abc02b169"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/0f074a91ee26083234082205f1be8f7abc02b169", "type": "zip", "shasum": "", "reference": "0f074a91ee26083234082205f1be8f7abc02b169"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.4.0"}, "time": "2022-01-26T11:40:00+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "2a047696be3343fda0e4df8f47e1531a0a0bfae5"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/2a047696be3343fda0e4df8f47e1531a0a0bfae5", "type": "zip", "shasum": "", "reference": "2a047696be3343fda0e4df8f47e1531a0a0bfae5"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.3.0"}, "time": "2022-01-12T17:14:49+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "a03cc7067ea9ee95ce69aa10359025ef631b9c07"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/a03cc7067ea9ee95ce69aa10359025ef631b9c07", "type": "zip", "shasum": "", "reference": "a03cc7067ea9ee95ce69aa10359025ef631b9c07"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.2.0"}, "time": "2021-12-16T00:58:11+00:00", "require-dev": {"ergebnis/phpstan-rules": "^0.15.3", "pestphp/pest": "^2.0.0", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-strict-rules": "^0.12.11", "styleci/cli": "^1.2.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^0.12.1"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "53300e35d09510e02f982fd504e4d5c2e486f4e6"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/53300e35d09510e02f982fd504e4d5c2e486f4e6", "type": "zip", "shasum": "", "reference": "53300e35d09510e02f982fd504e4d5c2e486f4e6"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.1.1"}, "time": "2021-12-16T00:51:44+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "d4307db8155620cd39a34e8877d3dc8df45b0441"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/d4307db8155620cd39a34e8877d3dc8df45b0441", "type": "zip", "shasum": "", "reference": "d4307db8155620cd39a34e8877d3dc8df45b0441"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.1.0"}, "time": "2021-12-06T20:01:30+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/termwind.git", "type": "git", "reference": "0454f539f0c5239136e6a22ecc9aac5dd203658c"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/termwind/zipball/0454f539f0c5239136e6a22ecc9aac5dd203658c", "type": "zip", "shasum": "", "reference": "0454f539f0c5239136e6a22ecc9aac5dd203658c"}, "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.0.0"}, "time": "2021-11-28T05:11:02+00:00", "require": {"php": "^8.0", "ext-mbstring": "*", "symfony/console": "^5.3.0"}, "require-dev": {"ergebnis/phpstan-rules": "^0.15.3", "pestphp/pest": "^1.20.0", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-strict-rules": "^0.12.11", "styleci/cli": "^1.2", "symfony/var-dumper": "^5.2.7", "thecodingmachine/phpstan-strict-rules": "^0.12.1"}}]}, "security-advisories": [], "last-modified": "Thu, 08 May 2025 08:24:02 GMT"}