{"minified": "composer/2.0", "packages": {"symfony/service-contracts": [{"name": "symfony/service-contracts", "description": "Generic abstractions related to writing services", "keywords": ["interfaces", "standards", "interoperability", "contracts", "abstractions", "decoupling"], "homepage": "https://symfony.com", "version": "v3.6.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "type": "zip", "shasum": "", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "type": "library", "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}}, {"version": "v3.6.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0-BETA1"}}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "type": "zip", "shasum": "", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "type": "zip", "shasum": "", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v3.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "c744140e155c9dc1dd6ad152e0a57367f3a5bab0"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/c744140e155c9dc1dd6ad152e0a57367f3a5bab0", "type": "zip", "shasum": "", "reference": "c744140e155c9dc1dd6ad152e0a57367f3a5bab0"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.5"}, "time": "2025-04-25T09:18:22+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.4-dev"}}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0"}}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "465bd9d58590043f2a7a4a6484b67e4c30baef55"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/465bd9d58590043f2a7a4a6484b67e4c30baef55", "type": "zip", "shasum": "", "reference": "465bd9d58590043f2a7a4a6484b67e4c30baef55"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.3"}, "time": "2024-09-25T14:18:03+00:00"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "11bbf19a0fb7b36345861e85c5768844c552906e"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/11bbf19a0fb7b36345861e85c5768844c552906e", "type": "zip", "shasum": "", "reference": "11bbf19a0fb7b36345861e85c5768844c552906e"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.2"}, "time": "2023-12-19T21:51:00+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "fe07cbc8d837f60caf7018068e350cc5163681a0"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/fe07cbc8d837f60caf7018068e350cc5163681a0", "type": "zip", "shasum": "", "reference": "fe07cbc8d837f60caf7018068e350cc5163681a0"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.1"}, "time": "2023-12-26T14:02:43+00:00"}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "b3313c2dbffaf71c8de2934e2ea56ed2291a3838"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/b3313c2dbffaf71c8de2934e2ea56ed2291a3838", "type": "zip", "shasum": "", "reference": "b3313c2dbffaf71c8de2934e2ea56ed2291a3838"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.0"}, "time": "2023-07-30T20:28:31+00:00", "require": {"php": ">=8.1", "psr/container": "^2.0"}}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "40da9cc13ec349d9e4966ce18b5fbcd724ab10a4"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/40da9cc13ec349d9e4966ce18b5fbcd724ab10a4", "type": "zip", "shasum": "", "reference": "40da9cc13ec349d9e4966ce18b5fbcd724ab10a4"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.3.0"}, "time": "2023-05-23T14:45:45+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "a8c9cedf55f314f3a186041d19537303766df09a"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/a8c9cedf55f314f3a186041d19537303766df09a", "type": "zip", "shasum": "", "reference": "a8c9cedf55f314f3a186041d19537303766df09a"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.2.1"}, "time": "2023-03-01T10:32:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.3-dev"}}, "suggest": {"symfony/service-implementation": ""}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "aac98028c69df04ee77eb69b96b86ee51fbf4b75"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/aac98028c69df04ee77eb69b96b86ee51fbf4b75", "type": "zip", "shasum": "", "reference": "aac98028c69df04ee77eb69b96b86ee51fbf4b75"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.2.0"}, "time": "2022-11-25T10:21:52+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "925e713fe8fcacf6bc05e936edd8dd5441a21239"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/925e713fe8fcacf6bc05e936edd8dd5441a21239", "type": "zip", "shasum": "", "reference": "925e713fe8fcacf6bc05e936edd8dd5441a21239"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.1.1"}, "time": "2022-05-30T19:18:58+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.1-dev"}}}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "d66cd8ab656780f62c4215b903a420eb86358957"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/d66cd8ab656780f62c4215b903a420eb86358957", "type": "zip", "shasum": "", "reference": "d66cd8ab656780f62c4215b903a420eb86358957"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.1.0"}, "time": "2022-05-07T08:07:09+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "type": "zip", "shasum": "", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.0.2"}, "time": "2022-05-30T19:17:58+00:00", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.0.2", "psr/container": "^2.0"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "e517458f278c2131ca9f262f8fbaf01410f2c65c"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/e517458f278c2131ca9f262f8fbaf01410f2c65c", "type": "zip", "shasum": "", "reference": "e517458f278c2131ca9f262f8fbaf01410f2c65c"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.0.1"}, "time": "2022-03-13T20:10:05+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "36715ebf9fb9db73db0cb24263c79077c6fe8603"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/36715ebf9fb9db73db0cb24263c79077c6fe8603", "type": "zip", "shasum": "", "reference": "36715ebf9fb9db73db0cb24263c79077c6fe8603"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.0.0"}, "time": "2021-11-04T17:53:12+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/f37b419f7aea2e9abf10abd261832cace12e3300", "type": "zip", "shasum": "", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.4"}, "time": "2024-09-25T14:11:13+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/a2329596ddc8fd568900e3fc76cba42489ecc7f3", "type": "zip", "shasum": "", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.3"}, "time": "2023-04-21T15:04:16+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "type": "zip", "shasum": "", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "time": "2022-05-30T19:17:29+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "24d9dc654b83e91aa59f9d167b131bc3b5bea24c"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/24d9dc654b83e91aa59f9d167b131bc3b5bea24c", "type": "zip", "shasum": "", "reference": "24d9dc654b83e91aa59f9d167b131bc3b5bea24c"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.1"}, "time": "2022-03-13T20:07:29+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "1ab11b933cd6bc5464b08e81e2c5b07dec58b0fc"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/1ab11b933cd6bc5464b08e81e2c5b07dec58b0fc", "type": "zip", "shasum": "", "reference": "1ab11b933cd6bc5464b08e81e2c5b07dec58b0fc"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.0"}, "time": "2021-11-04T16:48:04+00:00", "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1"}}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "d664541b99d6fb0247ec5ff32e87238582236204"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/d664541b99d6fb0247ec5ff32e87238582236204", "type": "zip", "shasum": "", "reference": "d664541b99d6fb0247ec5ff32e87238582236204"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.4.1"}, "time": "2021-11-04T16:37:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.4-dev"}}, "require": {"php": ">=7.2.5", "psr/container": "^1.1"}}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "f040a30e04b57fbcc9c6cbcf4dbaa96bd318b9bb"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/f040a30e04b57fbcc9c6cbcf4dbaa96bd318b9bb", "type": "zip", "shasum": "", "reference": "f040a30e04b57fbcc9c6cbcf4dbaa96bd318b9bb"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.4.0"}, "time": "2021-04-01T10:43:52+00:00", "conflict": "__unset"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/d15da7ba4957ffb8f1747218be9e1a121fd298a1", "type": "zip", "shasum": "", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "time": "2020-09-07T11:33:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.2-dev"}}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "58c7475e5457c5492c26cc740cc0ad7464be9442"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/58c7475e5457c5492c26cc740cc0ad7464be9442", "type": "zip", "shasum": "", "reference": "58c7475e5457c5492c26cc740cc0ad7464be9442"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.1.3"}, "time": "2020-07-06T13:23:11+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "66a8f0957a3ca54e4f724e49028ab19d75a8918b"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/66a8f0957a3ca54e4f724e49028ab19d75a8918b", "type": "zip", "shasum": "", "reference": "66a8f0957a3ca54e4f724e49028ab19d75a8918b"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.1.2"}, "time": "2020-05-20T17:43:50+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "ff3652f695b7bd4d6da14bbe651d26b3d6c2bbcc"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/ff3652f695b7bd4d6da14bbe651d26b3d6c2bbcc", "type": "zip", "shasum": "", "reference": "ff3652f695b7bd4d6da14bbe651d26b3d6c2bbcc"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.1.1"}, "time": "2020-05-08T21:41:03+00:00", "require": {"php": "^7.2.5", "psr/container": "^1.0"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "13111afaf14f1597a4d97a36a36cbd7a4fd95166"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/13111afaf14f1597a4d97a36a36cbd7a4fd95166", "type": "zip", "shasum": "", "reference": "13111afaf14f1597a4d97a36a36cbd7a4fd95166"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.1.0"}, "time": "2020-03-18T08:01:26+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "144c5e51266b281231e947b51223ba14acf1a749"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/144c5e51266b281231e947b51223ba14acf1a749", "type": "zip", "shasum": "", "reference": "144c5e51266b281231e947b51223ba14acf1a749"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.0.1"}, "time": "2019-11-18T17:27:11+00:00", "funding": "__unset"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "9d99e1556417bf227a62e14856d630672bf10eaf"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/9d99e1556417bf227a62e14856d630672bf10eaf", "type": "zip", "shasum": "", "reference": "9d99e1556417bf227a62e14856d630672bf10eaf"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "time": "2019-11-09T09:18:34+00:00", "require": {"php": "^7.2.9", "psr/container": "^1.0"}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "afa00c500c2d6aea6e3b2f4862355f507bc5ebb4"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/afa00c500c2d6aea6e3b2f4862355f507bc5ebb4", "type": "zip", "shasum": "", "reference": "afa00c500c2d6aea6e3b2f4862355f507bc5ebb4"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-27T14:01:05+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "1.1-dev"}}, "require": {"php": ">=7.1.3", "psr/container": "^1.0"}}, {"version": "v1.1.13", "version_normalized": "********", "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.13"}}, {"version": "v1.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "eedb374f02031714a48848758a27812f3eca317a"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/eedb374f02031714a48848758a27812f3eca317a", "type": "zip", "shasum": "", "reference": "eedb374f02031714a48848758a27812f3eca317a"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.12"}, "time": "2022-03-09T13:39:03+00:00"}, {"version": "v1.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "633df678bec3452e04a7b0337c9bcfe7354124b3"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/633df678bec3452e04a7b0337c9bcfe7354124b3", "type": "zip", "shasum": "", "reference": "633df678bec3452e04a7b0337c9bcfe7354124b3"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.11"}, "time": "2021-11-04T13:32:43+00:00"}, {"version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "b776d18b303a39f56c63747bcb977ad4b27aca26"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/b776d18b303a39f56c63747bcb977ad4b27aca26", "type": "zip", "shasum": "", "reference": "b776d18b303a39f56c63747bcb977ad4b27aca26"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.9"}, "time": "2020-07-06T13:19:58+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "ffc7f5692092df31515df2a5ecf3b7302b3ddacf"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/ffc7f5692092df31515df2a5ecf3b7302b3ddacf", "type": "zip", "shasum": "", "reference": "ffc7f5692092df31515df2a5ecf3b7302b3ddacf"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.8"}, "time": "2019-10-14T12:27:06+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "require": {"php": "^7.1.3", "psr/container": "^1.0"}, "funding": "__unset"}, {"version": "v1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "ffcde9615dc5bb4825b9f6aed07716f1f57faae0"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/ffcde9615dc5bb4825b9f6aed07716f1f57faae0", "type": "zip", "shasum": "", "reference": "ffcde9615dc5bb4825b9f6aed07716f1f57faae0"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.7"}, "time": "2019-09-17T11:12:18+00:00"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "ea7263d6b6d5f798b56a45a5b8d686725f2719a3"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/ea7263d6b6d5f798b56a45a5b8d686725f2719a3", "type": "zip", "shasum": "", "reference": "ea7263d6b6d5f798b56a45a5b8d686725f2719a3"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "time": "2019-08-20T14:44:19+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "f391a00de78ec7ec8cf5cdcdae59ec7b883edb8d"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/f391a00de78ec7ec8cf5cdcdae59ec7b883edb8d", "type": "zip", "shasum": "", "reference": "f391a00de78ec7ec8cf5cdcdae59ec7b883edb8d"}, "time": "2019-06-13T11:15:36+00:00"}, {"version": "v1.1.4", "version_normalized": "*******"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/service-contracts.git", "type": "git", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0"}, "dist": {"url": "https://api.github.com/repos/symfony/service-contracts/zipball/191afdcb5804db960d26d8566b7e9a2843cab3a0", "type": "zip", "shasum": "", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0"}, "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.2"}, "time": "2019-05-28T07:50:59+00:00", "require": {"php": "^7.1.3"}, "suggest": {"psr/container": "", "symfony/service-implementation": ""}}, {"version": "v1.1.1", "version_normalized": "*******", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}]}]}, "security-advisories": [], "last-modified": "Sun, 25 May 2025 20:28:21 GMT"}