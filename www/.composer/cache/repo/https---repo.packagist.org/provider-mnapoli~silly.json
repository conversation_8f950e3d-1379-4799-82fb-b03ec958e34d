{"minified": "composer/2.0", "packages": {"mnapoli/silly": [{"name": "mnapoli/silly", "description": "Silly CLI micro-framework based on Symfony Console", "keywords": ["framework", "cli", "console", "micro-framework", "silly", "PSR-11"], "homepage": "", "version": "1.9.1", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "913d0bc5ff28c5a999d7476191e19be3d1afa04c"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/913d0bc5ff28c5a999d7476191e19be3d1afa04c", "type": "zip", "shasum": "", "reference": "913d0bc5ff28c5a999d7476191e19be3d1afa04c"}, "type": "library", "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.9.1"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/mnapoli/silly", "type": "tidelift"}], "time": "2024-10-30T10:26:44+00:00", "autoload": {"psr-4": {"Silly\\": "src/"}}, "require": {"php": ">=7.4", "symfony/console": "~3.0|~4.0|~5.0|~6.0|~7.0", "php-di/invoker": "~2.0", "psr/container": "^1.0|^2.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7|^8|^9|^10", "mnapoli/phpunit-easymock": "~1.0", "friendsofphp/php-cs-fixer": "^2.12"}}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "fdb69ac9335b4c83f70baf433eae91e691901f1c"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/fdb69ac9335b4c83f70baf433eae91e691901f1c", "type": "zip", "shasum": "", "reference": "fdb69ac9335b4c83f70baf433eae91e691901f1c"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.9.0"}, "time": "2024-01-18T15:47:39+00:00"}, {"version": "1.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "895b189e3a59a5ea47aff6965a92180fdf417ced"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/895b189e3a59a5ea47aff6965a92180fdf417ced", "type": "zip", "shasum": "", "reference": "895b189e3a59a5ea47aff6965a92180fdf417ced"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.8.3"}, "time": "2023-07-17T05:50:57+00:00", "require": {"php": ">=7.4", "symfony/console": "~3.0|~4.0|~5.0|~6.0", "php-di/invoker": "~2.0", "psr/container": "^1.0|^2.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7|^8|^9", "mnapoli/phpunit-easymock": "~1.0", "friendsofphp/php-cs-fixer": "^2.12"}}, {"version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "a0549aeffa9a7d9eac11662acc286d946697a3e4"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/a0549aeffa9a7d9eac11662acc286d946697a3e4", "type": "zip", "shasum": "", "reference": "a0549aeffa9a7d9eac11662acc286d946697a3e4"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.8.2"}, "time": "2023-05-10T10:16:25+00:00"}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "3ff92aab46f365eb341c581dcba074f812420827"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/3ff92aab46f365eb341c581dcba074f812420827", "type": "zip", "shasum": "", "reference": "3ff92aab46f365eb341c581dcba074f812420827"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.8.1"}, "time": "2022-09-06T14:21:36+00:00"}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "c35cd1df043bedd229c6d8a7c14f0e7b9b3c1a27"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/c35cd1df043bedd229c6d8a7c14f0e7b9b3c1a27", "type": "zip", "shasum": "", "reference": "c35cd1df043bedd229c6d8a7c14f0e7b9b3c1a27"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.8.0"}, "time": "2022-02-20T19:29:23+00:00"}, {"version": "1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "e437baa502c3e1691d342374e71446d4bac1cad5"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/e437baa502c3e1691d342374e71446d4bac1cad5", "type": "zip", "shasum": "", "reference": "e437baa502c3e1691d342374e71446d4bac1cad5"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.7.3"}, "time": "2021-12-13T09:21:21+00:00", "require": {"php": ">=7.0", "symfony/console": "~3.0|~4.0|~5.0", "php-di/invoker": "~2.0", "psr/container": "^1.0|^2.0"}, "require-dev": {"phpunit/phpunit": "~6.4", "mnapoli/phpunit-easymock": "~1.0", "friendsofphp/php-cs-fixer": "^2.12"}}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "66807f87abd2ab8e5708754d70b4b601f5614c32"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/66807f87abd2ab8e5708754d70b4b601f5614c32", "type": "zip", "shasum": "", "reference": "66807f87abd2ab8e5708754d70b4b601f5614c32"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.7.2"}, "time": "2019-11-26T20:07:27+00:00", "require": {"php": ">=7.0", "symfony/console": "~3.0|~4.0|~5.0", "php-di/invoker": "~2.0", "psr/container": "^1.0"}, "funding": "__unset"}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "5f7f34f4db75685e6fe7d652c12d9a7b6e8034d6"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/5f7f34f4db75685e6fe7d652c12d9a7b6e8034d6", "type": "zip", "shasum": "", "reference": "5f7f34f4db75685e6fe7d652c12d9a7b6e8034d6"}, "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/master"}, "time": "2018-12-26T21:17:30+00:00", "require": {"php": ">=7.0", "symfony/console": "~3.0|~4.0", "php-di/invoker": "~2.0", "psr/container": "^1.0"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "bd6d1ec6d1644a5dfd5325492f2f8d36037da32c"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/bd6d1ec6d1644a5dfd5325492f2f8d36037da32c", "type": "zip", "shasum": "", "reference": "bd6d1ec6d1644a5dfd5325492f2f8d36037da32c"}, "time": "2017-11-18T14:01:30+00:00", "require-dev": {"phpunit/phpunit": "~6.4", "mnapoli/phpunit-easymock": "~1.0"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "2b0fbbd009d3a9a05b866db95a168f255b913e7c"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/2b0fbbd009d3a9a05b866db95a168f255b913e7c", "type": "zip", "shasum": "", "reference": "2b0fbbd009d3a9a05b866db95a168f255b913e7c"}, "time": "2017-05-22T20:20:32+00:00", "require": {"php": ">=7.0", "symfony/console": "~3.0", "php-di/invoker": "~2.0", "psr/container": "^1.0"}, "require-dev": {"phpunit/phpunit": "~5.0", "mnapoli/phpunit-easymock": "~0.2.0"}}, {"keywords": ["framework", "cli", "console", "micro-framework", "silly"], "version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "807df4a844972ac74d07518c3a0aa9cb575b470b"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/807df4a844972ac74d07518c3a0aa9cb575b470b", "type": "zip", "shasum": "", "reference": "807df4a844972ac74d07518c3a0aa9cb575b470b"}, "time": "2016-09-16T11:44:03+00:00", "require": {"php": ">=5.5", "symfony/console": "~2.6|~3.0", "php-di/invoker": "~1.2", "container-interop/container-interop": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.1.0"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "69ff63c3b5478eb61259768b3cf0915ce8230a9a"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/69ff63c3b5478eb61259768b3cf0915ce8230a9a", "type": "zip", "shasum": "", "reference": "69ff63c3b5478eb61259768b3cf0915ce8230a9a"}, "time": "2016-08-31T11:03:19+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "2f53397797d992fa2862105992b198b25679d283"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/2f53397797d992fa2862105992b198b25679d283", "type": "zip", "shasum": "", "reference": "2f53397797d992fa2862105992b198b25679d283"}, "time": "2016-08-13T18:47:51+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "b95e1be1a669991c9c1c532b9efe985f1063ece7"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/b95e1be1a669991c9c1c532b9efe985f1063ece7", "type": "zip", "shasum": "", "reference": "b95e1be1a669991c9c1c532b9efe985f1063ece7"}, "time": "2016-08-01T11:29:53+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "6b3fbe09e1e628233ad06aa1db6a0ce2991c0ccf"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/6b3fbe09e1e628233ad06aa1db6a0ce2991c0ccf", "type": "zip", "shasum": "", "reference": "6b3fbe09e1e628233ad06aa1db6a0ce2991c0ccf"}, "time": "2016-02-27T10:11:44+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "185991dc022d0566ba603e91ce68facfbf007422"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/185991dc022d0566ba603e91ce68facfbf007422", "type": "zip", "shasum": "", "reference": "185991dc022d0566ba603e91ce68facfbf007422"}, "time": "2016-02-01T21:26:07+00:00", "require": {"php": ">=5.5", "symfony/console": "~2.6", "php-di/invoker": "~1.2", "container-interop/container-interop": "~1.0"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "df547fa04fc17948fb5e733f94fc7dd7a12d2e67"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/df547fa04fc17948fb5e733f94fc7dd7a12d2e67", "type": "zip", "shasum": "", "reference": "df547fa04fc17948fb5e733f94fc7dd7a12d2e67"}, "time": "2015-05-24T15:58:25+00:00", "require": {"php": ">=5.5", "symfony/console": "~2.6", "php-di/invoker": "~1.0", "container-interop/container-interop": "~1.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "623f0385d64c48c9f21eb39fca29efd0258e515a"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/623f0385d64c48c9f21eb39fca29efd0258e515a", "type": "zip", "shasum": "", "reference": "623f0385d64c48c9f21eb39fca29efd0258e515a"}, "time": "2015-04-04T06:11:35+00:00", "require": {"php": ">=5.5", "symfony/console": "~2.6", "mnapoli/invoker": "~0.2.1", "container-interop/container-interop": "~1.0"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "bab1868f57193b0518180c65937f036571becf1c"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/bab1868f57193b0518180c65937f036571becf1c", "type": "zip", "shasum": "", "reference": "bab1868f57193b0518180c65937f036571becf1c"}, "time": "2015-03-02T08:42:40+00:00", "extra": {"branch-alias": {"dev-master": "0.2.x-dev"}}, "require": {"php": ">=5.5", "symfony/console": "~2.6", "mnapoli/php-di": "~4.4"}, "require-dev": {"phpunit/phpunit": "~4.5"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "019912937cd3d7607bc8272e4fa386cdff0430a9"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/019912937cd3d7607bc8272e4fa386cdff0430a9", "type": "zip", "shasum": "", "reference": "019912937cd3d7607bc8272e4fa386cdff0430a9"}, "time": "2015-02-28T02:46:45+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/silly.git", "type": "git", "reference": "471677f5c8a0753d38b25e43e750148ddeafd885"}, "dist": {"url": "https://api.github.com/repos/mnapoli/silly/zipball/471677f5c8a0753d38b25e43e750148ddeafd885", "type": "zip", "shasum": "", "reference": "471677f5c8a0753d38b25e43e750148ddeafd885"}, "time": "2015-02-12T10:46:30+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Wed, 30 Oct 2024 10:27:04 GMT"}