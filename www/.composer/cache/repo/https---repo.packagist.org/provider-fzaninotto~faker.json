{"minified": "composer/2.0", "packages": {"fzaninotto/faker": [{"name": "fzaninotto/faker", "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["faker", "fixtures", "data"], "homepage": "", "version": "v1.9.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "type": "zip", "shasum": "", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "type": "library", "time": "2020-12-11T09:56:16+00:00", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "abandoned": true, "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.2"}}, {"version": "v1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/fc10d778e4b84d5bd315dad194661e091d307c6f", "type": "zip", "shasum": "", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f"}, "time": "2019-12-12T13:22:17+00:00", "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.1"}}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "27a216cbe72327b2d6369fab721a5843be71e57d"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/27a216cbe72327b2d6369fab721a5843be71e57d", "type": "zip", "shasum": "", "reference": "27a216cbe72327b2d6369fab721a5843be71e57d"}, "time": "2019-11-14T13:13:06+00:00", "extra": {"branch-alias": []}, "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.0"}}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/f72816b43e74063c8b10357394b6bba8cb1c10de", "type": "zip", "shasum": "", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de"}, "time": "2018-07-12T10:23:15+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^1.5", "ext-intl": "*"}, "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/master"}}, {"version": "v1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/d3ed4cc37051c1ca52d22d76b437d14809fc7e0d", "type": "zip", "shasum": "", "reference": "d3ed4cc37051c1ca52d22d76b437d14809fc7e0d"}, "time": "2017-08-15T16:48:10+00:00", "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0", "squizlabs/php_codesniffer": "^1.5", "ext-intl": "*"}}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "dd4564efb00c557559af29c2964fba764d0dfacc"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/dd4564efb00c557559af29c2964fba764d0dfacc", "type": "zip", "shasum": "", "reference": "dd4564efb00c557559af29c2964fba764d0dfacc"}, "time": "2017-08-15T16:34:15+00:00", "extra": {"branch-alias": []}}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "44f9a286a04b80c76a4e5fb7aad8bb539b920123"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/44f9a286a04b80c76a4e5fb7aad8bb539b920123", "type": "zip", "shasum": "", "reference": "44f9a286a04b80c76a4e5fb7aad8bb539b920123"}, "time": "2016-04-29T12:21:54+00:00", "require": {"php": "^5.3.3|^7.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5", "ext-intl": "*"}, "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.6.0"}}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "d0190b156bcca848d401fb80f31f504f37141c8d"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/d0190b156bcca848d401fb80f31f504f37141c8d", "type": "zip", "shasum": "", "reference": "d0190b156bcca848d401fb80f31f504f37141c8d"}, "time": "2015-05-29T06:29:14+00:00", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "suggest": {"ext-intl": "*"}, "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/master"}}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "010c7efedd88bf31141a02719f51fb44c732d5a0"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/010c7efedd88bf31141a02719f51fb44c732d5a0", "type": "zip", "shasum": "", "reference": "010c7efedd88bf31141a02719f51fb44c732d5a0"}, "time": "2014-06-04T14:43:02+00:00", "autoload": {"psr-0": {"Faker": "src/", "Faker\\PHPUnit": "test/"}}, "extra": {"branch-alias": []}, "suggest": "__unset"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "1d143fd8caf4d264602450bc01d7484af788706b"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/1d143fd8caf4d264602450bc01d7484af788706b", "type": "zip", "shasum": "", "reference": "1d143fd8caf4d264602450bc01d7484af788706b"}, "time": "2013-12-16T21:56:48+00:00", "extra": {"branch-alias": {"dev-master": "1.3.0-dev"}}, "require-dev": "__unset"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "4ad4bc4b5c8d3c0f3cf55d2fedc2f65b313ec62f"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/4ad4bc4b5c8d3c0f3cf55d2fedc2f65b313ec62f", "type": "zip", "shasum": "", "reference": "4ad4bc4b5c8d3c0f3cf55d2fedc2f65b313ec62f"}, "time": "2013-06-09T18:05:57+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "3bf14b6cb8b98be3a3a3f9fed66ecb2517bf87ac"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/3bf14b6cb8b98be3a3a3f9fed66ecb2517bf87ac", "type": "zip", "shasum": "", "reference": "3bf14b6cb8b98be3a3a3f9fed66ecb2517bf87ac"}, "time": "2012-10-29T09:32:06+00:00", "autoload": {"psr-0": {"Faker": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.1.0"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/fzaninotto/Faker.git", "type": "git", "reference": "f3d296e385dd4a46a5ddf507539a6c150c32983e"}, "dist": {"url": "https://api.github.com/repos/fzaninotto/Faker/zipball/f3d296e385dd4a46a5ddf507539a6c150c32983e", "type": "zip", "shasum": "", "reference": "f3d296e385dd4a46a5ddf507539a6c150c32983e"}, "time": "2012-07-10T14:43:46+00:00", "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.0.0"}, "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:10:38 GMT"}