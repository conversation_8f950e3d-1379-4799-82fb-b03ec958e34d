{"minified": "composer/2.0", "packages": {"league/csv": [{"name": "league/csv", "description": "CSV data manipulation made easy in PHP", "keywords": ["csv", "import", "filter", "convert", "export", "transform", "read", "write"], "homepage": "https://csv.thephpleague.com", "version": "9.24.1", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "e0221a3f16aa2a823047d59fab5809d552e29bc8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/e0221a3f16aa2a823047d59fab5809d552e29bc8", "type": "zip", "shasum": "", "reference": "e0221a3f16aa2a823047d59fab5809d552e29bc8"}, "type": "library", "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2025-06-25T14:53:51+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "require": {"php": "^8.1.2", "ext-filter": "*"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.75.0", "phpbench/phpbench": "^1.4.1", "phpstan/phpstan": "^1.12.27", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.2", "phpstan/phpstan-strict-rules": "^1.6.2", "phpunit/phpunit": "^10.5.16 || ^11.5.22", "symfony/var-dumper": "^6.4.8 || ^7.3.0"}, "suggest": {"ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters", "ext-pdo": "Required to use the package with the PDO extension", "ext-sqlite3": "Required to use the package with the SQLite3 extension", "ext-mysqli": "Requiered to use the package with the MySQLi extension", "ext-pgsql": "Requiered to use the package with the PgSQL extension"}}, {"version": "9.24.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "3f11e12ccd7d6dc25729056e7ce386e4084089ea"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/3f11e12ccd7d6dc25729056e7ce386e4084089ea", "type": "zip", "shasum": "", "reference": "3f11e12ccd7d6dc25729056e7ce386e4084089ea"}, "time": "2025-06-24T19:54:25+00:00"}, {"version": "9.23.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "774008ad8a634448e4f8e288905e070e8b317ff3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/774008ad8a634448e4f8e288905e070e8b317ff3", "type": "zip", "shasum": "", "reference": "774008ad8a634448e4f8e288905e070e8b317ff3"}, "time": "2025-03-28T06:52:04+00:00", "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.69.0", "phpbench/phpbench": "^1.4.0", "phpstan/phpstan": "^1.12.18", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.2", "phpstan/phpstan-strict-rules": "^1.6.2", "phpunit/phpunit": "^10.5.16 || ^11.5.7", "symfony/var-dumper": "^6.4.8 || ^7.2.3"}}, {"version": "9.22.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "afc109aa11f3086b8be8dfffa04ac31480b36b76"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/afc109aa11f3086b8be8dfffa04ac31480b36b76", "type": "zip", "shasum": "", "reference": "afc109aa11f3086b8be8dfffa04ac31480b36b76"}, "time": "2025-02-28T10:00:39+00:00"}, {"version": "9.21.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "72196d11ebba22d868954cb39c0c7346207430cc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/72196d11ebba22d868954cb39c0c7346207430cc", "type": "zip", "shasum": "", "reference": "72196d11ebba22d868954cb39c0c7346207430cc"}, "time": "2025-01-08T19:27:58+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src/"}}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.64.0", "phpbench/phpbench": "^1.3.1", "phpstan/phpstan": "^1.12.11", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.1", "phpstan/phpstan-strict-rules": "^1.6.1", "phpunit/phpunit": "^10.5.16 || ^11.4.3", "symfony/var-dumper": "^6.4.8 || ^7.1.8"}, "suggest": {"ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters"}}, {"version": "9.20.1", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "491d1e79e973a7370c7571dc0fe4a7241f4936ee"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/491d1e79e973a7370c7571dc0fe4a7241f4936ee", "type": "zip", "shasum": "", "reference": "491d1e79e973a7370c7571dc0fe4a7241f4936ee"}, "time": "2024-12-18T10:11:15+00:00"}, {"version": "9.20.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "553579df208641ada6ffb450b3a151e2fcfa4f31"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/553579df208641ada6ffb450b3a151e2fcfa4f31", "type": "zip", "shasum": "", "reference": "553579df208641ada6ffb450b3a151e2fcfa4f31"}, "time": "2024-12-13T15:49:27+00:00"}, {"version": "9.19.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "f81df48a012a9e86d077e74eaff666fd15bfab88"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/f81df48a012a9e86d077e74eaff666fd15bfab88", "type": "zip", "shasum": "", "reference": "f81df48a012a9e86d077e74eaff666fd15bfab88"}, "time": "2024-12-08T08:09:35+00:00"}, {"version": "9.18.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "b02d010e4055ae992247f6ffd1e7b103ef2a0790"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/b02d010e4055ae992247f6ffd1e7b103ef2a0790", "type": "zip", "shasum": "", "reference": "b02d010e4055ae992247f6ffd1e7b103ef2a0790"}, "time": "2024-10-18T08:14:48+00:00", "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.64.0", "phpbench/phpbench": "^1.3.1", "phpstan/phpstan": "^1.12.6", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.1", "phpunit/phpunit": "^10.5.16 || ^11.4.1", "symfony/var-dumper": "^6.4.8 || ^7.1.5"}}, {"version": "9.17.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "8cab815fb11ec93aa2f7b8a57b3daa1f1a364011"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/8cab815fb11ec93aa2f7b8a57b3daa1f1a364011", "type": "zip", "shasum": "", "reference": "8cab815fb11ec93aa2f7b8a57b3daa1f1a364011"}, "time": "2024-10-10T10:30:28+00:00", "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.64.0", "phpbench/phpbench": "^1.3.1", "phpstan/phpstan": "^1.12.5", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.1", "phpunit/phpunit": "^10.5.16 || ^11.4.0", "symfony/var-dumper": "^6.4.8 || ^7.1.5"}}, {"version": "9.16.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "998280c6c34bd67d8125fdc8b45bae28d761b440"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/998280c6c34bd67d8125fdc8b45bae28d761b440", "type": "zip", "shasum": "", "reference": "998280c6c34bd67d8125fdc8b45bae28d761b440"}, "time": "2024-05-24T11:04:54+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "doctrine/collections": "^2.2.2", "friendsofphp/php-cs-fixer": "^3.57.1", "phpbench/phpbench": "^1.2.15", "phpstan/phpstan": "^1.11.1", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpunit/phpunit": "^10.5.16 || ^11.1.3", "symfony/var-dumper": "^6.4.6 || ^7.0.7"}}, {"version": "9.15.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "fa7e2441c0bc9b2360f4314fd6c954f7ff40d435"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/fa7e2441c0bc9b2360f4314fd6c954f7ff40d435", "type": "zip", "shasum": "", "reference": "fa7e2441c0bc9b2360f4314fd6c954f7ff40d435"}, "time": "2024-02-20T20:00:00+00:00", "require": {"php": "^8.1.2", "ext-filter": "*", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "doctrine/collections": "^2.1.4", "friendsofphp/php-cs-fixer": "^v3.22.0", "phpbench/phpbench": "^1.2.15", "phpstan/phpstan": "^1.10.57", "phpstan/phpstan-deprecation-rules": "^1.1.4", "phpstan/phpstan-phpunit": "^1.3.15", "phpstan/phpstan-strict-rules": "^1.5.2", "phpunit/phpunit": "^10.5.9", "symfony/var-dumper": "^6.4.2"}, "suggest": {"ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-dom": "Required to use the XMLConverter and the HTMLConverter classes"}}, {"version": "9.14.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "34bf0df7340b60824b9449b5c526fcc3325070d5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/34bf0df7340b60824b9449b5c526fcc3325070d5", "type": "zip", "shasum": "", "reference": "34bf0df7340b60824b9449b5c526fcc3325070d5"}, "time": "2023-12-29T07:34:53+00:00", "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "doctrine/collections": "^2.1.4", "friendsofphp/php-cs-fixer": "^v3.22.0", "phpbench/phpbench": "^1.2.15", "phpstan/phpstan": "^1.10.50", "phpstan/phpstan-deprecation-rules": "^1.1.4", "phpstan/phpstan-phpunit": "^1.3.15", "phpstan/phpstan-strict-rules": "^1.5.2", "phpunit/phpunit": "^10.5.3", "symfony/var-dumper": "^6.4.0"}}, {"version": "9.13.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "3690cc71bfe8dc3b6daeef356939fac95348f0a8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/3690cc71bfe8dc3b6daeef356939fac95348f0a8", "type": "zip", "shasum": "", "reference": "3690cc71bfe8dc3b6daeef356939fac95348f0a8"}, "time": "2023-12-16T11:03:20+00:00"}, {"version": "9.12.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "c1dc31e23eb3cd0f7b537ee1a669d5f58d5cfe21"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/c1dc31e23eb3cd0f7b537ee1a669d5f58d5cfe21", "type": "zip", "shasum": "", "reference": "c1dc31e23eb3cd0f7b537ee1a669d5f58d5cfe21"}, "time": "2023-12-01T17:54:07+00:00", "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "doctrine/collections": "^2.1.4", "friendsofphp/php-cs-fixer": "^v3.22.0", "phpbench/phpbench": "^1.2.15", "phpstan/phpstan": "^1.10.46", "phpstan/phpstan-deprecation-rules": "^1.1.4", "phpstan/phpstan-phpunit": "^1.3.15", "phpstan/phpstan-strict-rules": "^1.5.2", "phpunit/phpunit": "^10.4.2", "symfony/var-dumper": "^6.4.0"}}, {"version": "9.11.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "33149c4bea4949aa4fa3d03fb11ed28682168b39"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/33149c4bea4949aa4fa3d03fb11ed28682168b39", "type": "zip", "shasum": "", "reference": "33149c4bea4949aa4fa3d03fb11ed28682168b39"}, "time": "2023-09-23T10:09:54+00:00", "require": {"php": "^8.1.2", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "doctrine/collections": "^2.1.3", "friendsofphp/php-cs-fixer": "^v3.22.0", "phpbench/phpbench": "^1.2.14", "phpstan/phpstan": "^1.10.26", "phpstan/phpstan-deprecation-rules": "^1.1.3", "phpstan/phpstan-phpunit": "^1.3.13", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^10.3.1", "symfony/var-dumper": "^6.3.3"}}, {"version": "9.10.0", "version_normalized": "********", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "d24b0d484812313b07ab74b0fe4db9661606df6c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/d24b0d484812313b07ab74b0fe4db9661606df6c", "type": "zip", "shasum": "", "reference": "d24b0d484812313b07ab74b0fe4db9661606df6c"}, "time": "2023-08-04T15:12:48+00:00"}, {"version": "9.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "b4418ede47fbd88facc34e40a16c8ce9153b961b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/b4418ede47fbd88facc34e40a16c8ce9153b961b", "type": "zip", "shasum": "", "reference": "b4418ede47fbd88facc34e40a16c8ce9153b961b"}, "time": "2023-03-11T15:57:12+00:00", "require-dev": {"ext-dom": "*", "doctrine/collections": "^2.1.2", "friendsofphp/php-cs-fixer": "^v3.14.3", "phpbench/phpbench": "^1.2.8", "phpstan/phpstan": "^1.10.4", "phpstan/phpstan-deprecation-rules": "^1.1.2", "phpstan/phpstan-phpunit": "^1.3.10", "phpstan/phpstan-strict-rules": "^1.5.0", "phpunit/phpunit": "^10.0.14", "ext-xdebug": "*"}}, {"version": "9.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "9d2e0265c5d90f5dd601bc65ff717e05cec19b47"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "type": "zip", "shasum": "", "reference": "9d2e0265c5d90f5dd601bc65ff717e05cec19b47"}, "time": "2022-01-04T00:13:07+00:00", "require": {"php": "^7.4 || ^8.0", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "friendsofphp/php-cs-fixer": "^v3.4.0", "phpunit/phpunit": "^9.5.11", "phpstan/phpstan": "^1.3.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpstan/phpstan-phpunit": "^1.0.0"}, "suggest": {"ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-dom": "Required to use the XMLConverter and or the HTMLConverter classes"}}, {"homepage": "http://csv.thephpleague.com", "version": "9.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "002f55f649e7511710dc7154ff44c7be32c8195c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/002f55f649e7511710dc7154ff44c7be32c8195c", "type": "zip", "shasum": "", "reference": "002f55f649e7511710dc7154ff44c7be32c8195c"}, "time": "2021-11-30T07:09:34+00:00", "require": {"php": "^7.3 || ^8.0", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "friendsofphp/php-cs-fixer": "^3.0", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0.0"}}, {"version": "9.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "d8149032aa74a9daca6f7b35d63c46a35c9bc1d5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/d8149032aa74a9daca6f7b35d63c46a35c9bc1d5", "type": "zip", "shasum": "", "reference": "d8149032aa74a9daca6f7b35d63c46a35c9bc1d5"}, "time": "2021-11-21T19:32:00+00:00"}, {"version": "9.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "8544655c460fd01eed0ad258e514488d4b388645"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/8544655c460fd01eed0ad258e514488d4b388645", "type": "zip", "shasum": "", "reference": "8544655c460fd01eed0ad258e514488d4b388645"}, "time": "2021-10-05T19:41:46+00:00", "require-dev": {"ext-curl": "*", "ext-dom": "*", "friendsofphp/php-cs-fixer": "^3.0", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-strict-rules": "^0.12.11", "phpstan/phpstan-phpunit": "^0.12.22"}}, {"version": "9.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "0ec57e8264ec92565974ead0d1724cf1026e10c1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/0ec57e8264ec92565974ead0d1724cf1026e10c1", "type": "zip", "shasum": "", "reference": "0ec57e8264ec92565974ead0d1724cf1026e10c1"}, "time": "2021-04-17T16:32:08+00:00", "require-dev": {"ext-curl": "*", "ext-dom": "*", "friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^0.12.0", "phpstan/phpstan-strict-rules": "^0.12.0", "phpstan/phpstan-phpunit": "^0.12.0"}}, {"version": "9.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "4cacd9c72c4aa8bdbef43315b2ca25c46a0f833f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/4cacd9c72c4aa8bdbef43315b2ca25c46a0f833f", "type": "zip", "shasum": "", "reference": "4cacd9c72c4aa8bdbef43315b2ca25c46a0f833f"}, "time": "2021-03-26T22:08:10+00:00"}, {"version": "9.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "f28da6e483bf979bac10e2add384c90ae9983e4e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/f28da6e483bf979bac10e2add384c90ae9983e4e", "type": "zip", "shasum": "", "reference": "f28da6e483bf979bac10e2add384c90ae9983e4e"}, "time": "2020-12-10T19:40:30+00:00", "require": {"php": ">=7.2.5", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^8.5", "phpstan/phpstan": "^0.12.0", "phpstan/phpstan-strict-rules": "^0.12.0", "phpstan/phpstan-phpunit": "^0.12.0"}}, {"version": "9.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "634322df4aed210fdfbb7c94e434dc860da733d9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/634322df4aed210fdfbb7c94e434dc860da733d9", "type": "zip", "shasum": "", "reference": "634322df4aed210fdfbb7c94e434dc860da733d9"}, "time": "2020-09-05T08:40:12+00:00", "require": {"php": "^7.2.5", "ext-json": "*", "ext-mbstring": "*"}}, {"version": "9.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "7351a74625601914409b42b32cabb91a93773b7b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/7351a74625601914409b42b32cabb91a93773b7b", "type": "zip", "shasum": "", "reference": "7351a74625601914409b42b32cabb91a93773b7b"}, "time": "2020-03-17T15:15:35+00:00", "require-dev": {"ext-curl": "*", "friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^8.0", "phpstan/phpstan": "^0.12.0", "phpstan/phpstan-strict-rules": "^0.12.0", "phpstan/phpstan-phpunit": "^0.12.0"}}, {"description": "Csv data manipulation made easy in PHP", "keywords": ["csv", "import", "filter", "export", "read", "write"], "version": "9.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "b348d09d0d258a4f068efb50a2510dc63101c213"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/b348d09d0d258a4f068efb50a2510dc63101c213", "type": "zip", "shasum": "", "reference": "b348d09d0d258a4f068efb50a2510dc63101c213"}, "support": {"docs": "https://csv.thephpleague.com", "forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "time": "2019-12-15T19:51:41+00:00", "require": {"php": ">=7.0.10", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"ext-curl": "*", "friendsofphp/php-cs-fixer": "^2.12", "phpunit/phpunit": "^6.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-strict-rules": "^0.9.0", "phpstan/phpstan-phpunit": "^0.9.4"}, "suggest": {"ext-iconv": "Needed to ease transcoding CSV using iconv stream filters"}, "funding": "__unset"}, {"version": "9.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "bf83acc23a7d60978fce36a384f97bf9d7d2ea0c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/bf83acc23a7d60978fce36a384f97bf9d7d2ea0c", "type": "zip", "shasum": "", "reference": "bf83acc23a7d60978fce36a384f97bf9d7d2ea0c"}, "time": "2019-10-17T06:05:32+00:00"}, {"version": "9.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "e7225b6cc853942caef1c9c4d8889b716c34bc04"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/e7225b6cc853942caef1c9c4d8889b716c34bc04", "type": "zip", "shasum": "", "reference": "e7225b6cc853942caef1c9c4d8889b716c34bc04"}, "time": "2019-10-02T18:32:54+00:00"}, {"version": "9.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "d16f85d1f958a765844db4bc7174017edf2dd637"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/d16f85d1f958a765844db4bc7174017edf2dd637", "type": "zip", "shasum": "", "reference": "d16f85d1f958a765844db4bc7174017edf2dd637"}, "time": "2019-07-30T14:39:11+00:00"}, {"version": "9.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "b574a7d8b28f1528e011d8652fb7d2e83410d4c9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/b574a7d8b28f1528e011d8652fb7d2e83410d4c9", "type": "zip", "shasum": "", "reference": "b574a7d8b28f1528e011d8652fb7d2e83410d4c9"}, "time": "2019-06-07T06:24:33+00:00", "require": {"php": ">=7.0.10", "ext-mbstring": "*"}}, {"version": "9.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "f3a3c69b6e152417e1b62d995bcad2237b053cc6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/f3a3c69b6e152417e1b62d995bcad2237b053cc6", "type": "zip", "shasum": "", "reference": "f3a3c69b6e152417e1b62d995bcad2237b053cc6"}, "time": "2019-03-08T06:56:16+00:00"}, {"version": "9.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "9c8ad06fb5d747c149875beb6133566c00eaa481"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/9c8ad06fb5d747c149875beb6133566c00eaa481", "type": "zip", "shasum": "", "reference": "9c8ad06fb5d747c149875beb6133566c00eaa481"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/master"}, "time": "2018-05-01T18:32:48+00:00", "require-dev": {"ext-curl": "*", "friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^6.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-strict-rules": "^0.9.0", "phpstan/phpstan-phpunit": "^0.9.4"}}, {"version": "9.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "0d0b12f1a0093a6c39014a5d118f6ba4274539ee"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/0d0b12f1a0093a6c39014a5d118f6ba4274539ee", "type": "zip", "shasum": "", "reference": "0d0b12f1a0093a6c39014a5d118f6ba4274539ee"}, "time": "2018-03-12T07:20:01+00:00"}, {"version": "9.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "90bedd96fa3e3d2a988be985b9b1ee29cdb38301"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/90bedd96fa3e3d2a988be985b9b1ee29cdb38301", "type": "zip", "shasum": "", "reference": "90bedd96fa3e3d2a988be985b9b1ee29cdb38301"}, "time": "2018-02-05T14:07:11+00:00", "require-dev": {"ext-curl": "*", "friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^6.0"}}, {"version": "9.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "66118f5c2a7e4da77e743e69f74773c63b73e8f9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/66118f5c2a7e4da77e743e69f74773c63b73e8f9", "type": "zip", "shasum": "", "reference": "66118f5c2a7e4da77e743e69f74773c63b73e8f9"}, "time": "2017-11-28T08:29:49+00:00"}, {"version": "9.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "bfa3aa8e755377cd6cc2242cfd074e48c7c300ba"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/bfa3aa8e755377cd6cc2242cfd074e48c7c300ba", "type": "zip", "shasum": "", "reference": "bfa3aa8e755377cd6cc2242cfd074e48c7c300ba"}, "time": "2017-10-20T08:03:26+00:00"}, {"version": "9.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "5dc305e7958190bcab0cc2778888a4f658d29aa1"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/5dc305e7958190bcab0cc2778888a4f658d29aa1", "type": "zip", "shasum": "", "reference": "5dc305e7958190bcab0cc2778888a4f658d29aa1"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/9.0.1"}, "time": "2017-08-21T13:42:10+00:00"}, {"version": "9.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "60ea563773f1c50162a948c44901c93b9a0aee56"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/60ea563773f1c50162a948c44901c93b9a0aee56", "type": "zip", "shasum": "", "reference": "60ea563773f1c50162a948c44901c93b9a0aee56"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/master"}, "time": "2017-08-18T10:27:55+00:00"}, {"version": "8.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "d2aab1e7bde802582c3879acf03d92716577c76d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/d2aab1e7bde802582c3879acf03d92716577c76d", "type": "zip", "shasum": "", "reference": "d2aab1e7bde802582c3879acf03d92716577c76d"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/8.x"}, "time": "2018-02-06T08:27:03+00:00", "autoload": {"psr-4": {"League\\Csv\\": "src"}}, "extra": {"branch-alias": {"dev-master": "8.2-dev"}}, "require": {"php": ">=5.5.0", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^4.0", "friendsofphp/php-cs-fixer": "^1.9"}, "suggest": "__unset"}, {"version": "8.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "fa8bc05f64eb6c66b96edfaf60648f022ecb5f55"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/fa8bc05f64eb6c66b96edfaf60648f022ecb5f55", "type": "zip", "shasum": "", "reference": "fa8bc05f64eb6c66b96edfaf60648f022ecb5f55"}, "time": "2017-07-12T07:18:20+00:00"}, {"version": "8.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "43fd8b022815a0758d85e925dd92a43fe0d41bb4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/43fd8b022815a0758d85e925dd92a43fe0d41bb4", "type": "zip", "shasum": "", "reference": "43fd8b022815a0758d85e925dd92a43fe0d41bb4"}, "time": "2017-02-23T08:25:03+00:00"}, {"version": "8.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "ef7eef710810c8bd0cf9371582ccd0123ff96d4b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/ef7eef710810c8bd0cf9371582ccd0123ff96d4b", "type": "zip", "shasum": "", "reference": "ef7eef710810c8bd0cf9371582ccd0123ff96d4b"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/master"}, "time": "2017-01-25T13:32:07+00:00"}, {"version": "8.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "33447984f7a7038fefaa5a6177e8407b66bc85b4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/33447984f7a7038fefaa5a6177e8407b66bc85b4", "type": "zip", "shasum": "", "reference": "33447984f7a7038fefaa5a6177e8407b66bc85b4"}, "time": "2016-10-27T11:21:24+00:00", "extra": {"branch-alias": {"dev-master": "8.1-dev"}}}, {"version": "8.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "3b22a40804aa0bc5224ffb2f5e8248edf0a9a38c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/3b22a40804aa0bc5224ffb2f5e8248edf0a9a38c", "type": "zip", "shasum": "", "reference": "3b22a40804aa0bc5224ffb2f5e8248edf0a9a38c"}, "time": "2016-09-05T08:16:07+00:00", "extra": {"branch-alias": {"dev-master": "8.0-dev"}}}, {"version": "8.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "63f2fc3598a4dbbc906c9db9d6f04bc7f72b0645"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/63f2fc3598a4dbbc906c9db9d6f04bc7f72b0645", "type": "zip", "shasum": "", "reference": "63f2fc3598a4dbbc906c9db9d6f04bc7f72b0645"}, "time": "2016-05-30T09:35:50+00:00", "require-dev": {"phpunit/phpunit": "^4.0", "fabpot/php-cs-fixer": "^1.9"}}, {"version": "8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "0a1b535df48507f58ed3c7ab44e72272b11163ac"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/0a1b535df48507f58ed3c7ab44e72272b11163ac", "type": "zip", "shasum": "", "reference": "0a1b535df48507f58ed3c7ab44e72272b11163ac"}, "time": "2015-12-11T09:28:14+00:00"}, {"version": "7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "69bafa6ff924fbf9effe4275d6eb16be81a853ef"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/69bafa6ff924fbf9effe4275d6eb16be81a853ef", "type": "zip", "shasum": "", "reference": "69bafa6ff924fbf9effe4275d6eb16be81a853ef"}, "time": "2015-11-02T07:36:25+00:00", "extra": {"branch-alias": {"dev-master": "7.2-dev"}}, "require": {"php": ">=5.4.0", "ext-mbstring": "*"}}, {"version": "7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "2ee1760c262c41986f6371775907fc9e8603fd26"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/2ee1760c262c41986f6371775907fc9e8603fd26", "type": "zip", "shasum": "", "reference": "2ee1760c262c41986f6371775907fc9e8603fd26"}, "time": "2015-06-10T11:12:37+00:00", "extra": {"branch-alias": {"dev-master": "7.1-dev"}}, "require-dev": {"phpunit/phpunit": "~4.0", "scrutinizer/ocular": "~1.1"}}, {"version": "7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "ca5242603b75a9557c675e34fd70a8cb32d42465"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/ca5242603b75a9557c675e34fd70a8cb32d42465", "type": "zip", "shasum": "", "reference": "ca5242603b75a9557c675e34fd70a8cb32d42465"}, "time": "2015-05-20T12:55:53+00:00", "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "3a2a199d686bc8b52689b74d5a66cb2e9b9370c9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/3a2a199d686bc8b52689b74d5a66cb2e9b9370c9", "type": "zip", "shasum": "", "reference": "3a2a199d686bc8b52689b74d5a66cb2e9b9370c9"}, "time": "2015-05-06T12:03:42+00:00"}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "90a92cc55eeff660599840665890eda28a689920"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/90a92cc55eeff660599840665890eda28a689920", "type": "zip", "shasum": "", "reference": "90a92cc55eeff660599840665890eda28a689920"}, "time": "2015-03-23T11:54:26+00:00", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}}, {"homepage": "", "version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "42169da9f32996b274bc0d125974660ee999c6cf"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/42169da9f32996b274bc0d125974660ee999c6cf", "type": "zip", "shasum": "", "reference": "42169da9f32996b274bc0d125974660ee999c6cf"}, "time": "2015-02-19T12:09:05+00:00"}, {"version": "6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "5ffb2b42ed5d7ae20d80d4480b34743f5086d4a8"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/5ffb2b42ed5d7ae20d80d4480b34743f5086d4a8", "type": "zip", "shasum": "", "reference": "5ffb2b42ed5d7ae20d80d4480b34743f5086d4a8"}, "time": "2015-01-21T12:06:27+00:00", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}}, {"version": "6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "a7f92b56d37bfce593903b4afe211ce9429188ab"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/a7f92b56d37bfce593903b4afe211ce9429188ab", "type": "zip", "shasum": "", "reference": "a7f92b56d37bfce593903b4afe211ce9429188ab"}, "time": "2014-12-12T11:37:47+00:00", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "25198fd4036de1b5d46447ae2382fdafd86bade9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/25198fd4036de1b5d46447ae2382fdafd86bade9", "type": "zip", "shasum": "", "reference": "25198fd4036de1b5d46447ae2382fdafd86bade9"}, "time": "2014-12-08T07:58:23+00:00", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "539230130d2bf9f548ac0a49a306a08dc0dfec85"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/539230130d2bf9f548ac0a49a306a08dc0dfec85", "type": "zip", "shasum": "", "reference": "539230130d2bf9f548ac0a49a306a08dc0dfec85"}, "time": "2014-11-12T19:54:22+00:00", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "replace": {"bakame/csv": "*"}}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "7c881c29c0e585cdf716ce3a5e57f9484fbd74f7"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/7c881c29c0e585cdf716ce3a5e57f9484fbd74f7", "type": "zip", "shasum": "", "reference": "7c881c29c0e585cdf716ce3a5e57f9484fbd74f7"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/master"}, "time": "2014-08-28T10:14:25+00:00", "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}}, {"description": "Csv data manipulation made easy in PHP 5.4+", "version": "5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "35ed8f94c6e9a381c27dddf9bc0349b045943aea"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/35ed8f94c6e9a381c27dddf9bc0349b045943aea", "type": "zip", "shasum": "", "reference": "35ed8f94c6e9a381c27dddf9bc0349b045943aea"}, "time": "2014-04-17T09:24:36+00:00", "require-dev": {"phpunit/phpunit": "*"}, "extra": "__unset"}, {"version": "5.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "3cdd4d14b233b5a0863eb7c13b46fb23978081bc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/3cdd4d14b233b5a0863eb7c13b46fb23978081bc", "type": "zip", "shasum": "", "reference": "3cdd4d14b233b5a0863eb7c13b46fb23978081bc"}, "time": "2014-04-09T14:11:58+00:00"}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "fa5a2db4f90d2e504dd1df9e59c9707200689ad5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/fa5a2db4f90d2e504dd1df9e59c9707200689ad5", "type": "zip", "shasum": "", "reference": "fa5a2db4f90d2e504dd1df9e59c9707200689ad5"}, "time": "2014-03-24T08:20:11+00:00"}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "921624ad42b49104c76b6b54b3e7ac567e866912"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/921624ad42b49104c76b6b54b3e7ac567e866912", "type": "zip", "shasum": "", "reference": "921624ad42b49104c76b6b54b3e7ac567e866912"}, "time": "2014-03-13T09:54:30+00:00"}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "afb552801ce3a4284a57ae9719e270b10df0130a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/afb552801ce3a4284a57ae9719e270b10df0130a", "type": "zip", "shasum": "", "reference": "afb552801ce3a4284a57ae9719e270b10df0130a"}, "time": "2014-03-11T09:04:56+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "f8953568fa1b6910f0cb7e0ec4b861d47db34b17"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/f8953568fa1b6910f0cb7e0ec4b861d47db34b17", "type": "zip", "shasum": "", "reference": "f8953568fa1b6910f0cb7e0ec4b861d47db34b17"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/5.0.0"}, "time": "2014-02-28T14:00:47+00:00", "autoload": {"psr-4": {"League\\CSv\\": "test", "League\\Csv\\": "src"}}, "require-dev": "__unset", "replace": "__unset"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "67136229aa994e4438db067ca7c6f6e24895f718"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/67136229aa994e4438db067ca7c6f6e24895f718", "type": "zip", "shasum": "", "reference": "67136229aa994e4438db067ca7c6f6e24895f718"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/4.2.1"}, "time": "2014-02-22T12:00:15+00:00", "autoload": {"psr-4": {"Bakame\\CSv\\": "test", "Bakame\\Csv\\": "src"}}}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "4cbd9e3f428e17ea4888ad7bf9a8cc12be0ee318"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/4cbd9e3f428e17ea4888ad7bf9a8cc12be0ee318", "type": "zip", "shasum": "", "reference": "4cbd9e3f428e17ea4888ad7bf9a8cc12be0ee318"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/4.2.0"}, "time": "2014-02-17T13:39:35+00:00"}, {"version": "4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "a24d0ce6d0f9e3463d09a6cf25ba41a3487be6de"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/a24d0ce6d0f9e3463d09a6cf25ba41a3487be6de", "type": "zip", "shasum": "", "reference": "a24d0ce6d0f9e3463d09a6cf25ba41a3487be6de"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/4.1.2"}, "time": "2014-02-14T15:00:38+00:00"}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "837f7293ff6e50bce0e9ebbf4075d143adbb5cfc"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/837f7293ff6e50bce0e9ebbf4075d143adbb5cfc", "type": "zip", "shasum": "", "reference": "837f7293ff6e50bce0e9ebbf4075d143adbb5cfc"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/4.1.1"}, "time": "2014-02-14T13:03:36+00:00", "autoload": {"psr-0": {"Bakame": ["src", "test"]}}}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "a614a6b32d596e474050dcb660430b80c2fed5d5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/a614a6b32d596e474050dcb660430b80c2fed5d5", "type": "zip", "shasum": "", "reference": "a614a6b32d596e474050dcb660430b80c2fed5d5"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/4.1.0"}, "time": "2014-02-07T11:44:18+00:00"}, {"keywords": ["iterator", "csv", "import", "export", "filtering", "SplFileObject", "reading", "writing"], "version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "910ed1d312129dafede3823a854c3b8f58160a27"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/910ed1d312129dafede3823a854c3b8f58160a27", "type": "zip", "shasum": "", "reference": "910ed1d312129dafede3823a854c3b8f58160a27"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/4.0.0"}, "time": "2014-02-05T16:18:00+00:00"}, {"description": "A simple library to easily load, manipulate and save CSV files in PHP 5.4+", "keywords": ["php", "csv", "splfileinfo", "SplFileObject", "mb_string", "spltmpfileobject", "traversable"], "version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "188bcc583ed36970c35b5de76a51c299ab804160"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/188bcc583ed36970c35b5de76a51c299ab804160", "type": "zip", "shasum": "", "reference": "188bcc583ed36970c35b5de76a51c299ab804160"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/3.3.0"}, "time": "2014-01-28T09:54:14+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "bb3d71e926882016140b2616ab6ae15604600ab0"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/bb3d71e926882016140b2616ab6ae15604600ab0", "type": "zip", "shasum": "", "reference": "bb3d71e926882016140b2616ab6ae15604600ab0"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/3.2.0"}, "time": "2014-01-16T09:38:26+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "43591db79ccbe95110696c8370b473b8a7f7d4c9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/43591db79ccbe95110696c8370b473b8a7f7d4c9", "type": "zip", "shasum": "", "reference": "43591db79ccbe95110696c8370b473b8a7f7d4c9"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/3.1.0"}, "time": "2014-01-13T09:18:15+00:00"}, {"description": "Bakame/csv is a lightweight PHP CSV Manipulation library", "version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "d4474b8ecd4061c9a685af5d54dc9abc31affefd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/d4474b8ecd4061c9a685af5d54dc9abc31affefd", "type": "zip", "shasum": "", "reference": "d4474b8ecd4061c9a685af5d54dc9abc31affefd"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/3.0.1"}, "time": "2014-01-10T12:50:18+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "b278afa05691e34a913d0c2696c8bf14726f6ff4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/b278afa05691e34a913d0c2696c8bf14726f6ff4", "type": "zip", "shasum": "", "reference": "b278afa05691e34a913d0c2696c8bf14726f6ff4"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/3.0.0"}, "time": "2014-01-10T11:47:34+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "8b003d0299aead80ed222ae3b0e6b3dacd219fc5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/8b003d0299aead80ed222ae3b0e6b3dacd219fc5", "type": "zip", "shasum": "", "reference": "8b003d0299aead80ed222ae3b0e6b3dacd219fc5"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/2.0.0"}, "time": "2014-01-09T12:32:16+00:00"}, {"description": "Bakame / csv is a lightweight PHP CSV Manipultion library", "version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/csv.git", "type": "git", "reference": "be01691db4cc63351cb24ce2031cb0e598666f23"}, "dist": {"url": "https://api.github.com/repos/thephpleague/csv/zipball/be01691db4cc63351cb24ce2031cb0e598666f23", "type": "zip", "shasum": "", "reference": "be01691db4cc63351cb24ce2031cb0e598666f23"}, "support": {"issues": "https://github.com/thephpleague/csv/issues", "source": "https://github.com/thephpleague/csv/tree/1.0.0"}, "time": "2013-12-03T14:46:41+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 25 Jun 2025 15:06:26 GMT"}