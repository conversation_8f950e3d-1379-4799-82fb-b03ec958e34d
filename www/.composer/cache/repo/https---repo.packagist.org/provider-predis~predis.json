{"minified": "composer/2.0", "packages": {"predis/predis": [{"name": "predis/predis", "description": "A flexible and feature-complete Redis/Valkey client for PHP.", "keywords": ["nosql", "redis", "predis"], "homepage": "http://github.com/predis/predis", "version": "v3.0.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "34fb0a7da0330df1bab4280fcac4afdeeccc3edf"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/34fb0a7da0330df1bab4280fcac4afdeeccc3edf", "type": "zip", "shasum": "", "reference": "34fb0a7da0330df1bab4280fcac4afdeeccc3edf"}, "type": "library", "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v3.0.1"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2025-05-16T18:30:32+00:00", "autoload": {"psr-4": {"Predis\\": "src/"}}, "require": {"php": "^7.2 || ^8.0", "psr/http-message": "^1.0|^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ~9.4.4", "phpunit/phpcov": "^6.0 || ^8.0"}, "suggest": {"ext-relay": "Faster connection with in-memory caching (>=0.6.2)"}}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "7d86f7afb37940bfc7aaa2909147616881377667"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/7d86f7afb37940bfc7aaa2909147616881377667", "type": "zip", "shasum": "", "reference": "7d86f7afb37940bfc7aaa2909147616881377667"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v3.0.0"}, "time": "2025-05-02T23:18:59+00:00"}, {"version": "v3.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "b5c942b2a1c66baf42c190ea8af42d7fe182995e"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/b5c942b2a1c66baf42c190ea8af42d7fe182995e", "type": "zip", "shasum": "", "reference": "b5c942b2a1c66baf42c190ea8af42d7fe182995e"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v3.0.0-RC1"}, "time": "2025-04-17T15:51:06+00:00"}, {"description": "A flexible and feature-complete Redis client for PHP.", "version": "v3.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "cf28b2a9975e9310a3192f0b72d7f1060b280db5"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/cf28b2a9975e9310a3192f0b72d7f1060b280db5", "type": "zip", "shasum": "", "reference": "cf28b2a9975e9310a3192f0b72d7f1060b280db5"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v3.0.0-alpha1"}, "time": "2024-01-19T16:57:22+00:00", "require": {"php": "^7.2 || ^8.0"}}, {"description": "A flexible and feature-complete Redis/Valkey client for PHP.", "version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/f49e13ee3a2a825631562aa0223ac922ec5d058b", "type": "zip", "shasum": "", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.4.0"}, "time": "2025-04-30T15:16:02+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.4", "phpunit/phpcov": "^6.0 || ^8.0"}}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "fdd52d0284f968e65adbcb82e4083b1cdc60024d"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/fdd52d0284f968e65adbcb82e4083b1cdc60024d", "type": "zip", "shasum": "", "reference": "fdd52d0284f968e65adbcb82e4083b1cdc60024d"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.4.0-RC1"}, "time": "2025-03-24T17:07:15+00:00"}, {"description": "A flexible and feature-complete Redis client for PHP.", "version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "bac46bfdb78cd6e9c7926c697012aae740cb9ec9"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/bac46bfdb78cd6e9c7926c697012aae740cb9ec9", "type": "zip", "shasum": "", "reference": "bac46bfdb78cd6e9c7926c697012aae740cb9ec9"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.3.0"}, "time": "2024-11-21T20:00:02+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.4"}}, {"version": "v2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "b1d3255ed9ad4d7254f9f9bba386c99f4bb983d1"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/b1d3255ed9ad4d7254f9f9bba386c99f4bb983d1", "type": "zip", "shasum": "", "reference": "b1d3255ed9ad4d7254f9f9bba386c99f4bb983d1"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.2.2"}, "time": "2023-09-13T16:42:03+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ~9.4.4"}}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "5f2b410a74afaff296a87a494e4c5488cf9fab57"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/5f2b410a74afaff296a87a494e4c5488cf9fab57", "type": "zip", "shasum": "", "reference": "5f2b410a74afaff296a87a494e4c5488cf9fab57"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.2.1"}, "time": "2023-08-15T23:01:46+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "33b70b971a32b0d28b4f748b0547593dce316e0d"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/33b70b971a32b0d28b4f748b0547593dce316e0d", "type": "zip", "shasum": "", "reference": "33b70b971a32b0d28b4f748b0547593dce316e0d"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.2.0"}, "time": "2023-06-14T10:37:31+00:00"}, {"version": "v2.2.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "95c23fb94d5cac0e8ca71f6f888f1b5c74bfbf61"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/95c23fb94d5cac0e8ca71f6f888f1b5c74bfbf61", "type": "zip", "shasum": "", "reference": "95c23fb94d5cac0e8ca71f6f888f1b5c74bfbf61"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.2.0-RC1"}, "time": "2023-05-09T17:45:20+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "a77a43913a74f9331f637bb12867eb8e274814e5"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/a77a43913a74f9331f637bb12867eb8e274814e5", "type": "zip", "shasum": "", "reference": "a77a43913a74f9331f637bb12867eb8e274814e5"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.1.2"}, "time": "2023-03-02T18:32:04+00:00", "suggest": "__unset"}, {"version": "v2.1.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator"}], "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "c5b60884e89630f9518a7919f0566db438f0fc9a"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/c5b60884e89630f9518a7919f0566db438f0fc9a", "type": "zip", "shasum": "", "reference": "c5b60884e89630f9518a7919f0566db438f0fc9a"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.1.1"}, "time": "2023-01-17T20:57:35+00:00", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "require-dev": {"phpunit/phpunit": "^8.0 || ~9.4.4"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis"}}, {"version": "v2.1.0", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "fbf270156f529a9920551c6f499c52a11ca4a1c3"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/fbf270156f529a9920551c6f499c52a11ca4a1c3", "type": "zip", "shasum": "", "reference": "fbf270156f529a9920551c6f499c52a11ca4a1c3"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.1.0"}, "time": "2023-01-16T23:33:05+00:00", "suggest": {"ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol", "ext-curl": "Allows access to Webdis when paired with phpiredis"}}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "ff59f745815150c65ed388f7d64e7660fe961771"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/ff59f745815150c65ed388f7d64e7660fe961771", "type": "zip", "shasum": "", "reference": "ff59f745815150c65ed388f7d64e7660fe961771"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.0.3"}, "time": "2022-10-11T16:52:29+00:00"}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "8b5fa928560b48a054fb1fd485fc65f2d8aa9e5c"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/8b5fa928560b48a054fb1fd485fc65f2d8aa9e5c", "type": "zip", "shasum": "", "reference": "8b5fa928560b48a054fb1fd485fc65f2d8aa9e5c"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.0.2"}, "time": "2022-09-06T14:34:14+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "7683215023a6669d618c56df7c025ab8e6840bfd"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/7683215023a6669d618c56df7c025ab8e6840bfd", "type": "zip", "shasum": "", "reference": "7683215023a6669d618c56df7c025ab8e6840bfd"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.0.1"}, "time": "2022-09-04T21:57:36+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "99c253733dee9447d26257dc669d33d5ac84713d"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/99c253733dee9447d26257dc669d33d5ac84713d", "type": "zip", "shasum": "", "reference": "99c253733dee9447d26257dc669d33d5ac84713d"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.0.0"}, "time": "2022-06-08T13:14:56+00:00"}, {"version": "v2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "4cc119a03cbdbbdf139e50e43369b4e24ce75679"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/4cc119a03cbdbbdf139e50e43369b4e24ce75679", "type": "zip", "shasum": "", "reference": "4cc119a03cbdbbdf139e50e43369b4e24ce75679"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.0.0-beta.1"}, "time": "2022-05-26T18:48:12+00:00"}, {"description": "Flexible and feature-complete Redis client for PHP and HHVM", "version": "v1.1.10", "version_normalized": "********", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "type": "zip", "shasum": "", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "time": "2022-01-05T17:46:08+00:00", "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "extra": "__unset"}, {"version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "c50c3393bb9f47fa012d0cdfb727a266b0818259"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/c50c3393bb9f47fa012d0cdfb727a266b0818259", "type": "zip", "shasum": "", "reference": "c50c3393bb9f47fa012d0cdfb727a266b0818259"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.9"}, "time": "2021-10-05T19:02:38+00:00"}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "cf5c118a077fbab8b9af1482c20952173125c041"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/cf5c118a077fbab8b9af1482c20952173125c041", "type": "zip", "shasum": "", "reference": "cf5c118a077fbab8b9af1482c20952173125c041"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.8"}, "time": "2021-09-29T17:48:39+00:00"}, {"version": "v1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "b240daa106d4e02f0c5b7079b41e31ddf66fddf8"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/b240daa106d4e02f0c5b7079b41e31ddf66fddf8", "type": "zip", "shasum": "", "reference": "b240daa106d4e02f0c5b7079b41e31ddf66fddf8"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.7"}, "time": "2021-04-04T19:34:46+00:00"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "9930e933c67446962997b05201c69c2319bf26de"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/9930e933c67446962997b05201c69c2319bf26de", "type": "zip", "shasum": "", "reference": "9930e933c67446962997b05201c69c2319bf26de"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.6"}, "time": "2020-09-11T19:18:05+00:00", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "./tests/phpunit_php7.patch", "Fix PHP 8 compatibility": "./tests/phpunit_php8.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "./tests/phpunit_mock_objects.patch"}}, "composer-exit-on-patch-failure": true}, "require-dev": {"phpunit/phpunit": "~4.8", "cweagans/composer-patches": "^1.6"}}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "8ca99f91e45d49f214abc234ce69149591406bbd"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/8ca99f91e45d49f214abc234ce69149591406bbd", "type": "zip", "shasum": "", "reference": "8ca99f91e45d49f214abc234ce69149591406bbd"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.5"}, "time": "2020-09-10T13:35:23+00:00"}, {"version": "v1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "8be2418f0116572f1937083daf5cceb1bddc9f0d"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/8be2418f0116572f1937083daf5cceb1bddc9f0d", "type": "zip", "shasum": "", "reference": "8be2418f0116572f1937083daf5cceb1bddc9f0d"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.4"}, "time": "2020-08-29T22:15:08+00:00"}, {"version": "v1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "2ce537d75e610550f5337e41b2a971417999b028"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/2ce537d75e610550f5337e41b2a971417999b028", "type": "zip", "shasum": "", "reference": "2ce537d75e610550f5337e41b2a971417999b028"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.3"}, "time": "2020-08-18T21:00:59+00:00"}, {"homepage": "http://github.com/nrk/predis", "version": "v1.1.2", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net"}], "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "82eb18c6c3860849cb6e2ff34b0c4b39d5daee46"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/82eb18c6c3860849cb6e2ff34b0c4b39d5daee46", "type": "zip", "shasum": "", "reference": "82eb18c6c3860849cb6e2ff34b0c4b39d5daee46"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.2"}, "funding": [{"url": "https://www.paypal.me/tillkruss", "type": "custom"}, {"url": "https://github.com/tillkruss", "type": "github"}], "time": "2020-08-11T17:28:15+00:00", "require": {"php": ">=5.3.9", "cweagans/composer-patches": "^1.6"}, "require-dev": {"phpunit/phpunit": "~4.8"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "f0210e38881631afeafb56ab43405a92cafd9fd1"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/f0210e38881631afeafb56ab43405a92cafd9fd1", "type": "zip", "shasum": "", "reference": "f0210e38881631afeafb56ab43405a92cafd9fd1"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.1"}, "time": "2016-06-16T16:22:20+00:00", "require": {"php": ">=5.3.9"}, "extra": "__unset"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "0e17edbefb50c6cbd1acc4a6f6ef06399deb1af2"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/0e17edbefb50c6cbd1acc4a6f6ef06399deb1af2", "type": "zip", "shasum": "", "reference": "0e17edbefb50c6cbd1acc4a6f6ef06399deb1af2"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.0"}, "time": "2016-06-01T22:06:21+00:00"}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "9ead747663bb1b1ae017dfa0d152aca87792b42f"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/9ead747663bb1b1ae017dfa0d152aca87792b42f", "type": "zip", "shasum": "", "reference": "9ead747663bb1b1ae017dfa0d152aca87792b42f"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.0.4"}, "time": "2016-05-30T15:25:52+00:00", "require": {"php": ">=5.3.2"}}, {"description": "Flexible and feature-complete PHP client library for Redis", "version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "84060b9034d756b4d79641667d7f9efe1aeb8e04"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/84060b9034d756b4d79641667d7f9efe1aeb8e04", "type": "zip", "shasum": "", "reference": "84060b9034d756b4d79641667d7f9efe1aeb8e04"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.0.3"}, "time": "2015-07-30T18:34:15+00:00", "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "0b92e8d156c615a823e533655908df8cdffc3543"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/0b92e8d156c615a823e533655908df8cdffc3543", "type": "zip", "shasum": "", "reference": "0b92e8d156c615a823e533655908df8cdffc3543"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.0.2"}, "time": "2015-07-30T09:20:39+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "7a170b3d8123c556597b4fbdb88631f99de180c2"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/7a170b3d8123c556597b4fbdb88631f99de180c2", "type": "zip", "shasum": "", "reference": "7a170b3d8123c556597b4fbdb88631f99de180c2"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.0.1"}, "time": "2015-01-02T12:51:34+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "d4be306d0aca28b5633b96adef03b29fea569c2f"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/d4be306d0aca28b5633b96adef03b29fea569c2f", "type": "zip", "shasum": "", "reference": "d4be306d0aca28b5633b96adef03b29fea569c2f"}, "support": {"issues": "https://github.com/nrk/predis/issues", "source": "https://github.com/predis/predis/tree/v1.0.0"}, "time": "2014-08-01T09:59:50+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "v0.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "4123fcd85d61354c6c9900db76c9597dbd129bf6"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/4123fcd85d61354c6c9900db76c9597dbd129bf6", "type": "zip", "shasum": "", "reference": "4123fcd85d61354c6c9900db76c9597dbd129bf6"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.7"}, "time": "2014-08-01T09:43:10+00:00", "autoload": {"psr-0": {"Predis": "lib/"}}, "extra": "__unset"}, {"version": "v0.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "239864d9a0929f29bf8c5a8370ef743537c4953d"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/239864d9a0929f29bf8c5a8370ef743537c4953d", "type": "zip", "shasum": "", "reference": "239864d9a0929f29bf8c5a8370ef743537c4953d"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.6"}, "time": "2014-07-15T09:54:33+00:00"}, {"version": "v0.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "5f2eea628eb465d866ad2771927d83769c8f956c"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/5f2eea628eb465d866ad2771927d83769c8f956c", "type": "zip", "shasum": "", "reference": "5f2eea628eb465d866ad2771927d83769c8f956c"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.5"}, "time": "2014-01-16T14:10:29+00:00", "require-dev": "__unset"}, {"version": "v0.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "cb18d67b6ab6b8e567c1ab50fe999d1d624f9884"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/cb18d67b6ab6b8e567c1ab50fe999d1d624f9884", "type": "zip", "shasum": "", "reference": "cb18d67b6ab6b8e567c1ab50fe999d1d624f9884"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.4"}, "time": "2013-07-27T09:13:54+00:00"}, {"version": "v0.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "8c0498a893ec1bee5a98c602ad3a07855aa22113"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/8c0498a893ec1bee5a98c602ad3a07855aa22113", "type": "zip", "shasum": "", "reference": "8c0498a893ec1bee5a98c602ad3a07855aa22113"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.3"}, "time": "2013-02-18T14:03:45+00:00"}, {"version": "v0.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "aa1b070f9bc0fab48fcba017ba65ecd7198c86db"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/aa1b070f9bc0fab48fcba017ba65ecd7198c86db", "type": "zip", "shasum": "", "reference": "aa1b070f9bc0fab48fcba017ba65ecd7198c86db"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.2"}, "time": "2013-02-03T12:59:55+00:00"}, {"version": "v0.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "495e3c6f8aa38104c5744dcbfb00b128d50c7768"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/495e3c6f8aa38104c5744dcbfb00b128d50c7768", "type": "zip", "shasum": "", "reference": "495e3c6f8aa38104c5744dcbfb00b128d50c7768"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.1"}, "time": "2013-01-19T10:18:58+00:00"}, {"version": "v0.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "0a622aee31571a380a17e55a6ca095c30dbcfe55"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/0a622aee31571a380a17e55a6ca095c30dbcfe55", "type": "zip", "shasum": "", "reference": "0a622aee31571a380a17e55a6ca095c30dbcfe55"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.8.0"}, "time": "2012-10-22T23:46:05+00:00"}, {"version": "v0.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "4bc6f588fc688cc2f829359ac3f57c32883990a2"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/4bc6f588fc688cc2f829359ac3f57c32883990a2", "type": "zip", "shasum": "", "reference": "4bc6f588fc688cc2f829359ac3f57c32883990a2"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.7.3"}, "time": "2012-06-01T09:17:31+00:00", "require": {"php": ">=5.3.0"}, "suggest": "__unset"}, {"version": "v0.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "74cc0e32250c86f91428854aae7b41df7d5ecca2"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/74cc0e32250c86f91428854aae7b41df7d5ecca2", "type": "zip", "shasum": "", "reference": "74cc0e32250c86f91428854aae7b41df7d5ecca2"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.7.2"}, "time": "2012-04-01T08:09:20+00:00"}, {"version": "v0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "135fd5004b6a57081d43beffd73382f091b6c855"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/135fd5004b6a57081d43beffd73382f091b6c855", "type": "zip", "shasum": "", "reference": "135fd5004b6a57081d43beffd73382f091b6c855"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.7.1"}, "time": "2011-12-27T15:19:23+00:00"}, {"version": "v0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/predis/predis.git", "type": "git", "reference": "5561ce6bd3e1dc2337d7b3d01da7f86d8fe38fa3"}, "dist": {"url": "https://api.github.com/repos/predis/predis/zipball/5561ce6bd3e1dc2337d7b3d01da7f86d8fe38fa3", "type": "zip", "shasum": "", "reference": "5561ce6bd3e1dc2337d7b3d01da7f86d8fe38fa3"}, "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v0.7.0"}, "time": "2011-12-11T16:24:01+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 16 May 2025 18:31:48 GMT"}