{"minified": "composer/2.0", "packages": {"ramsey/uuid": [{"name": "ramsey/uuid", "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["uuid", "identifier", "guid"], "homepage": "", "version": "4.9.0", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/4e0e23cc785f0724a0e838279a9eb03f28b092a0", "type": "zip", "shasum": "", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0"}, "type": "library", "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.9.0"}, "funding": [], "time": "2025-06-25T14:20:11+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "extra": {"captainhook": {"force-install": true}}, "require": {"php": "^8.0", "brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "ramsey/collection": "^1.2 || ^2.0"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "replace": {"rhumsaa/uuid": "self.version"}}, {"version": "4.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "type": "zip", "shasum": "", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.8.1"}, "time": "2025-06-01T06:28:46+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "ramsey/collection": "^1.2 || ^2.0"}}, {"version": "4.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "6700833915c00f890615fbcb653faed513836836"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/6700833915c00f890615fbcb653faed513836836", "type": "zip", "shasum": "", "reference": "6700833915c00f890615fbcb653faed513836836"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.8.0"}, "time": "2025-06-01T02:32:15+00:00"}, {"version": "4.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "91039bc1faa45ba123c4328958e620d382ec7088"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/91039bc1faa45ba123c4328958e620d382ec7088", "type": "zip", "shasum": "", "reference": "91039bc1faa45ba123c4328958e620d382ec7088"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.6"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2024-04-27T21:32:50+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12", "ramsey/collection": "^1.2 || ^2.0"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}}, {"version": "4.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e", "type": "zip", "shasum": "", "reference": "5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.5"}, "time": "2023-11-08T05:53:05+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11", "ramsey/collection": "^1.2 || ^2.0"}}, {"version": "4.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "60a4c63ab724854332900504274f6150ff26d286"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/60a4c63ab724854332900504274f6150ff26d286", "type": "zip", "shasum": "", "reference": "60a4c63ab724854332900504274f6150ff26d286"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.4"}, "time": "2023-04-15T23:01:58+00:00"}, {"version": "4.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "433b2014e3979047db08a17a205f410ba3869cf2"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/433b2014e3979047db08a17a205f410ba3869cf2", "type": "zip", "shasum": "", "reference": "433b2014e3979047db08a17a205f410ba3869cf2"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.3"}, "time": "2023-01-12T18:13:24+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10", "ramsey/collection": "^1.2 || ^2.0"}}, {"version": "4.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "c1de830e5f8bacf58ec407b58016cd23cc5953fc"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/c1de830e5f8bacf58ec407b58016cd23cc5953fc", "type": "zip", "shasum": "", "reference": "c1de830e5f8bacf58ec407b58016cd23cc5953fc"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.2"}, "time": "2023-01-12T18:04:24+00:00", "require": {"ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "paragonie/random-lib": "^2", "phpunit/phpunit": "^8.5 || ^9", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter"}}, {"version": "4.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "a1acf96007170234a8399586a6e2ab8feba109d1"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/a1acf96007170234a8399586a6e2ab8feba109d1", "type": "zip", "shasum": "", "reference": "a1acf96007170234a8399586a6e2ab8feba109d1"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.1"}, "time": "2022-12-31T22:20:34+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10", "ramsey/collection": "^1.2 || ^2.0"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}}, {"version": "4.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "5ed9ad582647bbc3864ef78db34bdc1afdcf9b49"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/5ed9ad582647bbc3864ef78db34bdc1afdcf9b49", "type": "zip", "shasum": "", "reference": "5ed9ad582647bbc3864ef78db34bdc1afdcf9b49"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.0"}, "time": "2022-12-19T22:30:49+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10", "ramsey/collection": "^1.2"}}, {"version": "4.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "ad63bc700e7d021039e30ce464eba384c4a1d40f"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/ad63bc700e7d021039e30ce464eba384c4a1d40f", "type": "zip", "shasum": "", "reference": "ad63bc700e7d021039e30ce464eba384c4a1d40f"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.6.0"}, "time": "2022-11-05T23:03:38+00:00", "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10", "ramsey/collection": "^1.0"}}, {"version": "4.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "a161a26d917604dc6d3aa25100fddf2556e9f35d"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/a161a26d917604dc6d3aa25100fddf2556e9f35d", "type": "zip", "shasum": "", "reference": "a161a26d917604dc6d3aa25100fddf2556e9f35d"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.5.1"}, "time": "2022-09-16T03:22:46+00:00", "require": {"php": "^8.0", "ext-ctype": "*", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10", "ramsey/collection": "^1.0"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}}, {"version": "4.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "ef842484ba57f163c6d465ab744bfecb872a11d4"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/ef842484ba57f163c6d465ab744bfecb872a11d4", "type": "zip", "shasum": "", "reference": "ef842484ba57f163c6d465ab744bfecb872a11d4"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.5.0"}, "time": "2022-09-15T01:44:53+00:00"}, {"version": "4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "373f7bacfcf3de038778ff27dcce5672ddbf4c8a"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/373f7bacfcf3de038778ff27dcce5672ddbf4c8a", "type": "zip", "shasum": "", "reference": "373f7bacfcf3de038778ff27dcce5672ddbf4c8a"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.4.0"}, "time": "2022-08-05T17:58:37+00:00", "require": {"php": "^8.0", "ext-ctype": "*", "ext-json": "*", "brick/math": "^0.8 || ^0.9 || ^0.10", "ramsey/collection": "^1.0"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5 || ^9", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}}, {"version": "4.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "8505afd4fea63b81a85d3b7b53ac3cb8dc347c28"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/8505afd4fea63b81a85d3b7b53ac3cb8dc347c28", "type": "zip", "shasum": "", "reference": "8505afd4fea63b81a85d3b7b53ac3cb8dc347c28"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.3.1"}, "time": "2022-03-27T21:42:02+00:00", "require": {"php": "^8.0", "ext-ctype": "*", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5 || ^9", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}}, {"version": "4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "8ced2afadd9ce455e2d5919d27c57e344bae4a09"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/8ced2afadd9ce455e2d5919d27c57e344bae4a09", "type": "zip", "shasum": "", "reference": "8ced2afadd9ce455e2d5919d27c57e344bae4a09"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.3.0"}, "time": "2022-03-26T22:00:50+00:00", "extra": {"captainhook": {"force-install": true}, "branch-alias": {"dev-main": "4.x-dev"}}}, {"version": "4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "type": "zip", "shasum": "", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.3"}, "time": "2021-09-25T23:10:38+00:00", "require": {"php": "^7.2 || ^8.0", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.14"}}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "445999c26a53aca1faa0b7b8f4f0d61fc9484c71"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/445999c26a53aca1faa0b7b8f4f0d61fc9484c71", "type": "zip", "shasum": "", "reference": "445999c26a53aca1faa0b7b8f4f0d61fc9484c71"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.2"}, "time": "2021-09-24T18:53:47+00:00", "require": {"php": "^7.2 || ~8.0.0 || ~8.1.0", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.14"}}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "fe665a03df4f056aa65af552a96e1976df8c8dae"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/fe665a03df4f056aa65af552a96e1976df8c8dae", "type": "zip", "shasum": "", "reference": "fe665a03df4f056aa65af552a96e1976df8c8dae"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.1"}, "time": "2021-08-11T01:06:55+00:00", "require": {"php": "^7.2 || ^8", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "7231612a5221f5524d3575bebdce20eeef8547a1"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/7231612a5221f5524d3575bebdce20eeef8547a1", "type": "zip", "shasum": "", "reference": "7231612a5221f5524d3575bebdce20eeef8547a1"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.0"}, "time": "2021-08-06T22:30:43+00:00"}, {"homepage": "https://github.com/ramsey/uuid", "version": "4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "2df6bbdf0133247bfa0063ccbcf59185a243f52d"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/2df6bbdf0133247bfa0063ccbcf59185a243f52d", "type": "zip", "shasum": "", "reference": "2df6bbdf0133247bfa0063ccbcf59185a243f52d"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid"}, "time": "2021-09-25T23:00:53+00:00", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "require": {"php": "^7.2 || ^8.0", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^3", "dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7.0", "doctrine/annotations": "^1.8", "goaop/framework": "^2", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-mockery": "^1.3", "php-mock/php-mock-phpunit": "^2.5", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^0.17.1", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "psy/psysh": "^0.10.0", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "3.9.4"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter"}}, {"version": "4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "97d153506bedcab27aa3cc421c9f091d96cb61f8"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/97d153506bedcab27aa3cc421c9f091d96cb61f8", "type": "zip", "shasum": "", "reference": "97d153506bedcab27aa3cc421c9f091d96cb61f8"}, "time": "2021-09-24T18:48:21+00:00", "require": {"php": "^7.2 || ~8.0.0", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "cd4032040a750077205918c86049aa0f43d22947"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/cd4032040a750077205918c86049aa0f43d22947", "type": "zip", "shasum": "", "reference": "cd4032040a750077205918c86049aa0f43d22947"}, "time": "2020-08-18T17:17:46+00:00", "require": {"php": "^7.2 || ^8", "ext-json": "*", "brick/math": "^0.8 || ^0.9", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "988dbefc7878d0a35f12afb4df1f7dd0bd153c43"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/988dbefc7878d0a35f12afb4df1f7dd0bd153c43", "type": "zip", "shasum": "", "reference": "988dbefc7878d0a35f12afb4df1f7dd0bd153c43"}, "time": "2020-07-28T16:51:01+00:00", "require": {"php": "^7.2 || ^8", "ext-json": "*", "brick/math": "^0.8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "cf8c74db9d51e97504e1f5b976283f50da8e7ff2"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/cf8c74db9d51e97504e1f5b976283f50da8e7ff2", "type": "zip", "shasum": "", "reference": "cf8c74db9d51e97504e1f5b976283f50da8e7ff2"}, "time": "2021-09-25T22:59:24+00:00", "require": {"php": "^7.2 || ^8.0", "ext-json": "*", "brick/math": "^0.8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^3", "dealerdirect/phpcodesniffer-composer-installer": "^0.6.2", "doctrine/annotations": "^1.8", "goaop/framework": "^2", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-mockery": "^1.3", "php-mock/php-mock-phpunit": "^2.5", "php-parallel-lint/php-parallel-lint": "^1.1", "phpstan/extension-installer": "^1.0", "phpstan/phpdoc-parser": "0.4.3", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "psy/psysh": "^0.10.0", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "3.9.4"}}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "833c5c95299aa75a6c7f00d8c80aa9b246c87671"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/833c5c95299aa75a6c7f00d8c80aa9b246c87671", "type": "zip", "shasum": "", "reference": "833c5c95299aa75a6c7f00d8c80aa9b246c87671"}, "time": "2021-09-24T18:44:03+00:00", "require": {"php": "^7.2 || ~8.0.0", "ext-json": "*", "brick/math": "^0.8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "ba8fff1d3abb8bb4d35a135ed22a31c6ef3ede3d"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/ba8fff1d3abb8bb4d35a135ed22a31c6ef3ede3d", "type": "zip", "shasum": "", "reference": "ba8fff1d3abb8bb4d35a135ed22a31c6ef3ede3d"}, "time": "2020-03-29T20:13:32+00:00", "require": {"php": "^7.2 || ^8", "ext-json": "*", "brick/math": "^0.8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "2c9644b1d0c2bc58732413252bcbb6dce2eb0e0e"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/2c9644b1d0c2bc58732413252bcbb6dce2eb0e0e", "type": "zip", "shasum": "", "reference": "2c9644b1d0c2bc58732413252bcbb6dce2eb0e0e"}, "time": "2020-03-22T02:34:13+00:00", "require-dev": {"codeception/aspect-mock": "^3", "dealerdirect/phpcodesniffer-composer-installer": "^0.6.2", "doctrine/annotations": "^1.8", "goaop/framework": "^2", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-mockery": "^1.3", "php-mock/php-mock-phpunit": "^2.5", "phpstan/extension-installer": "^1.0", "phpstan/phpdoc-parser": "0.4.3", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "psy/psysh": "^0.10.0", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "3.9.4"}}, {"description": "A PHP library for generating RFC 4122 version 1, 2, 3, 4, and 5 universally unique identifiers (UUID).", "version": "4.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "5ee7f7aaf21be3612cd76f0a0841ff8194aa86af"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/5ee7f7aaf21be3612cd76f0a0841ff8194aa86af", "type": "zip", "shasum": "", "reference": "5ee7f7aaf21be3612cd76f0a0841ff8194aa86af"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "time": "2020-03-01T06:26:02+00:00", "require": {"php": "^7.2 | ^8", "ext-json": "*", "brick/math": "^0.8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^3", "dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "doctrine/annotations": "^1.8", "goaop/framework": "^2", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-mockery": "^1.3", "php-mock/php-mock-phpunit": "^2.5", "phpstan/extension-installer": "^1.0", "phpstan/phpdoc-parser": "0.4.1", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^3.7.2"}}, {"version": "4.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "f374dae8b2e40bdd8cabc16edfbf87bddad9f11d"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/f374dae8b2e40bdd8cabc16edfbf87bddad9f11d", "type": "zip", "shasum": "", "reference": "f374dae8b2e40bdd8cabc16edfbf87bddad9f11d"}, "time": "2020-02-27T03:12:45+00:00"}, {"version": "4.0.0-alpha5", "version_normalized": "*******-alpha5", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "b00ba84a385293a3e49c881e341e8c58aa1f445b"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/b00ba84a385293a3e49c881e341e8c58aa1f445b", "type": "zip", "shasum": "", "reference": "b00ba84a385293a3e49c881e341e8c58aa1f445b"}, "time": "2020-02-23T06:52:32+00:00"}, {"version": "4.0.0-alpha4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "a8f1692b7fb4c749cce262dddee20d07ed43b40e"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/a8f1692b7fb4c749cce262dddee20d07ed43b40e", "type": "zip", "shasum": "", "reference": "a8f1692b7fb4c749cce262dddee20d07ed43b40e"}, "time": "2020-02-23T05:00:08+00:00", "require": {"php": "^7.2 | ^8", "ext-json": "*", "brick/math": "^0.8", "symfony/polyfill-ctype": "^1.8"}}, {"version": "4.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "a7cf07a2d8adbe4a9a2fb2ab821b3bd9167ac40c"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/a7cf07a2d8adbe4a9a2fb2ab821b3bd9167ac40c", "type": "zip", "shasum": "", "reference": "a7cf07a2d8adbe4a9a2fb2ab821b3bd9167ac40c"}, "time": "2020-02-21T08:51:31+00:00"}, {"version": "4.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "1396eaf7565f478c65fb1a2d8a5c18ff3a0df41a"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/1396eaf7565f478c65fb1a2d8a5c18ff3a0df41a", "type": "zip", "shasum": "", "reference": "1396eaf7565f478c65fb1a2d8a5c18ff3a0df41a"}, "time": "2020-02-21T07:46:04+00:00"}, {"description": "A PHP 7.2+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "version": "4.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "9988854cf1ab240a9596fa9747bd41aba571de03"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/9988854cf1ab240a9596fa9747bd41aba571de03", "type": "zip", "shasum": "", "reference": "9988854cf1ab240a9596fa9747bd41aba571de03"}, "time": "2020-01-22T06:41:45+00:00"}, {"description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "version": "3.9.7", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "type": "zip", "shasum": "", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178"}, "time": "2022-12-19T21:55:10+00:00", "require": {"php": "^5.4 | ^7.0 | ^8.0", "ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | >=2.1.0 <=2.3.2", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "nikic/php-parser": "<=4.5.0", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1 | ^2.6", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": ">=4.8.36 <9.0.0 | >=9.3.0", "squizlabs/php_codesniffer": "^3.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter"}, "extra": "__unset"}, {"version": "3.9.6", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "ffa80ab953edd85d5b6c004f96181a538aad35a3"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/ffa80ab953edd85d5b6c004f96181a538aad35a3", "type": "zip", "shasum": "", "reference": "ffa80ab953edd85d5b6c004f96181a538aad35a3"}, "time": "2021-09-25T23:07:42+00:00", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}}, {"version": "3.9.5", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "e62af90c1cf1552167e69e68c4c96b6a07a919c7"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/e62af90c1cf1552167e69e68c4c96b6a07a919c7", "type": "zip", "shasum": "", "reference": "e62af90c1cf1552167e69e68c4c96b6a07a919c7"}, "time": "2021-09-24T18:27:08+00:00", "require": {"php": "^5.4 | ^7 | ~8.0.0 | ~8.1.0", "ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "symfony/polyfill-ctype": "^1.8"}}, {"version": "3.9.4", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "be2451bef8147b7352a28fb4cddb08adc497ada3"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/be2451bef8147b7352a28fb4cddb08adc497ada3", "type": "zip", "shasum": "", "reference": "be2451bef8147b7352a28fb4cddb08adc497ada3"}, "time": "2021-08-06T20:32:15+00:00", "require": {"php": "^5.4 | ^7 | ^8", "ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | ^2.1", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1", "phpunit/phpunit": "^4.8 | ^5.4 | ^6.5", "squizlabs/php_codesniffer": "^3.5"}}, {"version": "3.9.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/7e1633a6964b48589b142d60542f9ed31bd37a92", "type": "zip", "shasum": "", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92"}, "time": "2020-02-21T04:36:14+00:00", "require": {"php": "^5.4 | ^7 | ^8", "ext-json": "*", "paragonie/random_compat": "^1 | ^2 | 9.99.99", "symfony/polyfill-ctype": "^1.8"}}, {"version": "3.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "7779489a47d443f845271badbdcedfe4df8e06fb"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/7779489a47d443f845271badbdcedfe4df8e06fb", "type": "zip", "shasum": "", "reference": "7779489a47d443f845271badbdcedfe4df8e06fb"}, "time": "2019-12-17T08:18:51+00:00"}, {"version": "3.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "5ac2740e0c8c599d2bbe7f113a939f2b5b216c67"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/5ac2740e0c8c599d2bbe7f113a939f2b5b216c67", "type": "zip", "shasum": "", "reference": "5ac2740e0c8c599d2bbe7f113a939f2b5b216c67"}, "time": "2019-12-01T04:55:27+00:00", "require": {"php": "^5.4 | ^7", "ext-json": "*", "paragonie/random_compat": "^1 | ^2 | 9.99.99", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | ^2.1", "jakub-onderka/php-parallel-lint": "^0.9.0", "mockery/mockery": "^0.9.9", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1", "phpunit/phpunit": "^4.8 | ^5.4 | ^6.5", "squizlabs/php_codesniffer": "^2.3"}}, {"version": "3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "8fde15adcc90190a65e173c34da4ab1f29e05c5b"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/8fde15adcc90190a65e173c34da4ab1f29e05c5b", "type": "zip", "shasum": "", "reference": "8fde15adcc90190a65e173c34da4ab1f29e05c5b"}, "time": "2019-11-30T23:55:53+00:00"}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "d09ea80159c1929d75b3f9c60504d613aeb4a1e3"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/d09ea80159c1929d75b3f9c60504d613aeb4a1e3", "type": "zip", "shasum": "", "reference": "d09ea80159c1929d75b3f9c60504d613aeb4a1e3"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid"}, "time": "2018-07-19T23:38:55+00:00", "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}}, "require": {"php": "^5.4 || ^7.0", "paragonie/random_compat": "^1.0|^2.0|9.99.99", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"codeception/aspect-mock": "^1.0 | ~2.0.0", "doctrine/annotations": "~1.2.0", "goaop/framework": "1.0.0-alpha.2 | ^1.0 | ~2.1.0", "ircmaxell/random-lib": "^1.1", "jakub-onderka/php-parallel-lint": "^0.9.0", "mockery/mockery": "^0.9.9", "moontoast/math": "^1.1", "php-mock/php-mock-phpunit": "^0.3|^1.1", "phpunit/phpunit": "^4.7|^5.0|^6.5", "squizlabs/php_codesniffer": "^2.3"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ircmaxell/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type.", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid"}}, {"version": "3.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "44abcdad877d9a46685a3a4d221e3b2c4b87cb76"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/44abcdad877d9a46685a3a4d221e3b2c4b87cb76", "type": "zip", "shasum": "", "reference": "44abcdad877d9a46685a3a4d221e3b2c4b87cb76"}, "time": "2018-01-20T00:28:24+00:00", "require": {"php": "^5.4 || ^7.0", "paragonie/random_compat": "^1.0|^2.0"}, "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "mockery/mockery": "^0.9.9", "goaop/framework": "1.0.0-alpha.2 | ^1.0 | ^2.1", "doctrine/annotations": "~1.2.0", "codeception/aspect-mock": "^1.0 | ~2.0.0", "php-mock/php-mock-phpunit": "^0.3|^1.1"}, "suggest": {"ircmaxell/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type.", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid"}}, {"version": "3.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "bba83ad77bb9deb6d3c352a7361b818e415b221d"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/bba83ad77bb9deb6d3c352a7361b818e415b221d", "type": "zip", "shasum": "", "reference": "bba83ad77bb9deb6d3c352a7361b818e415b221d"}, "time": "2018-01-13T22:22:03+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "mockery/mockery": "^0.9.4", "goaop/framework": "1.0.0-alpha.2 | ^1.0 | ^2.1", "doctrine/annotations": "~1.2.0", "codeception/aspect-mock": "^1.0 | ~2.0.0", "php-mock/php-mock-phpunit": "^0.3|^1.1"}}, {"version": "3.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "45cffe822057a09e05f7bd09ec5fb88eeecd2334"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/45cffe822057a09e05f7bd09ec5fb88eeecd2334", "type": "zip", "shasum": "", "reference": "45cffe822057a09e05f7bd09ec5fb88eeecd2334"}, "time": "2017-09-22T20:46:04+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|>=5.0 <5.4", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "mockery/mockery": "^0.9.4", "goaop/framework": "1.0.0-alpha.2 | ^1.0 | ^2.1", "doctrine/annotations": "~1.2.0", "codeception/aspect-mock": "^1.0 | ^2.0", "php-mock/php-mock-phpunit": "^0.3|^1.1"}}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "0ef23d1b10cf1bc576e9d865a7e9c47982c5715e"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/0ef23d1b10cf1bc576e9d865a7e9c47982c5715e", "type": "zip", "shasum": "", "reference": "0ef23d1b10cf1bc576e9d865a7e9c47982c5715e"}, "time": "2017-08-04T13:39:04+00:00"}, {"version": "3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "4ae32dd9ab8860a4bbd750ad269cba7f06f7934e"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/4ae32dd9ab8860a4bbd750ad269cba7f06f7934e", "type": "zip", "shasum": "", "reference": "4ae32dd9ab8860a4bbd750ad269cba7f06f7934e"}, "time": "2017-03-26T20:37:53+00:00"}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "0b7bdfb180e72c8d76e75a649ced67e392201458"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/0b7bdfb180e72c8d76e75a649ced67e392201458", "type": "zip", "shasum": "", "reference": "0b7bdfb180e72c8d76e75a649ced67e392201458"}, "time": "2017-03-18T15:38:09+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|>=5.0 <5.4", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "mockery/mockery": "^0.9.4", "goaop/framework": "1.0.0-alpha.2", "doctrine/annotations": "~1.2.0", "codeception/aspect-mock": "1.0.0", "php-mock/php-mock-phpunit": "^0.3|^1.1"}}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "5677cfe02397dd6b58c861870dfaa5d9007d3954"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/5677cfe02397dd6b58c861870dfaa5d9007d3954", "type": "zip", "shasum": "", "reference": "5677cfe02397dd6b58c861870dfaa5d9007d3954"}, "time": "2016-11-22T19:21:44+00:00", "require": {"php": ">=5.4", "paragonie/random_compat": "^1.0|^2.0"}}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "a07797b986671b0dc823885a81d5e3516b931599"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/a07797b986671b0dc823885a81d5e3516b931599", "type": "zip", "shasum": "", "reference": "a07797b986671b0dc823885a81d5e3516b931599"}, "time": "2016-10-02T15:51:17+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|>=5.0 <5.4", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "mockery/mockery": "^0.9.4", "goaop/framework": "1.0.0-alpha.2", "codeception/aspect-mock": "1.0.0"}}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "a6d15c8618ea3951fd54d34e326b68d3d0bc0786"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/a6d15c8618ea3951fd54d34e326b68d3d0bc0786", "type": "zip", "shasum": "", "reference": "a6d15c8618ea3951fd54d34e326b68d3d0bc0786"}, "time": "2016-08-02T18:39:32+00:00"}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "b4fe3b7387cb323fd15ad5837cae992422c9fa5c"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/b4fe3b7387cb323fd15ad5837cae992422c9fa5c", "type": "zip", "shasum": "", "reference": "b4fe3b7387cb323fd15ad5837cae992422c9fa5c"}, "time": "2016-04-24T00:30:41+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "mockery/mockery": "^0.9.4", "goaop/framework": "1.0.0-alpha.2", "codeception/aspect-mock": "1.0.0"}}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "fb12163881a98588bcdc4ba820a96e0ff5ad97dc"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/fb12163881a98588bcdc4ba820a96e0ff5ad97dc", "type": "zip", "shasum": "", "reference": "fb12163881a98588bcdc4ba820a96e0ff5ad97dc"}, "time": "2016-04-24T00:08:55+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "f44f53e5ceb7474a83b6e11e6623ff9d6f6da598"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/f44f53e5ceb7474a83b6e11e6623ff9d6f6da598", "type": "zip", "shasum": "", "reference": "f44f53e5ceb7474a83b6e11e6623ff9d6f6da598"}, "time": "2016-03-22T18:40:53+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1", "mockery/mockery": "^0.9.4"}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "adee1ba4a6885ed800021a98dd69ae2394d695ec"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/adee1ba4a6885ed800021a98dd69ae2394d695ec", "type": "zip", "shasum": "", "reference": "adee1ba4a6885ed800021a98dd69ae2394d695ec"}, "time": "2016-02-17T23:32:34+00:00", "require": {"php": ">=5.4"}, "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7|^5.0", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1"}, "extra": "__unset"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "3cc2dd253e296ce05f99635b2f633048adfbaa96"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/3cc2dd253e296ce05f99635b2f633048adfbaa96", "type": "zip", "shasum": "", "reference": "3cc2dd253e296ce05f99635b2f633048adfbaa96"}, "time": "2015-12-17T15:21:44+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1", "apigen/apigen": "^4.1"}, "suggest": {"ircmaxell/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type.", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "3c84b9e2965a5fa666dec8617a3a66a8179c6ca8"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/3c84b9e2965a5fa666dec8617a3a66a8179c6ca8", "type": "zip", "shasum": "", "reference": "3c84b9e2965a5fa666dec8617a3a66a8179c6ca8"}, "time": "2015-10-21T16:27:25+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "0c0ac34e867219bb9936cc5b823f8a5af840d7eb"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/0c0ac34e867219bb9936cc5b823f8a5af840d7eb", "type": "zip", "shasum": "", "reference": "0c0ac34e867219bb9936cc5b823f8a5af840d7eb"}, "time": "2015-09-28T16:27:51+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "suggest": {"ramsey/uuid-doctrine": "Allow the use of a UUID as Doctrine field type.", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "moontoast/math": "Support for converting UUID to 128-bit integer (in string form).", "ircmaxell/random-lib": "Provides RandomLib to use with the RandomLibAdapter"}}, {"version": "3.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "d5276bdba6ec6e4fc91eaf2727177332b333642a"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/d5276bdba6ec6e4fc91eaf2727177332b333642a", "type": "zip", "shasum": "", "reference": "d5276bdba6ec6e4fc91eaf2727177332b333642a"}, "time": "2015-08-31T14:09:22+00:00", "require-dev": {"moontoast/math": "^1.1", "ircmaxell/random-lib": "^1.1", "phpunit/phpunit": "^4.7", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "satooshi/php-coveralls": "^0.6.1"}}, {"homepage": "https://github.com/ramsey/rhumsaa-uuid", "version": "3.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "41f8db48808eee34c5e0b1199283dcce968fd117"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/41f8db48808eee34c5e0b1199283dcce968fd117", "type": "zip", "shasum": "", "reference": "41f8db48808eee34c5e0b1199283dcce968fd117"}, "support": {"issues": "https://github.com/ramsey/rhumsaa-uuid/issues", "source": "https://github.com/ramsey/rhumsaa-uuid"}, "time": "2015-07-28T20:40:04+00:00"}, {"version": "3.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "b5e431e7f132387d91c9cd798fa34a29e15c9fa4"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/b5e431e7f132387d91c9cd798fa34a29e15c9fa4", "type": "zip", "shasum": "", "reference": "b5e431e7f132387d91c9cd798fa34a29e15c9fa4"}, "time": "2015-07-28T16:45:12+00:00"}, {"homepage": "https://github.com/ramsey/uuid", "version": "3.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "32d2a7ffe9aca7370c98f092f86c0e16f2b2d15b"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/32d2a7ffe9aca7370c98f092f86c0e16f2b2d15b", "type": "zip", "shasum": "", "reference": "32d2a7ffe9aca7370c98f092f86c0e16f2b2d15b"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid"}, "time": "2015-07-16T18:03:15+00:00"}, {"description": "A PHP 5.3+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "version": "2.9.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "homepage": "http://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "b2ef4dd9584268d73f92f752a62bc24cd534dc9a"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/b2ef4dd9584268d73f92f752a62bc24cd534dc9a", "type": "zip", "shasum": "", "reference": "b2ef4dd9584268d73f92f752a62bc24cd534dc9a"}, "time": "2016-03-22T18:20:19+00:00", "autoload": {"psr-4": {"Rhumsaa\\Uuid\\": "src/"}}, "bin": ["bin/uuid"], "require": {"php": ">=5.3.3", "paragonie/random_compat": "^1.0|^2.0"}, "require-dev": {"moontoast/math": "~1.1", "symfony/console": "~2.3|~3.0", "doctrine/dbal": ">=2.3", "phpunit/phpunit": "~4.1|~5.0", "satooshi/php-coveralls": "~0.6", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0"}, "suggest": {"moontoast/math": "Support for converting UUID to 128-bit integer (in string form).", "symfony/console": "Support for use of the bin/uuid command line tool.", "doctrine/dbal": "Allow the use of a UUID as doctrine field type."}, "extra": "__unset"}, {"version": "2.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "805d8e1894c52e5b1582e75ca8803a8d85650df9"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/805d8e1894c52e5b1582e75ca8803a8d85650df9", "type": "zip", "shasum": "", "reference": "805d8e1894c52e5b1582e75ca8803a8d85650df9"}, "time": "2015-12-17T16:54:24+00:00", "require": {"php": ">=5.3.3"}}, {"version": "2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "767a5b5f70cd990c04ef21d8374794d1a02fa9e8"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/767a5b5f70cd990c04ef21d8374794d1a02fa9e8", "type": "zip", "shasum": "", "reference": "767a5b5f70cd990c04ef21d8374794d1a02fa9e8"}, "time": "2015-08-31T13:34:50+00:00", "require-dev": {"moontoast/math": "~1.1", "symfony/console": "~2.3", "doctrine/dbal": ">=2.3", "phpunit/phpunit": "~4.1", "satooshi/php-coveralls": "~0.6", "squizlabs/php_codesniffer": "^2.3", "jakub-onderka/php-parallel-lint": "^0.9.0"}}, {"description": "NO LONGER MAINTAINED. Use ramsey/uuid instead. A PHP 5.3+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/rhumsaa-uuid", "version": "2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "9c1e2d34bdefd42608c612e08d6e1da1e13a3530"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/9c1e2d34bdefd42608c612e08d6e1da1e13a3530", "type": "zip", "shasum": "", "reference": "9c1e2d34bdefd42608c612e08d6e1da1e13a3530"}, "support": {"issues": "https://github.com/ramsey/rhumsaa-uuid/issues", "source": "https://github.com/ramsey/rhumsaa-uuid"}, "time": "2015-07-23T19:00:41+00:00"}, {"description": "A PHP 5.3+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "version": "2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "c1d7d8f0ac860fceaadadbda7154e8b5d2f9789e"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/c1d7d8f0ac860fceaadadbda7154e8b5d2f9789e", "type": "zip", "shasum": "", "reference": "c1d7d8f0ac860fceaadadbda7154e8b5d2f9789e"}, "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid"}, "time": "2015-06-16T15:12:41+00:00", "require-dev": {"moontoast/math": "~1.1", "symfony/console": "~2.3", "doctrine/dbal": ">=2.3", "phpunit/phpunit": "~4.1", "satooshi/php-coveralls": "~0.6"}}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "cca98c652cac412c9c2f109c69e5532f313435fc"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/cca98c652cac412c9c2f109c69e5532f313435fc", "type": "zip", "shasum": "", "reference": "cca98c652cac412c9c2f109c69e5532f313435fc"}, "time": "2014-11-09T18:42:56+00:00", "extra": {"branch-alias": {"dev-master": "2.8.x-dev"}}, "replace": "__unset"}, {"version": "2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "eb1d371067aa8c2b83d6691837ee4896a6f76a44"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/eb1d371067aa8c2b83d6691837ee4896a6f76a44", "type": "zip", "shasum": "", "reference": "eb1d371067aa8c2b83d6691837ee4896a6f76a44"}, "time": "2014-10-30T03:36:49+00:00", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}}, {"version": "2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "b976326ca5977d7333f34e3c828ae7c22a49a65a"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/b976326ca5977d7333f34e3c828ae7c22a49a65a", "type": "zip", "shasum": "", "reference": "b976326ca5977d7333f34e3c828ae7c22a49a65a"}, "time": "2014-08-27T22:39:41+00:00"}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "ccabc07c649fbac6b409fe42439e042232f9db58"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/ccabc07c649fbac6b409fe42439e042232f9db58", "type": "zip", "shasum": "", "reference": "ccabc07c649fbac6b409fe42439e042232f9db58"}, "time": "2014-07-28T17:51:57+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "8569983a11ed96087cdf94c7aa65866138681beb"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/8569983a11ed96087cdf94c7aa65866138681beb", "type": "zip", "shasum": "", "reference": "8569983a11ed96087cdf94c7aa65866138681beb"}, "time": "2014-02-22T00:08:43+00:00", "autoload": {"psr-0": {"Rhumsaa\\Uuid": "src/"}}, "require-dev": {"moontoast/math": "~1.1", "symfony/console": "~2.3", "doctrine/dbal": ">=2.3", "phpunit/phpunit": "~3.7"}}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "f4d16d233a2f570f0689d4ebab2b166632a9575d"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/f4d16d233a2f570f0689d4ebab2b166632a9575d", "type": "zip", "shasum": "", "reference": "f4d16d233a2f570f0689d4ebab2b166632a9575d"}, "time": "2014-01-31T18:32:03+00:00", "extra": {"branch-alias": {"dev-master": "2.6.x-dev"}}, "require": {"php": ">=5.3.3", "moontoast/math": "~1.1", "symfony/console": "~2.4"}, "require-dev": {"doctrine/dbal": ">=2.3", "phpunit/phpunit": "~3.7"}, "suggest": {"doctrine/dbal": "Allow the use of a UUID as doctrine field type."}}, {"version": "2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "ecbc32913fca757b5225213f5de02d1101e4e645"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/ecbc32913fca757b5225213f5de02d1101e4e645", "type": "zip", "shasum": "", "reference": "ecbc32913fca757b5225213f5de02d1101e4e645"}, "time": "2014-01-28T00:25:52+00:00", "suggest": {"doctrine/dbal": "Allow the use of a UUID as doctrine field type.", "moontoast/math": "Provides support for large integers."}}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "806f46722fcd2a5b151a94471d0d5d63d519e1bc"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/806f46722fcd2a5b151a94471d0d5d63d519e1bc", "type": "zip", "shasum": "", "reference": "806f46722fcd2a5b151a94471d0d5d63d519e1bc"}, "time": "2014-01-17T23:40:22+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "4b0bd0f6cb740dec8c4aebf20957925981a21706"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/4b0bd0f6cb740dec8c4aebf20957925981a21706", "type": "zip", "shasum": "", "reference": "4b0bd0f6cb740dec8c4aebf20957925981a21706"}, "time": "2013-10-30T22:43:52+00:00", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"doctrine/dbal": ">=2.3", "moontoast/math": "1.1.0", "phpunit/phpunit": "~3.7"}, "bin": "__unset"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "721181e32766487294ee3d1e49ef2a52d605cc66"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/721181e32766487294ee3d1e49ef2a52d605cc66", "type": "zip", "shasum": "", "reference": "721181e32766487294ee3d1e49ef2a52d605cc66"}, "time": "2013-07-30T00:05:42+00:00", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "require-dev": {"doctrine/dbal": ">=2.3", "moontoast/math": "1.1.0"}}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "eca9792eb013d0036a3eeb971a35c72a561df997"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/eca9792eb013d0036a3eeb971a35c72a561df997", "type": "zip", "shasum": "", "reference": "eca9792eb013d0036a3eeb971a35c72a561df997"}, "time": "2013-07-16T15:13:38+00:00", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "0edd54dd466a4cf2101d74702a3d4ad29b2ddd57"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/0edd54dd466a4cf2101d74702a3d4ad29b2ddd57", "type": "zip", "shasum": "", "reference": "0edd54dd466a4cf2101d74702a3d4ad29b2ddd57"}, "time": "2013-07-04T16:06:55+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "1e5a0dcaf55644aab427a6fdc9abecc5be9a2340"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/1e5a0dcaf55644aab427a6fdc9abecc5be9a2340", "type": "zip", "shasum": "", "reference": "1e5a0dcaf55644aab427a6fdc9abecc5be9a2340"}, "time": "2013-07-03T23:06:31+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "9d175d1d7960103a51337f99a522275e0a7a966a"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/9d175d1d7960103a51337f99a522275e0a7a966a", "type": "zip", "shasum": "", "reference": "9d175d1d7960103a51337f99a522275e0a7a966a"}, "time": "2013-04-29T20:38:09+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "79a655e886df849fd678ec5df6f335134b1e1249"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/79a655e886df849fd678ec5df6f335134b1e1249", "type": "zip", "shasum": "", "reference": "79a655e886df849fd678ec5df6f335134b1e1249"}, "time": "2013-04-15T23:43:05+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "257d668b6e96eca89a315d99dba3a4ef2c9e9132"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/257d668b6e96eca89a315d99dba3a4ef2c9e9132", "type": "zip", "shasum": "", "reference": "257d668b6e96eca89a315d99dba3a4ef2c9e9132"}, "time": "2013-02-11T19:16:50+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}, {"description": "A PHP 5.3+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID). Requires a 64-bit build of PHP.", "version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "96d8414fc796187e6699a9747f65d0fb1e2568c5"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/96d8414fc796187e6699a9747f65d0fb1e2568c5", "type": "zip", "shasum": "", "reference": "96d8414fc796187e6699a9747f65d0fb1e2568c5"}, "time": "2012-11-29T16:24:57+00:00", "require-dev": {"doctrine/dbal": ">=2.3"}, "suggest": {"doctrine/dbal": "Allow the use of a UUID as doctrine field type."}, "extra": "__unset"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "5215d7e0b09d5fbca443722ca3c73a7476e9fcad"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/5215d7e0b09d5fbca443722ca3c73a7476e9fcad", "type": "zip", "shasum": "", "reference": "5215d7e0b09d5fbca443722ca3c73a7476e9fcad"}, "time": "2012-08-28T03:03:23+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "2544a394331c983e287ddb14d599404bcc63879c"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/2544a394331c983e287ddb14d599404bcc63879c", "type": "zip", "shasum": "", "reference": "2544a394331c983e287ddb14d599404bcc63879c"}, "time": "2012-08-07T01:44:44+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "homepage": "http://benramsey.com"}], "source": {"url": "https://github.com/ramsey/uuid.git", "type": "git", "reference": "7844efd86d56fdc247436485aa8bfbf3d6ee8096"}, "dist": {"url": "https://api.github.com/repos/ramsey/uuid/zipball/7844efd86d56fdc247436485aa8bfbf3d6ee8096", "type": "zip", "shasum": "", "reference": "7844efd86d56fdc247436485aa8bfbf3d6ee8096"}, "time": "2012-07-19T23:56:14+00:00", "require-dev": "__unset", "suggest": "__unset"}]}, "security-advisories": [], "last-modified": "Wed, 25 Jun 2025 23:04:08 GMT"}