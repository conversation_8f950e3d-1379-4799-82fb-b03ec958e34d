{"minified": "composer/2.0", "packages": {"zendframework/zend-hydrator": [{"name": "zendframework/zend-hydrator", "description": "Serialize objects to arrays, and vice versa", "keywords": ["ZendFramework", "zf", "hydrator"], "homepage": "", "version": "3.0.2", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [], "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "f9987bfa25a89d6191e1dbb0413b3c35613f0271"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/f9987bfa25a89d6191e1dbb0413b3c35613f0271", "type": "zip", "shasum": "", "reference": "f9987bfa25a89d6191e1dbb0413b3c35613f0271"}, "type": "library", "time": "2019-03-15T14:12:03+00:00", "autoload": {"psr-4": {"Zend\\Hydrator\\": "src/"}}, "extra": {"branch-alias": {"dev-release-2.4": "2.4.x-dev", "dev-master": "3.0.x-dev", "dev-develop": "3.1.x-dev"}, "zf": {"component": "Zend\\Hydrator", "config-provider": "Zend\\Hydrator\\ConfigProvider"}}, "require": {"php": "^7.2", "zendframework/zend-stdlib": "^3.2.1"}, "require-dev": {"phpspec/prophecy": "^1.7.5", "phpstan/phpstan": "^0.10.5", "phpunit/phpunit": "^7.5", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-eventmanager": "^3.2.1", "zendframework/zend-modulemanager": "^2.8", "zendframework/zend-serializer": "^2.9", "zendframework/zend-servicemanager": "^3.3.2"}, "suggest": {"zendframework/zend-eventmanager": "^3.2, to support aggregate hydrator usage", "zendframework/zend-serializer": "^2.9, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^3.3, to support hydrator plugin manager usage"}, "abandoned": "laminas/laminas-hydrator", "support": {"chat": "https://zendframework-slack.herokuapp.com", "docs": "https://docs.zendframework.com/zend-hydrator/", "forum": "https://discourse.zendframework.com/c/questions/components", "issues": "https://github.com/zendframework/zend-hydrator/issues", "rss": "https://github.com/zendframework/zend-hydrator/releases.atom", "source": "https://github.com/zendframework/zend-hydrator"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "bdc189f3bc635b497552d3bbce4d55e0d16db835"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/bdc189f3bc635b497552d3bbce4d55e0d16db835", "type": "zip", "shasum": "", "reference": "bdc189f3bc635b497552d3bbce4d55e0d16db835"}, "time": "2019-01-07T19:27:11+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "baa10aaafe92559d2579d3dc8417b7b690f260bc"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/baa10aaafe92559d2579d3dc8417b7b690f260bc", "type": "zip", "shasum": "", "reference": "baa10aaafe92559d2579d3dc8417b7b690f260bc"}, "time": "2018-12-10T17:48:39+00:00"}, {"version": "2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "2bfc6845019e7b6d38b0ab5e55190244dc510285"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/2bfc6845019e7b6d38b0ab5e55190244dc510285", "type": "zip", "shasum": "", "reference": "2bfc6845019e7b6d38b0ab5e55190244dc510285"}, "time": "2019-10-04T11:17:36+00:00", "extra": {"branch-alias": {"dev-release-2.4": "2.4.x-dev"}, "zf": {"component": "Zend\\Hydrator", "config-provider": "Zend\\Hydrator\\ConfigProvider"}}, "require": {"php": "^5.6 || ^7.0", "zendframework/zend-stdlib": "^3.0"}, "require-dev": {"phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-eventmanager": "^2.6.2 || ^3.0", "zendframework/zend-filter": "^2.6", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3"}, "suggest": {"zendframework/zend-eventmanager": "^2.6.2 || ^3.0, to support aggregate hydrator usage", "zendframework/zend-filter": "^2.6, to support naming strategy hydrator usage", "zendframework/zend-serializer": "^2.6.1, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3, to support hydrator plugin manager usage"}}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "70b02f4d8676e64af932625751750b5ca72fff3a"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/70b02f4d8676e64af932625751750b5ca72fff3a", "type": "zip", "shasum": "", "reference": "70b02f4d8676e64af932625751750b5ca72fff3a"}, "time": "2018-11-19T19:16:10+00:00", "extra": {"branch-alias": {"dev-release-1.0": "1.0.x-dev", "dev-release-1.1": "1.1.x-dev", "dev-master": "2.4.x-dev", "dev-develop": "2.5.x-dev"}, "zf": {"component": "Zend\\Hydrator", "config-provider": "Zend\\Hydrator\\ConfigProvider"}}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "bd48bc3bc046df007a94125f868dd1aa1b73a813"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/bd48bc3bc046df007a94125f868dd1aa1b73a813", "type": "zip", "shasum": "", "reference": "bd48bc3bc046df007a94125f868dd1aa1b73a813"}, "time": "2018-04-30T21:22:14+00:00"}, {"description": "", "keywords": ["zf2", "hydrator"], "homepage": "https://github.com/zendframework/zend-hydrator", "version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "de0d6465fbc4b7ca345fddc148834c321c4b361f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/de0d6465fbc4b7ca345fddc148834c321c4b361f", "type": "zip", "shasum": "", "reference": "de0d6465fbc4b7ca345fddc148834c321c4b361f"}, "time": "2017-10-02T15:01:27+00:00", "extra": {"branch-alias": {"dev-release-1.0": "1.0-dev", "dev-release-1.1": "1.1-dev", "dev-master": "2.3-dev", "dev-develop": "2.4-dev"}, "zf": {"component": "Zend\\Hydrator", "config-provider": "Zend\\Hydrator\\ConfigProvider"}}, "require-dev": {"zendframework/zend-eventmanager": "^3.0", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-filter": "^2.6", "phpunit/phpunit": "^5.7.21 || ^6.3", "zendframework/zend-coding-standard": "~1.0.0"}, "suggest": {"zendframework/zend-eventmanager": "^2.6.2 || ^3.0, to support aggregate hydrator usage", "zendframework/zend-serializer": "^2.6.1, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3, to support hydrator plugin manager usage", "zendframework/zend-filter": "^2.6, to support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/master"}}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "7aa33949227fcabb5634a61f97fe78cccbb25c98"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/7aa33949227fcabb5634a61f97fe78cccbb25c98", "type": "zip", "shasum": "", "reference": "7aa33949227fcabb5634a61f97fe78cccbb25c98"}, "time": "2017-09-20T22:02:25+00:00", "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/2.3.0"}}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "837e2d378b36241198c0f1ff018295af52e6c855"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/837e2d378b36241198c0f1ff018295af52e6c855", "type": "zip", "shasum": "", "reference": "837e2d378b36241198c0f1ff018295af52e6c855"}, "time": "2017-09-20T21:17:27+00:00", "extra": {"branch-alias": {"dev-release-1.0": "1.0-dev", "dev-release-1.1": "1.1-dev", "dev-master": "2.2-dev", "dev-develop": "2.3-dev"}, "zf": {"component": "Zend\\Hydrator", "config-provider": "Zend\\Hydrator\\ConfigProvider"}}, "require": {"php": "^7.0 || ^5.6", "zendframework/zend-stdlib": "^3.0"}, "require-dev": {"zendframework/zend-eventmanager": "^3.0", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-filter": "^2.6", "phpunit/phpunit": "^6.0.10 || ^5.7.17", "zendframework/zend-coding-standard": "~1.0.0"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/master"}}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "e3ecc4920c1724e1949213163fee82200e8e6f6c"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/e3ecc4920c1724e1949213163fee82200e8e6f6c", "type": "zip", "shasum": "", "reference": "e3ecc4920c1724e1949213163fee82200e8e6f6c"}, "time": "2017-05-17T18:40:45+00:00", "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/2.2.2"}}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "0ac0d3e569781f1895670b0c8d0dc7f25b8a3182"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/0ac0d3e569781f1895670b0c8d0dc7f25b8a3182", "type": "zip", "shasum": "", "reference": "0ac0d3e569781f1895670b0c8d0dc7f25b8a3182"}, "time": "2016-04-18T17:59:29+00:00", "require": {"php": "^5.5 || ^7.0", "zendframework/zend-stdlib": "^3.0"}, "require-dev": {"zendframework/zend-eventmanager": "^3.0", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-filter": "^2.6", "phpunit/phpunit": "^4.5", "squizlabs/php_codesniffer": "^2.3.1"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/2.2.1"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "e530a90cd30040190449ee1ad7a466688c2971c4"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/e530a90cd30040190449ee1ad7a466688c2971c4", "type": "zip", "shasum": "", "reference": "e530a90cd30040190449ee1ad7a466688c2971c4"}, "time": "2016-04-06T19:10:21+00:00", "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/2.2.0"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "2c8a6ec8320ea48a8a17a22a1404cece7aaf76e9"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/2c8a6ec8320ea48a8a17a22a1404cece7aaf76e9", "type": "zip", "shasum": "", "reference": "2c8a6ec8320ea48a8a17a22a1404cece7aaf76e9"}, "time": "2016-02-18T22:31:17+00:00", "extra": {"branch-alias": {"dev-release-1.0": "1.0-dev", "dev-release-1.1": "1.1-dev", "dev-master": "2.1-dev", "dev-develop": "2.2-dev"}}, "require-dev": {"zendframework/zend-eventmanager": "^3.0", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-filter": "^2.6", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.0@dev"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/2.1.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "2c8e029ff243e2cac390b06a90609dc2dfe97c76"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/2c8e029ff243e2cac390b06a90609dc2dfe97c76", "type": "zip", "shasum": "", "reference": "2c8e029ff243e2cac390b06a90609dc2dfe97c76"}, "time": "2015-09-17T14:15:23+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev", "dev-develop": "2.1-dev"}}, "require": {"php": ">=5.5", "zendframework/zend-stdlib": "^2.5.1"}, "require-dev": {"zendframework/zend-eventmanager": "^2.5.1", "zendframework/zend-inputfilter": "^2.5.1", "zendframework/zend-serializer": "^2.5.1", "zendframework/zend-servicemanager": "^2.5.1", "zendframework/zend-filter": "^2.5.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.0@dev"}, "suggest": {"zendframework/zend-eventmanager": "^2.5.1, to support aggregate hydrator usage", "zendframework/zend-serializer": "^2.5.1, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^2.5.1, to support hydrator plugin manager usage", "zendframework/zend-filter": "^2.5.1, to support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/master"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "22652e1661a5a10b3f564cf7824a2206cf5a4a65"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/22652e1661a5a10b3f564cf7824a2206cf5a4a65", "type": "zip", "shasum": "", "reference": "22652e1661a5a10b3f564cf7824a2206cf5a4a65"}, "time": "2016-02-18T22:38:26+00:00", "extra": {"branch-alias": {"dev-release-1.0": "1.0-dev", "dev-release-1.1": "1.1-dev", "dev-master": "2.0-dev", "dev-develop": "2.1-dev"}}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"zendframework/zend-eventmanager": "^2.6.2 || ^3.0", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-filter": "^2.6", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.0@dev"}, "suggest": {"zendframework/zend-eventmanager": "^2.6.2 || ^3.0, to support aggregate hydrator usage", "zendframework/zend-serializer": "^2.6.1, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3, to support hydrator plugin manager usage", "zendframework/zend-filter": "^2.6, to support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/release-1.1"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/zendframework/zend-hydrator.git", "type": "git", "reference": "f3ed8b833355140350bbed98d8a7b8b66875903f"}, "dist": {"url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/f3ed8b833355140350bbed98d8a7b8b66875903f", "type": "zip", "shasum": "", "reference": "f3ed8b833355140350bbed98d8a7b8b66875903f"}, "time": "2015-09-17T14:06:43+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev", "dev-develop": "1.1-dev"}}, "require": {"php": ">=5.5", "zendframework/zend-stdlib": "^2.5.1"}, "require-dev": {"zendframework/zend-eventmanager": "^2.5.1", "zendframework/zend-inputfilter": "^2.5.1", "zendframework/zend-serializer": "^2.5.1", "zendframework/zend-servicemanager": "^2.5.1", "zendframework/zend-filter": "^2.5.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.0@dev"}, "suggest": {"zendframework/zend-eventmanager": "^2.5.1, to support aggregate hydrator usage", "zendframework/zend-serializer": "^2.5.1, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^2.5.1, to support hydrator plugin manager usage", "zendframework/zend-filter": "^2.5.1, to support naming strategy hydrator usage"}, "support": {"issues": "https://github.com/zendframework/zend-hydrator/issues", "source": "https://github.com/zendframework/zend-hydrator/tree/master"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:21:36 GMT"}