{"minified": "composer/2.0", "packages": {"composer/semver": [{"name": "composer/semver", "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["validation", "semver", "versioning", "semantic"], "homepage": "", "version": "3.4.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "type": "zip", "shasum": "", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "type": "library", "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00", "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^7", "phpstan/phpstan": "^1.11"}}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/c51258e759afdb17f1fd1fe83bc12baaef6309d6", "type": "zip", "shasum": "", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6"}, "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.2"}, "time": "2024-07-12T11:35:52+00:00", "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^1.4"}}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "8536c1b9103405bcbd310c69e7a5739a1c2b1f0b"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/8536c1b9103405bcbd310c69e7a5739a1c2b1f0b", "type": "zip", "shasum": "", "reference": "8536c1b9103405bcbd310c69e7a5739a1c2b1f0b"}, "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.1"}, "time": "2024-07-12T09:13:09+00:00"}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "type": "zip", "shasum": "", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "time": "2023-08-31T09:50:34+00:00"}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/3953f23262f2bff1919fc82183ad9acb13ff62c9", "type": "zip", "shasum": "", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.2"}, "time": "2022-04-01T19:23:25+00:00"}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "5d8e574bb0e69188786b8ef77d43341222a41a71"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/5d8e574bb0e69188786b8ef77d43341222a41a71", "type": "zip", "shasum": "", "reference": "5d8e574bb0e69188786b8ef77d43341222a41a71"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.1"}, "time": "2022-03-16T11:22:07+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "f79c90ad4e9b41ac4dfc5d77bf398cf61fbd718b"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/f79c90ad4e9b41ac4dfc5d77bf398cf61fbd718b", "type": "zip", "shasum": "", "reference": "f79c90ad4e9b41ac4dfc5d77bf398cf61fbd718b"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.0"}, "time": "2022-03-15T08:35:57+00:00"}, {"version": "3.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "a951f614bd64dcd26137bc9b7b2637ddcfc57649"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/a951f614bd64dcd26137bc9b7b2637ddcfc57649", "type": "zip", "shasum": "", "reference": "a951f614bd64dcd26137bc9b7b2637ddcfc57649"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.9"}, "time": "2022-02-04T13:58:43+00:00"}, {"version": "3.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "3976a9e563da06e8dd8ca856fa2adcd66cdd98f3"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/3976a9e563da06e8dd8ca856fa2adcd66cdd98f3", "type": "zip", "shasum": "", "reference": "3976a9e563da06e8dd8ca856fa2adcd66cdd98f3"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.8"}, "time": "2022-02-04T12:12:24+00:00"}, {"version": "3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "deac27056b57e46faf136fae7b449eeaa71661ee"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/deac27056b57e46faf136fae7b449eeaa71661ee", "type": "zip", "shasum": "", "reference": "deac27056b57e46faf136fae7b449eeaa71661ee"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.7"}, "time": "2022-01-04T09:57:54+00:00", "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^0.12.54"}}, {"version": "3.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "83e511e247de329283478496f7a1e114c9517506"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/83e511e247de329283478496f7a1e114c9517506", "type": "zip", "shasum": "", "reference": "83e511e247de329283478496f7a1e114c9517506"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.6"}, "time": "2021-10-25T11:34:17+00:00"}, {"version": "3.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "31f3ea725711245195f62e54ffa402d8ef2fdba9"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/31f3ea725711245195f62e54ffa402d8ef2fdba9", "type": "zip", "shasum": "", "reference": "31f3ea725711245195f62e54ffa402d8ef2fdba9"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.5"}, "time": "2021-05-24T12:41:47+00:00"}, {"version": "3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "a02fdf930a3c1c3ed3a49b5f63859c0c20e10464"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/a02fdf930a3c1c3ed3a49b5f63859c0c20e10464", "type": "zip", "shasum": "", "reference": "a02fdf930a3c1c3ed3a49b5f63859c0c20e10464"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.4"}, "time": "2020-11-13T08:59:24+00:00"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "bcf1cc72150023e1c3dca4a353296b4c83deea41"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/bcf1cc72150023e1c3dca4a353296b4c83deea41", "type": "zip", "shasum": "", "reference": "bcf1cc72150023e1c3dca4a353296b4c83deea41"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.3"}, "time": "2020-11-12T11:25:27+00:00", "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^0.12.19"}}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "4089fddb67bcf6bf860d91b979e95be303835002"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/4089fddb67bcf6bf860d91b979e95be303835002", "type": "zip", "shasum": "", "reference": "4089fddb67bcf6bf860d91b979e95be303835002"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.2"}, "time": "2020-10-14T08:51:15+00:00"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "ebb714493b3a54f1dbbec6b15ab7bc9b3440e17b"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/ebb714493b3a54f1dbbec6b15ab7bc9b3440e17b", "type": "zip", "shasum": "", "reference": "ebb714493b3a54f1dbbec6b15ab7bc9b3440e17b"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.1"}, "time": "2020-09-27T13:14:03+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "da7ce661431b17a71271cdf7f5437dc722133123"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/da7ce661431b17a71271cdf7f5437dc722133123", "type": "zip", "shasum": "", "reference": "da7ce661431b17a71271cdf7f5437dc722133123"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.0"}, "time": "2020-09-09T09:39:19+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "c64053b1543d7bc054883f0a78a86c4242ae315d"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/c64053b1543d7bc054883f0a78a86c4242ae315d", "type": "zip", "shasum": "", "reference": "c64053b1543d7bc054883f0a78a86c4242ae315d"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/main"}, "time": "2020-09-08T20:59:47+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "c6e35eb8251125bfdd1c812f379da0dc89814df1"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/c6e35eb8251125bfdd1c812f379da0dc89814df1", "type": "zip", "shasum": "", "reference": "c6e35eb8251125bfdd1c812f379da0dc89814df1"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.0.1"}, "time": "2020-09-08T14:25:42+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "3426bd5efa8a12d230824536c42a8a4ad30b7940"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/3426bd5efa8a12d230824536c42a8a4ad30b7940", "type": "zip", "shasum": "", "reference": "3426bd5efa8a12d230824536c42a8a4ad30b7940"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.0.0"}, "time": "2020-05-26T18:22:04+00:00", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "0ec124f57c7e23925c006cbad0de853e3aec3ba2"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/0ec124f57c7e23925c006cbad0de853e3aec3ba2", "type": "zip", "shasum": "", "reference": "0ec124f57c7e23925c006cbad0de853e3aec3ba2"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/2.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-04-21T13:19:12+00:00", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require": {"php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5 || ^7", "phpstan/phpstan": "^0.12.19"}}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/647490bbcaf7fc4891c58f47b825eb99d19c377a", "type": "zip", "shasum": "", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.7.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-12-03T15:47:16+00:00", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "38276325bd896f90dfcfe30029aa5db40df387a7"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/38276325bd896f90dfcfe30029aa5db40df387a7", "type": "zip", "shasum": "", "reference": "38276325bd896f90dfcfe30029aa5db40df387a7"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.7.1"}, "time": "2020-09-27T13:13:07+00:00", "require": {"php": "^5.3.2 || ^7.0"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "114f819054a2ea7db03287f5efb757e2af6e4079"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/114f819054a2ea7db03287f5efb757e2af6e4079", "type": "zip", "shasum": "", "reference": "114f819054a2ea7db03287f5efb757e2af6e4079"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.7.0"}, "time": "2020-09-09T09:34:06+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "9787c20e39dfeea673665abee0679c73ba67105d"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/9787c20e39dfeea673665abee0679c73ba67105d", "type": "zip", "shasum": "", "reference": "9787c20e39dfeea673665abee0679c73ba67105d"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.6.0"}, "time": "2020-09-08T20:42:08+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "610045d2a33b8e368abebc789f6f4180b4f88ab6"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/610045d2a33b8e368abebc789f6f4180b4f88ab6", "type": "zip", "shasum": "", "reference": "610045d2a33b8e368abebc789f6f4180b4f88ab6"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.5.2"}, "time": "2020-09-08T14:18:25+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/c6bea70230ef4dd483e6bbcab6005f682ed3a8de", "type": "zip", "shasum": "", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.5.1"}, "time": "2020-01-13T12:06:48+00:00", "funding": "__unset"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "46d9139568ccb8d9e7cdd4539cab7347568a5e2e"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/46d9139568ccb8d9e7cdd4539cab7347568a5e2e", "type": "zip", "shasum": "", "reference": "46d9139568ccb8d9e7cdd4539cab7347568a5e2e"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.5.0"}, "time": "2019-03-19T17:25:45+00:00", "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5", "phpunit/phpunit-mock-objects": "2.3.0 || ^3.0"}}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "c7cb9a2095a074d131b65a8a0cd294479d785573"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/c7cb9a2095a074d131b65a8a0cd294479d785573", "type": "zip", "shasum": "", "reference": "c7cb9a2095a074d131b65a8a0cd294479d785573"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.4.2"}, "time": "2016-08-30T16:08:34+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "03c9de5aa25e7672c4ad251eeaba0c47a06c8b98"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/03c9de5aa25e7672c4ad251eeaba0c47a06c8b98", "type": "zip", "shasum": "", "reference": "03c9de5aa25e7672c4ad251eeaba0c47a06c8b98"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.4.1"}, "time": "2016-06-02T09:04:51+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "84c47f3d8901440403217afc120683c7385aecb8"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/84c47f3d8901440403217afc120683c7385aecb8", "type": "zip", "shasum": "", "reference": "84c47f3d8901440403217afc120683c7385aecb8"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/master"}, "time": "2016-03-30T13:16:03+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "df4463baa9f44fe6cf0a6da4fde2934d4c0a2747"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/df4463baa9f44fe6cf0a6da4fde2934d4c0a2747", "type": "zip", "shasum": "", "reference": "df4463baa9f44fe6cf0a6da4fde2934d4c0a2747"}, "time": "2016-02-25T22:23:39+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "0faeb6e433f6b352f0dc55ec1faf5c6b605a35d3"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/0faeb6e433f6b352f0dc55ec1faf5c6b605a35d3", "type": "zip", "shasum": "", "reference": "0faeb6e433f6b352f0dc55ec1faf5c6b605a35d3"}, "time": "2015-11-10T11:17:42+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "7486d83e924729a2cb7d90a7337da1b291adb706"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/7486d83e924729a2cb7d90a7337da1b291adb706", "type": "zip", "shasum": "", "reference": "7486d83e924729a2cb7d90a7337da1b291adb706"}, "time": "2015-11-03T20:15:49+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "d0e1ccc6d44ab318b758d709e19176037da6b1ba"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/d0e1ccc6d44ab318b758d709e19176037da6b1ba", "type": "zip", "shasum": "", "reference": "d0e1ccc6d44ab318b758d709e19176037da6b1ba"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.0.0"}, "time": "2015-09-21T09:42:36+00:00", "extra": {"branch-alias": {"dev-master": "0.1-dev"}}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "~2.3"}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/composer/semver.git", "type": "git", "reference": "f6469477aa1952f2fcbd0ab52fcc7662f1dd0173"}, "dist": {"url": "https://api.github.com/repos/composer/semver/zipball/f6469477aa1952f2fcbd0ab52fcc7662f1dd0173", "type": "zip", "shasum": "", "reference": "f6469477aa1952f2fcbd0ab52fcc7662f1dd0173"}, "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/0.1.0"}, "time": "2015-07-23T06:21:50+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 19 Sep 2024 14:15:36 GMT"}