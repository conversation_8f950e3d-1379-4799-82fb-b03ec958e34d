{"minified": "composer/2.0", "packages": {"spatie/color": [{"name": "spatie/color", "description": "A little library to handle color conversions", "keywords": ["conversion", "color", "rgb", "spatie"], "homepage": "https://github.com/spatie/color", "version": "1.8.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "142af7fec069a420babea80a5412eb2f646dcd8c"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/142af7fec069a420babea80a5412eb2f646dcd8c", "type": "zip", "shasum": "", "reference": "142af7fec069a420babea80a5412eb2f646dcd8c"}, "type": "library", "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.8.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2025-02-10T09:22:41+00:00", "autoload": {"psr-4": {"Spatie\\Color\\": "src"}}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"pestphp/pest": "^1.22", "phpunit/phpunit": "^6.5||^9.0"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "614f1e0674262c620db908998a11eacd16494835"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/614f1e0674262c620db908998a11eacd16494835", "type": "zip", "shasum": "", "reference": "614f1e0674262c620db908998a11eacd16494835"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.7.0"}, "time": "2024-12-30T14:23:15+00:00"}, {"version": "1.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "45c4354ffa7e65f05c502b009834ecac7928daa3"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/45c4354ffa7e65f05c502b009834ecac7928daa3", "type": "zip", "shasum": "", "reference": "45c4354ffa7e65f05c502b009834ecac7928daa3"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.6.3"}, "time": "2024-12-23T11:00:34+00:00"}, {"version": "1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "b4fac074a9e5999dcca12cbfab0f7c73e2684d6d"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/b4fac074a9e5999dcca12cbfab0f7c73e2684d6d", "type": "zip", "shasum": "", "reference": "b4fac074a9e5999dcca12cbfab0f7c73e2684d6d"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.6.2"}, "time": "2024-12-09T16:20:38+00:00"}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "4c540ffbef68a3df3d209718ae06deaab081e708"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/4c540ffbef68a3df3d209718ae06deaab081e708", "type": "zip", "shasum": "", "reference": "4c540ffbef68a3df3d209718ae06deaab081e708"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.6.1"}, "time": "2024-11-18T15:00:47+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "02ce48c480f86d65702188f738f4e8ccad1b999a"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/02ce48c480f86d65702188f738f4e8ccad1b999a", "type": "zip", "shasum": "", "reference": "02ce48c480f86d65702188f738f4e8ccad1b999a"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.6.0"}, "time": "2024-09-20T14:00:15+00:00"}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "49739265900cabce4640cd26c3266fd8d2cca390"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/49739265900cabce4640cd26c3266fd8d2cca390", "type": "zip", "shasum": "", "reference": "49739265900cabce4640cd26c3266fd8d2cca390"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.5.3"}, "time": "2022-12-18T12:58:32+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "d6e9b2766d8e24aab835e414248728762bd63530"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/d6e9b2766d8e24aab835e414248728762bd63530", "type": "zip", "shasum": "", "reference": "d6e9b2766d8e24aab835e414248728762bd63530"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.5.2"}, "time": "2022-06-24T21:50:06+00:00", "require-dev": {"phpunit/phpunit": "^6.5||^9.0"}}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "351f82e8a6354a7858a86391c181ae015dbd7048"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/351f82e8a6354a7858a86391c181ae015dbd7048", "type": "zip", "shasum": "", "reference": "351f82e8a6354a7858a86391c181ae015dbd7048"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.5.1"}, "time": "2022-04-12T14:45:41+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "c0615ecb8cb3b6d769eb1d479d999aaaebb65e55"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/c0615ecb8cb3b6d769eb1d479d999aaaebb65e55", "type": "zip", "shasum": "", "reference": "c0615ecb8cb3b6d769eb1d479d999aaaebb65e55"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.5.0"}, "time": "2022-01-11T18:45:48+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "fafbf8900541230e10abb2b165990e4758c28024"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/fafbf8900541230e10abb2b165990e4758c28024", "type": "zip", "shasum": "", "reference": "fafbf8900541230e10abb2b165990e4758c28024"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.3.0"}, "time": "2021-09-06T08:57:14+00:00"}, {"version": "1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "a82a3080bd6ddc4914a6960d0a590c1c8b601e81"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/a82a3080bd6ddc4914a6960d0a590c1c8b601e81", "type": "zip", "shasum": "", "reference": "a82a3080bd6ddc4914a6960d0a590c1c8b601e81"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.2.4"}, "time": "2021-02-18T09:37:02+00:00"}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "b375a12d2035ad80989e1a322b1e30132dfc1707"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/b375a12d2035ad80989e1a322b1e30132dfc1707", "type": "zip", "shasum": "", "reference": "b375a12d2035ad80989e1a322b1e30132dfc1707"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.2.2"}, "time": "2020-11-18T08:22:19+00:00", "require": {"php": "^7.3"}, "require-dev": {"phpunit/phpunit": "^6.5"}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "6ee5f04b5dc976a68a80d3be93ff79e5b1157203"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/6ee5f04b5dc976a68a80d3be93ff79e5b1157203", "type": "zip", "shasum": "", "reference": "6ee5f04b5dc976a68a80d3be93ff79e5b1157203"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/1.2.1"}, "time": "2020-07-17T07:55:14+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "d413da3815d8d818273254d4b471259f27de27f1"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/d413da3815d8d818273254d4b471259f27de27f1", "type": "zip", "shasum": "", "reference": "d413da3815d8d818273254d4b471259f27de27f1"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/master"}, "time": "2020-06-22T08:41:46+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "5c424dbfa919cc7acdacbf6fdcedb89f790bc04b"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/5c424dbfa919cc7acdacbf6fdcedb89f790bc04b", "type": "zip", "shasum": "", "reference": "5c424dbfa919cc7acdacbf6fdcedb89f790bc04b"}, "time": "2017-02-03T10:05:49+00:00", "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "5.*"}, "funding": "__unset"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "91a1d69388c23f506054a8852b0a784e48b74d79"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/91a1d69388c23f506054a8852b0a784e48b74d79", "type": "zip", "shasum": "", "reference": "91a1d69388c23f506054a8852b0a784e48b74d79"}, "time": "2017-01-26T15:07:46+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "caf09faf0c3d9d388260907be0423863a408467e"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/caf09faf0c3d9d388260907be0423863a408467e", "type": "zip", "shasum": "", "reference": "caf09faf0c3d9d388260907be0423863a408467e"}, "time": "2016-10-17T08:24:30+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "933ff429d50745f9f5622cc54c20dd74feb3dad8"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/933ff429d50745f9f5622cc54c20dd74feb3dad8", "type": "zip", "shasum": "", "reference": "933ff429d50745f9f5622cc54c20dd74feb3dad8"}, "time": "2016-09-22T08:27:02+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "001be37ef4e2c33e63bb327636c789f8c01a6bc8"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/001be37ef4e2c33e63bb327636c789f8c01a6bc8", "type": "zip", "shasum": "", "reference": "001be37ef4e2c33e63bb327636c789f8c01a6bc8"}, "time": "2016-09-20T13:42:47+00:00"}, {"description": "A little library to deal with color conversions", "version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/spatie/color.git", "type": "git", "reference": "fa9cd15955fbe9da54399ff3d8d8535f523af21e"}, "dist": {"url": "https://api.github.com/repos/spatie/color/zipball/fa9cd15955fbe9da54399ff3d8d8535f523af21e", "type": "zip", "shasum": "", "reference": "fa9cd15955fbe9da54399ff3d8d8535f523af21e"}, "support": {"issues": "https://github.com/spatie/color/issues", "source": "https://github.com/spatie/color/tree/0.1.0"}, "time": "2016-09-20T13:38:21+00:00"}]}, "security-advisories": [], "last-modified": "Mon, 10 Feb 2025 09:23:02 GMT"}