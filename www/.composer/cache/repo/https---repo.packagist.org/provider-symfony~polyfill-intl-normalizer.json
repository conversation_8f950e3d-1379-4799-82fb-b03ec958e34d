{"minified": "composer/2.0", "packages": {"symfony/polyfill-intl-normalizer": [{"name": "symfony/polyfill-intl-normalizer", "description": "Symfony polyfill for intl's Normalizer class and related functions", "keywords": ["intl", "compatibility", "normalizer", "portable", "polyfill", "shim"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "type": "zip", "shasum": "", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}}, {"version": "v1.31.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "type": "zip", "shasum": "", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "type": "zip", "shasum": "", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "type": "zip", "shasum": "", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.28.0"}, "time": "2023-01-26T09:26:14+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/19bd1e4fcd5b91116f14d8533c57831ed00571b6", "type": "zip", "shasum": "", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/219aa369ceff116e673852dce47c3a41794c14bd", "type": "zip", "shasum": "", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8590a5f561694770bdcd3f9b5c69dde6945028e8", "type": "zip", "shasum": "", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.25.0"}, "time": "2021-02-19T12:13:01+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.24.0"}}, {"version": "v1.23.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.23.0"}}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/43a0283138253ed1d48d352ab6d0bdb3f809f248", "type": "zip", "shasum": "", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.22.1"}, "time": "2021-01-22T09:19:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "6e971c891537eb617a00bb07a43d182a6915faba"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/6e971c891537eb617a00bb07a43d182a6915faba", "type": "zip", "shasum": "", "reference": "6e971c891537eb617a00bb07a43d182a6915faba"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.22.0"}, "time": "2021-01-07T17:09:11+00:00"}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "727d1096295d807c309fb01a851577302394c897"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/727d1096295d807c309fb01a851577302394c897", "type": "zip", "shasum": "", "reference": "727d1096295d807c309fb01a851577302394c897"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "8db0ae7936b42feb370840cf24de1a144fb0ef27"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8db0ae7936b42feb370840cf24de1a144fb0ef27", "type": "zip", "shasum": "", "reference": "8db0ae7936b42feb370840cf24de1a144fb0ef27"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.19.0"}, "time": "2020-10-23T09:01:57+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e", "type": "zip", "shasum": "", "reference": "37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.18.1"}, "time": "2020-07-14T12:35:20+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/master"}}, {"version": "v1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "40309d1700e8f72447bb9e7b54af756eeea35620"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/40309d1700e8f72447bb9e7b54af756eeea35620", "type": "zip", "shasum": "", "reference": "40309d1700e8f72447bb9e7b54af756eeea35620"}, "time": "2020-06-14T14:40:37+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "1357b1d168eb7f68ad6a134838e46b0b159444a9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/1357b1d168eb7f68ad6a134838e46b0b159444a9", "type": "zip", "shasum": "", "reference": "1357b1d168eb7f68ad6a134838e46b0b159444a9"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.17.0"}, "time": "2020-05-12T16:14:59+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "cc65fba80f49c547e16f1afcc6fd26a1b101556e"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/cc65fba80f49c547e16f1afcc6fd26a1b101556e", "type": "zip", "shasum": "", "reference": "cc65fba80f49c547e16f1afcc6fd26a1b101556e"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/master"}, "time": "2020-05-08T16:50:20+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "e62715f03f90dd8d2f3eb5daa21b4d19d71aebde"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/e62715f03f90dd8d2f3eb5daa21b4d19d71aebde", "type": "zip", "shasum": "", "reference": "e62715f03f90dd8d2f3eb5daa21b4d19d71aebde"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.15.0"}, "time": "2020-02-27T09:26:54+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "e62b4845992282d14037950542fc8e8650ae2a65"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/e62b4845992282d14037950542fc8e8650ae2a65", "type": "zip", "shasum": "", "reference": "e62b4845992282d14037950542fc8e8650ae2a65"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.14.0"}, "time": "2020-01-13T11:15:53+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}, {"version": "v1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "c49f5adbcd70338f15cd513a9c349b41ee1d0ee4"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/c49f5adbcd70338f15cd513a9c349b41ee1d0ee4", "type": "zip", "shasum": "", "reference": "c49f5adbcd70338f15cd513a9c349b41ee1d0ee4"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.13.2"}, "time": "2019-12-03T12:24:37+00:00", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}}, {"version": "v1.13.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "cfe6ad557c15f3797f667e9518ce759aa04ae4f3"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/cfe6ad557c15f3797f667e9518ce759aa04ae4f3", "type": "zip", "shasum": "", "reference": "cfe6ad557c15f3797f667e9518ce759aa04ae4f3"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.13.1"}, "time": "2019-11-27T13:56:44+00:00"}, {"version": "v1.13.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/master"}}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "c7fcec8e5cd3fc87f91120b0b5d3cb1b7d44f7a9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/c7fcec8e5cd3fc87f91120b0b5d3cb1b7d44f7a9", "type": "zip", "shasum": "", "reference": "c7fcec8e5cd3fc87f91120b0b5d3cb1b7d44f7a9"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.12.0"}, "time": "2019-08-06T08:03:45+00:00", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}}, {"version": "v1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "d570b8ea2cb7f33f217832339a181a206cf54a04"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/d570b8ea2cb7f33f217832339a181a206cf54a04", "type": "zip", "shasum": "", "reference": "d570b8ea2cb7f33f217832339a181a206cf54a04"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/master"}, "time": "2019-01-07T19:39:47+00:00", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "f8ed52909fc049b42a772c64ec1e6b31792abad6"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/f8ed52909fc049b42a772c64ec1e6b31792abad6", "type": "zip", "shasum": "", "reference": "f8ed52909fc049b42a772c64ec1e6b31792abad6"}, "time": "2018-09-21T06:26:08+00:00"}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "341271b1ef00a7a350cdf4fae801c796c0cfa0d3"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/341271b1ef00a7a350cdf4fae801c796c0cfa0d3", "type": "zip", "shasum": "", "reference": "341271b1ef00a7a350cdf4fae801c796c0cfa0d3"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.9.0"}, "time": "2018-08-06T14:22:27+00:00"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "00fad3a3e2db93a2b788d83dfdd432a3c7e8fb4f"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/00fad3a3e2db93a2b788d83dfdd432a3c7e8fb4f", "type": "zip", "shasum": "", "reference": "00fad3a3e2db93a2b788d83dfdd432a3c7e8fb4f"}, "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/master"}, "time": "2018-04-26T10:06:28+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "4e5fca97448b2e5ac785e1c55b2f6a152f61a4da"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/4e5fca97448b2e5ac785e1c55b2f6a152f61a4da", "type": "zip", "shasum": "", "reference": "4e5fca97448b2e5ac785e1c55b2f6a152f61a4da"}, "time": "2018-01-30T19:27:44+00:00", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "9969b51578b6dbe4bf8aeef7f3cbefadef77752f"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/9969b51578b6dbe4bf8aeef7f3cbefadef77752f", "type": "zip", "shasum": "", "reference": "9969b51578b6dbe4bf8aeef7f3cbefadef77752f"}, "time": "2017-10-11T12:05:26+00:00", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "d3bc26b73d64387ce009b04638de30bcb38393a8"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/d3bc26b73d64387ce009b04638de30bcb38393a8", "type": "zip", "shasum": "", "reference": "d3bc26b73d64387ce009b04638de30bcb38393a8"}, "time": "2017-06-14T15:44:48+00:00", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "58fb9117dff26b961fd775acfa7ece4ab9eaabdf"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/58fb9117dff26b961fd775acfa7ece4ab9eaabdf", "type": "zip", "shasum": "", "reference": "58fb9117dff26b961fd775acfa7ece4ab9eaabdf"}, "time": "2017-06-09T08:25:21+00:00", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "92df6b67f5cc2356f16c754784c1f22dbda178d7"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/92df6b67f5cc2356f16c754784c1f22dbda178d7", "type": "zip", "shasum": "", "reference": "92df6b67f5cc2356f16c754784c1f22dbda178d7"}, "time": "2016-11-14T01:06:16+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}}, {"version": "v1.3.0", "version_normalized": "*******"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "5a4d25700c0c01abc799532156dd68f3d8798b64"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/5a4d25700c0c01abc799532156dd68f3d8798b64", "type": "zip", "shasum": "", "reference": "5a4d25700c0c01abc799532156dd68f3d8798b64"}, "time": "2016-05-18T14:26:46+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "8444df29707a1c3572ae43c9d90325d23bc16ddc"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8444df29707a1c3572ae43c9d90325d23bc16ddc", "type": "zip", "shasum": "", "reference": "8444df29707a1c3572ae43c9d90325d23bc16ddc"}, "time": "2016-02-15T04:08:08+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "0f9a67d9bd1bf5c73730a9d1b940e5511943f49a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/0f9a67d9bd1bf5c73730a9d1b940e5511943f49a", "type": "zip", "shasum": "", "reference": "0f9a67d9bd1bf5c73730a9d1b940e5511943f49a"}, "time": "2016-01-20T09:13:37+00:00", "suggest": "__unset"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-normalizer.git", "type": "git", "reference": "b0235b9e98e224821e23018a9487764ad6dec859"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/b0235b9e98e224821e23018a9487764ad6dec859", "type": "zip", "shasum": "", "reference": "b0235b9e98e224821e23018a9487764ad6dec859"}, "time": "2015-11-04T20:28:58+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "v1.0.0", "version_normalized": "*******"}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 09:41:34 GMT"}