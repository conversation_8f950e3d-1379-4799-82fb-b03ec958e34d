{"minified": "composer/2.0", "packages": {"doctrine/cache": [{"name": "doctrine/cache", "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "keywords": ["redis", "couchdb", "php", "abstraction", "cache", "caching", "xcache", "memcached", "apcu"], "homepage": "https://www.doctrine-project.org/projects/cache.html", "version": "2.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "type": "zip", "shasum": "", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "type": "library", "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "require": {"php": "~7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "doctrine/coding-standard": "^9", "psr/cache": "^1.0 || ^2.0 || ^3.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "conflict": {"doctrine/common": ">2.2,<2.4"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/331b4d5dbaeab3827976273e9356b3b453c300ce", "type": "zip", "shasum": "", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.1.1"}, "time": "2021-07-17T14:49:29+00:00", "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "doctrine/coding-standard": "^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "ac77408b22cc6c4d0b4947d20a3889be3043566e"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/ac77408b22cc6c4d0b4947d20a3889be3043566e", "type": "zip", "shasum": "", "reference": "ac77408b22cc6c4d0b4947d20a3889be3043566e"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.1.0"}, "time": "2021-07-14T11:22:57+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "c9622c6820d3ede1e2315a6a377ea1076e421d88"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/c9622c6820d3ede1e2315a6a377ea1076e421d88", "type": "zip", "shasum": "", "reference": "c9622c6820d3ede1e2315a6a377ea1076e421d88"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.0.3"}, "time": "2021-05-25T09:43:04+00:00", "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "doctrine/coding-standard": "^8.0", "psr/cache": "^1.0 || ^2.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.2"}, "conflict": {"doctrine/common": ">2.2,<2.4", "psr/cache": ">=3"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "14e0af5ba34a53c96c042c21aac1b0582d8f216b"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/14e0af5ba34a53c96c042c21aac1b0582d8f216b", "type": "zip", "shasum": "", "reference": "14e0af5ba34a53c96c042c21aac1b0582d8f216b"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.0.2"}, "time": "2021-05-20T15:07:13+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "0fc7b7b12992e3be1d7fcdcd281e53b671306f93"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/0fc7b7b12992e3be1d7fcdcd281e53b671306f93", "type": "zip", "shasum": "", "reference": "0fc7b7b12992e3be1d7fcdcd281e53b671306f93"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.0.1"}, "time": "2021-05-19T06:49:24+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "64d8b1dedd6ae7bb6b4c3094a44c585ec04e86b2"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/64d8b1dedd6ae7bb6b4c3094a44c585ec04e86b2", "type": "zip", "shasum": "", "reference": "64d8b1dedd6ae7bb6b4c3094a44c585ec04e86b2"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.0.0"}, "time": "2021-04-28T18:03:31+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "type": "zip", "shasum": "", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "time": "2022-05-20T20:06:54+00:00", "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "doctrine/coding-standard": "^9", "psr/cache": "^1.0 || ^2.0 || ^3.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "conflict": {"doctrine/common": ">2.2,<2.4"}}, {"version": "1.12.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/4cf401d14df219fa6f38b671f5493449151c9ad8", "type": "zip", "shasum": "", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.12.1"}, "time": "2021-07-17T14:39:21+00:00", "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "doctrine/coding-standard": "^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "92032beb419568d3b61ae645f48bbb693cc7e00e"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/92032beb419568d3b61ae645f48bbb693cc7e00e", "type": "zip", "shasum": "", "reference": "92032beb419568d3b61ae645f48bbb693cc7e00e"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.12.0"}, "time": "2021-07-14T11:09:49+00:00"}, {"version": "1.11.3", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "3bb5588cec00a0268829cc4a518490df6741af9d"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/3bb5588cec00a0268829cc4a518490df6741af9d", "type": "zip", "shasum": "", "reference": "3bb5588cec00a0268829cc4a518490df6741af9d"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.11.3"}, "time": "2021-05-25T09:01:55+00:00", "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "doctrine/coding-standard": "^8.0", "psr/cache": "^1.0 || ^2.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.2"}, "conflict": {"doctrine/common": ">2.2,<2.4", "psr/cache": ">=3"}}, {"version": "1.11.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "9c53086695937c50c47936ed86d96150ffbcf60d"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/9c53086695937c50c47936ed86d96150ffbcf60d", "type": "zip", "shasum": "", "reference": "9c53086695937c50c47936ed86d96150ffbcf60d"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.11.2"}, "time": "2021-05-20T14:57:29+00:00"}, {"version": "1.11.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "163074496dc7c3c7b8ccbf3d4376c0187424ed81"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/163074496dc7c3c7b8ccbf3d4376c0187424ed81", "type": "zip", "shasum": "", "reference": "163074496dc7c3c7b8ccbf3d4376c0187424ed81"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.11.1"}, "time": "2021-05-18T16:45:32+00:00"}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "a9c1b59eba5a08ca2770a76eddb88922f504e8e0"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/a9c1b59eba5a08ca2770a76eddb88922f504e8e0", "type": "zip", "shasum": "", "reference": "a9c1b59eba5a08ca2770a76eddb88922f504e8e0"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.11.0"}, "time": "2021-04-13T14:46:17+00:00"}, {"version": "1.10.2", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "13e3381b25847283a91948d04640543941309727"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/13e3381b25847283a91948d04640543941309727", "type": "zip", "shasum": "", "reference": "13e3381b25847283a91948d04640543941309727"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.10.x"}, "time": "2020-07-07T18:54:01+00:00", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0", "doctrine/coding-standard": "^6.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}}, {"version": "1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "35a4a70cd94e09e2259dfae7488afc6b474ecbd3"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/35a4a70cd94e09e2259dfae7488afc6b474ecbd3", "type": "zip", "shasum": "", "reference": "35a4a70cd94e09e2259dfae7488afc6b474ecbd3"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.10.1"}, "time": "2020-05-27T16:24:54+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/382e7f4db9a12dc6c19431743a2b096041bcdd62", "type": "zip", "shasum": "", "reference": "382e7f4db9a12dc6c19431743a2b096041bcdd62"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.10.0"}, "time": "2019-11-29T15:36:20+00:00", "require": {"php": "~7.1"}, "funding": "__unset"}, {"keywords": ["redis", "couchdb", "php", "abstraction", "cache", "caching", "xcache", "memcached", "riak", "apcu"], "version": "1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "89a5c76c39c292f7798f964ab3c836c3f8192a55"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/89a5c76c39c292f7798f964ab3c836c3f8192a55", "type": "zip", "shasum": "", "reference": "89a5c76c39c292f7798f964ab3c836c3f8192a55"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.9.1"}, "time": "2019-11-15T14:31:57+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "c15dcd24b756f9e52ea7c3ae8227354f3628f11a"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/c15dcd24b756f9e52ea7c3ae8227354f3628f11a", "type": "zip", "shasum": "", "reference": "c15dcd24b756f9e52ea7c3ae8227354f3628f11a"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/master"}, "time": "2019-11-11T10:31:52+00:00"}, {"description": "Caching library offering an object-oriented API for many cache backends", "keywords": ["cache", "caching"], "homepage": "https://www.doctrine-project.org", "version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "cf73f063172f4ec18b6cbb293e30363e11e8d30a"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/cf73f063172f4ec18b6cbb293e30363e11e8d30a", "type": "zip", "shasum": "", "reference": "cf73f063172f4ec18b6cbb293e30363e11e8d30a"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.8.2"}, "time": "2019-11-11T01:40:42+00:00", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0", "doctrine/coding-standard": "^4.0"}}, {"version": "v1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "d4374ae95b36062d02ef310100ed33d78738d76c"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/d4374ae95b36062d02ef310100ed33d78738d76c", "type": "zip", "shasum": "", "reference": "d4374ae95b36062d02ef310100ed33d78738d76c"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/v1.8.1"}, "time": "2019-10-28T09:31:32+00:00"}, {"version": "v1.8.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "d768d58baee9a4862ca783840eca1b9add7a7f57"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/d768d58baee9a4862ca783840eca1b9add7a7f57", "type": "zip", "shasum": "", "reference": "d768d58baee9a4862ca783840eca1b9add7a7f57"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/v1.8.0"}, "time": "2018-08-21T18:01:43+00:00"}, {"homepage": "http://www.doctrine-project.org", "version": "v1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "b3217d58609e9c8e661cd41357a54d926c4a2a1a"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/b3217d58609e9c8e661cd41357a54d926c4a2a1a", "type": "zip", "shasum": "", "reference": "b3217d58609e9c8e661cd41357a54d926c4a2a1a"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/v1.7.1"}, "time": "2017-08-25T07:02:50+00:00", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0"}}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "53d9518ffeb019c51d542ff60cb578f076d3ff16"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/53d9518ffeb019c51d542ff60cb578f076d3ff16", "type": "zip", "shasum": "", "reference": "53d9518ffeb019c51d542ff60cb578f076d3ff16"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/master"}, "time": "2017-07-22T13:00:15+00:00"}, {"version": "v1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "eb152c5100571c7a45470ff2a35095ab3f3b900b"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/eb152c5100571c7a45470ff2a35095ab3f3b900b", "type": "zip", "shasum": "", "reference": "eb152c5100571c7a45470ff2a35095ab3f3b900b"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.6.x"}, "time": "2017-07-22T12:49:21+00:00", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "require": {"php": "~5.5|~7.0"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.0", "satooshi/php-coveralls": "~0.6", "predis/predis": "~1.0"}, "suggest": "__unset"}, {"version": "v1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "b6f544a20f4807e81f7044d31e679ccbb1866dc3"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/b6f544a20f4807e81f7044d31e679ccbb1866dc3", "type": "zip", "shasum": "", "reference": "b6f544a20f4807e81f7044d31e679ccbb1866dc3"}, "time": "2016-10-29T11:16:17+00:00"}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "f8af318d14bdb0eff0336795b428b547bd39ccb6"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/f8af318d14bdb0eff0336795b428b547bd39ccb6", "type": "zip", "shasum": "", "reference": "f8af318d14bdb0eff0336795b428b547bd39ccb6"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/v1.6.0"}, "time": "2015-12-31T16:37:02+00:00"}, {"version": "v1.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "47cdc76ceb95cc591d9c79a36dc3794975b5d136"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/47cdc76ceb95cc591d9c79a36dc3794975b5d136", "type": "zip", "shasum": "", "reference": "47cdc76ceb95cc591d9c79a36dc3794975b5d136"}, "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.5.x"}, "time": "2015-12-19T05:03:47+00:00", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": ">=3.7", "satooshi/php-coveralls": "~0.6", "predis/predis": "~1.0"}}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "6ecaf07f0dfcc3dd454d0c549c2d16f29a19bffc"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/6ecaf07f0dfcc3dd454d0c549c2d16f29a19bffc", "type": "zip", "shasum": "", "reference": "6ecaf07f0dfcc3dd454d0c549c2d16f29a19bffc"}, "time": "2015-12-19T01:09:56+00:00"}, {"version": "v1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "47c7128262da274f590ae6f86eb137a7a64e82af"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/47c7128262da274f590ae6f86eb137a7a64e82af", "type": "zip", "shasum": "", "reference": "47c7128262da274f590ae6f86eb137a7a64e82af"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.5.2"}, "time": "2015-12-03T10:50:37+00:00"}, {"version": "v1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "2b9cec5a5e722010cbebc91713d4c11eaa064d5e"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/2b9cec5a5e722010cbebc91713d4c11eaa064d5e", "type": "zip", "shasum": "", "reference": "2b9cec5a5e722010cbebc91713d4c11eaa064d5e"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.5.1"}, "time": "2015-11-02T18:35:48+00:00"}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "eb8a73619af4f1c8711e2ce482f5de3643258a1f"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/eb8a73619af4f1c8711e2ce482f5de3643258a1f", "type": "zip", "shasum": "", "reference": "eb8a73619af4f1c8711e2ce482f5de3643258a1f"}, "support": {"source": "https://github.com/doctrine/cache/tree/master"}, "time": "2015-10-28T11:27:45+00:00"}, {"version": "v1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "6433826dd02c9e5be8a127320dc13e7e6625d020"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/6433826dd02c9e5be8a127320dc13e7e6625d020", "type": "zip", "shasum": "", "reference": "6433826dd02c9e5be8a127320dc13e7e6625d020"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.4.4"}, "time": "2015-11-02T18:33:51+00:00", "autoload": {"psr-0": {"Doctrine\\Common\\Cache\\": "lib/"}}}, {"version": "v1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "cdeb14a6feb34821a6f5fe05435ef04a68a7f147"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/cdeb14a6feb34821a6f5fe05435ef04a68a7f147", "type": "zip", "shasum": "", "reference": "cdeb14a6feb34821a6f5fe05435ef04a68a7f147"}, "support": {"source": "https://github.com/doctrine/cache/tree/1.4.x"}, "time": "2015-10-28T10:28:03+00:00"}, {"version": "v1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "8c434000f420ade76a07c64cbe08ca47e5c101ca"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/8c434000f420ade76a07c64cbe08ca47e5c101ca", "type": "zip", "shasum": "", "reference": "8c434000f420ade76a07c64cbe08ca47e5c101ca"}, "support": {"source": "https://github.com/doctrine/cache/tree/master"}, "time": "2015-08-31T12:36:41+00:00"}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "c9eadeb743ac6199f7eec423cb9426bc518b7b03"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/c9eadeb743ac6199f7eec423cb9426bc518b7b03", "type": "zip", "shasum": "", "reference": "c9eadeb743ac6199f7eec423cb9426bc518b7b03"}, "time": "2015-04-15T00:11:59+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "2346085d2b027b233ae1d5de59b07440b9f288c8"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/2346085d2b027b233ae1d5de59b07440b9f288c8", "type": "zip", "shasum": "", "reference": "2346085d2b027b233ae1d5de59b07440b9f288c8"}, "time": "2015-01-15T20:38:55+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "require-dev": {"phpunit/phpunit": ">=3.7", "satooshi/php-coveralls": "~0.6", "predis/predis": "~0.8"}}, {"version": "v1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "2196b831e62b04986a5c4d208a1b48e0680da369"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/2196b831e62b04986a5c4d208a1b48e0680da369", "type": "zip", "shasum": "", "reference": "2196b831e62b04986a5c4d208a1b48e0680da369"}, "support": {"source": "https://github.com/doctrine/cache/tree/1.3"}, "time": "2015-08-31T13:09:29+00:00", "require-dev": {"phpunit/phpunit": ">=3.7", "satooshi/php-coveralls": "~0.6"}}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "cf483685798a72c93bf4206e3dd6358ea07d64e7"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/cf483685798a72c93bf4206e3dd6358ea07d64e7", "type": "zip", "shasum": "", "reference": "cf483685798a72c93bf4206e3dd6358ea07d64e7"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.3.1"}, "time": "2014-09-17T14:24:04+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.jwage.com/", "role": "Creator"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>", "homepage": "http://www.instaclick.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "e16d7adf45664a50fa86f515b6d5e7f670130449"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/e16d7adf45664a50fa86f515b6d5e7f670130449", "type": "zip", "shasum": "", "reference": "e16d7adf45664a50fa86f515b6d5e7f670130449"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.3.0"}, "time": "2013-10-25T19:04:14+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "d0e4447707a064a5814b18cb0dcc2f24185f6179"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/d0e4447707a064a5814b18cb0dcc2f24185f6179", "type": "zip", "shasum": "", "reference": "d0e4447707a064a5814b18cb0dcc2f24185f6179"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.2.0"}, "time": "2013-09-26T19:23:25+00:00", "require-dev": "__unset"}, {"version": "v1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "2c9761ff1d13e188d5f7378066c1ce2882d7a336"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/2c9761ff1d13e188d5f7378066c1ce2882d7a336", "type": "zip", "shasum": "", "reference": "2c9761ff1d13e188d5f7378066c1ce2882d7a336"}, "support": {"source": "https://github.com/doctrine/cache/tree/master"}, "time": "2013-08-07T16:04:25+00:00"}, {"version": "v1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/cache.git", "type": "git", "reference": "914abe4a49f2eae2b268073cca147b530da0308a"}, "dist": {"url": "https://api.github.com/repos/doctrine/cache/zipball/914abe4a49f2eae2b268073cca147b530da0308a", "type": "zip", "shasum": "", "reference": "914abe4a49f2eae2b268073cca147b530da0308a"}, "support": {"source": "https://github.com/doctrine/cache/tree/v1.0"}, "time": "2013-01-10T22:43:46+00:00", "conflict": "__unset", "extra": "__unset"}]}, "security-advisories": [{"advisoryId": "PKSA-5g3y-77s3-wsh1", "affectedVersions": ">=1.0.0,<1.3.2|>=1.4.0,<1.4.2"}], "last-modified": "Sat, 23 Mar 2024 16:18:13 GMT"}