{"minified": "composer/2.0", "packages": {"mnapoli/php-di": [{"name": "mnapoli/php-di", "description": "The dependency injection container for humans", "keywords": ["dependency injection", "container", "di", "ioc", "container-interop", "PSR-11", "psr11"], "homepage": "https://php-di.org/", "version": "7.0.11", "version_normalized": "********", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "32f111a6d214564520a57831d397263e8946c1d2"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/32f111a6d214564520a57831d397263e8946c1d2", "type": "zip", "shasum": "", "reference": "32f111a6d214564520a57831d397263e8946c1d2"}, "type": "library", "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.11"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/php-di/php-di", "type": "tidelift"}], "time": "2025-06-03T07:45:57+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"DI\\": "src/"}}, "require": {"php": ">=8.0", "psr/container": "^1.1 || ^2.0", "php-di/invoker": "^2.0", "laravel/serializable-closure": "^1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^10 || ^11", "mnapoli/phpunit-easymock": "^1.3", "friendsofphp/proxy-manager-lts": "^1", "friendsofphp/php-cs-fixer": "^3", "vimeo/psalm": "^5|^6"}, "suggest": {"friendsofphp/proxy-manager-lts": "Install it if you want to use lazy injection (version ^1)"}, "provide": {"psr/container-implementation": "^1.0"}, "abandoned": "php-di/php-di"}, {"version": "7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "0d1ed64126577e9a095b3204dcaee58cf76432c2"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/0d1ed64126577e9a095b3204dcaee58cf76432c2", "type": "zip", "shasum": "", "reference": "0d1ed64126577e9a095b3204dcaee58cf76432c2"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.10"}, "time": "2025-04-22T08:53:15+00:00"}, {"version": "7.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "d8480267f5cf239650debba704f3ecd15b638cde"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/d8480267f5cf239650debba704f3ecd15b638cde", "type": "zip", "shasum": "", "reference": "d8480267f5cf239650debba704f3ecd15b638cde"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.9"}, "time": "2025-02-28T12:46:35+00:00", "require-dev": {"phpunit/phpunit": "^9.6", "mnapoli/phpunit-easymock": "^1.3", "friendsofphp/proxy-manager-lts": "^1", "friendsofphp/php-cs-fixer": "^3", "vimeo/psalm": "^5|^6"}}, {"version": "7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "98ddc81f8f768a2ad39e4cbe737285eaeabe577a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/98ddc81f8f768a2ad39e4cbe737285eaeabe577a", "type": "zip", "shasum": "", "reference": "98ddc81f8f768a2ad39e4cbe737285eaeabe577a"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.8"}, "time": "2025-01-28T21:02:46+00:00", "require-dev": {"phpunit/phpunit": "^9.6", "mnapoli/phpunit-easymock": "^1.3", "friendsofphp/proxy-manager-lts": "^1", "friendsofphp/php-cs-fixer": "^3", "vimeo/psalm": "^4.6"}}, {"version": "7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "e87435e3c0e8f22977adc5af0d5cdcc467e15cf1"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/e87435e3c0e8f22977adc5af0d5cdcc467e15cf1", "type": "zip", "shasum": "", "reference": "e87435e3c0e8f22977adc5af0d5cdcc467e15cf1"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.7"}, "time": "2024-07-21T15:55:45+00:00", "require": {"php": ">=8.0", "psr/container": "^1.1 || ^2.0", "php-di/invoker": "^2.0", "laravel/serializable-closure": "^1.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "mnapoli/phpunit-easymock": "^1.3", "friendsofphp/proxy-manager-lts": "^1", "friendsofphp/php-cs-fixer": "^3", "vimeo/psalm": "^4.6"}}, {"version": "7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "8097948a89f6ec782839b3e958432f427cac37fd"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/8097948a89f6ec782839b3e958432f427cac37fd", "type": "zip", "shasum": "", "reference": "8097948a89f6ec782839b3e958432f427cac37fd"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.6"}, "time": "2023-11-02T10:04:50+00:00"}, {"version": "7.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "9ea40a5a6970bf1ca5cbe148bc16cbad6ca3db6c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/9ea40a5a6970bf1ca5cbe148bc16cbad6ca3db6c", "type": "zip", "shasum": "", "reference": "9ea40a5a6970bf1ca5cbe148bc16cbad6ca3db6c"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.5"}, "time": "2023-08-10T14:57:56+00:00"}, {"version": "7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "8ed79468dfb163824bbf48de5e35d1729f9313b6"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/8ed79468dfb163824bbf48de5e35d1729f9313b6", "type": "zip", "shasum": "", "reference": "8ed79468dfb163824bbf48de5e35d1729f9313b6"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.4"}, "time": "2023-08-08T15:59:16+00:00"}, {"version": "7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "d5dad2500f409d8b78371823c8b382fe9b5d0917"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/d5dad2500f409d8b78371823c8b382fe9b5d0917", "type": "zip", "shasum": "", "reference": "d5dad2500f409d8b78371823c8b382fe9b5d0917"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.3"}, "time": "2023-06-17T10:21:14+00:00"}, {"version": "7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "5d1a8664e24f23b25e0426bbcb1288287fb49181"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/5d1a8664e24f23b25e0426bbcb1288287fb49181", "type": "zip", "shasum": "", "reference": "5d1a8664e24f23b25e0426bbcb1288287fb49181"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.2"}, "time": "2023-02-07T17:34:03+00:00", "require-dev": {"phpunit/phpunit": "^9.5", "mnapoli/phpunit-easymock": "^1.3", "ocramius/proxy-manager": "^2.11.2", "friendsofphp/php-cs-fixer": "^3", "vimeo/psalm": "^4.6"}, "suggest": {"ocramius/proxy-manager": "Install it if you want to use lazy injection (version ^2.3)"}}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "1c7f1cc9cf6f51ff7f5f44bb1fa59243fcb7474a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/1c7f1cc9cf6f51ff7f5f44bb1fa59243fcb7474a", "type": "zip", "shasum": "", "reference": "1c7f1cc9cf6f51ff7f5f44bb1fa59243fcb7474a"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.1"}, "time": "2023-01-13T22:30:45+00:00"}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f0ca9a0e0fb800974fcaf7b2f896ca1e840fd15b"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f0ca9a0e0fb800974fcaf7b2f896ca1e840fd15b", "type": "zip", "shasum": "", "reference": "f0ca9a0e0fb800974fcaf7b2f896ca1e840fd15b"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.0"}, "time": "2023-01-12T14:08:11+00:00"}, {"version": "7.0.0-rc1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "8948ffbc43f3e19c66d908593b6a94b2fc1fe6f6"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/8948ffbc43f3e19c66d908593b6a94b2fc1fe6f6", "type": "zip", "shasum": "", "reference": "8948ffbc43f3e19c66d908593b6a94b2fc1fe6f6"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.0-rc1"}, "time": "2023-01-08T19:14:13+00:00"}, {"version": "7.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "5762e370a49c5e74e51be2910ab1ffb1f856fe6b"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/5762e370a49c5e74e51be2910ab1ffb1f856fe6b", "type": "zip", "shasum": "", "reference": "5762e370a49c5e74e51be2910ab1ffb1f856fe6b"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.0-beta3"}, "time": "2021-03-06T20:32:01+00:00", "require": {"php": ">=7.2.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "opis/closure": "^3.5.5"}, "require-dev": {"phpunit/phpunit": "^8.5|^9.0", "mnapoli/phpunit-easymock": "^1.2", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "^2.0.2", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.12"}, "suggest": {"doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~2.0)"}}, {"version": "7.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "b7d3d8f76de627850648e7f96a3bf29618746c09"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/b7d3d8f76de627850648e7f96a3bf29618746c09", "type": "zip", "shasum": "", "reference": "b7d3d8f76de627850648e7f96a3bf29618746c09"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.0-beta2"}, "time": "2020-10-05T14:11:47+00:00", "require": {"php": ">=7.4.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "opis/closure": "^3.5.5"}, "require-dev": {"phpunit/phpunit": "^9.3-dev", "mnapoli/phpunit-easymock": "^1.3", "doctrine/annotations": "^1.7", "ocramius/proxy-manager": "^2.3", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.12"}, "suggest": {"doctrine/annotations": "Install it if you want to use annotations (version ^1.7)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ^2.3)"}}, {"version": "7.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "4aeb4ac24c49e7cc243c0629ab74f6c214056411"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/4aeb4ac24c49e7cc243c0629ab74f6c214056411", "type": "zip", "shasum": "", "reference": "4aeb4ac24c49e7cc243c0629ab74f6c214056411"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/7.0.0-beta1"}, "time": "2020-10-05T13:41:42+00:00"}, {"version": "6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "ae0f1b3b03d8b29dff81747063cbfd6276246cc4"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/ae0f1b3b03d8b29dff81747063cbfd6276246cc4", "type": "zip", "shasum": "", "reference": "ae0f1b3b03d8b29dff81747063cbfd6276246cc4"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.4.0"}, "time": "2022-04-09T16:46:38+00:00", "require": {"php": ">=7.4.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "laravel/serializable-closure": "^1.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "mnapoli/phpunit-easymock": "^1.2", "doctrine/annotations": "~1.10", "ocramius/proxy-manager": "^2.11.2", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.12"}, "suggest": {"doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~2.0)"}}, {"version": "6.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "b8126d066ce144765300ee0ab040c1ed6c9ef588"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/b8126d066ce144765300ee0ab040c1ed6c9ef588", "type": "zip", "shasum": "", "reference": "b8126d066ce144765300ee0ab040c1ed6c9ef588"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.5"}, "time": "2021-09-02T09:49:58+00:00", "require": {"php": ">=7.2.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "opis/closure": "^3.5.5"}, "require-dev": {"phpunit/phpunit": "^8.5|^9.0", "mnapoli/phpunit-easymock": "^1.2", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "^2.0.2", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.12"}}, {"version": "6.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f53bcba06ab31b18e911b77c039377f4ccd1f7a5"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f53bcba06ab31b18e911b77c039377f4ccd1f7a5", "type": "zip", "shasum": "", "reference": "f53bcba06ab31b18e911b77c039377f4ccd1f7a5"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.4"}, "time": "2021-06-10T08:04:48+00:00"}, {"version": "6.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "da8e476cafc8011477e2ec9fd2e4706947758af2"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/da8e476cafc8011477e2ec9fd2e4706947758af2", "type": "zip", "shasum": "", "reference": "da8e476cafc8011477e2ec9fd2e4706947758af2"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.3"}, "time": "2021-05-01T16:26:47+00:00"}, {"version": "6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "12a15cc288bbc89896dd80ce5a36731781613ba2"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/12a15cc288bbc89896dd80ce5a36731781613ba2", "type": "zip", "shasum": "", "reference": "12a15cc288bbc89896dd80ce5a36731781613ba2"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.2"}, "time": "2021-04-20T12:24:31+00:00"}, {"version": "6.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "78278800b18e7c5582fd4d4e629715f5eebbfcc0"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/78278800b18e7c5582fd4d4e629715f5eebbfcc0", "type": "zip", "shasum": "", "reference": "78278800b18e7c5582fd4d4e629715f5eebbfcc0"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.1"}, "time": "2021-03-25T10:29:47+00:00"}, {"version": "6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "955cacea6b0beaba07e8c11b8367f5b3d5abe89f"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/955cacea6b0beaba07e8c11b8367f5b3d5abe89f", "type": "zip", "shasum": "", "reference": "955cacea6b0beaba07e8c11b8367f5b3d5abe89f"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.3.0"}, "time": "2020-10-12T14:39:15+00:00", "require-dev": {"phpunit/phpunit": "^8.5|^9.0", "mnapoli/phpunit-easymock": "^1.2", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.12"}}, {"version": "6.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "96a9ddb0c5d790d120c3332821be09d9bc9db288"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/96a9ddb0c5d790d120c3332821be09d9bc9db288", "type": "zip", "shasum": "", "reference": "96a9ddb0c5d790d120c3332821be09d9bc9db288"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.2.4"}, "time": "2020-10-01T11:44:12+00:00", "require-dev": {"phpunit/phpunit": "^8.5", "mnapoli/phpunit-easymock": "^1.2", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.12"}}, {"version": "6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "63c444646f5f41d84818af1c780abed8ebcc082c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/63c444646f5f41d84818af1c780abed8ebcc082c", "type": "zip", "shasum": "", "reference": "63c444646f5f41d84818af1c780abed8ebcc082c"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.2.3"}, "time": "2020-09-24T12:21:43+00:00"}, {"version": "6.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "40140b5bca07c5fed6919a0f1029ff67617faccd"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/40140b5bca07c5fed6919a0f1029ff67617faccd", "type": "zip", "shasum": "", "reference": "40140b5bca07c5fed6919a0f1029ff67617faccd"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/master"}, "time": "2020-08-23T16:23:17+00:00"}, {"version": "6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "6875fe557c244b3830862c072c7719ca4ac2efe4"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/6875fe557c244b3830862c072c7719ca4ac2efe4", "type": "zip", "shasum": "", "reference": "6875fe557c244b3830862c072c7719ca4ac2efe4"}, "time": "2020-06-18T09:54:32+00:00"}, {"version": "6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "5ca8b43f6dff54a30b0cd8bba86f76d8d1fd24c4"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/5ca8b43f6dff54a30b0cd8bba86f76d8d1fd24c4", "type": "zip", "shasum": "", "reference": "5ca8b43f6dff54a30b0cd8bba86f76d8d1fd24c4"}, "time": "2020-06-10T16:26:00+00:00", "require": {"php": ">=7.2.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "opis/closure": "^3.5.4"}}, {"homepage": "http://php-di.org/", "version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "69238bd49acc0eb6a967029311eeadc3f7c5d538"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/69238bd49acc0eb6a967029311eeadc3f7c5d538", "type": "zip", "shasum": "", "reference": "69238bd49acc0eb6a967029311eeadc3f7c5d538"}, "time": "2020-04-06T09:54:49+00:00", "require": {"php": ">=7.2.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "jeremeamia/superclosure": "^2.0", "nikic/php-parser": "^2.0|^3.0|^4.0"}}, {"version": "6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "9bdcc2f41f5fb700ddd01bc4fa8d5bd7b3f94620"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/9bdcc2f41f5fb700ddd01bc4fa8d5bd7b3f94620", "type": "zip", "shasum": "", "reference": "9bdcc2f41f5fb700ddd01bc4fa8d5bd7b3f94620"}, "time": "2019-12-12T07:58:02+00:00", "require": {"php": ">=7.0.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "jeremeamia/superclosure": "^2.0", "nikic/php-parser": "^2.0|^3.0|^4.0"}, "require-dev": {"phpunit/phpunit": "~6.4", "mnapoli/phpunit-easymock": "~1.0", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2", "friendsofphp/php-cs-fixer": "^2.4", "phpstan/phpstan": "^0.9.2"}, "funding": "__unset"}, {"version": "6.0.10", "version_normalized": "********", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "a6c813bf6b0d0bdeade3ac5a920e2c2a5b1a6ce3"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/a6c813bf6b0d0bdeade3ac5a920e2c2a5b1a6ce3", "type": "zip", "shasum": "", "reference": "a6c813bf6b0d0bdeade3ac5a920e2c2a5b1a6ce3"}, "time": "2019-10-21T11:58:24+00:00"}, {"version": "6.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "00a5eb0f0dd94deabb71acfbf44c37b80d53d0a1"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/00a5eb0f0dd94deabb71acfbf44c37b80d53d0a1", "type": "zip", "shasum": "", "reference": "00a5eb0f0dd94deabb71acfbf44c37b80d53d0a1"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.9"}, "time": "2019-07-08T07:24:22+00:00"}, {"version": "6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "a634876c69b643b779a89e4b7bec1ed1df803afc"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/a634876c69b643b779a89e4b7bec1ed1df803afc", "type": "zip", "shasum": "", "reference": "a634876c69b643b779a89e4b7bec1ed1df803afc"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.8"}, "time": "2019-04-02T20:25:18+00:00"}, {"version": "6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "78326779037852fb8c13be606110434b9911eebd"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/78326779037852fb8c13be606110434b9911eebd", "type": "zip", "shasum": "", "reference": "78326779037852fb8c13be606110434b9911eebd"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.7"}, "time": "2019-03-07T20:02:32+00:00"}, {"version": "6.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f16f27b7bc0876c7e76f568e0733d59997c8ec9c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f16f27b7bc0876c7e76f568e0733d59997c8ec9c", "type": "zip", "shasum": "", "reference": "f16f27b7bc0876c7e76f568e0733d59997c8ec9c"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.6"}, "time": "2019-02-28T14:45:49+00:00"}, {"version": "6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "5e8b809960d5c3bfa096a90da9a78650e80b1f0e"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/5e8b809960d5c3bfa096a90da9a78650e80b1f0e", "type": "zip", "shasum": "", "reference": "5e8b809960d5c3bfa096a90da9a78650e80b1f0e"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.5"}, "time": "2018-09-17T15:34:44+00:00"}, {"version": "6.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "2c7a3a6b7b6c5dbe299a7a0cde386cea5d095eb3"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/2c7a3a6b7b6c5dbe299a7a0cde386cea5d095eb3", "type": "zip", "shasum": "", "reference": "2c7a3a6b7b6c5dbe299a7a0cde386cea5d095eb3"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.4"}, "time": "2018-08-31T21:13:20+00:00"}, {"version": "6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "61ef5c9fcbf04c8504a46d20bd27ea608eab7864"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/61ef5c9fcbf04c8504a46d20bd27ea608eab7864", "type": "zip", "shasum": "", "reference": "61ef5c9fcbf04c8504a46d20bd27ea608eab7864"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.3"}, "time": "2018-08-25T22:21:38+00:00"}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "71a7f2ed1e728138060ea159cbb1a92dc8620bd2"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/71a7f2ed1e728138060ea159cbb1a92dc8620bd2", "type": "zip", "shasum": "", "reference": "71a7f2ed1e728138060ea159cbb1a92dc8620bd2"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.2"}, "time": "2018-04-23T06:54:40+00:00"}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "b632aef4b45af94571a9f4b0e4544ae69dc8eb13"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/b632aef4b45af94571a9f4b0e4544ae69dc8eb13", "type": "zip", "shasum": "", "reference": "b632aef4b45af94571a9f4b0e4544ae69dc8eb13"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.1"}, "time": "2018-04-21T17:47:16+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "4773f7644b0c98ebfc22a1368e519ab5ac2e076c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/4773f7644b0c98ebfc22a1368e519ab5ac2e076c", "type": "zip", "shasum": "", "reference": "4773f7644b0c98ebfc22a1368e519ab5ac2e076c"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0"}, "time": "2018-02-20T07:27:46+00:00", "require": {"php": ">=7.0.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "jeremeamia/superclosure": "^2.0", "nikic/php-parser": "^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "~6.4", "mnapoli/phpunit-easymock": "~1.0", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2", "friendsofphp/php-cs-fixer": "^2.4"}}, {"version": "6.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "e4c85d7533525b22cab96cb7d3e64daeec8c1434"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/e4c85d7533525b22cab96cb7d3e64daeec8c1434", "type": "zip", "shasum": "", "reference": "e4c85d7533525b22cab96cb7d3e64daeec8c1434"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-beta3"}, "time": "2018-01-20T09:11:49+00:00"}, {"version": "6.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "defa62e7f412066e99501025cbc236fbac0fbe6a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/defa62e7f412066e99501025cbc236fbac0fbe6a", "type": "zip", "shasum": "", "reference": "defa62e7f412066e99501025cbc236fbac0fbe6a"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-beta2"}, "time": "2018-01-14T09:14:54+00:00"}, {"version": "6.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "93ac9f1435c298efe5d97c4537096371b79e839c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/93ac9f1435c298efe5d97c4537096371b79e839c", "type": "zip", "shasum": "", "reference": "93ac9f1435c298efe5d97c4537096371b79e839c"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-beta1"}, "time": "2017-12-03T08:23:55+00:00"}, {"keywords": ["dependency injection", "container", "di"], "version": "6.0.0-alpha4", "version_normalized": "*******-alpha4", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "5540f7166b11e507e85759be10cf9021efe44a46"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/5540f7166b11e507e85759be10cf9021efe44a46", "type": "zip", "shasum": "", "reference": "5540f7166b11e507e85759be10cf9021efe44a46"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-alpha4"}, "time": "2017-07-16T11:36:48+00:00", "require-dev": {"phpunit/phpunit": "~5.7", "mnapoli/phpunit-easymock": "~0.2.3", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2"}}, {"version": "6.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "ec4c132ebc9d5e2bad37006aa38d7c2f54819519"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/ec4c132ebc9d5e2bad37006aa38d7c2f54819519", "type": "zip", "shasum": "", "reference": "ec4c132ebc9d5e2bad37006aa38d7c2f54819519"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-alpha3"}, "time": "2017-06-05T12:26:46+00:00", "require": {"php": ">=7.0.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1"}, "replace": {"mnapoli/php-di": "*"}}, {"version": "6.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "c2d4a52480446f8c235c10752ad7d1cc8cebb81a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/c2d4a52480446f8c235c10752ad7d1cc8cebb81a", "type": "zip", "shasum": "", "reference": "c2d4a52480446f8c235c10752ad7d1cc8cebb81a"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-alpha2"}, "time": "2017-06-04T10:42:20+00:00", "autoload": {"files": ["src/DI/functions.php"], "psr-4": {"DI\\": "src/DI/"}}}, {"version": "6.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "ff03746f30bf776641a809e26e28892c73eebfd0"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/ff03746f30bf776641a809e26e28892c73eebfd0", "type": "zip", "shasum": "", "reference": "ff03746f30bf776641a809e26e28892c73eebfd0"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/6.0.0-alpha1"}, "time": "2017-04-15T16:09:37+00:00", "require": {"php": ">=7.0.0", "container-interop/container-interop": "~1.2", "psr/container": "~1.0", "php-di/invoker": "^1.3.2", "php-di/phpdoc-reader": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "~5.0", "mnapoli/phpunit-easymock": "~0.2.0", "doctrine/cache": "~1.4", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2"}, "suggest": {"doctrine/cache": "Install it if you want to use the cache (version ~1.4)", "doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~2.0)"}, "provide": {"container-interop/container-interop-implementation": "^1.0", "psr/container-implementation": "^1.0"}}, {"version": "5.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "3f9255659595f3e289f473778bb6c51aa72abbbd"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/3f9255659595f3e289f473778bb6c51aa72abbbd", "type": "zip", "shasum": "", "reference": "3f9255659595f3e289f473778bb6c51aa72abbbd"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.6"}, "time": "2017-12-03T08:20:27+00:00", "require": {"php": ">=5.5.0", "container-interop/container-interop": "~1.2", "psr/container": "~1.0", "php-di/invoker": "^1.3.2", "php-di/phpdoc-reader": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.2.0", "doctrine/cache": "~1.4", "doctrine/annotations": "~1.2", "phpbench/phpbench": "@dev", "ocramius/proxy-manager": "~1.0|~2.0"}, "suggest": {"doctrine/cache": "Install it if you want to use the cache (version ~1.4)", "doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~1.0 or ~2.0)"}}, {"version": "5.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "770064f4e9c7199ca93397a005aa36e9d13a5a4f"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/770064f4e9c7199ca93397a005aa36e9d13a5a4f", "type": "zip", "shasum": "", "reference": "770064f4e9c7199ca93397a005aa36e9d13a5a4f"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.5"}, "time": "2017-11-11T15:09:16+00:00"}, {"keywords": ["dependency injection", "container", "di", "ioc", "container-interop", "PSR-11", "psr11"], "version": "5.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "6b9342ad230b72dfd0ba527139a9337848a68fa6"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/6b9342ad230b72dfd0ba527139a9337848a68fa6", "type": "zip", "shasum": "", "reference": "6b9342ad230b72dfd0ba527139a9337848a68fa6"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.4"}, "time": "2017-11-11T11:33:20+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"DI\\": "src/"}}, "require": {"php": ">=7.0.0", "psr/container": "^1.0", "php-di/invoker": "^2.0", "php-di/phpdoc-reader": "^2.0.1", "jeremeamia/superclosure": "^2.0", "nikic/php-parser": "^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "~5.7", "mnapoli/phpunit-easymock": "~0.2.3", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~2.0.2", "friendsofphp/php-cs-fixer": "^2.4"}, "suggest": {"doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~2.0)"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": "__unset"}, {"keywords": ["dependency injection", "container", "di"], "version": "5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "8ecded470bb0255c93f2996f78bb3b644c06599a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/8ecded470bb0255c93f2996f78bb3b644c06599a", "type": "zip", "shasum": "", "reference": "8ecded470bb0255c93f2996f78bb3b644c06599a"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.3"}, "time": "2017-04-11T19:42:20+00:00", "autoload": {"files": ["src/DI/functions.php"], "psr-4": {"DI\\": "src/DI/"}}, "require": {"php": ">=5.5.0", "container-interop/container-interop": "~1.2", "psr/container": "~1.0", "php-di/invoker": "^1.3.2", "php-di/phpdoc-reader": "^2.0.1"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.2.0", "doctrine/cache": "~1.4", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~1.0|~2.0"}, "suggest": {"doctrine/cache": "Install it if you want to use the cache (version ~1.4)", "doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~1.0 or ~2.0)"}, "provide": {"container-interop/container-interop-implementation": "^1.0", "psr/container-implementation": "^1.0"}, "replace": {"mnapoli/php-di": "*"}}, {"version": "5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "bbe2ed4b48e5b832436bf50928c30af8aedf61f8"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/bbe2ed4b48e5b832436bf50928c30af8aedf61f8", "type": "zip", "shasum": "", "reference": "bbe2ed4b48e5b832436bf50928c30af8aedf61f8"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.2"}, "time": "2017-03-27T19:30:46+00:00"}, {"version": "5.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "4ec345f6e7f5e18e955d0973f8611dcc0ab348be"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/4ec345f6e7f5e18e955d0973f8611dcc0ab348be", "type": "zip", "shasum": "", "reference": "4ec345f6e7f5e18e955d0973f8611dcc0ab348be"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.1"}, "time": "2017-03-22T21:09:54+00:00", "provide": {"container-interop/container-interop-implementation": "^1.0"}}, {"version": "5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "e348393488fa909e4bc0707ba5c9c44cd602a1cb"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/e348393488fa909e4bc0707ba5c9c44cd602a1cb", "type": "zip", "shasum": "", "reference": "e348393488fa909e4bc0707ba5c9c44cd602a1cb"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.0"}, "time": "2016-08-23T20:18:00+00:00", "require": {"php": ">=5.5.0", "container-interop/container-interop": "~1.0", "php-di/invoker": "^1.3.2", "php-di/phpdoc-reader": "^2.0.1"}}, {"version": "5.4.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "9845d40a1bf91179a0751dca036ab3a9621fa3d0"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/9845d40a1bf91179a0751dca036ab3a9621fa3d0", "type": "zip", "shasum": "", "reference": "9845d40a1bf91179a0751dca036ab3a9621fa3d0"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.4.0-beta1"}, "time": "2016-07-15T12:38:33+00:00"}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "854a6d8f54e2146f0a34f0a28f0adea688b634a3"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/854a6d8f54e2146f0a34f0a28f0adea688b634a3", "type": "zip", "shasum": "", "reference": "854a6d8f54e2146f0a34f0a28f0adea688b634a3"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.3.0"}, "time": "2016-06-01T17:23:41+00:00", "require": {"php": ">=5.5.0", "container-interop/container-interop": "~1.0", "php-di/invoker": "^1.1.1", "php-di/phpdoc-reader": "^2.0.1"}}, {"version": "5.3.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "43b330246094e5b47a2bc22ddd209ebdd4ebabbb"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/43b330246094e5b47a2bc22ddd209ebdd4ebabbb", "type": "zip", "shasum": "", "reference": "43b330246094e5b47a2bc22ddd209ebdd4ebabbb"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.3.0-beta1"}, "time": "2016-05-15T10:23:20+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.2.0", "doctrine/cache": "~1.4", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~1.0"}, "suggest": {"doctrine/cache": "Install it if you want to use the cache (version ~1.4)", "doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~1.0)"}, "provide": "__unset"}, {"version": "5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f574bcc841201ab04587b1c6da1234d4044f67d8"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f574bcc841201ab04587b1c6da1234d4044f67d8", "type": "zip", "shasum": "", "reference": "f574bcc841201ab04587b1c6da1234d4044f67d8"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.2.2"}, "time": "2016-02-09T22:00:00+00:00", "require": {"php": ">=5.4.0", "container-interop/container-interop": "~1.0", "php-di/invoker": "^1.1.1", "php-di/phpdoc-reader": "^2.0.1"}}, {"version": "5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f3ae41b493b9e95edcb698ecbd15990063c0414c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f3ae41b493b9e95edcb698ecbd15990063c0414c", "type": "zip", "shasum": "", "reference": "f3ae41b493b9e95edcb698ecbd15990063c0414c"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.2.1"}, "time": "2016-01-14T21:46:36+00:00"}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "e20ff1b465a15548e22efcea79e06df64d9e2068"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/e20ff1b465a15548e22efcea79e06df64d9e2068", "type": "zip", "shasum": "", "reference": "e20ff1b465a15548e22efcea79e06df64d9e2068"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.2.0"}, "time": "2015-12-20T13:46:44+00:00"}, {"version": "5.2.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "773145e1a0bf412d54b0e480fd7c9fe0e160df3e"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/773145e1a0bf412d54b0e480fd7c9fe0e160df3e", "type": "zip", "shasum": "", "reference": "773145e1a0bf412d54b0e480fd7c9fe0e160df3e"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.2.0-beta1"}, "time": "2015-11-29T20:40:37+00:00"}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f4a8088fa4eb480ee66c51b5ee4e4e30cce78489"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f4a8088fa4eb480ee66c51b5ee4e4e30cce78489", "type": "zip", "shasum": "", "reference": "f4a8088fa4eb480ee66c51b5ee4e4e30cce78489"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.1.0"}, "time": "2015-09-08T08:56:29+00:00", "require": {"php": ">=5.4.0", "container-interop/container-interop": "~1.0", "php-di/invoker": "^1.0.1", "php-di/phpdoc-reader": "~2.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.1.4", "doctrine/cache": "~1.4", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~1.0"}}, {"version": "5.1.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "c76acebd31cab0736b739e70d3ab43cda3cf445a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/c76acebd31cab0736b739e70d3ab43cda3cf445a", "type": "zip", "shasum": "", "reference": "c76acebd31cab0736b739e70d3ab43cda3cf445a"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.1.0-beta1"}, "time": "2015-09-06T16:59:21+00:00"}, {"version": "5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "b8e9f51c41047dd463e0415167be2155f305c187"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/b8e9f51c41047dd463e0415167be2155f305c187", "type": "zip", "shasum": "", "reference": "b8e9f51c41047dd463e0415167be2155f305c187"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.0.4"}, "time": "2015-09-02T16:50:29+00:00", "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.1.4", "doctrine/cache": "~1.0", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~1.0"}, "suggest": {"doctrine/cache": "Install it if you want to use the cache (version ~1.0)", "doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~1.0)"}}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "c59af31dcf2ee6246e28464e775599321df40955"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/c59af31dcf2ee6246e28464e775599321df40955", "type": "zip", "shasum": "", "reference": "c59af31dcf2ee6246e28464e775599321df40955"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.0.3"}, "time": "2015-08-12T13:46:41+00:00", "require": {"php": ">=5.4.0", "container-interop/container-interop": "~1.0", "php-di/invoker": "~1.0", "php-di/phpdoc-reader": "~2.0"}}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "563d41af8a9b1c45f919bc83f514f32c4515d17e"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/563d41af8a9b1c45f919bc83f514f32c4515d17e", "type": "zip", "shasum": "", "reference": "563d41af8a9b1c45f919bc83f514f32c4515d17e"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/4.4.9"}, "time": "2015-07-26T12:56:50+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "166e0431d011efb64f01e770ccfdfefc766c3a28"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/166e0431d011efb64f01e770ccfdfefc766c3a28", "type": "zip", "shasum": "", "reference": "166e0431d011efb64f01e770ccfdfefc766c3a28"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.0.1"}, "time": "2015-07-24T14:06:47+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "c3bbc0d334b888ec8baa75f95b4900ee17b18fa7"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/c3bbc0d334b888ec8baa75f95b4900ee17b18fa7", "type": "zip", "shasum": "", "reference": "c3bbc0d334b888ec8baa75f95b4900ee17b18fa7"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/5.0.0"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/php-di/php-di", "type": "tidelift"}], "time": "2015-06-10T06:16:52+00:00", "replace": "__unset"}, {"description": "PHP-DI is a Container that makes Dependency Injection as practical as possible in PHP", "homepage": "http://mnapoli.github.com/PHP-DI/", "version": "5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "7afd2574c3f4c23814a43dbca23d7cd1741aa489"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/7afd2574c3f4c23814a43dbca23d7cd1741aa489", "type": "zip", "shasum": "", "reference": "7afd2574c3f4c23814a43dbca23d7cd1741aa489"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/5.0"}, "time": "2015-05-15T14:29:13+00:00", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "require": {"php": ">=5.4.0", "doctrine/annotations": "~1.2", "doctrine/cache": "~1.0", "mnapoli/phpdocreader": "~1.3", "container-interop/container-interop": "~1.0", "php-di/invoker": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.1.4", "ocramius/proxy-manager": "~1.0"}, "suggest": {"ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~1.0)"}, "funding": "__unset"}, {"version": "5.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "2325afb15d74728f52cb9721c9e184829f8f343a"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/2325afb15d74728f52cb9721c9e184829f8f343a", "type": "zip", "shasum": "", "reference": "2325afb15d74728f52cb9721c9e184829f8f343a"}, "time": "2015-04-25T02:05:04+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "~1.2", "doctrine/cache": "~1.0", "mnapoli/phpdocreader": "~1.3", "container-interop/container-interop": "~1.0", "php-di/invoker": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.1.4", "ocramius/proxy-manager": "~0.5"}, "suggest": {"ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~0.5)"}}, {"version": "4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "cc10862b66267b6e6f793eda2867b0aef5b693be"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/cc10862b66267b6e6f793eda2867b0aef5b693be", "type": "zip", "shasum": "", "reference": "cc10862b66267b6e6f793eda2867b0aef5b693be"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/4.x"}, "time": "2015-07-24T09:21:24+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "~1.2", "doctrine/cache": "~1.0", "php-di/phpdoc-reader": "~1.3", "myclabs/php-enum": "~1.1", "ocramius/proxy-manager": "~0.5", "container-interop/container-interop": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": "__unset", "extra": "__unset"}, {"description": "The dependency injection container for humans", "homepage": "http://php-di.org/", "version": "4.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "563d41af8a9b1c45f919bc83f514f32c4515d17e"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/563d41af8a9b1c45f919bc83f514f32c4515d17e", "type": "zip", "shasum": "", "reference": "563d41af8a9b1c45f919bc83f514f32c4515d17e"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/4.4.9"}, "time": "2015-07-26T12:56:50+00:00", "require": {"php": ">=5.4.0", "container-interop/container-interop": "~1.0", "php-di/invoker": "~1.0", "php-di/phpdoc-reader": "~2.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "mnapoli/phpunit-easymock": "~0.1.4", "doctrine/cache": "~1.0", "doctrine/annotations": "~1.2", "ocramius/proxy-manager": "~1.0"}, "suggest": {"doctrine/cache": "Install it if you want to use the cache (version ~1.0)", "doctrine/annotations": "Install it if you want to use annotations (version ~1.2)", "ocramius/proxy-manager": "Install it if you want to use lazy injection (version ~1.0)"}, "replace": {"mnapoli/php-di": "*"}}, {"description": "PHP-DI is a Container that makes Dependency Injection as practical as possible in PHP", "homepage": "http://mnapoli.github.com/PHP-DI/", "version": "4.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "77a5206c58aef16e3db44ea8cb52d351a9a31aa8"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/77a5206c58aef16e3db44ea8cb52d351a9a31aa8", "type": "zip", "shasum": "", "reference": "77a5206c58aef16e3db44ea8cb52d351a9a31aa8"}, "support": {"issues": "https://github.com/PHP-DI/PHP-DI/issues", "source": "https://github.com/PHP-DI/PHP-DI/tree/4.4.8"}, "time": "2015-07-20T16:47:28+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "~1.2", "doctrine/cache": "~1.0", "php-di/phpdoc-reader": "~1.3", "myclabs/php-enum": "~1.1", "ocramius/proxy-manager": "~0.5", "container-interop/container-interop": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": "__unset", "replace": "__unset"}, {"version": "4.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "265d8a1d8cbc18600fe2c3358d33ca0f5be7b6ce"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/265d8a1d8cbc18600fe2c3358d33ca0f5be7b6ce", "type": "zip", "shasum": "", "reference": "265d8a1d8cbc18600fe2c3358d33ca0f5be7b6ce"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/master"}, "time": "2015-03-24T05:51:50+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "~1.2", "doctrine/cache": "~1.0", "mnapoli/phpdocreader": "~1.3", "myclabs/php-enum": "~1.1", "ocramius/proxy-manager": "~0.5", "container-interop/container-interop": "~1.0"}}, {"version": "4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "92f54750b616bef0a8b06b5a0ab6c867d845ce32"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/92f54750b616bef0a8b06b5a0ab6c867d845ce32", "type": "zip", "shasum": "", "reference": "92f54750b616bef0a8b06b5a0ab6c867d845ce32"}, "time": "2015-01-10T09:52:23+00:00"}, {"version": "4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "2c324795faed02dbb22224717dd2f87dae11e9f1"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/2c324795faed02dbb22224717dd2f87dae11e9f1", "type": "zip", "shasum": "", "reference": "2c324795faed02dbb22224717dd2f87dae11e9f1"}, "time": "2014-12-11T22:20:25+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "mnapoli/phpdocreader": "~1.3", "myclabs/php-enum": "1.*", "ocramius/proxy-manager": "~0.3", "container-interop/container-interop": "~1.0"}}, {"version": "4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "16fab8e5cccf9edc01d793e5cec2befe5fb38411"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/16fab8e5cccf9edc01d793e5cec2befe5fb38411", "type": "zip", "shasum": "", "reference": "16fab8e5cccf9edc01d793e5cec2befe5fb38411"}, "time": "2014-11-27T23:42:32+00:00"}, {"version": "4.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "cbf775bcfc4220dfc62ab326eb879662f6ce0fca"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/cbf775bcfc4220dfc62ab326eb879662f6ce0fca", "type": "zip", "shasum": "", "reference": "cbf775bcfc4220dfc62ab326eb879662f6ce0fca"}, "time": "2014-11-27T04:40:03+00:00"}, {"version": "4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "0e267e20226589105ac3c6ae9d893cfbc350a193"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/0e267e20226589105ac3c6ae9d893cfbc350a193", "type": "zip", "shasum": "", "reference": "0e267e20226589105ac3c6ae9d893cfbc350a193"}, "time": "2014-11-10T01:37:46+00:00", "autoload": {"files": ["src/DI/functions.php"], "psr-0": {"DI\\": "src/", "UnitTests\\": "tests/", "IntegrationTests\\": "tests/"}}}, {"version": "4.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "de13eb81314d9a51673e74d73d57f5768788ec0f"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/de13eb81314d9a51673e74d73d57f5768788ec0f", "type": "zip", "shasum": "", "reference": "de13eb81314d9a51673e74d73d57f5768788ec0f"}, "time": "2014-11-04T10:15:17+00:00"}, {"version": "4.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "556e3be63a821bbad0585a34f634080305392252"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/556e3be63a821bbad0585a34f634080305392252", "type": "zip", "shasum": "", "reference": "556e3be63a821bbad0585a34f634080305392252"}, "time": "2014-10-14T06:13:26+00:00"}, {"version": "4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "99939b3da11d74cb5ddec7ee78ef7c989734182b"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/99939b3da11d74cb5ddec7ee78ef7c989734182b", "type": "zip", "shasum": "", "reference": "99939b3da11d74cb5ddec7ee78ef7c989734182b"}, "time": "2014-08-12T16:16:06+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "mnapoli/phpdocreader": "~1.2", "myclabs/php-enum": "1.*", "ocramius/proxy-manager": "~0.3", "container-interop/container-interop": "~1.0"}}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "9e6029df2a8a0cc8202b7a2dd9f5191b221198c1"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/9e6029df2a8a0cc8202b7a2dd9f5191b221198c1", "type": "zip", "shasum": "", "reference": "9e6029df2a8a0cc8202b7a2dd9f5191b221198c1"}, "time": "2014-08-08T14:50:28+00:00"}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "11a94910524cfd648ea06f393247be48036a2563"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/11a94910524cfd648ea06f393247be48036a2563", "type": "zip", "shasum": "", "reference": "11a94910524cfd648ea06f393247be48036a2563"}, "time": "2014-08-06T23:30:08+00:00"}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "66bb638286843cb777245c55758215008ff6ade4"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/66bb638286843cb777245c55758215008ff6ade4", "type": "zip", "shasum": "", "reference": "66bb638286843cb777245c55758215008ff6ade4"}, "time": "2014-07-29T14:56:29+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "mnapoli/phpdocreader": "~1.2", "myclabs/php-enum": "1.*", "ocramius/proxy-manager": "~0.3", "symfony/yaml": "2.*", "container-interop/container-interop": "~1.0"}}, {"version": "4.2.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "64745b75a2f3f44ad36dd6b1c589a207debff1ca"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/64745b75a2f3f44ad36dd6b1c589a207debff1ca", "type": "zip", "shasum": "", "reference": "64745b75a2f3f44ad36dd6b1c589a207debff1ca"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/4.2"}, "time": "2014-06-06T14:28:28+00:00", "require-dev": "__unset"}, {"version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "54d13efe55fdc3de46f56df4b591c6b21f9a3b97"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/54d13efe55fdc3de46f56df4b591c6b21f9a3b97", "type": "zip", "shasum": "", "reference": "54d13efe55fdc3de46f56df4b591c6b21f9a3b97"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/master"}, "time": "2014-04-17T11:22:29+00:00"}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "f6eb3e51406dfad1a9a5b2a5d0847def7b5d13b6"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/f6eb3e51406dfad1a9a5b2a5d0847def7b5d13b6", "type": "zip", "shasum": "", "reference": "f6eb3e51406dfad1a9a5b2a5d0847def7b5d13b6"}, "time": "2014-04-17T07:12:22+00:00"}, {"version": "4.1.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "4b30cb1f079f68e2c674a07bba74cb90561d7e8b"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/4b30cb1f079f68e2c674a07bba74cb90561d7e8b", "type": "zip", "shasum": "", "reference": "4b30cb1f079f68e2c674a07bba74cb90561d7e8b"}, "time": "2014-03-22T20:20:04+00:00", "require": {"php": ">=5.3.3", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "mnapoli/phpdocreader": "~1.2", "myclabs/php-enum": "1.*", "ocramius/proxy-manager": "~0.3", "symfony/yaml": "2.*"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "6c1a1d4ad23e49460975b7f57218b90fe3bca434"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/6c1a1d4ad23e49460975b7f57218b90fe3bca434", "type": "zip", "shasum": "", "reference": "6c1a1d4ad23e49460975b7f57218b90fe3bca434"}, "time": "2014-02-04T10:22:57+00:00"}, {"version": "4.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "73123357b5f2b539a56ab41d8f6c7eef6db5e8f9"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/73123357b5f2b539a56ab41d8f6c7eef6db5e8f9", "type": "zip", "shasum": "", "reference": "73123357b5f2b539a56ab41d8f6c7eef6db5e8f9"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/4.0"}, "time": "2014-01-14T11:38:45+00:00", "autoload": {"files": ["src/DI/functions.php"], "psr-0": {"DI": "src/", "UnitTests": "tests/", "IntegrationTests": "tests/"}}, "require": {"php": ">=5.3.3", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "mnapoli/phpdocreader": "~1.1", "myclabs/php-enum": "1.*", "ocramius/proxy-manager": "~0.3", "symfony/yaml": "2.*"}}, {"version": "4.0.0-beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "3c52c70bab4f66478cd2355d0efbc3f194d50922"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/3c52c70bab4f66478cd2355d0efbc3f194d50922", "type": "zip", "shasum": "", "reference": "3c52c70bab4f66478cd2355d0efbc3f194d50922"}, "time": "2013-12-27T02:19:31+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "a039bcd574cce6b2370577f927fbda5e90c7540f"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/a039bcd574cce6b2370577f927fbda5e90c7540f", "type": "zip", "shasum": "", "reference": "a039bcd574cce6b2370577f927fbda5e90c7540f"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/master"}, "time": "2013-10-29T09:22:31+00:00", "autoload": {"psr-0": {"DI": "src/", "UnitTests": "tests/", "IntegrationTests": "tests/"}}, "require": {"php": ">=5.3.0", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "myclabs/php-enum": "1.*", "symfony/yaml": "2.*", "ocramius/proxy-manager": "~0.3"}}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "c33f4efffd4f64c351dc25907622f54e4ad04374"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/c33f4efffd4f64c351dc25907622f54e4ad04374", "type": "zip", "shasum": "", "reference": "c33f4efffd4f64c351dc25907622f54e4ad04374"}, "time": "2013-10-11T10:13:25+00:00"}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "14ea2c8e131689633b0d3da567a365a6a4cae5d3"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/14ea2c8e131689633b0d3da567a365a6a4cae5d3", "type": "zip", "shasum": "", "reference": "14ea2c8e131689633b0d3da567a365a6a4cae5d3"}, "time": "2013-09-24T17:33:08+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "48de53e279dce1c8bf05c9cce455608a425b0283"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/48de53e279dce1c8bf05c9cce455608a425b0283", "type": "zip", "shasum": "", "reference": "48de53e279dce1c8bf05c9cce455608a425b0283"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.3.0"}, "time": "2013-07-30T16:04:56+00:00", "require": {"php": ">=5.3.0", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "myclabs/php-enum": "1.*", "symfony/yaml": "2.*", "ocramius/proxy-manager": "0.3.*"}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "302191e662d6028d2f833dda450b74f19ef12832"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/302191e662d6028d2f833dda450b74f19ef12832", "type": "zip", "shasum": "", "reference": "302191e662d6028d2f833dda450b74f19ef12832"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.2.0"}, "time": "2013-07-23T13:56:31+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "aa60d91daca1baa8de08d586bfc4ecfdf868f844"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/aa60d91daca1baa8de08d586bfc4ecfdf868f844", "type": "zip", "shasum": "", "reference": "aa60d91daca1baa8de08d586bfc4ecfdf868f844"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.1.1"}, "time": "2013-07-12T13:12:23+00:00", "require": {"php": ">=5.3.0", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "myclabs/php-enum": "1.*", "symfony/yaml": "2.*"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "a551562200aee7b5b99b19330ab61e806e1a99ae"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/a551562200aee7b5b99b19330ab61e806e1a99ae", "type": "zip", "shasum": "", "reference": "a551562200aee7b5b99b19330ab61e806e1a99ae"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.1.0"}, "time": "2013-06-23T20:22:46+00:00"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "227f719984fa54c9664f2a42be33542ae7b21866"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/227f719984fa54c9664f2a42be33542ae7b21866", "type": "zip", "shasum": "", "reference": "227f719984fa54c9664f2a42be33542ae7b21866"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.6"}, "time": "2013-06-18T09:37:44+00:00"}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "55eb0f328bf0754b996af8810f58d2a43bd7575a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/55eb0f328bf0754b996af8810f58d2a43bd7575a", "type": "zip", "shasum": "", "reference": "55eb0f328bf0754b996af8810f58d2a43bd7575a"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.5"}, "time": "2013-06-11T09:46:55+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "e22bd1f99b4e8503c50cc008889c95185408737e"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/e22bd1f99b4e8503c50cc008889c95185408737e", "type": "zip", "shasum": "", "reference": "e22bd1f99b4e8503c50cc008889c95185408737e"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.4"}, "time": "2013-06-10T15:33:19+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "c07a57981795277da5b343f2a48f3397b5bddc70"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/c07a57981795277da5b343f2a48f3397b5bddc70", "type": "zip", "shasum": "", "reference": "c07a57981795277da5b343f2a48f3397b5bddc70"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.3"}, "time": "2013-06-10T15:04:03+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "f7252a24d5db184f3be4896c2bc2b41aace7301c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/f7252a24d5db184f3be4896c2bc2b41aace7301c", "type": "zip", "shasum": "", "reference": "f7252a24d5db184f3be4896c2bc2b41aace7301c"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.2"}, "time": "2013-05-22T07:58:30+00:00", "require": {"php": ">=5.3.0", "doctrine/annotations": "1.*", "doctrine/cache": "1.*", "myclabs/php-enum": "*", "symfony/yaml": "v2.2.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "edff14679f7919be922552d63660f3c0d32efb41"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/edff14679f7919be922552d63660f3c0d32efb41", "type": "zip", "shasum": "", "reference": "edff14679f7919be922552d63660f3c0d32efb41"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.1"}, "time": "2013-05-04T22:47:47+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "01e191a33f061ccacd11c9c83ce15f29f8f3512b"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/01e191a33f061ccacd11c9c83ce15f29f8f3512b", "type": "zip", "shasum": "", "reference": "01e191a33f061ccacd11c9c83ce15f29f8f3512b"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/3.0.0"}, "time": "2013-04-23T21:51:04+00:00"}, {"description": "PHP dependency injection using annotations", "version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "957ec74808579556725c2a822d969e9dd96298eb"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/957ec74808579556725c2a822d969e9dd96298eb", "type": "zip", "shasum": "", "reference": "957ec74808579556725c2a822d969e9dd96298eb"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/2.1.0"}, "time": "2013-01-29T14:50:40+00:00", "autoload": {"psr-0": {"DI": "src/", "UnitTests": "tests/", "Benchmarks": "tests/", "IntegrationTests": "tests/"}}, "require": {"php": ">=5.3.0", "doctrine/annotations": "1.*", "doctrine/cache": "1.*"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "aeea260985b4149b1029d015d63fb6237a762981"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/aeea260985b4149b1029d015d63fb6237a762981", "type": "zip", "shasum": "", "reference": "aeea260985b4149b1029d015d63fb6237a762981"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/2.0.1"}, "time": "2012-12-09T15:01:10+00:00", "require": {"php": ">=5.3.0", "doctrine/common": "2.*"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "eecac315e92c3e01a4c13f960467edaa694be5c2"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/eecac315e92c3e01a4c13f960467edaa694be5c2", "type": "zip", "shasum": "", "reference": "eecac315e92c3e01a4c13f960467edaa694be5c2"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/2.0.0"}, "time": "2012-12-05T17:03:22+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "license": ["LGPL"], "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "4511b79fc8062e23ef6f60017777da9aa3dec96c"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/4511b79fc8062e23ef6f60017777da9aa3dec96c", "type": "zip", "shasum": "", "reference": "4511b79fc8062e23ef6f60017777da9aa3dec96c"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/1.1.0"}, "time": "2012-11-01T21:37:39+00:00", "require": {"doctrine/common": "2.*"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "5aeecd1aa0debc65c1f1d11a6e034dea757af78f"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/5aeecd1aa0debc65c1f1d11a6e034dea757af78f", "type": "zip", "shasum": "", "reference": "5aeecd1aa0debc65c1f1d11a6e034dea757af78f"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/1.0.3"}, "time": "2012-10-26T19:58:44+00:00", "autoload": {"psr-0": {"DI": "src/", "DI\\Tests": "tests/"}}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "630a165b893197c5d7083ddc12e45d5a079786e7"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/630a165b893197c5d7083ddc12e45d5a079786e7", "type": "zip", "shasum": "", "reference": "630a165b893197c5d7083ddc12e45d5a079786e7"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/1.0.2"}, "time": "2012-10-26T19:52:05+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "2769024a40469b27958fb9fe5acf939edaff76b9"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/2769024a40469b27958fb9fe5acf939edaff76b9", "type": "zip", "shasum": "", "reference": "2769024a40469b27958fb9fe5acf939edaff76b9"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/1.0.1"}, "time": "2012-10-26T19:39:38+00:00", "require": {"doctrine/common": "2.*", "mnapoli/phpbench": "dev-master"}}, {"description": "", "keywords": [], "homepage": "", "version": "1.0.0", "version_normalized": "*******", "license": [], "source": {"url": "https://github.com/PHP-DI/PHP-DI.git", "type": "git", "reference": "73ae6029efbb4a148b9c23472ee05c088735ec62"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/PHP-DI/zipball/73ae6029efbb4a148b9c23472ee05c088735ec62", "type": "zip", "shasum": "", "reference": "73ae6029efbb4a148b9c23472ee05c088735ec62"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/1.0.0"}, "time": "2012-10-06T11:35:10+00:00", "require": {"doctrine/common": "2.*"}}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/PHP-DI.git", "type": "git", "reference": "7199cf9d91db296118a9826c14deeb816810da46"}, "dist": {"url": "https://api.github.com/repos/mnapoli/PHP-DI/zipball/7199cf9d91db296118a9826c14deeb816810da46", "type": "zip", "shasum": "", "reference": "7199cf9d91db296118a9826c14deeb816810da46"}, "support": {"issues": "https://github.com/mnapoli/PHP-DI/issues", "source": "https://github.com/mnapoli/PHP-DI/tree/0.9.0"}, "time": "2012-09-29T18:16:51+00:00", "require": {"doctrine/common": "2.2.*"}}]}, "security-advisories": [], "last-modified": "Tue, 03 Jun 2025 07:46:30 GMT"}