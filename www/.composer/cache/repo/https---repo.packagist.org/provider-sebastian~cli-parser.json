{"minified": "composer/2.0", "packages": {"sebastian/cli-parser": [{"name": "sebastian/cli-parser", "description": "Library for parsing CLI options", "keywords": [], "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "version": "4.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "6d584c727d9114bcdc14c86711cd1cad51778e7c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/6d584c727d9114bcdc14c86711cd1cad51778e7c", "type": "zip", "shasum": "", "reference": "6d584c727d9114bcdc14c86711cd1cad51778e7c"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:53:50+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "15c5dd40dc4f38794d383bb95465193f5e0ae180"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/15c5dd40dc4f38794d383bb95465193f5e0ae180", "type": "zip", "shasum": "", "reference": "15c5dd40dc4f38794d383bb95465193f5e0ae180"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/3.0.2"}, "time": "2024-07-03T04:41:36+00:00", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "00a74d5568694711f0222e54fb281e1d15fdf04a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/00a74d5568694711f0222e54fb281e1d15fdf04a", "type": "zip", "shasum": "", "reference": "00a74d5568694711f0222e54fb281e1d15fdf04a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/3.0.1"}, "time": "2024-03-02T07:26:58+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "efd6ce5bb8131fe981e2f879dbd47605fbe0cc6f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/efd6ce5bb8131fe981e2f879dbd47605fbe0cc6f", "type": "zip", "shasum": "", "reference": "efd6ce5bb8131fe981e2f879dbd47605fbe0cc6f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/3.0.0"}, "time": "2024-02-02T05:48:04+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "type": "zip", "shasum": "", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "time": "2024-03-02T07:12:49+00:00", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "efdc130dbbbb8ef0b545a994fd811725c5282cae"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/efdc130dbbbb8ef0b545a994fd811725c5282cae", "type": "zip", "shasum": "", "reference": "efdc130dbbbb8ef0b545a994fd811725c5282cae"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.0"}, "time": "2023-02-03T06:58:15+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "type": "zip", "shasum": "", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "time": "2024-03-02T06:27:43+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/442e7c7e687e42adc03470c7b668bc4b2402c0b2", "type": "zip", "shasum": "", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.1"}, "time": "2020-09-28T06:08:49+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "type": "git", "reference": "2a4a38c56e62f7295bedb8b1b7439ad523d4ea82"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2a4a38c56e62f7295bedb8b1b7439ad523d4ea82", "type": "zip", "shasum": "", "reference": "2a4a38c56e62f7295bedb8b1b7439ad523d4ea82"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/master"}, "time": "2020-08-12T10:49:21+00:00", "require": {"php": "^7.3 || ^8.0"}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:54:45 GMT"}