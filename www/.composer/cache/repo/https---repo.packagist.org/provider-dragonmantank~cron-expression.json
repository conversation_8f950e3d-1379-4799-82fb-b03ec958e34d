{"minified": "composer/2.0", "packages": {"dragonmantank/cron-expression": [{"name": "dragonmantank/cron-expression", "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "homepage": "", "version": "v3.4.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "type": "zip", "shasum": "", "reference": "8c784d071debd117328803d86b2097615b457500"}, "type": "library", "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2024-10-09T13:47:03+00:00", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "phpstan/extension-installer": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}}, {"version": "v3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "type": "zip", "shasum": "", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.3"}, "time": "2023-08-10T19:36:49+00:00", "require-dev": {"phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpstan/extension-installer": "^1.0"}, "extra": "__unset"}, {"version": "v3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "782ca5968ab8b954773518e9e49a6f892a34b2a8"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/782ca5968ab8b954773518e9e49a6f892a34b2a8", "type": "zip", "shasum": "", "reference": "782ca5968ab8b954773518e9e49a6f892a34b2a8"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.2"}, "time": "2022-09-10T18:51:20+00:00"}, {"version": "v3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "be85b3f05b46c39bbc0d95f6c071ddff669510fa"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/be85b3f05b46c39bbc0d95f6c071ddff669510fa", "type": "zip", "shasum": "", "reference": "be85b3f05b46c39bbc0d95f6c071ddff669510fa"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.1"}, "time": "2022-01-18T15:43:28+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "63f2a76a045bac6ec93cc2daf2b534b412aa0313"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/63f2a76a045bac6ec93cc2daf2b534b412aa0313", "type": "zip", "shasum": "", "reference": "63f2a76a045bac6ec93cc2daf2b534b412aa0313"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.0"}, "time": "2022-01-14T16:02:05+00:00"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "9545dea2a1d92b60c8b3d06f02025c83e999bde0"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/9545dea2a1d92b60c8b3d06f02025c83e999bde0", "type": "zip", "shasum": "", "reference": "9545dea2a1d92b60c8b3d06f02025c83e999bde0"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.2.4"}, "time": "2022-01-13T04:09:37+00:00", "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.7.0"}, "require-dev": {"phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.0|^8.0|^9.0", "phpstan/phpstan-webmozart-assert": "^0.12.7", "phpstan/extension-installer": "^1.0"}}, {"version": "v3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "47c53bbb260d3c398fba9bfa9683dcf67add2579"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/47c53bbb260d3c398fba9bfa9683dcf67add2579", "type": "zip", "shasum": "", "reference": "47c53bbb260d3c398fba9bfa9683dcf67add2579"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.2.3"}, "time": "2022-01-06T05:35:07+00:00"}, {"version": "v3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "c9e208317b0cf679097cf976ffbb0b0eec81d4df"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/c9e208317b0cf679097cf976ffbb0b0eec81d4df", "type": "zip", "shasum": "", "reference": "c9e208317b0cf679097cf976ffbb0b0eec81d4df"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.2.2"}, "time": "2022-01-05T06:05:42+00:00"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "cf3bbe954c2b3892f6dadc89faa5b30ee0207994"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/cf3bbe954c2b3892f6dadc89faa5b30ee0207994", "type": "zip", "shasum": "", "reference": "cf3bbe954c2b3892f6dadc89faa5b30ee0207994"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.2.1"}, "time": "2022-01-05T04:03:07+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "570c4f0188ce342ec69afaa244661fdb4faca8d1"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/570c4f0188ce342ec69afaa244661fdb4faca8d1", "type": "zip", "shasum": "", "reference": "570c4f0188ce342ec69afaa244661fdb4faca8d1"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.2.0"}, "time": "2022-01-05T02:58:46+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "7a8c6e56ab3ffcc538d05e8155bb42269abf1a0c"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/7a8c6e56ab3ffcc538d05e8155bb42269abf1a0c", "type": "zip", "shasum": "", "reference": "7a8c6e56ab3ffcc538d05e8155bb42269abf1a0c"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.1.0"}, "time": "2020-11-24T19:55:57+00:00"}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "48212cdc0a79051d50d7fc2f0645c5a321caf926"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/48212cdc0a79051d50d7fc2f0645c5a321caf926", "type": "zip", "shasum": "", "reference": "48212cdc0a79051d50d7fc2f0645c5a321caf926"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.0.2"}, "time": "2020-10-13T01:26:01+00:00", "require": {"php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.11|^0.12", "phpunit/phpunit": "^7.0|^8.0|^9.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "fa4e95ff5a7f1d62c3fbc05c32729b7f3ca14b52"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/fa4e95ff5a7f1d62c3fbc05c32729b7f3ca14b52", "type": "zip", "shasum": "", "reference": "fa4e95ff5a7f1d62c3fbc05c32729b7f3ca14b52"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/3.0.1"}, "time": "2020-08-21T02:30:13+00:00", "require": {"php": "^7.1"}, "require-dev": {"phpstan/phpstan": "^0.11", "phpunit/phpunit": "^6.4|^7.0"}}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "70c187e8d95c828fc93a2d09d63179c13381482e"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/70c187e8d95c828fc93a2d09d63179c13381482e", "type": "zip", "shasum": "", "reference": "70c187e8d95c828fc93a2d09d63179c13381482e"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.0.0"}, "time": "2020-03-25T18:33:19+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "type": "zip", "shasum": "", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v2.3.1"}, "time": "2020-10-13T00:52:37+00:00", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "replace": "__unset"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "72b6fbf76adb3cf5bc0db68559b33d41219aba27"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/72b6fbf76adb3cf5bc0db68559b33d41219aba27", "type": "zip", "shasum": "", "reference": "72b6fbf76adb3cf5bc0db68559b33d41219aba27"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v2.3.0"}, "time": "2019-03-31T00:38:28+00:00", "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0"}, "funding": "__unset"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "92a2c3768d50e21a1f26a53cb795ce72806266c5"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/92a2c3768d50e21a1f26a53cb795ce72806266c5", "type": "zip", "shasum": "", "reference": "92a2c3768d50e21a1f26a53cb795ce72806266c5"}, "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/master"}, "time": "2018-06-06T03:12:17+00:00", "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.4"}, "extra": "__unset"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "3f00985deec8df53d4cc1e5c33619bda1ee309a5"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/3f00985deec8df53d4cc1e5c33619bda1ee309a5", "type": "zip", "shasum": "", "reference": "3f00985deec8df53d4cc1e5c33619bda1ee309a5"}, "time": "2018-04-06T15:51:55+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "8a84aee649c3a3ba03a721c1fb080e08dfbcd68b"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8a84aee649c3a3ba03a721c1fb080e08dfbcd68b", "type": "zip", "shasum": "", "reference": "8a84aee649c3a3ba03a721c1fb080e08dfbcd68b"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v2.0.0"}, "time": "2017-10-12T15:59:13+00:00", "require-dev": {"phpunit/phpunit": "~5.7"}}, {"version": "v1.2.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "9504fa9ea681b586028adaaa0877db4aecf32bad"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/9504fa9ea681b586028adaaa0877db4aecf32bad", "type": "zip", "shasum": "", "reference": "9504fa9ea681b586028adaaa0877db4aecf32bad"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.2.0"}, "time": "2017-01-23T04:29:33+00:00", "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}}, {"version": "v1.2.0", "version_normalized": "*******"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "c9ee7886f5a12902b225a1a12f36bb45f9ab89e5"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/c9ee7886f5a12902b225a1a12f36bb45f9ab89e5", "type": "zip", "shasum": "", "reference": "c9ee7886f5a12902b225a1a12f36bb45f9ab89e5"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.1.0"}, "time": "2016-01-26T21:23:30+00:00", "autoload": {"psr-0": {"Cron": "src/"}}}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "fd92e883195e5dfa77720b1868cf084b08be4412"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/fd92e883195e5dfa77720b1868cf084b08be4412", "type": "zip", "shasum": "", "reference": "fd92e883195e5dfa77720b1868cf084b08be4412"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.0.4"}, "time": "2015-01-11T23:07:46+00:00", "require-dev": {"phpunit/phpunit": "4.*"}}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "a47ac8d5ec15049013792401af5a4d13e3dbe7f6"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/a47ac8d5ec15049013792401af5a4d13e3dbe7f6", "type": "zip", "shasum": "", "reference": "a47ac8d5ec15049013792401af5a4d13e3dbe7f6"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.0.3"}, "time": "2013-11-23T19:48:39+00:00", "require-dev": "__unset"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "5718f64bf7e30d1d3f52531cef8f4ec7d76abbdd"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/5718f64bf7e30d1d3f52531cef8f4ec7d76abbdd", "type": "zip", "shasum": "", "reference": "5718f64bf7e30d1d3f52531cef8f4ec7d76abbdd"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.0.2"}, "time": "2013-04-05T18:55:56+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "a490be18aff4695446ee5091c4847540c64ca9ef"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/a490be18aff4695446ee5091c4847540c64ca9ef", "type": "zip", "shasum": "", "reference": "a490be18aff4695446ee5091c4847540c64ca9ef"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.0.1"}, "time": "2013-03-06T02:49:08+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dragonmantank/cron-expression.git", "type": "git", "reference": "5a027522ef35bdc6996144fb9123472a5746bc02"}, "dist": {"url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/5a027522ef35bdc6996144fb9123472a5746bc02", "type": "zip", "shasum": "", "reference": "5a027522ef35bdc6996144fb9123472a5746bc02"}, "support": {"source": "https://github.com/dragonmantank/cron-expression/tree/v1.0.0"}, "time": "2012-07-15T21:19:48+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 09 Oct 2024 13:52:12 GMT"}