{"minified": "composer/2.0", "packages": {"nesbot/carbon": [{"name": "nesbot/carbon", "description": "An API extension for DateTime that supports 281 different languages.", "keywords": ["time", "date", "datetime"], "homepage": "https://carbon.nesbot.com", "version": "3.10.1", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1fd1935b2d90aef2f093c5e35f7ae1257c448d00"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1fd1935b2d90aef2f093c5e35f7ae1257c448d00", "type": "zip", "shasum": "", "reference": "1fd1935b2d90aef2f093c5e35f7ae1257c448d00"}, "type": "library", "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-06-21T15:19:35+00:00", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "bin": ["bin/carbon"], "require": {"php": "^8.1", "ext-json": "*", "carbonphp/carbon-doctrine-types": "<100.0", "psr/clock": "^1.0", "symfony/clock": "^6.3.12 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1 || ^6.0 || ^7.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.75.0", "kylekatarnls/multi-tester": "^2.5.3", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.17", "phpunit/phpunit": "^10.5.46", "squizlabs/php_codesniffer": "^3.13.0"}, "provide": {"psr/clock-implementation": "1.0"}}, {"version": "3.10.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c1397390dd0a7e0f11660f0ae20f753d88c1f3d9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c1397390dd0a7e0f11660f0ae20f753d88c1f3d9", "type": "zip", "shasum": "", "reference": "c1397390dd0a7e0f11660f0ae20f753d88c1f3d9"}, "time": "2025-06-12T10:24:28+00:00"}, {"version": "3.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ced71f79398ece168e24f7f7710462f462310d4d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ced71f79398ece168e24f7f7710462f462310d4d", "type": "zip", "shasum": "", "reference": "ced71f79398ece168e24f7f7710462f462310d4d"}, "time": "2025-05-01T19:51:51+00:00", "require": {"php": "^8.1", "ext-json": "*", "carbonphp/carbon-doctrine-types": "<100.0", "psr/clock": "^1.0", "symfony/clock": "^6.3 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.57.2", "kylekatarnls/multi-tester": "^2.5.3", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.11.2", "phpunit/phpunit": "^10.5.20", "squizlabs/php_codesniffer": "^3.9.0"}}, {"version": "3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "6d16a8a015166fe54e22c042e0805c5363aef50d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6d16a8a015166fe54e22c042e0805c5363aef50d", "type": "zip", "shasum": "", "reference": "6d16a8a015166fe54e22c042e0805c5363aef50d"}, "time": "2025-03-27T12:57:33+00:00"}, {"version": "3.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ff2f20cf83bd4d503720632ce8a426dc747bf7fd"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ff2f20cf83bd4d503720632ce8a426dc747bf7fd", "type": "zip", "shasum": "", "reference": "ff2f20cf83bd4d503720632ce8a426dc747bf7fd"}, "time": "2025-02-20T17:33:38+00:00"}, {"version": "3.8.5", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b1a53a27898639579a67de42e8ced5d5386aa9a4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b1a53a27898639579a67de42e8ced5d5386aa9a4", "type": "zip", "shasum": "", "reference": "b1a53a27898639579a67de42e8ced5d5386aa9a4"}, "time": "2025-02-11T16:28:45+00:00"}, {"version": "3.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "129700ed449b1f02d70272d2ac802357c8c30c58"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/129700ed449b1f02d70272d2ac802357c8c30c58", "type": "zip", "shasum": "", "reference": "129700ed449b1f02d70272d2ac802357c8c30c58"}, "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "time": "2024-12-27T09:25:35+00:00"}, {"version": "3.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f01cfa96468f4c38325f507ab81a4f1d2cd93cfe"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f01cfa96468f4c38325f507ab81a4f1d2cd93cfe", "type": "zip", "shasum": "", "reference": "f01cfa96468f4c38325f507ab81a4f1d2cd93cfe"}, "time": "2024-12-21T18:03:19+00:00"}, {"version": "3.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e1268cdbc486d97ce23fef2c666dc3c6b6de9947"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e1268cdbc486d97ce23fef2c666dc3c6b6de9947", "type": "zip", "shasum": "", "reference": "e1268cdbc486d97ce23fef2c666dc3c6b6de9947"}, "time": "2024-11-07T17:46:48+00:00"}, {"version": "3.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "10ac0aa86b8062219ce21e8189123d611ca3ecd9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/10ac0aa86b8062219ce21e8189123d611ca3ecd9", "type": "zip", "shasum": "", "reference": "10ac0aa86b8062219ce21e8189123d611ca3ecd9"}, "time": "2024-11-03T16:02:24+00:00"}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bbd3eef89af8ba66a3aa7952b5439168fbcc529f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bbd3eef89af8ba66a3aa7952b5439168fbcc529f", "type": "zip", "shasum": "", "reference": "bbd3eef89af8ba66a3aa7952b5439168fbcc529f"}, "time": "2024-08-19T06:22:39+00:00", "require": {"php": "^8.1", "ext-json": "*", "carbonphp/carbon-doctrine-types": "*", "psr/clock": "^1.0", "symfony/clock": "^6.3 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"}}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "cb4374784c87d0a0294e8513a52eb63c0aff3139"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/cb4374784c87d0a0294e8513a52eb63c0aff3139", "type": "zip", "shasum": "", "reference": "cb4374784c87d0a0294e8513a52eb63c0aff3139"}, "time": "2024-07-16T22:29:20+00:00"}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "39c8ef752db6865717cc3fba63970c16f057982c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/39c8ef752db6865717cc3fba63970c16f057982c", "type": "zip", "shasum": "", "reference": "39c8ef752db6865717cc3fba63970c16f057982c"}, "time": "2024-06-20T15:52:59+00:00"}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "415782b7e48223342f1a616c16c45a95b15b2318"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/415782b7e48223342f1a616c16c45a95b15b2318", "type": "zip", "shasum": "", "reference": "415782b7e48223342f1a616c16c45a95b15b2318"}, "time": "2024-06-03T17:25:54+00:00"}, {"version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8eab8983c83c30e0bacbef8d311e3f3b8172727f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8eab8983c83c30e0bacbef8d311e3f3b8172727f", "type": "zip", "shasum": "", "reference": "8eab8983c83c30e0bacbef8d311e3f3b8172727f"}, "time": "2024-05-24T14:26:34+00:00", "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.52.1", "kylekatarnls/multi-tester": "^2.5.3", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.10.65", "phpunit/phpunit": "^10.5.15", "squizlabs/php_codesniffer": "^3.9.0"}}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8ff64b92c1b1ec84fcde9f8bb9ff2ca34cb8a77a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8ff64b92c1b1ec84fcde9f8bb9ff2ca34cb8a77a", "type": "zip", "shasum": "", "reference": "8ff64b92c1b1ec84fcde9f8bb9ff2ca34cb8a77a"}, "time": "2024-05-01T06:54:22+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7219739c4e01d4680c980545821733b6ed8ee880"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7219739c4e01d4680c980545821733b6ed8ee880", "type": "zip", "shasum": "", "reference": "7219739c4e01d4680c980545821733b6ed8ee880"}, "time": "2024-04-18T16:35:06+00:00"}, {"version": "3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "82c28278c1c8f7b82dcdab25692237f052ffc8d8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/82c28278c1c8f7b82dcdab25692237f052ffc8d8", "type": "zip", "shasum": "", "reference": "82c28278c1c8f7b82dcdab25692237f052ffc8d8"}, "time": "2024-04-05T09:58:10+00:00"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4d599a6e2351d6b6bf21737accdfe1a4ce3fdbb1"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4d599a6e2351d6b6bf21737accdfe1a4ce3fdbb1", "type": "zip", "shasum": "", "reference": "4d599a6e2351d6b6bf21737accdfe1a4ce3fdbb1"}, "time": "2024-03-30T18:22:00+00:00"}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "2d69b6de67e2a3c0652d0c9dfcfda8b4563c4cee"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/2d69b6de67e2a3c0652d0c9dfcfda8b4563c4cee", "type": "zip", "shasum": "", "reference": "2d69b6de67e2a3c0652d0c9dfcfda8b4563c4cee"}, "time": "2024-03-28T12:59:49+00:00"}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b4272c2e78d30f9085b079aedb692b2da879b313"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b4272c2e78d30f9085b079aedb692b2da879b313", "type": "zip", "shasum": "", "reference": "b4272c2e78d30f9085b079aedb692b2da879b313"}, "time": "2024-03-27T21:37:24+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "6bfea14bb0792e85f2e1ff0919487c88419c5883"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6bfea14bb0792e85f2e1ff0919487c88419c5883", "type": "zip", "shasum": "", "reference": "6bfea14bb0792e85f2e1ff0919487c88419c5883"}, "time": "2024-03-27T18:14:36+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "34ccf6f6b49c915421c7886c88c0cb77f3ebbfd2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/34ccf6f6b49c915421c7886c88c0cb77f3ebbfd2", "type": "zip", "shasum": "", "reference": "34ccf6f6b49c915421c7886c88c0cb77f3ebbfd2"}, "time": "2024-03-13T12:42:37+00:00", "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.18.0", "kylekatarnls/multi-tester": "^2.2.0", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.13.0", "phpstan/extension-installer": "^1.3.0", "phpstan/phpstan": "^1.10.20", "phpunit/phpunit": "^10.2.2", "squizlabs/php_codesniffer": "^3.7.2"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "237716a15fcd4f25e26b80bfa3bf3d7a2827a3b1"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/237716a15fcd4f25e26b80bfa3bf3d7a2827a3b1", "type": "zip", "shasum": "", "reference": "237716a15fcd4f25e26b80bfa3bf3d7a2827a3b1"}, "time": "2024-03-06T17:26:51+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "cf30cfceac9693bdb339ffb51f091e6039bdf10d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/cf30cfceac9693bdb339ffb51f091e6039bdf10d", "type": "zip", "shasum": "", "reference": "cf30cfceac9693bdb339ffb51f091e6039bdf10d"}, "time": "2024-02-06T09:28:31+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "975ce92d55067cacb7d2ddbf43acfb1d220083ed"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/975ce92d55067cacb7d2ddbf43acfb1d220083ed", "type": "zip", "shasum": "", "reference": "975ce92d55067cacb7d2ddbf43acfb1d220083ed"}, "time": "2024-02-05T18:22:18+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7ae1c6fd10d2a93a0a0f5ab8fc8be37cb0830dec"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7ae1c6fd10d2a93a0a0f5ab8fc8be37cb0830dec", "type": "zip", "shasum": "", "reference": "7ae1c6fd10d2a93a0a0f5ab8fc8be37cb0830dec"}, "time": "2024-01-31T20:20:32+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}}}, {"version": "3.0.0-rc.3", "version_normalized": "*******-RC3"}, {"version": "3.0.0-rc.1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8cde64b3230650c8d8fe50c9554880449ac37781"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8cde64b3230650c8d8fe50c9554880449ac37781", "type": "zip", "shasum": "", "reference": "8cde64b3230650c8d8fe50c9554880449ac37781"}, "time": "2024-01-26T22:34:54+00:00"}, {"version": "3.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4f83b0d7c4ea99e35e94e86e4f3f303aa32ab378"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4f83b0d7c4ea99e35e94e86e4f3f303aa32ab378", "type": "zip", "shasum": "", "reference": "4f83b0d7c4ea99e35e94e86e4f3f303aa32ab378"}, "time": "2024-01-24T09:02:00+00:00"}, {"version": "3.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e61a85b2d60dce5db49ba9a7e2157260fd529b34"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e61a85b2d60dce5db49ba9a7e2157260fd529b34", "type": "zip", "shasum": "", "reference": "e61a85b2d60dce5db49ba9a7e2157260fd529b34"}, "time": "2024-01-23T23:28:55+00:00"}, {"version": "3.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "616a9d6c2d19b407121bfff15025d25ec3399c95"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/616a9d6c2d19b407121bfff15025d25ec3399c95", "type": "zip", "shasum": "", "reference": "616a9d6c2d19b407121bfff15025d25ec3399c95"}, "time": "2024-01-22T07:05:06+00:00"}, {"version": "2.73.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9228ce90e1035ff2f0db84b40ec2e023ed802075", "type": "zip", "shasum": "", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "time": "2025-01-08T20:10:23+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "carbonphp/carbon-doctrine-types": "*", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.72.6", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1e9d50601e7035a4c61441a208cb5bed73e108c5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1e9d50601e7035a4c61441a208cb5bed73e108c5", "type": "zip", "shasum": "", "reference": "1e9d50601e7035a4c61441a208cb5bed73e108c5"}, "time": "2024-12-27T09:28:11+00:00"}, {"version": "2.72.5", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/afd46589c216118ecd48ff2b95d77596af1e57ed", "type": "zip", "shasum": "", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed"}, "time": "2024-06-03T19:18:41+00:00", "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.72.4", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "117671bd1a44c819b941dcd152bd0268466464e0"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/117671bd1a44c819b941dcd152bd0268466464e0", "type": "zip", "shasum": "", "reference": "117671bd1a44c819b941dcd152bd0268466464e0"}, "time": "2024-06-03T15:00:23+00:00"}, {"version": "2.72.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "0c6fd108360c562f6e4fd1dedb8233b423e91c83"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/0c6fd108360c562f6e4fd1dedb8233b423e91c83", "type": "zip", "shasum": "", "reference": "0c6fd108360c562f6e4fd1dedb8233b423e91c83"}, "time": "2024-01-25T10:35:09+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}}}, {"version": "2.72.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3e7edc41b58d65509baeb0d4a14c8fa41d627130"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3e7edc41b58d65509baeb0d4a14c8fa41d627130", "type": "zip", "shasum": "", "reference": "3e7edc41b58d65509baeb0d4a14c8fa41d627130"}, "time": "2024-01-19T00:21:53+00:00"}, {"version": "2.72.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "2b3b3db0a2d0556a177392ff1a3bf5608fa09f78"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/2b3b3db0a2d0556a177392ff1a3bf5608fa09f78", "type": "zip", "shasum": "", "reference": "2b3b3db0a2d0556a177392ff1a3bf5608fa09f78"}, "time": "2023-12-08T23:47:49+00:00"}, {"version": "2.72.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a6885fcbad2ec4360b0e200ee0da7d9b7c90786b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a6885fcbad2ec4360b0e200ee0da7d9b7c90786b", "type": "zip", "shasum": "", "reference": "a6885fcbad2ec4360b0e200ee0da7d9b7c90786b"}, "time": "2023-11-28T10:13:25+00:00"}, {"version": "2.71.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "98276233188583f2ff845a0f992a235472d9466a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/98276233188583f2ff845a0f992a235472d9466a", "type": "zip", "shasum": "", "reference": "98276233188583f2ff845a0f992a235472d9466a"}, "time": "2023-09-25T11:31:05+00:00", "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.70.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d3298b38ea8612e5f77d38d1a99438e42f70341d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d3298b38ea8612e5f77d38d1a99438e42f70341d", "type": "zip", "shasum": "", "reference": "d3298b38ea8612e5f77d38d1a99438e42f70341d"}, "time": "2023-09-07T16:43:50+00:00"}, {"version": "2.69.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4308217830e4ca445583a37d1bf4aff4153fa81c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4308217830e4ca445583a37d1bf4aff4153fa81c", "type": "zip", "shasum": "", "reference": "4308217830e4ca445583a37d1bf4aff4153fa81c"}, "time": "2023-08-03T09:00:52+00:00"}, {"version": "2.68.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4f991ed2a403c85efbc4f23eb4030063fdbe01da"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4f991ed2a403c85efbc4f23eb4030063fdbe01da", "type": "zip", "shasum": "", "reference": "4f991ed2a403c85efbc4f23eb4030063fdbe01da"}, "time": "2023-06-20T18:29:04+00:00", "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": "__unset"}, {"version": "2.68.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ae0ae7c27b27e493270e6f2980eea764f8b3469e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ae0ae7c27b27e493270e6f2980eea764f8b3469e", "type": "zip", "shasum": "", "reference": "ae0ae7c27b27e493270e6f2980eea764f8b3469e"}, "time": "2023-06-15T15:10:15+00:00"}, {"version": "2.67.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c1001b3bc75039b07f38a79db5237c4c529e04c8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c1001b3bc75039b07f38a79db5237c4c529e04c8", "type": "zip", "shasum": "", "reference": "c1001b3bc75039b07f38a79db5237c4c529e04c8"}, "time": "2023-05-25T22:09:47+00:00"}, {"version": "2.66.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "496712849902241f04902033b0441b269effe001"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/496712849902241f04902033b0441b269effe001", "type": "zip", "shasum": "", "reference": "496712849902241f04902033b0441b269effe001"}, "time": "2023-01-29T18:53:47+00:00"}, {"version": "2.65.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "09acf64155c16dc6f580f36569ae89344e9734a3"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/09acf64155c16dc6f580f36569ae89344e9734a3", "type": "zip", "shasum": "", "reference": "09acf64155c16dc6f580f36569ae89344e9734a3"}, "time": "2023-01-06T15:55:01+00:00"}, {"version": "2.64.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f2e59963f4c4f4fdfb9fcfd752e8d2e2b79a4e2c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f2e59963f4c4f4fdfb9fcfd752e8d2e2b79a4e2c", "type": "zip", "shasum": "", "reference": "f2e59963f4c4f4fdfb9fcfd752e8d2e2b79a4e2c"}, "time": "2023-01-01T23:17:36+00:00"}, {"version": "2.64.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "889546413c97de2d05063b8cb7b193c2531ea211"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/889546413c97de2d05063b8cb7b193c2531ea211", "type": "zip", "shasum": "", "reference": "889546413c97de2d05063b8cb7b193c2531ea211"}, "time": "2022-11-26T17:36:00+00:00"}, {"version": "2.63.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ad35dd71a6a212b98e4b87e97389b6fa85f0e347"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ad35dd71a6a212b98e4b87e97389b6fa85f0e347", "type": "zip", "shasum": "", "reference": "ad35dd71a6a212b98e4b87e97389b6fa85f0e347"}, "time": "2022-10-30T18:34:28+00:00", "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.62.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "01bc4cdefe98ef58d1f9cb31bdbbddddf2a88f7a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/01bc4cdefe98ef58d1f9cb31bdbbddddf2a88f7a", "type": "zip", "shasum": "", "reference": "01bc4cdefe98ef58d1f9cb31bdbbddddf2a88f7a"}, "time": "2022-09-02T07:48:13+00:00"}, {"version": "2.62.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7507aec3d626797ce2123cf6c6556683be22b5f8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7507aec3d626797ce2123cf6c6556683be22b5f8", "type": "zip", "shasum": "", "reference": "7507aec3d626797ce2123cf6c6556683be22b5f8"}, "time": "2022-08-28T19:48:05+00:00"}, {"version": "2.61.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bdf4f4fe3a3eac4de84dbec0738082a862c68ba6"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bdf4f4fe3a3eac4de84dbec0738082a862c68ba6", "type": "zip", "shasum": "", "reference": "bdf4f4fe3a3eac4de84dbec0738082a862c68ba6"}, "time": "2022-08-06T12:41:24+00:00"}, {"version": "2.60.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "00a259ae02b003c563158b54fb6743252b638ea6"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/00a259ae02b003c563158b54fb6743252b638ea6", "type": "zip", "shasum": "", "reference": "00a259ae02b003c563158b54fb6743252b638ea6"}, "time": "2022-07-27T15:57:48+00:00"}, {"version": "2.59.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a9000603ea337c8df16cc41f8b6be95a65f4d0f5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a9000603ea337c8df16cc41f8b6be95a65f4d0f5", "type": "zip", "shasum": "", "reference": "a9000603ea337c8df16cc41f8b6be95a65f4d0f5"}, "time": "2022-06-29T21:43:55+00:00"}, {"version": "2.59.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9cea2f72585a46d0651ee6d4c8c8f752e78c16c6"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9cea2f72585a46d0651ee6d4c8c8f752e78c16c6", "type": "zip", "shasum": "", "reference": "9cea2f72585a46d0651ee6d4c8c8f752e78c16c6"}, "time": "2022-06-26T20:26:59+00:00"}, {"version": "2.58.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "97a34af22bde8d0ac20ab34b29d7bfe360902055"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/97a34af22bde8d0ac20ab34b29d7bfe360902055", "type": "zip", "shasum": "", "reference": "97a34af22bde8d0ac20ab34b29d7bfe360902055"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2022-04-25T19:31:17+00:00", "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54 || ^1.0", "phpunit/php-file-iterator": "^2.0.5", "phpunit/phpunit": "^7.5.20 || ^8.5.23", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.57.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4a54375c21eea4811dbd1149fe6b246517554e78"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4a54375c21eea4811dbd1149fe6b246517554e78", "type": "zip", "shasum": "", "reference": "4a54375c21eea4811dbd1149fe6b246517554e78"}, "time": "2022-02-13T18:13:33+00:00", "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54 || ^1.0", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.56.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "626ec8cbb724cd3c3400c3ed8f730545b744e3f4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/626ec8cbb724cd3c3400c3ed8f730545b744e3f4", "type": "zip", "shasum": "", "reference": "626ec8cbb724cd3c3400c3ed8f730545b744e3f4"}, "time": "2022-01-21T17:08:38+00:00"}, {"version": "2.55.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8c2a18ce3e67c34efc1b29f64fe61304368259a2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8c2a18ce3e67c34efc1b29f64fe61304368259a2", "type": "zip", "shasum": "", "reference": "8c2a18ce3e67c34efc1b29f64fe61304368259a2"}, "time": "2021-12-03T14:59:52+00:00", "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.55.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "67f34dc7a8a97fcb9b26e60ec3819b856764d60f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/67f34dc7a8a97fcb9b26e60ec3819b856764d60f", "type": "zip", "shasum": "", "reference": "67f34dc7a8a97fcb9b26e60ec3819b856764d60f"}, "time": "2021-12-03T13:47:17+00:00"}, {"version": "2.55.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b46249911cba301e9eb3f05af68cf2aacc1f9d05"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b46249911cba301e9eb3f05af68cf2aacc1f9d05", "type": "zip", "shasum": "", "reference": "b46249911cba301e9eb3f05af68cf2aacc1f9d05"}, "time": "2021-12-02T16:53:55+00:00"}, {"version": "2.54.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "eed83939f1aed3eee517d03a33f5ec587ac529b5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/eed83939f1aed3eee517d03a33f5ec587ac529b5", "type": "zip", "shasum": "", "reference": "eed83939f1aed3eee517d03a33f5ec587ac529b5"}, "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "time": "2021-11-01T21:22:20+00:00", "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "doctrine/dbal": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.53.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f4655858a784988f880c1b8c7feabbf02dfdf045"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f4655858a784988f880c1b8c7feabbf02dfdf045", "type": "zip", "shasum": "", "reference": "f4655858a784988f880c1b8c7feabbf02dfdf045"}, "time": "2021-09-06T09:29:23+00:00", "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.53.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "875f62a876f476d8a6040381e4b2673b3d4a28fe"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/875f62a876f476d8a6040381e4b2673b3d4a28fe", "type": "zip", "shasum": "", "reference": "875f62a876f476d8a6040381e4b2673b3d4a28fe"}, "time": "2021-09-06T07:30:54+00:00"}, {"version": "2.52.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "369c0e2737c56a0f39c946dd261855255a6fccbe"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/369c0e2737c56a0f39c946dd261855255a6fccbe", "type": "zip", "shasum": "", "reference": "369c0e2737c56a0f39c946dd261855255a6fccbe"}, "time": "2021-08-14T19:10:52+00:00", "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.51.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8619c299d1e0d4b344e1f98ca07a1ce2cfbf1922"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8619c299d1e0d4b344e1f98ca07a1ce2cfbf1922", "type": "zip", "shasum": "", "reference": "8619c299d1e0d4b344e1f98ca07a1ce2cfbf1922"}, "time": "2021-07-28T13:16:28+00:00"}, {"version": "2.51.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "48ebff252df4d3b60ecb8e2583f79dfba6b56bc8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/48ebff252df4d3b60ecb8e2583f79dfba6b56bc8", "type": "zip", "shasum": "", "reference": "48ebff252df4d3b60ecb8e2583f79dfba6b56bc8"}, "time": "2021-07-28T08:28:41+00:00"}, {"version": "2.50.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f47f17d17602b2243414a44ad53d9f8b9ada5fdb"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f47f17d17602b2243414a44ad53d9f8b9ada5fdb", "type": "zip", "shasum": "", "reference": "f47f17d17602b2243414a44ad53d9f8b9ada5fdb"}, "time": "2021-06-28T22:38:45+00:00", "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}}, {"homepage": "http://carbon.nesbot.com", "version": "2.49.0", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "93d9db91c0235c486875d22f1e08b50bdf3e6eee"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/93d9db91c0235c486875d22f1e08b50bdf3e6eee", "type": "zip", "shasum": "", "reference": "93d9db91c0235c486875d22f1e08b50bdf3e6eee"}, "time": "2021-06-02T07:31:40+00:00"}, {"version": "2.48.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8d1f50f1436fb4b05e7127360483dd9c6e73da16"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8d1f50f1436fb4b05e7127360483dd9c6e73da16", "type": "zip", "shasum": "", "reference": "8d1f50f1436fb4b05e7127360483dd9c6e73da16"}, "time": "2021-05-26T22:08:38+00:00"}, {"version": "2.48.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d3c447f21072766cddec3522f9468a5849a76147"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d3c447f21072766cddec3522f9468a5849a76147", "type": "zip", "shasum": "", "reference": "d3c447f21072766cddec3522f9468a5849a76147"}, "time": "2021-05-07T10:08:30+00:00"}, {"version": "2.47.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "902fc35edb0e54ee42befa4a73451fa5ab82f0dc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/902fc35edb0e54ee42befa4a73451fa5ab82f0dc", "type": "zip", "shasum": "", "reference": "902fc35edb0e54ee42befa4a73451fa5ab82f0dc"}, "time": "2021-05-26T22:09:12+00:00"}, {"version": "2.47.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "606262fd8888b75317ba9461825a24fc34001e1e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/606262fd8888b75317ba9461825a24fc34001e1e", "type": "zip", "shasum": "", "reference": "606262fd8888b75317ba9461825a24fc34001e1e"}, "time": "2021-04-13T21:54:02+00:00"}, {"version": "2.46.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "2fd2c4a77d58a4e95234c8a61c5df1f157a91bf4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/2fd2c4a77d58a4e95234c8a61c5df1f157a91bf4", "type": "zip", "shasum": "", "reference": "2fd2c4a77d58a4e95234c8a61c5df1f157a91bf4"}, "time": "2021-02-24T17:30:44+00:00"}, {"version": "2.45.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "528783b188bdb853eb21239b1722831e0f000a8d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/528783b188bdb853eb21239b1722831e0f000a8d", "type": "zip", "shasum": "", "reference": "528783b188bdb853eb21239b1722831e0f000a8d"}, "time": "2021-02-11T18:30:17+00:00"}, {"version": "2.45.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e2ba3174ce869da1713c38340dbb36572dfacd5a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e2ba3174ce869da1713c38340dbb36572dfacd5a", "type": "zip", "shasum": "", "reference": "e2ba3174ce869da1713c38340dbb36572dfacd5a"}, "time": "2021-02-07T21:35:59+00:00"}, {"version": "2.44.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e6ef33cb1f67a4bed831ed6d0f7e156739a5d8cd"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e6ef33cb1f67a4bed831ed6d0f7e156739a5d8cd", "type": "zip", "shasum": "", "reference": "e6ef33cb1f67a4bed831ed6d0f7e156739a5d8cd"}, "time": "2021-01-26T20:46:41+00:00"}, {"version": "2.43.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d32c57d8389113742f4a88725a170236470012e2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d32c57d8389113742f4a88725a170236470012e2", "type": "zip", "shasum": "", "reference": "d32c57d8389113742f4a88725a170236470012e2"}, "time": "2020-12-17T20:55:32+00:00", "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.42.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d0463779663437392fe42ff339ebc0213bd55498"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d0463779663437392fe42ff339ebc0213bd55498", "type": "zip", "shasum": "", "reference": "d0463779663437392fe42ff339ebc0213bd55498"}, "time": "2020-11-28T14:25:28+00:00"}, {"version": "2.41.5", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c4a9caf97cfc53adfc219043bcecf42bc663acee"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c4a9caf97cfc53adfc219043bcecf42bc663acee", "type": "zip", "shasum": "", "reference": "c4a9caf97cfc53adfc219043bcecf42bc663acee"}, "time": "2020-10-23T06:02:30+00:00", "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.35", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.41.4", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "6571aec754a648ef476a8d8f57993f7bc965afe4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6571aec754a648ef476a8d8f57993f7bc965afe4", "type": "zip", "shasum": "", "reference": "6571aec754a648ef476a8d8f57993f7bc965afe4"}, "time": "2020-10-22T07:28:05+00:00"}, {"version": "2.41.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e148788eeae9b9b7b87996520358b86faad37b52"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e148788eeae9b9b7b87996520358b86faad37b52", "type": "zip", "shasum": "", "reference": "e148788eeae9b9b7b87996520358b86faad37b52"}, "time": "2020-10-12T20:36:09+00:00"}, {"version": "2.41.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "35959c93ada06469107a05df6b15b65074a960cf"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/35959c93ada06469107a05df6b15b65074a960cf", "type": "zip", "shasum": "", "reference": "35959c93ada06469107a05df6b15b65074a960cf"}, "time": "2020-10-10T23:35:06+00:00"}, {"version": "2.41.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "91d8d8dca31936a137213d7c48f8ac981e501ff4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/91d8d8dca31936a137213d7c48f8ac981e501ff4", "type": "zip", "shasum": "", "reference": "91d8d8dca31936a137213d7c48f8ac981e501ff4"}, "time": "2020-10-10T11:06:56+00:00"}, {"version": "2.41.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8690b13ad4da6d54d692afea15aab30b36fee52e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8690b13ad4da6d54d692afea15aab30b36fee52e", "type": "zip", "shasum": "", "reference": "8690b13ad4da6d54d692afea15aab30b36fee52e"}, "time": "2020-10-04T09:11:05+00:00"}, {"version": "2.40.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d9a76d8b7eb0f97cf3a82529393245212f40ba3b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d9a76d8b7eb0f97cf3a82529393245212f40ba3b", "type": "zip", "shasum": "", "reference": "d9a76d8b7eb0f97cf3a82529393245212f40ba3b"}, "time": "2020-09-23T08:17:37+00:00"}, {"version": "2.40.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "6c7646154181013ecd55e80c201b9fd873c6ee5d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6c7646154181013ecd55e80c201b9fd873c6ee5d", "type": "zip", "shasum": "", "reference": "6c7646154181013ecd55e80c201b9fd873c6ee5d"}, "time": "2020-09-11T19:00:58+00:00"}, {"version": "2.39.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "326efde1bc09077a26cb77f6e2e32e13f06c27f2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/326efde1bc09077a26cb77f6e2e32e13f06c27f2", "type": "zip", "shasum": "", "reference": "326efde1bc09077a26cb77f6e2e32e13f06c27f2"}, "time": "2020-09-10T12:16:42+00:00"}, {"version": "2.39.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7af467873250583cc967a59ee9df29fabab193c1"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7af467873250583cc967a59ee9df29fabab193c1", "type": "zip", "shasum": "", "reference": "7af467873250583cc967a59ee9df29fabab193c1"}, "time": "2020-09-04T13:11:37+00:00"}, {"version": "2.39.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "0a41ea7f7fedacf307b7a339800e10356a042918"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/0a41ea7f7fedacf307b7a339800e10356a042918", "type": "zip", "shasum": "", "reference": "0a41ea7f7fedacf307b7a339800e10356a042918"}, "time": "2020-08-24T12:35:58+00:00", "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.8", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.35", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.38.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d8f6a6a91d1eb9304527b040500f61923e97674b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d8f6a6a91d1eb9304527b040500f61923e97674b", "type": "zip", "shasum": "", "reference": "d8f6a6a91d1eb9304527b040500f61923e97674b"}, "time": "2020-08-04T19:12:46+00:00"}, {"version": "2.37.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1f61206de973d67f36ce50f041c792ddac663c3e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1f61206de973d67f36ce50f041c792ddac663c3e", "type": "zip", "shasum": "", "reference": "1f61206de973d67f36ce50f041c792ddac663c3e"}, "time": "2020-07-28T06:04:54+00:00", "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.8", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.30", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.36.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ee7378a36cc62952100e718bcc58be4c7210e55f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ee7378a36cc62952100e718bcc58be4c7210e55f", "type": "zip", "shasum": "", "reference": "ee7378a36cc62952100e718bcc58be4c7210e55f"}, "time": "2020-07-04T12:29:56+00:00"}, {"version": "2.36.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d0b65958d9942fd1b501fdb0800c67e8323aa08d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d0b65958d9942fd1b501fdb0800c67e8323aa08d", "type": "zip", "shasum": "", "reference": "d0b65958d9942fd1b501fdb0800c67e8323aa08d"}, "time": "2020-06-25T20:20:01+00:00"}, {"version": "2.35.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4b9bd835261ef23d36397a46a76b496a458305e5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4b9bd835261ef23d36397a46a76b496a458305e5", "type": "zip", "shasum": "", "reference": "4b9bd835261ef23d36397a46a76b496a458305e5"}, "time": "2020-05-24T18:27:52+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "^2.8", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.34.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3e87404329b8072295ea11d548b47a1eefe5a162"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3e87404329b8072295ea11d548b47a1eefe5a162", "type": "zip", "shasum": "", "reference": "3e87404329b8072295ea11d548b47a1eefe5a162"}, "time": "2020-05-19T22:14:16+00:00"}, {"version": "2.34.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7e89e9b4cffb49670a29e7ed1eac97c2cbc7485b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7e89e9b4cffb49670a29e7ed1eac97c2cbc7485b", "type": "zip", "shasum": "", "reference": "7e89e9b4cffb49670a29e7ed1eac97c2cbc7485b"}, "time": "2020-05-19T21:15:11+00:00"}, {"version": "2.34.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "52ea68aebbad8a3b27b5d24e4c66ebe1933f8399"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/52ea68aebbad8a3b27b5d24e4c66ebe1933f8399", "type": "zip", "shasum": "", "reference": "52ea68aebbad8a3b27b5d24e4c66ebe1933f8399"}, "time": "2020-05-12T19:53:34+00:00"}, {"version": "2.33.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4d93cb95a80d9ffbff4018fe58ae3b7dd7f4b99b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4d93cb95a80d9ffbff4018fe58ae3b7dd7f4b99b", "type": "zip", "shasum": "", "reference": "4d93cb95a80d9ffbff4018fe58ae3b7dd7f4b99b"}, "time": "2020-04-20T15:05:43+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "branch-alias": {"dev-master": "2.x-dev"}}}, {"version": "2.32.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f10e22cf546704fab1db4ad4b9dedbc5c797a0dc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f10e22cf546704fab1db4ad4b9dedbc5c797a0dc", "type": "zip", "shasum": "", "reference": "f10e22cf546704fab1db4ad4b9dedbc5c797a0dc"}, "time": "2020-03-31T13:43:19+00:00", "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}}, {"version": "2.32.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ecb525c766deb3bbbb6a0082ea0e41d3d9ae477c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ecb525c766deb3bbbb6a0082ea0e41d3d9ae477c", "type": "zip", "shasum": "", "reference": "ecb525c766deb3bbbb6a0082ea0e41d3d9ae477c"}, "time": "2020-03-26T13:04:10+00:00"}, {"version": "2.32.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7410a349613bf32d02d8aaed1c669698ddd2b718"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7410a349613bf32d02d8aaed1c669698ddd2b718", "type": "zip", "shasum": "", "reference": "7410a349613bf32d02d8aaed1c669698ddd2b718"}, "time": "2020-03-24T16:01:47+00:00"}, {"version": "2.31.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bbc0ab53f41a4c6f223c18efcdbd9bc725eb5d2d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bbc0ab53f41a4c6f223c18efcdbd9bc725eb5d2d", "type": "zip", "shasum": "", "reference": "bbc0ab53f41a4c6f223c18efcdbd9bc725eb5d2d"}, "time": "2020-03-01T11:11:58+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "^2.8", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.30.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "912dff66d2690ca66abddb9b291a1df5f371d3b4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/912dff66d2690ca66abddb9b291a1df5f371d3b4", "type": "zip", "shasum": "", "reference": "912dff66d2690ca66abddb9b291a1df5f371d3b4"}, "time": "2020-02-07T15:25:46+00:00"}, {"version": "2.29.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e509be5bf2d703390e69e14496d9a1168452b0a2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e509be5bf2d703390e69e14496d9a1168452b0a2", "type": "zip", "shasum": "", "reference": "e509be5bf2d703390e69e14496d9a1168452b0a2"}, "time": "2020-01-21T09:36:43+00:00"}, {"version": "2.29.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "faf862506030dc48c061c840c7f50933f1df4ed8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/faf862506030dc48c061c840c7f50933f1df4ed8", "type": "zip", "shasum": "", "reference": "faf862506030dc48c061c840c7f50933f1df4ed8"}, "time": "2020-01-21T07:29:55+00:00"}, {"version": "2.28.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e2bcbcd43e67ee6101d321d5de916251d2870ca8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e2bcbcd43e67ee6101d321d5de916251d2870ca8", "type": "zip", "shasum": "", "reference": "e2bcbcd43e67ee6101d321d5de916251d2870ca8"}, "time": "2019-12-16T16:30:25+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "dev-php-7.1-compatibility", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.28.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "097c36f9e55343f6394418453e01a41b627eb5c7"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/097c36f9e55343f6394418453e01a41b627eb5c7", "type": "zip", "shasum": "", "reference": "097c36f9e55343f6394418453e01a41b627eb5c7"}, "time": "2019-12-12T22:14:18+00:00"}, {"version": "2.27.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "13b8485a8690f103bf19cba64879c218b102b726"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/13b8485a8690f103bf19cba64879c218b102b726", "type": "zip", "shasum": "", "reference": "13b8485a8690f103bf19cba64879c218b102b726"}, "time": "2019-11-20T06:59:06+00:00"}, {"version": "2.26.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e01ecc0b71168febb52ae1fdc1cfcc95428e604e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e01ecc0b71168febb52ae1fdc1cfcc95428e604e", "type": "zip", "shasum": "", "reference": "e01ecc0b71168febb52ae1fdc1cfcc95428e604e"}, "time": "2019-10-21T21:32:25+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "symfony/translation": "^3.4 || ^4.0"}}, {"version": "2.25.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d07636581795383e2fea2d711212d30f941f2039"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d07636581795383e2fea2d711212d30f941f2039", "type": "zip", "shasum": "", "reference": "d07636581795383e2fea2d711212d30f941f2039"}, "time": "2019-10-20T11:05:44+00:00"}, {"version": "2.25.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "443fe5f1498147e0fbc792142b5dc43e2e8a533f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/443fe5f1498147e0fbc792142b5dc43e2e8a533f", "type": "zip", "shasum": "", "reference": "443fe5f1498147e0fbc792142b5dc43e2e8a533f"}, "time": "2019-10-14T14:18:59+00:00"}, {"version": "2.25.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d59c6cea9c4a3547bb6c0dfec451319abdaa4fb1"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d59c6cea9c4a3547bb6c0dfec451319abdaa4fb1", "type": "zip", "shasum": "", "reference": "d59c6cea9c4a3547bb6c0dfec451319abdaa4fb1"}, "time": "2019-10-05T15:52:23+00:00"}, {"version": "2.25.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b70da677101cca7b584c7489770d2677c2733593"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b70da677101cca7b584c7489770d2677c2733593", "type": "zip", "shasum": "", "reference": "b70da677101cca7b584c7489770d2677c2733593"}, "time": "2019-09-30T16:22:22+00:00"}, {"version": "2.25.0-beta.3", "version_normalized": "********-beta3"}, {"version": "2.25.0-beta.2", "version_normalized": "********-beta2", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "380e7daf0daa68f87757e4e6dbb528ab61a5eb9c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/380e7daf0daa68f87757e4e6dbb528ab61a5eb9c", "type": "zip", "shasum": "", "reference": "380e7daf0daa68f87757e4e6dbb528ab61a5eb9c"}, "time": "2019-09-13T20:03:07+00:00"}, {"version": "2.25.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a333fc82bf1ebfc0f90e359fec7d2d4eac7f4a18"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a333fc82bf1ebfc0f90e359fec7d2d4eac7f4a18", "type": "zip", "shasum": "", "reference": "a333fc82bf1ebfc0f90e359fec7d2d4eac7f4a18"}, "time": "2019-09-09T15:56:37+00:00"}, {"description": "A API extension for DateTime that supports 281 different languages.", "version": "2.24.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "934459c5ac0658bc765ad1e53512c7c77adcac29"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/934459c5ac0658bc765ad1e53512c7c77adcac29", "type": "zip", "shasum": "", "reference": "934459c5ac0658bc765ad1e53512c7c77adcac29"}, "time": "2019-08-31T16:37:55+00:00"}, {"version": "2.24.0-beta.4", "version_normalized": "********-beta4"}, {"version": "2.24.0-beta.3", "version_normalized": "********-beta3", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "56d1789adc187e72ae95d14600e43a7313576e96"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/56d1789adc187e72ae95d14600e43a7313576e96", "type": "zip", "shasum": "", "reference": "56d1789adc187e72ae95d14600e43a7313576e96"}, "time": "2019-08-28T14:47:42+00:00"}, {"version": "2.24.0-beta.2", "version_normalized": "********-beta2", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c400dc08679651bd7c5b795224e96259e5de0447"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c400dc08679651bd7c5b795224e96259e5de0447", "type": "zip", "shasum": "", "reference": "c400dc08679651bd7c5b795224e96259e5de0447"}, "time": "2019-08-27T13:28:22+00:00"}, {"version": "2.24.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c4bf16abadc2a7df9f7231e59acda304f5170748"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c4bf16abadc2a7df9f7231e59acda304f5170748", "type": "zip", "shasum": "", "reference": "c4bf16abadc2a7df9f7231e59acda304f5170748"}, "time": "2019-08-23T06:11:49+00:00"}, {"version": "2.23.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "767617a047e5b8b8b3b0b6023a2650847ed7df02"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/767617a047e5b8b8b3b0b6023a2650847ed7df02", "type": "zip", "shasum": "", "reference": "767617a047e5b8b8b3b0b6023a2650847ed7df02"}, "time": "2019-08-17T13:57:34+00:00"}, {"version": "2.23.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "97a08830a22ce0b69549a4966773c0eae900468d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/97a08830a22ce0b69549a4966773c0eae900468d", "type": "zip", "shasum": "", "reference": "97a08830a22ce0b69549a4966773c0eae900468d"}, "time": "2019-08-12T17:19:41+00:00"}, {"version": "2.23.0-beta.4", "version_normalized": "********-beta4"}, {"version": "2.23.0-beta.3", "version_normalized": "********-beta3", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "2becd3bc19abd382a6ec6d15520055cfb33a16a2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/2becd3bc19abd382a6ec6d15520055cfb33a16a2", "type": "zip", "shasum": "", "reference": "2becd3bc19abd382a6ec6d15520055cfb33a16a2"}, "time": "2019-08-07T11:45:07+00:00"}, {"version": "2.23.0-beta.2", "version_normalized": "********-beta2", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3e11671493ee379e91a47b05e8f346bc11ddca86"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3e11671493ee379e91a47b05e8f346bc11ddca86", "type": "zip", "shasum": "", "reference": "3e11671493ee379e91a47b05e8f346bc11ddca86"}, "time": "2019-08-04T18:05:52+00:00"}, {"version": "2.23.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e02d0eda05b766b7ebab8ea16d52b8b4f206e202"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e02d0eda05b766b7ebab8ea16d52b8b4f206e202", "type": "zip", "shasum": "", "reference": "e02d0eda05b766b7ebab8ea16d52b8b4f206e202"}, "time": "2019-08-03T22:35:28+00:00"}, {"description": "A simple API extension for DateTime.", "version": "2.22.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "738fbd8d80b2c5e158fda76c29c2de432fcc6f7e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/738fbd8d80b2c5e158fda76c29c2de432fcc6f7e", "type": "zip", "shasum": "", "reference": "738fbd8d80b2c5e158fda76c29c2de432fcc6f7e"}, "time": "2019-08-07T12:36:44+00:00"}, {"version": "2.22.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ec355b933f9b635fbc418c3b351e3886686f5a21"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ec355b933f9b635fbc418c3b351e3886686f5a21", "type": "zip", "shasum": "", "reference": "ec355b933f9b635fbc418c3b351e3886686f5a21"}, "time": "2019-08-07T08:59:53+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "^2.6", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.22.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f572ec37cf37e192e851197a5867d041ab404785"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f572ec37cf37e192e851197a5867d041ab404785", "type": "zip", "shasum": "", "reference": "f572ec37cf37e192e851197a5867d041ab404785"}, "time": "2019-08-06T18:57:53+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "dev-php-7.1-compatibility", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.22.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1a0e48b5f656065ba3c265b058b25d36c2162a5e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1a0e48b5f656065ba3c265b058b25d36c2162a5e", "type": "zip", "shasum": "", "reference": "1a0e48b5f656065ba3c265b058b25d36c2162a5e"}, "time": "2019-07-28T09:02:12+00:00"}, {"version": "2.22.0-beta.3", "version_normalized": "********-beta3"}, {"version": "2.22.0-beta.2", "version_normalized": "********-beta2", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7870e278591629c0f5980d5cfaffb391c0287c5a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7870e278591629c0f5980d5cfaffb391c0287c5a", "type": "zip", "shasum": "", "reference": "7870e278591629c0f5980d5cfaffb391c0287c5a"}, "time": "2019-07-25T15:13:48+00:00"}, {"version": "2.22.0-beta.1", "version_normalized": "********-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ca54dafe95b03410ee738d1d5fdcefef46809890"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ca54dafe95b03410ee738d1d5fdcefef46809890", "type": "zip", "shasum": "", "reference": "ca54dafe95b03410ee738d1d5fdcefef46809890"}, "time": "2019-07-19T20:42:30+00:00"}, {"version": "2.21.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "58bdbbfab17ccd2ec7347b99e997f18232def4dc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/58bdbbfab17ccd2ec7347b99e997f18232def4dc", "type": "zip", "shasum": "", "reference": "58bdbbfab17ccd2ec7347b99e997f18232def4dc"}, "time": "2019-07-18T18:47:28+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "^2.6", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.21.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a8bfb6631e3bc982427406d13ad817fc45d47b61"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a8bfb6631e3bc982427406d13ad817fc45d47b61", "type": "zip", "shasum": "", "reference": "a8bfb6631e3bc982427406d13ad817fc45d47b61"}, "time": "2019-07-17T07:49:05+00:00"}, {"version": "2.21.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "833a9657afde50de2db0a3a4bde029c7239f6540"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/833a9657afde50de2db0a3a4bde029c7239f6540", "type": "zip", "shasum": "", "reference": "833a9657afde50de2db0a3a4bde029c7239f6540"}, "time": "2019-07-16T18:07:04+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "dev-php-7.1-compatibility", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.21.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "da6410b77f74eecd98d24ffb6dfecefccf3ca17f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/da6410b77f74eecd98d24ffb6dfecefccf3ca17f", "type": "zip", "shasum": "", "reference": "da6410b77f74eecd98d24ffb6dfecefccf3ca17f"}, "time": "2019-07-14T17:06:46+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "^2.6", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.20.0", "version_normalized": "********", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bc671b896c276795fad8426b0aa24e8ade0f2498"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bc671b896c276795fad8426b0aa24e8ade0f2498", "type": "zip", "shasum": "", "reference": "bc671b896c276795fad8426b0aa24e8ade0f2498"}, "time": "2019-06-25T10:00:57+00:00", "bin": "__unset"}, {"version": "2.19.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "adcad3f3af52d0ad4ad7b05f43aa58243b6ca67b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/adcad3f3af52d0ad4ad7b05f43aa58243b6ca67b", "type": "zip", "shasum": "", "reference": "adcad3f3af52d0ad4ad7b05f43aa58243b6ca67b"}, "time": "2019-06-07T09:56:45+00:00"}, {"version": "2.19.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5cf102d1476fdf92f1f991bd1a1a0d93c65755ca"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5cf102d1476fdf92f1f991bd1a1a0d93c65755ca", "type": "zip", "shasum": "", "reference": "5cf102d1476fdf92f1f991bd1a1a0d93c65755ca"}, "time": "2019-06-04T20:07:46+00:00"}, {"version": "2.19.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c3545b39197d314205226ac3c33cb4db99aa54ab"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c3545b39197d314205226ac3c33cb4db99aa54ab", "type": "zip", "shasum": "", "reference": "c3545b39197d314205226ac3c33cb4db99aa54ab"}, "time": "2019-05-30T10:15:05+00:00"}, {"version": "2.18.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8322b7bd1805be31867c13bf3cdaab948a0dd406"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8322b7bd1805be31867c13bf3cdaab948a0dd406", "type": "zip", "shasum": "", "reference": "8322b7bd1805be31867c13bf3cdaab948a0dd406"}, "time": "2019-05-16T18:44:35+00:00"}, {"version": "2.17.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "96acbc0c03782e8115156dd4dd8b736267155066"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/96acbc0c03782e8115156dd4dd8b736267155066", "type": "zip", "shasum": "", "reference": "96acbc0c03782e8115156dd4dd8b736267155066"}, "time": "2019-04-27T18:04:27+00:00"}, {"version": "2.17.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9b49d637ad009e5e211142bc7492adcb19dbd645"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9b49d637ad009e5e211142bc7492adcb19dbd645", "type": "zip", "shasum": "", "reference": "9b49d637ad009e5e211142bc7492adcb19dbd645"}, "time": "2019-04-17T08:51:36+00:00"}, {"version": "2.16.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "373d9f0d58651af366435148c39beb702c2b7ef4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/373d9f0d58651af366435148c39beb702c2b7ef4", "type": "zip", "shasum": "", "reference": "373d9f0d58651af366435148c39beb702c2b7ef4"}, "time": "2019-04-06T17:09:23+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^0.1", "phpmd/phpmd": "^2.6", "phpstan/phpstan": "^0.10.8", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.16.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "720a9c36927396efeeb48a972e9d129d44b6dc28"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/720a9c36927396efeeb48a972e9d129d44b6dc28", "type": "zip", "shasum": "", "reference": "720a9c36927396efeeb48a972e9d129d44b6dc28"}, "time": "2019-03-29T12:23:12+00:00"}, {"version": "2.16.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b509524136d579f6b15ad9af4e5bd4533d1ab6c2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b509524136d579f6b15ad9af4e5bd4533d1ab6c2", "type": "zip", "shasum": "", "reference": "b509524136d579f6b15ad9af4e5bd4533d1ab6c2"}, "time": "2019-03-20T12:20:01+00:00"}, {"version": "2.16.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "dd16fedc022180ea4292a03aabe95e9895677911"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/dd16fedc022180ea4292a03aabe95e9895677911", "type": "zip", "shasum": "", "reference": "dd16fedc022180ea4292a03aabe95e9895677911"}, "time": "2019-03-12T09:31:40+00:00"}, {"version": "2.15.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b83b387cfe2b37c8ab34f089d7eb4804b9353131"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b83b387cfe2b37c8ab34f089d7eb4804b9353131", "type": "zip", "shasum": "", "reference": "b83b387cfe2b37c8ab34f089d7eb4804b9353131"}, "time": "2019-03-07T21:57:49+00:00"}, {"version": "2.14.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a1f4f9abcde8241ce33bf5090896e9c16d0b4232"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a1f4f9abcde8241ce33bf5090896e9c16d0b4232", "type": "zip", "shasum": "", "reference": "a1f4f9abcde8241ce33bf5090896e9c16d0b4232"}, "time": "2019-02-28T09:07:12+00:00"}, {"version": "2.14.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e68f869575bd703276be8d4264799cd3cce28107"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e68f869575bd703276be8d4264799cd3cce28107", "type": "zip", "shasum": "", "reference": "e68f869575bd703276be8d4264799cd3cce28107"}, "time": "2019-02-27T13:59:59+00:00"}, {"version": "2.14.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7a748bc397bd14b8a43b13beca73a8ffec554fd9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7a748bc397bd14b8a43b13beca73a8ffec554fd9", "type": "zip", "shasum": "", "reference": "7a748bc397bd14b8a43b13beca73a8ffec554fd9"}, "time": "2019-02-25T20:57:37+00:00"}, {"version": "2.13.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d77e6cd59e1bb7aad6ad8060a61a440251627ed0"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d77e6cd59e1bb7aad6ad8060a61a440251627ed0", "type": "zip", "shasum": "", "reference": "d77e6cd59e1bb7aad6ad8060a61a440251627ed0"}, "time": "2019-02-20T20:39:03+00:00"}, {"version": "2.12.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "35dc8834ae9ad79b77c22d1e276fd838ca8c503d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/35dc8834ae9ad79b77c22d1e276fd838ca8c503d", "type": "zip", "shasum": "", "reference": "35dc8834ae9ad79b77c22d1e276fd838ca8c503d"}, "time": "2019-02-06T21:23:55+00:00", "require": {"php": "^7.1.8", "ext-json": "*", "symfony/translation": "^3.4 || ^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "phpmd/phpmd": "^2.6", "phpstan/phpstan": "^0.10.8", "phpunit/phpunit": "^7.1.5", "squizlabs/php_codesniffer": "^3.4"}}, {"version": "2.11.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "48d211435ecb3671a2237614dc19fc2be185acb6"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/48d211435ecb3671a2237614dc19fc2be185acb6", "type": "zip", "shasum": "", "reference": "48d211435ecb3671a2237614dc19fc2be185acb6"}, "time": "2019-01-29T16:35:35+00:00"}, {"version": "2.10.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "39a8ebfffc2a75de9d2ade3f0e12c7e11669f7ce"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/39a8ebfffc2a75de9d2ade3f0e12c7e11669f7ce", "type": "zip", "shasum": "", "reference": "39a8ebfffc2a75de9d2ade3f0e12c7e11669f7ce"}, "time": "2019-01-14T09:25:45+00:00", "require": {"php": "^7.1.8", "ext-json": "*", "symfony/translation": "^4.0"}}, {"version": "2.10.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "69ff95a9ccdfd5e907678ae844b3fc402110d1a3"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/69ff95a9ccdfd5e907678ae844b3fc402110d1a3", "type": "zip", "shasum": "", "reference": "69ff95a9ccdfd5e907678ae844b3fc402110d1a3"}, "time": "2019-01-02T21:35:25+00:00", "require-dev": {"phpunit/phpunit": "^7.1.5"}, "suggest": {"friendsofphp/php-cs-fixer": "Needed for the `composer phpcs` command. Allow to automatically fix code style.", "phpstan/phpstan": "Needed for the `composer phpstan` command. Allow to detect potential errors."}}, {"version": "2.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "99dd27fc02740642884e2aebde312ccca5a0ec3c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/99dd27fc02740642884e2aebde312ccca5a0ec3c", "type": "zip", "shasum": "", "reference": "99dd27fc02740642884e2aebde312ccca5a0ec3c"}, "time": "2018-12-25T17:37:52+00:00"}, {"version": "2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5f3c0bf16b73646eb12b00396eda18041ed0b6f5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5f3c0bf16b73646eb12b00396eda18041ed0b6f5", "type": "zip", "shasum": "", "reference": "5f3c0bf16b73646eb12b00396eda18041ed0b6f5"}, "time": "2018-12-25T13:09:51+00:00"}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "13bbbe16e4afcbbff7476cdcd1472a68950496a5"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/13bbbe16e4afcbbff7476cdcd1472a68950496a5", "type": "zip", "shasum": "", "reference": "13bbbe16e4afcbbff7476cdcd1472a68950496a5"}, "time": "2018-12-18T10:35:13+00:00"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "90ff5556d4bd25954c272a22654e7bd43e8ab055"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/90ff5556d4bd25954c272a22654e7bd43e8ab055", "type": "zip", "shasum": "", "reference": "90ff5556d4bd25954c272a22654e7bd43e8ab055"}, "time": "2018-12-02T10:14:51+00:00"}, {"version": "2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ab9bfecb5b9ea9557f027dcd01762495c351632f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ab9bfecb5b9ea9557f027dcd01762495c351632f", "type": "zip", "shasum": "", "reference": "ab9bfecb5b9ea9557f027dcd01762495c351632f"}, "time": "2018-11-22T16:45:03+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1f334c85c7fdd79d1fa506c87393c32f6bb75718"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1f334c85c7fdd79d1fa506c87393c32f6bb75718", "type": "zip", "shasum": "", "reference": "1f334c85c7fdd79d1fa506c87393c32f6bb75718"}, "time": "2018-11-19T19:58:55+00:00"}, {"version": "2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "92edfddcfe37b0d7956045d57f8ba714df8c0fb8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/92edfddcfe37b0d7956045d57f8ba714df8c0fb8", "type": "zip", "shasum": "", "reference": "92edfddcfe37b0d7956045d57f8ba714df8c0fb8"}, "time": "2018-11-15T06:40:11+00:00"}, {"version": "2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d5603a1540ea9acff343f31a14ffe9849b56132a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d5603a1540ea9acff343f31a14ffe9849b56132a", "type": "zip", "shasum": "", "reference": "d5603a1540ea9acff343f31a14ffe9849b56132a"}, "time": "2018-11-13T14:41:34+00:00"}, {"version": "2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "43c7426adf88345b00d480893f2efdf8cd59be3d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/43c7426adf88345b00d480893f2efdf8cd59be3d", "type": "zip", "shasum": "", "reference": "43c7426adf88345b00d480893f2efdf8cd59be3d"}, "time": "2018-11-12T21:46:13+00:00"}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1e0feb9741e6449feb18f5fb3a5542421fb4a865"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1e0feb9741e6449feb18f5fb3a5542421fb4a865", "type": "zip", "shasum": "", "reference": "1e0feb9741e6449feb18f5fb3a5542421fb4a865"}, "time": "2018-11-08T13:33:56+00:00"}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "790f2bc8c560541cc5a12e506195e3df4ee2c862"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/790f2bc8c560541cc5a12e506195e3df4ee2c862", "type": "zip", "shasum": "", "reference": "790f2bc8c560541cc5a12e506195e3df4ee2c862"}, "time": "2018-10-26T20:15:03+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "phpunit/phpunit": "^7.1.5"}, "suggest": "__unset"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "fc20926069bb310f8c798a39f26d5cb799ee1507"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/fc20926069bb310f8c798a39f26d5cb799ee1507", "type": "zip", "shasum": "", "reference": "fc20926069bb310f8c798a39f26d5cb799ee1507"}, "time": "2018-10-17T10:02:04+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5ca4ba7e34e37e2c688239cf792d1a4e40fc13c0"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5ca4ba7e34e37e2c688239cf792d1a4e40fc13c0", "type": "zip", "shasum": "", "reference": "5ca4ba7e34e37e2c688239cf792d1a4e40fc13c0"}, "time": "2018-10-16T13:07:21+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9e67c5b7c0ce5f1b3d688fd3449317fb880118dd"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9e67c5b7c0ce5f1b3d688fd3449317fb880118dd", "type": "zip", "shasum": "", "reference": "9e67c5b7c0ce5f1b3d688fd3449317fb880118dd"}, "time": "2018-10-17T10:01:53+00:00", "autoload": {"psr-4": {"": "src/"}}, "require": {"php": "^7.1.8", "symfony/translation": "^4.0"}}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "22092e6179a9f2755400dfa28f86909081470d92"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/22092e6179a9f2755400dfa28f86909081470d92", "type": "zip", "shasum": "", "reference": "22092e6179a9f2755400dfa28f86909081470d92"}, "time": "2018-09-27T07:39:41+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a55caf4cbec67a1e3b81f03b8413a1a0f0aa12f2"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a55caf4cbec67a1e3b81f03b8413a1a0f0aa12f2", "type": "zip", "shasum": "", "reference": "a55caf4cbec67a1e3b81f03b8413a1a0f0aa12f2"}, "time": "2018-10-17T10:01:44+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ace4cd4f8d7ffba9beeab8f1e09a6dc8e804c1cf"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ace4cd4f8d7ffba9beeab8f1e09a6dc8e804c1cf", "type": "zip", "shasum": "", "reference": "ace4cd4f8d7ffba9beeab8f1e09a6dc8e804c1cf"}, "time": "2018-09-20T07:48:42+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9e270ea934d8410c4e2beb714fdf297a4a7a7643"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9e270ea934d8410c4e2beb714fdf297a4a7a7643", "type": "zip", "shasum": "", "reference": "9e270ea934d8410c4e2beb714fdf297a4a7a7643"}, "time": "2018-10-17T10:01:36+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "713c4019b8b53d22f220f4abbaf433205c13e872"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/713c4019b8b53d22f220f4abbaf433205c13e872", "type": "zip", "shasum": "", "reference": "713c4019b8b53d22f220f4abbaf433205c13e872"}, "time": "2018-09-03T13:49:24+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5ff49f12e0d275b17f31dd1e6bb861c1eb7d53dd"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5ff49f12e0d275b17f31dd1e6bb861c1eb7d53dd", "type": "zip", "shasum": "", "reference": "5ff49f12e0d275b17f31dd1e6bb861c1eb7d53dd"}, "time": "2018-10-17T13:35:24+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "79c3cd96855b49b096cae6a64b1e17da13cefb2e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/79c3cd96855b49b096cae6a64b1e17da13cefb2e", "type": "zip", "shasum": "", "reference": "79c3cd96855b49b096cae6a64b1e17da13cefb2e"}, "time": "2018-08-23T16:45:38+00:00"}, {"version": "2.0.0-beta.6", "version_normalized": "*******-beta6"}, {"version": "2.0.0-beta.5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ae6ebdaa65071243e1697845a22602bf82a5580e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ae6ebdaa65071243e1697845a22602bf82a5580e", "type": "zip", "shasum": "", "reference": "ae6ebdaa65071243e1697845a22602bf82a5580e"}, "time": "2018-08-23T14:23:28+00:00"}, {"version": "2.0.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3c8b8af57eaac337395f1cda78d88c97228400db"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3c8b8af57eaac337395f1cda78d88c97228400db", "type": "zip", "shasum": "", "reference": "3c8b8af57eaac337395f1cda78d88c97228400db"}, "time": "2018-08-23T09:05:40+00:00"}, {"version": "2.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "fd911463a14dd8ded42942651e5fb1356a38ed11"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/fd911463a14dd8ded42942651e5fb1356a38ed11", "type": "zip", "shasum": "", "reference": "fd911463a14dd8ded42942651e5fb1356a38ed11"}, "time": "2018-08-20T15:41:39+00:00"}, {"version": "2.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "0a3aecf9625436cd7c440a52f80280d34f1e6c77"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/0a3aecf9625436cd7c440a52f80280d34f1e6c77", "type": "zip", "shasum": "", "reference": "0a3aecf9625436cd7c440a52f80280d34f1e6c77"}, "time": "2018-08-13T11:25:28+00:00"}, {"version": "2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "6b83dd0c65ce312e98f60483ea6463151f3d721d"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6b83dd0c65ce312e98f60483ea6463151f3d721d", "type": "zip", "shasum": "", "reference": "6b83dd0c65ce312e98f60483ea6463151f3d721d"}, "time": "2018-08-08T05:36:26+00:00"}, {"version": "1.39.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4be0c005164249208ce1b5ca633cd57bdd42ff33", "type": "zip", "shasum": "", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "time": "2019-10-14T05:51:36+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "update-helper": "Carbon\\Upgrade"}, "bin": ["bin/upgrade-carbon"], "require": {"php": ">=5.3.9", "kylekatarnls/update-helper": "^1.1", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}}, {"version": "1.39.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "dd62a58af4e0775a45ea5f99d0363d81b7d9a1e0"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/dd62a58af4e0775a45ea5f99d0363d81b7d9a1e0", "type": "zip", "shasum": "", "reference": "dd62a58af4e0775a45ea5f99d0363d81b7d9a1e0"}, "time": "2019-06-11T09:07:59+00:00"}, {"version": "1.38.4", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "8dd4172bfe1784952c4d58c4db725d183b1c23ad"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8dd4172bfe1784952c4d58c4db725d183b1c23ad", "type": "zip", "shasum": "", "reference": "8dd4172bfe1784952c4d58c4db725d183b1c23ad"}, "time": "2019-06-03T15:41:40+00:00"}, {"version": "1.38.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "00149d95fc91ef10f19d3a66889bc3bb7500fa7b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/00149d95fc91ef10f19d3a66889bc3bb7500fa7b", "type": "zip", "shasum": "", "reference": "00149d95fc91ef10f19d3a66889bc3bb7500fa7b"}, "time": "2018-05-18T15:26:18+00:00", "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "extra": "__unset", "bin": "__unset"}, {"version": "1.38.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7621621f76c99f01318313f576baea22edd86367"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7621621f76c99f01318313f576baea22edd86367", "type": "zip", "shasum": "", "reference": "7621621f76c99f01318313f576baea22edd86367"}, "time": "2019-05-30T22:56:55+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "update-helper": "Carbon\\Upgrade"}, "bin": ["bin/upgrade-carbon"], "require": {"php": ">=5.3.9", "kylekatarnls/update-helper": "^1.1", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}}, {"version": "1.38.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "59ace7b305ca6edeef52db6377116d0bd76ec7a8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/59ace7b305ca6edeef52db6377116d0bd76ec7a8", "type": "zip", "shasum": "", "reference": "59ace7b305ca6edeef52db6377116d0bd76ec7a8"}, "time": "2019-05-30T21:35:57+00:00"}, {"version": "1.38.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7f11e8fecef08b74f1cdf93f3e001389c911d5cc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7f11e8fecef08b74f1cdf93f3e001389c911d5cc", "type": "zip", "shasum": "", "reference": "7f11e8fecef08b74f1cdf93f3e001389c911d5cc"}, "time": "2019-05-30T08:57:38+00:00", "require": {"php": ">=5.3.9", "kylekatarnls/update-helper": "^1.0", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}}, {"version": "1.37.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5be4fdf97076a685b23efdedfc2b73ad0c5eab70"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5be4fdf97076a685b23efdedfc2b73ad0c5eab70", "type": "zip", "shasum": "", "reference": "5be4fdf97076a685b23efdedfc2b73ad0c5eab70"}, "time": "2019-04-19T10:27:42+00:00", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"friendsofphp/php-cs-fixer": "Needed for the `composer phpcs` command. Allow to automatically fix code style.", "phpstan/phpstan": "Needed for the `composer phpstan` command. Allow to detect potential errors."}, "bin": "__unset"}, {"version": "1.37.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f8a29bb3b0cf1f4dc94ba1deaeccd8084b206ce8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f8a29bb3b0cf1f4dc94ba1deaeccd8084b206ce8", "type": "zip", "shasum": "", "reference": "f8a29bb3b0cf1f4dc94ba1deaeccd8084b206ce8"}, "time": "2019-05-03T07:53:30+00:00", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "require": {"php": "^7.1.8 || ^8.0", "ext-json": "*", "symfony/translation": "^3.4 || ^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^1.1", "phpmd/phpmd": "^2.6", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}, "suggest": "__unset"}, {"version": "1.36.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "cd324b98bc30290f233dd0e75e6ce49f7ab2a6c9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/cd324b98bc30290f233dd0e75e6ce49f7ab2a6c9", "type": "zip", "shasum": "", "reference": "cd324b98bc30290f233dd0e75e6ce49f7ab2a6c9"}, "time": "2018-12-28T10:07:33+00:00", "autoload": {"psr-4": {"": "src/"}}, "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"friendsofphp/php-cs-fixer": "Needed for the `composer phpcs` command. Allow to automatically fix code style.", "phpstan/phpstan": "Needed for the `composer phpstan` command. Allow to detect potential errors."}}, {"version": "1.36.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "63da8cdf89d7a5efe43aabc794365f6e7b7b8983"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/63da8cdf89d7a5efe43aabc794365f6e7b7b8983", "type": "zip", "shasum": "", "reference": "63da8cdf89d7a5efe43aabc794365f6e7b7b8983"}, "time": "2018-11-22T18:23:02+00:00"}, {"version": "1.36.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "f5e9fa283fa389ffa49e58391a02f063dcdd1815"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/f5e9fa283fa389ffa49e58391a02f063dcdd1815", "type": "zip", "shasum": "", "reference": "f5e9fa283fa389ffa49e58391a02f063dcdd1815"}, "time": "2018-11-16T11:47:11+00:00"}, {"version": "1.35.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5c05a2be472b22f63291d192410df9f0e0de3b19"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5c05a2be472b22f63291d192410df9f0e0de3b19", "type": "zip", "shasum": "", "reference": "5c05a2be472b22f63291d192410df9f0e0de3b19"}, "time": "2018-11-14T21:55:58+00:00"}, {"version": "1.35.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3c0342ed6cb7c770d0c33595c51c286716552f65"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3c0342ed6cb7c770d0c33595c51c286716552f65", "type": "zip", "shasum": "", "reference": "3c0342ed6cb7c770d0c33595c51c286716552f65"}, "time": "2018-11-14T12:39:51+00:00"}, {"version": "1.34.4", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b9a444d829c9a16735aceca1b173e765745bcc0c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b9a444d829c9a16735aceca1b173e765745bcc0c", "type": "zip", "shasum": "", "reference": "b9a444d829c9a16735aceca1b173e765745bcc0c"}, "time": "2018-11-13T08:26:10+00:00"}, {"version": "1.34.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3b903d236ccd757d29c20632bf55ae91071c1b6f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3b903d236ccd757d29c20632bf55ae91071c1b6f", "type": "zip", "shasum": "", "reference": "3b903d236ccd757d29c20632bf55ae91071c1b6f"}, "time": "2018-11-13T07:01:46+00:00"}, {"version": "1.34.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "6689b51b1dd5b1471c0f2c6df426037824114acc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6689b51b1dd5b1471c0f2c6df426037824114acc", "type": "zip", "shasum": "", "reference": "6689b51b1dd5b1471c0f2c6df426037824114acc"}, "time": "2018-11-12T22:45:42+00:00"}, {"version": "1.34.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "19201b87f7dba2a7cbf1cccdf0e1da13c04ee9c9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/19201b87f7dba2a7cbf1cccdf0e1da13c04ee9c9", "type": "zip", "shasum": "", "reference": "19201b87f7dba2a7cbf1cccdf0e1da13c04ee9c9"}, "time": "2018-11-08T13:33:47+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": "__unset"}, {"version": "1.34.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "1dbd3cb01c5645f3e7deda7aa46ef780d95fcc33"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1dbd3cb01c5645f3e7deda7aa46ef780d95fcc33", "type": "zip", "shasum": "", "reference": "1dbd3cb01c5645f3e7deda7aa46ef780d95fcc33"}, "time": "2018-09-20T19:36:25+00:00"}, {"version": "1.33.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "55667c1007a99e82030874b1bb14d24d07108413"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/55667c1007a99e82030874b1bb14d24d07108413", "type": "zip", "shasum": "", "reference": "55667c1007a99e82030874b1bb14d24d07108413"}, "time": "2018-08-07T08:39:47+00:00"}, {"version": "1.32.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "64563e2b9f69e4db1b82a60e81efa327a30ff343"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/64563e2b9f69e4db1b82a60e81efa327a30ff343", "type": "zip", "shasum": "", "reference": "64563e2b9f69e4db1b82a60e81efa327a30ff343"}, "time": "2018-07-05T06:59:26+00:00"}, {"version": "1.31.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "385780c8ca2dbfd25452666e3f55e8dc1df58c41"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/385780c8ca2dbfd25452666e3f55e8dc1df58c41", "type": "zip", "shasum": "", "reference": "385780c8ca2dbfd25452666e3f55e8dc1df58c41"}, "time": "2018-06-25T13:15:16+00:00"}, {"version": "1.31.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4dbe0e51d24a4c7e000999324d21fc25edbf7b05"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4dbe0e51d24a4c7e000999324d21fc25edbf7b05", "type": "zip", "shasum": "", "reference": "4dbe0e51d24a4c7e000999324d21fc25edbf7b05"}, "time": "2018-06-24T14:32:22+00:00"}, {"version": "1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "863a1a651ea324e1838da3a52753a4239b9d4bea"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/863a1a651ea324e1838da3a52753a4239b9d4bea", "type": "zip", "shasum": "", "reference": "863a1a651ea324e1838da3a52753a4239b9d4bea"}, "time": "2018-06-15T11:52:26+00:00", "extra": "__unset"}, {"version": "1.29.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ed6aa898982f441ccc9b2acdec51490f2bc5d337"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ed6aa898982f441ccc9b2acdec51490f2bc5d337", "type": "zip", "shasum": "", "reference": "ed6aa898982f441ccc9b2acdec51490f2bc5d337"}, "time": "2018-05-29T15:23:46+00:00"}, {"version": "1.29.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "861f8da904c6d4ee937b25d6f4d35a18a8e1e1ac"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/861f8da904c6d4ee937b25d6f4d35a18a8e1e1ac", "type": "zip", "shasum": "", "reference": "861f8da904c6d4ee937b25d6f4d35a18a8e1e1ac"}, "time": "2018-05-24T16:44:37+00:00"}, {"version": "1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b1cc4493fafb1b1402d50474fe6305e0558c6829"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b1cc4493fafb1b1402d50474fe6305e0558c6829", "type": "zip", "shasum": "", "reference": "b1cc4493fafb1b1402d50474fe6305e0558c6829"}, "time": "2018-05-24T13:09:20+00:00"}, {"version": "1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "00149d95fc91ef10f19d3a66889bc3bb7500fa7b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/00149d95fc91ef10f19d3a66889bc3bb7500fa7b", "type": "zip", "shasum": "", "reference": "00149d95fc91ef10f19d3a66889bc3bb7500fa7b"}, "time": "2018-05-18T15:26:18+00:00"}, {"version": "1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ef81c39b67200dcd7401c24363dcac05ac3a4fe9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ef81c39b67200dcd7401c24363dcac05ac3a4fe9", "type": "zip", "shasum": "", "reference": "ef81c39b67200dcd7401c24363dcac05ac3a4fe9"}, "time": "2018-04-23T09:02:57+00:00"}, {"version": "1.26.6", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c6820f814496d71da7498d423427e6193d1f57c9"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c6820f814496d71da7498d423427e6193d1f57c9", "type": "zip", "shasum": "", "reference": "c6820f814496d71da7498d423427e6193d1f57c9"}, "time": "2019-06-03T15:42:58+00:00", "extra": {"update-helper": "Carbon\\Upgrade"}, "bin": ["bin/upgrade-carbon"], "require": {"php": ">=5.3.9", "kylekatarnls/update-helper": "^1.1", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}}, {"version": "1.26.5", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9cb442b81892b57d3d449b48ff2d13deb94a6527"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9cb442b81892b57d3d449b48ff2d13deb94a6527", "type": "zip", "shasum": "", "reference": "9cb442b81892b57d3d449b48ff2d13deb94a6527"}, "time": "2019-05-30T23:00:05+00:00"}, {"version": "1.26.4", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "e3d9014279133a3cccc01f6a691322a2d5a6a87b"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/e3d9014279133a3cccc01f6a691322a2d5a6a87b", "type": "zip", "shasum": "", "reference": "e3d9014279133a3cccc01f6a691322a2d5a6a87b"}, "time": "2018-04-17T15:35:42+00:00", "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "extra": "__unset", "bin": "__unset"}, {"version": "1.26.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b63c0d809256a434ea74e6b41ca5f5f9d16220e6"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b63c0d809256a434ea74e6b41ca5f5f9d16220e6", "type": "zip", "shasum": "", "reference": "b63c0d809256a434ea74e6b41ca5f5f9d16220e6"}, "time": "2018-04-16T14:54:26+00:00"}, {"version": "1.26.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "c0ae2011b47f00a51dc84b18399894953097bca3"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/c0ae2011b47f00a51dc84b18399894953097bca3", "type": "zip", "shasum": "", "reference": "c0ae2011b47f00a51dc84b18399894953097bca3"}, "time": "2018-04-16T14:06:36+00:00"}, {"version": "1.26.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bab61b25da5a28a6a7a5785d34802174b2a24b99"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bab61b25da5a28a6a7a5785d34802174b2a24b99", "type": "zip", "shasum": "", "reference": "bab61b25da5a28a6a7a5785d34802174b2a24b99"}, "time": "2018-04-16T08:58:42+00:00", "extra": {"branch-alias": {"dev-master": "1.23-dev"}}}, {"version": "1.26.0", "version_normalized": "********"}, {"version": "1.25.3", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ad6afecd38ce2d7f7bd1b5d47ffd8e93ebbd3ed8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ad6afecd38ce2d7f7bd1b5d47ffd8e93ebbd3ed8", "type": "zip", "shasum": "", "reference": "ad6afecd38ce2d7f7bd1b5d47ffd8e93ebbd3ed8"}, "time": "2019-06-03T17:56:44+00:00", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "extra": {"update-helper": "Carbon\\Upgrade"}, "bin": ["bin/upgrade-carbon"], "require": {"php": ">=5.3.9", "kylekatarnls/update-helper": "^1.1", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}}, {"version": "1.25.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "59082944add8b30226775eac3606cb90f80da535"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/59082944add8b30226775eac3606cb90f80da535", "type": "zip", "shasum": "", "reference": "59082944add8b30226775eac3606cb90f80da535"}, "time": "2019-05-31T11:38:01+00:00"}, {"version": "1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "cbcf13da0b531767e39eb86e9687f5deba9857b4"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/cbcf13da0b531767e39eb86e9687f5deba9857b4", "type": "zip", "shasum": "", "reference": "cbcf13da0b531767e39eb86e9687f5deba9857b4"}, "time": "2018-03-19T15:50:49+00:00", "extra": {"branch-alias": {"dev-master": "1.23-dev"}}, "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": "__unset"}, {"version": "1.24.2", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bba6c6e410c6b4317e37a9474aeaa753808c3875"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bba6c6e410c6b4317e37a9474aeaa753808c3875", "type": "zip", "shasum": "", "reference": "bba6c6e410c6b4317e37a9474aeaa753808c3875"}, "time": "2018-03-10T10:10:14+00:00"}, {"version": "1.24.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "ef12cbd8ba5ce624f0a6a95e0850c4cc13e71e41"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ef12cbd8ba5ce624f0a6a95e0850c4cc13e71e41", "type": "zip", "shasum": "", "reference": "ef12cbd8ba5ce624f0a6a95e0850c4cc13e71e41"}, "time": "2018-03-09T15:49:34+00:00"}, {"version": "1.24.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a05bed23de4c00040fe33cc6820c7e6be8d00c04"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a05bed23de4c00040fe33cc6820c7e6be8d00c04", "type": "zip", "shasum": "", "reference": "a05bed23de4c00040fe33cc6820c7e6be8d00c04"}, "time": "2018-03-09T12:52:49+00:00"}, {"version": "1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "4a874a39b2b00d7e0146cd46fab6f47c41ce9e65"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4a874a39b2b00d7e0146cd46fab6f47c41ce9e65", "type": "zip", "shasum": "", "reference": "4a874a39b2b00d7e0146cd46fab6f47c41ce9e65"}, "time": "2018-02-28T09:22:05+00:00", "require": {"php": ">=5.3.0", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}}, {"version": "1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7cdf42c0b1cc763ab7e4c33c47a24e27c66bfccc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7cdf42c0b1cc763ab7e4c33c47a24e27c66bfccc", "type": "zip", "shasum": "", "reference": "7cdf42c0b1cc763ab7e4c33c47a24e27c66bfccc"}, "time": "2017-01-16T07:55:07+00:00", "require": {"php": ">=5.3.0", "symfony/translation": "~2.6 || ~3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "~4.0 || ~5.0"}}, {"version": "1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "3b114ab800c6432ad42387ccf6bc8d4388a2885a"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/3b114ab800c6432ad42387ccf6bc8d4388a2885a", "type": "zip", "shasum": "", "reference": "3b114ab800c6432ad42387ccf6bc8d4388a2885a"}, "time": "2017-01-15T14:47:48+00:00"}, {"version": "1.21.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "7b08ec6f75791e130012f206e3f7b0e76e18e3d7"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/7b08ec6f75791e130012f206e3f7b0e76e18e3d7", "type": "zip", "shasum": "", "reference": "7b08ec6f75791e130012f206e3f7b0e76e18e3d7"}, "time": "2015-11-04T20:07:17+00:00", "require": {"php": ">=5.3.0", "symfony/translation": "~2.6|~3.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "extra": "__unset"}, {"version": "1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bfd3eaba109c9a2405c92174c8e17f20c2b9caf3"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bfd3eaba109c9a2405c92174c8e17f20c2b9caf3", "type": "zip", "shasum": "", "reference": "bfd3eaba109c9a2405c92174c8e17f20c2b9caf3"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.20.0"}, "time": "2015-06-25T04:19:39+00:00", "autoload": {"psr-0": {"Carbon": "src"}}, "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "68868e0b02d2d803d0052a59d4e5003cccf87320"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/68868e0b02d2d803d0052a59d4e5003cccf87320", "type": "zip", "shasum": "", "reference": "68868e0b02d2d803d0052a59d4e5003cccf87320"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.19.0"}, "time": "2015-05-09T03:23:44+00:00", "require": {"php": ">=5.3.0", "symfony/translation": "~2.6"}}, {"version": "1.18.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "99e2f69f7bdc2cc4334b2d00f1e0ba450623ea36"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/99e2f69f7bdc2cc4334b2d00f1e0ba450623ea36", "type": "zip", "shasum": "", "reference": "99e2f69f7bdc2cc4334b2d00f1e0ba450623ea36"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.18.0"}, "time": "2015-03-26T03:05:57+00:00", "require": {"php": ">=5.3.0", "symfony/translation": "2.6.*"}}, {"version": "1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "a1dd1ad9abfc8b3c4d8768068e6c71d293424e86"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/a1dd1ad9abfc8b3c4d8768068e6c71d293424e86", "type": "zip", "shasum": "", "reference": "a1dd1ad9abfc8b3c4d8768068e6c71d293424e86"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.17.0"}, "time": "2015-03-08T14:05:44+00:00", "require": {"php": ">=5.3.0"}}, {"version": "1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9b7665041754eaa9bb2622349bc9adbc1cf61cb8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9b7665041754eaa9bb2622349bc9adbc1cf61cb8", "type": "zip", "shasum": "", "reference": "9b7665041754eaa9bb2622349bc9adbc1cf61cb8"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.16.0"}, "time": "2015-03-04T04:50:11+00:00"}, {"version": "1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "48cb9ae6d9436e156316a59d198cb1ca797f8cd8"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/48cb9ae6d9436e156316a59d198cb1ca797f8cd8", "type": "zip", "shasum": "", "reference": "48cb9ae6d9436e156316a59d198cb1ca797f8cd8"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.15.0"}, "time": "2015-03-03T03:24:43+00:00"}, {"homepage": "https://github.com/briannesbitt/Carbon", "version": "1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "bb87460c995d97fe55b39e65f6ffb7f64b0a941e"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/bb87460c995d97fe55b39e65f6ffb7f64b0a941e", "type": "zip", "shasum": "", "reference": "bb87460c995d97fe55b39e65f6ffb7f64b0a941e"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.14.0"}, "time": "2015-02-06T05:07:29+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5cb6e71055f7b0b57956b73d324cc4de31278f42"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5cb6e71055f7b0b57956b73d324cc4de31278f42", "type": "zip", "shasum": "", "reference": "5cb6e71055f7b0b57956b73d324cc4de31278f42"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.13.0"}, "time": "2014-09-26T02:52:02+00:00"}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "cde7a00d1410a17fb6d61b993cb017a78be1137c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/cde7a00d1410a17fb6d61b993cb017a78be1137c", "type": "zip", "shasum": "", "reference": "cde7a00d1410a17fb6d61b993cb017a78be1137c"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.12.0"}, "time": "2014-09-10T03:26:33+00:00"}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "2b60366be6dcb9f9cd3e244ab25eb98eaea99551"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/2b60366be6dcb9f9cd3e244ab25eb98eaea99551", "type": "zip", "shasum": "", "reference": "2b60366be6dcb9f9cd3e244ab25eb98eaea99551"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.11.0"}, "time": "2014-08-26T03:18:44+00:00"}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "9b42a1aec56011c2ac4d75c0ddad0794762344fc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9b42a1aec56011c2ac4d75c0ddad0794762344fc", "type": "zip", "shasum": "", "reference": "9b42a1aec56011c2ac4d75c0ddad0794762344fc"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.10.0"}, "time": "2014-07-18T03:44:47+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b94de7192b01d0e80794eae984dcc773220ab0dc"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b94de7192b01d0e80794eae984dcc773220ab0dc", "type": "zip", "shasum": "", "reference": "b94de7192b01d0e80794eae984dcc773220ab0dc"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.9.0"}, "time": "2014-05-13T02:29:30+00:00", "require-dev": {"phpunit/phpunit": "3.7.*"}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "21c4cb4301969c7d85aee8a62eefdfa881413af0"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/21c4cb4301969c7d85aee8a62eefdfa881413af0", "type": "zip", "shasum": "", "reference": "21c4cb4301969c7d85aee8a62eefdfa881413af0"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.8.0"}, "time": "2014-01-07T05:10:44+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "03ede52a1f360441b5826cb8a798e0d0a919731f"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/03ede52a1f360441b5826cb8a798e0d0a919731f", "type": "zip", "shasum": "", "reference": "03ede52a1f360441b5826cb8a798e0d0a919731f"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.7.0"}, "time": "2013-12-05T04:13:29+00:00"}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "b0450b539d1567b2acee6467b6b323a8547b5559"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/b0450b539d1567b2acee6467b6b323a8547b5559", "type": "zip", "shasum": "", "reference": "b0450b539d1567b2acee6467b6b323a8547b5559"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.6.0"}, "time": "2013-11-23T15:22:05+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "64c0192173e78ad405411f16d3c1a699386d882c"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/64c0192173e78ad405411f16d3c1a699386d882c", "type": "zip", "shasum": "", "reference": "64c0192173e78ad405411f16d3c1a699386d882c"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.5.0"}, "time": "2013-11-21T21:09:53+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "06f0b8a99a90c5392ceccb09b75b74ff6c08ec07"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/06f0b8a99a90c5392ceccb09b75b74ff6c08ec07", "type": "zip", "shasum": "", "reference": "06f0b8a99a90c5392ceccb09b75b74ff6c08ec07"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.4.0"}, "time": "2013-09-09T02:39:19+00:00", "require-dev": "__unset"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "24c4262e994f7f2a258db5ccb5b2f34073f162ca"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/24c4262e994f7f2a258db5ccb5b2f34073f162ca", "type": "zip", "shasum": "", "reference": "24c4262e994f7f2a258db5ccb5b2f34073f162ca"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.3.0"}, "time": "2013-08-21T04:36:40+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "5df97d55fd9b9b630c71c01050ff8901ca4085ef"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/5df97d55fd9b9b630c71c01050ff8901ca4085ef", "type": "zip", "shasum": "", "reference": "5df97d55fd9b9b630c71c01050ff8901ca4085ef"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.2.0"}, "time": "2012-10-15T00:41:18+00:00", "autoload": {"psr-0": {"Carbon": "."}}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "205dedbf0ae08f0025f364871cf788c230b97eee"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/205dedbf0ae08f0025f364871cf788c230b97eee", "type": "zip", "shasum": "", "reference": "205dedbf0ae08f0025f364871cf788c230b97eee"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.1.0"}, "time": "2012-09-17T02:04:24+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "abc60599a101171f37bdd1885d7d564644880fae"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/abc60599a101171f37bdd1885d7d564644880fae", "type": "zip", "shasum": "", "reference": "abc60599a101171f37bdd1885d7d564644880fae"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.0.1"}, "time": "2012-09-11T03:37:22+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/CarbonPHP/carbon.git", "type": "git", "reference": "d59535c3175d1188baea460bd7772d41ea227b56"}, "dist": {"url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/d59535c3175d1188baea460bd7772d41ea227b56", "type": "zip", "shasum": "", "reference": "d59535c3175d1188baea460bd7772d41ea227b56"}, "support": {"issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon/tree/1.0.0"}, "time": "2012-09-11T02:21:48+00:00"}]}, "security-advisories": [{"advisoryId": "PKSA-csyb-yc4p-mnbs", "affectedVersions": "<2.72.6|>=3.0.0,<3.8.4"}], "last-modified": "Sat, 21 Jun 2025 15:33:43 GMT"}