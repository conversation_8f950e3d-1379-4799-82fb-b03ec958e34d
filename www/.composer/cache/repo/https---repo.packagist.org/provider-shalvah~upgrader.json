{"minified": "composer/2.0", "packages": {"shalvah/upgrader": [{"name": "shalvah/upgrader", "description": "Create automatic upgrades for your package.", "keywords": ["upgrade"], "homepage": "http://github.com/shalvah/upgrader", "version": "0.6.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "d95ed17fe9f5e1ee7d47ad835595f1af080a867f"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/d95ed17fe9f5e1ee7d47ad835595f1af080a867f", "type": "zip", "shasum": "", "reference": "d95ed17fe9f5e1ee7d47ad835595f1af080a867f"}, "type": "library", "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.6.0"}, "funding": [{"url": "https://patreon.com/shalvah", "type": "patreon"}], "time": "2024-02-20T11:51:46+00:00", "autoload": {"psr-4": {"Shalvah\\Upgrader\\": "src/"}}, "require": {"php": ">=8.0", "illuminate/support": ">=8.0", "nikic/php-parser": "^5.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.2.0", "pestphp/pest": "^1.21", "phpstan/phpstan": "^1.0", "spatie/ray": "^1.33"}}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "e1ee67cc411ca9af61375548431e2f38f694ea85"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/e1ee67cc411ca9af61375548431e2f38f694ea85", "type": "zip", "shasum": "", "reference": "e1ee67cc411ca9af61375548431e2f38f694ea85"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.5.0"}, "time": "2024-02-20T11:48:40+00:00", "require": {"php": ">=8.0", "illuminate/support": ">=8.0", "nikic/php-parser": "^4.13|^5.0"}}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "b9c5dcccf226ff29b5e6f9a53dfe3b96a30acf7a"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/b9c5dcccf226ff29b5e6f9a53dfe3b96a30acf7a", "type": "zip", "shasum": "", "reference": "b9c5dcccf226ff29b5e6f9a53dfe3b96a30acf7a"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.4.0"}, "time": "2024-02-14T23:57:54+00:00"}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "c7f1cca8be5cd0114cd816d8bfff9c80565f7390"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/c7f1cca8be5cd0114cd816d8bfff9c80565f7390", "type": "zip", "shasum": "", "reference": "c7f1cca8be5cd0114cd816d8bfff9c80565f7390"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.3.1"}, "time": "2023-02-06T01:20:37+00:00", "require": {"php": ">=8.0", "illuminate/support": "^8.0|^9.0|^10.0", "nikic/php-parser": "^4.13"}}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "2a2b1452bd4a1484784deb2af49fb5e1abbcf420"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/2a2b1452bd4a1484784deb2af49fb5e1abbcf420", "type": "zip", "shasum": "", "reference": "2a2b1452bd4a1484784deb2af49fb5e1abbcf420"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.3.0"}, "time": "2022-09-10T02:18:15+00:00", "require": {"php": ">=8.0", "illuminate/support": "^8.0|^9.0", "nikic/php-parser": "^4.13"}}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "cad9bea6ac6584bf05e58dbc8af43bcb636370e6"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/cad9bea6ac6584bf05e58dbc8af43bcb636370e6", "type": "zip", "shasum": "", "reference": "cad9bea6ac6584bf05e58dbc8af43bcb636370e6"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.2.1"}, "time": "2022-09-10T02:11:25+00:00", "require": {"php": ">=7.4", "illuminate/support": "^6.0|^7.0|^8.0|^9.0", "nikic/php-parser": "^4.13"}}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "3f07bf967b450c05f0783790c6dd353a6775a424"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/3f07bf967b450c05f0783790c6dd353a6775a424", "type": "zip", "shasum": "", "reference": "3f07bf967b450c05f0783790c6dd353a6775a424"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.2.0"}, "time": "2022-01-14T11:07:47+00:00"}, {"version": "0.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "ce368ee2333b3546afc698add299b23b3f46cd56"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/ce368ee2333b3546afc698add299b23b3f46cd56", "type": "zip", "shasum": "", "reference": "ce368ee2333b3546afc698add299b23b3f46cd56"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.1.5"}, "time": "2021-09-20T13:50:15+00:00", "require": {"php": ">=7.4", "illuminate/support": "^6.0|^7.0|^8.0", "nikic/php-parser": "^4.13"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.2.0", "pestphp/pest": "^1.10", "phpstan/phpstan": "^0.12.90", "spatie/ray": "^1.28"}}, {"version": "0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "3006962363988cbb75ac7860915949fa8fe7cc2a"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/3006962363988cbb75ac7860915949fa8fe7cc2a", "type": "zip", "shasum": "", "reference": "3006962363988cbb75ac7860915949fa8fe7cc2a"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.1.4"}, "time": "2021-09-09T15:46:25+00:00", "require": {"php": ">=7.4", "illuminate/support": "^6.0|^7.0|^8.0", "nikic/php-parser": "^4.10"}}, {"version": "0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "bc67e8bf8050fe4e6b380453f9a8ee60d3c07729"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/bc67e8bf8050fe4e6b380453f9a8ee60d3c07729", "type": "zip", "shasum": "", "reference": "bc67e8bf8050fe4e6b380453f9a8ee60d3c07729"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.1.3"}, "time": "2021-09-09T12:31:45+00:00"}, {"version": "0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "573e69bacfc266477d6c113eb869d0e6c2d7f36c"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/573e69bacfc266477d6c113eb869d0e6c2d7f36c", "type": "zip", "shasum": "", "reference": "573e69bacfc266477d6c113eb869d0e6c2d7f36c"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.1.2"}, "time": "2021-07-29T13:32:06+00:00", "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.2.0", "mockery/mockery": "^1.4", "pestphp/pest": "^1.10", "phpstan/phpstan": "^0.12.90", "spatie/ray": "^1.28"}}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "242f2eceeed64dbfff73bf77815ad4563190b8a3"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/242f2eceeed64dbfff73bf77815ad4563190b8a3", "type": "zip", "shasum": "", "reference": "242f2eceeed64dbfff73bf77815ad4563190b8a3"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.1.1"}, "time": "2021-07-28T20:11:00+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/shalvah/upgrader.git", "type": "git", "reference": "e6a3e01fc67ab997c549972c15bb9a0cdf359914"}, "dist": {"url": "https://api.github.com/repos/shalvah/upgrader/zipball/e6a3e01fc67ab997c549972c15bb9a0cdf359914", "type": "zip", "shasum": "", "reference": "e6a3e01fc67ab997c549972c15bb9a0cdf359914"}, "support": {"issues": "https://github.com/shalvah/upgrader/issues", "source": "https://github.com/shalvah/upgrader/tree/0.1.0"}, "time": "2021-07-24T15:22:08+00:00", "require": {"php": ">=7.4", "illuminate/support": "^8.51", "nikic/php-parser": "^4.10"}}]}, "security-advisories": [], "last-modified": "Sat, 20 Apr 2024 12:17:11 GMT"}