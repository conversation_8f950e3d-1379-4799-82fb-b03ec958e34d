{"minified": "composer/2.0", "packages": {"phpdocumentor/type-resolver": [{"name": "phpdocumentor/type-resolver", "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "keywords": [], "homepage": "", "version": "1.10.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "type": "zip", "shasum": "", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "type": "library", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "funding": [], "time": "2024-11-09T15:12:26+00:00", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "require": {"php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0", "doctrine/deprecations": "^1.0"}, "require-dev": {"ext-tokenizer": "*", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/extension-installer": "^1.1", "vimeo/psalm": "^4.25", "rector/rector": "^0.13.9", "phpbench/phpbench": "^1.2"}}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "1fb5ba8d045f5dd984ebded5b1cc66f29459422d"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/1fb5ba8d045f5dd984ebded5b1cc66f29459422d", "type": "zip", "shasum": "", "reference": "1fb5ba8d045f5dd984ebded5b1cc66f29459422d"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.9.0"}, "time": "2024-11-03T20:11:34+00:00", "require": {"php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18", "doctrine/deprecations": "^1.0"}}, {"version": "1.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "type": "zip", "shasum": "", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.2"}, "time": "2024-02-23T11:10:43+00:00", "require": {"php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13", "doctrine/deprecations": "^1.0"}}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "bc3dc91a5e9b14aa06d1d9e90647c5c5a2cc5353"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/bc3dc91a5e9b14aa06d1d9e90647c5c5a2cc5353", "type": "zip", "shasum": "", "reference": "bc3dc91a5e9b14aa06d1d9e90647c5c5a2cc5353"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.1"}, "time": "2024-01-18T19:15:27+00:00", "require": {"php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13", "doctrine/deprecations": "^1.0"}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "fad452781b3d774e3337b0c0b245dd8e5a4455fc"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/fad452781b3d774e3337b0c0b245dd8e5a4455fc", "type": "zip", "shasum": "", "reference": "fad452781b3d774e3337b0c0b245dd8e5a4455fc"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.0"}, "time": "2024-01-11T11:49:22+00:00"}, {"version": "1.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "8430ca5c93b5b933ef1aaf8dd93f80d0c55d2349"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/8430ca5c93b5b933ef1aaf8dd93f80d0c55d2349", "type": "zip", "shasum": "", "reference": "8430ca5c93b5b933ef1aaf8dd93f80d0c55d2349"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.4"}, "time": "2024-01-11T11:21:11+00:00"}, {"version": "1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "3219c6ee25c9ea71e3d9bbaf39c67c9ebd499419"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/3219c6ee25c9ea71e3d9bbaf39c67c9ebd499419", "type": "zip", "shasum": "", "reference": "3219c6ee25c9ea71e3d9bbaf39c67c9ebd499419"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.3"}, "time": "2023-08-12T11:01:26+00:00"}, {"version": "1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "b2fe4d22a5426f38e014855322200b97b5362c0d"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/b2fe4d22a5426f38e014855322200b97b5362c0d", "type": "zip", "shasum": "", "reference": "b2fe4d22a5426f38e014855322200b97b5362c0d"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.2"}, "time": "2023-05-30T18:13:47+00:00"}, {"version": "1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "dfc078e8af9c99210337325ff5aa152872c98714"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/dfc078e8af9c99210337325ff5aa152872c98714", "type": "zip", "shasum": "", "reference": "dfc078e8af9c99210337325ff5aa152872c98714"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.1"}, "time": "2023-03-27T19:02:04+00:00"}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "1534aea9bde19a5c85c5d1e1f834ab63f4c5dcf5"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/1534aea9bde19a5c85c5d1e1f834ab63f4c5dcf5", "type": "zip", "shasum": "", "reference": "1534aea9bde19a5c85c5d1e1f834ab63f4c5dcf5"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.7.0"}, "time": "2023-03-12T10:13:29+00:00"}, {"version": "1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "48f445a408c131e38cab1c235aa6d2bb7a0bb20d"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/48f445a408c131e38cab1c235aa6d2bb7a0bb20d", "type": "zip", "shasum": "", "reference": "48f445a408c131e38cab1c235aa6d2bb7a0bb20d"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.2"}, "time": "2022-10-14T12:47:21+00:00", "require": {"php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/extension-installer": "^1.1", "vimeo/psalm": "^4.25", "rector/rector": "^0.13.9"}}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "type": "zip", "shasum": "", "reference": "77a32518733312af16a44300404e945338981de3"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.1"}, "time": "2022-03-15T21:29:03+00:00", "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "93ebd0014cab80c4ea9f5e297ea48672f1b87706"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/93ebd0014cab80c4ea9f5e297ea48672f1b87706", "type": "zip", "shasum": "", "reference": "93ebd0014cab80c4ea9f5e297ea48672f1b87706"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.0"}, "time": "2022-01-04T19:58:01+00:00"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "a12f7e301eb7258bb68acd89d4aefa05c2906cae"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/a12f7e301eb7258bb68acd89d4aefa05c2906cae", "type": "zip", "shasum": "", "reference": "a12f7e301eb7258bb68acd89d4aefa05c2906cae"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.5.1"}, "time": "2021-10-02T14:08:47+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "30f38bffc6f24293dadd1823936372dfa9e86e2f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/30f38bffc6f24293dadd1823936372dfa9e86e2f", "type": "zip", "shasum": "", "reference": "30f38bffc6f24293dadd1823936372dfa9e86e2f"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.5.0"}, "time": "2021-09-17T15:28:14+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "type": "zip", "shasum": "", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.4.0"}, "time": "2020-09-17T18:55:26+00:00", "require-dev": {"ext-tokenizer": "*"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e878a14a65245fbe78f8080eba03b47c3b705651", "type": "zip", "shasum": "", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.x"}, "time": "2020-06-27T10:12:23+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "30441f2752e493c639526b215ed81d54f369d693"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/30441f2752e493c639526b215ed81d54f369d693", "type": "zip", "shasum": "", "reference": "30441f2752e493c639526b215ed81d54f369d693"}, "time": "2020-06-19T20:22:09+00:00", "require": {"php": "^7.2", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"mockery/mockery": "~1", "ext-tokenizer": "^7.2"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "7462d5f123dfc080dfdf26897032a6513644fc95"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/7462d5f123dfc080dfdf26897032a6513644fc95", "type": "zip", "shasum": "", "reference": "7462d5f123dfc080dfdf26897032a6513644fc95"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/master"}, "time": "2020-02-18T18:59:58+00:00", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/2e32a6d48972b2c1976ed5d8967145b6cec4a4a9", "type": "zip", "shasum": "", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/0.7.2"}, "time": "2019-08-22T18:11:29+00:00", "require": {"php": "^7.1", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"mockery/mockery": "~1", "ext-tokenizer": "^7.1", "phpunit/phpunit": "^7.0"}, "funding": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "4dfca962ce4d6c11ae17efcc65621cc4b22004c7"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/4dfca962ce4d6c11ae17efcc65621cc4b22004c7", "type": "zip", "shasum": "", "reference": "4dfca962ce4d6c11ae17efcc65621cc4b22004c7"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.0.0"}, "time": "2019-07-08T19:35:58+00:00"}, {"version": "0.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/2e32a6d48972b2c1976ed5d8967145b6cec4a4a9", "type": "zip", "shasum": "", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/0.7.2"}, "time": "2019-08-22T18:11:29+00:00"}, {"version": "0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "9c0562f9af69b06b002b017a3f0672bbfaba349f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c0562f9af69b06b002b017a3f0672bbfaba349f", "type": "zip", "shasum": "", "reference": "9c0562f9af69b06b002b017a3f0672bbfaba349f"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/master"}, "time": "2018-09-25T05:46:06+00:00", "extra": {"branch-alias": {"dev-master": "0.x-dev"}}, "require": {"php": ">=7.1", "phpdocumentor/reflection-common": "~2.0.0-beta1"}, "require-dev": {"mockery/mockery": "~1", "phpunit/phpunit": "~6"}}, {"version": "0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "804908512a82bbc7c952d5358ed1c8fd871766da"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/804908512a82bbc7c952d5358ed1c8fd871766da", "type": "zip", "shasum": "", "reference": "804908512a82bbc7c952d5358ed1c8fd871766da"}, "time": "2018-08-22T20:25:19+00:00"}, {"version": "0.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "f989f458daac6b90f2ebbcbc729e774caec493e4"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/f989f458daac6b90f2ebbcbc729e774caec493e4", "type": "zip", "shasum": "", "reference": "f989f458daac6b90f2ebbcbc729e774caec493e4"}, "time": "2018-08-08T14:13:08+00:00"}, {"description": "", "version": "0.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "e81ce9e82df06b49b2d0e0a2393a08955aeda05b"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e81ce9e82df06b49b2d0e0a2393a08955aeda05b", "type": "zip", "shasum": "", "reference": "e81ce9e82df06b49b2d0e0a2393a08955aeda05b"}, "time": "2018-06-14T12:29:58+00:00", "require": {"php": ">=7.1", "phpdocumentor/reflection-common": "^2"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^6.5"}}, {"version": "0.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "2870950c8eb446517e2e29acf8bbc209ed3ab0e4"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/2870950c8eb446517e2e29acf8bbc209ed3ab0e4", "type": "zip", "shasum": "", "reference": "2870950c8eb446517e2e29acf8bbc209ed3ab0e4"}, "time": "2018-02-12T14:33:22+00:00", "require": {"php": ">=7.1", "phpdocumentor/reflection-common": "^2.0"}}, {"version": "0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "6b6613f1aa77d42eaf8569feefe7b904e4b4acb2"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6b6613f1aa77d42eaf8569feefe7b904e4b4acb2", "type": "zip", "shasum": "", "reference": "6b6613f1aa77d42eaf8569feefe7b904e4b4acb2"}, "time": "2018-01-26T19:53:18+00:00", "extra": "__unset"}, {"version": "0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "cf842904952e64e703800d094cdf34e715a8a3ae"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/cf842904952e64e703800d094cdf34e715a8a3ae", "type": "zip", "shasum": "", "reference": "cf842904952e64e703800d094cdf34e715a8a3ae"}, "time": "2017-12-30T13:23:38+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"phpunit/phpunit": "^6.4", "mockery/mockery": "^1.0"}}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "7159da1ff4ab5de0c033bab91c9d3f52dd9a7a02"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/7159da1ff4ab5de0c033bab91c9d3f52dd9a7a02", "type": "zip", "shasum": "", "reference": "7159da1ff4ab5de0c033bab91c9d3f52dd9a7a02"}, "time": "2017-11-15T21:30:06+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c977708995954784726e25d0cd1dddf4e65b0f7", "type": "zip", "shasum": "", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "time": "2017-07-14T14:27:02+00:00", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"phpunit/phpunit": "^5.2||^4.8.24", "mockery/mockery": "^0.9.4"}}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "fb3933512008d8162b3cdf9e18dba9309b7c3773"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/fb3933512008d8162b3cdf9e18dba9309b7c3773", "type": "zip", "shasum": "", "reference": "fb3933512008d8162b3cdf9e18dba9309b7c3773"}, "time": "2017-06-03T08:32:36+00:00"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "e224fb2ea2fba6d3ad6fdaef91cd09a172155ccb"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e224fb2ea2fba6d3ad6fdaef91cd09a172155ccb", "type": "zip", "shasum": "", "reference": "e224fb2ea2fba6d3ad6fdaef91cd09a172155ccb"}, "time": "2016-11-25T06:54:22+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0"}}, {"version": "0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "b39c7a5b194f9ed7bd0dd345c751007a41862443"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/b39c7a5b194f9ed7bd0dd345c751007a41862443", "type": "zip", "shasum": "", "reference": "b39c7a5b194f9ed7bd0dd345c751007a41862443"}, "time": "2016-06-10T07:14:17+00:00"}, {"version": "0.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "9891754231e55d42f0d16988ffb799af39f31a12"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9891754231e55d42f0d16988ffb799af39f31a12", "type": "zip", "shasum": "", "reference": "9891754231e55d42f0d16988ffb799af39f31a12"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/0.1.8"}, "time": "2016-03-28T10:02:29+00:00", "require-dev": {"phpunit/phpunit": "^5.2", "mockery/mockery": "^0.9.4"}}, {"version": "0.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "c3a754741681739030adefe914b57c448e58a28a"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/c3a754741681739030adefe914b57c448e58a28a", "type": "zip", "shasum": "", "reference": "c3a754741681739030adefe914b57c448e58a28a"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/master"}, "time": "2016-03-19T17:53:07+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev"}, "require-dev": {"phpunit/phpunit": "^4.6", "mockery/mockery": "^0.9.4"}}, {"version": "0.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "60fe23665f018d608a7113acc9ab17a69fb87871"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/60fe23665f018d608a7113acc9ab17a69fb87871", "type": "zip", "shasum": "", "reference": "60fe23665f018d608a7113acc9ab17a69fb87871"}, "time": "2015-12-02T08:05:55+00:00"}, {"version": "0.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "83e31258fb03b9a27884a83b81501cb4cb297a81"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/83e31258fb03b9a27884a83b81501cb4cb297a81", "type": "zip", "shasum": "", "reference": "83e31258fb03b9a27884a83b81501cb4cb297a81"}, "time": "2015-07-18T13:58:32+00:00"}, {"version": "0.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "14bb2da6387c6a58648b4a87ff02d4c971050309"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/14bb2da6387c6a58648b4a87ff02d4c971050309", "type": "zip", "shasum": "", "reference": "14bb2da6387c6a58648b4a87ff02d4c971050309"}, "time": "2015-07-02T17:28:34+00:00"}, {"version": "0.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "25b5b40e1a7b9f57e953a1830fa04c38f7655ade"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/25b5b40e1a7b9f57e953a1830fa04c38f7655ade", "type": "zip", "shasum": "", "reference": "25b5b40e1a7b9f57e953a1830fa04c38f7655ade"}, "time": "2015-06-26T10:24:18+00:00"}, {"version": "0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "f49ac4864bf790ab72625b7183452ec41f3b675b"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/f49ac4864bf790ab72625b7183452ec41f3b675b", "type": "zip", "shasum": "", "reference": "f49ac4864bf790ab72625b7183452ec41f3b675b"}, "time": "2015-06-26T10:07:09+00:00"}, {"version": "0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "2ac0adaea9697334b85b1622ce930014e6b02a11"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/2ac0adaea9697334b85b1622ce930014e6b02a11", "type": "zip", "shasum": "", "reference": "2ac0adaea9697334b85b1622ce930014e6b02a11"}, "time": "2015-06-12T22:00:00+00:00", "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^0.1"}}, {"version": "0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/TypeResolver.git", "type": "git", "reference": "e99540d3c74b6336923454da60054a2838ca78a3"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e99540d3c74b6336923454da60054a2838ca78a3", "type": "zip", "shasum": "", "reference": "e99540d3c74b6336923454da60054a2838ca78a3"}, "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/0.1"}, "time": "2015-06-12T18:03:13+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 09 Nov 2024 15:14:23 GMT"}