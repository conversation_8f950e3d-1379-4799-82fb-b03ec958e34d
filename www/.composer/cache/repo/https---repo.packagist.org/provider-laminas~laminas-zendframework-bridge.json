{"minified": "composer/2.0", "packages": {"laminas/laminas-zendframework-bridge": [{"name": "laminas/laminas-zendframework-bridge", "description": "Alias legacy ZF class names to Laminas Project equivalents.", "keywords": ["ZendFramework", "zf", "autoloading", "laminas"], "homepage": "", "version": "1.8.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [], "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "eb0d96c708b92177a92bc2239543d3ed523452c6"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/eb0d96c708b92177a92bc2239543d3ed523452c6", "type": "zip", "shasum": "", "reference": "eb0d96c708b92177a92bc2239543d3ed523452c6"}, "type": "library", "support": {"forum": "https://discourse.laminas.dev/", "issues": "https://github.com/laminas/laminas-zendframework-bridge/issues", "rss": "https://github.com/laminas/laminas-zendframework-bridge/releases.atom", "source": "https://github.com/laminas/laminas-zendframework-bridge"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-11-24T13:56:19+00:00", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ZendFrameworkBridge\\": "src//"}}, "extra": {"laminas": {"module": "Laminas\\ZendFrameworkBridge"}}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"phpunit/phpunit": "^10.4", "psalm/plugin-phpunit": "^0.18.0", "squizlabs/php_codesniffer": "^3.7.1", "vimeo/psalm": "^5.16.0"}, "abandoned": true}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "5ef52e26392777a26dbb8f20fe24f91b406459f6"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/5ef52e26392777a26dbb8f20fe24f91b406459f6", "type": "zip", "shasum": "", "reference": "5ef52e26392777a26dbb8f20fe24f91b406459f6"}, "time": "2022-12-12T11:44:10+00:00", "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "^0.18.0", "squizlabs/php_codesniffer": "^3.7.1", "vimeo/psalm": "^4.29.0"}}, {"version": "1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "e112dd2c099f4f6142c16fc65fda89a638e06885"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/e112dd2c099f4f6142c16fc65fda89a638e06885", "type": "zip", "shasum": "", "reference": "e112dd2c099f4f6142c16fc65fda89a638e06885"}, "time": "2022-07-29T13:28:29+00:00", "require": {"php": ">=7.4, <8.2"}, "require-dev": {"phpunit/phpunit": "^9.5.14", "psalm/plugin-phpunit": "^0.15.2", "squizlabs/php_codesniffer": "^3.6.2", "vimeo/psalm": "^4.21.0"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "d74d2da21beae5aceff1e8c07b901066b0e1b719"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/d74d2da21beae5aceff1e8c07b901066b0e1b719", "type": "zip", "shasum": "", "reference": "d74d2da21beae5aceff1e8c07b901066b0e1b719"}, "time": "2022-07-14T16:30:30+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "7f049390b756d34ba5940a8fb47634fbb51f79ab"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/7f049390b756d34ba5940a8fb47634fbb51f79ab", "type": "zip", "shasum": "", "reference": "7f049390b756d34ba5940a8fb47634fbb51f79ab"}, "time": "2022-02-22T22:17:01+00:00"}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "88bf037259869891afce6504cacc4f8a07b24d0f"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/88bf037259869891afce6504cacc4f8a07b24d0f", "type": "zip", "shasum": "", "reference": "88bf037259869891afce6504cacc4f8a07b24d0f"}, "time": "2021-12-21T14:34:37+00:00", "require": {"php": "^7.3 || ~8.0.0 || ~8.1.0"}, "require-dev": {"phpunit/phpunit": "^9.3", "psalm/plugin-phpunit": "^0.15.1", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.6"}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "bf180a382393e7db5c1e8d0f2ec0c4af9c724baf"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/bf180a382393e7db5c1e8d0f2ec0c4af9c724baf", "type": "zip", "shasum": "", "reference": "bf180a382393e7db5c1e8d0f2ec0c4af9c724baf"}, "time": "2021-09-03T17:53:30+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "13af2502d9bb6f7d33be2de4b51fb68c6cdb476e"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/13af2502d9bb6f7d33be2de4b51fb68c6cdb476e", "type": "zip", "shasum": "", "reference": "13af2502d9bb6f7d33be2de4b51fb68c6cdb476e"}, "time": "2021-06-24T12:49:22+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.1 || ^9.3", "psalm/plugin-phpunit": "^0.15.1", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.6"}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "9aaa3fde4a184c53f893d938ea9d3f9375cd0622"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/9aaa3fde4a184c53f893d938ea9d3f9375cd0622", "type": "zip", "shasum": "", "reference": "9aaa3fde4a184c53f893d938ea9d3f9375cd0622"}, "time": "2021-06-24T10:54:07+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "6cccbddfcfc742eb02158d6137ca5687d92cee32"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/6cccbddfcfc742eb02158d6137ca5687d92cee32", "type": "zip", "shasum": "", "reference": "6cccbddfcfc742eb02158d6137ca5687d92cee32"}, "time": "2021-02-25T21:54:58+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "6ede70583e101030bcace4dcddd648f760ddf642"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/6ede70583e101030bcace4dcddd648f760ddf642", "type": "zip", "shasum": "", "reference": "6ede70583e101030bcace4dcddd648f760ddf642"}, "time": "2020-09-14T14:23:00+00:00", "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.1 || ^9.3", "squizlabs/php_codesniffer": "^3.5"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "4939c81f63a8a4968c108c440275c94955753b19"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/4939c81f63a8a4968c108c440275c94955753b19", "type": "zip", "shasum": "", "reference": "4939c81f63a8a4968c108c440275c94955753b19"}, "time": "2020-08-18T16:34:51+00:00", "extra": {"laminas": {"module": "Laminas\\ZendFrameworkBridge"}, "branch-alias": {"dev-master": "1.0.x-dev", "dev-develop": "1.1.x-dev"}}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "fcd87520e4943d968557803919523772475e8ea3"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/fcd87520e4943d968557803919523772475e8ea3", "type": "zip", "shasum": "", "reference": "fcd87520e4943d968557803919523772475e8ea3"}, "time": "2020-05-20T16:45:56+00:00", "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.1", "squizlabs/php_codesniffer": "^3.5"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "bfbbdb6c998d50dbf69d2187cb78a5f1fa36e1e9"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/bfbbdb6c998d50dbf69d2187cb78a5f1fa36e1e9", "type": "zip", "shasum": "", "reference": "bfbbdb6c998d50dbf69d2187cb78a5f1fa36e1e9"}, "time": "2020-04-03T16:01:00+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "faf68f6109ceeff24241226033ab59640c7eb63b"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/faf68f6109ceeff24241226033ab59640c7eb63b", "type": "zip", "shasum": "", "reference": "faf68f6109ceeff24241226033ab59640c7eb63b"}, "funding": [], "time": "2020-03-26T16:07:12+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "0fb9675b84a1666ab45182b6c5b29956921e818d"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/0fb9675b84a1666ab45182b6c5b29956921e818d", "type": "zip", "shasum": "", "reference": "0fb9675b84a1666ab45182b6c5b29956921e818d"}, "support": {"docs": "https://docs.laminas.dev/laminas-zendframework-bridge/", "forum": "https://discourse.laminas.dev/", "issues": "https://github.com/laminas/laminas-zendframework-bridge/issues", "rss": "https://github.com/laminas/laminas-zendframework-bridge/releases.atom", "source": "https://github.com/laminas/laminas-zendframework-bridge"}, "time": "2020-01-07T22:58:31+00:00", "funding": "__unset"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "32d7095e436a31b8d98e485a5c63d70df74915a8"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/32d7095e436a31b8d98e485a5c63d70df74915a8", "type": "zip", "shasum": "", "reference": "32d7095e436a31b8d98e485a5c63d70df74915a8"}, "time": "2019-12-31T15:24:03+00:00"}, {"version": "0.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "bb83db06ec80e683a9f8bb5913eea41df0405566"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/bb83db06ec80e683a9f8bb5913eea41df0405566", "type": "zip", "shasum": "", "reference": "bb83db06ec80e683a9f8bb5913eea41df0405566"}, "time": "2019-12-23T17:21:43+00:00", "extra": {"laminas": {"module": "Laminas\\ZendFrameworkBridge"}, "branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "0.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "19a9a914eaae5fb7904ab73e603b0fdda77332a9"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/19a9a914eaae5fb7904ab73e603b0fdda77332a9", "type": "zip", "shasum": "", "reference": "19a9a914eaae5fb7904ab73e603b0fdda77332a9"}, "time": "2019-12-18T17:34:48+00:00"}, {"version": "0.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "40eafa41bc3404f3ca8ad598b997e6de2880da14"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/40eafa41bc3404f3ca8ad598b997e6de2880da14", "type": "zip", "shasum": "", "reference": "40eafa41bc3404f3ca8ad598b997e6de2880da14"}, "time": "2019-12-17T23:10:47+00:00"}, {"version": "0.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "bfd38fbf767648441ddcca7f00b0a3961a1656d1"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/bfd38fbf767648441ddcca7f00b0a3961a1656d1", "type": "zip", "shasum": "", "reference": "bfd38fbf767648441ddcca7f00b0a3961a1656d1"}, "time": "2019-12-16T20:56:25+00:00"}, {"version": "0.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "a161721d6c6d52331024853855add807efcdd95f"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/a161721d6c6d52331024853855add807efcdd95f", "type": "zip", "shasum": "", "reference": "a161721d6c6d52331024853855add807efcdd95f"}, "time": "2019-12-10T22:33:39+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "9c43a4d23a1082d8fb4a588b5dab36c61cd20fb6"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/9c43a4d23a1082d8fb4a588b5dab36c61cd20fb6", "type": "zip", "shasum": "", "reference": "9c43a4d23a1082d8fb4a588b5dab36c61cd20fb6"}, "time": "2019-11-27T15:01:24+00:00"}, {"version": "0.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "706cb6b2b548fe7ce2cc1dce51b6c92f8f562043"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/706cb6b2b548fe7ce2cc1dce51b6c92f8f562043", "type": "zip", "shasum": "", "reference": "706cb6b2b548fe7ce2cc1dce51b6c92f8f562043"}, "time": "2019-11-14T15:47:19+00:00"}, {"version": "0.3.7", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "6524baeca2a44a7e1423254775f5c41873d9c358"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/6524baeca2a44a7e1423254775f5c41873d9c358", "type": "zip", "shasum": "", "reference": "6524baeca2a44a7e1423254775f5c41873d9c358"}, "time": "2019-11-12T18:22:37+00:00", "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.1"}}, {"version": "0.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "8be2ff5d8b35c2f815e59602244c540b41915fa5"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/8be2ff5d8b35c2f815e59602244c540b41915fa5", "type": "zip", "shasum": "", "reference": "8be2ff5d8b35c2f815e59602244c540b41915fa5"}, "time": "2019-11-07T17:34:08+00:00"}, {"version": "0.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "b3dae63df6c9f4b7172dc7f41a9563dbd532e6cb"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/b3dae63df6c9f4b7172dc7f41a9563dbd532e6cb", "type": "zip", "shasum": "", "reference": "b3dae63df6c9f4b7172dc7f41a9563dbd532e6cb"}, "time": "2019-11-06T20:35:09+00:00"}, {"version": "0.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "791842b9a5ab903a900793b6aaa25a196baa5200"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/791842b9a5ab903a900793b6aaa25a196baa5200", "type": "zip", "shasum": "", "reference": "791842b9a5ab903a900793b6aaa25a196baa5200"}, "time": "2019-11-06T20:17:43+00:00"}, {"version": "0.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "3a02de0fa162706f352a8fcd231cf0387ed810e6"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/3a02de0fa162706f352a8fcd231cf0387ed810e6", "type": "zip", "shasum": "", "reference": "3a02de0fa162706f352a8fcd231cf0387ed810e6"}, "time": "2019-11-06T15:48:01+00:00"}, {"version": "0.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "3d1b1b3d46b6feab0f2838355da318f88bd48936"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/3d1b1b3d46b6feab0f2838355da318f88bd48936", "type": "zip", "shasum": "", "reference": "3d1b1b3d46b6feab0f2838355da318f88bd48936"}, "time": "2019-10-30T14:46:51+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": "^5.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.1"}}, {"version": "0.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "8fd4a33bc68e311d03588fa8a2d2925d4ae2075b"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/8fd4a33bc68e311d03588fa8a2d2925d4ae2075b", "type": "zip", "shasum": "", "reference": "8fd4a33bc68e311d03588fa8a2d2925d4ae2075b"}, "time": "2019-04-25T21:25:36+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "0d159d8d7580e78b8a20b317e6c669cf11a14e04"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/0d159d8d7580e78b8a20b317e6c669cf11a14e04", "type": "zip", "shasum": "", "reference": "0d159d8d7580e78b8a20b317e6c669cf11a14e04"}, "time": "2019-04-12T16:08:13+00:00"}, {"version": "0.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "d00009279a100c148000cd1b463708d9e4862e8e"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/d00009279a100c148000cd1b463708d9e4862e8e", "type": "zip", "shasum": "", "reference": "d00009279a100c148000cd1b463708d9e4862e8e"}, "time": "2019-04-11T22:32:19+00:00"}, {"version": "0.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "9c63d49aa8477e7d7b8399bdb5286a6d9c72a21f"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/9c63d49aa8477e7d7b8399bdb5286a6d9c72a21f", "type": "zip", "shasum": "", "reference": "9c63d49aa8477e7d7b8399bdb5286a6d9c72a21f"}, "time": "2019-04-11T17:43:16+00:00", "require-dev": "__unset"}, {"version": "0.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "de5e823dbcf574aa8b091e2b07ee8dba466f1272"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/de5e823dbcf574aa8b091e2b07ee8dba466f1272", "type": "zip", "shasum": "", "reference": "de5e823dbcf574aa8b091e2b07ee8dba466f1272"}, "time": "2019-04-10T21:37:09+00:00"}, {"version": "0.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "82fa963da2a23e560a454c642c734527b2da2052"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/82fa963da2a23e560a454c642c734527b2da2052", "type": "zip", "shasum": "", "reference": "82fa963da2a23e560a454c642c734527b2da2052"}, "time": "2019-04-10T19:06:32+00:00"}, {"version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "6f38312df08f5e521818c9d16b524945fa48f5a1"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/6f38312df08f5e521818c9d16b524945fa48f5a1", "type": "zip", "shasum": "", "reference": "6f38312df08f5e521818c9d16b524945fa48f5a1"}, "time": "2019-04-10T19:01:56+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "c9a1b5d356f14c52a128bb08470912aef15fc54d"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/c9a1b5d356f14c52a128bb08470912aef15fc54d", "type": "zip", "shasum": "", "reference": "c9a1b5d356f14c52a128bb08470912aef15fc54d"}, "time": "2019-04-01T14:43:35+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/laminas/laminas-zendframework-bridge.git", "type": "git", "reference": "2410efee2545629707ff48f0f2cc90886fd28e06"}, "dist": {"url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/2410efee2545629707ff48f0f2cc90886fd28e06", "type": "zip", "shasum": "", "reference": "2410efee2545629707ff48f0f2cc90886fd28e06"}, "time": "2019-03-27T15:46:00+00:00", "autoload": {"files": ["src/autoload.php", "src/autoload-functions-containerconfigtest.php", "src/autoload-functions-diactoros.php", "src/autoload-functions-stratigility.php"]}, "require": {"php": "^7.1"}}]}, "security-advisories": [], "last-modified": "Mon, 27 Jan 2025 03:35:28 GMT"}