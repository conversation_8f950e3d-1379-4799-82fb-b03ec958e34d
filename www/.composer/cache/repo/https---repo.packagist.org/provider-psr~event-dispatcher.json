{"minified": "composer/2.0", "packages": {"psr/event-dispatcher": [{"name": "psr/event-dispatcher", "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "homepage": "", "version": "1.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "source": {"url": "https://github.com/php-fig/event-dispatcher.git", "type": "git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "type": "zip", "shasum": "", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "type": "library", "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=7.2.0"}}, {"version": "0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/event-dispatcher.git", "type": "git", "reference": "8380006c4192e22528ce40c3a56b81590707128a"}, "dist": {"url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/8380006c4192e22528ce40c3a56b81590707128a", "type": "zip", "shasum": "", "reference": "8380006c4192e22528ce40c3a56b81590707128a"}, "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/master"}, "time": "2018-12-26T17:32:27+00:00"}, {"version": "0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/event-dispatcher.git", "type": "git", "reference": "14a25ea9045cfc34c9edc31ad468a5cc6e914a8c"}, "dist": {"url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/14a25ea9045cfc34c9edc31ad468a5cc6e914a8c", "type": "zip", "shasum": "", "reference": "14a25ea9045cfc34c9edc31ad468a5cc6e914a8c"}, "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/0.6.0"}, "time": "2018-09-27T17:52:39+00:00", "require": {"php": ">=7.1.0"}}, {"version": "0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/event-dispatcher.git", "type": "git", "reference": "3a8ccc0acc46f76270726cbed7f9a84cce47c12c"}, "dist": {"url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/3a8ccc0acc46f76270726cbed7f9a84cce47c12c", "type": "zip", "shasum": "", "reference": "3a8ccc0acc46f76270726cbed7f9a84cce47c12c"}, "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/0.5.0"}, "time": "2018-09-15T17:00:24+00:00"}, {"version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/event-dispatcher.git", "type": "git", "reference": "909879458094ebe1491f04de5782366e056b66e7"}, "dist": {"url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/909879458094ebe1491f04de5782366e056b66e7", "type": "zip", "shasum": "", "reference": "909879458094ebe1491f04de5782366e056b66e7"}, "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/0.4.0"}, "time": "2018-08-20T08:14:46+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/php-fig/event-dispatcher.git", "type": "git", "reference": "ba7f8a4012b3527dcbb194ed6acf5470b6b484d3"}, "dist": {"url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/ba7f8a4012b3527dcbb194ed6acf5470b6b484d3", "type": "zip", "shasum": "", "reference": "ba7f8a4012b3527dcbb194ed6acf5470b6b484d3"}, "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/alpha3"}, "time": "2018-08-03T04:05:55+00:00", "autoload": {"psr-4": {"Psr\\Event\\Dispatcher\\": "src/"}}}]}, "security-advisories": [], "last-modified": "Wed, 17 Apr 2024 21:40:54 GMT"}