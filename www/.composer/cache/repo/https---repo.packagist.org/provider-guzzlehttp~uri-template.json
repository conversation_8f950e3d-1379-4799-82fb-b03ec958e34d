{"minified": "composer/2.0", "packages": {"guzzlehttp/uri-template": [{"name": "guzzlehttp/uri-template", "description": "A polyfill class for uri_template of PHP", "keywords": ["uri-template", "guzzlehttp"], "homepage": "", "version": "v1.0.4", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "30e286560c137526eccd4ce21b2de477ab0676d2"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/30e286560c137526eccd4ce21b2de477ab0676d2", "type": "zip", "shasum": "", "reference": "30e286560c137526eccd4ce21b2de477ab0676d2"}, "type": "library", "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "time": "2025-02-03T10:55:03+00:00", "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/ecea8feef63bd4fef1f037ecb288386999ecc11c", "type": "zip", "shasum": "", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c"}, "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.3"}, "time": "2023-12-03T19:50:20+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "61bf437fc2197f587f6857d3ff903a24f1731b5d"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/61bf437fc2197f587f6857d3ff903a24f1731b5d", "type": "zip", "shasum": "", "reference": "61bf437fc2197f587f6857d3ff903a24f1731b5d"}, "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.2"}, "time": "2023-08-27T10:19:19+00:00", "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.19 || ^9.5.8", "uri-template/tests": "1.0.0"}, "extra": "__unset"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "b945d74a55a25a949158444f09ec0d3c120d69e2"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/b945d74a55a25a949158444f09ec0d3c120d69e2", "type": "zip", "shasum": "", "reference": "b945d74a55a25a949158444f09ec0d3c120d69e2"}, "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.1"}, "time": "2021-10-07T12:57:01+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require-dev": {"phpunit/phpunit": "^8.5.19 || ^9.5.8", "uri-template/tests": "1.0.0"}}, {"homepage": "https://github.com/guzzlehttp/uri-template", "version": "v1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos", "role": "Developer"}], "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "88fcf8a3ea7489a8af6b25c9dfd3f688ddb51966"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/88fcf8a3ea7489a8af6b25c9dfd3f688ddb51966", "type": "zip", "shasum": "", "reference": "88fcf8a3ea7489a8af6b25c9dfd3f688ddb51966"}, "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/gmponos", "type": "github"}], "time": "2021-08-14T22:34:51+00:00"}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "db46525d6d8fee71033b73cc07160f3e5271a8ce"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/db46525d6d8fee71033b73cc07160f3e5271a8ce", "type": "zip", "shasum": "", "reference": "db46525d6d8fee71033b73cc07160f3e5271a8ce"}, "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/master"}, "time": "2020-07-21T13:45:09+00:00", "require": {"php": "^7.1 || ^8.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3", "uri-template/tests": "1.0.0"}}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "5268fefbf5e297ec8c273e1c1bb73f73afbb4277"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/5268fefbf5e297ec8c273e1c1bb73f73afbb4277", "type": "zip", "shasum": "", "reference": "5268fefbf5e297ec8c273e1c1bb73f73afbb4277"}, "funding": [], "time": "2020-06-30T08:59:00+00:00", "autoload": {"psr-4": {"GuzzleHttp\\Utility\\": "src"}}, "require": {"php": "^7.2"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/guzzle/uri-template.git", "type": "git", "reference": "081ca179294bef55fd3ad5d97de627949fc1297d"}, "dist": {"url": "https://api.github.com/repos/guzzle/uri-template/zipball/081ca179294bef55fd3ad5d97de627949fc1297d", "type": "zip", "shasum": "", "reference": "081ca179294bef55fd3ad5d97de627949fc1297d"}, "time": "2020-06-30T06:14:54+00:00"}]}, "security-advisories": [], "last-modified": "Mon, 03 Feb 2025 10:57:55 GMT"}