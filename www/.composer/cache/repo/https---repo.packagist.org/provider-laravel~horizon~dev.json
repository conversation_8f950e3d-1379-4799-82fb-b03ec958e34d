{"minified": "composer/2.0", "packages": {"laravel/horizon": [{"name": "laravel/horizon", "description": "Dashboard and code-driven configuration for Laravel queues.", "keywords": ["queue", "laravel"], "homepage": "", "version": "dev-master", "version_normalized": "dev-master", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "bc7fc71414376c71bfc6b0c98c5c2eb079992e1b"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/bc7fc71414376c71bfc6b0c98c5c2eb079992e1b", "type": "zip", "shasum": "", "reference": "bc7fc71414376c71bfc6b0c98c5c2eb079992e1b"}, "type": "library", "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/master"}, "funding": [], "time": "2025-05-23T14:34:38+00:00", "autoload": {"psr-4": {"Laravel\\Horizon\\": "src/"}}, "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "6.x-dev"}}, "require": {"ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "ramsey/uuid": "^4.0", "php": "^8.0", "illuminate/contracts": "^9.21|^10.0|^11.0|^12.0", "illuminate/queue": "^9.21|^10.0|^11.0|^12.0", "illuminate/support": "^9.21|^10.0|^11.0|^12.0", "nesbot/carbon": "^2.17|^3.0", "symfony/console": "^6.0|^7.0", "symfony/error-handler": "^6.0|^7.0", "symfony/polyfill-php83": "^1.28", "symfony/process": "^6.0|^7.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^7.0|^8.0|^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0|^10.4|^11.5", "predis/predis": "^1.1|^2.0"}, "suggest": {"ext-redis": "Required to use the Redis PHP driver.", "predis/predis": "Required when not using the Redis PHP driver (^1.1|^2.0)."}}, {"version": "5.x-dev", "version_normalized": "5.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "d9b8a10abadcb6c00bd576d1ec7bec2bd19f171f"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/d9b8a10abadcb6c00bd576d1ec7bec2bd19f171f", "type": "zip", "shasum": "", "reference": "d9b8a10abadcb6c00bd576d1ec7bec2bd19f171f"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/5.x"}, "time": "2025-06-10T15:08:57+00:00", "default-branch": true, "require": {"ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "ramsey/uuid": "^4.0", "nesbot/carbon": "^2.17|^3.0", "php": "^8.0", "symfony/process": "^6.0|^7.0", "symfony/error-handler": "^6.0|^7.0", "symfony/console": "^6.0|^7.0", "symfony/polyfill-php83": "^1.28", "illuminate/contracts": "^9.21|^10.0|^11.0|^12.0", "illuminate/queue": "^9.21|^10.0|^11.0|^12.0", "illuminate/support": "^9.21|^10.0|^11.0|^12.0"}, "require-dev": {"mockery/mockery": "^1.0", "predis/predis": "^1.1|^2.0", "phpstan/phpstan": "^1.10", "orchestra/testbench": "^7.0|^8.0|^9.0|^10.0", "phpunit/phpunit": "^9.0|^10.4|^11.5"}}, {"version": "4.x-dev", "version_normalized": "4.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "16581058295e74b5ae5cd1da12b93b6f913f6cde"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/16581058295e74b5ae5cd1da12b93b6f913f6cde", "type": "zip", "shasum": "", "reference": "16581058295e74b5ae5cd1da12b93b6f913f6cde"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/4.x"}, "time": "2025-01-27T00:31:17+00:00", "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "4.x-dev"}}, "require": {"php": "^7.2", "ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "illuminate/contracts": "^7.0", "illuminate/queue": "^7.0", "illuminate/support": "^7.0", "symfony/process": "^5.0", "symfony/error-handler": "^5.0", "ramsey/uuid": "^3.5|^4.0", "cakephp/chronos": "^2.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^5.0", "phpunit/phpunit": "^8.0", "predis/predis": "^1.1"}, "suggest": {"ext-redis": "Required to use the Redis PHP driver.", "predis/predis": "Required when not using the Redis PHP driver (^1.1)."}, "default-branch": "__unset"}, {"version": "3.0.x-dev", "version_normalized": "3.0.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "df7adc1ddacbfed64a1a8dbca6e9cd1b55bbb480"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/df7adc1ddacbfed64a1a8dbca6e9cd1b55bbb480", "type": "zip", "shasum": "", "reference": "df7adc1ddacbfed64a1a8dbca6e9cd1b55bbb480"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/3.0"}, "time": "2020-03-02T13:35:43+00:00", "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "3.0-dev"}}, "require": {"php": ">=7.1.0", "ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "cakephp/chronos": "^1.0", "predis/predis": "^1.1", "ramsey/uuid": "^3.5", "symfony/process": "^4.2", "symfony/debug": "^4.2", "illuminate/contracts": "~5.7.0|~5.8.0|^6.0", "illuminate/queue": "~5.7.0|~5.8.0|^6.0", "illuminate/support": "~5.7.0|~5.8.0|^6.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0|^8.0", "orchestra/testbench": "^3.7|^4.0"}, "suggest": "__unset"}, {"version": "2.0.x-dev", "version_normalized": "2.0.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "53bbdd4e28c29b4e5ebca127651483aacc9d9a7f"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/53bbdd4e28c29b4e5ebca127651483aacc9d9a7f", "type": "zip", "shasum": "", "reference": "53bbdd4e28c29b4e5ebca127651483aacc9d9a7f"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/2.0"}, "time": "2019-04-18T20:08:02+00:00", "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": ">=7.1.0", "ext-pcntl": "*", "ext-posix": "*", "cakephp/chronos": "^1.0", "predis/predis": "^1.1", "ramsey/uuid": "^3.5", "symfony/debug": "~3.3|~4.0", "illuminate/contracts": "~5.5.0|~5.6.0|~5.7.0|~5.8.0", "illuminate/queue": "~5.5.0|~5.6.0|~5.7.0|~5.8.0", "illuminate/support": "~5.5.0|~5.6.0|~5.7.0|~5.8.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/database": "^3.7", "orchestra/testbench": "^3.7", "phpunit/phpunit": "^7.0"}, "funding": "__unset"}, {"version": "1.0.x-dev", "version_normalized": "1.0.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "d1c2f86353010e4a9557047c1e3f69aeedbacf66"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/d1c2f86353010e4a9557047c1e3f69aeedbacf66", "type": "zip", "shasum": "", "reference": "d1c2f86353010e4a9557047c1e3f69aeedbacf66"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/1.0"}, "time": "2019-04-18T20:09:40+00:00", "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=7.1.0", "ext-pcntl": "*", "ext-posix": "*", "cakephp/chronos": "^1.0", "illuminate/contracts": "~5.5", "illuminate/queue": "~5.5", "illuminate/support": "~5.5", "predis/predis": "^1.1", "ramsey/uuid": "^3.5", "symfony/debug": "~3.3|~4.0"}, "require-dev": {"orchestra/database": "~3.5", "orchestra/testbench": "~3.5", "phpunit/phpunit": "~6.0", "mockery/mockery": "~1.0"}}, {"version": "dev-polling", "version_normalized": "dev-polling", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "308ed73ec14d0220b28b39dd576c1dba584efb8d"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/308ed73ec14d0220b28b39dd576c1dba584efb8d", "type": "zip", "shasum": "", "reference": "308ed73ec14d0220b28b39dd576c1dba584efb8d"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/polling"}, "funding": [], "time": "2025-06-09T17:41:56+00:00", "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "6.x-dev"}}, "require": {"php": "^8.0", "ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "illuminate/contracts": "^9.21|^10.0|^11.0|^12.0", "illuminate/queue": "^9.21|^10.0|^11.0|^12.0", "illuminate/support": "^9.21|^10.0|^11.0|^12.0", "nesbot/carbon": "^2.17|^3.0", "ramsey/uuid": "^4.0", "symfony/console": "^6.0|^7.0", "symfony/error-handler": "^6.0|^7.0", "symfony/polyfill-php83": "^1.28", "symfony/process": "^6.0|^7.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^7.0|^8.0|^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0|^10.4|^11.5", "predis/predis": "^1.1|^2.0"}, "suggest": {"ext-redis": "Required to use the Redis PHP driver.", "predis/predis": "Required when not using the Redis PHP driver (^1.1|^2.0)."}}, {"version": "dev-feature-1310", "version_normalized": "dev-feature-1310", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "bea664a3cd78eee9729ede94713b1f4edc98b738"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/bea664a3cd78eee9729ede94713b1f4edc98b738", "type": "zip", "shasum": "", "reference": "bea664a3cd78eee9729ede94713b1f4edc98b738"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/feature-1310"}, "time": "2023-09-06T15:56:19+00:00", "extra": {"laravel": {"aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}, "providers": ["Laravel\\Horizon\\HorizonServiceProvider"]}, "branch-alias": {"dev-master": "5.x-dev"}}, "require": {"php": "^7.3|^8.0", "ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "illuminate/contracts": "^8.17|^9.0|^10.0", "illuminate/queue": "^8.17|^9.0|^10.0", "illuminate/support": "^8.17|^9.0|^10.0", "nesbot/carbon": "^2.17", "ramsey/uuid": "^4.0", "symfony/process": "^5.0|^6.0", "symfony/error-handler": "^5.0|^6.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0", "predis/predis": "^1.1|^2.0"}}, {"version": "dev-fix/tags-count", "version_normalized": "dev-fix/tags-count", "source": {"url": "https://github.com/laravel/horizon.git", "type": "git", "reference": "9749e5419a3c110f4c41b970608b68f2028d6757"}, "dist": {"url": "https://api.github.com/repos/laravel/horizon/zipball/9749e5419a3c110f4c41b970608b68f2028d6757", "type": "zip", "shasum": "", "reference": "9749e5419a3c110f4c41b970608b68f2028d6757"}, "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/fix/tags-count"}, "time": "2023-05-16T17:53:00+00:00"}]}, "last-modified": "<PERSON><PERSON>, 10 Jun 2025 15:09:05 GMT"}