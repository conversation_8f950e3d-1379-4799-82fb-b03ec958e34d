{"minified": "composer/2.0", "packages": {"symfony/polyfill-intl-grapheme": [{"name": "symfony/polyfill-intl-grapheme", "description": "Symfony polyfill for intl's grapheme_* functions", "keywords": ["intl", "compatibility", "portable", "grapheme", "polyfill", "shim"], "homepage": "https://symfony.com", "version": "v1.32.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "type": "zip", "shasum": "", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}}, {"version": "v1.31.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64647a7c30b2283f5d49b874d84a18fc22054b7a", "type": "zip", "shasum": "", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "type": "zip", "shasum": "", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "875e90aeea2777b6f135677f618529449334a612"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/875e90aeea2777b6f135677f618529449334a612", "type": "zip", "shasum": "", "reference": "875e90aeea2777b6f135677f618529449334a612"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.28.0"}, "time": "2023-01-26T09:26:14+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "511a08c03c1960e08a883f4cffcacd219b758354"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/511a08c03c1960e08a883f4cffcacd219b758354", "type": "zip", "shasum": "", "reference": "511a08c03c1960e08a883f4cffcacd219b758354"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "433d05519ce6990bf3530fba6957499d327395c2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/433d05519ce6990bf3530fba6957499d327395c2", "type": "zip", "shasum": "", "reference": "433d05519ce6990bf3530fba6957499d327395c2"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "81b86b50cf841a64252b439e738e97f4a34e2783"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/81b86b50cf841a64252b439e738e97f4a34e2783", "type": "zip", "shasum": "", "reference": "81b86b50cf841a64252b439e738e97f4a34e2783"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.25.0"}, "time": "2021-11-23T21:10:46+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.24.0"}}, {"version": "v1.23.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "16880ba9c5ebe3642d1995ab866db29270b36535"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/16880ba9c5ebe3642d1995ab866db29270b36535", "type": "zip", "shasum": "", "reference": "16880ba9c5ebe3642d1995ab866db29270b36535"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.23.1"}, "time": "2021-05-27T12:26:48+00:00"}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "24b72c6baa32c746a4d0840147c9715e42bb68ab"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/24b72c6baa32c746a4d0840147c9715e42bb68ab", "type": "zip", "shasum": "", "reference": "24b72c6baa32c746a4d0840147c9715e42bb68ab"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.23.0"}, "time": "2021-05-27T09:17:38+00:00"}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "5601e09b69f26c1828b13b6bb87cb07cddba3170"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/5601e09b69f26c1828b13b6bb87cb07cddba3170", "type": "zip", "shasum": "", "reference": "5601e09b69f26c1828b13b6bb87cb07cddba3170"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.22.1"}, "time": "2021-01-22T09:19:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "267a9adeb8ecb8071040a740930e077cdfb987af"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/267a9adeb8ecb8071040a740930e077cdfb987af", "type": "zip", "shasum": "", "reference": "267a9adeb8ecb8071040a740930e077cdfb987af"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.22.0"}, "time": "2021-01-07T16:49:33+00:00"}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "c7cf3f858ec7d70b89559d6e6eb1f7c2517d479c"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/c7cf3f858ec7d70b89559d6e6eb1f7c2517d479c", "type": "zip", "shasum": "", "reference": "c7cf3f858ec7d70b89559d6e6eb1f7c2517d479c"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "64fbe93b02024763359aea2bc81af05086c6af82"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64fbe93b02024763359aea2bc81af05086c6af82", "type": "zip", "shasum": "", "reference": "64fbe93b02024763359aea2bc81af05086c6af82"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.19.0"}, "time": "2020-10-23T09:01:57+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "b740103edbdcc39602239ee8860f0f45a8eb9aa5"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b740103edbdcc39602239ee8860f0f45a8eb9aa5", "type": "zip", "shasum": "", "reference": "b740103edbdcc39602239ee8860f0f45a8eb9aa5"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.18.1"}, "time": "2020-07-14T12:35:20+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/master"}}, {"version": "v1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "6e4dbcf5e81eba86e36731f94fe56b1726835846"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/6e4dbcf5e81eba86e36731f94fe56b1726835846", "type": "zip", "shasum": "", "reference": "6e4dbcf5e81eba86e36731f94fe56b1726835846"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.17.1"}, "time": "2020-06-06T08:46:27+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "e094b0770f7833fdf257e6ba4775be4e258230b2"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/e094b0770f7833fdf257e6ba4775be4e258230b2", "type": "zip", "shasum": "", "reference": "e094b0770f7833fdf257e6ba4775be4e258230b2"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.17.0"}, "time": "2020-05-12T16:47:27+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "81f5385a0237989ec43a5ed2d94176c5c9616cc8"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/81f5385a0237989ec43a5ed2d94176c5c9616cc8", "type": "zip", "shasum": "", "reference": "81f5385a0237989ec43a5ed2d94176c5c9616cc8"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/master"}, "time": "2020-05-08T16:50:20+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "b6786f69dd7b062390582f20520ab4918283217e"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b6786f69dd7b062390582f20520ab4918283217e", "type": "zip", "shasum": "", "reference": "b6786f69dd7b062390582f20520ab4918283217e"}, "time": "2020-03-09T19:04:49+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "699871accfb394eb6f34ba1210df437f79b14d58"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/699871accfb394eb6f34ba1210df437f79b14d58", "type": "zip", "shasum": "", "reference": "699871accfb394eb6f34ba1210df437f79b14d58"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.14.0"}, "time": "2020-01-13T11:15:53+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}, {"version": "v1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "45c566a1ca16273f7ea6b930e013462e00e14502"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/45c566a1ca16273f7ea6b930e013462e00e14502", "type": "zip", "shasum": "", "reference": "45c566a1ca16273f7ea6b930e013462e00e14502"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/master"}, "time": "2019-11-27T13:56:44+00:00", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}}, {"version": "v1.13.1", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.13.1"}}, {"version": "v1.13.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/master"}}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "f6d623160da72288a9b704d324e5a0e4b385331a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/f6d623160da72288a9b704d324e5a0e4b385331a", "type": "zip", "shasum": "", "reference": "f6d623160da72288a9b704d324e5a0e4b385331a"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.12.0"}, "time": "2019-08-06T08:03:45+00:00", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}}, {"version": "v1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "d4b2e06aed3f7a4e3de06f9ad9189cd6a1f0a2ec"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/d4b2e06aed3f7a4e3de06f9ad9189cd6a1f0a2ec", "type": "zip", "shasum": "", "reference": "d4b2e06aed3f7a4e3de06f9ad9189cd6a1f0a2ec"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/master"}, "time": "2019-01-07T19:39:47+00:00", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "378c8743ff5352fe124c9c2b32d5f03e798f33d3"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/378c8743ff5352fe124c9c2b32d5f03e798f33d3", "type": "zip", "shasum": "", "reference": "378c8743ff5352fe124c9c2b32d5f03e798f33d3"}, "time": "2018-09-21T06:26:08+00:00"}, {"version": "v1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "243574de943ebd9a68a5e971b88055b1fd6a2796"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/243574de943ebd9a68a5e971b88055b1fd6a2796", "type": "zip", "shasum": "", "reference": "243574de943ebd9a68a5e971b88055b1fd6a2796"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.9.0"}, "time": "2018-08-06T14:22:27+00:00"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "f30a9fb355ef8cc7411caf8da7c59b4e2db137fe"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/f30a9fb355ef8cc7411caf8da7c59b4e2db137fe", "type": "zip", "shasum": "", "reference": "f30a9fb355ef8cc7411caf8da7c59b4e2db137fe"}, "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/master"}, "time": "2018-04-26T10:06:28+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "9a3548340d338baf5f263bbc171f138045ab547e"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/9a3548340d338baf5f263bbc171f138045ab547e", "type": "zip", "shasum": "", "reference": "9a3548340d338baf5f263bbc171f138045ab547e"}, "time": "2018-01-30T19:27:44+00:00", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "56f9af33902ea40dce76bb52870ea93d192dfcf3"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/56f9af33902ea40dce76bb52870ea93d192dfcf3", "type": "zip", "shasum": "", "reference": "56f9af33902ea40dce76bb52870ea93d192dfcf3"}, "time": "2017-10-11T12:05:26+00:00", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "e1cc0762b6144b3a8c3cd20b061faa9ff177388f"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/e1cc0762b6144b3a8c3cd20b061faa9ff177388f", "type": "zip", "shasum": "", "reference": "e1cc0762b6144b3a8c3cd20b061faa9ff177388f"}, "time": "2017-06-14T15:44:48+00:00", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "8e8010bf08a5979c58905496cec92199c26ed0df"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/8e8010bf08a5979c58905496cec92199c26ed0df", "type": "zip", "shasum": "", "reference": "8e8010bf08a5979c58905496cec92199c26ed0df"}, "time": "2017-06-09T14:10:54+00:00", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}}, {"version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "f0aaa9763f5d603b672637a2da18f8d21852b940"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/f0aaa9763f5d603b672637a2da18f8d21852b940", "type": "zip", "shasum": "", "reference": "f0aaa9763f5d603b672637a2da18f8d21852b940"}, "time": "2016-11-14T01:06:16+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}}, {"version": "v1.3.0", "version_normalized": "*******"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "900b52f2c4f1d1db24c047081c907dcf08faacca"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/900b52f2c4f1d1db24c047081c907dcf08faacca", "type": "zip", "shasum": "", "reference": "900b52f2c4f1d1db24c047081c907dcf08faacca"}, "time": "2016-05-18T14:26:46+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "23ecaac64bf7374bd0d068175a0e736916b5f11a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/23ecaac64bf7374bd0d068175a0e736916b5f11a", "type": "zip", "shasum": "", "reference": "23ecaac64bf7374bd0d068175a0e736916b5f11a"}, "time": "2016-02-15T04:08:08+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "b5dc8c8413da1fada15e45a8059d90f7c05e309d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b5dc8c8413da1fada15e45a8059d90f7c05e309d", "type": "zip", "shasum": "", "reference": "b5dc8c8413da1fada15e45a8059d90f7c05e309d"}, "time": "2016-01-20T09:13:37+00:00", "suggest": "__unset"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-intl-grapheme.git", "type": "git", "reference": "9332d285b58a16b144b3bf0bfd3b6334d9a43006"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/9332d285b58a16b144b3bf0bfd3b6334d9a43006", "type": "zip", "shasum": "", "reference": "9332d285b58a16b144b3bf0bfd3b6334d9a43006"}, "time": "2015-11-04T20:28:58+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}, {"version": "v1.0.0", "version_normalized": "*******"}]}, "security-advisories": [], "last-modified": "Fri, 02 May 2025 09:41:34 GMT"}