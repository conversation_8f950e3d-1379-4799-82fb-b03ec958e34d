{"minified": "composer/2.0", "packages": {"nunomaduro/collision": [{"name": "nunomaduro/collision", "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["symfony", "php", "cli", "error", "console", "command-line", "laravel", "handling", "dev", "artisan", "laravel-zero"], "homepage": "", "version": "v8.8.2", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "60207965f9b7b7a4ce15a0f75d57f9dadb105bdb"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/60207965f9b7b7a4ce15a0f75d57f9dadb105bdb", "type": "zip", "shasum": "", "reference": "60207965f9b7b7a4ce15a0f75d57f9dadb105bdb"}, "type": "library", "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2025-06-25T02:12:12+00:00", "autoload": {"files": ["./src/Adapters/Phpunit/Autoload.php"], "psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}, "branch-alias": {"dev-8.x": "8.x-dev"}}, "require": {"php": "^8.2.0", "filp/whoops": "^2.18.1", "nunomaduro/termwind": "^2.3.1", "symfony/console": "^7.3.0"}, "require-dev": {"brianium/paratest": "^7.8.3", "laravel/framework": "^11.44.2 || ^12.18", "laravel/pint": "^1.22.1", "laravel/tinker": "^2.10.1", "laravel/sail": "^1.43.1", "laravel/sanctum": "^4.1.1", "larastan/larastan": "^3.4.2", "orchestra/testbench-core": "^9.12.0 || ^10.4", "pestphp/pest": "^3.8.2", "sebastian/environment": "^7.2.1 || ^8.0"}, "conflict": {"laravel/framework": "<11.44.2 || >=13.0.0", "phpunit/phpunit": "<11.5.15 || >=13.0.0"}}, {"version": "v8.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "44ccb82e3e21efb5446748d2a3c81a030ac22bd5"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/44ccb82e3e21efb5446748d2a3c81a030ac22bd5", "type": "zip", "shasum": "", "reference": "44ccb82e3e21efb5446748d2a3c81a030ac22bd5"}, "time": "2025-06-11T01:04:21+00:00"}, {"version": "v8.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "4cf9f3b47afff38b139fb79ce54fc71799022ce8"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/4cf9f3b47afff38b139fb79ce54fc71799022ce8", "type": "zip", "shasum": "", "reference": "4cf9f3b47afff38b139fb79ce54fc71799022ce8"}, "time": "2025-04-03T14:33:09+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.18.0", "nunomaduro/termwind": "^2.3.0", "symfony/console": "^7.2.5"}, "require-dev": {"brianium/paratest": "^7.8.3", "laravel/framework": "^11.44.2 || ^12.6", "laravel/pint": "^1.21.2", "laravel/tinker": "^2.10.1", "laravel/sail": "^1.41.0", "laravel/sanctum": "^4.0.8", "larastan/larastan": "^3.2", "orchestra/testbench-core": "^9.12.0 || ^10.1", "pestphp/pest": "^3.8.0", "sebastian/environment": "^7.2.0 || ^8.0"}}, {"version": "v8.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "586cb8181a257a2152b6a855ca8d9598878a1a26"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/586cb8181a257a2152b6a855ca8d9598878a1a26", "type": "zip", "shasum": "", "reference": "586cb8181a257a2152b6a855ca8d9598878a1a26"}, "time": "2025-03-14T22:37:40+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.17.0", "nunomaduro/termwind": "^2.3.0", "symfony/console": "^7.2.1"}, "require-dev": {"laravel/framework": "^11.44.2", "laravel/pint": "^1.21.2", "laravel/tinker": "^2.10.1", "laravel/sail": "^1.41.0", "laravel/sanctum": "^4.0.8", "larastan/larastan": "^2.10.0", "orchestra/testbench-core": "^9.12.0", "pestphp/pest": "^3.7.4", "sebastian/environment": "^6.1.0 || ^7.2.0"}, "conflict": {"laravel/framework": "<11.39.1 || >=13.0.0", "phpunit/phpunit": "<11.5.3 || >=12.0.0"}}, {"version": "v8.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "86f003c132143d5a2ab214e19933946409e0cae7"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/86f003c132143d5a2ab214e19933946409e0cae7", "type": "zip", "shasum": "", "reference": "86f003c132143d5a2ab214e19933946409e0cae7"}, "time": "2025-01-23T13:41:43+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.16.0", "nunomaduro/termwind": "^2.3.0", "symfony/console": "^7.2.1"}, "require-dev": {"laravel/framework": "^11.39.1", "laravel/pint": "^1.20.0", "laravel/tinker": "^2.10.0", "laravel/sail": "^1.40.0", "laravel/sanctum": "^4.0.7", "larastan/larastan": "^2.9.12", "orchestra/testbench-core": "^9.9.2", "pestphp/pest": "^3.7.3", "sebastian/environment": "^6.1.0 || ^7.2.0"}}, {"keywords": ["symfony", "php", "cli", "error", "console", "command-line", "laravel", "handling", "artisan", "laravel-zero"], "version": "v8.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "a94c0fb8f33da6a113cdc0eac936f7c42134de9d"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/a94c0fb8f33da6a113cdc0eac936f7c42134de9d", "type": "zip", "shasum": "", "reference": "a94c0fb8f33da6a113cdc0eac936f7c42134de9d"}, "time": "2025-01-23T12:48:18+00:00", "require-dev": {"laravel/framework": "^11.39.1", "laravel/pint": "^1.20.0", "laravel/tinker": "^2.10.0", "laravel/sail": "^1.40.0", "laravel/sanctum": "^4.0.7", "larastan/larastan": "^2.9.12", "orchestra/testbench-core": "^9.9.2", "pestphp/pest": "^2.36.0 || ^3.7.2", "sebastian/environment": "^6.1.0 || ^7.2.0"}}, {"version": "v8.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "f5c101b929c958e849a633283adff296ed5f38f5"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/f5c101b929c958e849a633283adff296ed5f38f5", "type": "zip", "shasum": "", "reference": "f5c101b929c958e849a633283adff296ed5f38f5"}, "time": "2024-10-15T16:06:32+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.16.0", "nunomaduro/termwind": "^2.1.0", "symfony/console": "^7.1.5"}, "require-dev": {"laravel/framework": "^11.28.0", "laravel/pint": "^1.18.1", "laravel/tinker": "^2.10.0", "laravel/sail": "^1.36.0", "laravel/sanctum": "^4.0.3", "larastan/larastan": "^2.9.8", "orchestra/testbench-core": "^9.5.3", "pestphp/pest": "^2.36.0 || ^3.4.0", "sebastian/environment": "^6.1.0 || ^7.2.0"}, "conflict": {"laravel/framework": "<11.0.0 || >=12.0.0", "phpunit/phpunit": "<10.5.1 || >=12.0.0"}}, {"version": "v8.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "e7d1aa8ed753f63fa816932bbc89678238843b4a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/e7d1aa8ed753f63fa816932bbc89678238843b4a", "type": "zip", "shasum": "", "reference": "e7d1aa8ed753f63fa816932bbc89678238843b4a"}, "time": "2024-08-03T15:32:23+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.15.4", "nunomaduro/termwind": "^2.0.1", "symfony/console": "^7.1.3"}, "require-dev": {"laravel/framework": "^11.19.0", "laravel/pint": "^1.17.1", "laravel/tinker": "^2.9.0", "laravel/sail": "^1.31.0", "laravel/sanctum": "^4.0.2", "larastan/larastan": "^2.9.8", "orchestra/testbench-core": "^9.2.3", "pestphp/pest": "^2.35.0 || ^3.0.0", "sebastian/environment": "^6.1.0 || ^7.0.0"}}, {"version": "v8.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b49f5b2891ce52726adfd162841c69d4e4c84229"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b49f5b2891ce52726adfd162841c69d4e4c84229", "type": "zip", "shasum": "", "reference": "b49f5b2891ce52726adfd162841c69d4e4c84229"}, "time": "2024-07-16T22:41:01+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.15.4", "nunomaduro/termwind": "^2.0.1", "symfony/console": "^7.1.2"}, "require-dev": {"laravel/framework": "^11.16.0", "laravel/pint": "^1.16.2", "laravel/tinker": "^2.9.0", "laravel/sail": "^1.30.2", "laravel/sanctum": "^4.0.2", "larastan/larastan": "^2.9.8", "orchestra/testbench-core": "^9.2.1", "pestphp/pest": "^2.34.9 || ^3.0.0", "sebastian/environment": "^6.1.0 || ^7.0.0"}}, {"version": "v8.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "13e5d538b95a744d85f447a321ce10adb28e9af9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/13e5d538b95a744d85f447a321ce10adb28e9af9", "type": "zip", "shasum": "", "reference": "13e5d538b95a744d85f447a321ce10adb28e9af9"}, "time": "2024-03-06T16:20:09+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.15.4", "nunomaduro/termwind": "^2.0.1", "symfony/console": "^7.0.4"}, "require-dev": {"laravel/framework": "^11.0.0", "laravel/pint": "^1.14.0", "laravel/tinker": "^2.9.0", "laravel/sail": "^1.28.2", "laravel/sanctum": "^4.0.0", "larastan/larastan": "^2.9.2", "orchestra/testbench-core": "^9.0.0", "pestphp/pest": "^2.34.1 || ^3.0.0", "sebastian/environment": "^6.0.1 || ^7.0.0"}}, {"version": "v8.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0d655ffbf3edf9b366e0eea5ab9c7871e0ab3357"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0d655ffbf3edf9b366e0eea5ab9c7871e0ab3357", "type": "zip", "shasum": "", "reference": "0d655ffbf3edf9b366e0eea5ab9c7871e0ab3357"}, "time": "2024-01-12T13:38:24+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.15.4", "nunomaduro/termwind": "^2.0.0", "symfony/console": "^7.0.2"}, "require-dev": {"laravel/framework": "^11.0.0", "laravel/pint": "^1.13.8", "laravel/tinker": "^2.9.0", "laravel/sail": "^1.27.0", "laravel/sanctum": "^4.0.0", "larastan/larastan": "^2.8.1", "orchestra/testbench-core": "^9.0.0", "pestphp/pest": "^2.31.0 || ^3.0.0", "sebastian/environment": "^6.0.1 || ^7.0.0"}}, {"version": "v8.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0225cf94fc9aaf645bbc42b5fc838aace025e0e3"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0225cf94fc9aaf645bbc42b5fc838aace025e0e3", "type": "zip", "shasum": "", "reference": "0225cf94fc9aaf645bbc42b5fc838aace025e0e3"}, "time": "2023-12-08T16:03:53+00:00", "require": {"php": "^8.2.0", "filp/whoops": "^2.15.4", "nunomaduro/termwind": "^2.0.0", "symfony/console": "^7.0.1"}, "require-dev": {"laravel/framework": "^11.0.0", "laravel/pint": "^1.13.6", "laravel/tinker": "dev-develop", "laravel/sail": "^1.26.2", "laravel/sanctum": "^4.0.0", "nunomaduro/larastan": "^3.0.0", "orchestra/testbench-core": "^9.0.0", "pestphp/pest": "^2.27.0", "sebastian/environment": "^6.0.1"}, "conflict": {"laravel/framework": "<11.0.0 || >=12.0.0", "phpunit/phpunit": "<10.5.1 || >=11.0.0"}}, {"version": "v8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0098cfa00e6ed53a7892a3ce6630f7f5b40ad732"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0098cfa00e6ed53a7892a3ce6630f7f5b40ad732", "type": "zip", "shasum": "", "reference": "0098cfa00e6ed53a7892a3ce6630f7f5b40ad732"}, "time": "2023-12-04T12:08:38+00:00"}, {"version": "v7.12.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "995245421d3d7593a6960822063bdba4f5d7cf1a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/995245421d3d7593a6960822063bdba4f5d7cf1a", "type": "zip", "shasum": "", "reference": "995245421d3d7593a6960822063bdba4f5d7cf1a"}, "time": "2025-03-14T22:35:49+00:00", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "require": {"php": "^8.1.0", "filp/whoops": "^2.17.0", "nunomaduro/termwind": "^1.17.0", "symfony/console": "^6.4.17"}, "require-dev": {"brianium/paratest": "^7.4.8", "laravel/framework": "^10.48.29", "laravel/pint": "^1.21.2", "laravel/sail": "^1.41.0", "laravel/sanctum": "^3.3.3", "laravel/tinker": "^2.10.1", "nunomaduro/larastan": "^2.10.0", "orchestra/testbench-core": "^8.35.0", "pestphp/pest": "^2.36.0", "phpunit/phpunit": "^10.5.36", "sebastian/environment": "^6.1.0", "spatie/laravel-ignition": "^2.9.1"}, "conflict": {"laravel/framework": ">=11.0.0"}}, {"version": "v7.11.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "994ea93df5d4132f69d3f1bd74730509df6e8a05"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/994ea93df5d4132f69d3f1bd74730509df6e8a05", "type": "zip", "shasum": "", "reference": "994ea93df5d4132f69d3f1bd74730509df6e8a05"}, "time": "2024-10-15T15:12:40+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.16.0", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.4.12"}, "require-dev": {"brianium/paratest": "^7.3.1", "laravel/framework": "^10.48.22", "laravel/pint": "^1.18.1", "laravel/sail": "^1.36.0", "laravel/sanctum": "^3.3.3", "laravel/tinker": "^2.10.0", "nunomaduro/larastan": "^2.9.8", "orchestra/testbench-core": "^8.28.3", "pestphp/pest": "^2.35.1", "phpunit/phpunit": "^10.5.36", "sebastian/environment": "^6.1.0", "spatie/laravel-ignition": "^2.8.0"}}, {"version": "v7.10.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "49ec67fa7b002712da8526678abd651c09f375b2"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/49ec67fa7b002712da8526678abd651c09f375b2", "type": "zip", "shasum": "", "reference": "49ec67fa7b002712da8526678abd651c09f375b2"}, "time": "2023-10-11T15:45:01+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.15.3", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.3.4"}, "require-dev": {"brianium/paratest": "^7.3.0", "laravel/framework": "^10.28.0", "laravel/pint": "^1.13.3", "laravel/sail": "^1.25.0", "laravel/sanctum": "^3.3.1", "laravel/tinker": "^2.8.2", "nunomaduro/larastan": "^2.6.4", "orchestra/testbench-core": "^8.13.0", "pestphp/pest": "^2.23.2", "phpunit/phpunit": "^10.4.1", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.3.1"}}, {"version": "v7.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "296d0cf9fe462837ac0da8a568b56fc026b132da"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/296d0cf9fe462837ac0da8a568b56fc026b132da", "type": "zip", "shasum": "", "reference": "296d0cf9fe462837ac0da8a568b56fc026b132da"}, "time": "2023-09-19T10:45:09+00:00", "require-dev": {"brianium/paratest": "^7.2.7", "laravel/framework": "^10.23.1", "laravel/pint": "^1.13.1", "laravel/sail": "^1.25.0", "laravel/sanctum": "^3.3.1", "laravel/tinker": "^2.8.2", "nunomaduro/larastan": "^2.6.4", "orchestra/testbench-core": "^8.11.0", "pestphp/pest": "^2.19.1", "phpunit/phpunit": "^10.3.5", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.3.0"}, "conflict": "__unset"}, {"version": "v7.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "61553ad3260845d7e3e49121b7074619233d361b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/61553ad3260845d7e3e49121b7074619233d361b", "type": "zip", "shasum": "", "reference": "61553ad3260845d7e3e49121b7074619233d361b"}, "time": "2023-08-07T08:03:21+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.15.3", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.3.2"}, "require-dev": {"brianium/paratest": "^7.2.4", "laravel/framework": "^10.17.1", "laravel/pint": "^1.10.5", "laravel/sail": "^1.23.1", "laravel/sanctum": "^3.2.5", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.6.4", "orchestra/testbench-core": "^8.5.9", "pestphp/pest": "^2.12.1", "phpunit/phpunit": "^10.3.1", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.2.0"}}, {"version": "v7.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "1813f0a3470f2ac171443bc37b324c2a693005f9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/1813f0a3470f2ac171443bc37b324c2a693005f9", "type": "zip", "shasum": "", "reference": "1813f0a3470f2ac171443bc37b324c2a693005f9"}, "time": "2023-08-04T10:26:10+00:00"}, {"version": "v7.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "69a07197d055456d29911116fca3bc2c985f524b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/69a07197d055456d29911116fca3bc2c985f524b", "type": "zip", "shasum": "", "reference": "69a07197d055456d29911116fca3bc2c985f524b"}, "time": "2023-06-29T09:10:16+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.15.2", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.3.0"}, "require-dev": {"brianium/paratest": "^7.2.2", "laravel/framework": "^10.14.1", "laravel/pint": "^1.10.3", "laravel/sail": "^1.23.0", "laravel/sanctum": "^3.2.5", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.6.3", "orchestra/testbench-core": "^8.5.8", "pestphp/pest": "^2.8.1", "phpunit/phpunit": "^10.2.2", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.2.0"}, "conflict": {"phpunit/phpunit": "<10.1.2"}}, {"version": "v7.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "87faf7bc1c42d7fef7c60ec5c143050ce2a6189a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/87faf7bc1c42d7fef7c60ec5c143050ce2a6189a", "type": "zip", "shasum": "", "reference": "87faf7bc1c42d7fef7c60ec5c143050ce2a6189a"}, "time": "2023-06-15T10:51:08+00:00", "require-dev": {"brianium/paratest": "^7.2.0", "laravel/framework": "^10.13.5", "laravel/pint": "^1.10.2", "laravel/sail": "^1.22.0", "laravel/sanctum": "^3.2.5", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.6.3", "orchestra/testbench-core": "^8.5.7", "pestphp/pest": "^2", "phpunit/phpunit": "^10.2.2", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.1.3"}}, {"version": "v7.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "76b3cabda0aabda455fc3b9db6c3615f5a87c7ff"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/76b3cabda0aabda455fc3b9db6c3615f5a87c7ff", "type": "zip", "shasum": "", "reference": "76b3cabda0aabda455fc3b9db6c3615f5a87c7ff"}, "time": "2023-04-22T22:12:40+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.15.2", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.2.8"}, "require-dev": {"brianium/paratest": "^7.1.3", "laravel/framework": "^10.8.0", "laravel/pint": "^1.9.0", "laravel/sail": "^1.21.4", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.6.0", "orchestra/testbench-core": "^8.5.0", "pestphp/pest": "^2.5.2", "phpunit/phpunit": "^10.1.1", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.1.0"}}, {"version": "v7.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "ebaefea0ee2fd064e66ef37b9fbe6f79d38aeedc"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/ebaefea0ee2fd064e66ef37b9fbe6f79d38aeedc", "type": "zip", "shasum": "", "reference": "ebaefea0ee2fd064e66ef37b9fbe6f79d38aeedc"}, "time": "2023-04-22T22:02:40+00:00"}, {"version": "v7.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "bbbc6fb9c1ee88f8aa38e47abd15c465f946f85e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/bbbc6fb9c1ee88f8aa38e47abd15c465f946f85e", "type": "zip", "shasum": "", "reference": "bbbc6fb9c1ee88f8aa38e47abd15c465f946f85e"}, "time": "2023-04-14T10:39:16+00:00", "require-dev": {"brianium/paratest": "^7.1.3", "laravel/framework": "^10.7.1", "laravel/pint": "^1.8.0", "laravel/sail": "^1.21.4", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.5.1", "orchestra/testbench-core": "^8.4.2", "pestphp/pest": "^2.5.0", "phpunit/phpunit": "^10.1.0", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.1.0"}, "conflict": {"phpunit/phpunit": "<10.1.0"}}, {"version": "v7.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "42bab217d4913d6610f341d0468cec860aae165e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/42bab217d4913d6610f341d0468cec860aae165e", "type": "zip", "shasum": "", "reference": "42bab217d4913d6610f341d0468cec860aae165e"}, "time": "2023-03-31T08:17:12+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.15.1", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.2.7"}, "require-dev": {"brianium/paratest": "^7.1.2", "laravel/framework": "^10.5.1", "laravel/pint": "^1.7.0", "laravel/sail": "^1.21.3", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.5.1", "orchestra/testbench-core": "^8.2.0", "pestphp/pest": "^2.3.0", "phpunit/phpunit": "^10.0.19", "sebastian/environment": "^6.0.0", "spatie/laravel-ignition": "^2.0.0"}, "conflict": {"phpunit/phpunit": "<10.0.17"}}, {"version": "v7.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "c680af93e414110b36056029f63120e6bc78f6e3"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/c680af93e414110b36056029f63120e6bc78f6e3", "type": "zip", "shasum": "", "reference": "c680af93e414110b36056029f63120e6bc78f6e3"}, "time": "2023-03-23T21:41:35+00:00", "require-dev": {"brianium/paratest": "^7.1.2", "laravel/framework": "^10.4.1", "laravel/pint": "^1.7.0", "laravel/sail": "^1.21.2", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.5.1", "orchestra/testbench-core": "^8.1.1", "pestphp/pest": "^2.0.2", "phpunit/phpunit": "^10.0.17", "sebastian/environment": "^6.0.0", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v7.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "9e111c8d8e88862d334879853dc849542e4b015a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/9e111c8d8e88862d334879853dc849542e4b015a", "type": "zip", "shasum": "", "reference": "9e111c8d8e88862d334879853dc849542e4b015a"}, "time": "2023-03-21T21:02:58+00:00"}, {"version": "v7.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "c2f3b0e82d5d8e79b44bb44fa5cedcb463dd7c64"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/c2f3b0e82d5d8e79b44bb44fa5cedcb463dd7c64", "type": "zip", "shasum": "", "reference": "c2f3b0e82d5d8e79b44bb44fa5cedcb463dd7c64"}, "time": "2023-03-21T13:34:19+00:00"}, {"version": "v7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "3bcc76e436f754272df0b9d466b4989421565ca9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/3bcc76e436f754272df0b9d466b4989421565ca9", "type": "zip", "shasum": "", "reference": "3bcc76e436f754272df0b9d466b4989421565ca9"}, "time": "2023-03-21T13:11:23+00:00"}, {"version": "v7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "8ee125cb0888cf694b559caa015e104f577bb4f0"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/8ee125cb0888cf694b559caa015e104f577bb4f0", "type": "zip", "shasum": "", "reference": "8ee125cb0888cf694b559caa015e104f577bb4f0"}, "time": "2023-03-19T17:08:17+00:00", "require-dev": {"brianium/paratest": "^7.1.1", "laravel/framework": "^10.4.1", "laravel/pint": "^1.6.0", "laravel/sail": "^1.21.2", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.5.1", "orchestra/testbench-core": "^8.1.0", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.16", "sebastian/environment": "^6.0.0", "spatie/laravel-ignition": "^2.0.0"}, "conflict": {"phpunit/phpunit": "<10.0.16"}}, {"version": "v7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "f502ff3b2051124c89b4dd3a8a497ca65f3ce26c"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/f502ff3b2051124c89b4dd3a8a497ca65f3ce26c", "type": "zip", "shasum": "", "reference": "f502ff3b2051124c89b4dd3a8a497ca65f3ce26c"}, "time": "2023-03-14T14:34:49+00:00", "require-dev": {"brianium/paratest": "^7.1.1", "laravel/framework": "^10.3.3", "laravel/pint": "^1.6.0", "laravel/sail": "^1.21.2", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.5.1", "orchestra/testbench-core": "^8.0.5", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.16", "sebastian/environment": "^6.0.0", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "ec91efc82a40ca4224a53494e7e93775cebc39e5"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/ec91efc82a40ca4224a53494e7e93775cebc39e5", "type": "zip", "shasum": "", "reference": "ec91efc82a40ca4224a53494e7e93775cebc39e5"}, "time": "2023-03-13T17:02:54+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "2b97fed4950cf0ff148c18b853975ec8ea135e90"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/2b97fed4950cf0ff148c18b853975ec8ea135e90", "type": "zip", "shasum": "", "reference": "2b97fed4950cf0ff148c18b853975ec8ea135e90"}, "time": "2023-03-03T10:00:22+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.14.6", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.2.7"}, "require-dev": {"brianium/paratest": "^7.1.0", "laravel/framework": "^10.2.0", "laravel/pint": "^1.6.0", "laravel/sail": "^1.21.1", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.1", "nunomaduro/larastan": "^2.4.1", "orchestra/testbench-core": "^8.0.3", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.14", "sebastian/environment": "^6.0.0", "spatie/laravel-ignition": "^2.0.0"}, "conflict": "__unset"}, {"version": "v7.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "5c654ee5fa187cf2f4cb226d773582ec6d402a55"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/5c654ee5fa187cf2f4cb226d773582ec6d402a55", "type": "zip", "shasum": "", "reference": "5c654ee5fa187cf2f4cb226d773582ec6d402a55"}, "time": "2023-02-19T16:25:13+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.14.6", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.2.5"}, "require-dev": {"laravel/framework": "^10.0.3", "laravel/pint": "^1.5.0", "laravel/sail": "^1.20.2", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.0", "nunomaduro/larastan": "^2.4.1", "orchestra/testbench-core": "^8.0.1", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.9", "sebastian/environment": "^6.0.0", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "d9205550494a8e6865ae59eaa3fd0f77f6c916fb"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/d9205550494a8e6865ae59eaa3fd0f77f6c916fb", "type": "zip", "shasum": "", "reference": "d9205550494a8e6865ae59eaa3fd0f77f6c916fb"}, "time": "2023-02-17T11:52:52+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.14.6", "nunomaduro/termwind": "^1.15.1", "sebastian/environment": "^6.0.0", "symfony/console": "^6.2.5"}, "require-dev": {"laravel/framework": "^10.0.0", "laravel/pint": "^1.5.0", "laravel/sail": "^1.20.2", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.0", "nunomaduro/larastan": "^2.4.0", "orchestra/testbench-core": "^8.0.0", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.7", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "fbf3c8a8ee08068bee7d81ee0cee5ddf032aaa84"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/fbf3c8a8ee08068bee7d81ee0cee5ddf032aaa84", "type": "zip", "shasum": "", "reference": "fbf3c8a8ee08068bee7d81ee0cee5ddf032aaa84"}, "time": "2023-02-16T14:34:30+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.14.6", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.2.5"}}, {"version": "v7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "15e5fe35305419c9c6cf196be8d62cf70087a20c"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/15e5fe35305419c9c6cf196be8d62cf70087a20c", "type": "zip", "shasum": "", "reference": "15e5fe35305419c9c6cf196be8d62cf70087a20c"}, "time": "2023-02-11T18:10:08+00:00", "require-dev": {"laravel/framework": "^10.0.0", "laravel/pint": "^1.4.1", "laravel/sail": "^1.20.1", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.0", "nunomaduro/larastan": "^2.4.0", "orchestra/testbench-core": "^8.0.0", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.7", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0422294fcfb1ca2e15dfc20122d218c45c5bbf51"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0422294fcfb1ca2e15dfc20122d218c45c5bbf51", "type": "zip", "shasum": "", "reference": "0422294fcfb1ca2e15dfc20122d218c45c5bbf51"}, "time": "2023-02-08T12:07:35+00:00", "require-dev": {"laravel/framework": "^10.0.0", "laravel/pint": "^1.4.1", "laravel/sail": "^1.20.1", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.0", "nunomaduro/larastan": "^2.4.0", "orchestra/testbench-core": "^8.0.0", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.6", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "ad7b2fc3182407ad326934426077c5e8dd0062ad"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/ad7b2fc3182407ad326934426077c5e8dd0062ad", "type": "zip", "shasum": "", "reference": "ad7b2fc3182407ad326934426077c5e8dd0062ad"}, "time": "2023-02-07T15:13:11+00:00", "require": {"php": "^8.1.0", "filp/whoops": "^2.14.6", "nunomaduro/termwind": "^1.15.0", "symfony/console": "^6.2.5"}, "require-dev": {"laravel/framework": "^10.0.0", "laravel/pint": "^1.4.1", "laravel/sail": "^1.19.0", "laravel/sanctum": "^3.2.1", "laravel/tinker": "^2.8.0", "nunomaduro/larastan": "^2.4.0", "orchestra/testbench-core": "^8.0.0", "pestphp/pest": "^2.0.0", "phpunit/phpunit": "^10.0.5", "spatie/laravel-ignition": "^2.0.0"}}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "f05978827b9343cba381ca05b8c7deee346b6015"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/f05978827b9343cba381ca05b8c7deee346b6015", "type": "zip", "shasum": "", "reference": "f05978827b9343cba381ca05b8c7deee346b6015"}, "time": "2023-01-03T12:54:54+00:00", "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}, "branch-alias": {"dev-develop": "6.x-dev"}}, "require": {"php": "^8.0.0", "filp/whoops": "^2.14.5", "symfony/console": "^6.0.2"}, "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.26.1", "laravel/pint": "^1.1.1", "nunomaduro/larastan": "^1.0.3", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.7", "phpunit/phpunit": "^9.5.23", "spatie/ignition": "^1.4.1"}}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "83699b231e7f277bfa2e823788973bf4082f019a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/83699b231e7f277bfa2e823788973bf4082f019a", "type": "zip", "shasum": "", "reference": "83699b231e7f277bfa2e823788973bf4082f019a"}, "time": "2022-12-23T21:36:49+00:00"}, {"version": "v6.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0f6349c3ed5dd28467087b08fb59384bb458a22b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0f6349c3ed5dd28467087b08fb59384bb458a22b", "type": "zip", "shasum": "", "reference": "0f6349c3ed5dd28467087b08fb59384bb458a22b"}, "time": "2022-09-29T12:29:49+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "17f600e2e8872856ff2846243efb74ad4b6da531"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/17f600e2e8872856ff2846243efb74ad4b6da531", "type": "zip", "shasum": "", "reference": "17f600e2e8872856ff2846243efb74ad4b6da531"}, "time": "2022-08-29T09:11:20+00:00"}, {"version": "v6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "5f058f7e39278b701e455b3c82ec5298cf001d89"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/5f058f7e39278b701e455b3c82ec5298cf001d89", "type": "zip", "shasum": "", "reference": "5f058f7e39278b701e455b3c82ec5298cf001d89"}, "time": "2022-06-27T16:11:16+00:00", "require": {"php": "^8.0.0", "facade/ignition-contracts": "^1.0.2", "filp/whoops": "^2.14.5", "symfony/console": "^6.0.2"}, "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.7", "laravel/pint": "^0.2.1", "nunomaduro/larastan": "^1.0.2", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.3.0", "phpunit/phpunit": "^9.5.11"}}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "c379636dc50e829edb3a8bcb944a01aa1aed8f25"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/c379636dc50e829edb3a8bcb944a01aa1aed8f25", "type": "zip", "shasum": "", "reference": "c379636dc50e829edb3a8bcb944a01aa1aed8f25"}, "time": "2022-04-05T15:31:38+00:00", "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.7", "nunomaduro/larastan": "^1.0.2", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.3.0", "phpunit/phpunit": "^9.5.11"}}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "df09e21a5e5d5a7d51a8b9ecd44d3dd150d97fec"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/df09e21a5e5d5a7d51a8b9ecd44d3dd150d97fec", "type": "zip", "shasum": "", "reference": "df09e21a5e5d5a7d51a8b9ecd44d3dd150d97fec"}, "time": "2022-01-18T17:49:08+00:00", "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.0", "nunomaduro/larastan": "^1.0.2", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.0.0", "phpunit/phpunit": "^9.5.11"}}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "5338ecc3909ef3ed150f6408f14e44cdb62bfdd0"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/5338ecc3909ef3ed150f6408f14e44cdb62bfdd0", "type": "zip", "shasum": "", "reference": "5338ecc3909ef3ed150f6408f14e44cdb62bfdd0"}, "time": "2022-01-10T17:17:19+00:00"}, {"version": "v5.11.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "8b610eef8582ccdc05d8f2ab23305e2d37049461"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/8b610eef8582ccdc05d8f2ab23305e2d37049461", "type": "zip", "shasum": "", "reference": "8b610eef8582ccdc05d8f2ab23305e2d37049461"}, "time": "2022-01-10T16:22:52+00:00", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "require": {"php": "^7.3 || ^8.0", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.14.3", "symfony/console": "^5.0"}, "require-dev": {"brianium/paratest": "^6.1", "fideloper/proxy": "^4.4.1", "fruitcake/laravel-cors": "^2.0.3", "laravel/framework": "8.x-dev", "nunomaduro/larastan": "^0.6.2", "nunomaduro/mock-final-classes": "^1.0", "orchestra/testbench": "^6.0", "phpstan/phpstan": "^0.12.64", "phpunit/phpunit": "^9.5.0"}}, {"version": "v5.10.0", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "3004cfa49c022183395eabc6d0e5207dfe498d00"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/3004cfa49c022183395eabc6d0e5207dfe498d00", "type": "zip", "shasum": "", "reference": "3004cfa49c022183395eabc6d0e5207dfe498d00"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=66BYDWAT92N6L", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2021-09-20T15:06:32+00:00"}, {"version": "v5.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "63456f5c3e8c4bc52bd573e5c85674d64d84fd43"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/63456f5c3e8c4bc52bd573e5c85674d64d84fd43", "type": "zip", "shasum": "", "reference": "63456f5c3e8c4bc52bd573e5c85674d64d84fd43"}, "time": "2021-08-26T15:32:09+00:00", "require": {"php": "^7.3 || ^8.0", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.7.2", "symfony/console": "^5.0"}, "require-dev": {"brianium/paratest": "^6.1", "fideloper/proxy": "^4.4.1", "friendsofphp/php-cs-fixer": "^3.0", "fruitcake/laravel-cors": "^2.0.3", "laravel/framework": "^8.0 || ^9.0", "nunomaduro/larastan": "^0.6.2", "nunomaduro/mock-final-classes": "^1.0", "orchestra/testbench": "^6.0 || ^7.0", "phpstan/phpstan": "^0.12.64", "phpunit/phpunit": "^9.5.0"}}, {"version": "v5.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0c3c393462eada1233513664e2d22bb9f69ca393"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0c3c393462eada1233513664e2d22bb9f69ca393", "type": "zip", "shasum": "", "reference": "0c3c393462eada1233513664e2d22bb9f69ca393"}, "time": "2021-08-13T14:23:01+00:00"}, {"version": "v5.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0f0f46b6345ee788325b5ce9d53c4e851b2293a0"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0f0f46b6345ee788325b5ce9d53c4e851b2293a0", "type": "zip", "shasum": "", "reference": "0f0f46b6345ee788325b5ce9d53c4e851b2293a0"}, "time": "2021-08-12T13:56:54+00:00"}, {"version": "v5.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "0122ac6b03c75279ef78d1c0ad49725dfc52a8d2"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/0122ac6b03c75279ef78d1c0ad49725dfc52a8d2", "type": "zip", "shasum": "", "reference": "0122ac6b03c75279ef78d1c0ad49725dfc52a8d2"}, "time": "2021-07-26T20:39:06+00:00", "require-dev": {"brianium/paratest": "^6.1", "fideloper/proxy": "^4.4.1", "friendsofphp/php-cs-fixer": "^2.17.3", "fruitcake/laravel-cors": "^2.0.3", "laravel/framework": "^8.0 || ^9.0", "nunomaduro/larastan": "^0.6.2", "nunomaduro/mock-final-classes": "^1.0", "orchestra/testbench": "^6.0 || ^7.0", "phpstan/phpstan": "^0.12.64", "phpunit/phpunit": "^9.5.0"}}, {"version": "v5.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b5cb36122f1c142c3c3ee20a0ae778439ef0244b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b5cb36122f1c142c3c3ee20a0ae778439ef0244b", "type": "zip", "shasum": "", "reference": "b5cb36122f1c142c3c3ee20a0ae778439ef0244b"}, "time": "2021-06-22T20:47:22+00:00", "require-dev": {"brianium/paratest": "^6.1", "fideloper/proxy": "^4.4.1", "friendsofphp/php-cs-fixer": "^2.17.3", "fruitcake/laravel-cors": "^2.0.3", "laravel/framework": "^9.0", "nunomaduro/larastan": "^0.6.2", "nunomaduro/mock-final-classes": "^1.0", "orchestra/testbench": "^7.0", "phpstan/phpstan": "^0.12.64", "phpunit/phpunit": "^9.5.0"}}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "41b7e9999133d5082700d31a1d0977161df8322a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/41b7e9999133d5082700d31a1d0977161df8322a", "type": "zip", "shasum": "", "reference": "41b7e9999133d5082700d31a1d0977161df8322a"}, "time": "2021-04-09T13:38:32+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "aca63581f380f63a492b1e3114604e411e39133a"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/aca63581f380f63a492b1e3114604e411e39133a", "type": "zip", "shasum": "", "reference": "aca63581f380f63a492b1e3114604e411e39133a"}, "time": "2021-01-25T15:34:13+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "aca954fd03414ba0dd85d7d8e42ba9b251893d1f"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/aca954fd03414ba0dd85d7d8e42ba9b251893d1f", "type": "zip", "shasum": "", "reference": "aca954fd03414ba0dd85d7d8e42ba9b251893d1f"}, "time": "2021-01-13T10:00:08+00:00"}, {"version": "v5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "7c2b95589bf81e274e61e47f7672a1b2c3e06eaa"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/7c2b95589bf81e274e61e47f7672a1b2c3e06eaa", "type": "zip", "shasum": "", "reference": "7c2b95589bf81e274e61e47f7672a1b2c3e06eaa"}, "time": "2020-10-29T14:50:40+00:00", "require-dev": {"nunomaduro/mock-final-classes": "^1.0", "friendsofphp/php-cs-fixer": "^2.16.4", "fideloper/proxy": "^4.4.0", "fruitcake/laravel-cors": "^2.0.1", "laravel/framework": "^8.0", "laravel/tinker": "^2.4.1", "nunomaduro/larastan": "^0.6.2", "orchestra/testbench": "^6.0", "phpstan/phpstan": "^0.12.36", "phpunit/phpunit": "^9.3.3"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "4a343299054e9368d0db4a982a780cc4ffa12707"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/4a343299054e9368d0db4a982a780cc4ffa12707", "type": "zip", "shasum": "", "reference": "4a343299054e9368d0db4a982a780cc4ffa12707"}, "time": "2020-08-27T18:58:22+00:00", "require": {"php": "^7.3", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.7.2", "symfony/console": "^5.0"}}, {"version": "v5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "51c3c5ff1155dba6eb30b96c6fe861096d131cb6"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/51c3c5ff1155dba6eb30b96c6fe861096d131cb6", "type": "zip", "shasum": "", "reference": "51c3c5ff1155dba6eb30b96c6fe861096d131cb6"}, "time": "2020-08-27T18:55:25+00:00", "conflict": {"illuminate/support": "<8.0"}}, {"version": "v5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "a8a1ef97ffa8c41bccff4318036e5e14a76bd87f"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/a8a1ef97ffa8c41bccff4318036e5e14a76bd87f", "type": "zip", "shasum": "", "reference": "a8a1ef97ffa8c41bccff4318036e5e14a76bd87f"}, "time": "2020-08-25T17:59:40+00:00", "conflict": "__unset"}, {"version": "v5.0.0-BETA5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "424a2ff61dccf0f98fbefaec252d444411b4bf83"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/424a2ff61dccf0f98fbefaec252d444411b4bf83", "type": "zip", "shasum": "", "reference": "424a2ff61dccf0f98fbefaec252d444411b4bf83"}, "time": "2020-08-09T17:15:39+00:00"}, {"version": "v5.0.0-BETA4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "805929cb4bb6b24ef56a28a4121eb1336803effd"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/805929cb4bb6b24ef56a28a4121eb1336803effd", "type": "zip", "shasum": "", "reference": "805929cb4bb6b24ef56a28a4121eb1336803effd"}, "time": "2020-06-25T16:51:17+00:00", "require-dev": {"rector/rector": "^0.7.37", "nunomaduro/mock-final-classes": "^1.0", "friendsofphp/php-cs-fixer": "^2.16.3", "fideloper/proxy": "^4.3", "fruitcake/laravel-cors": "^2.0.1", "laravel/framework": "^8.0", "laravel/tinker": "^2.4", "nunomaduro/larastan": "^0.6.1", "orchestra/testbench": "^6.0", "phpstan/phpstan": "^0.12.30", "phpunit/phpunit": "^9.2.4"}}, {"version": "v5.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "795a1cfb7c5ef400565d0fd7ed276ef44006c990"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/795a1cfb7c5ef400565d0fd7ed276ef44006c990", "type": "zip", "shasum": "", "reference": "795a1cfb7c5ef400565d0fd7ed276ef44006c990"}, "time": "2020-06-22T00:50:44+00:00"}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "33deb65cf7712bce6f6ba66c032fb33c446bdfb0"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/33deb65cf7712bce6f6ba66c032fb33c446bdfb0", "type": "zip", "shasum": "", "reference": "33deb65cf7712bce6f6ba66c032fb33c446bdfb0"}, "time": "2020-06-05T15:31:28+00:00", "require-dev": {"rector/rector": "^0.7.23", "nunomaduro/mock-final-classes": "^1.0", "friendsofphp/php-cs-fixer": "^2.16.3", "facade/ignition": "^2.0.2", "fideloper/proxy": "^4.3", "fruitcake/laravel-cors": "^1.0.6", "laravel/framework": "^7.10.3", "laravel/tinker": "^2.4", "nunomaduro/larastan": "^0.5.8", "orchestra/testbench": "^5.2", "phpstan/phpstan": "^0.12.25", "phpunit/phpunit": "^9.1.4"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "f82d97153a4e07724ff8db984deec420447546cd"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/f82d97153a4e07724ff8db984deec420447546cd", "type": "zip", "shasum": "", "reference": "f82d97153a4e07724ff8db984deec420447546cd"}, "time": "2020-05-11T13:56:01+00:00"}, {"version": "v4.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "7c125dc2463f3e144ddc7e05e63077109508c94e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/7c125dc2463f3e144ddc7e05e63077109508c94e", "type": "zip", "shasum": "", "reference": "7c125dc2463f3e144ddc7e05e63077109508c94e"}, "time": "2020-10-29T15:12:23+00:00", "require": {"php": "^7.2.5 || ^8.0", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "symfony/console": "^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "facade/ignition": "^2.0", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^1.0", "laravel/framework": "^7.0", "laravel/tinker": "^2.0", "nunomaduro/larastan": "^0.6", "orchestra/testbench": "^5.0", "phpstan/phpstan": "^0.12.3", "phpunit/phpunit": "^8.5.1 || ^9.0"}}, {"version": "v4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "d50490417eded97be300a92cd7df7badc37a9018"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/d50490417eded97be300a92cd7df7badc37a9018", "type": "zip", "shasum": "", "reference": "d50490417eded97be300a92cd7df7badc37a9018"}, "time": "2020-04-04T19:56:08+00:00", "require": {"php": "^7.2.5", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "symfony/console": "^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "facade/ignition": "^2.0", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^1.0", "laravel/framework": "^7.0", "laravel/tinker": "^2.0", "nunomaduro/larastan": "^0.5", "orchestra/testbench": "^5.0", "phpstan/phpstan": "^0.12.3", "phpunit/phpunit": "^8.5.1 || ^9.0"}}, {"version": "v4.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "a430bce33d1ad07f756ea6cae9afce9ef8670b42"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/a430bce33d1ad07f756ea6cae9afce9ef8670b42", "type": "zip", "shasum": "", "reference": "a430bce33d1ad07f756ea6cae9afce9ef8670b42"}, "time": "2020-03-07T12:46:00+00:00", "require": {"php": "^7.2.5", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "jakub-onderka/php-console-highlighter": "^0.4", "symfony/console": "^5.0"}, "require-dev": {"facade/ignition": "^2.0", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^1.0", "laravel/framework": "^7.0", "laravel/tinker": "^2.0", "nunomaduro/larastan": "^0.5", "orchestra/testbench": "^5.0", "phpstan/phpstan": "^0.12.3", "phpunit/phpunit": "^8.5.1 || ^9.0"}}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "e81356da90969139c85709f8353e59f1e7d5e01c"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/e81356da90969139c85709f8353e59f1e7d5e01c", "type": "zip", "shasum": "", "reference": "e81356da90969139c85709f8353e59f1e7d5e01c"}, "time": "2020-03-03T18:17:47+00:00"}, {"version": "v4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "1a7c9c49ece828a02b1118234652ada53c1f840b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/1a7c9c49ece828a02b1118234652ada53c1f840b", "type": "zip", "shasum": "", "reference": "1a7c9c49ece828a02b1118234652ada53c1f840b"}, "funding": [], "time": "2020-02-26T22:38:35+00:00"}, {"version": "v4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "f7505183009bebd7636f3efded2b177a8827b2a3"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/f7505183009bebd7636f3efded2b177a8827b2a3", "type": "zip", "shasum": "", "reference": "f7505183009bebd7636f3efded2b177a8827b2a3"}, "time": "2020-02-04T21:11:24+00:00", "funding": "__unset"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "4f2bc853693b3b74e3083c3edfdfb4395fecf8f8"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/4f2bc853693b3b74e3083c3edfdfb4395fecf8f8", "type": "zip", "shasum": "", "reference": "4f2bc853693b3b74e3083c3edfdfb4395fecf8f8"}, "time": "2020-01-22T21:47:54+00:00", "require-dev": {"nunomaduro/larastan": "^0.5", "phpstan/phpstan": "^0.12.3", "orchestra/testbench": "^5.0", "laravel/framework": "^7.0", "phpunit/phpunit": "^8.5.1"}}, {"version": "v4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "556b2ee357cd40ed5bf7d230cf2ba85528fc0714"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/556b2ee357cd40ed5bf7d230cf2ba85528fc0714", "type": "zip", "shasum": "", "reference": "556b2ee357cd40ed5bf7d230cf2ba85528fc0714"}, "time": "2020-01-22T21:36:14+00:00"}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "f7c45764dfe4ba5f2618d265a6f1f9c72732e01d"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/f7c45764dfe4ba5f2618d265a6f1f9c72732e01d", "type": "zip", "shasum": "", "reference": "f7c45764dfe4ba5f2618d265a6f1f9c72732e01d"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=66BYDWAT92N6L", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2021-02-11T09:01:42+00:00", "require": {"php": "^7.2.5 || ^8.0", "filp/whoops": "^2.1.4", "symfony/console": "~2.8|~3.3|~4.0", "php-parallel-lint/php-console-highlighter": "0.5.*"}, "require-dev": {"laravel/framework": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "88b58b5bd9bdcc54756480fb3ce87234696544ee"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/88b58b5bd9bdcc54756480fb3ce87234696544ee", "type": "zip", "shasum": "", "reference": "88b58b5bd9bdcc54756480fb3ce87234696544ee"}, "time": "2020-10-29T16:05:21+00:00", "require": {"php": "^7.1 || ^8.0", "filp/whoops": "^2.1.4", "symfony/console": "~2.8|~3.3|~4.0", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*"}}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "af42d339fe2742295a54f6fdd42aaa6f8c4aca68"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/af42d339fe2742295a54f6fdd42aaa6f8c4aca68", "type": "zip", "shasum": "", "reference": "af42d339fe2742295a54f6fdd42aaa6f8c4aca68"}, "time": "2019-03-07T21:35:13+00:00", "require": {"php": "^7.1", "filp/whoops": "^2.1.4", "symfony/console": "~2.8|~3.3|~4.0", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*"}, "require-dev": {"laravel/framework": "5.8.*", "nunomaduro/larastan": "^0.3.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "~8.0"}, "funding": "__unset"}, {"version": "v3.0.0", "version_normalized": "*******"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b5feb0c0d92978ec7169232ce5d70d6da6b29f63"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b5feb0c0d92978ec7169232ce5d70d6da6b29f63", "type": "zip", "shasum": "", "reference": "b5feb0c0d92978ec7169232ce5d70d6da6b29f63"}, "time": "2018-11-21T21:40:54+00:00", "require-dev": {"laravel/framework": "5.7.*", "nunomaduro/larastan": "^0.3.0", "phpstan/phpstan": "^0.10", "phpunit/phpunit": "~7.3"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "1149ad9f36f61b121ae61f5f6de820fc77b51e6b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/1149ad9f36f61b121ae61f5f6de820fc77b51e6b", "type": "zip", "shasum": "", "reference": "1149ad9f36f61b121ae61f5f6de820fc77b51e6b"}, "time": "2018-10-03T20:01:54+00:00", "require": {"php": "^7.1", "filp/whoops": "^2.1.4", "symfony/console": "~2.8|~3.3|~4.0", "jakub-onderka/php-console-highlighter": "0.3.*"}, "require-dev": {"phpunit/phpunit": "~7.3", "phpstan/phpstan": "^0.10", "laravel/framework": "5.7.*"}}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b1f606399ae77e9479b5597cd1aa3d8ea0078176"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b1f606399ae77e9479b5597cd1aa3d8ea0078176", "type": "zip", "shasum": "", "reference": "b1f606399ae77e9479b5597cd1aa3d8ea0078176"}, "time": "2018-06-16T22:05:52+00:00", "require-dev": {"phpunit/phpunit": "~7.2", "phpstan/phpstan": "^0.9.2", "laravel/framework": "5.6.*"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "245958b02c6a9edf24627380f368333ac5413a51"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/245958b02c6a9edf24627380f368333ac5413a51", "type": "zip", "shasum": "", "reference": "245958b02c6a9edf24627380f368333ac5413a51"}, "time": "2018-03-21T20:11:24+00:00", "require-dev": {"phpunit/phpunit": "~7.0", "laravel/framework": "5.6.*"}}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "e60011a179d97f33ee394865b1cec472247df3b9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/e60011a179d97f33ee394865b1cec472247df3b9", "type": "zip", "shasum": "", "reference": "e60011a179d97f33ee394865b1cec472247df3b9"}, "time": "2018-03-20T21:27:23+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "4e310ef9384f53ee8dda8736afb3cbaf320753a0"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/4e310ef9384f53ee8dda8736afb3cbaf320753a0", "type": "zip", "shasum": "", "reference": "4e310ef9384f53ee8dda8736afb3cbaf320753a0"}, "time": "2018-02-18T12:29:27+00:00"}, {"version": "v1.1.22", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "236609bd5a067587f600bc55c5d683b840798b22"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/236609bd5a067587f600bc55c5d683b840798b22", "type": "zip", "shasum": "", "reference": "236609bd5a067587f600bc55c5d683b840798b22"}, "time": "2018-02-07T10:42:20+00:00", "require": {"php": "^7.1", "filp/whoops": "^2.1.4", "symfony/console": "~2.8|~3.3|~4.0"}, "require-dev": {"phpunit/phpunit": "~6.5", "laravel/framework": "5.5.*"}}, {"version": "v1.1.21", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "80f46ba5646ede4c6f6071e48a4b476a6b5c740f"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/80f46ba5646ede4c6f6071e48a4b476a6b5c740f", "type": "zip", "shasum": "", "reference": "80f46ba5646ede4c6f6071e48a4b476a6b5c740f"}, "time": "2018-01-18T22:43:42+00:00"}, {"version": "v1.1.20", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "6db3dc2d9a357a85534292d7a4f00e731d3c81aa"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/6db3dc2d9a357a85534292d7a4f00e731d3c81aa", "type": "zip", "shasum": "", "reference": "6db3dc2d9a357a85534292d7a4f00e731d3c81aa"}, "time": "2018-01-10T22:08:37+00:00"}, {"version": "v1.1.19", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "c6384cb9962721e3b4ee9551c2ea564fdc5577fa"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/c6384cb9962721e3b4ee9551c2ea564fdc5577fa", "type": "zip", "shasum": "", "reference": "c6384cb9962721e3b4ee9551c2ea564fdc5577fa"}, "time": "2018-01-10T21:11:37+00:00"}, {"version": "v1.1.18", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b117d73eadcf7dd490faeaaa80ddde2bdb03bf1f"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b117d73eadcf7dd490faeaaa80ddde2bdb03bf1f", "type": "zip", "shasum": "", "reference": "b117d73eadcf7dd490faeaaa80ddde2bdb03bf1f"}, "time": "2017-12-22T21:57:56+00:00", "require-dev": {"phpunit/phpunit": "~6.5"}}, {"version": "v1.1.17", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "312f56cf740185ed831301096890a54ea5a02eb9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/312f56cf740185ed831301096890a54ea5a02eb9", "type": "zip", "shasum": "", "reference": "312f56cf740185ed831301096890a54ea5a02eb9"}, "time": "2017-12-19T20:01:32+00:00"}, {"version": "v1.1.16", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "12d1dc55ecafcdcb83ebdc1af9d7cb4f7608c4e3"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/12d1dc55ecafcdcb83ebdc1af9d7cb4f7608c4e3", "type": "zip", "shasum": "", "reference": "12d1dc55ecafcdcb83ebdc1af9d7cb4f7608c4e3"}, "time": "2017-12-17T21:12:10+00:00", "require-dev": "__unset"}, {"version": "v1.1.15", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b1edf8086c12df42e2ef2a51edda35a3550fbd28"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b1edf8086c12df42e2ef2a51edda35a3550fbd28", "type": "zip", "shasum": "", "reference": "b1edf8086c12df42e2ef2a51edda35a3550fbd28"}, "time": "2017-12-17T20:48:36+00:00"}, {"version": "v1.1.14", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "97cd596c1f696517c9c92ce210284b353088115e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/97cd596c1f696517c9c92ce210284b353088115e", "type": "zip", "shasum": "", "reference": "97cd596c1f696517c9c92ce210284b353088115e"}, "time": "2017-12-17T00:14:56+00:00"}, {"version": "v1.1.13", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "39d437a2d0e703336cfbd957588fed51d7e50b3c"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/39d437a2d0e703336cfbd957588fed51d7e50b3c", "type": "zip", "shasum": "", "reference": "39d437a2d0e703336cfbd957588fed51d7e50b3c"}, "time": "2017-12-11T19:24:08+00:00"}, {"version": "v1.1.12", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "67f5612e092cf0e87e97448e3826df08a2b7b759"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/67f5612e092cf0e87e97448e3826df08a2b7b759", "type": "zip", "shasum": "", "reference": "67f5612e092cf0e87e97448e3826df08a2b7b759"}, "time": "2017-12-11T19:23:44+00:00"}, {"version": "v1.1.11", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "c612fecd810b8511ffa7b6bf4f1587e85bbbe949"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/c612fecd810b8511ffa7b6bf4f1587e85bbbe949", "type": "zip", "shasum": "", "reference": "c612fecd810b8511ffa7b6bf4f1587e85bbbe949"}, "time": "2017-12-04T21:22:02+00:00", "require": {"php": "^7.1", "filp/whoops": "^2.1.4", "symfony/console": "~2.8|~3.3"}}, {"version": "v1.1.10", "version_normalized": "********", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "671569d5f1362a320120de1432d1af53ddd17e72"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/671569d5f1362a320120de1432d1af53ddd17e72", "type": "zip", "shasum": "", "reference": "671569d5f1362a320120de1432d1af53ddd17e72"}, "time": "2017-12-04T21:04:40+00:00"}, {"version": "v1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "276db430a4b1c12caf8d0a47cac73cf2aa4bffe4"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/276db430a4b1c12caf8d0a47cac73cf2aa4bffe4", "type": "zip", "shasum": "", "reference": "276db430a4b1c12caf8d0a47cac73cf2aa4bffe4"}, "time": "2017-11-15T21:15:11+00:00"}, {"version": "v1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "4958cf29a6fb342101ecf64965a45598f0444f1e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/4958cf29a6fb342101ecf64965a45598f0444f1e", "type": "zip", "shasum": "", "reference": "4958cf29a6fb342101ecf64965a45598f0444f1e"}, "time": "2017-11-05T21:32:49+00:00"}, {"version": "v1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "6b5c0bd55f2768e16c5fb32085c1af22b48e6aea"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/6b5c0bd55f2768e16c5fb32085c1af22b48e6aea", "type": "zip", "shasum": "", "reference": "6b5c0bd55f2768e16c5fb32085c1af22b48e6aea"}, "time": "2017-11-05T21:27:51+00:00"}, {"version": "v1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "e5ab3adab581a9a6c59ee4c786e7ecab79883fd6"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/e5ab3adab581a9a6c59ee4c786e7ecab79883fd6", "type": "zip", "shasum": "", "reference": "e5ab3adab581a9a6c59ee4c786e7ecab79883fd6"}, "time": "2017-10-22T17:58:30+00:00"}, {"version": "v1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "98f85f27aa9e40a4199dad33c3b532208a0d40e1"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/98f85f27aa9e40a4199dad33c3b532208a0d40e1", "type": "zip", "shasum": "", "reference": "98f85f27aa9e40a4199dad33c3b532208a0d40e1"}, "time": "2017-10-17T19:42:21+00:00"}, {"version": "v1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "e9581e545cdc2d659ae2144b3b3a645609411fd4"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/e9581e545cdc2d659ae2144b3b3a645609411fd4", "type": "zip", "shasum": "", "reference": "e9581e545cdc2d659ae2144b3b3a645609411fd4"}, "time": "2017-10-12T18:24:48+00:00", "require": {"php": ">=7.1", "filp/whoops": "^2.1.4", "symfony/console": "~3.3"}}, {"version": "v1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "6be35cea338cc6e56229700607befe5f4cc16fab"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/6be35cea338cc6e56229700607befe5f4cc16fab", "type": "zip", "shasum": "", "reference": "6be35cea338cc6e56229700607befe5f4cc16fab"}, "time": "2017-10-11T23:29:07+00:00"}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "74e11a78e621e32a785161d71094b6eff93349da"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/74e11a78e621e32a785161d71094b6eff93349da", "type": "zip", "shasum": "", "reference": "74e11a78e621e32a785161d71094b6eff93349da"}, "time": "2017-10-11T22:43:44+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "c8573ed1e29fa1449184fad1ce4bba332d836ea4"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/c8573ed1e29fa1449184fad1ce4bba332d836ea4", "type": "zip", "shasum": "", "reference": "c8573ed1e29fa1449184fad1ce4bba332d836ea4"}, "time": "2017-10-10T23:11:44+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "b056b13d258f77ede4699705925dc8187bc0569e"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/b056b13d258f77ede4699705925dc8187bc0569e", "type": "zip", "shasum": "", "reference": "b056b13d258f77ede4699705925dc8187bc0569e"}, "time": "2017-10-10T23:06:39+00:00"}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "54ae9f9fd30ad08a0854a837a04c160ed274add9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/54ae9f9fd30ad08a0854a837a04c160ed274add9", "type": "zip", "shasum": "", "reference": "54ae9f9fd30ad08a0854a837a04c160ed274add9"}, "time": "2017-10-09T22:25:40+00:00"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "6c7e76eb769e70149c166e393338ed5295280bf9"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/6c7e76eb769e70149c166e393338ed5295280bf9", "type": "zip", "shasum": "", "reference": "6c7e76eb769e70149c166e393338ed5295280bf9"}, "time": "2017-10-08T21:19:28+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "9379057c7e0f93cb6a43db499a28be1519c40e02"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/9379057c7e0f93cb6a43db499a28be1519c40e02", "type": "zip", "shasum": "", "reference": "9379057c7e0f93cb6a43db499a28be1519c40e02"}, "time": "2017-10-08T21:09:37+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "7e9b149721ab20e16dea53b6bf4c51b92684e98b"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/7e9b149721ab20e16dea53b6bf4c51b92684e98b", "type": "zip", "shasum": "", "reference": "7e9b149721ab20e16dea53b6bf4c51b92684e98b"}, "time": "2017-10-08T21:04:51+00:00"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "8d75366d0bfd89f5b8a399b461e55882bb524127"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/8d75366d0bfd89f5b8a399b461e55882bb524127", "type": "zip", "shasum": "", "reference": "8d75366d0bfd89f5b8a399b461e55882bb524127"}, "time": "2017-10-08T21:00:36+00:00"}, {"keywords": ["cli", "error", "console", "handling", "laravel-zero"], "version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/nunomaduro/collision.git", "type": "git", "reference": "35422996dfc0573a873d363daf5b24152e823e71"}, "dist": {"url": "https://api.github.com/repos/nunomaduro/collision/zipball/35422996dfc0573a873d363daf5b24152e823e71", "type": "zip", "shasum": "", "reference": "35422996dfc0573a873d363daf5b24152e823e71"}, "time": "2017-10-08T16:49:09+00:00", "extra": "__unset"}]}, "security-advisories": [], "last-modified": "Wed, 25 Jun 2025 02:13:20 GMT"}