{"minified": "composer/2.0", "packages": {"amphp/sync": [{"name": "amphp/sync", "description": "Non-blocking synchronization primitives for PHP based on Amp and Revolt.", "keywords": ["synchronization", "async", "asynchronous", "semaphore", "mutex"], "homepage": "https://github.com/amphp/sync", "version": "v2.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "217097b785130d77cfcc58ff583cf26cd1770bf1"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/217097b785130d77cfcc58ff583cf26cd1770bf1", "type": "zip", "shasum": "", "reference": "217097b785130d77cfcc58ff583cf26cd1770bf1"}, "type": "library", "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.3.0"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-08-03T19:31:26+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Sync\\": "src"}}, "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/serialization": "^1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "5.23"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "375ef5b54a0d12c38e12728dde05a55e30f2fbec"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/375ef5b54a0d12c38e12728dde05a55e30f2fbec", "type": "zip", "shasum": "", "reference": "375ef5b54a0d12c38e12728dde05a55e30f2fbec"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.2.0"}, "time": "2024-03-12T01:00:01+00:00"}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "50ddc7392cc8034b3e4798cef3cc90d3f4c0441c"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/50ddc7392cc8034b3e4798cef3cc90d3f4c0441c", "type": "zip", "shasum": "", "reference": "50ddc7392cc8034b3e4798cef3cc90d3f4c0441c"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.1.0"}, "time": "2023-08-19T13:53:40+00:00", "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.4"}}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "ad8a0e80cc586cece4f8ce57460f8bbf442ba8fc"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/ad8a0e80cc586cece4f8ce57460f8bbf442ba8fc", "type": "zip", "shasum": "", "reference": "ad8a0e80cc586cece4f8ce57460f8bbf442ba8fc"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0"}, "time": "2022-12-30T22:11:37+00:00"}, {"description": "Mutex, Semaphore, and other synchronization tools for Amp.", "version": "v2.0.0-beta.6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "fd142c33fec3d6cb631c701a3cc9194d1e537a3f"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/fd142c33fec3d6cb631c701a3cc9194d1e537a3f", "type": "zip", "shasum": "", "reference": "fd142c33fec3d6cb631c701a3cc9194d1e537a3f"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0-beta.6"}, "time": "2022-11-07T21:41:33+00:00", "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2-dev", "psalm/phar": "^4.19"}}, {"version": "v2.0.0-beta.5", "version_normalized": "*******-beta5", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "707c74a304160a2c71c0a3d8c9b25d98401601b0"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/707c74a304160a2c71c0a3d8c9b25d98401601b0", "type": "zip", "shasum": "", "reference": "707c74a304160a2c71c0a3d8c9b25d98401601b0"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0-beta.5"}, "time": "2022-07-13T17:19:49+00:00", "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/serialization": "^1", "revolt/event-loop": "^0.2"}}, {"version": "v2.0.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "f25b8d23e7c268d0427e4d315d9734e26ae19aa8"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/f25b8d23e7c268d0427e4d315d9734e26ae19aa8", "type": "zip", "shasum": "", "reference": "f25b8d23e7c268d0427e4d315d9734e26ae19aa8"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0-beta.4"}, "time": "2022-04-03T16:54:49+00:00"}, {"version": "v2.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "f41cf7fd91018e436ac76da710f157e968dbf750"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/f41cf7fd91018e436ac76da710f157e968dbf750", "type": "zip", "shasum": "", "reference": "f41cf7fd91018e436ac76da710f157e968dbf750"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0-beta.3"}, "time": "2022-02-06T15:36:09+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/serialization": "^1", "revolt/event-loop": "^0.2"}}, {"version": "v2.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "8ad0e1ff5e3477866d2babbf957b3b78ffb6bc39"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/8ad0e1ff5e3477866d2babbf957b3b78ffb6bc39", "type": "zip", "shasum": "", "reference": "8ad0e1ff5e3477866d2babbf957b3b78ffb6bc39"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0-beta.2"}, "time": "2022-02-03T20:56:08+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "revolt/event-loop": "^0.2"}}, {"version": "v2.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "8933d35d03ec29f0a6ee6b29d669b2200bf2766c"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/8933d35d03ec29f0a6ee6b29d669b2200bf2766c", "type": "zip", "shasum": "", "reference": "8933d35d03ec29f0a6ee6b29d669b2200bf2766c"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.0.0-beta.1"}, "time": "2021-12-06T01:07:02+00:00", "require": {"php": ">=8", "amphp/amp": "^3", "revolt/event-loop": "^0.1"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "dev-master"}}, {"version": "v1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "85ab06764f4f36d63b1356b466df6111cf4b89cf"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/85ab06764f4f36d63b1356b466df6111cf4b89cf", "type": "zip", "shasum": "", "reference": "85ab06764f4f36d63b1356b466df6111cf4b89cf"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v1.4.2"}, "time": "2021-10-25T18:29:10+00:00", "autoload": {"files": ["src/functions.php", "src/ConcurrentIterator/functions.php"], "psr-4": {"Amp\\Sync\\": "src"}}, "require": {"php": ">=7.1", "amphp/amp": "^2.2"}, "require-dev": {"phpunit/phpunit": "^9 || ^8 || ^7", "amphp/phpunit-util": "^1.1", "amphp/php-cs-fixer-config": "dev-master"}}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "3482098ba7498cd6555a8eb0ed49d947aab2558f"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/3482098ba7498cd6555a8eb0ed49d947aab2558f", "type": "zip", "shasum": "", "reference": "3482098ba7498cd6555a8eb0ed49d947aab2558f"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v1.4.1"}, "time": "2021-10-25T18:01:58+00:00"}, {"version": "v1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "613047ac54c025aa800a9cde5b05c3add7327ed4"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/613047ac54c025aa800a9cde5b05c3add7327ed4", "type": "zip", "shasum": "", "reference": "613047ac54c025aa800a9cde5b05c3add7327ed4"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v1.4.0"}, "funding": [], "time": "2020-05-07T18:57:50+00:00"}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "512d62e83c8b8d5c848183005c70e70df2bcca55"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/512d62e83c8b8d5c848183005c70e70df2bcca55", "type": "zip", "shasum": "", "reference": "512d62e83c8b8d5c848183005c70e70df2bcca55"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v1.3.0"}, "time": "2019-11-08T18:42:56+00:00", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Sync\\": "src"}}, "require-dev": {"phpunit/phpunit": "^8 || ^7", "amphp/phpunit-util": "^1.1", "amphp/php-cs-fixer-config": "dev-master"}, "funding": "__unset"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "dae12b6bd1aef8f720edf8ceb4595c57a1a1b7db"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/dae12b6bd1aef8f720edf8ceb4595c57a1a1b7db", "type": "zip", "shasum": "", "reference": "dae12b6bd1aef8f720edf8ceb4595c57a1a1b7db"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/master"}, "time": "2019-09-26T20:57:15+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "ceaef57bade610ebae3338f0ea4be1161a59b0ff"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/ceaef57bade610ebae3338f0ea4be1161a59b0ff", "type": "zip", "shasum": "", "reference": "ceaef57bade610ebae3338f0ea4be1161a59b0ff"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v1.1.0"}, "time": "2019-09-24T17:08:58+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "a1d8f244eb19e3e2a96abc4686cebc80995bbc90"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/a1d8f244eb19e3e2a96abc4686cebc80995bbc90", "type": "zip", "shasum": "", "reference": "a1d8f244eb19e3e2a96abc4686cebc80995bbc90"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/thread"}, "time": "2017-11-29T21:48:53+00:00", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\Sync\\": "lib"}}, "require": {"amphp/amp": "^2"}, "require-dev": {"phpunit/phpunit": "^6", "amphp/phpunit-util": "^1", "friendsofphp/php-cs-fixer": "^2.3"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/sync.git", "type": "git", "reference": "fbc88427e0cafcbc4ab53d026bbab4d5d5d2ebb6"}, "dist": {"url": "https://api.github.com/repos/amphp/sync/zipball/fbc88427e0cafcbc4ab53d026bbab4d5d5d2ebb6", "type": "zip", "shasum": "", "reference": "fbc88427e0cafcbc4ab53d026bbab4d5d5d2ebb6"}, "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/master"}, "time": "2017-11-29T19:45:56+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 04 Aug 2024 17:35:45 GMT"}