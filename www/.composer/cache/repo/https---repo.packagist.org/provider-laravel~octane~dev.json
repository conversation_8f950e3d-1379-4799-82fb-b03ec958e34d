{"minified": "composer/2.0", "packages": {"laravel/octane": [{"name": "laravel/octane", "description": "Supercharge your Laravel application's performance.", "keywords": ["laravel", "swoole", "roadrunner", "octane", "frankenphp"], "homepage": "", "version": "2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/laravel/octane.git", "type": "git", "reference": "3840152c7037f513491ae4f2432b6f0e270d2386"}, "dist": {"url": "https://api.github.com/repos/laravel/octane/zipball/3840152c7037f513491ae4f2432b6f0e270d2386", "type": "zip", "shasum": "", "reference": "3840152c7037f513491ae4f2432b6f0e270d2386"}, "type": "library", "support": {"issues": "https://github.com/laravel/octane/issues", "source": "https://github.com/laravel/octane"}, "funding": [], "time": "2025-07-08T15:08:12+00:00", "autoload": {"psr-4": {"Laravel\\Octane\\": "src"}}, "extra": {"laravel": {"aliases": {"Octane": "Laravel\\Octane\\Facades\\Octane"}, "providers": ["Laravel\\Octane\\OctaneServiceProvider"]}, "branch-alias": {"dev-master": "2.x-dev"}}, "bin": ["bin/roadrunner-worker", "bin/swoole-server"], "default-branch": true, "require": {"php": "^8.1.0", "laminas/laminas-diactoros": "^3.0", "symfony/psr-http-message-bridge": "^2.2.0|^6.4|^7.0", "nesbot/carbon": "^2.66.0|^3.0", "symfony/console": "^6.0|^7.0", "laravel/prompts": "^0.1.24|^0.2.0|^0.3.0", "laravel/serializable-closure": "^1.3|^2.0", "laravel/framework": "^10.10.1|^11.0|^12.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.6.1", "laravel/scout": "^10.2.1", "laravel/socialite": "^5.6.1", "mockery/mockery": "^1.5.1", "livewire/livewire": "^2.12.3|^3.0", "nunomaduro/collision": "^6.4.0|^7.5.2|^8.0", "spiral/roadrunner-http": "^3.3.0", "spiral/roadrunner-cli": "^2.6.0", "inertiajs/inertia-laravel": "^1.3.2|^2.0", "orchestra/testbench": "^8.21|^9.0|^10.0", "phpunit/phpunit": "^10.4|^11.5", "phpstan/phpstan": "^2.1.7"}, "conflict": {"spiral/roadrunner": "<2023.1.0", "spiral/roadrunner-cli": "<2.6.0", "spiral/roadrunner-http": "<3.3.0"}}, {"keywords": ["laravel", "swoole", "roadrunner", "octane"], "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"url": "https://github.com/laravel/octane.git", "type": "git", "reference": "8a90a12898bc53e28a027e0017752d6c6269f930"}, "dist": {"url": "https://api.github.com/repos/laravel/octane/zipball/8a90a12898bc53e28a027e0017752d6c6269f930", "type": "zip", "shasum": "", "reference": "8a90a12898bc53e28a027e0017752d6c6269f930"}, "time": "2023-05-16T06:48:32+00:00", "extra": {"laravel": {"aliases": {"Octane": "Laravel\\Octane\\Facades\\Octane"}, "providers": ["Laravel\\Octane\\OctaneServiceProvider"]}, "branch-alias": {"dev-master": "1.x-dev"}}, "require": {"php": "^8.0", "laminas/laminas-diactoros": "^2.5", "symfony/psr-http-message-bridge": "^2.0", "laravel/serializable-closure": "^1.0", "nesbot/carbon": "^2.60", "laravel/framework": "^8.83.26|^9.38.0|^10.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.2", "mockery/mockery": "^1.4", "phpunit/phpunit": "^9.3", "spiral/roadrunner": "^2.8.2", "orchestra/testbench": "^6.16|^7.0|^8.0", "nunomaduro/collision": "^5.10|^6.0|^7.0", "inertiajs/inertia-laravel": "^0.6.9", "laravel/scout": "^9.8", "laravel/socialite": "^5.6", "livewire/livewire": "^2.12", "phpstan/phpstan": "^1.10"}, "conflict": "__unset"}]}, "last-modified": "Tu<PERSON>, 08 Jul 2025 15:08:19 GMT"}