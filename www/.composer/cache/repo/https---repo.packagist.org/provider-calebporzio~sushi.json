{"minified": "composer/2.0", "packages": {"calebporzio/sushi": [{"name": "caleb<PERSON>zio/sushi", "description": "Eloquent's missing \"array\" driver.", "keywords": [], "homepage": "", "version": "v2.5.3", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "caleb<PERSON><PERSON>@gmail.com"}], "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "bf184973f943216b2aaa8dbc79631ea806038bb1"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/bf184973f943216b2aaa8dbc79631ea806038bb1", "type": "zip", "shasum": "", "reference": "bf184973f943216b2aaa8dbc79631ea806038bb1"}, "type": "library", "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.5.3"}, "funding": [{"url": "https://github.com/calebporzio", "type": "github"}], "time": "2025-02-13T21:03:57+00:00", "autoload": {"psr-4": {"Sushi\\": "src/"}}, "require": {"php": "^7.1.3|^8.0", "ext-pdo_sqlite": "*", "ext-sqlite3": "*", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0"}, "require-dev": {"doctrine/dbal": "^2.9 || ^3.1.4", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0 || ^10.0 || ^11.0"}}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "01dd34fe3374f5fb7ce63756c0419385e31cd532"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/01dd34fe3374f5fb7ce63756c0419385e31cd532", "type": "zip", "shasum": "", "reference": "01dd34fe3374f5fb7ce63756c0419385e31cd532"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.5.2"}, "time": "2024-04-24T15:23:03+00:00", "require": {"php": "^7.1.3|^8.0", "ext-pdo_sqlite": "*", "ext-sqlite3": "*", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0"}, "require-dev": {"doctrine/dbal": "^2.9 || ^3.1.4", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0 || ^10.0"}}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "20aad4e9d9ea74df64a86f6d327e55bdfc92d204"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/20aad4e9d9ea74df64a86f6d327e55bdfc92d204", "type": "zip", "shasum": "", "reference": "20aad4e9d9ea74df64a86f6d327e55bdfc92d204"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.5.1"}, "time": "2024-02-05T14:40:50+00:00", "require": {"php": "^7.1.3|^8.0", "ext-sqlite3": "*", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0"}}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "b0fb1e28509a103ada5c37d5f872ddd92dc20717"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/b0fb1e28509a103ada5c37d5f872ddd92dc20717", "type": "zip", "shasum": "", "reference": "b0fb1e28509a103ada5c37d5f872ddd92dc20717"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.5.0"}, "time": "2024-02-04T14:21:59+00:00"}, {"version": "v2.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "932d09781bff75c812541d2d269400fd7d730bab"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/932d09781bff75c812541d2d269400fd7d730bab", "type": "zip", "shasum": "", "reference": "932d09781bff75c812541d2d269400fd7d730bab"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.4.5"}, "time": "2023-10-17T14:42:34+00:00", "require": {"php": "^7.1.3|^8.0", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0 || ^11.0"}}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "8eeafda290e9a09abe6b102c3925c9434d1c87a5"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/8eeafda290e9a09abe6b102c3925c9434d1c87a5", "type": "zip", "shasum": "", "reference": "8eeafda290e9a09abe6b102c3925c9434d1c87a5"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.4.4"}, "time": "2023-01-11T16:19:01+00:00", "require": {"php": "^7.1.3|^8.0", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0 || ^10.0"}, "require-dev": {"doctrine/dbal": "^2.9 || ^3.1.4", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0 || ^6.0 || ^7.0 || ^8.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0"}}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "cec0768285971add48f3ccebfefad856be6f5078"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/cec0768285971add48f3ccebfefad856be6f5078", "type": "zip", "shasum": "", "reference": "cec0768285971add48f3ccebfefad856be6f5078"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.4.0"}, "time": "2022-01-18T22:48:40+00:00", "require": {"php": "^7.1.3|^8.0", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "require-dev": {"doctrine/dbal": "^2.9", "orchestra/database": "3.8.* || 3.9.* || ^4.0 || ^5.0 || ^6.0 || ^7.0", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0 || ^6.0 || ^7.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0"}}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "9139590922edfa1db61e9aa49401889e85ed3b5f"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/9139590922edfa1db61e9aa49401889e85ed3b5f", "type": "zip", "shasum": "", "reference": "9139590922edfa1db61e9aa49401889e85ed3b5f"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.3.1"}, "time": "2021-08-18T14:28:54+00:00", "require": {"php": "^7.1.3|^8.0", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0"}, "require-dev": {"doctrine/dbal": "^2.9", "orchestra/database": "3.8.* || 3.9.* || ^4.0 || ^5.0", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0 || ^6.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0"}}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "75a2418601296797f6b8bd4cb49b2d95797df102"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/75a2418601296797f6b8bd4cb49b2d95797df102", "type": "zip", "shasum": "", "reference": "75a2418601296797f6b8bd4cb49b2d95797df102"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.3.0"}, "time": "2021-08-12T13:54:53+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "3faf827881c27fde73eb5339586e59b14f5cb408"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/3faf827881c27fde73eb5339586e59b14f5cb408", "type": "zip", "shasum": "", "reference": "3faf827881c27fde73eb5339586e59b14f5cb408"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.2.0"}, "time": "2021-04-16T21:59:24+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "8624f602371673f8c2219f27de7cabfab6c0fce2"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/8624f602371673f8c2219f27de7cabfab6c0fce2", "type": "zip", "shasum": "", "reference": "8624f602371673f8c2219f27de7cabfab6c0fce2"}, "support": {"source": "https://github.com/calebporzio/sushi/tree/v2.1.1"}, "time": "2020-11-02T18:15:45+00:00", "require-dev": {"doctrine/dbal": "^2.10", "orchestra/database": "3.8.* || 3.9.* || ^4.0 || ^5.0", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0 || ^6.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "39a7362537fb18136622ee0f6770b430a0eb10e4"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/39a7362537fb18136622ee0f6770b430a0eb10e4", "type": "zip", "shasum": "", "reference": "39a7362537fb18136622ee0f6770b430a0eb10e4"}, "support": {"issues": "https://github.com/calebporzio/sushi/issues", "source": "https://github.com/calebporzio/sushi/tree/master"}, "time": "2020-09-02T02:34:49+00:00", "require": {"php": "^7.1.3", "illuminate/database": "^5.8 || ^6.0 || ^7.0 || ^8.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0 || ^8.0"}, "require-dev": {"doctrine/dbal": "^2.10", "orchestra/database": "3.8.* || 3.9.* || ^4.0", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0", "phpunit/phpunit": "^7.5 || ^8.4 || ^9.0"}}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "df69a1af1fbbc0aed96f6563770f8d79fe0eb6a5"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/df69a1af1fbbc0aed96f6563770f8d79fe0eb6a5", "type": "zip", "shasum": "", "reference": "df69a1af1fbbc0aed96f6563770f8d79fe0eb6a5"}, "time": "2020-03-04T02:49:53+00:00", "require": {"php": "^7.1.3", "illuminate/database": "^5.8 || ^6.0 || ^7.0", "illuminate/support": "^5.8 || ^6.0 || ^7.0"}}, {"version": "v1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "a5620ae4eda911f6c589480f51340a41ac4a417f"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/a5620ae4eda911f6c589480f51340a41ac4a417f", "type": "zip", "shasum": "", "reference": "a5620ae4eda911f6c589480f51340a41ac4a417f"}, "funding": [], "time": "2020-02-15T17:02:53+00:00", "require-dev": {"phpunit/phpunit": "^7.5 || ^8.4 || ^9.0", "orchestra/database": "3.8.* || 3.9.* || ^4.0", "orchestra/testbench": "3.8.* || 3.9.* || ^4.0"}}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "1e5d2ec72db71a9b6fdffbc4a862c544ca3510e5"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/1e5d2ec72db71a9b6fdffbc4a862c544ca3510e5", "type": "zip", "shasum": "", "reference": "1e5d2ec72db71a9b6fdffbc4a862c544ca3510e5"}, "time": "2020-02-14T01:53:06+00:00", "require": {"php": "^7.1.3", "illuminate/database": "^5.8 || ^6.0", "illuminate/support": "^5.8 || ^6.0"}, "funding": "__unset"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "c73e66df62a61318e9f8a3c94a89734f90af28ce"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/c73e66df62a61318e9f8a3c94a89734f90af28ce", "type": "zip", "shasum": "", "reference": "c73e66df62a61318e9f8a3c94a89734f90af28ce"}, "time": "2020-02-14T00:19:35+00:00"}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "41cdd5c3b703af83ed9f0ce6e61b14eeaa5f1aac"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/41cdd5c3b703af83ed9f0ce6e61b14eeaa5f1aac", "type": "zip", "shasum": "", "reference": "41cdd5c3b703af83ed9f0ce6e61b14eeaa5f1aac"}, "time": "2020-02-06T17:42:09+00:00"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "e050694207bf1818d5da9a19bf86bd4dd83f1e25"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/e050694207bf1818d5da9a19bf86bd4dd83f1e25", "type": "zip", "shasum": "", "reference": "e050694207bf1818d5da9a19bf86bd4dd83f1e25"}, "time": "2020-02-06T17:15:56+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "cb0ba066f45525df5284cf0afe1b9a15a7428606"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/cb0ba066f45525df5284cf0afe1b9a15a7428606", "type": "zip", "shasum": "", "reference": "cb0ba066f45525df5284cf0afe1b9a15a7428606"}, "support": {"issues": "https://github.com/calebporzio/sushi/issues", "source": "https://github.com/calebporzio/sushi/tree/v1.0.2"}, "time": "2020-02-04T04:12:17+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "ba984a44f0dd1149fde72b6a7cf7d74251593b75"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/ba984a44f0dd1149fde72b6a7cf7d74251593b75", "type": "zip", "shasum": "", "reference": "ba984a44f0dd1149fde72b6a7cf7d74251593b75"}, "support": {"issues": "https://github.com/calebporzio/sushi/issues", "source": "https://github.com/calebporzio/sushi/tree/master"}, "time": "2020-01-28T14:02:44+00:00", "require": {"illuminate/database": "~5.8.0|^6.0.0", "illuminate/support": "~5.8.0|^6.0.0", "illuminate/filesystem": "~5.8.0|^6.0.0"}, "require-dev": {"phpunit/phpunit": "^8.5", "orchestra/testbench": "^4.5"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/calebporzio/sushi.git", "type": "git", "reference": "467f79f8ae855b60c5811db52218c41c9dc14414"}, "dist": {"url": "https://api.github.com/repos/calebporzio/sushi/zipball/467f79f8ae855b60c5811db52218c41c9dc14414", "type": "zip", "shasum": "", "reference": "467f79f8ae855b60c5811db52218c41c9dc14414"}, "support": {"issues": "https://github.com/calebporzio/sushi/issues", "source": "https://github.com/calebporzio/sushi/tree/v1.0.0"}, "time": "2020-01-25T04:15:01+00:00", "require": {"illuminate/database": "~5.8.0|^6.0.0", "illuminate/support": "~5.8.0|^6.0.0", "orchestra/testbench": "^4.5"}, "require-dev": {"phpunit/phpunit": "^8.5"}}]}, "security-advisories": [], "last-modified": "Thu, 13 Feb 2025 21:04:51 GMT"}