{"minified": "composer/2.0", "packages": {"league/uri": [{"name": "league/uri", "description": "URI manipulation library", "keywords": ["http", "url", "https", "middleware", "uri", "ftp", "hostname", "data-uri", "ws", "rfc3986", "parse_url", "rfc6570", "querystring", "psr-7", "uri-template", "query-string", "rfc3987", "parse_str", "file-uri"], "homepage": "https://uri.thephpleague.com", "version": "7.5.1", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "81fb5145d2644324614cc532b28efd0215bda430"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430", "type": "zip", "shasum": "", "reference": "81fb5145d2644324614cc532b28efd0215bda430"}, "type": "library", "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.5.1"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:40:02+00:00", "autoload": {"psr-4": {"League\\Uri\\": ""}}, "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "require": {"php": "^8.1", "league/uri-interfaces": "^7.5"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "conflict": {"league/uri-schemes": "^1.0"}}, {"version": "7.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "deaf0214f1fcdef41dc92fe4e7a5affa9d64fb60"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/deaf0214f1fcdef41dc92fe4e7a5affa9d64fb60", "type": "zip", "shasum": "", "reference": "deaf0214f1fcdef41dc92fe4e7a5affa9d64fb60"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.5.0"}, "time": "2024-12-08T08:18:47+00:00", "require": {"php": "^8.1", "league/uri-interfaces": "^7.3"}}, {"version": "7.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "bedb6e55eff0c933668addaa7efa1e1f2c417cc4"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/bedb6e55eff0c933668addaa7efa1e1f2c417cc4", "type": "zip", "shasum": "", "reference": "bedb6e55eff0c933668addaa7efa1e1f2c417cc4"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.4.1"}, "time": "2024-03-23T07:42:40+00:00"}, {"version": "7.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "bf414ba956d902f5d98bf9385fcf63954f09dce5"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/bf414ba956d902f5d98bf9385fcf63954f09dce5", "type": "zip", "shasum": "", "reference": "bf414ba956d902f5d98bf9385fcf63954f09dce5"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.4.0"}, "time": "2023-12-01T06:24:25+00:00"}, {"version": "7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "36743c3961bb82bf93da91917b6bced0358a8d45"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/36743c3961bb82bf93da91917b6bced0358a8d45", "type": "zip", "shasum": "", "reference": "36743c3961bb82bf93da91917b6bced0358a8d45"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.3.0"}, "time": "2023-09-09T17:21:43+00:00"}, {"version": "7.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "8b644f8ff80352530bbc0ea467d5b5a89b60d832"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/8b644f8ff80352530bbc0ea467d5b5a89b60d832", "type": "zip", "shasum": "", "reference": "8b644f8ff80352530bbc0ea467d5b5a89b60d832"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.2.1"}, "time": "2023-08-30T21:06:57+00:00", "require": {"php": "^8.1", "league/uri-interfaces": "^7.2"}}, {"version": "7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "795796acd29983cc1433261ffd9d0ebab10c03b9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/795796acd29983cc1433261ffd9d0ebab10c03b9", "type": "zip", "shasum": "", "reference": "795796acd29983cc1433261ffd9d0ebab10c03b9"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.2.0"}, "time": "2023-08-30T19:43:38+00:00", "require": {"php": "^8.1", "league/uri-interfaces": "^7.1"}}, {"version": "7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "c0bf6dfa86b7804fe870b3f3d9c653e35a2c9e3e"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/c0bf6dfa86b7804fe870b3f3d9c653e35a2c9e3e", "type": "zip", "shasum": "", "reference": "c0bf6dfa86b7804fe870b3f3d9c653e35a2c9e3e"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.1.0"}, "time": "2023-08-21T20:15:03+00:00"}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "c7a7e9c5b096c0591a60324276dc901c561fb821"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/c7a7e9c5b096c0591a60324276dc901c561fb821", "type": "zip", "shasum": "", "reference": "c7a7e9c5b096c0591a60324276dc901c561fb821"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.0.0"}, "time": "2023-08-10T14:26:14+00:00", "require": {"php": "^8.1", "league/uri-interfaces": "^7.0"}}, {"version": "7.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "63fdb3d98bd1dc79396daf931e84265d254066b9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/63fdb3d98bd1dc79396daf931e84265d254066b9", "type": "zip", "shasum": "", "reference": "63fdb3d98bd1dc79396daf931e84265d254066b9"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.0.0-beta.2"}, "time": "2023-06-26T18:29:19+00:00", "require": {"php": "^8.1", "ext-json": "*", "league/uri-interfaces": "7.0.0-beta.2", "psr/http-message": "^2.0"}, "suggest": {"ext-intl": "Needed to improve host validation", "ext-fileinfo": "Needed to create Data URI from a filepath", "league/uri-components": "Needed to easily manipulate URI objects", "psr/http-factory": "Needed to use the URI factory", "symfony/polyfill-intl-idn": "to use the IDNA feature via Symfony Polyfill"}}, {"version": "7.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "4e6ac0cab0ae9c6b517bbfb0336f18036583b9cd"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/4e6ac0cab0ae9c6b517bbfb0336f18036583b9cd", "type": "zip", "shasum": "", "reference": "4e6ac0cab0ae9c6b517bbfb0336f18036583b9cd"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.0.0-beta.1"}, "time": "2023-06-26T14:31:20+00:00", "require": {"php": "^8.1", "ext-json": "*", "league/uri-interfaces": "7.x-dev", "psr/http-message": "^2.0"}}, {"version": "6.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "a700b4656e4c54371b799ac61e300ab25a2d1d39"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/a700b4656e4c54371b799ac61e300ab25a2d1d39", "type": "zip", "shasum": "", "reference": "a700b4656e4c54371b799ac61e300ab25a2d1d39"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.8.0"}, "time": "2022-09-13T19:58:47+00:00", "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "require": {"php": "^8.1", "ext-json": "*", "psr/http-message": "^1.0.1", "league/uri-interfaces": "^2.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.9.5", "nyholm/psr7": "^1.5.1", "php-http/psr7-integration-tests": "^1.1.1", "phpbench/phpbench": "^1.2.6", "phpstan/phpstan": "^1.8.5", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.1.1", "phpstan/phpstan-strict-rules": "^1.4.3", "phpunit/phpunit": "^9.5.24", "psr/http-factory": "^1.0.1"}, "suggest": {"league/uri-components": "Needed to easily manipulate URI objects", "ext-intl": "Needed to improve host validation", "ext-fileinfo": "Needed to create Data URI from a filepath", "psr/http-factory": "Needed to use the URI factory"}}, {"version": "6.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "d3b50812dd51f3fbf176344cc2981db03d10fe06"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/d3b50812dd51f3fbf176344cc2981db03d10fe06", "type": "zip", "shasum": "", "reference": "d3b50812dd51f3fbf176344cc2981db03d10fe06"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.7.2"}, "time": "2022-09-13T19:50:42+00:00", "require": {"php": "^7.4 || ^8.0", "ext-json": "*", "psr/http-message": "^1.0", "league/uri-interfaces": "^2.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.3.2", "nyholm/psr7": "^1.5", "php-http/psr7-integration-tests": "^1.1", "phpstan/phpstan": "^1.2.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpunit/phpunit": "^9.5.10", "psr/http-factory": "^1.0"}}, {"version": "6.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "2d7c87a0860f3126a39f44a8a9bf2fed402dcfea"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/2d7c87a0860f3126a39f44a8a9bf2fed402dcfea", "type": "zip", "shasum": "", "reference": "2d7c87a0860f3126a39f44a8a9bf2fed402dcfea"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.7.1"}, "time": "2022-06-29T09:48:18+00:00"}, {"version": "6.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "9bae56eeccfbb1875c33d8757f0edeb65ee64f4d"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/9bae56eeccfbb1875c33d8757f0edeb65ee64f4d", "type": "zip", "shasum": "", "reference": "9bae56eeccfbb1875c33d8757f0edeb65ee64f4d"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.7.0"}, "time": "2022-06-28T21:54:49+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^v3.3.2", "php-http/psr7-integration-tests": "^1.1", "phpstan/phpstan": "^1.2.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpunit/phpunit": "^9.5.10", "psr/http-factory": "^1.0"}}, {"version": "6.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "4147f19b9de3b5af6a258f35d7a0efbbf9963298"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/4147f19b9de3b5af6a258f35d7a0efbbf9963298", "type": "zip", "shasum": "", "reference": "4147f19b9de3b5af6a258f35d7a0efbbf9963298"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.6.0"}, "time": "2022-05-28T05:44:35+00:00"}, {"homepage": "http://uri.thephpleague.com", "version": "6.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "c68ca445abb04817d740ddd6d0b3551826ef0c5a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/c68ca445abb04817d740ddd6d0b3551826ef0c5a", "type": "zip", "shasum": "", "reference": "c68ca445abb04817d740ddd6d0b3551826ef0c5a"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.5.0"}, "time": "2021-08-27T09:54:07+00:00", "require": {"php": "^7.3 || ^8.0", "ext-json": "*", "psr/http-message": "^1.0", "league/uri-interfaces": "^2.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19 || ^3.0", "phpunit/phpunit": "^8.0 || ^9.0", "phpstan/phpstan": "^0.12.90", "phpstan/phpstan-strict-rules": "^0.12.11", "phpstan/phpstan-phpunit": "^0.12.22", "psr/http-factory": "^1.0"}}, {"version": "6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "09da64118eaf4c5d52f9923a1e6a5be1da52fd9a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/09da64118eaf4c5d52f9923a1e6a5be1da52fd9a", "type": "zip", "shasum": "", "reference": "09da64118eaf4c5d52f9923a1e6a5be1da52fd9a"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.4.0"}, "time": "2020-11-22T14:29:11+00:00", "require": {"php": ">=7.2", "ext-json": "*", "psr/http-message": "^1.0", "league/uri-interfaces": "^2.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^8.0 || ^9.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "psr/http-factory": "^1.0"}}, {"version": "6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "aa3babf16e9d7535544bd94e3def026ac56640f3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/aa3babf16e9d7535544bd94e3def026ac56640f3", "type": "zip", "shasum": "", "reference": "aa3babf16e9d7535544bd94e3def026ac56640f3"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/master"}, "time": "2020-08-13T17:18:44+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12"}, "suggest": {"league/uri-components": "Needed to easily manipulate URI objects", "ext-intl": "Needed to improve host validation", "ext-fileinfo": "Needed to create Data URI from a filepath"}}, {"version": "6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "6998530902550c6e3fefb5ef98d56fe92ecdb603"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/6998530902550c6e3fefb5ef98d56fe92ecdb603", "type": "zip", "shasum": "", "reference": "6998530902550c6e3fefb5ef98d56fe92ecdb603"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.2.1"}, "time": "2020-03-17T14:40:17+00:00", "require": {"php": "^7.2", "ext-json": "*", "psr/http-message": "^1.0", "league/uri-interfaces": "^2.1"}}, {"version": "6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "7b29ca33b287b4277d5ce7c26bc1be5d4118b484"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/7b29ca33b287b4277d5ce7c26bc1be5d4118b484", "type": "zip", "shasum": "", "reference": "7b29ca33b287b4277d5ce7c26bc1be5d4118b484"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/master"}, "time": "2020-02-08T16:31:57+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^7.0 | ^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12"}, "funding": "__unset"}, {"version": "6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "ca30d887ad61ef59070171cacfa4b9dec5117f47"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/ca30d887ad61ef59070171cacfa4b9dec5117f47", "type": "zip", "shasum": "", "reference": "ca30d887ad61ef59070171cacfa4b9dec5117f47"}, "time": "2020-01-30T07:55:24+00:00", "require": {"php": "^7.2", "ext-fileinfo": "*", "ext-mbstring": "*", "ext-json": "*", "psr/http-message": "^1.0", "league/uri-interfaces": "^2.0"}, "suggest": {"league/uri-components": "Needed to easily manipulate URI objects", "ext-intl": "Needed to improve host validation"}}, {"keywords": ["http", "url", "https", "middleware", "uri", "ftp", "hostname", "data-uri", "ws", "rfc3986", "parse_url", "querystring", "psr-7", "query-string", "rfc3987", "parse_str", "file-uri"], "version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "4bc9d7ffa0e951d0f8cbbf7f24aaf37b8d9d1330"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/4bc9d7ffa0e951d0f8cbbf7f24aaf37b8d9d1330", "type": "zip", "shasum": "", "reference": "4bc9d7ffa0e951d0f8cbbf7f24aaf37b8d9d1330"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.1.0"}, "time": "2020-01-29T17:05:53+00:00", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "cf488ec34faa3b5c600659c8fc18d67c4752a5cb"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/cf488ec34faa3b5c600659c8fc18d67c4752a5cb", "type": "zip", "shasum": "", "reference": "cf488ec34faa3b5c600659c8fc18d67c4752a5cb"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.0.1"}, "time": "2019-11-23T20:59:00+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^7.0 | ^8.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpstan/phpstan-phpunit": "^0.11"}}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "7fa2caf7bc538c02c939ea3c4599367cc58d8415"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/7fa2caf7bc538c02c939ea3c4599367cc58d8415", "type": "zip", "shasum": "", "reference": "7fa2caf7bc538c02c939ea3c4599367cc58d8415"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.0.0"}, "time": "2019-10-18T10:41:45+00:00"}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "f2bceb755f1108758cf4cf925e4cd7699ce686aa"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/f2bceb755f1108758cf4cf925e4cd7699ce686aa", "type": "zip", "shasum": "", "reference": "f2bceb755f1108758cf4cf925e4cd7699ce686aa"}, "type": "metapackage", "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/master"}, "time": "2018-03-14T17:19:39+00:00", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "require": {"ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "league/uri-components": "^1.8", "league/uri-hostname-parser": "^1.1", "league/uri-interfaces": "^1.0", "league/uri-manipulations": "^1.5", "league/uri-parser": "^1.4", "league/uri-schemes": "^1.2", "php": ">=7.0.13", "psr/http-message": "^1.0"}, "autoload": "__unset", "suggest": "__unset", "conflict": "__unset", "require-dev": "__unset"}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "872194ac1ca5e51fbb9e0e5ba4d27f91aa783e82"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/872194ac1ca5e51fbb9e0e5ba4d27f91aa783e82", "type": "zip", "shasum": "", "reference": "872194ac1ca5e51fbb9e0e5ba4d27f91aa783e82"}, "time": "2017-12-01T13:36:42+00:00", "require": {"ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "league/uri-components": "^1.5.0", "league/uri-hostname-parser": "^1.0.4", "league/uri-interfaces": "^1.0", "league/uri-manipulations": "^1.3", "league/uri-parser": "^1.3.0", "league/uri-schemes": "^1.1.1", "php": ">=7.0.13", "psr/http-message": "^1.0"}}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "80d8fa64a1a86843828e8989cd4fb405d9d7aa15"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/80d8fa64a1a86843828e8989cd4fb405d9d7aa15", "type": "zip", "shasum": "", "reference": "80d8fa64a1a86843828e8989cd4fb405d9d7aa15"}, "time": "2017-11-17T12:04:54+00:00", "extra": {"branch-alias": {"dev-master": "5.1.x-dev"}}, "require": {"ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "jeremykendall/php-domain-parser": "4.0.3-alpha", "league/uri-interfaces": "^1.0", "league/uri-components": "^1.3", "league/uri-manipulations": "^1.1", "league/uri-parser": "^1.2", "league/uri-schemes": "^1.1", "php": ">=7.0", "psr/http-message": "^1.0"}}, {"keywords": ["http", "url", "https", "middleware", "uri", "ftp", "data-uri", "ws", "rfc3986", "parse_url", "psr-7", "rfc3987", "file-uri"], "version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "ef6f413061bd4a9926c4c4394a5f5d384bfd8487"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/ef6f413061bd4a9926c4c4394a5f5d384bfd8487", "type": "zip", "shasum": "", "reference": "ef6f413061bd4a9926c4c4394a5f5d384bfd8487"}, "time": "2017-02-06T08:50:02+00:00", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "require": {"php": ">=7.0", "ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "league/uri-schemes": "^1.0", "league/uri-manipulations": "^1.0"}}, {"keywords": ["http", "url", "https", "uri", "ftp", "data-uri", "ws", "rfc3986", "parse_url", "psr-7"], "version": "4.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "e7a31846c3f00c190bd2817a36e943c22a1e2512"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/e7a31846c3f00c190bd2817a36e943c22a1e2512", "type": "zip", "shasum": "", "reference": "e7a31846c3f00c190bd2817a36e943c22a1e2512"}, "type": "library", "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/4.x"}, "time": "2017-10-17T10:28:56+00:00", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Uri\\": "src"}}, "extra": {"branch-alias": {"dev-master": "4.2.x-dev"}}, "require": {"ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "jeremykendall/php-domain-parser": "4.0.3-alpha", "php": ">=5.5.9", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.9", "phpunit/phpunit": "^4.0", "zendframework/zend-diactoros": "^1.3"}}, {"version": "4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "a2e73bad7e60c3bc61b649680fb8b46876e342e3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/a2e73bad7e60c3bc61b649680fb8b46876e342e3", "type": "zip", "shasum": "", "reference": "a2e73bad7e60c3bc61b649680fb8b46876e342e3"}, "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/master"}, "time": "2016-12-12T11:36:42+00:00", "require": {"ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "jeremykendall/php-domain-parser": "^3.0", "php": ">=5.5.9", "psr/http-message": "^1.0"}}, {"version": "4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "d7f082d3532d8c648427ce2c486d4db4ae604278"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/d7f082d3532d8c648427ce2c486d4db4ae604278", "type": "zip", "shasum": "", "reference": "d7f082d3532d8c648427ce2c486d4db4ae604278"}, "time": "2016-11-24T11:59:59+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^1.9", "phpunit/phpunit": "^4.0"}}, {"version": "4.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "1c02f4eb7e430f75c3edf69e52619429135f2882"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/1c02f4eb7e430f75c3edf69e52619429135f2882", "type": "zip", "shasum": "", "reference": "1c02f4eb7e430f75c3edf69e52619429135f2882"}, "time": "2016-09-30T08:41:39+00:00"}, {"keywords": ["http", "data", "url", "uri", "ftp", "data-uri", "ws", "rfc3986", "parse_url", "psr-7"], "homepage": "http://url.thephpleague.com", "version": "4.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "0cbce9fe7d9808690ebda67b110ad96bcae9daee"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/0cbce9fe7d9808690ebda67b110ad96bcae9daee", "type": "zip", "shasum": "", "reference": "0cbce9fe7d9808690ebda67b110ad96bcae9daee"}, "time": "2016-03-24T08:38:29+00:00", "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "require": {"php": ">=5.5.9", "ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "jeremykendall/php-domain-parser": "^3.0", "psr/http-message": "^1.0"}, "require-dev": {"fabpot/php-cs-fixer": "^1.9", "phpunit/phpunit": "^4.0"}}, {"version": "4.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "a4f0ea3323745214c955af2f6451d7743f30a076"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/a4f0ea3323745214c955af2f6451d7743f30a076", "type": "zip", "shasum": "", "reference": "a4f0ea3323745214c955af2f6451d7743f30a076"}, "time": "2016-02-18T14:46:01+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "671150fbd1d4120746195d6bec1aa78b95b14104"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/671150fbd1d4120746195d6bec1aa78b95b14104", "type": "zip", "shasum": "", "reference": "671150fbd1d4120746195d6bec1aa78b95b14104"}, "time": "2015-11-03T07:54:30+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=5.5.9", "ext-mbstring": "*", "ext-intl": "*", "ext-fileinfo": "*", "psr/http-message": "^1.0", "jeremykendall/php-domain-parser": "^3.0"}, "require-dev": {"phpunit/phpunit": "^4.0", "fabpot/php-cs-fixer": "^1.9"}}, {"keywords": ["http", "url", "uri", "ftp", "ws", "rfc3986", "parse_url", "psr-7"], "version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri.git", "type": "git", "reference": "a22120c5937814dbadaffccef32bf11040f46c0b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri/zipball/a22120c5937814dbadaffccef32bf11040f46c0b", "type": "zip", "shasum": "", "reference": "a22120c5937814dbadaffccef32bf11040f46c0b"}, "time": "2015-09-23T11:09:45+00:00"}]}, "security-advisories": [], "last-modified": "Tu<PERSON>, 17 Dec 2024 16:31:00 GMT"}