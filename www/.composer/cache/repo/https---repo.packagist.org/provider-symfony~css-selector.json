{"minified": "composer/2.0", "packages": {"symfony/css-selector": [{"name": "symfony/css-selector", "description": "Converts CSS selectors to XPath expressions", "keywords": [], "homepage": "https://symfony.com", "version": "v7.3.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "type": "zip", "shasum": "", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "type": "library", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "require": {"php": ">=8.2"}}, {"version": "v7.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0-RC1"}}, {"version": "v7.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0-BETA1"}}, {"version": "v7.2.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.2.0"}}, {"version": "v7.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.2.0-RC1"}}, {"version": "v7.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.2.0-BETA1"}}, {"version": "v7.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4aa4f6b3d6749c14d3aa815eef8226632e7bbc66"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4aa4f6b3d6749c14d3aa815eef8226632e7bbc66", "type": "zip", "shasum": "", "reference": "4aa4f6b3d6749c14d3aa815eef8226632e7bbc66"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.1.6"}, "time": "2024-09-25T14:20:29+00:00"}, {"version": "v7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "1c7cee86c6f812896af54434f8ce29c8d94f9ff4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/1c7cee86c6f812896af54434f8ce29c8d94f9ff4", "type": "zip", "shasum": "", "reference": "1c7cee86c6f812896af54434f8ce29c8d94f9ff4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.1.1"}, "time": "2024-05-31T14:57:53+00:00"}, {"version": "v7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "843f2f7ac5e4c5bf0ec77daef23ca6d4d8922adc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/843f2f7ac5e4c5bf0ec77daef23ca6d4d8922adc", "type": "zip", "shasum": "", "reference": "843f2f7ac5e4c5bf0ec77daef23ca6d4d8922adc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.1.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v7.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.1.0-RC1"}}, {"version": "v7.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.1.0-BETA1"}}, {"version": "v7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "63b9f8c9b3c28c43ad06764c67fe092af2576d17"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/63b9f8c9b3c28c43ad06764c67fe092af2576d17", "type": "zip", "shasum": "", "reference": "63b9f8c9b3c28c43ad06764c67fe092af2576d17"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.8"}, "time": "2024-05-31T14:55:39+00:00"}, {"version": "v7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b08a4ad89e84b29cec285b7b1f781a7ae51cf4bc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b08a4ad89e84b29cec285b7b1f781a7ae51cf4bc", "type": "zip", "shasum": "", "reference": "b08a4ad89e84b29cec285b7b1f781a7ae51cf4bc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.7"}, "time": "2024-04-18T09:29:19+00:00"}, {"version": "v7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ec60a4edf94e63b0556b6a0888548bb400a3a3be"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ec60a4edf94e63b0556b6a0888548bb400a3a3be", "type": "zip", "shasum": "", "reference": "ec60a4edf94e63b0556b6a0888548bb400a3a3be"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.3"}, "time": "2024-01-23T15:02:46+00:00"}, {"version": "v7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bb51d46e53ef8d50d523f0c5faedba056a27943e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bb51d46e53ef8d50d523f0c5faedba056a27943e", "type": "zip", "shasum": "", "reference": "bb51d46e53ef8d50d523f0c5faedba056a27943e"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.0"}, "time": "2023-10-31T17:59:56+00:00"}, {"version": "v7.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.0-RC1"}}, {"version": "v7.0.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.0-BETA3"}}, {"version": "v7.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b729ade9dfb26c1945e72b2515509d45adc08a5f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b729ade9dfb26c1945e72b2515509d45adc08a5f", "type": "zip", "shasum": "", "reference": "b729ade9dfb26c1945e72b2515509d45adc08a5f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v7.0.0-BETA1"}, "time": "2023-07-30T09:45:37+00:00"}, {"version": "v6.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "cb23e97813c5837a041b73a6d63a9ddff0778f5e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/cb23e97813c5837a041b73a6d63a9ddff0778f5e", "type": "zip", "shasum": "", "reference": "cb23e97813c5837a041b73a6d63a9ddff0778f5e"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00", "require": {"php": ">=8.1"}}, {"version": "v6.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4b61b02fe15db48e3687ce1c45ea385d1780fe08"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4b61b02fe15db48e3687ce1c45ea385d1780fe08", "type": "zip", "shasum": "", "reference": "4b61b02fe15db48e3687ce1c45ea385d1780fe08"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.8"}, "time": "2024-05-31T14:49:08+00:00"}, {"version": "v6.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "1c5d5c2103c3762aff27a27e1e2409e30a79083b"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/1c5d5c2103c3762aff27a27e1e2409e30a79083b", "type": "zip", "shasum": "", "reference": "1c5d5c2103c3762aff27a27e1e2409e30a79083b"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.7"}, "time": "2024-04-18T09:22:46+00:00"}, {"version": "v6.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ee0f7ed5cf298cc019431bb3b3977ebc52b86229"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ee0f7ed5cf298cc019431bb3b3977ebc52b86229", "type": "zip", "shasum": "", "reference": "ee0f7ed5cf298cc019431bb3b3977ebc52b86229"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.3"}, "time": "2024-01-23T14:51:35+00:00"}, {"version": "v6.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d036c6c0d0b09e24a14a35f8292146a658f986e4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d036c6c0d0b09e24a14a35f8292146a658f986e4", "type": "zip", "shasum": "", "reference": "d036c6c0d0b09e24a14a35f8292146a658f986e4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.0"}, "time": "2023-10-31T08:40:20+00:00"}, {"version": "v6.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.0-RC1"}}, {"version": "v6.4.0-BETA3", "version_normalized": "*******-beta3", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.0-BETA3"}}, {"version": "v6.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "40eff9db7468587bbf18416ec5cbc2ee6867a356"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/40eff9db7468587bbf18416ec5cbc2ee6867a356", "type": "zip", "shasum": "", "reference": "40eff9db7468587bbf18416ec5cbc2ee6867a356"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.0-BETA1"}, "time": "2023-07-30T09:31:49+00:00"}, {"version": "v6.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7bb2f446287397cc41ebd9acfa0755b16db05fbc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7bb2f446287397cc41ebd9acfa0755b16db05fbc", "type": "zip", "shasum": "", "reference": "7bb2f446287397cc41ebd9acfa0755b16db05fbc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.3.12"}, "time": "2024-01-23T14:35:58+00:00"}, {"version": "v6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "883d961421ab1709877c10ac99451632a3d6fa57"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/883d961421ab1709877c10ac99451632a3d6fa57", "type": "zip", "shasum": "", "reference": "883d961421ab1709877c10ac99451632a3d6fa57"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.3.2"}, "time": "2023-07-12T16:00:22+00:00"}, {"version": "v6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "88453e64cd86c5b60e8d2fb2c6f953bbc353ffbf"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/88453e64cd86c5b60e8d2fb2c6f953bbc353ffbf", "type": "zip", "shasum": "", "reference": "88453e64cd86c5b60e8d2fb2c6f953bbc353ffbf"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.3.0"}, "time": "2023-03-20T16:43:42+00:00"}, {"version": "v6.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.3.0-RC1"}}, {"version": "v6.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.3.0-BETA1"}}, {"version": "v6.2.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "61444c8e4cbe80217a233364d8254fa6df247f50"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/61444c8e4cbe80217a233364d8254fa6df247f50", "type": "zip", "shasum": "", "reference": "61444c8e4cbe80217a233364d8254fa6df247f50"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.13"}, "time": "2023-07-12T15:50:46+00:00"}, {"version": "v6.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "aedf3cb0f5b929ec255d96bbb4909e9932c769e0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/aedf3cb0f5b929ec255d96bbb4909e9932c769e0", "type": "zip", "shasum": "", "reference": "aedf3cb0f5b929ec255d96bbb4909e9932c769e0"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.7"}, "time": "2023-02-14T08:44:56+00:00"}, {"version": "v6.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bf1b9d4ad8b1cf0dbde8b08e0135a2f6259b9ba1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bf1b9d4ad8b1cf0dbde8b08e0135a2f6259b9ba1", "type": "zip", "shasum": "", "reference": "bf1b9d4ad8b1cf0dbde8b08e0135a2f6259b9ba1"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.5"}, "time": "2023-01-01T08:38:09+00:00"}, {"version": "v6.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ab1df4ba3ded7b724766ba3a6e0eca0418e74f80"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ab1df4ba3ded7b724766ba3a6e0eca0418e74f80", "type": "zip", "shasum": "", "reference": "ab1df4ba3ded7b724766ba3a6e0eca0418e74f80"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.3"}, "time": "2022-12-28T14:26:22+00:00"}, {"version": "v6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "91c342ffc99283c43653ed8eb47bc2a94db7f398"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/91c342ffc99283c43653ed8eb47bc2a94db7f398", "type": "zip", "shasum": "", "reference": "91c342ffc99283c43653ed8eb47bc2a94db7f398"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.0"}, "time": "2022-08-26T05:51:22+00:00"}, {"version": "v6.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.0-RC1"}}, {"version": "v6.2.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.2.0-BETA1"}}, {"version": "v6.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "750a731856cd1b01c15ee0ea241addca75a376e0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/750a731856cd1b01c15ee0ea241addca75a376e0", "type": "zip", "shasum": "", "reference": "750a731856cd1b01c15ee0ea241addca75a376e0"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.1.11"}, "time": "2023-01-01T08:36:55+00:00"}, {"version": "v6.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a90ee25248cacfe0b1eab3b6c20e492ec5e22357"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a90ee25248cacfe0b1eab3b6c20e492ec5e22357", "type": "zip", "shasum": "", "reference": "a90ee25248cacfe0b1eab3b6c20e492ec5e22357"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.1.9"}, "time": "2022-12-28T14:22:24+00:00"}, {"version": "v6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0dd5e36b80e1de97f8f74ed7023ac2b837a36443"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0dd5e36b80e1de97f8f74ed7023ac2b837a36443", "type": "zip", "shasum": "", "reference": "0dd5e36b80e1de97f8f74ed7023ac2b837a36443"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.1.3"}, "time": "2022-06-27T17:24:16+00:00"}, {"version": "v6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "05c40f02f621609404b8820ff8bc39acb46e19cf"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/05c40f02f621609404b8820ff8bc39acb46e19cf", "type": "zip", "shasum": "", "reference": "05c40f02f621609404b8820ff8bc39acb46e19cf"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.1.0"}, "time": "2022-02-25T11:15:52+00:00"}, {"version": "v6.1.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.1.0-RC1"}}, {"version": "v6.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.1.0-BETA1"}}, {"version": "v6.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f1d00bddb83a4cb2138564b2150001cb6ce272b1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f1d00bddb83a4cb2138564b2150001cb6ce272b1", "type": "zip", "shasum": "", "reference": "f1d00bddb83a4cb2138564b2150001cb6ce272b1"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.19"}, "time": "2023-01-01T08:36:10+00:00", "require": {"php": ">=8.0.2"}}, {"version": "v6.0.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3e526b732295b5d4c16c38d557b74ba8498a92b4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3e526b732295b5d4c16c38d557b74ba8498a92b4", "type": "zip", "shasum": "", "reference": "3e526b732295b5d4c16c38d557b74ba8498a92b4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.17"}, "time": "2022-12-28T14:21:34+00:00"}, {"version": "v6.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ab2746acddc4f03a7234c8441822ac5d5c63efe9"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ab2746acddc4f03a7234c8441822ac5d5c63efe9", "type": "zip", "shasum": "", "reference": "ab2746acddc4f03a7234c8441822ac5d5c63efe9"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.11"}, "time": "2022-06-27T17:10:44+00:00"}, {"version": "v6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "1955d595c12c111629cc814d3f2a2ff13580508a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/1955d595c12c111629cc814d3f2a2ff13580508a", "type": "zip", "shasum": "", "reference": "1955d595c12c111629cc814d3f2a2ff13580508a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.3"}, "time": "2022-01-02T09:55:41+00:00"}, {"version": "v6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "380f86c1a9830226f42a08b5926f18aed4195f25"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/380f86c1a9830226f42a08b5926f18aed4195f25", "type": "zip", "shasum": "", "reference": "380f86c1a9830226f42a08b5926f18aed4195f25"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.2"}, "time": "2021-12-16T22:13:01+00:00"}, {"version": "v6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ede53cafe1784b9131a48774b54f281d5d003f65"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ede53cafe1784b9131a48774b54f281d5d003f65", "type": "zip", "shasum": "", "reference": "ede53cafe1784b9131a48774b54f281d5d003f65"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.1"}, "time": "2021-12-08T15:13:44+00:00"}, {"version": "v6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3a61e2e4fbda3fb7fb5d83620c30fef726139e1c"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3a61e2e4fbda3fb7fb5d83620c30fef726139e1c", "type": "zip", "shasum": "", "reference": "3a61e2e4fbda3fb7fb5d83620c30fef726139e1c"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.0"}, "time": "2021-09-09T12:56:10+00:00"}, {"version": "v6.0.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.0-RC1"}}, {"version": "v6.0.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.0-BETA1"}}, {"version": "v5.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4f7f3c35fba88146b56d0025d20ace3f3901f097"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4f7f3c35fba88146b56d0025d20ace3f3901f097", "type": "zip", "shasum": "", "reference": "4f7f3c35fba88146b56d0025d20ace3f3901f097"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.45"}, "time": "2024-09-25T14:11:13+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.4.40", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "type": "zip", "shasum": "", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.40"}, "time": "2024-05-31T14:33:22+00:00"}, {"version": "v5.4.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0934c9f1d433776f25c629bdc93f3e157d139e08"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0934c9f1d433776f25c629bdc93f3e157d139e08", "type": "zip", "shasum": "", "reference": "0934c9f1d433776f25c629bdc93f3e157d139e08"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.39"}, "time": "2024-04-18T08:26:06+00:00"}, {"version": "v5.4.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9e615d367e2bed41f633abb383948c96a2dbbfae"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9e615d367e2bed41f633abb383948c96a2dbbfae", "type": "zip", "shasum": "", "reference": "9e615d367e2bed41f633abb383948c96a2dbbfae"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.35"}, "time": "2024-01-23T13:51:25+00:00"}, {"version": "v5.4.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0ad3f7e9a1ab492c5b4214cf22a9dc55dcf8600a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0ad3f7e9a1ab492c5b4214cf22a9dc55dcf8600a", "type": "zip", "shasum": "", "reference": "0ad3f7e9a1ab492c5b4214cf22a9dc55dcf8600a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.26"}, "time": "2023-07-07T06:10:25+00:00"}, {"version": "v5.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "95f3c7468db1da8cc360b24fa2a26e7cefcb355d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/95f3c7468db1da8cc360b24fa2a26e7cefcb355d", "type": "zip", "shasum": "", "reference": "95f3c7468db1da8cc360b24fa2a26e7cefcb355d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.21"}, "time": "2023-02-14T08:03:56+00:00"}, {"version": "v5.4.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f4a7d150f5b9e8f974f6f127d8167e420d11fc62"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f4a7d150f5b9e8f974f6f127d8167e420d11fc62", "type": "zip", "shasum": "", "reference": "f4a7d150f5b9e8f974f6f127d8167e420d11fc62"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.19"}, "time": "2023-01-01T08:32:19+00:00"}, {"version": "v5.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "052ef49b660f9ad2a3adb311c555c9bc11ba61f4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/052ef49b660f9ad2a3adb311c555c9bc11ba61f4", "type": "zip", "shasum": "", "reference": "052ef49b660f9ad2a3adb311c555c9bc11ba61f4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.17"}, "time": "2022-12-23T11:40:44+00:00"}, {"version": "v5.4.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c1681789f059ab756001052164726ae88512ae3d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c1681789f059ab756001052164726ae88512ae3d", "type": "zip", "shasum": "", "reference": "c1681789f059ab756001052164726ae88512ae3d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.11"}, "time": "2022-06-27T16:58:25+00:00"}, {"version": "v5.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b0a190285cd95cb019237851205b8140ef6e368e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b0a190285cd95cb019237851205b8140ef6e368e", "type": "zip", "shasum": "", "reference": "b0a190285cd95cb019237851205b8140ef6e368e"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.3"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v5.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "cfcbee910e159df402603502fe387e8b677c22fd"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/cfcbee910e159df402603502fe387e8b677c22fd", "type": "zip", "shasum": "", "reference": "cfcbee910e159df402603502fe387e8b677c22fd"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.2"}, "time": "2021-12-16T21:58:21+00:00"}, {"version": "v5.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "44b933f98bb4b5220d10bed9ce5662f8c2d13dcc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/44b933f98bb4b5220d10bed9ce5662f8c2d13dcc", "type": "zip", "shasum": "", "reference": "44b933f98bb4b5220d10bed9ce5662f8c2d13dcc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.0"}, "time": "2021-09-09T08:06:01+00:00"}, {"version": "v5.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.0-RC1"}}, {"version": "v5.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.0-BETA1"}}, {"version": "v5.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9e3a9e99095fd55fb68c0ffe2f7e10ae13ac66ee"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9e3a9e99095fd55fb68c0ffe2f7e10ae13ac66ee", "type": "zip", "shasum": "", "reference": "9e3a9e99095fd55fb68c0ffe2f7e10ae13ac66ee"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.14"}, "time": "2022-01-02T09:51:59+00:00"}, {"version": "v5.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7fb120adc7f600a59027775b224c13a33530dd90"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7fb120adc7f600a59027775b224c13a33530dd90", "type": "zip", "shasum": "", "reference": "7fb120adc7f600a59027775b224c13a33530dd90"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.4"}, "time": "2021-07-21T12:38:00+00:00"}, {"version": "v5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "fcd0b29a7a0b1bb5bfbedc6231583d77fea04814"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/fcd0b29a7a0b1bb5bfbedc6231583d77fea04814", "type": "zip", "shasum": "", "reference": "fcd0b29a7a0b1bb5bfbedc6231583d77fea04814"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.0"}, "time": "2021-05-26T17:40:38+00:00", "require": {"php": ">=7.2.5"}}, {"version": "v5.3.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5d5f97809015102116208b976eb2edb44b689560"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5d5f97809015102116208b976eb2edb44b689560", "type": "zip", "shasum": "", "reference": "5d5f97809015102116208b976eb2edb44b689560"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.0-RC1"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.3.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "59a684f5ac454f066ecbe6daecce6719aed283fb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/59a684f5ac454f066ecbe6daecce6719aed283fb", "type": "zip", "shasum": "", "reference": "59a684f5ac454f066ecbe6daecce6719aed283fb"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.0-BETA1"}, "time": "2021-04-07T16:07:52+00:00"}, {"version": "v5.2.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7fb120adc7f600a59027775b224c13a33530dd90"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7fb120adc7f600a59027775b224c13a33530dd90", "type": "zip", "shasum": "", "reference": "7fb120adc7f600a59027775b224c13a33530dd90"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.12"}, "time": "2021-07-21T12:38:00+00:00", "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}}, {"version": "v5.2.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "fcd0b29a7a0b1bb5bfbedc6231583d77fea04814"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/fcd0b29a7a0b1bb5bfbedc6231583d77fea04814", "type": "zip", "shasum": "", "reference": "fcd0b29a7a0b1bb5bfbedc6231583d77fea04814"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.0"}, "time": "2021-05-26T17:40:38+00:00", "require": {"php": ">=7.2.5"}}, {"version": "v5.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5d5f97809015102116208b976eb2edb44b689560"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5d5f97809015102116208b976eb2edb44b689560", "type": "zip", "shasum": "", "reference": "5d5f97809015102116208b976eb2edb44b689560"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.9"}, "time": "2021-05-16T13:07:46+00:00"}, {"version": "v5.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "59a684f5ac454f066ecbe6daecce6719aed283fb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/59a684f5ac454f066ecbe6daecce6719aed283fb", "type": "zip", "shasum": "", "reference": "59a684f5ac454f066ecbe6daecce6719aed283fb"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.3.0-BETA1"}, "time": "2021-04-07T16:07:52+00:00"}, {"version": "v5.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f65f217b3314504a1ec99c2d6ef69016bb13490f", "type": "zip", "shasum": "", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.4"}, "time": "2021-01-27T10:01:46+00:00"}, {"version": "v5.2.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.3"}}, {"version": "v5.2.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.2"}}, {"description": "Symfony CssSelector Component", "version": "v5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f789e7ead4c79e04ca9a6d6162fc629c89bd8054"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f789e7ead4c79e04ca9a6d6162fc629c89bd8054", "type": "zip", "shasum": "", "reference": "f789e7ead4c79e04ca9a6d6162fc629c89bd8054"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.1"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b8d8eb06b0942e84a69e7acebc3e9c1e6e6e7256"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b8d8eb06b0942e84a69e7acebc3e9c1e6e6e7256", "type": "zip", "shasum": "", "reference": "b8d8eb06b0942e84a69e7acebc3e9c1e6e6e7256"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.0"}, "time": "2020-10-28T21:31:18+00:00"}, {"version": "v5.2.0-RC2", "version_normalized": "*******-RC2", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.0-RC2"}}, {"version": "v5.2.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.0-RC1"}}, {"version": "v5.2.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6cbebda22ffc0d4bb8fea0c1311c2ca54c4c8fa0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6cbebda22ffc0d4bb8fea0c1311c2ca54c4c8fa0", "type": "zip", "shasum": "", "reference": "6cbebda22ffc0d4bb8fea0c1311c2ca54c4c8fa0"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.0-BETA3"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.2.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bbd38ed2a927e26699b118e85923ed5706ca2ece"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bbd38ed2a927e26699b118e85923ed5706ca2ece", "type": "zip", "shasum": "", "reference": "bbd38ed2a927e26699b118e85923ed5706ca2ece"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.0-BETA2"}, "time": "2020-10-13T13:22:54+00:00", "extra": {"branch-version": "5.2"}}, {"version": "v5.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "59f93b18026535f06263f768082017ff7a9160d8"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/59f93b18026535f06263f768082017ff7a9160d8", "type": "zip", "shasum": "", "reference": "59f93b18026535f06263f768082017ff7a9160d8"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2020-05-20T17:44:07+00:00", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}}, {"description": "Converts CSS selectors to XPath expressions", "version": "v5.1.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f65f217b3314504a1ec99c2d6ef69016bb13490f", "type": "zip", "shasum": "", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.11"}, "time": "2021-01-27T10:01:46+00:00", "extra": "__unset"}, {"description": "Symfony CssSelector Component", "version": "v5.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f8315dad49f6b48de98954741c65f6ff59ad15d2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f8315dad49f6b48de98954741c65f6ff59ad15d2", "type": "zip", "shasum": "", "reference": "f8315dad49f6b48de98954741c65f6ff59ad15d2"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.10"}, "time": "2020-12-08T17:02:38+00:00"}, {"version": "v5.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b8d8eb06b0942e84a69e7acebc3e9c1e6e6e7256"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b8d8eb06b0942e84a69e7acebc3e9c1e6e6e7256", "type": "zip", "shasum": "", "reference": "b8d8eb06b0942e84a69e7acebc3e9c1e6e6e7256"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.0-RC2"}, "time": "2020-10-28T21:31:18+00:00"}, {"version": "v5.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6cbebda22ffc0d4bb8fea0c1311c2ca54c4c8fa0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6cbebda22ffc0d4bb8fea0c1311c2ca54c4c8fa0", "type": "zip", "shasum": "", "reference": "6cbebda22ffc0d4bb8fea0c1311c2ca54c4c8fa0"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.8"}, "time": "2020-10-24T12:01:57+00:00"}, {"version": "v5.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e544e24472d4c97b2d11ade7caacd446727c6bf9"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e544e24472d4c97b2d11ade7caacd446727c6bf9", "type": "zip", "shasum": "", "reference": "e544e24472d4c97b2d11ade7caacd446727c6bf9"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.4"}, "time": "2020-05-20T17:43:50+00:00", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}, {"version": "v5.1.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.5"}}, {"version": "v5.1.5", "version_normalized": "*******"}, {"version": "v5.1.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.4"}}, {"version": "v5.1.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.1"}}, {"version": "v5.1.2", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/5.1"}}, {"version": "v5.1.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.1"}}, {"version": "v5.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/5.1"}}, {"version": "v5.1.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v5.1.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c039862c4571cfb2e892033a9f39e9b59f84b446"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c039862c4571cfb2e892033a9f39e9b59f84b446", "type": "zip", "shasum": "", "reference": "c039862c4571cfb2e892033a9f39e9b59f84b446"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.1.0-BETA1"}, "time": "2020-03-30T11:43:41+00:00", "require": {"php": "^7.2.5"}}, {"version": "v5.1.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/master"}}, {"version": "v5.0.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "79c224cdbfae58d54b257a8c684ad445042c90f2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/79c224cdbfae58d54b257a8c684ad445042c90f2", "type": "zip", "shasum": "", "reference": "79c224cdbfae58d54b257a8c684ad445042c90f2"}, "support": {"source": "https://github.com/symfony/css-selector/tree/5.0"}, "time": "2020-05-20T17:38:26+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.2.5"}}, {"version": "v5.0.10", "version_normalized": "********"}, {"version": "v5.0.9", "version_normalized": "*******"}, {"version": "v5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5f8d5271303dad260692ba73dfa21777d38e124e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5f8d5271303dad260692ba73dfa21777d38e124e", "type": "zip", "shasum": "", "reference": "5f8d5271303dad260692ba73dfa21777d38e124e"}, "time": "2020-03-27T16:56:45+00:00", "require": {"php": "^7.2.5"}}, {"version": "v5.0.7", "version_normalized": "*******"}, {"version": "v5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "1d13e7da72fb04e666e18b3c109d3ae039e8bcfe"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/1d13e7da72fb04e666e18b3c109d3ae039e8bcfe", "type": "zip", "shasum": "", "reference": "1d13e7da72fb04e666e18b3c109d3ae039e8bcfe"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.0.6"}, "time": "2020-03-16T12:10:54+00:00"}, {"version": "v5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a0b51ba9938ccc206d9284de7eb527c2d4550b44"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a0b51ba9938ccc206d9284de7eb527c2d4550b44", "type": "zip", "shasum": "", "reference": "a0b51ba9938ccc206d9284de7eb527c2d4550b44"}, "support": {"source": "https://github.com/symfony/css-selector/tree/5.0"}, "funding": [], "time": "2020-02-04T09:41:09+00:00"}, {"version": "v5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ff60c90cb7950b592ebc84ad1289d0345bf24f9f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ff60c90cb7950b592ebc84ad1289d0345bf24f9f", "type": "zip", "shasum": "", "reference": "ff60c90cb7950b592ebc84ad1289d0345bf24f9f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.0.4"}, "time": "2020-01-04T14:08:26+00:00", "funding": "__unset"}, {"version": "v5.0.3", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/5.0"}}, {"version": "v5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "19d29e7098b7b2c3313cb03902ca30f100dcb837"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/19d29e7098b7b2c3313cb03902ca30f100dcb837", "type": "zip", "shasum": "", "reference": "19d29e7098b7b2c3313cb03902ca30f100dcb837"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v5.0.0"}, "time": "2019-11-18T17:27:11+00:00"}, {"version": "v5.0.1", "version_normalized": "*******"}, {"version": "v5.0.0", "version_normalized": "*******"}, {"version": "v5.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "dd2d4070d094cb8ff3cdb9b101f5b2456ddeaf53"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/dd2d4070d094cb8ff3cdb9b101f5b2456ddeaf53", "type": "zip", "shasum": "", "reference": "dd2d4070d094cb8ff3cdb9b101f5b2456ddeaf53"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2019-10-22T17:21:12+00:00", "require": {"php": "^7.2.9"}}, {"version": "v5.0.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.0.0-BETA1"}}, {"version": "v5.0.0-BETA1", "version_normalized": "*******-beta1"}, {"description": "Converts CSS selectors to XPath expressions", "version": "v4.4.44", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bd0a6737e48de45b4b0b7b6fc98c78404ddceaed"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bd0a6737e48de45b4b0b7b6fc98c78404ddceaed", "type": "zip", "shasum": "", "reference": "bd0a6737e48de45b4b0b7b6fc98c78404ddceaed"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00", "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "extra": "__unset"}, {"version": "v4.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0628e6c6d7c92f1a7bae543959bdc17347be2436"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0628e6c6d7c92f1a7bae543959bdc17347be2436", "type": "zip", "shasum": "", "reference": "0628e6c6d7c92f1a7bae543959bdc17347be2436"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"version": "v4.4.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5194f18bd80d106f11efa8f7cd0fbdcc3af96ce6"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5194f18bd80d106f11efa8f7cd0fbdcc3af96ce6", "type": "zip", "shasum": "", "reference": "5194f18bd80d106f11efa8f7cd0fbdcc3af96ce6"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.27"}, "time": "2021-07-21T12:19:41+00:00"}, {"version": "v4.4.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c1e29de6dc893b130b45d20d8051efbb040560a9"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c1e29de6dc893b130b45d20d8051efbb040560a9", "type": "zip", "shasum": "", "reference": "c1e29de6dc893b130b45d20d8051efbb040560a9"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.25"}, "time": "2021-05-26T17:39:37+00:00", "require": {"php": ">=7.1.3"}}, {"version": "v4.4.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "947cacaf1b3a2af6f13a435392873d5ddaba5f70"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/947cacaf1b3a2af6f13a435392873d5ddaba5f70", "type": "zip", "shasum": "", "reference": "947cacaf1b3a2af6f13a435392873d5ddaba5f70"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.24"}, "time": "2021-05-16T09:52:47+00:00"}, {"version": "v4.4.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "01c77324d1d47efbfd7891f62a7c256c69330115"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/01c77324d1d47efbfd7891f62a7c256c69330115", "type": "zip", "shasum": "", "reference": "01c77324d1d47efbfd7891f62a7c256c69330115"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.22"}, "time": "2021-04-07T15:47:03+00:00"}, {"version": "v4.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f907d3e53ecb2a5fad8609eb2f30525287a734c8"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f907d3e53ecb2a5fad8609eb2f30525287a734c8", "type": "zip", "shasum": "", "reference": "f907d3e53ecb2a5fad8609eb2f30525287a734c8"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.20"}, "time": "2021-01-27T09:09:26+00:00"}, {"version": "v4.4.19", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.19"}}, {"description": "Symfony CssSelector Component", "version": "v4.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "74bd82e75da256ad20851af6ded07823332216c7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/74bd82e75da256ad20851af6ded07823332216c7", "type": "zip", "shasum": "", "reference": "74bd82e75da256ad20851af6ded07823332216c7"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.18"}, "time": "2020-12-08T16:59:59+00:00"}, {"version": "v4.4.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5e6efcb6e5d120249da366417e2517c55b50c931"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5e6efcb6e5d120249da366417e2517c55b50c931", "type": "zip", "shasum": "", "reference": "5e6efcb6e5d120249da366417e2517c55b50c931"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.17"}, "time": "2020-10-28T20:42:29+00:00"}, {"version": "v4.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "719506cffda9dba80c75d94ac50f1a2561520e4f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/719506cffda9dba80c75d94ac50f1a2561520e4f", "type": "zip", "shasum": "", "reference": "719506cffda9dba80c75d94ac50f1a2561520e4f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.16"}, "time": "2020-10-24T11:50:19+00:00"}, {"version": "v4.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bf17dc9f6ce144e41f786c32435feea4d8e11dcc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bf17dc9f6ce144e41f786c32435feea4d8e11dcc", "type": "zip", "shasum": "", "reference": "bf17dc9f6ce144e41f786c32435feea4d8e11dcc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.15"}, "time": "2020-07-05T09:39:30+00:00", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}, {"version": "v4.4.14", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.14"}}, {"version": "v4.4.13", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.12"}}, {"version": "v4.4.12", "version_normalized": "********"}, {"version": "v4.4.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.11"}}, {"version": "v4.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "afc26133a6fbdd4f8842e38893e0ee4685c7c94b"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/afc26133a6fbdd4f8842e38893e0ee4685c7c94b", "type": "zip", "shasum": "", "reference": "afc26133a6fbdd4f8842e38893e0ee4685c7c94b"}, "support": {"source": "https://github.com/symfony/css-selector/tree/4.4"}, "time": "2020-03-27T16:54:36+00:00", "require": {"php": "^7.1.3"}}, {"version": "v4.4.9", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.7"}}, {"version": "v4.4.8", "version_normalized": "*******"}, {"version": "v4.4.7", "version_normalized": "*******"}, {"version": "v4.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "402251c6fd69806a70a2b0e1426d16f8487f0f9a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/402251c6fd69806a70a2b0e1426d16f8487f0f9a", "type": "zip", "shasum": "", "reference": "402251c6fd69806a70a2b0e1426d16f8487f0f9a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/4.4"}, "time": "2020-03-16T08:56:54+00:00"}, {"version": "v4.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d0a6dd288fa8848dcc3d1f58b94de6a7cc5d2d22"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d0a6dd288fa8848dcc3d1f58b94de6a7cc5d2d22", "type": "zip", "shasum": "", "reference": "d0a6dd288fa8848dcc3d1f58b94de6a7cc5d2d22"}, "funding": [], "time": "2020-02-04T09:01:01+00:00"}, {"version": "v4.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a167b1860995b926d279f9bb538f873e3bfa3465"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a167b1860995b926d279f9bb538f873e3bfa3465", "type": "zip", "shasum": "", "reference": "a167b1860995b926d279f9bb538f873e3bfa3465"}, "time": "2020-01-04T13:00:46+00:00", "funding": "__unset"}, {"version": "v4.4.3", "version_normalized": "*******"}, {"version": "v4.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "64acec7e0d67125e9f4656c68d4a38a42ab5a0b7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/64acec7e0d67125e9f4656c68d4a38a42ab5a0b7", "type": "zip", "shasum": "", "reference": "64acec7e0d67125e9f4656c68d4a38a42ab5a0b7"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.0-BETA1"}, "time": "2019-10-12T00:35:04+00:00"}, {"version": "v4.4.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/4.4"}}, {"version": "v4.4.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.0-BETA1"}}, {"version": "v4.4.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/4.4"}}, {"version": "v4.4.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.4.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.0-BETA1"}}, {"version": "v4.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "32203e7cc318dcfd1d5fb12ab35e595fc6016206"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/32203e7cc318dcfd1d5fb12ab35e595fc6016206", "type": "zip", "shasum": "", "reference": "32203e7cc318dcfd1d5fb12ab35e595fc6016206"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.11"}, "time": "2020-01-04T12:24:57+00:00", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}}, {"version": "v4.3.10", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/4.3"}}, {"version": "v4.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f4b3ff6a549d9ed28b2b0ecd1781bf67cf220ee9"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f4b3ff6a549d9ed28b2b0ecd1781bf67cf220ee9", "type": "zip", "shasum": "", "reference": "f4b3ff6a549d9ed28b2b0ecd1781bf67cf220ee9"}, "time": "2019-10-02T08:36:26+00:00"}, {"version": "v4.3.8", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.7"}}, {"version": "v4.3.7", "version_normalized": "*******"}, {"version": "v4.3.6", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/4.3"}}, {"version": "v4.3.5", "version_normalized": "*******"}, {"version": "v4.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c6e5e2a00db768c92c3ae131532af4e1acc7bd03"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c6e5e2a00db768c92c3ae131532af4e1acc7bd03", "type": "zip", "shasum": "", "reference": "c6e5e2a00db768c92c3ae131532af4e1acc7bd03"}, "time": "2019-08-20T14:07:54+00:00"}, {"version": "v4.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "105c98bb0c5d8635bea056135304bd8edcc42b4d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/105c98bb0c5d8635bea056135304bd8edcc42b4d", "type": "zip", "shasum": "", "reference": "105c98bb0c5d8635bea056135304bd8edcc42b4d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.0-BETA2"}, "time": "2019-01-16T21:53:39+00:00"}, {"version": "v4.3.2", "version_normalized": "*******", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.0-BETA1"}}, {"version": "v4.3.1", "version_normalized": "*******"}, {"version": "v4.3.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.0-BETA2"}}, {"version": "v4.3.0-RC1", "version_normalized": "*******-RC1", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.0-BETA1"}}, {"version": "v4.3.0-BETA2", "version_normalized": "*******-beta2", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.0-BETA2"}}, {"version": "v4.3.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.3.0-BETA1"}}, {"version": "v4.2.12", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "48eddf66950fa57996e1be4a55916d65c10c604a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/48eddf66950fa57996e1be4a55916d65c10c604a", "type": "zip", "shasum": "", "reference": "48eddf66950fa57996e1be4a55916d65c10c604a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.2.8"}, "time": "2019-01-16T20:31:39+00:00", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}}, {"version": "v4.2.11", "version_normalized": "********"}, {"version": "v4.2.10", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "support": {"source": "https://github.com/symfony/css-selector/tree/v4.2.5"}}, {"version": "v4.2.9", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.2.8"}}, {"version": "v4.2.8", "version_normalized": "*******"}, {"version": "v4.2.7", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.2.5"}}, {"version": "v4.2.6", "version_normalized": "4.2.6.0"}, {"version": "v4.2.5", "version_normalized": "*******"}, {"version": "v4.2.4", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/css-selector/tree/4.2"}}, {"version": "v4.2.3", "version_normalized": "*******"}, {"version": "v4.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "76dac1dbe2830213e95892c7c2ec1edd74113ea4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/76dac1dbe2830213e95892c7c2ec1edd74113ea4", "type": "zip", "shasum": "", "reference": "76dac1dbe2830213e95892c7c2ec1edd74113ea4"}, "time": "2019-01-03T09:07:35+00:00"}, {"version": "v4.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "aa9fa526ba1b2ec087ffdfb32753803d999fcfcd"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/aa9fa526ba1b2ec087ffdfb32753803d999fcfcd", "type": "zip", "shasum": "", "reference": "aa9fa526ba1b2ec087ffdfb32753803d999fcfcd"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2018-11-11T19:52:12+00:00"}, {"version": "v4.2.0", "version_normalized": "*******"}, {"version": "v4.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.2.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3504dc5c848fe7362561fdeddaf24c2616577a43"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3504dc5c848fe7362561fdeddaf24c2616577a43", "type": "zip", "shasum": "", "reference": "3504dc5c848fe7362561fdeddaf24c2616577a43"}, "time": "2018-10-02T16:38:08+00:00"}, {"version": "v4.1.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "70f0cdf76779af4d5be14a3cd11c0200fd304ee6"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/70f0cdf76779af4d5be14a3cd11c0200fd304ee6", "type": "zip", "shasum": "", "reference": "70f0cdf76779af4d5be14a3cd11c0200fd304ee6"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v4.1.12"}, "time": "2019-01-16T18:35:49+00:00", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}}, {"version": "v4.1.11", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/4.1"}}, {"version": "v4.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0acb5c774f54b3c4ddc9ce6b0f94aef125a01632"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0acb5c774f54b3c4ddc9ce6b0f94aef125a01632", "type": "zip", "shasum": "", "reference": "0acb5c774f54b3c4ddc9ce6b0f94aef125a01632"}, "time": "2019-01-03T09:05:57+00:00"}, {"version": "v4.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9e4dc57949853315561f0cd5eb84d0707465502a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9e4dc57949853315561f0cd5eb84d0707465502a", "type": "zip", "shasum": "", "reference": "9e4dc57949853315561f0cd5eb84d0707465502a"}, "time": "2018-11-11T19:51:29+00:00"}, {"version": "v4.1.8", "version_normalized": "*******"}, {"version": "v4.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d67de79a70a27d93c92c47f37ece958bf8de4d8a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d67de79a70a27d93c92c47f37ece958bf8de4d8a", "type": "zip", "shasum": "", "reference": "d67de79a70a27d93c92c47f37ece958bf8de4d8a"}, "time": "2018-10-02T16:36:10+00:00"}, {"version": "v4.1.6", "version_normalized": "*******"}, {"version": "v4.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9ac515bde3c725ca46efa918d37e37c7cece6353"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9ac515bde3c725ca46efa918d37e37c7cece6353", "type": "zip", "shasum": "", "reference": "9ac515bde3c725ca46efa918d37e37c7cece6353"}, "time": "2018-09-08T13:24:10+00:00"}, {"version": "v4.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2a4df7618f869b456f9096781e78c57b509d76c7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2a4df7618f869b456f9096781e78c57b509d76c7", "type": "zip", "shasum": "", "reference": "2a4df7618f869b456f9096781e78c57b509d76c7"}, "time": "2018-07-26T09:10:45+00:00"}, {"version": "v4.1.3", "version_normalized": "*******"}, {"version": "v4.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "03ac71606ecb0b0ce792faa17d74cc32c2949ef4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/03ac71606ecb0b0ce792faa17d74cc32c2949ef4", "type": "zip", "shasum": "", "reference": "03ac71606ecb0b0ce792faa17d74cc32c2949ef4"}, "time": "2018-05-30T07:26:09+00:00"}, {"version": "v4.1.1", "version_normalized": "*******"}, {"version": "v4.1.0", "version_normalized": "*******"}, {"version": "v4.1.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ac02c27e870bfc1c2dc32f34f56ea9bca275b96a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ac02c27e870bfc1c2dc32f34f56ea9bca275b96a", "type": "zip", "shasum": "", "reference": "ac02c27e870bfc1c2dc32f34f56ea9bca275b96a"}, "time": "2018-05-16T14:33:22+00:00"}, {"version": "v4.1.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v4.1.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "797df6009c3ef46f54156011639a8347115f7223"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/797df6009c3ef46f54156011639a8347115f7223", "type": "zip", "shasum": "", "reference": "797df6009c3ef46f54156011639a8347115f7223"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2018-03-30T15:58:13+00:00"}, {"version": "v4.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "72a316c5034bddd69710146ce8e72e34e15ef154"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/72a316c5034bddd69710146ce8e72e34e15ef154", "type": "zip", "shasum": "", "reference": "72a316c5034bddd69710146ce8e72e34e15ef154"}, "support": {"source": "https://github.com/symfony/css-selector/tree/4.0"}, "time": "2018-07-26T09:08:35+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}, {"version": "v4.0.14", "version_normalized": "********"}, {"version": "v4.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0383a1a4eb1ffcac28719975d3e01bfa14be8ab3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0383a1a4eb1ffcac28719975d3e01bfa14be8ab3", "type": "zip", "shasum": "", "reference": "0383a1a4eb1ffcac28719975d3e01bfa14be8ab3"}, "time": "2018-05-11T15:58:37+00:00"}, {"version": "v4.0.12", "version_normalized": "********"}, {"version": "v4.0.11", "version_normalized": "********"}, {"version": "v4.0.10", "version_normalized": "********"}, {"version": "v4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "03f965583147957f1ecbad7ea1c9d6fd5e525ec2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/03f965583147957f1ecbad7ea1c9d6fd5e525ec2", "type": "zip", "shasum": "", "reference": "03f965583147957f1ecbad7ea1c9d6fd5e525ec2"}, "time": "2018-03-19T22:35:49+00:00"}, {"version": "v4.0.8", "version_normalized": "*******"}, {"version": "v4.0.7", "version_normalized": "*******"}, {"version": "v4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c69f1e93aa898fd9fec627ebef467188151c8dc2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c69f1e93aa898fd9fec627ebef467188151c8dc2", "type": "zip", "shasum": "", "reference": "c69f1e93aa898fd9fec627ebef467188151c8dc2"}, "time": "2018-02-03T14:58:37+00:00"}, {"version": "v4.0.5", "version_normalized": "*******"}, {"version": "v4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f97600434e3141ef3cbb9ea42cf500fba88022b7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f97600434e3141ef3cbb9ea42cf500fba88022b7", "type": "zip", "shasum": "", "reference": "f97600434e3141ef3cbb9ea42cf500fba88022b7"}, "time": "2018-01-03T07:38:00+00:00"}, {"version": "v4.0.3", "version_normalized": "*******"}, {"version": "v4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2b71219bf15530f293f6a9262de841d0ca90b11c"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2b71219bf15530f293f6a9262de841d0ca90b11c", "type": "zip", "shasum": "", "reference": "2b71219bf15530f293f6a9262de841d0ca90b11c"}, "time": "2017-12-14T19:48:22+00:00"}, {"version": "v4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4d007264ba31da78685a92f49aa13b62cbbd89b4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4d007264ba31da78685a92f49aa13b62cbbd89b4", "type": "zip", "shasum": "", "reference": "4d007264ba31da78685a92f49aa13b62cbbd89b4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2017-11-07T14:45:01+00:00"}, {"version": "v4.0.0", "version_normalized": "*******"}, {"version": "v4.0.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v4.0.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v4.0.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v4.0.0-BETA3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7b8e4b9a76abb56ec1b4fd12b2385b8d288568a7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7b8e4b9a76abb56ec1b4fd12b2385b8d288568a7", "type": "zip", "shasum": "", "reference": "7b8e4b9a76abb56ec1b4fd12b2385b8d288568a7"}, "time": "2017-11-05T16:26:21+00:00"}, {"version": "v4.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "220f8a4c7be74d0e10cf74021af47fcbc6585459"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/220f8a4c7be74d0e10cf74021af47fcbc6585459", "type": "zip", "shasum": "", "reference": "220f8a4c7be74d0e10cf74021af47fcbc6585459"}, "time": "2017-10-24T14:16:56+00:00"}, {"version": "v4.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "61cd2d10c88485ea77c90404f7986d373af64f09"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/61cd2d10c88485ea77c90404f7986d373af64f09", "type": "zip", "shasum": "", "reference": "61cd2d10c88485ea77c90404f7986d373af64f09"}, "time": "2017-10-02T06:59:24+00:00"}, {"version": "v3.4.47", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "da3d9da2ce0026771f5fe64cb332158f1bd2bc33"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/da3d9da2ce0026771f5fe64cb332158f1bd2bc33", "type": "zip", "shasum": "", "reference": "da3d9da2ce0026771f5fe64cb332158f1bd2bc33"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00", "require": {"php": "^5.5.9|>=7.0.8"}, "extra": "__unset"}, {"version": "v3.4.46", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.46"}}, {"version": "v3.4.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9ccf6e78077a3fc1596e6c7b5958008965a11518"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9ccf6e78077a3fc1596e6c7b5958008965a11518", "type": "zip", "shasum": "", "reference": "9ccf6e78077a3fc1596e6c7b5958008965a11518"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.45"}, "time": "2020-03-16T08:31:04+00:00", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}, {"version": "v3.4.44", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.44"}}, {"version": "v3.4.43", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/3.4"}}, {"version": "v3.4.42", "version_normalized": "********"}, {"version": "v3.4.41", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.40"}}, {"version": "v3.4.40", "version_normalized": "********"}, {"version": "v3.4.39", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/3.4"}}, {"version": "v3.4.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ee9b946e7223b11257329a054c64396b19d619e1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ee9b946e7223b11257329a054c64396b19d619e1", "type": "zip", "shasum": "", "reference": "ee9b946e7223b11257329a054c64396b19d619e1"}, "funding": [], "time": "2020-02-04T08:04:52+00:00"}, {"version": "v3.4.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e1b3e1a0621d6e48ee46092b4c7d8280f746b3c5"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e1b3e1a0621d6e48ee46092b4c7d8280f746b3c5", "type": "zip", "shasum": "", "reference": "e1b3e1a0621d6e48ee46092b4c7d8280f746b3c5"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.37"}, "time": "2020-01-01T11:03:25+00:00", "funding": "__unset"}, {"version": "v3.4.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f819f71ae3ba6f396b4c015bd5895de7d2f1f85f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f819f71ae3ba6f396b4c015bd5895de7d2f1f85f", "type": "zip", "shasum": "", "reference": "f819f71ae3ba6f396b4c015bd5895de7d2f1f85f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/3.4"}, "time": "2019-10-01T11:57:37+00:00"}, {"version": "v3.4.35", "version_normalized": "********"}, {"version": "v3.4.34", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.34"}}, {"version": "v3.4.33", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/3.4"}}, {"version": "v3.4.32", "version_normalized": "********"}, {"version": "v3.4.31", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e18c5c4b35e7f17513448a25d02f7af34a4bdb41"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e18c5c4b35e7f17513448a25d02f7af34a4bdb41", "type": "zip", "shasum": "", "reference": "e18c5c4b35e7f17513448a25d02f7af34a4bdb41"}, "time": "2019-08-20T13:31:17+00:00"}, {"version": "v3.4.30", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8ca29297c29b64fb3a1a135e71cb25f67f9fdccf"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8ca29297c29b64fb3a1a135e71cb25f67f9fdccf", "type": "zip", "shasum": "", "reference": "8ca29297c29b64fb3a1a135e71cb25f67f9fdccf"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.25"}, "time": "2019-01-16T09:39:14+00:00"}, {"version": "v3.4.29", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.24"}}, {"version": "v3.4.28", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.25"}}, {"version": "v3.4.27", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.24"}}, {"version": "v3.4.26", "version_normalized": "********"}, {"version": "v3.4.25", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.25"}}, {"version": "v3.4.24", "version_normalized": "3.4.24.0", "support": {"source": "https://github.com/symfony/css-selector/tree/v3.4.24"}}, {"version": "v3.4.23", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/3.4"}}, {"version": "v3.4.22", "version_normalized": "********"}, {"version": "v3.4.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "12f86295c46c36af9896cf21db6b6b8a1465315d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/12f86295c46c36af9896cf21db6b6b8a1465315d", "type": "zip", "shasum": "", "reference": "12f86295c46c36af9896cf21db6b6b8a1465315d"}, "time": "2019-01-02T09:30:52+00:00"}, {"version": "v3.4.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "345b9a48595d1ab9630db791dbc3e721bf0233e8"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/345b9a48595d1ab9630db791dbc3e721bf0233e8", "type": "zip", "shasum": "", "reference": "345b9a48595d1ab9630db791dbc3e721bf0233e8"}, "time": "2018-11-11T19:48:54+00:00"}, {"version": "v3.4.19", "version_normalized": "********"}, {"version": "v3.4.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3503415d4aafabc31cd08c3a4ebac7f43fde8feb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3503415d4aafabc31cd08c3a4ebac7f43fde8feb", "type": "zip", "shasum": "", "reference": "3503415d4aafabc31cd08c3a4ebac7f43fde8feb"}, "time": "2018-10-02T16:33:53+00:00"}, {"version": "v3.4.17", "version_normalized": "********"}, {"version": "v3.4.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b2d6f39145261c082537264b7624f49847915711"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b2d6f39145261c082537264b7624f49847915711", "type": "zip", "shasum": "", "reference": "b2d6f39145261c082537264b7624f49847915711"}, "time": "2018-09-08T13:15:14+00:00"}, {"version": "v3.4.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "edda5a6155000ff8c3a3f85ee5c421af93cca416"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/edda5a6155000ff8c3a3f85ee5c421af93cca416", "type": "zip", "shasum": "", "reference": "edda5a6155000ff8c3a3f85ee5c421af93cca416"}, "time": "2018-07-26T09:06:28+00:00"}, {"version": "v3.4.14", "version_normalized": "********"}, {"version": "v3.4.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d2ce52290b648ae33b5301d09bc14ee378612914"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d2ce52290b648ae33b5301d09bc14ee378612914", "type": "zip", "shasum": "", "reference": "d2ce52290b648ae33b5301d09bc14ee378612914"}, "time": "2018-05-16T12:49:49+00:00"}, {"version": "v3.4.12", "version_normalized": "********"}, {"version": "v3.4.11", "version_normalized": "********"}, {"version": "v3.4.10", "version_normalized": "********"}, {"version": "v3.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "519a80d7c1d95c6cc0b67f686d15fe27c6910de0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/519a80d7c1d95c6cc0b67f686d15fe27c6910de0", "type": "zip", "shasum": "", "reference": "519a80d7c1d95c6cc0b67f686d15fe27c6910de0"}, "time": "2018-03-19T22:32:39+00:00"}, {"version": "v3.4.8", "version_normalized": "*******"}, {"version": "v3.4.7", "version_normalized": "*******"}, {"version": "v3.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "544655f1fc078a9cd839fdda2b7b1e64627c826a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/544655f1fc078a9cd839fdda2b7b1e64627c826a", "type": "zip", "shasum": "", "reference": "544655f1fc078a9cd839fdda2b7b1e64627c826a"}, "time": "2018-02-03T14:55:07+00:00"}, {"version": "v3.4.5", "version_normalized": "*******"}, {"version": "v3.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e66394bc7610e69279bfdb3ab11b4fe65403f556"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e66394bc7610e69279bfdb3ab11b4fe65403f556", "type": "zip", "shasum": "", "reference": "e66394bc7610e69279bfdb3ab11b4fe65403f556"}, "time": "2018-01-03T07:37:34+00:00"}, {"version": "v3.4.3", "version_normalized": "*******"}, {"version": "v3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "eac760b414cf1f64362c3dd047b989e4db121332"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/eac760b414cf1f64362c3dd047b989e4db121332", "type": "zip", "shasum": "", "reference": "eac760b414cf1f64362c3dd047b989e4db121332"}, "time": "2017-12-14T19:40:10+00:00"}, {"version": "v3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7134b93e90ea7e7881fcb2da006d21b4c5f31908"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7134b93e90ea7e7881fcb2da006d21b4c5f31908", "type": "zip", "shasum": "", "reference": "7134b93e90ea7e7881fcb2da006d21b4c5f31908"}, "time": "2017-11-05T16:10:10+00:00"}, {"version": "v3.4.0", "version_normalized": "*******"}, {"version": "v3.4.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.4.0-BETA4", "version_normalized": "*******-beta4"}, {"version": "v3.4.0-BETA3", "version_normalized": "*******-beta3"}, {"version": "v3.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6bbad1ad4cd69dd440b24a62979cc469c5800490"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6bbad1ad4cd69dd440b24a62979cc469c5800490", "type": "zip", "shasum": "", "reference": "6bbad1ad4cd69dd440b24a62979cc469c5800490"}, "time": "2017-10-24T14:12:06+00:00"}, {"version": "v3.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f38b363faac4d647f53ef389203436e6c3b27f01"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f38b363faac4d647f53ef389203436e6c3b27f01", "type": "zip", "shasum": "", "reference": "f38b363faac4d647f53ef389203436e6c3b27f01"}, "time": "2017-10-02T06:49:52+00:00"}, {"version": "v3.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a6abf0dbbf5e6f174132a6f479567e90fccd9496"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a6abf0dbbf5e6f174132a6f479567e90fccd9496", "type": "zip", "shasum": "", "reference": "a6abf0dbbf5e6f174132a6f479567e90fccd9496"}, "support": {"source": "https://github.com/symfony/css-selector/tree/3.3"}, "time": "2018-01-03T07:37:11+00:00", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}}, {"version": "v3.3.17", "version_normalized": "********"}, {"version": "v3.3.16", "version_normalized": "********"}, {"version": "v3.3.15", "version_normalized": "********"}, {"version": "v3.3.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "66e6e046032ebdf1f562c26928549f613d428bd1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/66e6e046032ebdf1f562c26928549f613d428bd1", "type": "zip", "shasum": "", "reference": "66e6e046032ebdf1f562c26928549f613d428bd1"}, "time": "2017-11-05T15:47:03+00:00"}, {"version": "v3.3.13", "version_normalized": "********"}, {"version": "v3.3.12", "version_normalized": "********"}, {"version": "v3.3.11", "version_normalized": "********"}, {"version": "v3.3.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "07447650225ca9223bd5c97180fe7c8267f7d332"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/07447650225ca9223bd5c97180fe7c8267f7d332", "type": "zip", "shasum": "", "reference": "07447650225ca9223bd5c97180fe7c8267f7d332"}, "time": "2017-10-02T06:42:24+00:00"}, {"version": "v3.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c5f5263ed231f164c58368efbce959137c7d9488"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c5f5263ed231f164c58368efbce959137c7d9488", "type": "zip", "shasum": "", "reference": "c5f5263ed231f164c58368efbce959137c7d9488"}, "time": "2017-07-29T21:54:42+00:00"}, {"version": "v3.3.8", "version_normalized": "*******"}, {"version": "v3.3.7", "version_normalized": "*******"}, {"version": "v3.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4d882dced7b995d5274293039370148e291808f2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4d882dced7b995d5274293039370148e291808f2", "type": "zip", "shasum": "", "reference": "4d882dced7b995d5274293039370148e291808f2"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2017-05-01T15:01:29+00:00", "require": {"php": ">=5.5.9"}}, {"version": "v3.3.5", "version_normalized": "*******"}, {"version": "v3.3.4", "version_normalized": "*******"}, {"version": "v3.3.3", "version_normalized": "*******"}, {"version": "v3.3.2", "version_normalized": "*******"}, {"version": "v3.3.1", "version_normalized": "*******"}, {"version": "v3.3.0", "version_normalized": "*******"}, {"version": "v3.3.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.3.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.2.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "02983c144038e697c959e6b06ef6666de759ccbc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/02983c144038e697c959e6b06ef6666de759ccbc", "type": "zip", "shasum": "", "reference": "02983c144038e697c959e6b06ef6666de759ccbc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/3.2"}, "time": "2017-05-01T14:55:58+00:00", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}}, {"version": "v3.2.13", "version_normalized": "********"}, {"version": "v3.2.12", "version_normalized": "********"}, {"version": "v3.2.11", "version_normalized": "********"}, {"version": "v3.2.10", "version_normalized": "********"}, {"version": "v3.2.9", "version_normalized": "*******"}, {"version": "v3.2.8", "version_normalized": "*******"}, {"version": "v3.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a48f13dc83c168f1253a5d2a5a4fb46c36244c4c"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a48f13dc83c168f1253a5d2a5a4fb46c36244c4c", "type": "zip", "shasum": "", "reference": "a48f13dc83c168f1253a5d2a5a4fb46c36244c4c"}, "time": "2017-02-21T09:12:04+00:00"}, {"version": "v3.2.6", "version_normalized": "*******"}, {"version": "v3.2.5", "version_normalized": "*******"}, {"version": "v3.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f0e628f04fc055c934b3211cfabdb1c59eefbfaa"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f0e628f04fc055c934b3211cfabdb1c59eefbfaa", "type": "zip", "shasum": "", "reference": "f0e628f04fc055c934b3211cfabdb1c59eefbfaa"}, "time": "2017-01-02T20:32:22+00:00"}, {"version": "v3.2.3", "version_normalized": "*******"}, {"version": "v3.2.2", "version_normalized": "*******"}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e1241f275814827c411d922ba8e64cf2a00b2994"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e1241f275814827c411d922ba8e64cf2a00b2994", "type": "zip", "shasum": "", "reference": "e1241f275814827c411d922ba8e64cf2a00b2994"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2016-11-03T08:11:03+00:00"}, {"version": "v3.2.0", "version_normalized": "*******"}, {"version": "v3.2.0-RC2", "version_normalized": "*******-RC2"}, {"version": "v3.2.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.2.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3912a05e7ae31afb78101f9de1be6b143c064fac"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3912a05e7ae31afb78101f9de1be6b143c064fac", "type": "zip", "shasum": "", "reference": "3912a05e7ae31afb78101f9de1be6b143c064fac"}, "time": "2016-09-06T11:07:23+00:00"}, {"version": "v3.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "722a87478a72d95dc2a3bcf41dc9c2d13fd4cb2d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/722a87478a72d95dc2a3bcf41dc9c2d13fd4cb2d", "type": "zip", "shasum": "", "reference": "722a87478a72d95dc2a3bcf41dc9c2d13fd4cb2d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/3.1"}, "time": "2017-01-02T20:31:54+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}, {"version": "v3.1.9", "version_normalized": "*******"}, {"version": "v3.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a37b3359566415a91cba55a2d95820b3fa1a9658"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a37b3359566415a91cba55a2d95820b3fa1a9658", "type": "zip", "shasum": "", "reference": "a37b3359566415a91cba55a2d95820b3fa1a9658"}, "time": "2016-11-03T08:04:31+00:00"}, {"version": "v3.1.7", "version_normalized": "*******"}, {"version": "v3.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ca809c64072e0fe61c1c7fb3c76cdc32265042ac"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ca809c64072e0fe61c1c7fb3c76cdc32265042ac", "type": "zip", "shasum": "", "reference": "ca809c64072e0fe61c1c7fb3c76cdc32265042ac"}, "time": "2016-09-06T11:02:40+00:00"}, {"version": "v3.1.5", "version_normalized": "*******"}, {"version": "v3.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2851e1932d77ce727776154d659b232d061e816a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2851e1932d77ce727776154d659b232d061e816a", "type": "zip", "shasum": "", "reference": "2851e1932d77ce727776154d659b232d061e816a"}, "time": "2016-06-29T05:41:56+00:00"}, {"version": "v3.1.3", "version_normalized": "*******"}, {"version": "v3.1.2", "version_normalized": "*******"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c526d7b3cb4fe1673c6a34e13be2ff63f519df99"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c526d7b3cb4fe1673c6a34e13be2ff63f519df99", "type": "zip", "shasum": "", "reference": "c526d7b3cb4fe1673c6a34e13be2ff63f519df99"}, "time": "2016-06-06T11:42:41+00:00"}, {"version": "v3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e17f386efef7258ac671c24e727673abd086b0cf"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e17f386efef7258ac671c24e727673abd086b0cf", "type": "zip", "shasum": "", "reference": "e17f386efef7258ac671c24e727673abd086b0cf"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2016-03-04T07:56:56+00:00"}, {"version": "v3.1.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v3.1.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v3.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b8999c1f33c224b2b66b38253f5e3a838d0d0115"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b8999c1f33c224b2b66b38253f5e3a838d0d0115", "type": "zip", "shasum": "", "reference": "b8999c1f33c224b2b66b38253f5e3a838d0d0115"}, "support": {"source": "https://github.com/symfony/css-selector/tree/3.0"}, "time": "2016-06-29T05:40:00+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "v3.0.8", "version_normalized": "*******"}, {"version": "v3.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e8a66c51bf65f188c02f8120c0748b2291d3a2d0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e8a66c51bf65f188c02f8120c0748b2291d3a2d0", "type": "zip", "shasum": "", "reference": "e8a66c51bf65f188c02f8120c0748b2291d3a2d0"}, "time": "2016-06-06T11:33:26+00:00"}, {"version": "v3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "65e764f404685f2dc20c057e889b3ad04b2e2db0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/65e764f404685f2dc20c057e889b3ad04b2e2db0", "type": "zip", "shasum": "", "reference": "65e764f404685f2dc20c057e889b3ad04b2e2db0"}, "time": "2016-03-04T07:55:57+00:00"}, {"version": "v3.0.5", "version_normalized": "*******"}, {"version": "v3.0.4", "version_normalized": "*******"}, {"version": "v3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6605602690578496091ac20ec7a5cbd160d4dff4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6605602690578496091ac20ec7a5cbd160d4dff4", "type": "zip", "shasum": "", "reference": "6605602690578496091ac20ec7a5cbd160d4dff4"}, "time": "2016-01-27T05:14:46+00:00"}, {"version": "v3.0.2", "version_normalized": "*******"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4613311fd46e146f506403ce2f8a0c71d402d2a3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4613311fd46e146f506403ce2f8a0c71d402d2a3", "type": "zip", "shasum": "", "reference": "4613311fd46e146f506403ce2f8a0c71d402d2a3"}, "time": "2015-12-05T17:45:07+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2563cd23b8822eac1b2a4a47d99de160b58492c1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2563cd23b8822eac1b2a4a47d99de160b58492c1", "type": "zip", "shasum": "", "reference": "2563cd23b8822eac1b2a4a47d99de160b58492c1"}, "support": {"source": "https://github.com/symfony/css-selector/tree/master"}, "time": "2015-10-30T23:35:59+00:00"}, {"version": "v3.0.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.8.52", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7b1692e418d7ccac24c373528453bc90e42797de"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7b1692e418d7ccac24c373528453bc90e42797de", "type": "zip", "shasum": "", "reference": "7b1692e418d7ccac24c373528453bc90e42797de"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.8.52"}, "time": "2018-11-11T11:18:13+00:00", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "require": {"php": ">=5.3.9"}}, {"version": "v2.8.50", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "support": {"source": "https://github.com/symfony/css-selector/tree/v2.8.50"}}, {"version": "v2.8.49", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/2.8"}}, {"version": "v2.8.48", "version_normalized": "********"}, {"version": "v2.8.47", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "208aca6c35e332f87c84707dd228d404370c8835"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/208aca6c35e332f87c84707dd228d404370c8835", "type": "zip", "shasum": "", "reference": "208aca6c35e332f87c84707dd228d404370c8835"}, "time": "2018-10-02T16:27:16+00:00"}, {"version": "v2.8.46", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4cca41ebe83cd5b4bd0c1a9f6bdfaec7103f97fb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4cca41ebe83cd5b4bd0c1a9f6bdfaec7103f97fb", "type": "zip", "shasum": "", "reference": "4cca41ebe83cd5b4bd0c1a9f6bdfaec7103f97fb"}, "time": "2018-09-08T12:44:02+00:00"}, {"version": "v2.8.45", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "294611f3a0d265bcf049e2da62cb4f712e3ed927"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/294611f3a0d265bcf049e2da62cb4f712e3ed927", "type": "zip", "shasum": "", "reference": "294611f3a0d265bcf049e2da62cb4f712e3ed927"}, "time": "2018-07-26T09:03:18+00:00"}, {"version": "v2.8.44", "version_normalized": "********"}, {"version": "v2.8.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3cdc270724e4666006118283c700a4d7f9cbe264"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3cdc270724e4666006118283c700a4d7f9cbe264", "type": "zip", "shasum": "", "reference": "3cdc270724e4666006118283c700a4d7f9cbe264"}, "time": "2018-03-10T18:19:36+00:00"}, {"version": "v2.8.42", "version_normalized": "********"}, {"version": "v2.8.41", "version_normalized": "********"}, {"version": "v2.8.40", "version_normalized": "********"}, {"version": "v2.8.39", "version_normalized": "********"}, {"version": "v2.8.38", "version_normalized": "********"}, {"version": "v2.8.37", "version_normalized": "********"}, {"version": "v2.8.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "99a4b2c2f1757d62d081b5f9f14128f23504897d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/99a4b2c2f1757d62d081b5f9f14128f23504897d", "type": "zip", "shasum": "", "reference": "99a4b2c2f1757d62d081b5f9f14128f23504897d"}, "time": "2018-02-03T14:55:47+00:00"}, {"version": "v2.8.35", "version_normalized": "********"}, {"version": "v2.8.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c5b39674eacd34adedbef78227c57109caa9e318"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c5b39674eacd34adedbef78227c57109caa9e318", "type": "zip", "shasum": "", "reference": "c5b39674eacd34adedbef78227c57109caa9e318"}, "time": "2018-01-03T07:36:31+00:00"}, {"version": "v2.8.33", "version_normalized": "********"}, {"version": "v2.8.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b7b041487197fb6d803b7edbcaae7f00a793b1c4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b7b041487197fb6d803b7edbcaae7f00a793b1c4", "type": "zip", "shasum": "", "reference": "b7b041487197fb6d803b7edbcaae7f00a793b1c4"}, "time": "2017-11-05T15:25:56+00:00"}, {"version": "v2.8.31", "version_normalized": "********"}, {"version": "v2.8.30", "version_normalized": "********"}, {"version": "v2.8.29", "version_normalized": "********"}, {"version": "v2.8.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ef01ca1352deb0c029cf496a89a6b175659c1ec3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ef01ca1352deb0c029cf496a89a6b175659c1ec3", "type": "zip", "shasum": "", "reference": "ef01ca1352deb0c029cf496a89a6b175659c1ec3"}, "time": "2017-10-01T21:00:16+00:00"}, {"version": "v2.8.27", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ba3204654efa779691fac9e948a96b4a7067e4ab"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ba3204654efa779691fac9e948a96b4a7067e4ab", "type": "zip", "shasum": "", "reference": "ba3204654efa779691fac9e948a96b4a7067e4ab"}, "time": "2017-05-01T14:31:55+00:00"}, {"version": "v2.8.26", "version_normalized": "********"}, {"version": "v2.8.25", "version_normalized": "********"}, {"version": "v2.8.24", "version_normalized": "********"}, {"version": "v2.8.23", "version_normalized": "********"}, {"version": "v2.8.22", "version_normalized": "********"}, {"version": "v2.8.21", "version_normalized": "********"}, {"version": "v2.8.20", "version_normalized": "********"}, {"version": "v2.8.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "742bd688bd778dde8991ba696cb372570610afcd"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/742bd688bd778dde8991ba696cb372570610afcd", "type": "zip", "shasum": "", "reference": "742bd688bd778dde8991ba696cb372570610afcd"}, "time": "2017-02-21T08:33:48+00:00"}, {"version": "v2.8.18", "version_normalized": "********"}, {"version": "v2.8.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f45daea42232d9ca5cf561ec64f0d4aea820877f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f45daea42232d9ca5cf561ec64f0d4aea820877f", "type": "zip", "shasum": "", "reference": "f45daea42232d9ca5cf561ec64f0d4aea820877f"}, "time": "2017-01-02T20:30:24+00:00"}, {"version": "v2.8.16", "version_normalized": "********"}, {"version": "v2.8.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "981abbbd6ba49af338a98490cbe29e7f39ca9fa9"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/981abbbd6ba49af338a98490cbe29e7f39ca9fa9", "type": "zip", "shasum": "", "reference": "981abbbd6ba49af338a98490cbe29e7f39ca9fa9"}, "time": "2016-11-03T07:52:58+00:00"}, {"version": "v2.8.14", "version_normalized": "********"}, {"version": "v2.8.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "71c8c3a04c126300c37089b1baa7c6322dfb845f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/71c8c3a04c126300c37089b1baa7c6322dfb845f", "type": "zip", "shasum": "", "reference": "71c8c3a04c126300c37089b1baa7c6322dfb845f"}, "time": "2016-09-06T10:55:00+00:00"}, {"version": "v2.8.12", "version_normalized": "********"}, {"version": "v2.8.11", "version_normalized": "********"}, {"version": "v2.8.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9da4c615ba303850986e0480cc472bf704cfdb64"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9da4c615ba303850986e0480cc472bf704cfdb64", "type": "zip", "shasum": "", "reference": "9da4c615ba303850986e0480cc472bf704cfdb64"}, "time": "2016-06-29T05:31:50+00:00"}, {"version": "v2.8.9", "version_normalized": "*******"}, {"version": "v2.8.8", "version_normalized": "*******"}, {"version": "v2.8.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9a0b2649328297fb6acd0c823789d92efcbd36ad"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9a0b2649328297fb6acd0c823789d92efcbd36ad", "type": "zip", "shasum": "", "reference": "9a0b2649328297fb6acd0c823789d92efcbd36ad"}, "time": "2016-06-06T11:11:27+00:00"}, {"version": "v2.8.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "07b7ced3ae0c12918477c095453ea8595000810e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/07b7ced3ae0c12918477c095453ea8595000810e", "type": "zip", "shasum": "", "reference": "07b7ced3ae0c12918477c095453ea8595000810e"}, "time": "2016-03-04T07:54:35+00:00"}, {"version": "v2.8.5", "version_normalized": "*******"}, {"version": "v2.8.4", "version_normalized": "*******"}, {"version": "v2.8.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8d83ff9777cdbd83e7f90d9c48f4729823791a5e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8d83ff9777cdbd83e7f90d9c48f4729823791a5e", "type": "zip", "shasum": "", "reference": "8d83ff9777cdbd83e7f90d9c48f4729823791a5e"}, "time": "2016-01-27T05:14:19+00:00"}, {"version": "v2.8.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ac06d8173bd80790536c0a4a634a7d705b91f54f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ac06d8173bd80790536c0a4a634a7d705b91f54f", "type": "zip", "shasum": "", "reference": "ac06d8173bd80790536c0a4a634a7d705b91f54f"}, "time": "2016-01-03T15:33:41+00:00"}, {"version": "v2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "eaa3320e32f09a01dc432c6efbe8051aee59cfef"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/eaa3320e32f09a01dc432c6efbe8051aee59cfef", "type": "zip", "shasum": "", "reference": "eaa3320e32f09a01dc432c6efbe8051aee59cfef"}, "time": "2015-12-05T17:37:59+00:00"}, {"version": "v2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b600fec37c0efca08046d481d79e7eabc07108ff"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b600fec37c0efca08046d481d79e7eabc07108ff", "type": "zip", "shasum": "", "reference": "b600fec37c0efca08046d481d79e7eabc07108ff"}, "time": "2015-10-30T20:15:42+00:00"}, {"version": "v2.8.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.7.51", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0b490439a4287f6502426bcb0418e707c2eebe5e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0b490439a4287f6502426bcb0418e707c2eebe5e", "type": "zip", "shasum": "", "reference": "0b490439a4287f6502426bcb0418e707c2eebe5e"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.7.51"}, "time": "2018-03-08T08:22:32+00:00", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}}, {"version": "v2.7.50", "version_normalized": "********", "support": {"source": "https://github.com/symfony/css-selector/tree/2.7"}}, {"version": "v2.7.49", "version_normalized": "********"}, {"version": "v2.7.48", "version_normalized": "********"}, {"version": "v2.7.47", "version_normalized": "********"}, {"version": "v2.7.46", "version_normalized": "********"}, {"version": "v2.7.45", "version_normalized": "********"}, {"version": "v2.7.44", "version_normalized": "********"}, {"version": "v2.7.43", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e94061afa13347383a163634972ed27adfb58d8f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e94061afa13347383a163634972ed27adfb58d8f", "type": "zip", "shasum": "", "reference": "e94061afa13347383a163634972ed27adfb58d8f"}, "time": "2018-02-03T10:49:50+00:00"}, {"version": "v2.7.42", "version_normalized": "********"}, {"version": "v2.7.41", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4f270b68f68e388e5b2a4abec4f96935ab2ef147"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4f270b68f68e388e5b2a4abec4f96935ab2ef147", "type": "zip", "shasum": "", "reference": "4f270b68f68e388e5b2a4abec4f96935ab2ef147"}, "time": "2018-01-03T07:23:28+00:00"}, {"version": "v2.7.40", "version_normalized": "********"}, {"version": "v2.7.39", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c31c877f664c2c79a1a9b8cc8c06f63ef53b3866"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c31c877f664c2c79a1a9b8cc8c06f63ef53b3866", "type": "zip", "shasum": "", "reference": "c31c877f664c2c79a1a9b8cc8c06f63ef53b3866"}, "time": "2017-10-29T09:49:53+00:00"}, {"version": "v2.7.38", "version_normalized": "********"}, {"version": "v2.7.37", "version_normalized": "********"}, {"version": "v2.7.36", "version_normalized": "********"}, {"version": "v2.7.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "cf66cb52bea19ef355983b69c6c1dfcb29e6d723"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/cf66cb52bea19ef355983b69c6c1dfcb29e6d723", "type": "zip", "shasum": "", "reference": "cf66cb52bea19ef355983b69c6c1dfcb29e6d723"}, "time": "2017-09-30T14:00:25+00:00"}, {"version": "v2.7.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f647c470481f35943fae4d553c5dd17cbad69e81"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f647c470481f35943fae4d553c5dd17cbad69e81", "type": "zip", "shasum": "", "reference": "f647c470481f35943fae4d553c5dd17cbad69e81"}, "time": "2017-04-29T15:58:46+00:00"}, {"version": "v2.7.33", "version_normalized": "********"}, {"version": "v2.7.32", "version_normalized": "********"}, {"version": "v2.7.31", "version_normalized": "********"}, {"version": "v2.7.30", "version_normalized": "********"}, {"version": "v2.7.29", "version_normalized": "********"}, {"version": "v2.7.28", "version_normalized": "********"}, {"version": "v2.7.27", "version_normalized": "********"}, {"version": "v2.7.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f63380ed2c5127bc5758f0746c9f01ea9a90a623"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f63380ed2c5127bc5758f0746c9f01ea9a90a623", "type": "zip", "shasum": "", "reference": "f63380ed2c5127bc5758f0746c9f01ea9a90a623"}, "time": "2017-02-21T08:32:25+00:00"}, {"version": "v2.7.25", "version_normalized": "********"}, {"version": "v2.7.24", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f7102a3a2904eb4254424148bfa3a691bd05ef33"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f7102a3a2904eb4254424148bfa3a691bd05ef33", "type": "zip", "shasum": "", "reference": "f7102a3a2904eb4254424148bfa3a691bd05ef33"}, "time": "2017-01-02T20:30:00+00:00"}, {"version": "v2.7.23", "version_normalized": "********"}, {"version": "v2.7.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3b37ddb7b68fbb0b7595037b392347a6f60acb80"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3b37ddb7b68fbb0b7595037b392347a6f60acb80", "type": "zip", "shasum": "", "reference": "3b37ddb7b68fbb0b7595037b392347a6f60acb80"}, "time": "2016-11-03T07:44:53+00:00"}, {"version": "v2.7.21", "version_normalized": "********"}, {"version": "v2.7.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8316e61de0e0536b6e2cd3b41d2986d431b9cd2e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8316e61de0e0536b6e2cd3b41d2986d431b9cd2e", "type": "zip", "shasum": "", "reference": "8316e61de0e0536b6e2cd3b41d2986d431b9cd2e"}, "time": "2016-09-06T07:26:07+00:00"}, {"version": "v2.7.19", "version_normalized": "********"}, {"version": "v2.7.18", "version_normalized": "********"}, {"version": "v2.7.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f2cc2c55a9982db7bc167385dbf549c640e8cc01"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f2cc2c55a9982db7bc167385dbf549c640e8cc01", "type": "zip", "shasum": "", "reference": "f2cc2c55a9982db7bc167385dbf549c640e8cc01"}, "time": "2016-06-28T06:24:06+00:00"}, {"version": "v2.7.16", "version_normalized": "********"}, {"version": "v2.7.15", "version_normalized": "********"}, {"version": "v2.7.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d255e420795234173c0b9c9dff3cde5a82b0a108"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d255e420795234173c0b9c9dff3cde5a82b0a108", "type": "zip", "shasum": "", "reference": "d255e420795234173c0b9c9dff3cde5a82b0a108"}, "time": "2016-06-06T11:03:51+00:00"}, {"version": "v2.7.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "b6e5e9087ed7affbd4c38948da8f93513a87de43"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/b6e5e9087ed7affbd4c38948da8f93513a87de43", "type": "zip", "shasum": "", "reference": "b6e5e9087ed7affbd4c38948da8f93513a87de43"}, "time": "2016-03-04T07:52:28+00:00"}, {"version": "v2.7.12", "version_normalized": "********"}, {"version": "v2.7.11", "version_normalized": "********"}, {"version": "v2.7.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5377825bb6496514f63603af417fa07afaa12357"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5377825bb6496514f63603af417fa07afaa12357", "type": "zip", "shasum": "", "reference": "5377825bb6496514f63603af417fa07afaa12357"}, "time": "2016-01-27T05:09:39+00:00"}, {"version": "v2.7.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "1a869e59cc3b2802961fc2124139659e12b72fe5"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/1a869e59cc3b2802961fc2124139659e12b72fe5", "type": "zip", "shasum": "", "reference": "1a869e59cc3b2802961fc2124139659e12b72fe5"}, "time": "2016-01-03T15:32:00+00:00"}, {"version": "v2.7.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "35bebec48d3d08e3138257419e3ca84070152012"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/35bebec48d3d08e3138257419e3ca84070152012", "type": "zip", "shasum": "", "reference": "35bebec48d3d08e3138257419e3ca84070152012"}, "time": "2015-12-05T17:37:09+00:00"}, {"version": "v2.7.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "abb47717fb88aebd9437da2fc8bb01a50a36679f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/abb47717fb88aebd9437da2fc8bb01a50a36679f", "type": "zip", "shasum": "", "reference": "abb47717fb88aebd9437da2fc8bb01a50a36679f"}, "time": "2015-10-30T20:10:21+00:00"}, {"version": "v2.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e1b865b26be4a56d22a8dee398375044a80c865b"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e1b865b26be4a56d22a8dee398375044a80c865b", "type": "zip", "shasum": "", "reference": "e1b865b26be4a56d22a8dee398375044a80c865b"}, "time": "2015-10-11T09:39:48+00:00", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}}}, {"version": "v2.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "abe19cc0429a06be0c133056d1f9859854860970"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/abe19cc0429a06be0c133056d1f9859854860970", "type": "zip", "shasum": "", "reference": "abe19cc0429a06be0c133056d1f9859854860970"}, "time": "2015-09-22T13:49:29+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7"}}, {"version": "v2.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ffb5f3b8a75f8d1b9801e74dc6789a0751a670ad"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ffb5f3b8a75f8d1b9801e74dc6789a0751a670ad", "type": "zip", "shasum": "", "reference": "ffb5f3b8a75f8d1b9801e74dc6789a0751a670ad"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.7.4"}, "time": "2015-08-24T07:13:45+00:00"}, {"version": "v2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0b5c07b516226b7dd32afbbc82fe547a469c5092"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0b5c07b516226b7dd32afbbc82fe547a469c5092", "type": "zip", "shasum": "", "reference": "0b5c07b516226b7dd32afbbc82fe547a469c5092"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.7.3"}, "time": "2015-05-15T13:33:16+00:00"}, {"version": "v2.7.2", "version_normalized": "*******"}, {"version": "v2.7.1", "version_normalized": "*******"}, {"version": "v2.7.0", "version_normalized": "*******"}, {"version": "v2.7.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "081e7e1e8473647dd63d6d4708e4f26bb63ab4b1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/081e7e1e8473647dd63d6d4708e4f26bb63ab4b1", "type": "zip", "shasum": "", "reference": "081e7e1e8473647dd63d6d4708e4f26bb63ab4b1"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.7.0-BETA2"}, "time": "2015-05-02T15:21:08+00:00"}, {"homepage": "http://symfony.com", "version": "v2.7.0-BETA1", "version_normalized": "*******-beta1", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5ce43b14eb8c35268413f7f3ecb9e3ca3dd6dfb1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5ce43b14eb8c35268413f7f3ecb9e3ca3dd6dfb1", "type": "zip", "shasum": "", "reference": "5ce43b14eb8c35268413f7f3ecb9e3ca3dd6dfb1"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.7.0-BETA1"}, "time": "2015-04-10T07:23:38+00:00", "autoload": {"psr-0": {"Symfony\\Component\\CssSelector\\": ""}}, "target-dir": "Symfony/Component/CssSelector"}, {"homepage": "https://symfony.com", "version": "v2.6.13", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "560e446b93883050a5874908652e8e912e8cbe44"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/560e446b93883050a5874908652e8e912e8cbe44", "type": "zip", "shasum": "", "reference": "560e446b93883050a5874908652e8e912e8cbe44"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.11"}, "time": "2015-05-15T13:32:45+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v2.6.12", "version_normalized": "********"}, {"version": "v2.6.11", "version_normalized": "********"}, {"version": "v2.6.10", "version_normalized": "********"}, {"version": "v2.6.9", "version_normalized": "*******"}, {"version": "v2.6.8", "version_normalized": "*******"}, {"version": "v2.6.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "189cf0f7f56d7c4be3b778df15a7f16a29f3680d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/189cf0f7f56d7c4be3b778df15a7f16a29f3680d", "type": "zip", "shasum": "", "reference": "189cf0f7f56d7c4be3b778df15a7f16a29f3680d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.7"}, "time": "2015-05-02T15:18:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.6.6", "version_normalized": "*******", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "db2c48df9658423a8c168d89f7b971b73d3d74a4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/db2c48df9658423a8c168d89f7b971b73d3d74a4", "type": "zip", "shasum": "", "reference": "db2c48df9658423a8c168d89f7b971b73d3d74a4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.6"}, "time": "2015-03-22T16:55:57+00:00"}, {"version": "v2.6.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "86cf0aa16065ffc4545374e9479dd7878bf1d90f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/86cf0aa16065ffc4545374e9479dd7878bf1d90f", "type": "zip", "shasum": "", "reference": "86cf0aa16065ffc4545374e9479dd7878bf1d90f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.5"}, "time": "2015-02-24T11:52:21+00:00"}, {"version": "v2.6.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3f80ecc614fec68d5b4a84a0703db3fdf5ce8584"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3f80ecc614fec68d5b4a84a0703db3fdf5ce8584", "type": "zip", "shasum": "", "reference": "3f80ecc614fec68d5b4a84a0703db3fdf5ce8584"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.4"}, "time": "2015-01-03T08:01:59+00:00", "require-dev": "__unset"}, {"version": "v2.6.3", "version_normalized": "*******"}, {"version": "v2.6.2", "version_normalized": "*******"}, {"version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "93eb315b545b60a908271762fb4bfa1f9954b851"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/93eb315b545b60a908271762fb4bfa1f9954b851", "type": "zip", "shasum": "", "reference": "93eb315b545b60a908271762fb4bfa1f9954b851"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.1"}, "time": "2014-12-02T20:19:20+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "41953ad30ffc5cd710d106cf01eff79f6effa117"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/41953ad30ffc5cd710d106cf01eff79f6effa117", "type": "zip", "shasum": "", "reference": "41953ad30ffc5cd710d106cf01eff79f6effa117"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.6.0-BETA2"}, "time": "2014-10-26T07:46:28+00:00"}, {"version": "v2.6.0-BETA2", "version_normalized": "*******-beta2"}, {"version": "v2.6.0-BETA1", "version_normalized": "*******-beta1"}, {"version": "v2.5.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d45b306421462295e76b94bcf76b963867450327"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d45b306421462295e76b94bcf76b963867450327", "type": "zip", "shasum": "", "reference": "d45b306421462295e76b94bcf76b963867450327"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.12"}, "time": "2015-01-03T08:01:13+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}}, {"version": "v2.5.11", "version_normalized": "********"}, {"version": "v2.5.10", "version_normalized": "********"}, {"version": "v2.5.9", "version_normalized": "*******"}, {"version": "v2.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8819e8e95f14a3544abd7225b0723b617075ca34"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8819e8e95f14a3544abd7225b0723b617075ca34", "type": "zip", "shasum": "", "reference": "8819e8e95f14a3544abd7225b0723b617075ca34"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.8"}, "time": "2014-12-02T20:15:53+00:00"}, {"version": "v2.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3046789831631e5ccdd0df1a1221380eab343922"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3046789831631e5ccdd0df1a1221380eab343922", "type": "zip", "shasum": "", "reference": "3046789831631e5ccdd0df1a1221380eab343922"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.7"}, "time": "2014-10-26T07:41:27+00:00"}, {"version": "v2.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7cdf543a3f31935aae58de4e6e607d4bdeb3f5dc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7cdf543a3f31935aae58de4e6e607d4bdeb3f5dc", "type": "zip", "shasum": "", "reference": "7cdf543a3f31935aae58de4e6e607d4bdeb3f5dc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.6"}, "time": "2014-10-09T16:00:03+00:00"}, {"version": "v2.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "caf5ecc3face1f22884fb74b8edab65ac5ba9976"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/caf5ecc3face1f22884fb74b8edab65ac5ba9976", "type": "zip", "shasum": "", "reference": "caf5ecc3face1f22884fb74b8edab65ac5ba9976"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.5"}, "time": "2014-09-22T09:14:18+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "143abf51b91e4517cde67d416bdf67a90cffa32d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/143abf51b91e4517cde67d416bdf67a90cffa32d", "type": "zip", "shasum": "", "reference": "143abf51b91e4517cde67d416bdf67a90cffa32d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.4"}, "time": "2014-08-31T03:22:04+00:00"}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e24b8215bf39a6a2ce0c262bc5b000724077afa9"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e24b8215bf39a6a2ce0c262bc5b000724077afa9", "type": "zip", "shasum": "", "reference": "e24b8215bf39a6a2ce0c262bc5b000724077afa9"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.3"}, "time": "2014-07-09T09:05:48+00:00"}, {"version": "v2.5.2", "version_normalized": "*******"}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8679e89e6cc9cd415120625071c3cbb3c70311b5"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8679e89e6cc9cd415120625071c3cbb3c70311b5", "type": "zip", "shasum": "", "reference": "8679e89e6cc9cd415120625071c3cbb3c70311b5"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.1"}, "time": "2014-07-08T12:21:33+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bcdd62af59bd3137ede467946a06d41f8644048c"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bcdd62af59bd3137ede467946a06d41f8644048c", "type": "zip", "shasum": "", "reference": "bcdd62af59bd3137ede467946a06d41f8644048c"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.0-RC1"}, "time": "2014-05-12T09:28:39+00:00"}, {"version": "v2.5.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.5.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "339e53ea57506b2532b15545cd9242f09cc48ad4"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/339e53ea57506b2532b15545cd9242f09cc48ad4", "type": "zip", "shasum": "", "reference": "339e53ea57506b2532b15545cd9242f09cc48ad4"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.0-BETA2"}, "time": "2014-04-18T20:40:13+00:00"}, {"version": "v2.5.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "39df955c4456b62c65e272dea2d5fd320d034ce3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/39df955c4456b62c65e272dea2d5fd320d034ce3", "type": "zip", "shasum": "", "reference": "39df955c4456b62c65e272dea2d5fd320d034ce3"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.5.0-BETA1"}, "time": "2014-03-26T11:51:10+00:00"}, {"version": "v2.4.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4e9ddc09c13475fb1a52fb578a8899c8303966c5"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4e9ddc09c13475fb1a52fb578a8899c8303966c5", "type": "zip", "shasum": "", "reference": "4e9ddc09c13475fb1a52fb578a8899c8303966c5"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.10"}, "time": "2014-09-22T08:51:05+00:00", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}}, {"version": "v2.4.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2014a6e253d29ded3b8dd179a8d96ac52f2dfba5"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2014a6e253d29ded3b8dd179a8d96ac52f2dfba5", "type": "zip", "shasum": "", "reference": "2014a6e253d29ded3b8dd179a8d96ac52f2dfba5"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.9"}, "time": "2014-08-31T03:18:18+00:00"}, {"version": "v2.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "52c35be379ec341c15d2edc7f798f7fd69708645"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/52c35be379ec341c15d2edc7f798f7fd69708645", "type": "zip", "shasum": "", "reference": "52c35be379ec341c15d2edc7f798f7fd69708645"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.8"}, "time": "2014-07-09T09:04:55+00:00"}, {"version": "v2.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "256a9363b26594b92bf6b5b136c49f806458abaa"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/256a9363b26594b92bf6b5b136c49f806458abaa", "type": "zip", "shasum": "", "reference": "256a9363b26594b92bf6b5b136c49f806458abaa"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.7"}, "time": "2014-07-08T11:46:35+00:00"}, {"version": "v2.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "268d0a51166edaf84dfcf043c57f273685cb7c93"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/268d0a51166edaf84dfcf043c57f273685cb7c93", "type": "zip", "shasum": "", "reference": "268d0a51166edaf84dfcf043c57f273685cb7c93"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.6"}, "time": "2014-05-12T09:27:48+00:00"}, {"version": "v2.4.5", "version_normalized": "*******"}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "479a5b409723f596ffc3b5178034e4d76ce615b3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/479a5b409723f596ffc3b5178034e4d76ce615b3", "type": "zip", "shasum": "", "reference": "479a5b409723f596ffc3b5178034e4d76ce615b3"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.4"}, "time": "2014-04-18T20:37:09+00:00"}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "c0ca90adac5b581dbd346df23c3e3355ac823238"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/c0ca90adac5b581dbd346df23c3e3355ac823238", "type": "zip", "shasum": "", "reference": "c0ca90adac5b581dbd346df23c3e3355ac823238"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.3"}, "time": "2014-03-26T11:35:33+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "ed1d61b2e23a0fd5dba0b20651258c4633d3e3a7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/ed1d61b2e23a0fd5dba0b20651258c4633d3e3a7", "type": "zip", "shasum": "", "reference": "ed1d61b2e23a0fd5dba0b20651258c4633d3e3a7"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.2"}, "time": "2014-02-11T13:52:09+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "352552da1f50a79f6a6fa427e4a85ee2ea1945f6"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/352552da1f50a79f6a6fa427e4a85ee2ea1945f6", "type": "zip", "shasum": "", "reference": "352552da1f50a79f6a6fa427e4a85ee2ea1945f6"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.1"}, "time": "2014-01-01T08:14:50+00:00"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d7524797f1a6b20a8c7c31fbdd64f38fbfce1797"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d7524797f1a6b20a8c7c31fbdd64f38fbfce1797", "type": "zip", "shasum": "", "reference": "d7524797f1a6b20a8c7c31fbdd64f38fbfce1797"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.0-RC1"}, "time": "2013-11-11T18:40:07+00:00"}, {"version": "v2.4.0-RC1", "version_normalized": "*******-RC1"}, {"version": "v2.4.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "13032e63b946d6e3f1ee0f60035298a175ae5088"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/13032e63b946d6e3f1ee0f60035298a175ae5088", "type": "zip", "shasum": "", "reference": "13032e63b946d6e3f1ee0f60035298a175ae5088"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.0-BETA2"}, "time": "2013-10-16T11:59:56+00:00"}, {"version": "v2.4.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2fd5d92f08774b0c9605e1a8a77273b16364ff20"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2fd5d92f08774b0c9605e1a8a77273b16364ff20", "type": "zip", "shasum": "", "reference": "2fd5d92f08774b0c9605e1a8a77273b16364ff20"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.4.0-BETA1"}, "time": "2013-09-19T09:47:34+00:00"}, {"homepage": "https://symfony.com", "version": "v2.3.42", "version_normalized": "********", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e750fff4bd738e54414fbfdd48ede6b0e99ab808"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e750fff4bd738e54414fbfdd48ede6b0e99ab808", "type": "zip", "shasum": "", "reference": "e750fff4bd738e54414fbfdd48ede6b0e99ab808"}, "support": {"source": "https://github.com/symfony/css-selector/tree/2.3"}, "time": "2016-03-04T07:12:06+00:00", "autoload": {"psr-0": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "extra": {"branch-alias": {"dev-master": "2.3-dev"}}}, {"version": "v2.3.41", "version_normalized": "********"}, {"version": "v2.3.40", "version_normalized": "********"}, {"version": "v2.3.39", "version_normalized": "********"}, {"version": "v2.3.38", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "39e0efb7369481cd51423541dbab3716e336b628"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/39e0efb7369481cd51423541dbab3716e336b628", "type": "zip", "shasum": "", "reference": "39e0efb7369481cd51423541dbab3716e336b628"}, "time": "2016-01-27T04:57:57+00:00"}, {"version": "v2.3.37", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6079e77daad20df46bfda42752e244dd2372f634"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6079e77daad20df46bfda42752e244dd2372f634", "type": "zip", "shasum": "", "reference": "6079e77daad20df46bfda42752e244dd2372f634"}, "time": "2016-01-02T02:53:47+00:00"}, {"version": "v2.3.36", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "21e27bd0cddcc99e4e5faebe92a0befce958d1e8"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/21e27bd0cddcc99e4e5faebe92a0befce958d1e8", "type": "zip", "shasum": "", "reference": "21e27bd0cddcc99e4e5faebe92a0befce958d1e8"}, "time": "2015-12-01T22:08:33+00:00"}, {"version": "v2.3.35", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "93cccf44bf22103694611dc1802714d2cd36f5fb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/93cccf44bf22103694611dc1802714d2cd36f5fb", "type": "zip", "shasum": "", "reference": "93cccf44bf22103694611dc1802714d2cd36f5fb"}, "time": "2015-11-18T08:19:46+00:00"}, {"version": "v2.3.34", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0a9834fb53164b2ef02ec7e95db4eec3ff7b0b34"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0a9834fb53164b2ef02ec7e95db4eec3ff7b0b34", "type": "zip", "shasum": "", "reference": "0a9834fb53164b2ef02ec7e95db4eec3ff7b0b34"}, "time": "2015-10-11T09:37:49+00:00", "autoload": {"psr-0": {"Symfony\\Component\\CssSelector\\": ""}}}, {"version": "v2.3.33", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6bc51c114f8d14ad7ad66366ccbd2d17cf6cf8f8"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6bc51c114f8d14ad7ad66366ccbd2d17cf6cf8f8", "type": "zip", "shasum": "", "reference": "6bc51c114f8d14ad7ad66366ccbd2d17cf6cf8f8"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.33"}, "time": "2015-09-21T06:00:16+00:00", "require-dev": {"symfony/phpunit-bridge": "~2.7"}}, {"version": "v2.3.32", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "75cc67b407df5617fd58c40caa872e9d81de38cc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/75cc67b407df5617fd58c40caa872e9d81de38cc", "type": "zip", "shasum": "", "reference": "75cc67b407df5617fd58c40caa872e9d81de38cc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.32"}, "time": "2015-05-15T13:28:34+00:00"}, {"version": "v2.3.31", "version_normalized": "********"}, {"version": "v2.3.30", "version_normalized": "********"}, {"version": "v2.3.29", "version_normalized": "********"}, {"version": "v2.3.28", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7deb1f67f7776866f85df1124360acb5b3134122"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7deb1f67f7776866f85df1124360acb5b3134122", "type": "zip", "shasum": "", "reference": "7deb1f67f7776866f85df1124360acb5b3134122"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.28"}, "time": "2015-05-01T14:06:45+00:00"}, {"homepage": "http://symfony.com", "version": "v2.3.27", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "241746d062f873d77d1886c2b97b56811bb5f659"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/241746d062f873d77d1886c2b97b56811bb5f659", "type": "zip", "shasum": "", "reference": "241746d062f873d77d1886c2b97b56811bb5f659"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.27"}, "time": "2015-03-22T16:41:17+00:00"}, {"version": "v2.3.26", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d11b64b8e4e96627dec3dabec936cdf410d48d78"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d11b64b8e4e96627dec3dabec936cdf410d48d78", "type": "zip", "shasum": "", "reference": "d11b64b8e4e96627dec3dabec936cdf410d48d78"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.26"}, "time": "2015-02-24T10:24:26+00:00"}, {"version": "v2.3.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "202ff2f0ba271d1a77f937b3ae177887da9241d7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/202ff2f0ba271d1a77f937b3ae177887da9241d7", "type": "zip", "shasum": "", "reference": "202ff2f0ba271d1a77f937b3ae177887da9241d7"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.25"}, "time": "2015-01-01T12:56:52+00:00", "require-dev": "__unset"}, {"version": "v2.3.24", "version_normalized": "********"}, {"version": "v2.3.23", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f886003774b307148a0b105f74ff11a564cb31b1"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f886003774b307148a0b105f74ff11a564cb31b1", "type": "zip", "shasum": "", "reference": "f886003774b307148a0b105f74ff11a564cb31b1"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.23"}, "time": "2014-11-30T13:33:44+00:00"}, {"version": "v2.3.22", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "5bb1c69c1e2aa246292792024aeacd75f7e5d26a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/5bb1c69c1e2aa246292792024aeacd75f7e5d26a", "type": "zip", "shasum": "", "reference": "5bb1c69c1e2aa246292792024aeacd75f7e5d26a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.22"}, "time": "2014-10-26T07:30:58+00:00"}, {"version": "v2.3.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "d9943386b648d21746bed25cc24f61fab1387943"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/d9943386b648d21746bed25cc24f61fab1387943", "type": "zip", "shasum": "", "reference": "d9943386b648d21746bed25cc24f61fab1387943"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.21"}, "time": "2014-10-09T12:30:02+00:00"}, {"version": "v2.3.20", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9b320f7b6baa094032996e3e7170ced7405f64f3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9b320f7b6baa094032996e3e7170ced7405f64f3", "type": "zip", "shasum": "", "reference": "9b320f7b6baa094032996e3e7170ced7405f64f3"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.20"}, "time": "2014-09-22T08:32:35+00:00"}, {"version": "v2.3.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8953d325d3341c246abadb79be172de95ef80664"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8953d325d3341c246abadb79be172de95ef80664", "type": "zip", "shasum": "", "reference": "8953d325d3341c246abadb79be172de95ef80664"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.19"}, "time": "2014-08-27T08:24:06+00:00"}, {"version": "v2.3.18", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "84460a630f5e61486c2641a1a9b9f30df091ad49"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/84460a630f5e61486c2641a1a9b9f30df091ad49", "type": "zip", "shasum": "", "reference": "84460a630f5e61486c2641a1a9b9f30df091ad49"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.18"}, "time": "2014-07-07T10:13:42+00:00"}, {"version": "v2.3.17", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "f31e5ef34bc16fd08c63992b50cfd4e47c5edf8f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/f31e5ef34bc16fd08c63992b50cfd4e47c5edf8f", "type": "zip", "shasum": "", "reference": "f31e5ef34bc16fd08c63992b50cfd4e47c5edf8f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.17"}, "time": "2014-07-07T09:57:21+00:00"}, {"version": "v2.3.16", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "2ff53e8a7870b453836e879b083b971d455e174d"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/2ff53e8a7870b453836e879b083b971d455e174d", "type": "zip", "shasum": "", "reference": "2ff53e8a7870b453836e879b083b971d455e174d"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.16"}, "time": "2014-05-12T09:13:35+00:00"}, {"version": "v2.3.15", "version_normalized": "********"}, {"version": "v2.3.14", "version_normalized": "********"}, {"version": "v2.3.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "0d2ab42d49536760f045069415832c9c604c8cc7"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/0d2ab42d49536760f045069415832c9c604c8cc7", "type": "zip", "shasum": "", "reference": "0d2ab42d49536760f045069415832c9c604c8cc7"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.13"}, "time": "2014-04-18T20:35:25+00:00"}, {"version": "v2.3.12", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "fd008ce53fdbcf315d602921abe6fecad19804d3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/fd008ce53fdbcf315d602921abe6fecad19804d3", "type": "zip", "shasum": "", "reference": "fd008ce53fdbcf315d602921abe6fecad19804d3"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.12"}, "time": "2014-03-26T07:21:50+00:00"}, {"version": "v2.3.11", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a7450fde13926ab5b796f7884a05e91372dcbccc"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a7450fde13926ab5b796f7884a05e91372dcbccc", "type": "zip", "shasum": "", "reference": "a7450fde13926ab5b796f7884a05e91372dcbccc"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.11"}, "time": "2014-02-11T10:29:24+00:00"}, {"version": "v2.3.10", "version_normalized": "********"}, {"version": "v2.3.9", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "48851624cb5808d16af98651aed59e5188f7277c"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/48851624cb5808d16af98651aed59e5188f7277c", "type": "zip", "shasum": "", "reference": "48851624cb5808d16af98651aed59e5188f7277c"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.9"}, "time": "2014-01-01T07:52:14+00:00"}, {"version": "v2.3.8", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8df20c54ffa650df860a3e42dbcd1fbd25a24977"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8df20c54ffa650df860a3e42dbcd1fbd25a24977", "type": "zip", "shasum": "", "reference": "8df20c54ffa650df860a3e42dbcd1fbd25a24977"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.8"}, "time": "2013-10-30T08:30:20+00:00"}, {"version": "v2.3.7", "version_normalized": "*******"}, {"version": "v2.3.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4e01c74f19ea049773fa3ee9fb2bb5bf5e14f7eb"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4e01c74f19ea049773fa3ee9fb2bb5bf5e14f7eb", "type": "zip", "shasum": "", "reference": "4e01c74f19ea049773fa3ee9fb2bb5bf5e14f7eb"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.6"}, "time": "2013-09-19T09:45:20+00:00"}, {"version": "v2.3.5", "version_normalized": "*******"}, {"version": "v2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "885544201cb24e79754da1dbd61bd779c2e4353e"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/885544201cb24e79754da1dbd61bd779c2e4353e", "type": "zip", "shasum": "", "reference": "885544201cb24e79754da1dbd61bd779c2e4353e"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.4"}, "time": "2013-07-21T12:12:18+00:00"}, {"version": "v2.3.3", "version_normalized": "*******"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e3aa2495604a483b58f318ad6de845b88ec410d6"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e3aa2495604a483b58f318ad6de845b88ec410d6", "type": "zip", "shasum": "", "reference": "e3aa2495604a483b58f318ad6de845b88ec410d6"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.2"}, "time": "2013-07-01T12:24:43+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "6cf5ed69c27ac001d52b448a621f631e4e9b4176"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/6cf5ed69c27ac001d52b448a621f631e4e9b4176", "type": "zip", "shasum": "", "reference": "6cf5ed69c27ac001d52b448a621f631e4e9b4176"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.3.1"}, "time": "2013-05-19T18:59:12+00:00"}, {"version": "v2.3.0", "version_normalized": "*******"}, {"version": "v2.2.11", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9ff0e208cfccd5a49a233221a645ce60a582f7d0"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9ff0e208cfccd5a49a233221a645ce60a582f7d0", "type": "zip", "shasum": "", "reference": "9ff0e208cfccd5a49a233221a645ce60a582f7d0"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.2.11"}, "time": "2013-09-19T09:36:05+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.2.10", "version_normalized": "********"}, {"version": "v2.2.9", "version_normalized": "*******"}, {"version": "v2.2.8", "version_normalized": "*******"}, {"version": "v2.2.7", "version_normalized": "*******"}, {"version": "v2.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "a5deeae9ed56895cff84fd9b9844727eec45a373"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/a5deeae9ed56895cff84fd9b9844727eec45a373", "type": "zip", "shasum": "", "reference": "a5deeae9ed56895cff84fd9b9844727eec45a373"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.2.6"}, "time": "2013-07-01T12:15:46+00:00"}, {"version": "v2.2.5", "version_normalized": "*******"}, {"version": "v2.2.4", "version_normalized": "*******"}, {"version": "v2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8b4d745ffad13dbbfb99fcaccd97ae2632048260"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8b4d745ffad13dbbfb99fcaccd97ae2632048260", "type": "zip", "shasum": "", "reference": "8b4d745ffad13dbbfb99fcaccd97ae2632048260"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.2.3"}, "time": "2013-05-19T19:00:13+00:00"}, {"version": "v2.2.2", "version_normalized": "*******"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "931f7fa8b01688f7c839784e9cf2897e118e37d2"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/931f7fa8b01688f7c839784e9cf2897e118e37d2", "type": "zip", "shasum": "", "reference": "931f7fa8b01688f7c839784e9cf2897e118e37d2"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.2.1"}, "time": "2013-01-17T15:25:59+00:00"}, {"version": "v2.2.0", "version_normalized": "*******"}, {"version": "v2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "bf7bb82a099dfb26b018daf70ad1985fea4d2997"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/bf7bb82a099dfb26b018daf70ad1985fea4d2997", "type": "zip", "shasum": "", "reference": "bf7bb82a099dfb26b018daf70ad1985fea4d2997"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.1.13"}, "time": "2013-05-17T00:31:34+00:00", "autoload": {"psr-0": {"Symfony\\Component\\CssSelector": ""}}, "extra": "__unset"}, {"version": "v2.1.12", "version_normalized": "********"}, {"version": "v2.1.11", "version_normalized": "********"}, {"version": "v2.1.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8e8cb053407749391e14c0cae7ac3b7e155c1392"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8e8cb053407749391e14c0cae7ac3b7e155c1392", "type": "zip", "shasum": "", "reference": "8e8cb053407749391e14c0cae7ac3b7e155c1392"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.1.10"}, "time": "2013-01-09T08:51:07+00:00"}, {"version": "v2.1.9", "version_normalized": "*******"}, {"version": "v2.1.8", "version_normalized": "*******"}, {"version": "v2.1.7", "version_normalized": "*******"}, {"version": "v2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "3bf44fd37af95e0fd7764040abf9744f189fba05"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/3bf44fd37af95e0fd7764040abf9744f189fba05", "type": "zip", "shasum": "", "reference": "3bf44fd37af95e0fd7764040abf9744f189fba05"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.1.6"}, "time": "2012-12-11T10:33:11+00:00"}, {"version": "v2.1.5", "version_normalized": "*******"}, {"version": "v2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "abf8279792230c6685234eb0fa95b0a85e9e9c7a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/abf8279792230c6685234eb0fa95b0a85e9e9c7a", "type": "zip", "shasum": "", "reference": "abf8279792230c6685234eb0fa95b0a85e9e9c7a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.1.4"}, "time": "2012-11-08T09:51:48+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "02fa0bf05b6db612e008d04184d6408c0599e73b"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/02fa0bf05b6db612e008d04184d6408c0599e73b", "type": "zip", "shasum": "", "reference": "02fa0bf05b6db612e008d04184d6408c0599e73b"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.1.3"}, "time": "2012-10-04T15:17:57+00:00"}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "676a7fd9b5027da85b58dd11db5561fef01c6271"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/676a7fd9b5027da85b58dd11db5561fef01c6271", "type": "zip", "shasum": "", "reference": "676a7fd9b5027da85b58dd11db5561fef01c6271"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.1.2"}, "time": "2012-08-22T13:48:41+00:00"}, {"version": "v2.1.1", "version_normalized": "*******"}, {"version": "v2.1.0", "version_normalized": "*******"}, {"version": "v2.0.25", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "354d622bce93ab079ed0ca16874f5815f2a73323"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/354d622bce93ab079ed0ca16874f5815f2a73323", "type": "zip", "shasum": "", "reference": "354d622bce93ab079ed0ca16874f5815f2a73323"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.25"}, "time": "2013-01-07T11:07:21+00:00", "require": {"php": ">=5.3.2"}, "extra": "__unset"}, {"version": "v2.0.24", "version_normalized": "********"}, {"version": "v2.0.23", "version_normalized": "********"}, {"version": "v2.0.22", "version_normalized": "********"}, {"version": "v2.0.21", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9f6108e4b181bb9b0b99f0358d80a5d76da4911a"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9f6108e4b181bb9b0b99f0358d80a5d76da4911a", "type": "zip", "shasum": "", "reference": "9f6108e4b181bb9b0b99f0358d80a5d76da4911a"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.21"}, "time": "2012-12-11T10:27:06+00:00"}, {"version": "v2.0.20", "version_normalized": "********"}, {"version": "v2.0.19", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e37dee425f5f3f7e31d30626ec04727e709b9b06"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e37dee425f5f3f7e31d30626ec04727e709b9b06", "type": "zip", "shasum": "", "reference": "e37dee425f5f3f7e31d30626ec04727e709b9b06"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.19"}, "time": "2012-07-09T12:43:50+00:00"}, {"version": "v2.0.18", "version_normalized": "********"}, {"version": "v2.0.17", "version_normalized": "********"}, {"version": "v2.0.16", "version_normalized": "********"}, {"version": "v2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "76ce6fd53c6ed1bca1b9b7d594e83cefd4748d1f"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/76ce6fd53c6ed1bca1b9b7d594e83cefd4748d1f", "type": "zip", "shasum": "", "reference": "76ce6fd53c6ed1bca1b9b7d594e83cefd4748d1f"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.15"}, "time": "2012-05-20T16:15:10+00:00"}, {"version": "v2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "23fa9f4df262d48e7bd740741ed3903f5b674778"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/23fa9f4df262d48e7bd740741ed3903f5b674778", "type": "zip", "shasum": "", "reference": "23fa9f4df262d48e7bd740741ed3903f5b674778"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.14"}, "time": "2012-05-16T07:53:50+00:00"}, {"version": "v2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "e22ee361ef90283fa48002cec4cc0c018ff4af37"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/e22ee361ef90283fa48002cec4cc0c018ff4af37", "type": "zip", "shasum": "", "reference": "e22ee361ef90283fa48002cec4cc0c018ff4af37"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.13"}, "time": "2012-03-11T09:18:25+00:00"}, {"version": "v2.0.12", "version_normalized": "********"}, {"version": "v2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "20165ecbb58254c560eb579879b90c3b41510b31"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/20165ecbb58254c560eb579879b90c3b41510b31", "type": "zip", "shasum": "", "reference": "20165ecbb58254c560eb579879b90c3b41510b31"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.10"}, "time": "2012-01-05T13:51:20+00:00"}, {"version": "v2.0.9", "version_normalized": "*******"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "4b6e5e7fbe86c68eb4b6b75a3b6ff53d6d6c1017"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/4b6e5e7fbe86c68eb4b6b75a3b6ff53d6d6c1017", "type": "zip", "shasum": "", "reference": "4b6e5e7fbe86c68eb4b6b75a3b6ff53d6d6c1017"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.7"}, "time": "2011-12-01T07:46:19+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "9afb1eed337e8bb70dcdef165a0e83100f28e8f3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/9afb1eed337e8bb70dcdef165a0e83100f28e8f3", "type": "zip", "shasum": "", "reference": "9afb1eed337e8bb70dcdef165a0e83100f28e8f3"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.6"}, "time": "2011-11-02T13:18:45+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "7e103001d8888bc7f98d3efd2d2eafae536da8d3"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/7e103001d8888bc7f98d3efd2d2eafae536da8d3", "type": "zip", "shasum": "", "reference": "7e103001d8888bc7f98d3efd2d2eafae536da8d3"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.5"}, "time": "2011-11-02T11:42:41+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/css-selector.git", "type": "git", "reference": "8b836c58bbfcd7b9193884ce82c6a6050ec856c6"}, "dist": {"url": "https://api.github.com/repos/symfony/css-selector/zipball/8b836c58bbfcd7b9193884ce82c6a6050ec856c6", "type": "zip", "shasum": "", "reference": "8b836c58bbfcd7b9193884ce82c6a6050ec856c6"}, "support": {"source": "https://github.com/symfony/css-selector/tree/v2.0.4"}, "time": "2011-09-26T22:55:43+00:00", "autoload": "__unset", "target-dir": "__unset"}]}, "security-advisories": [], "last-modified": "Thu, 29 May 2025 07:50:22 GMT"}