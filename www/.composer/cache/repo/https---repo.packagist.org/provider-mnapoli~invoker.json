{"minified": "composer/2.0", "packages": {"mnapoli/invoker": [{"name": "mnapoli/invoker", "description": "Generic and extensible callable invoker", "keywords": ["dependency", "invoke", "injection", "dependency-injection", "callable", "invoker"], "homepage": "https://github.com/PHP-DI/Invoker", "version": "2.3.6", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "59f15608528d8a8838d69b422a919fd6b16aa576"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/59f15608528d8a8838d69b422a919fd6b16aa576", "type": "zip", "shasum": "", "reference": "59f15608528d8a8838d69b422a919fd6b16aa576"}, "type": "library", "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.6"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}], "time": "2025-01-17T12:49:27+00:00", "autoload": {"psr-4": {"Invoker\\": "src/"}}, "require": {"php": ">=7.3", "psr/container": "^1.0|^2.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "athletic/athletic": "~0.1.8", "mnapoli/hard-mode": "~0.3.0"}, "abandoned": "php-di/invoker"}, {"version": "2.3.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "93d381d0ec42074f43530506849fdc2d3a86a809"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/93d381d0ec42074f43530506849fdc2d3a86a809", "type": "zip", "shasum": "", "reference": "93d381d0ec42074f43530506849fdc2d3a86a809"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.5"}, "time": "2025-01-17T10:02:37+00:00"}, {"version": "2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "33234b32dafa8eb69202f950a1fc92055ed76a86"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/33234b32dafa8eb69202f950a1fc92055ed76a86", "type": "zip", "shasum": "", "reference": "33234b32dafa8eb69202f950a1fc92055ed76a86"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.4"}, "time": "2023-09-08T09:24:21+00:00"}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "cd6d9f267d1a3474bdddf1be1da079f01b942786"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/cd6d9f267d1a3474bdddf1be1da079f01b942786", "type": "zip", "shasum": "", "reference": "cd6d9f267d1a3474bdddf1be1da079f01b942786"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.3"}, "time": "2021-12-13T09:22:56+00:00"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "5214cbe5aad066022cd845dbf313f0e47aed928f"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/5214cbe5aad066022cd845dbf313f0e47aed928f", "type": "zip", "shasum": "", "reference": "5214cbe5aad066022cd845dbf313f0e47aed928f"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.2"}, "time": "2021-07-30T15:05:32+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "360efcf2b30282ac4f9e6cb5b06ac795013d3d03"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/360efcf2b30282ac4f9e6cb5b06ac795013d3d03", "type": "zip", "shasum": "", "reference": "360efcf2b30282ac4f9e6cb5b06ac795013d3d03"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.1"}, "time": "2021-07-29T12:13:40+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "992fec6c56f2d1ad1ad5fee28267867c85bfb8f9"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/992fec6c56f2d1ad1ad5fee28267867c85bfb8f9", "type": "zip", "shasum": "", "reference": "992fec6c56f2d1ad1ad5fee28267867c85bfb8f9"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.0"}, "time": "2021-01-15T10:25:40+00:00", "require": {"php": ">=7.3", "psr/container": "~1.0"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "e08a7c87068daeaeef464b95d81643ea530bc535"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/e08a7c87068daeaeef464b95d81643ea530bc535", "type": "zip", "shasum": "", "reference": "e08a7c87068daeaeef464b95d81643ea530bc535"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.2.0"}, "time": "2020-10-12T12:15:50+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "6a6f8f276d2680e77d06294b9fd67b4881b1f82d"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/6a6f8f276d2680e77d06294b9fd67b4881b1f82d", "type": "zip", "shasum": "", "reference": "6a6f8f276d2680e77d06294b9fd67b4881b1f82d"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.1.0"}, "funding": [], "time": "2020-08-01T15:36:25+00:00", "require-dev": {"phpunit/phpunit": "^9.0", "athletic/athletic": "~0.1.8"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "540c27c86f663e20fe39a24cd72fa76cdb21d41a"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/540c27c86f663e20fe39a24cd72fa76cdb21d41a", "type": "zip", "shasum": "", "reference": "540c27c86f663e20fe39a24cd72fa76cdb21d41a"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.0.0"}, "time": "2017-03-20T19:28:22+00:00", "require": {"psr/container": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "athletic/athletic": "~0.1.8"}, "funding": "__unset"}, {"version": "1.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "1f4ca63b9abc66109e53b255e465d0ddb5c2e3f7"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/1f4ca63b9abc66109e53b255e465d0ddb5c2e3f7", "type": "zip", "shasum": "", "reference": "1f4ca63b9abc66109e53b255e465d0ddb5c2e3f7"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.3.3"}, "time": "2016-07-14T13:09:58+00:00", "require": {"container-interop/container-interop": "~1.1"}}, {"version": "1.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "4140c38ca17268adc500b1e5c85c4055e48608d2"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/4140c38ca17268adc500b1e5c85c4055e48608d2", "type": "zip", "shasum": "", "reference": "4140c38ca17268adc500b1e5c85c4055e48608d2"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.3.2"}, "time": "2016-06-12T09:09:55+00:00"}, {"version": "1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "e67ce4ca7b2cebcd06929bf383e0b18441e8ea4d"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/e67ce4ca7b2cebcd06929bf383e0b18441e8ea4d", "type": "zip", "shasum": "", "reference": "e67ce4ca7b2cebcd06929bf383e0b18441e8ea4d"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.3.1"}, "time": "2016-05-29T13:31:47+00:00"}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "c5c50237115803d7410d13d9d6afb5afe6526fac"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/c5c50237115803d7410d13d9d6afb5afe6526fac", "type": "zip", "shasum": "", "reference": "c5c50237115803d7410d13d9d6afb5afe6526fac"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.3.0"}, "time": "2016-03-20T17:49:41+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "9949fff87fcf14e8f2ccfbe36dac1e5921944c48"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/9949fff87fcf14e8f2ccfbe36dac1e5921944c48", "type": "zip", "shasum": "", "reference": "9949fff87fcf14e8f2ccfbe36dac1e5921944c48"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.2.0"}, "time": "2015-10-22T19:49:23+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "7b2d2cd0e40b1583fe6601fb343a280c378669c9"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/7b2d2cd0e40b1583fe6601fb343a280c378669c9", "type": "zip", "shasum": "", "reference": "7b2d2cd0e40b1583fe6601fb343a280c378669c9"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.1.1"}, "time": "2015-09-17T09:57:25+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "67ad285484a1f783ba2ce26866b5d48f171d3abf"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/67ad285484a1f783ba2ce26866b5d48f171d3abf", "type": "zip", "shasum": "", "reference": "67ad285484a1f783ba2ce26866b5d48f171d3abf"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.1.0"}, "time": "2015-09-12T09:14:11+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "2eb8f3a9b44c1427865134ef585d986ca89bce36"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/2eb8f3a9b44c1427865134ef585d986ca89bce36", "type": "zip", "shasum": "", "reference": "2eb8f3a9b44c1427865134ef585d986ca89bce36"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.0.1"}, "time": "2015-09-02T16:01:10+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHP-DI/Invoker.git", "type": "git", "reference": "7ea703c62dbb29d64763fa85258826034ce3c97d"}, "dist": {"url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/7ea703c62dbb29d64763fa85258826034ce3c97d", "type": "zip", "shasum": "", "reference": "7ea703c62dbb29d64763fa85258826034ce3c97d"}, "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/1.0.0"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}], "time": "2015-04-24T10:18:34+00:00"}, {"homepage": "https://github.com/mnapoli/Invoker", "version": "0.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/Invoker.git", "type": "git", "reference": "ca2c470d68335fa28be1a2b6e89c7d0792c690fb"}, "dist": {"url": "https://api.github.com/repos/mnapoli/Invoker/zipball/ca2c470d68335fa28be1a2b6e89c7d0792c690fb", "type": "zip", "shasum": "", "reference": "ca2c470d68335fa28be1a2b6e89c7d0792c690fb"}, "support": {"issues": "https://github.com/mnapoli/Invoker/issues", "source": "https://github.com/mnapoli/Invoker/tree/master"}, "time": "2015-04-01T07:51:10+00:00", "funding": "__unset"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/Invoker.git", "type": "git", "reference": "a4b30cf22aa6e9e9a512ab253bbfb3db808396d1"}, "dist": {"url": "https://api.github.com/repos/mnapoli/Invoker/zipball/a4b30cf22aa6e9e9a512ab253bbfb3db808396d1", "type": "zip", "shasum": "", "reference": "a4b30cf22aa6e9e9a512ab253bbfb3db808396d1"}, "time": "2015-03-30T09:10:28+00:00", "require-dev": {"phpunit/phpunit": "~4.5"}}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/mnapoli/Invoker.git", "type": "git", "reference": "989a7a55ad81df44e049025e36eef4e3fbec8b98"}, "dist": {"url": "https://api.github.com/repos/mnapoli/Invoker/zipball/989a7a55ad81df44e049025e36eef4e3fbec8b98", "type": "zip", "shasum": "", "reference": "989a7a55ad81df44e049025e36eef4e3fbec8b98"}, "time": "2015-03-22T05:23:39+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 17 Jan 2025 12:49:56 GMT"}