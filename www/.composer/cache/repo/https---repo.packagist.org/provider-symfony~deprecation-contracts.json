{"minified": "composer/2.0", "packages": {"symfony/deprecation-contracts": [{"name": "symfony/deprecation-contracts", "description": "A generic function and convention to trigger deprecation notices", "keywords": [], "homepage": "https://symfony.com", "version": "v3.6.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "type": "zip", "shasum": "", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "type": "library", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00", "autoload": {"files": ["function.php"]}, "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "require": {"php": ">=8.1"}}, {"version": "v3.6.0-BETA1", "version_normalized": "*******-beta1", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0-BETA1"}}, {"version": "v3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "type": "zip", "shasum": "", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}}, {"version": "v3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "type": "zip", "shasum": "", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "time": "2024-04-18T09:32:20+00:00"}, {"version": "v3.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "fae4ea6bf0065f4ac73673a836aedf11c0e776de"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/fae4ea6bf0065f4ac73673a836aedf11c0e776de", "type": "zip", "shasum": "", "reference": "fae4ea6bf0065f4ac73673a836aedf11c0e776de"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.4.3"}, "time": "2024-09-25T14:18:03+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.4-dev"}}}, {"version": "v3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/7c3aff79d10325257a001fcf92d991f24fc967cf", "type": "zip", "shasum": "", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.4.0"}, "time": "2023-05-23T14:45:45+00:00"}, {"version": "v3.3.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.3.0"}}, {"version": "v3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "e2d1534420bd723d0ef5aec58a22c5fe60ce6f5e"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e2d1534420bd723d0ef5aec58a22c5fe60ce6f5e", "type": "zip", "shasum": "", "reference": "e2d1534420bd723d0ef5aec58a22c5fe60ce6f5e"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.2.1"}, "time": "2023-03-01T10:25:55+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.3-dev"}}}, {"version": "v3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "1ee04c65529dea5d8744774d474e7cbd2f1206d3"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/1ee04c65529dea5d8744774d474e7cbd2f1206d3", "type": "zip", "shasum": "", "reference": "1ee04c65529dea5d8744774d474e7cbd2f1206d3"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.2.0"}, "time": "2022-11-25T10:21:52+00:00"}, {"version": "v3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "07f1b9cc2ffee6aaafcf4b710fbc38ff736bd918"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/07f1b9cc2ffee6aaafcf4b710fbc38ff736bd918", "type": "zip", "shasum": "", "reference": "07f1b9cc2ffee6aaafcf4b710fbc38ff736bd918"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.1.1"}, "time": "2022-02-25T11:15:52+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.1-dev"}}}, {"version": "v3.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.1.0"}}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "type": "zip", "shasum": "", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.2"}, "time": "2022-01-02T09:55:41+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.0.2"}}, {"version": "v3.0.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.1"}}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "c726b64c1ccfe2896cb7df2e1331c357ad1c8ced"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/c726b64c1ccfe2896cb7df2e1331c357ad1c8ced", "type": "zip", "shasum": "", "reference": "c726b64c1ccfe2896cb7df2e1331c357ad1c8ced"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.0"}, "time": "2021-11-01T23:48:49+00:00"}, {"version": "v2.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "type": "zip", "shasum": "", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "time": "2024-09-25T14:11:13+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "require": {"php": ">=7.1"}}, {"version": "v2.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "type": "zip", "shasum": "", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.3"}, "time": "2023-01-24T14:02:46+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "type": "zip", "shasum": "", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "time": "2022-01-02T09:53:40+00:00"}, {"version": "v2.5.1", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.1"}}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "6f981ee24cf69ee7ce9736146d1c57c2780598a8"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/6f981ee24cf69ee7ce9736146d1c57c2780598a8", "type": "zip", "shasum": "", "reference": "6f981ee24cf69ee7ce9736146d1c57c2780598a8"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.0"}, "time": "2021-07-12T14:48:14+00:00"}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "5f38c8804a9e97d23e0c8d63341088cd8a22d627"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5f38c8804a9e97d23e0c8d63341088cd8a22d627", "type": "zip", "shasum": "", "reference": "5f38c8804a9e97d23e0c8d63341088cd8a22d627"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.4.1"}, "time": "2021-03-23T23:28:01+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.4-dev"}}}, {"version": "v2.4.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.4.0"}}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5fa56b4074d1ae755beb55617ddafe6f5d78f665", "type": "zip", "shasum": "", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/master"}, "time": "2020-09-07T11:33:47+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.2-dev"}}}, {"version": "v2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "5e20b83385a77593259c9f8beb2c43cd03b2ac14"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5e20b83385a77593259c9f8beb2c43cd03b2ac14", "type": "zip", "shasum": "", "reference": "5e20b83385a77593259c9f8beb2c43cd03b2ac14"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.1.3"}, "time": "2020-06-06T08:49:21+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337", "type": "zip", "shasum": "", "reference": "dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.1.2"}, "time": "2020-05-27T08:34:37+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/deprecation-contracts.git", "type": "git", "reference": "ede224dcbc36138943a296107db2b8b2a690ac1c"}, "dist": {"url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/ede224dcbc36138943a296107db2b8b2a690ac1c", "type": "zip", "shasum": "", "reference": "ede224dcbc36138943a296107db2b8b2a690ac1c"}, "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.1.1"}, "time": "2020-02-14T07:31:56+00:00", "require": {"php": "^7.1"}}, {"version": "v2.1.0", "version_normalized": "*******", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/master"}}]}, "security-advisories": [], "last-modified": "Sun, 25 May 2025 20:28:18 GMT"}