{"minified": "composer/2.0", "packages": {"league/uri-interfaces": [{"name": "league/uri-interfaces", "description": "Common interfaces and classes for URI representation and interaction", "keywords": ["http", "url", "https", "uri", "ftp", "hostname", "data-uri", "ws", "rfc3986", "parse_url", "rfc6570", "querystring", "psr-7", "query-string", "rfc3987", "parse_str", "file-uri"], "homepage": "https://uri.thephpleague.com", "version": "7.5.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "type": "zip", "shasum": "", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"}, "type": "library", "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.5.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:18:47+00:00", "autoload": {"psr-4": {"League\\Uri\\": ""}}, "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "require": {"php": "^8.1", "ext-filter": "*", "psr/http-message": "^1.1 || ^2.0", "psr/http-factory": "^1"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}}, {"version": "7.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "8d43ef5c841032c87e2de015972c06f3865ef718"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/8d43ef5c841032c87e2de015972c06f3865ef718", "type": "zip", "shasum": "", "reference": "8d43ef5c841032c87e2de015972c06f3865ef718"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.4.1"}, "time": "2024-03-23T07:42:40+00:00"}, {"version": "7.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "bd8c487ec236930f7bbc42b8d374fa882fbba0f3"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/bd8c487ec236930f7bbc42b8d374fa882fbba0f3", "type": "zip", "shasum": "", "reference": "bd8c487ec236930f7bbc42b8d374fa882fbba0f3"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.4.0"}, "time": "2023-11-24T15:40:42+00:00"}, {"version": "7.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "c409b60ed2245ff94c965a8c798a60166db53361"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/c409b60ed2245ff94c965a8c798a60166db53361", "type": "zip", "shasum": "", "reference": "c409b60ed2245ff94c965a8c798a60166db53361"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.3.0"}, "time": "2023-09-09T17:21:43+00:00"}, {"version": "7.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "43fa071050fcba89aefb5d4789a4a5a73874c44b"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/43fa071050fcba89aefb5d4789a4a5a73874c44b", "type": "zip", "shasum": "", "reference": "43fa071050fcba89aefb5d4789a4a5a73874c44b"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.2.0"}, "time": "2023-08-30T19:43:38+00:00"}, {"version": "7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "c3ea9306c67c9a1a72312705e8adfcb9cf167310"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/c3ea9306c67c9a1a72312705e8adfcb9cf167310", "type": "zip", "shasum": "", "reference": "c3ea9306c67c9a1a72312705e8adfcb9cf167310"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.1.0"}, "time": "2023-08-21T20:15:03+00:00"}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "0b5f360829024564b49734b26058d98d90f5b3ba"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/0b5f360829024564b49734b26058d98d90f5b3ba", "type": "zip", "shasum": "", "reference": "0b5f360829024564b49734b26058d98d90f5b3ba"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.0.0"}, "time": "2023-08-10T14:26:14+00:00"}, {"keywords": ["path", "url", "components", "query", "idna", "uri", "host", "port", "fragment", "authority", "rfc3986", "scheme", "userinfo"], "version": "7.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "684e688fa3b3cf3726e651af16961df30c38f642"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/684e688fa3b3cf3726e651af16961df30c38f642", "type": "zip", "shasum": "", "reference": "684e688fa3b3cf3726e651af16961df30c38f642"}, "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.0.0-beta.2"}, "time": "2023-06-17T10:08:33+00:00", "require": {"php": "^8.1"}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/polyfill-intl-idn": "to use the IDNA feature via Symfony Polyfill"}}, {"version": "7.0.0-beta.1", "version_normalized": "*******-beta1", "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.0.0-beta.1"}}, {"description": "Common interface for URI representation", "keywords": ["url", "uri", "rfc3986", "rfc3987"], "homepage": "http://github.com/thephpleague/uri-interfaces", "version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "type": "zip", "shasum": "", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383"}, "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.3.0"}, "time": "2021-06-28T04:27:21+00:00", "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require": {"php": "^7.2 || ^8.0", "ext-json": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "phpstan/phpstan": "^0.12.90", "phpstan/phpstan-strict-rules": "^0.12.9", "phpstan/phpstan-phpunit": "^0.12.19", "phpunit/phpunit": "^8.5.15 || ^9.5"}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/intl": "to use the IDNA feature via Symfony Polyfill"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "667f150e589d65d79c89ffe662e426704f84224f"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/667f150e589d65d79c89ffe662e426704f84224f", "type": "zip", "shasum": "", "reference": "667f150e589d65d79c89ffe662e426704f84224f"}, "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.2.0"}, "time": "2020-10-31T13:45:51+00:00", "require": {"php": "^7.1 || ^8.0", "ext-json": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12"}, "suggest": "__unset"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "0068a469958895ceaf3afcb489c0258adfa1e406"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/0068a469958895ceaf3afcb489c0258adfa1e406", "type": "zip", "shasum": "", "reference": "0068a469958895ceaf3afcb489c0258adfa1e406"}, "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/master"}, "time": "2020-02-08T12:10:37+00:00", "require": {"php": "^7.1", "ext-json": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^7.0|^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12"}, "funding": "__unset"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "ae2601a183ad14b73628a305c0f906c030757b39"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/ae2601a183ad14b73628a305c0f906c030757b39", "type": "zip", "shasum": "", "reference": "ae2601a183ad14b73628a305c0f906c030757b39"}, "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.0.1"}, "time": "2019-12-17T13:59:29+00:00", "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpunit/phpunit": "^7.0|^8.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpstan/phpstan-phpunit": "^0.11"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "aac2aa37f20cc9987295b42454da8c4494c8ff22"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/aac2aa37f20cc9987295b42454da8c4494c8ff22", "type": "zip", "shasum": "", "reference": "aac2aa37f20cc9987295b42454da8c4494c8ff22"}, "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.0.0"}, "time": "2019-10-17T07:24:59+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "081760c53a4ce76c9935a755a21353610f5495f6"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/081760c53a4ce76c9935a755a21353610f5495f6", "type": "zip", "shasum": "", "reference": "081760c53a4ce76c9935a755a21353610f5495f6"}, "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/master"}, "time": "2018-11-05T14:00:06+00:00", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "require": {"php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0.0"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "b5063ffb8b129923c8988f1d4061fdeea53b47b9"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/b5063ffb8b129923c8988f1d4061fdeea53b47b9", "type": "zip", "shasum": "", "reference": "b5063ffb8b129923c8988f1d4061fdeea53b47b9"}, "time": "2018-05-22T12:10:22+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "dcc0be58e8b35a726274249e5eee053be1a56b66"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/dcc0be58e8b35a726274249e5eee053be1a56b66", "type": "zip", "shasum": "", "reference": "dcc0be58e8b35a726274249e5eee053be1a56b66"}, "time": "2017-01-04T08:02:42+00:00", "autoload": {"psr-4": {"League\\Uri\\Interfaces\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.0.0"}}, {"description": "Common interface for URI", "version": "0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "7be13cf760fc3d64e4dc95322a5d488f449b3318"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/7be13cf760fc3d64e4dc95322a5d488f449b3318", "type": "zip", "shasum": "", "reference": "7be13cf760fc3d64e4dc95322a5d488f449b3318"}, "time": "2016-12-09T07:32:44+00:00"}, {"version": "0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "05168f713b56cbf19a54d5ebbe46852d415f140a"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/05168f713b56cbf19a54d5ebbe46852d415f140a", "type": "zip", "shasum": "", "reference": "05168f713b56cbf19a54d5ebbe46852d415f140a"}, "time": "2016-12-01T20:10:27+00:00"}, {"version": "0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "823d257c4a70298d11201766b3843c44748565b2"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/823d257c4a70298d11201766b3843c44748565b2", "type": "zip", "shasum": "", "reference": "823d257c4a70298d11201766b3843c44748565b2"}, "time": "2016-11-29T12:09:07+00:00"}, {"version": "0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/thephpleague/uri-interfaces.git", "type": "git", "reference": "d71f4658cbea0837ad6e2811a2465cc5da0f1d6c"}, "dist": {"url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/d71f4658cbea0837ad6e2811a2465cc5da0f1d6c", "type": "zip", "shasum": "", "reference": "d71f4658cbea0837ad6e2811a2465cc5da0f1d6c"}, "time": "2016-10-17T13:50:03+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 08 Dec 2024 08:26:32 GMT"}