{"minified": "composer/2.0", "packages": {"amphp/amp": [{"name": "amphp/amp", "description": "A non-blocking concurrency framework for PHP applications.", "keywords": ["event", "event-loop", "async", "asynchronous", "concurrency", "non-blocking", "promise", "future", "awaitable"], "homepage": "https://amphp.org/amp", "version": "v3.1.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "7cf7fef3d667bfe4b2560bc87e67d5387a7bcde9"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/7cf7fef3d667bfe4b2560bc87e67d5387a7bcde9", "type": "zip", "shasum": "", "reference": "7cf7fef3d667bfe4b2560bc87e67d5387a7bcde9"}, "type": "library", "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.1.0"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-01-26T16:07:39+00:00", "autoload": {"files": ["src/functions.php", "src/Future/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\": "src"}}, "require": {"php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^9", "psalm/phar": "5.23.1"}}, {"version": "v3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "138801fb68cfc9c329da8a7b39d01ce7291ee4b0"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/138801fb68cfc9c329da8a7b39d01ce7291ee4b0", "type": "zip", "shasum": "", "reference": "138801fb68cfc9c329da8a7b39d01ce7291ee4b0"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.2"}, "time": "2024-05-10T21:37:46+00:00"}, {"version": "v3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "ff63f10210adb6e83335e0e25522bac5cd7dc4e2"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/ff63f10210adb6e83335e0e25522bac5cd7dc4e2", "type": "zip", "shasum": "", "reference": "ff63f10210adb6e83335e0e25522bac5cd7dc4e2"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.1"}, "time": "2024-04-18T15:24:36+00:00"}, {"version": "v3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "aaf0ec1d5a2c20b523258995a10e80c1fb765871"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/aaf0ec1d5a2c20b523258995a10e80c1fb765871", "type": "zip", "shasum": "", "reference": "aaf0ec1d5a2c20b523258995a10e80c1fb765871"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0"}, "time": "2022-12-18T16:52:44+00:00", "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^9", "psalm/phar": "^4.13"}}, {"version": "v3.0.0-beta.11", "version_normalized": "*******-beta11", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "92e225300d3f42d049f8904495df7c2f55d43acb"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/92e225300d3f42d049f8904495df7c2f55d43acb", "type": "zip", "shasum": "", "reference": "92e225300d3f42d049f8904495df7c2f55d43acb"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.11"}, "time": "2022-12-16T22:23:48+00:00", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require-dev": {"ext-json": "*", "amphp/php-cs-fixer-config": "^2-dev", "phpunit/phpunit": "^9", "psalm/phar": "^4.13"}}, {"version": "v3.0.0-beta.10", "version_normalized": "*******-beta10", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "352d8c030a74c17fc4bcf389956f85ad401f0c40"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/352d8c030a74c17fc4bcf389956f85ad401f0c40", "type": "zip", "shasum": "", "reference": "352d8c030a74c17fc4bcf389956f85ad401f0c40"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.10"}, "time": "2022-11-06T21:19:31+00:00", "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2-dev", "phpunit/phpunit": "^9", "psalm/phar": "^4.13"}}, {"version": "v3.0.0-beta.9", "version_normalized": "*******-beta9", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "354a2ec25269b630ece65f149e1ccb438380f56c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/354a2ec25269b630ece65f149e1ccb438380f56c", "type": "zip", "shasum": "", "reference": "354a2ec25269b630ece65f149e1ccb438380f56c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.9"}, "time": "2022-07-12T20:19:22+00:00", "require": {"php": ">=8.1", "revolt/event-loop": "^0.2 || ^0.1"}}, {"version": "v3.0.0-beta.8", "version_normalized": "*******-beta8", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "41764f01ef37057f990165d5de4d3b3122dc6c93"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/41764f01ef37057f990165d5de4d3b3122dc6c93", "type": "zip", "shasum": "", "reference": "41764f01ef37057f990165d5de4d3b3122dc6c93"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.8"}, "time": "2022-06-25T18:22:59+00:00"}, {"version": "v3.0.0-beta.7", "version_normalized": "*******-beta7", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "01c289ec8df91ce6f43e25e4fa00d669f4faa862"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/01c289ec8df91ce6f43e25e4fa00d669f4faa862", "type": "zip", "shasum": "", "reference": "01c289ec8df91ce6f43e25e4fa00d669f4faa862"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.7"}, "time": "2022-06-14T23:21:55+00:00"}, {"version": "v3.0.0-beta.6", "version_normalized": "*******-beta6", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "d8d0b8fabe62cc52749cd01242d1730a43c4802e"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/d8d0b8fabe62cc52749cd01242d1730a43c4802e", "type": "zip", "shasum": "", "reference": "d8d0b8fabe62cc52749cd01242d1730a43c4802e"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.6"}, "time": "2022-04-20T04:52:32+00:00"}, {"version": "v3.0.0-beta.5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "dc327dd458a69ee0a86faa37f32db257988c0646"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/dc327dd458a69ee0a86faa37f32db257988c0646", "type": "zip", "shasum": "", "reference": "dc327dd458a69ee0a86faa37f32db257988c0646"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.5"}, "time": "2022-04-03T16:28:42+00:00"}, {"version": "v3.0.0-beta.4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "379177aba93518e2df6d626677cbbdc48cc0d8ae"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/379177aba93518e2df6d626677cbbdc48cc0d8ae", "type": "zip", "shasum": "", "reference": "379177aba93518e2df6d626677cbbdc48cc0d8ae"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.4"}, "time": "2022-01-16T16:46:53+00:00", "require": {"php": ">=8", "revolt/event-loop": "^0.2 || ^0.1"}, "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "dev-master", "phpunit/phpunit": "^9", "psalm/phar": "^4.13"}}, {"version": "v3.0.0-beta.3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "e44a190dd4de9aa160667f315cbc19c3379689ba"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/e44a190dd4de9aa160667f315cbc19c3379689ba", "type": "zip", "shasum": "", "reference": "e44a190dd4de9aa160667f315cbc19c3379689ba"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.3"}, "time": "2021-12-12T20:09:19+00:00", "require": {"php": ">=8", "revolt/event-loop": "^0.1"}}, {"version": "v3.0.0-beta.2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "b8764391dd8774edd3068482133a30f9e9c1d74e"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/b8764391dd8774edd3068482133a30f9e9c1d74e", "type": "zip", "shasum": "", "reference": "b8764391dd8774edd3068482133a30f9e9c1d74e"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.2"}, "time": "2021-12-03T20:55:11+00:00"}, {"version": "v3.0.0-beta.1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "521624b9f73619825bac655b528a06f778803719"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/521624b9f73619825bac655b528a06f778803719", "type": "zip", "shasum": "", "reference": "521624b9f73619825bac655b528a06f778803719"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.0.0-beta.1"}, "time": "2021-12-03T00:02:05+00:00", "require": {"php": ">=8", "revolt/event-loop": "v1.x-dev"}, "require-dev": {"ext-json": "*", "amphp/phpunit-util": "v2.x-dev", "amphp/php-cs-fixer-config": "dev-master", "phpunit/phpunit": "^9", "psalm/phar": "^4.13"}}, {"version": "v2.6.4", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "type": "zip", "shasum": "", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.4"}, "time": "2024-03-21T18:52:26+00:00", "autoload": {"files": ["lib/functions.php", "lib/Internal/functions.php"], "psr-4": {"Amp\\": "lib"}}, "require": {"php": ">=7.1"}, "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^7 | ^8 | ^9", "vimeo/psalm": "^3.12", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.6.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "7e3560bfa626479c9b60b1fa3648d88ec5bd4235"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/7e3560bfa626479c9b60b1fa3648d88ec5bd4235", "type": "zip", "shasum": "", "reference": "7e3560bfa626479c9b60b1fa3648d88ec5bd4235"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.3"}, "time": "2024-03-21T18:43:59+00:00"}, {"version": "v2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "9d5100cebffa729aaffecd3ad25dc5aeea4f13bb"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/9d5100cebffa729aaffecd3ad25dc5aeea4f13bb", "type": "zip", "shasum": "", "reference": "9d5100cebffa729aaffecd3ad25dc5aeea4f13bb"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.2"}, "time": "2022-02-20T17:52:18+00:00", "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^7 | ^8 | ^9", "psalm/phar": "^3.11@dev", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"homepage": "http://amphp.org/amp", "version": "v2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "c5fc66a78ee38d7ac9195a37bacaf940eb3f65ae"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/c5fc66a78ee38d7ac9195a37bacaf940eb3f65ae", "type": "zip", "shasum": "", "reference": "c5fc66a78ee38d7ac9195a37bacaf940eb3f65ae"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.1"}, "time": "2021-09-23T18:43:08+00:00"}, {"version": "v2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "caa95edeb1ca1bf7532e9118ede4a3c3126408cc"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/caa95edeb1ca1bf7532e9118ede4a3c3126408cc", "type": "zip", "shasum": "", "reference": "caa95edeb1ca1bf7532e9118ede4a3c3126408cc"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.0"}, "time": "2021-07-16T20:06:06+00:00"}, {"version": "v2.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "efca2b32a7580087adb8aabbff6be1dc1bb924a9"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/efca2b32a7580087adb8aabbff6be1dc1bb924a9", "type": "zip", "shasum": "", "reference": "efca2b32a7580087adb8aabbff6be1dc1bb924a9"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.5.2"}, "time": "2021-01-10T17:06:37+00:00", "require": {"php": ">=7"}, "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^6.0.9 | ^7", "psalm/phar": "^3.11@dev", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "ecdc3c476b3ccff02f8e5d5bcc04f7ccfd18751c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/ecdc3c476b3ccff02f8e5d5bcc04f7ccfd18751c", "type": "zip", "shasum": "", "reference": "ecdc3c476b3ccff02f8e5d5bcc04f7ccfd18751c"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.5.1"}, "time": "2020-11-03T16:23:45+00:00"}, {"version": "v2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "f220a51458bf4dd0dedebb171ac3457813c72bbc"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/f220a51458bf4dd0dedebb171ac3457813c72bbc", "type": "zip", "shasum": "", "reference": "f220a51458bf4dd0dedebb171ac3457813c72bbc"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2020-07-14T21:47:18+00:00"}, {"version": "v2.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "1e58d53e4af390efc7813e36cd215bd82cba4b06"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/1e58d53e4af390efc7813e36cd215bd82cba4b06", "type": "zip", "shasum": "", "reference": "1e58d53e4af390efc7813e36cd215bd82cba4b06"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.4.4"}, "funding": [], "time": "2020-04-30T04:54:50+00:00", "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^6.0.9 | ^7", "vimeo/psalm": "^3.11@dev", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "23ac95fc6d6973231763f5ed7d1309b39429b974"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/23ac95fc6d6973231763f5ed7d1309b39429b974", "type": "zip", "shasum": "", "reference": "23ac95fc6d6973231763f5ed7d1309b39429b974"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2020-04-19T15:54:21+00:00"}, {"version": "v2.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "feca077369a47263b22156b3c6389e55f3809f24"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/feca077369a47263b22156b3c6389e55f3809f24", "type": "zip", "shasum": "", "reference": "feca077369a47263b22156b3c6389e55f3809f24"}, "time": "2020-04-04T15:05:26+00:00", "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^6.0.9 | ^7", "vimeo/psalm": "^3.9@dev", "jetbrains/phpstorm-stubs": "^2019.3"}}, {"version": "v2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "2ac3b550c4997f2ec304faa63c8b2885079a2dc4"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/2ac3b550c4997f2ec304faa63c8b2885079a2dc4", "type": "zip", "shasum": "", "reference": "2ac3b550c4997f2ec304faa63c8b2885079a2dc4"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.4.1"}, "time": "2020-02-10T18:10:57+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^6.0.9 | ^7", "phpstan/phpstan": "^0.8.5"}, "funding": "__unset"}, {"version": "v2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "13930a582947831bb66ff1aeac28672fd91c38ea"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/13930a582947831bb66ff1aeac28672fd91c38ea", "type": "zip", "shasum": "", "reference": "13930a582947831bb66ff1aeac28672fd91c38ea"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.4.0"}, "time": "2019-11-11T19:32:05+00:00"}, {"version": "v2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "9fa04aa6f87a8200fc0961de6bcc5465785c0c6f"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/9fa04aa6f87a8200fc0961de6bcc5465785c0c6f", "type": "zip", "shasum": "", "reference": "9fa04aa6f87a8200fc0961de6bcc5465785c0c6f"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.3.2"}, "time": "2019-10-26T14:31:57+00:00"}, {"version": "v2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "5a294f13810844e3bdda28c0f352821d735ca9b3"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/5a294f13810844e3bdda28c0f352821d735ca9b3", "type": "zip", "shasum": "", "reference": "5a294f13810844e3bdda28c0f352821d735ca9b3"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.3.1"}, "time": "2019-10-01T19:39:23+00:00"}, {"version": "v2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "16572846086712f73598e0cee10f5314cef520f8"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/16572846086712f73598e0cee10f5314cef520f8", "type": "zip", "shasum": "", "reference": "16572846086712f73598e0cee10f5314cef520f8"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.3.0"}, "time": "2019-10-01T19:01:44+00:00"}, {"version": "v2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "49a0bf693d5e60e578e5518d87fe7025244f9db5"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/49a0bf693d5e60e578e5518d87fe7025244f9db5", "type": "zip", "shasum": "", "reference": "49a0bf693d5e60e578e5518d87fe7025244f9db5"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2019-09-21T21:29:37+00:00"}, {"version": "v2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "c6a775a6c9fdd9ca4c909647a19b02d2d11a0568"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/c6a775a6c9fdd9ca4c909647a19b02d2d11a0568", "type": "zip", "shasum": "", "reference": "c6a775a6c9fdd9ca4c909647a19b02d2d11a0568"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.2.0"}, "time": "2019-08-02T20:37:42+00:00", "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^6.0.9", "phpstan/phpstan": "^0.8.5"}}, {"version": "v2.1.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "f824f0df1da2cc042934fa267d7a227fff9276c9"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/f824f0df1da2cc042934fa267d7a227fff9276c9", "type": "zip", "shasum": "", "reference": "f824f0df1da2cc042934fa267d7a227fff9276c9"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.1.2"}, "time": "2019-05-31T18:42:02+00:00"}, {"version": "v2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "7075ef7d74dbd32626bfd31c976b23055c3ade6a"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/7075ef7d74dbd32626bfd31c976b23055c3ade6a", "type": "zip", "shasum": "", "reference": "7075ef7d74dbd32626bfd31c976b23055c3ade6a"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2018-12-11T10:31:37+00:00", "require-dev": {"amphp/phpunit-util": "^1", "react/promise": "^2", "friendsofphp/php-cs-fixer": "^2.3", "phpunit/phpunit": "^6.0.9", "phpstan/phpstan": "^0.8.5"}}, {"version": "v2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "23b8bd2cfe2efeb7461640b355e8d856316323fe"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/23b8bd2cfe2efeb7461640b355e8d856316323fe", "type": "zip", "shasum": "", "reference": "23b8bd2cfe2efeb7461640b355e8d856316323fe"}, "time": "2018-12-10T19:13:25+00:00"}, {"version": "v2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "d561cc9736bc18dd94a2fc9cdae98b616bd92c43"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/d561cc9736bc18dd94a2fc9cdae98b616bd92c43", "type": "zip", "shasum": "", "reference": "d561cc9736bc18dd94a2fc9cdae98b616bd92c43"}, "time": "2018-04-30T20:49:57+00:00"}, {"version": "v2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "4a742beb59615f36ed998e2dc210e36576e44c44"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/4a742beb59615f36ed998e2dc210e36576e44c44", "type": "zip", "shasum": "", "reference": "4a742beb59615f36ed998e2dc210e36576e44c44"}, "time": "2018-01-27T19:18:05+00:00"}, {"version": "v2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "502b4be0008ed2f9479ad598187e49683ce6ef39"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/502b4be0008ed2f9479ad598187e49683ce6ef39", "type": "zip", "shasum": "", "reference": "502b4be0008ed2f9479ad598187e49683ce6ef39"}, "time": "2017-12-19T17:27:18+00:00"}, {"version": "v2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "b6be3871f3e82c86cf4dce7924286faef440fd38"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/b6be3871f3e82c86cf4dce7924286faef440fd38", "type": "zip", "shasum": "", "reference": "b6be3871f3e82c86cf4dce7924286faef440fd38"}, "time": "2017-12-05T07:56:44+00:00"}, {"version": "v2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "9f693b1d5394d5b35b97f83bbf3f1c94339b988a"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/9f693b1d5394d5b35b97f83bbf3f1c94339b988a", "type": "zip", "shasum": "", "reference": "9f693b1d5394d5b35b97f83bbf3f1c94339b988a"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.0.3"}, "time": "2017-11-07T09:04:49+00:00", "require-dev": {"amphp/phpunit-util": "^1", "react/promise": "^2", "friendsofphp/php-cs-fixer": "^2.3", "phpunit/phpunit": "^6.0.9"}}, {"version": "v2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "8b136336a9af595d00681c1407ef598ccfa599ab"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/8b136336a9af595d00681c1407ef598ccfa599ab", "type": "zip", "shasum": "", "reference": "8b136336a9af595d00681c1407ef598ccfa599ab"}, "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2017-09-19T19:08:25+00:00"}, {"version": "v2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "916cf3119d96ba6d3b5a8c9d9bdd258960400141"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/916cf3119d96ba6d3b5a8c9d9bdd258960400141", "type": "zip", "shasum": "", "reference": "916cf3119d96ba6d3b5a8c9d9bdd258960400141"}, "time": "2017-07-09T12:20:40+00:00"}, {"version": "v2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "62e7956c93043b9f98e25e5bc9b9a216a6241ea4"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/62e7956c93043b9f98e25e5bc9b9a216a6241ea4", "type": "zip", "shasum": "", "reference": "62e7956c93043b9f98e25e5bc9b9a216a6241ea4"}, "time": "2017-06-15T15:50:28+00:00"}, {"description": "A non-blocking concurrency framework for PHP applications", "homepage": "https://github.com/amphp/amp", "version": "v2.0.0-RC4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "c7fcbd96dc6ce646e1df2ec3e87f4e60b03f306f"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/c7fcbd96dc6ce646e1df2ec3e87f4e60b03f306f", "type": "zip", "shasum": "", "reference": "c7fcbd96dc6ce646e1df2ec3e87f4e60b03f306f"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2017-06-09T15:22:21+00:00", "autoload": {"files": ["lib/functions.php", "lib/Internal/functions.php"], "psr-4": {"Amp\\": "lib/"}}, "require-dev": {"amphp/phpunit-util": "dev-master", "react/promise": "^2", "friendsofphp/php-cs-fixer": "^2.3", "phpunit/phpunit": "^6.0.9"}}, {"version": "v2.0.0-RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "cd252e4165678a5341a09c5eeaac9ed720023393"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/cd252e4165678a5341a09c5eeaac9ed720023393", "type": "zip", "shasum": "", "reference": "cd252e4165678a5341a09c5eeaac9ed720023393"}, "time": "2017-05-24T05:09:04+00:00"}, {"version": "v2.0.0-RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "fdf18dedd721cf556d25ed077ac71561532109f3"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/fdf18dedd721cf556d25ed077ac71561532109f3", "type": "zip", "shasum": "", "reference": "fdf18dedd721cf556d25ed077ac71561532109f3"}, "time": "2017-05-16T18:08:27+00:00"}, {"keywords": ["event", "async", "asynchronous", "concurrency", "non-blocking", "promise", "future", "awaitable"], "version": "v2.0.0-RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "5d36c15a1cf3e11df7f9e731737564acaa945ca7"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/5d36c15a1cf3e11df7f9e731737564acaa945ca7", "type": "zip", "shasum": "", "reference": "5d36c15a1cf3e11df7f9e731737564acaa945ca7"}, "time": "2017-05-04T15:27:50+00:00"}, {"keywords": ["event", "async", "concurrency", "non-blocking", "promise"], "version": "v1.2.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator / Lead Developer"}], "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "4f2161da5f68f274f116985635aea63b5c0f54d2"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/4f2161da5f68f274f116985635aea63b5c0f54d2", "type": "zip", "shasum": "", "reference": "4f2161da5f68f274f116985635aea63b5c0f54d2"}, "time": "2016-05-12T12:54:59+00:00", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\": "lib/"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "~4.8", "fabpot/php-cs-fixer": "~1.9"}}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "87a5f29ab4248efe1e15823c3aabd707f767912e"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/87a5f29ab4248efe1e15823c3aabd707f767912e", "type": "zip", "shasum": "", "reference": "87a5f29ab4248efe1e15823c3aabd707f767912e"}, "time": "2016-05-12T05:51:11+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "bb85e40ce1d3b273a52272ce245dfcba9028727d"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/bb85e40ce1d3b273a52272ce245dfcba9028727d", "type": "zip", "shasum": "", "reference": "bb85e40ce1d3b273a52272ce245dfcba9028727d"}, "time": "2016-05-11T17:14:33+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "cc7db038b3083cd46c1b5ad93a9195f4ee8757ec"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/cc7db038b3083cd46c1b5ad93a9195f4ee8757ec", "type": "zip", "shasum": "", "reference": "cc7db038b3083cd46c1b5ad93a9195f4ee8757ec"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v1.1.1"}, "time": "2016-05-11T01:36:46+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "093d33c36c72c0b4bf505000eb53b97351939279"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/093d33c36c72c0b4bf505000eb53b97351939279", "type": "zip", "shasum": "", "reference": "093d33c36c72c0b4bf505000eb53b97351939279"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2016-03-09T13:10:31+00:00"}, {"version": "v1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "4b667478f4b70a62e0f3abe7cfb8250e5a11ce0f"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/4b667478f4b70a62e0f3abe7cfb8250e5a11ce0f", "type": "zip", "shasum": "", "reference": "4b667478f4b70a62e0f3abe7cfb8250e5a11ce0f"}, "time": "2016-02-22T22:17:50+00:00"}, {"version": "v1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "5341a4cb92ce9a8e1b44816a391f95dd5b5b2197"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/5341a4cb92ce9a8e1b44816a391f95dd5b5b2197", "type": "zip", "shasum": "", "reference": "5341a4cb92ce9a8e1b44816a391f95dd5b5b2197"}, "time": "2016-01-09T14:19:56+00:00"}, {"version": "v1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "9f76a3f16e0be7c845d47d8b4f3fbb8b9fe38e83"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/9f76a3f16e0be7c845d47d8b4f3fbb8b9fe38e83", "type": "zip", "shasum": "", "reference": "9f76a3f16e0be7c845d47d8b4f3fbb8b9fe38e83"}, "time": "2015-11-16T02:02:13+00:00"}, {"version": "v1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "3057329a5ceda286c5a42a8119cba28e27880ea9"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/3057329a5ceda286c5a42a8119cba28e27880ea9", "type": "zip", "shasum": "", "reference": "3057329a5ceda286c5a42a8119cba28e27880ea9"}, "time": "2015-09-30T12:31:53+00:00"}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "880c965fbdad9d6bcc382b7484c00173ee17df13"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/880c965fbdad9d6bcc382b7484c00173ee17df13", "type": "zip", "shasum": "", "reference": "880c965fbdad9d6bcc382b7484c00173ee17df13"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v1.0.4"}, "time": "2015-09-10T21:19:43+00:00"}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "8f884b0b9d6ad736bad6be2717397e04a75f942c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/8f884b0b9d6ad736bad6be2717397e04a75f942c", "type": "zip", "shasum": "", "reference": "8f884b0b9d6ad736bad6be2717397e04a75f942c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2015-08-20T01:28:14+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "57c5cb6f0d9967daa0c10dda16e68a4b732045ba"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/57c5cb6f0d9967daa0c10dda16e68a4b732045ba", "type": "zip", "shasum": "", "reference": "57c5cb6f0d9967daa0c10dda16e68a4b732045ba"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v1.0.2"}, "time": "2015-08-18T13:35:05+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "3d1fde0812b6ddc30d73e784d4b2bd18ead25efb"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/3d1fde0812b6ddc30d73e784d4b2bd18ead25efb", "type": "zip", "shasum": "", "reference": "3d1fde0812b6ddc30d73e784d4b2bd18ead25efb"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2015-08-12T00:01:25+00:00", "extra": "__unset"}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "6612bb84dfb56d795a0ad0dc6c21ac90af7f3060"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/6612bb84dfb56d795a0ad0dc6c21ac90af7f3060", "type": "zip", "shasum": "", "reference": "6612bb84dfb56d795a0ad0dc6c21ac90af7f3060"}, "time": "2015-08-08T15:50:22+00:00", "require-dev": {"fabpot/php-cs-fixer": "~1.9", "phpunit/phpunit": "~4.8"}}, {"keywords": ["event", "async", "concurrency", "non-blocking", "promise", "future"], "version": "v1.0.0-beta5", "version_normalized": "*******-beta5", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "e8ac57db313d07fe1dac2d6db1df537ac4f9bd42"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/e8ac57db313d07fe1dac2d6db1df537ac4f9bd42", "type": "zip", "shasum": "", "reference": "e8ac57db313d07fe1dac2d6db1df537ac4f9bd42"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v1.0.x"}, "time": "2015-05-21T00:09:30+00:00", "extra": {"branch-alias": {"dev-master": "1.1.0-dev", "dev-v1.0.x": "1.0.0-dev"}}, "require-dev": {"phpunit/phpunit": "~4.4.0"}}, {"version": "v1.0.0-beta4", "version_normalized": "*******-beta4", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "9fa6010f192f82a81381ae2dd372e1e75107d332"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/9fa6010f192f82a81381ae2dd372e1e75107d332", "type": "zip", "shasum": "", "reference": "9fa6010f192f82a81381ae2dd372e1e75107d332"}, "time": "2015-05-20T03:12:32+00:00", "extra": {"branch-alias": {"dev-master": "2.0.0-dev", "dev-v1.0.x": "1.0.0-dev"}}}, {"version": "v1.0.0-beta3", "version_normalized": "*******-beta3", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "02e85b29c6c38fa673ed6000d9d1dfaa0b929edc"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/02e85b29c6c38fa673ed6000d9d1dfaa0b929edc", "type": "zip", "shasum": "", "reference": "02e85b29c6c38fa673ed6000d9d1dfaa0b929edc"}, "time": "2015-05-18T15:40:09+00:00"}, {"version": "v1.0.0-beta2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "aa27ab7a103d683f0b69b798d83e6e9d8bd25c27"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/aa27ab7a103d683f0b69b798d83e6e9d8bd25c27", "type": "zip", "shasum": "", "reference": "aa27ab7a103d683f0b69b798d83e6e9d8bd25c27"}, "time": "2015-05-17T23:52:41+00:00"}, {"version": "v1.0.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "515403077e08c4bae13049a124ac61b2fe7401ff"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/515403077e08c4bae13049a124ac61b2fe7401ff", "type": "zip", "shasum": "", "reference": "515403077e08c4bae13049a124ac61b2fe7401ff"}, "time": "2015-05-12T19:03:24+00:00", "suggest": {"php-uv": "Extension for libuv backends (high performance use cases)."}, "extra": "__unset"}, {"keywords": ["libevent", "async", "concurrency", "non-blocking", "promise", "future", "libuv"], "version": "v1.0.0-alpha", "version_normalized": "*******-alpha", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "489e357348b166e2012c08c3e1b3d30207969a53"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/489e357348b166e2012c08c3e1b3d30207969a53", "type": "zip", "shasum": "", "reference": "489e357348b166e2012c08c3e1b3d30207969a53"}, "time": "2015-05-04T16:59:35+00:00"}, {"version": "v0.17.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "f58a53fb089e048969670788adbdebdf91c0d294"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/f58a53fb089e048969670788adbdebdf91c0d294", "type": "zip", "shasum": "", "reference": "f58a53fb089e048969670788adbdebdf91c0d294"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.17"}, "time": "2015-04-27T18:56:59+00:00", "require": {"php": ">=5.4.0"}, "suggest": {"php-uv": "Extension for libuv backends (high performance use cases).", "pecl/libevent": "Extension for libevent backends (high performance use cases)."}, "require-dev": "__unset"}, {"version": "v0.16.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "446e09a95d8fe5d4368802f7b126095c5e54415c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/446e09a95d8fe5d4368802f7b126095c5e54415c", "type": "zip", "shasum": "", "reference": "446e09a95d8fe5d4368802f7b126095c5e54415c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2015-02-18T14:59:48+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "v0.15.3", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "5a39ec6730c8af07f24a4dd153a5e0455323e254"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/5a39ec6730c8af07f24a4dd153a5e0455323e254", "type": "zip", "shasum": "", "reference": "5a39ec6730c8af07f24a4dd153a5e0455323e254"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.15.3"}, "time": "2015-01-22T01:16:07+00:00"}, {"version": "v0.15.2", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "313e3d3a612efa78ba78587aa611f1f14002674c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/313e3d3a612efa78ba78587aa611f1f14002674c", "type": "zip", "shasum": "", "reference": "313e3d3a612efa78ba78587aa611f1f14002674c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.15.2"}, "time": "2015-01-21T21:00:20+00:00"}, {"version": "v0.15.1", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "abbbb6425da6f59b764b086e97ea1b33e249d680"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/abbbb6425da6f59b764b086e97ea1b33e249d680", "type": "zip", "shasum": "", "reference": "abbbb6425da6f59b764b086e97ea1b33e249d680"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.15.1"}, "time": "2015-01-19T01:16:02+00:00"}, {"version": "v0.15.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "c21068fc946f5bfd692297e927773a732f205279"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/c21068fc946f5bfd692297e927773a732f205279", "type": "zip", "shasum": "", "reference": "c21068fc946f5bfd692297e927773a732f205279"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2014-12-08T16:52:09+00:00", "suggest": "__unset"}, {"version": "v0.14.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "df9a41d456e22c076ddd8beace4f2f0d2f0e2ac0"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/df9a41d456e22c076ddd8beace4f2f0d2f0e2ac0", "type": "zip", "shasum": "", "reference": "df9a41d456e22c076ddd8beace4f2f0d2f0e2ac0"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.14.0"}, "time": "2014-11-18T15:08:00+00:00"}, {"version": "v0.13.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "5c46db0c41bcf6a7b5ad74f2150610acb1e286fc"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/5c46db0c41bcf6a7b5ad74f2150610acb1e286fc", "type": "zip", "shasum": "", "reference": "5c46db0c41bcf6a7b5ad74f2150610acb1e286fc"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/master"}, "time": "2014-11-05T16:57:59+00:00"}, {"version": "v0.12.1", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "70222364d5763b38655dd98834b5959d1c866514"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/70222364d5763b38655dd98834b5959d1c866514", "type": "zip", "shasum": "", "reference": "70222364d5763b38655dd98834b5959d1c866514"}, "time": "2014-11-04T14:46:12+00:00"}, {"version": "v0.12.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "2fd6538e47e173586649b8101a7a49845357a6c3"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/2fd6538e47e173586649b8101a7a49845357a6c3", "type": "zip", "shasum": "", "reference": "2fd6538e47e173586649b8101a7a49845357a6c3"}, "time": "2014-10-23T02:18:34+00:00"}, {"version": "v0.11.4", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "d719e302a63fc5c4a9015a7517cd21a252db04cd"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/d719e302a63fc5c4a9015a7517cd21a252db04cd", "type": "zip", "shasum": "", "reference": "d719e302a63fc5c4a9015a7517cd21a252db04cd"}, "time": "2014-10-20T16:08:09+00:00"}, {"version": "v0.11.3", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "0b6945b9db3ea4557b5564bbe1fe420312256b86"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/0b6945b9db3ea4557b5564bbe1fe420312256b86", "type": "zip", "shasum": "", "reference": "0b6945b9db3ea4557b5564bbe1fe420312256b86"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.11.3"}, "time": "2014-10-20T13:56:01+00:00"}, {"version": "v0.11.2", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "9a2ab863f4a266a141dfb537b43e58e521bb986c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/9a2ab863f4a266a141dfb537b43e58e521bb986c", "type": "zip", "shasum": "", "reference": "9a2ab863f4a266a141dfb537b43e58e521bb986c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.11.2"}, "time": "2014-10-20T13:45:49+00:00"}, {"version": "v0.11.1", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "3354f4bea1de7974634b3c07c08ab5bca9fbb302"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/3354f4bea1de7974634b3c07c08ab5bca9fbb302", "type": "zip", "shasum": "", "reference": "3354f4bea1de7974634b3c07c08ab5bca9fbb302"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.11.1"}, "time": "2014-09-24T16:21:18+00:00", "require": {"php": ">=5.5.0"}}, {"version": "v0.11.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "969d15ebe88e9732dc7ef29862df2e1876f0336c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/969d15ebe88e9732dc7ef29862df2e1876f0336c", "type": "zip", "shasum": "", "reference": "969d15ebe88e9732dc7ef29862df2e1876f0336c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.11.0"}, "time": "2014-09-24T03:47:20+00:00"}, {"description": "Event loops and stream IO reactors for non-blocking PHP applications", "keywords": ["libevent", "async", "non-blocking", "libuv"], "homepage": "https://github.com/rdlowrey/alert", "version": "v0.10.2", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "1807f340419f22c41728969d39c8af824f0535e7"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/1807f340419f22c41728969d39c8af824f0535e7", "type": "zip", "shasum": "", "reference": "1807f340419f22c41728969d39c8af824f0535e7"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.10.2"}, "time": "2014-08-02T06:21:32+00:00", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Alert\\": "lib/"}}, "require": {"php": ">=5.4.0"}, "extra": "__unset"}, {"version": "v0.10.1", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "c3650dce889bc7b356c9f062a79912763464128d"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/c3650dce889bc7b356c9f062a79912763464128d", "type": "zip", "shasum": "", "reference": "c3650dce889bc7b356c9f062a79912763464128d"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.10.1"}, "time": "2014-08-02T05:09:07+00:00", "autoload": {"psr-4": {"Alert\\": "lib/"}}}, {"version": "v0.10.0", "version_normalized": "********", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "6777532a66a15f5f43e87404249dbb5c0e4a835a"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/6777532a66a15f5f43e87404249dbb5c0e4a835a", "type": "zip", "shasum": "", "reference": "6777532a66a15f5f43e87404249dbb5c0e4a835a"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.10.0"}, "time": "2014-08-01T19:37:02+00:00"}, {"version": "v0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "16190c9b325d156e394ab3ce3c725f98e73df973"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/16190c9b325d156e394ab3ce3c725f98e73df973", "type": "zip", "shasum": "", "reference": "16190c9b325d156e394ab3ce3c725f98e73df973"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.9.0"}, "time": "2014-07-22T17:47:22+00:00"}, {"description": "Event loops for event-driven/async PHP", "keywords": ["events", "libevent", "async", "non-blocking"], "version": "v0.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "a09cc62f81d8950e05e6c715668be3cb2a07d716"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/a09cc62f81d8950e05e6c715668be3cb2a07d716", "type": "zip", "shasum": "", "reference": "a09cc62f81d8950e05e6c715668be3cb2a07d716"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.8.1"}, "time": "2014-06-11T14:09:10+00:00", "autoload": {"files": ["src/bootstrap.php"]}}, {"version": "v0.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "1762f9d69db2a1d85cefe16d89755e3b61ae0602"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/1762f9d69db2a1d85cefe16d89755e3b61ae0602", "type": "zip", "shasum": "", "reference": "1762f9d69db2a1d85cefe16d89755e3b61ae0602"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.8.0"}, "time": "2014-04-27T04:17:40+00:00"}, {"version": "v0.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "7b351454f96f1e412ee72c42008ae353ac838ffd"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/7b351454f96f1e412ee72c42008ae353ac838ffd", "type": "zip", "shasum": "", "reference": "7b351454f96f1e412ee72c42008ae353ac838ffd"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.7.1"}, "time": "2014-04-14T21:06:08+00:00"}, {"version": "v0.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "ad0a2b571c3293b9efa42b7c01cd9d523874556d"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/ad0a2b571c3293b9efa42b7c01cd9d523874556d", "type": "zip", "shasum": "", "reference": "ad0a2b571c3293b9efa42b7c01cd9d523874556d"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.7.0"}, "time": "2014-04-09T20:32:28+00:00"}, {"version": "v0.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "885969ff84585be43d1844172d10c2abe972c556"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/885969ff84585be43d1844172d10c2abe972c556", "type": "zip", "shasum": "", "reference": "885969ff84585be43d1844172d10c2abe972c556"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.6.0"}, "time": "2014-03-07T17:02:03+00:00"}, {"version": "v0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "37a333ed2c62199ce8705bc7963e5143a7161e0c"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/37a333ed2c62199ce8705bc7963e5143a7161e0c", "type": "zip", "shasum": "", "reference": "37a333ed2c62199ce8705bc7963e5143a7161e0c"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.5.0"}, "time": "2014-03-07T02:35:29+00:00"}, {"version": "v0.3.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator / Lead Developer"}], "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "ddadfea57c183527f290f9dc40ca5722f2d4e8e7"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/ddadfea57c183527f290f9dc40ca5722f2d4e8e7", "type": "zip", "shasum": "", "reference": "ddadfea57c183527f290f9dc40ca5722f2d4e8e7"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.3.0"}, "time": "2013-11-24T16:30:03+00:00", "autoload": {"psr-0": {"Alert": "src"}}}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "7784616d5ed52732c2c9eab04d419441958f0db5"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/7784616d5ed52732c2c9eab04d419441958f0db5", "type": "zip", "shasum": "", "reference": "7784616d5ed52732c2c9eab04d419441958f0db5"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.2.0"}, "time": "2013-08-29T06:40:19+00:00"}, {"version": "v0.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "71e3331d21f90b9143ee91513560fa891e09f1ec"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/71e3331d21f90b9143ee91513560fa891e09f1ec", "type": "zip", "shasum": "", "reference": "71e3331d21f90b9143ee91513560fa891e09f1ec"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.1.2"}, "time": "2013-08-12T15:35:18+00:00"}, {"version": "v0.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "c316405996c1a8a1ccc1bac4285e651edcd7b982"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/c316405996c1a8a1ccc1bac4285e651edcd7b982", "type": "zip", "shasum": "", "reference": "c316405996c1a8a1ccc1bac4285e651edcd7b982"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.1.1"}, "time": "2013-08-12T15:17:08+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/amphp/amp.git", "type": "git", "reference": "a146462ddc974db46ff63a96e419f023600cec8d"}, "dist": {"url": "https://api.github.com/repos/amphp/amp/zipball/a146462ddc974db46ff63a96e419f023600cec8d", "type": "zip", "shasum": "", "reference": "a146462ddc974db46ff63a96e419f023600cec8d"}, "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v0.1.0"}, "time": "2013-08-05T20:09:11+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 26 Jan 2025 17:13:45 GMT"}