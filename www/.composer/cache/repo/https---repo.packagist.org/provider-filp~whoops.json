{"minified": "composer/2.0", "packages": {"filp/whoops": [{"name": "filp/whoops", "description": "php error handling for cool kids", "keywords": ["library", "exception", "error", "handling", "whoops", "throwable"], "homepage": "https://filp.github.io/whoops/", "version": "2.18.3", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "59a123a3d459c5a23055802237cb317f609867e5"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/59a123a3d459c5a23055802237cb317f609867e5", "type": "zip", "shasum": "", "reference": "59a123a3d459c5a23055802237cb317f609867e5"}, "type": "library", "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.18.3"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2025-06-16T00:02:10+00:00", "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "require": {"php": "^7.1 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.3.3", "mockery/mockery": "^1.0", "symfony/var-dumper": "^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}}, {"version": "2.18.2", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "89dabca1490bc77dbcab41c2b20968c7e44bf7c3"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/89dabca1490bc77dbcab41c2b20968c7e44bf7c3", "type": "zip", "shasum": "", "reference": "89dabca1490bc77dbcab41c2b20968c7e44bf7c3"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.18.2"}, "time": "2025-06-11T20:42:19+00:00"}, {"version": "2.18.1", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "8fcc6a862f2e7b94eb4221fd0819ddba3d30ab26"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/8fcc6a862f2e7b94eb4221fd0819ddba3d30ab26", "type": "zip", "shasum": "", "reference": "8fcc6a862f2e7b94eb4221fd0819ddba3d30ab26"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.18.1"}, "time": "2025-06-03T18:56:14+00:00"}, {"version": "2.18.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a7de6c3c6c3c022f5cfc337f8ede6a14460cf77e"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a7de6c3c6c3c022f5cfc337f8ede6a14460cf77e", "type": "zip", "shasum": "", "reference": "a7de6c3c6c3c022f5cfc337f8ede6a14460cf77e"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.18.0"}, "time": "2025-03-15T12:00:00+00:00"}, {"version": "2.17.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "075bc0c26631110584175de6523ab3f1652eb28e"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/075bc0c26631110584175de6523ab3f1652eb28e", "type": "zip", "shasum": "", "reference": "075bc0c26631110584175de6523ab3f1652eb28e"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.17.0"}, "time": "2025-01-25T12:00:00+00:00"}, {"version": "2.16.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "befcdc0e5dce67252aa6322d82424be928214fa2"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/befcdc0e5dce67252aa6322d82424be928214fa2", "type": "zip", "shasum": "", "reference": "befcdc0e5dce67252aa6322d82424be928214fa2"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.16.0"}, "time": "2024-09-25T12:00:00+00:00"}, {"version": "2.15.4", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a139776fa3f5985a50b509f2a02ff0f709d2a546"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a139776fa3f5985a50b509f2a02ff0f709d2a546", "type": "zip", "shasum": "", "reference": "a139776fa3f5985a50b509f2a02ff0f709d2a546"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.4"}, "time": "2023-11-03T12:00:00+00:00", "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "mockery/mockery": "^0.9 || ^1.0", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}}, {"version": "2.15.3", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "c83e88a30524f9360b11f585f71e6b17313b7187"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/c83e88a30524f9360b11f585f71e6b17313b7187", "type": "zip", "shasum": "", "reference": "c83e88a30524f9360b11f585f71e6b17313b7187"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.3"}, "time": "2023-07-13T12:00:00+00:00"}, {"version": "2.15.2", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "aac9304c5ed61bf7b1b7a6064bf9806ab842ce73"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/aac9304c5ed61bf7b1b7a6064bf9806ab842ce73", "type": "zip", "shasum": "", "reference": "aac9304c5ed61bf7b1b7a6064bf9806ab842ce73"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.2"}, "time": "2023-04-12T12:00:00+00:00"}, {"version": "2.15.1", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "e864ac957acd66e1565f25efda61e37791a5db0b"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/e864ac957acd66e1565f25efda61e37791a5db0b", "type": "zip", "shasum": "", "reference": "e864ac957acd66e1565f25efda61e37791a5db0b"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.1"}, "time": "2023-03-06T18:09:13+00:00"}, {"version": "2.15.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "3e8aebbca9f0ae6f618962c4ad514077fd365ab3"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/3e8aebbca9f0ae6f618962c4ad514077fd365ab3", "type": "zip", "shasum": "", "reference": "3e8aebbca9f0ae6f618962c4ad514077fd365ab3"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.0"}, "time": "2023-03-03T12:00:00+00:00"}, {"version": "2.14.6", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "f7948baaa0330277c729714910336383286305da"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/f7948baaa0330277c729714910336383286305da", "type": "zip", "shasum": "", "reference": "f7948baaa0330277c729714910336383286305da"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.6"}, "time": "2022-11-02T16:23:29+00:00"}, {"version": "2.14.5", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a63e5e8f26ebbebf8ed3c5c691637325512eb0dc"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a63e5e8f26ebbebf8ed3c5c691637325512eb0dc", "type": "zip", "shasum": "", "reference": "a63e5e8f26ebbebf8ed3c5c691637325512eb0dc"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.5"}, "time": "2022-01-07T12:00:00+00:00"}, {"version": "2.14.4", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "f056f1fe935d9ed86e698905a957334029899895"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/f056f1fe935d9ed86e698905a957334029899895", "type": "zip", "shasum": "", "reference": "f056f1fe935d9ed86e698905a957334029899895"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.4"}, "time": "2021-10-03T12:00:00+00:00"}, {"version": "2.14.3", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "89584ce67dd32307f1063cc43846674f4679feda"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/89584ce67dd32307f1063cc43846674f4679feda", "type": "zip", "shasum": "", "reference": "89584ce67dd32307f1063cc43846674f4679feda"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.3"}, "time": "2021-09-19T12:00:00+00:00", "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1"}}, {"version": "2.14.2", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "76587f323162d0e3e116bf7bd4b34d91d2c20189"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/76587f323162d0e3e116bf7bd4b34d91d2c20189", "type": "zip", "shasum": "", "reference": "76587f323162d0e3e116bf7bd4b34d91d2c20189"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.2"}}, {"version": "2.14.1", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "15ead64e9828f0fc90932114429c4f7923570cb1"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/15ead64e9828f0fc90932114429c4f7923570cb1", "type": "zip", "shasum": "", "reference": "15ead64e9828f0fc90932114429c4f7923570cb1"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.1"}, "time": "2021-08-29T12:00:00+00:00"}, {"version": "2.14.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "fdf92f03e150ed84d5967a833ae93abffac0315b"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/fdf92f03e150ed84d5967a833ae93abffac0315b", "type": "zip", "shasum": "", "reference": "fdf92f03e150ed84d5967a833ae93abffac0315b"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.0"}, "time": "2021-07-13T12:00:00+00:00"}, {"version": "2.13.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "2edbc73a4687d9085c8f20f398eebade844e8424"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/2edbc73a4687d9085c8f20f398eebade844e8424", "type": "zip", "shasum": "", "reference": "2edbc73a4687d9085c8f20f398eebade844e8424"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.13.0"}, "time": "2021-06-04T12:00:00+00:00"}, {"version": "2.12.1", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "c13c0be93cff50f88bbd70827d993026821914dd"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/c13c0be93cff50f88bbd70827d993026821914dd", "type": "zip", "shasum": "", "reference": "c13c0be93cff50f88bbd70827d993026821914dd"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.12.1"}, "time": "2021-04-25T12:00:00+00:00"}, {"version": "2.12.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "d501fd2658d55491a2295ff600ae5978eaad7403"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/d501fd2658d55491a2295ff600ae5978eaad7403", "type": "zip", "shasum": "", "reference": "d501fd2658d55491a2295ff600ae5978eaad7403"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.12.0"}, "time": "2021-03-30T12:00:00+00:00"}, {"version": "2.11.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "f6e14679f948d8a5cfb866fa7065a30c66bd64d3"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/f6e14679f948d8a5cfb866fa7065a30c66bd64d3", "type": "zip", "shasum": "", "reference": "f6e14679f948d8a5cfb866fa7065a30c66bd64d3"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.11.0"}, "time": "2021-03-19T12:00:00+00:00"}, {"version": "2.10.0", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "6ecda5217bf048088b891f7403b262906be5a957"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/6ecda5217bf048088b891f7403b262906be5a957", "type": "zip", "shasum": "", "reference": "6ecda5217bf048088b891f7403b262906be5a957"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.10.0"}, "time": "2021-03-16T12:00:00+00:00"}, {"version": "2.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "df7933820090489623ce0be5e85c7e693638e536"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/df7933820090489623ce0be5e85c7e693638e536", "type": "zip", "shasum": "", "reference": "df7933820090489623ce0be5e85c7e693638e536"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.9.2"}, "time": "2021-01-24T12:00:00+00:00"}, {"version": "2.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "307fb34a5ab697461ec4c9db865b20ff2fd40771"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/307fb34a5ab697461ec4c9db865b20ff2fd40771", "type": "zip", "shasum": "", "reference": "307fb34a5ab697461ec4c9db865b20ff2fd40771"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.9.1"}, "funding": [], "time": "2020-11-01T12:00:00+00:00"}, {"version": "2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "2ec31f3adc54c71a59c5e3c2143d7a0e2f8899f8"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/2ec31f3adc54c71a59c5e3c2143d7a0e2f8899f8", "type": "zip", "shasum": "", "reference": "2ec31f3adc54c71a59c5e3c2143d7a0e2f8899f8"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.9.0"}, "time": "2020-10-20T12:00:00+00:00"}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "fa50d9db1c0c2fae99cf988d27023effda5524a3"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/fa50d9db1c0c2fae99cf988d27023effda5524a3", "type": "zip", "shasum": "", "reference": "fa50d9db1c0c2fae99cf988d27023effda5524a3"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.8.0"}, "time": "2020-10-17T09:00:00+00:00"}, {"version": "2.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "5d5fe9bb3d656b514d455645b3addc5f7ba7714d"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/5d5fe9bb3d656b514d455645b3addc5f7ba7714d", "type": "zip", "shasum": "", "reference": "5d5fe9bb3d656b514d455645b3addc5f7ba7714d"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.7.3"}, "time": "2020-06-14T09:00:00+00:00", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "require": {"php": "^5.5.9 || ^7.0", "psr/log": "^1.0.1"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0", "mockery/mockery": "^0.9 || ^1.0", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}}, {"version": "2.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "17d0d3f266c8f925ebd035cd36f83cf802b47d4a"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/17d0d3f266c8f925ebd035cd36f83cf802b47d4a", "type": "zip", "shasum": "", "reference": "17d0d3f266c8f925ebd035cd36f83cf802b47d4a"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.7.2"}, "time": "2020-05-05T12:28:07+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "fff6f1e4f36be0e0d0b84d66b413d9dcb0c49130"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/fff6f1e4f36be0e0d0b84d66b413d9dcb0c49130", "type": "zip", "shasum": "", "reference": "fff6f1e4f36be0e0d0b84d66b413d9dcb0c49130"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.7.1"}, "time": "2020-01-15T10:00:00+00:00", "funding": "__unset"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "4c97f814aa2f0dd4d5bedc89181c10ef12c004c5"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/4c97f814aa2f0dd4d5bedc89181c10ef12c004c5", "type": "zip", "shasum": "", "reference": "4c97f814aa2f0dd4d5bedc89181c10ef12c004c5"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.7.0"}, "time": "2019-12-29T10:00:00+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "ecbc8f3ed2cafca3cfca3d5febaae5a9d2899508"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/ecbc8f3ed2cafca3cfca3d5febaae5a9d2899508", "type": "zip", "shasum": "", "reference": "ecbc8f3ed2cafca3cfca3d5febaae5a9d2899508"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.6.0"}, "time": "2019-12-25T10:00:00+00:00", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "ee9699e79d8fcdd15c107e035d7b965e4fa854ac"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/ee9699e79d8fcdd15c107e035d7b965e4fa854ac", "type": "zip", "shasum": "", "reference": "ee9699e79d8fcdd15c107e035d7b965e4fa854ac"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.5.1"}, "time": "2019-12-21T10:00:00+00:00", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7", "mockery/mockery": "^0.9 || ^1.0", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0"}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "cde50e6720a39fdacb240159d3eea6865d51fd96"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/cde50e6720a39fdacb240159d3eea6865d51fd96", "type": "zip", "shasum": "", "reference": "cde50e6720a39fdacb240159d3eea6865d51fd96"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.5.0"}, "time": "2019-08-07T09:00:00+00:00"}, {"version": "2.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "6fb502c23885701a991b0bba974b1a8eb6673577"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/6fb502c23885701a991b0bba974b1a8eb6673577", "type": "zip", "shasum": "", "reference": "6fb502c23885701a991b0bba974b1a8eb6673577"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.4.1"}, "time": "2019-07-04T09:00:00+00:00"}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "1a1a1044ad00e285bd2825fac4c3a0443d90ad33"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/1a1a1044ad00e285bd2825fac4c3a0443d90ad33", "type": "zip", "shasum": "", "reference": "1a1a1044ad00e285bd2825fac4c3a0443d90ad33"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.4.0"}, "time": "2019-06-23T09:00:00+00:00"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a28d5cef33627c069a2bd8fbb35ebe1a54881d35"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a28d5cef33627c069a2bd8fbb35ebe1a54881d35", "type": "zip", "shasum": "", "reference": "a28d5cef33627c069a2bd8fbb35ebe1a54881d35"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.3.2"}, "time": "2019-06-21T09:00:00+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "bc0fd11bc455cc20ee4b5edabc63ebbf859324c7"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/bc0fd11bc455cc20ee4b5edabc63ebbf859324c7", "type": "zip", "shasum": "", "reference": "bc0fd11bc455cc20ee4b5edabc63ebbf859324c7"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.3.1"}, "time": "2018-10-23T09:00:00+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a9f129b99df316f847584d482c89c14a9f796e2b"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a9f129b99df316f847584d482c89c14a9f796e2b", "type": "zip", "shasum": "", "reference": "a9f129b99df316f847584d482c89c14a9f796e2b"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.3.0"}, "time": "2018-10-20T09:00:00+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "e79cd403fb77fc8963a99ecc30e80ddd885b3311"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/e79cd403fb77fc8963a99ecc30e80ddd885b3311", "type": "zip", "shasum": "", "reference": "e79cd403fb77fc8963a99ecc30e80ddd885b3311"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/master"}, "time": "2018-06-30T13:14:06+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "181c4502d8f34db7aed7bfe88d4f87875b8e947a"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/181c4502d8f34db7aed7bfe88d4f87875b8e947a", "type": "zip", "shasum": "", "reference": "181c4502d8f34db7aed7bfe88d4f87875b8e947a"}, "time": "2018-03-03T17:56:25+00:00", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}}, {"version": "2.1.14", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "c6081b8838686aa04f1e83ba7e91f78b7b2a23e6"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/c6081b8838686aa04f1e83ba7e91f78b7b2a23e6", "type": "zip", "shasum": "", "reference": "c6081b8838686aa04f1e83ba7e91f78b7b2a23e6"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.14"}, "time": "2017-11-23T18:22:44+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7", "mockery/mockery": "0.9.*", "symfony/var-dumper": "^2.6 || ^3.0"}}, {"version": "2.1.13", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "9f640a4f129e57d3537887f840380109a4dd182b"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/9f640a4f129e57d3537887f840380109a4dd182b", "type": "zip", "shasum": "", "reference": "9f640a4f129e57d3537887f840380109a4dd182b"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.13"}, "time": "2017-11-17T08:15:51+00:00"}, {"version": "2.1.12", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a99f0b151846021ba7a73b4e3cba3ebc9f14f03e"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a99f0b151846021ba7a73b4e3cba3ebc9f14f03e", "type": "zip", "shasum": "", "reference": "a99f0b151846021ba7a73b4e3cba3ebc9f14f03e"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.12"}, "time": "2017-10-15T13:05:10+00:00", "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0", "mockery/mockery": "0.9.*", "symfony/var-dumper": "^2.6 || ^3.0"}}, {"version": "2.1.11", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "141494232ff7d5c565ce2a9cea2be0958243708c"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/141494232ff7d5c565ce2a9cea2be0958243708c", "type": "zip", "shasum": "", "reference": "141494232ff7d5c565ce2a9cea2be0958243708c"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.11"}, "time": "2017-10-15T12:52:10+00:00"}, {"keywords": ["library", "zf2", "exception", "error", "handling", "whoops"], "version": "2.1.10", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "ffbbd2c06c64b08fb47974eed5dbce4ca2bb0eec"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/ffbbd2c06c64b08fb47974eed5dbce4ca2bb0eec", "type": "zip", "shasum": "", "reference": "ffbbd2c06c64b08fb47974eed5dbce4ca2bb0eec"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.10"}, "time": "2017-08-03T18:23:40+00:00"}, {"version": "2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "b238974e1c7cc1859b0c16ddc1c02ecb70ecc07f"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/b238974e1c7cc1859b0c16ddc1c02ecb70ecc07f", "type": "zip", "shasum": "", "reference": "b238974e1c7cc1859b0c16ddc1c02ecb70ecc07f"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.9"}, "time": "2017-06-03T18:33:07+00:00"}, {"version": "2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "f2950be7da8b8d6c4e77821b6c9d486e36cdc4f3"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/f2950be7da8b8d6c4e77821b6c9d486e36cdc4f3", "type": "zip", "shasum": "", "reference": "f2950be7da8b8d6c4e77821b6c9d486e36cdc4f3"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.8"}, "time": "2017-03-07T09:04:45+00:00"}, {"version": "2.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "1a8192deae1f35aabc3a73aad19cd07c68091dd1"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/1a8192deae1f35aabc3a73aad19cd07c68091dd1", "type": "zip", "shasum": "", "reference": "1a8192deae1f35aabc3a73aad19cd07c68091dd1"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.7"}, "time": "2017-03-02T16:54:56+00:00"}, {"version": "2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "92bdc6800ac6e233c9e161a1768e082389a73530"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/92bdc6800ac6e233c9e161a1768e082389a73530", "type": "zip", "shasum": "", "reference": "92bdc6800ac6e233c9e161a1768e082389a73530"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.6"}, "time": "2017-02-18T14:22:27+00:00", "require": {"php": "^5.5.9 || ^7.0"}}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "2abce9d956589122c6443d6265f01cf7e9388e3c"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/2abce9d956589122c6443d6265f01cf7e9388e3c", "type": "zip", "shasum": "", "reference": "2abce9d956589122c6443d6265f01cf7e9388e3c"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.5"}, "time": "2016-12-26T16:13:31+00:00"}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "3f5cbd0e6a2fc17bc40b0238025ef1ff4c8c3efa"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/3f5cbd0e6a2fc17bc40b0238025ef1ff4c8c3efa", "type": "zip", "shasum": "", "reference": "3f5cbd0e6a2fc17bc40b0238025ef1ff4c8c3efa"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.4"}, "time": "2016-10-11T15:32:23+00:00"}, {"homepage": "https://github.com/filp/whoops", "version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "8828aaa2178e0a19325522e2a45282ff0a14649b"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/8828aaa2178e0a19325522e2a45282ff0a14649b", "type": "zip", "shasum": "", "reference": "8828aaa2178e0a19325522e2a45282ff0a14649b"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/master"}, "time": "2016-05-06T18:25:35+00:00", "require": {"php": ">=5.5.9"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0", "mockery/mockery": "0.9.*", "symfony/var-dumper": "~3.0"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "d13505b240a6f580bc75ba591da30299d6cb0eec"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/d13505b240a6f580bc75ba591da30299d6cb0eec", "type": "zip", "shasum": "", "reference": "d13505b240a6f580bc75ba591da30299d6cb0eec"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.2"}, "time": "2016-04-07T06:16:25+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "afab26b3c642beaa55b20de3d1f7852bd50f50f3"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/afab26b3c642beaa55b20de3d1f7852bd50f50f3", "type": "zip", "shasum": "", "reference": "afab26b3c642beaa55b20de3d1f7852bd50f50f3"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.1"}, "time": "2016-04-05T18:46:47+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "8da9523f07c62d56fe1cf36f8ae29b333afc181f"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/8da9523f07c62d56fe1cf36f8ae29b333afc181f", "type": "zip", "shasum": "", "reference": "8da9523f07c62d56fe1cf36f8ae29b333afc181f"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.1.0"}, "time": "2016-03-13T10:22:39+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a327942c50cbd62b25c6fbe08f173031ce25fbff"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a327942c50cbd62b25c6fbe08f173031ce25fbff", "type": "zip", "shasum": "", "reference": "a327942c50cbd62b25c6fbe08f173031ce25fbff"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.0.0"}, "time": "2016-01-06T17:51:21+00:00", "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0", "mockery/mockery": "0.9.*"}}, {"version": "2.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "eb1ce6439db161a9f00cd57f33a9cfa1355229ec"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/eb1ce6439db161a9f00cd57f33a9cfa1355229ec", "type": "zip", "shasum": "", "reference": "eb1ce6439db161a9f00cd57f33a9cfa1355229ec"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.0.0-alpha2"}, "time": "2015-12-15T12:27:17+00:00", "autoload": {"files": ["src/Whoops/functions.php"], "psr-4": {"Whoops\\": "src/Whoops/"}}}, {"version": "2.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "3bf469434ebd2eff519ab9fe96a46a7bf6e69358"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/3bf469434ebd2eff519ab9fe96a46a7bf6e69358", "type": "zip", "shasum": "", "reference": "3bf469434ebd2eff519ab9fe96a46a7bf6e69358"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.0.0-alpha1"}, "time": "2015-12-12T23:10:55+00:00", "require": {"php": ">=5.3.3", "phpunit/phpunit": "^4.8"}, "require-dev": {"mockery/mockery": "0.9.*"}, "suggest": {"symfony/var-dumper": "^2.6", "whoops/soap": "Formats errors as SOAP responses"}}, {"keywords": ["library", "zf2", "exception", "error", "handling", "silex-provider", "whoops"], "version": "1.1.10", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "72538eeb70bbfb11964412a3d098d109efd012f7"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/72538eeb70bbfb11964412a3d098d109efd012f7", "type": "zip", "shasum": "", "reference": "72538eeb70bbfb11964412a3d098d109efd012f7"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.1.7"}, "time": "2015-06-29T05:42:04+00:00", "autoload": {"psr-0": {"Whoops": "src/"}, "classmap": ["src/deprecated"]}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "require": {"php": ">=5.3.0"}, "suggest": "__unset"}, {"version": "1.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "c9abab430c5a019724425b57093fc4c853cf716a"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/c9abab430c5a019724425b57093fc4c853cf716a", "type": "zip", "shasum": "", "reference": "c9abab430c5a019724425b57093fc4c853cf716a"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.1.9"}, "time": "2015-11-22T19:20:15+00:00"}, {"version": "1.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "df6f140c89876f687c98c8af4f07ae09cf6bdc2e"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/df6f140c89876f687c98c8af4f07ae09cf6bdc2e", "type": "zip", "shasum": "", "reference": "df6f140c89876f687c98c8af4f07ae09cf6bdc2e"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.1.8"}, "time": "2015-11-22T18:42:52+00:00", "require": {"php": ">=5.3.0 <6"}}, {"version": "1.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "72538eeb70bbfb11964412a3d098d109efd012f7"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/72538eeb70bbfb11964412a3d098d109efd012f7", "type": "zip", "shasum": "", "reference": "72538eeb70bbfb11964412a3d098d109efd012f7"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.1.7"}, "time": "2015-06-29T05:42:04+00:00", "require": {"php": ">=5.3.0"}}, {"version": "1.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "37b5e653c99cbb9113108d7dc59a0ddc306acc46"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/37b5e653c99cbb9113108d7dc59a0ddc306acc46", "type": "zip", "shasum": "", "reference": "37b5e653c99cbb9113108d7dc59a0ddc306acc46"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.1.6"}, "time": "2015-05-29T15:12:07+00:00"}, {"version": "1.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "c982fe62c44798c433229cb0425c61b487cc1883"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/c982fe62c44798c433229cb0425c61b487cc1883", "type": "zip", "shasum": "", "reference": "c982fe62c44798c433229cb0425c61b487cc1883"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/master"}, "time": "2015-03-30T15:26:59+00:00"}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "dcce91553e2e056857f1ea844949e9a8a71b613d"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/dcce91553e2e056857f1ea844949e9a8a71b613d", "type": "zip", "shasum": "", "reference": "dcce91553e2e056857f1ea844949e9a8a71b613d"}, "time": "2015-02-17T15:50:31+00:00"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a85fab9a98f1f9b8ebcdbe71733f0d910e5b9adf"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a85fab9a98f1f9b8ebcdbe71733f0d910e5b9adf", "type": "zip", "shasum": "", "reference": "a85fab9a98f1f9b8ebcdbe71733f0d910e5b9adf"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.1.3"}, "time": "2014-10-26T09:05:09+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "9f451fbc7b8cad5e71300672c340c28c6bec09ff"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/9f451fbc7b8cad5e71300672c340c28c6bec09ff", "type": "zip", "shasum": "", "reference": "9f451fbc7b8cad5e71300672c340c28c6bec09ff"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/master"}, "time": "2014-07-11T05:56:54+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "ae05259236204ccf904a94e5b0d96df8d4351da5"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/ae05259236204ccf904a94e5b0d96df8d4351da5", "type": "zip", "shasum": "", "reference": "ae05259236204ccf904a94e5b0d96df8d4351da5"}, "time": "2014-05-13T05:05:11+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "a5865f8f02947590e36206b08368e5b08289a946"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/a5865f8f02947590e36206b08368e5b08289a946", "type": "zip", "shasum": "", "reference": "a5865f8f02947590e36206b08368e5b08289a946"}, "time": "2014-04-28T04:54:31+00:00", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}, {"version": "1.1.0-rc", "version_normalized": "*******-RC", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "70e23d1c5225af13f964e78edf38e0c5bef4c20f"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/70e23d1c5225af13f964e78edf38e0c5bef4c20f", "type": "zip", "shasum": "", "reference": "70e23d1c5225af13f964e78edf38e0c5bef4c20f"}, "time": "2014-03-31T04:21:53+00:00", "require-dev": {"php": ">=5.3.3", "mockery/mockery": "0.9.*"}}, {"version": "1.0.10", "version_normalized": "********", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "91e3fd4b0812017ffbeb24add55330664e1ea32a"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/91e3fd4b0812017ffbeb24add55330664e1ea32a", "type": "zip", "shasum": "", "reference": "91e3fd4b0812017ffbeb24add55330664e1ea32a"}, "time": "2013-12-04T14:19:30+00:00", "autoload": {"psr-0": {"Whoops": "src/"}}, "require-dev": {"mockery/mockery": "dev-master", "silex/silex": "1.0.*@dev"}, "extra": "__unset"}, {"version": "1.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "6de5a6fbb675c091ee4ee40fd01b2e3df4f200af"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/6de5a6fbb675c091ee4ee40fd01b2e3df4f200af", "type": "zip", "shasum": "", "reference": "6de5a6fbb675c091ee4ee40fd01b2e3df4f200af"}, "time": "2013-10-28T22:06:33+00:00"}, {"version": "1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "d086796e7cfb59f532d07738e5e4cf0ac3c6deb8"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/d086796e7cfb59f532d07738e5e4cf0ac3c6deb8", "type": "zip", "shasum": "", "reference": "d086796e7cfb59f532d07738e5e4cf0ac3c6deb8"}, "time": "2013-08-06T20:39:59+00:00"}, {"version": "1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "12e144c788b275e5869ac15eda7bd0ae5da775bd"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/12e144c788b275e5869ac15eda7bd0ae5da775bd", "type": "zip", "shasum": "", "reference": "12e144c788b275e5869ac15eda7bd0ae5da775bd"}, "time": "2013-06-10T12:22:13+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "5551ca6342fe6234f1678cb14bd430c0c0b29cff"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/5551ca6342fe6234f1678cb14bd430c0c0b29cff", "type": "zip", "shasum": "", "reference": "5551ca6342fe6234f1678cb14bd430c0c0b29cff"}, "time": "2013-05-10T22:13:22+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "b3403e3f95b955fda8b87c689656aee0d55fb561"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/b3403e3f95b955fda8b87c689656aee0d55fb561", "type": "zip", "shasum": "", "reference": "b3403e3f95b955fda8b87c689656aee0d55fb561"}, "time": "2013-05-10T17:04:33+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "74567151e2a7d47ecb54be2ddf020813dcc37d1f"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/74567151e2a7d47ecb54be2ddf020813dcc37d1f", "type": "zip", "shasum": "", "reference": "74567151e2a7d47ecb54be2ddf020813dcc37d1f"}, "time": "2013-04-24T15:55:28+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "fce769fc9296411ecb9c6796451d3b24ca720eff"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/fce769fc9296411ecb9c6796451d3b24ca720eff", "type": "zip", "shasum": "", "reference": "fce769fc9296411ecb9c6796451d3b24ca720eff"}, "time": "2013-04-23T07:20:17+00:00"}, {"keywords": ["library", "exception", "error", "handling", "silex-provider", "whoops"], "version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "f3788e3b848b53441b619a628ffbe79a45ab402d"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/f3788e3b848b53441b619a628ffbe79a45ab402d", "type": "zip", "shasum": "", "reference": "f3788e3b848b53441b619a628ffbe79a45ab402d"}, "time": "2013-04-12T11:09:30+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "f68ba5df072f068f3cbec0956db54086d70b955f"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/f68ba5df072f068f3cbec0956db54086d70b955f", "type": "zip", "shasum": "", "reference": "f68ba5df072f068f3cbec0956db54086d70b955f"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/1.0.0"}, "time": "2013-04-09T14:34:25+00:00"}, {"version": "0.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "967ff7cfdb9ccae4828671524f3584d6b66761bb"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/967ff7cfdb9ccae4828671524f3584d6b66761bb", "type": "zip", "shasum": "", "reference": "967ff7cfdb9ccae4828671524f3584d6b66761bb"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/master"}, "time": "2013-03-17T12:58:04+00:00"}, {"keywords": ["library", "exception", "error", "handling", "damnit", "silex-provider"], "homepage": "https://github.com/filp/damnit", "version": "0.8.4", "version_normalized": "*******", "source": {"url": "https://github.com/filp/whoops.git", "type": "git", "reference": "cfd53bc9ec647c609ec516b85f250a23e48308c1"}, "dist": {"url": "https://api.github.com/repos/filp/whoops/zipball/cfd53bc9ec647c609ec516b85f250a23e48308c1", "type": "zip", "shasum": "", "reference": "cfd53bc9ec647c609ec516b85f250a23e48308c1"}, "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/0.8.4"}, "time": "2013-03-15T20:27:49+00:00", "autoload": {"psr-0": {"Damnit": "src/"}}}]}, "security-advisories": [{"advisoryId": "PKSA-vbfj-ghxh-xgb7", "affectedVersions": "<2.1.13"}], "last-modified": "Mon, 16 Jun 2025 00:02:47 GMT"}